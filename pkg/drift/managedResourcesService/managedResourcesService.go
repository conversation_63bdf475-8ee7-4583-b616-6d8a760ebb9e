package service

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/argoproj/gitops-engine/pkg/diff"
	"github.com/devtron-labs/common-lib/utils/k8sObjectsUtil"
	"github.com/devtron-labs/devtron/api/bean/AppView"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/pkg/auth/authorisation/casbin"
	deploymentCommon "github.com/devtron-labs/devtron/pkg/deployment/common"
	bean3 "github.com/devtron-labs/devtron/pkg/deployment/common/bean"
	"github.com/devtron-labs/devtron/pkg/deployment/deployedApp/status/resourceTree"
	"github.com/devtron-labs/devtron/pkg/drift"
	"github.com/devtron-labs/devtron/pkg/generateManifest"
	"github.com/devtron-labs/devtron/pkg/k8s"
	k8sapplication "github.com/devtron-labs/devtron/pkg/k8s/application"
	k8sbean "github.com/devtron-labs/devtron/pkg/k8s/application/bean"
	"github.com/devtron-labs/devtron/pkg/k8s/bean"
	"github.com/devtron-labs/devtron/util"
	jujuErrors "github.com/juju/errors"
	"go.uber.org/zap"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
)

type ManagedResourceService interface {
	GetManagedResource(ctx context.Context, request *bean.ResourceRequestBean, token string) (*drift.ManagedResource, error)
	GetManagedResources(ctx context.Context, appId int, envId int, cdPipeline *pipelineConfig.Pipeline, deploymentConfig *bean3.DeploymentConfig) (drift.ManagedResourcesResponse, error)
}

type ManagedResourceServiceImpl struct {
	logger                    *zap.SugaredLogger
	enforcer                  casbin.Enforcer
	k8sCommonService          k8s.K8sCommonService
	k8sApplicationService     k8sapplication.K8sApplicationService
	deploymentTemplateService generateManifest.DeploymentTemplateService
	deploymentConfigService   deploymentCommon.DeploymentConfigService
	resourceTreeService       resourceTree.Service
}

func NewManagedResourceServiceImpl(logger *zap.SugaredLogger, enforcer casbin.Enforcer, k8sCommonService k8s.K8sCommonService,
	k8sApplicationService k8sapplication.K8sApplicationService, service generateManifest.DeploymentTemplateService,
	deploymentConfigService deploymentCommon.DeploymentConfigService, resourceTreeService resourceTree.Service) *ManagedResourceServiceImpl {
	return &ManagedResourceServiceImpl{
		logger:                    logger,
		enforcer:                  enforcer,
		k8sCommonService:          k8sCommonService,
		k8sApplicationService:     k8sApplicationService,
		deploymentTemplateService: service,
		deploymentConfigService:   deploymentConfigService,
		resourceTreeService:       resourceTreeService,
	}
}

func (handler *ManagedResourceServiceImpl) GetManagedResource(ctx context.Context, request *bean.ResourceRequestBean, token string) (*drift.ManagedResource, error) {
	isSuperAdmin := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionGet, "*")
	ctxSuper := util.SetSuperAdminInContext(ctx, isSuperAdmin)
	var desiredManifest unstructured.Unstructured
	var err error
	if request.AppType == k8sbean.DevtronAppType {
		desiredManifest, err = handler.deploymentTemplateService.GetDesiredObjectManifestForAppEnv(ctxSuper, request.K8sRequest.ResourceIdentifier.GroupVersionKind.Kind, request.DevtronAppIdentifier.AppId, request.DevtronAppIdentifier.EnvId)
		if err != nil && err.Error() != fmt.Sprintf(generateManifest.ErrObjectNotFound, request.K8sRequest.ResourceIdentifier.GroupVersionKind.Kind) {
			return nil, err
		}
	}

	liveResource, err := handler.k8sCommonService.GetResource(ctx, request)
	if err != nil {
		handler.logger.Errorw("error in getting resource", "err", err)
		return nil, err
	}

	managedResource := &drift.ManagedResource{}
	if liveResource != nil && liveResource.ManifestResponse != nil {
		err = liveResource.ManifestResponse.SetRunningEphemeralContainers()
		if err != nil {
			handler.logger.Errorw("error in setting running ephemeral containers and setting them in resource response", "err", err)
			return nil, err
		}
	} else {
		return managedResource, nil
	}

	hasActionUpdate := false
	if request.AppIdentifier == nil && request.DevtronAppIdentifier == nil && request.AppType != k8sbean.ArgoAppType && request.ClusterId > 0 {
		hasActionUpdate = handler.k8sApplicationService.ValidateClusterResourceBean(ctx, request.ClusterId, liveResource.ManifestResponse.Manifest, request.K8sRequest.ResourceIdentifier.GroupVersionKind, handler.k8sApplicationService.GetRbacCallbackForResource(token, casbin.ActionUpdate))
		if !hasActionUpdate {
			readAllowed := handler.k8sApplicationService.ValidateClusterResourceBean(ctx, request.ClusterId, liveResource.ManifestResponse.Manifest, request.K8sRequest.ResourceIdentifier.GroupVersionKind, handler.k8sApplicationService.GetRbacCallbackForResource(token, casbin.ActionGet))
			if !readAllowed {
				return nil, jujuErrors.New("unauthorized")
			}
		}
	}

	params := drift.DefaultDiffConfigParams()
	diffResult := diff.DiffResult{}
	if desiredManifest.Object != nil {
		diffResult, err = drift.Diff(&liveResource.ManifestResponse.Manifest, &desiredManifest, params)
		if err != nil {
			return nil, err
		}
	}

	managedResource = &drift.ManagedResource{
		DesiredState: desiredManifest,
		LiveState:    liveResource.ManifestResponse.Manifest,
		HasDrift:     diffResult.Modified,
	}

	if !hasActionUpdate {
		managedResource, err = hideSecretsInManagedResource(*managedResource)
		if err != nil {
			handler.logger.Errorw("error in hiding secret values", "err", err)
			return nil, err
		}
	}

	var unstructuredNL map[string]interface{}
	if err = json.Unmarshal(diffResult.NormalizedLive, &unstructuredNL); err != nil {
		handler.logger.Errorw("error in unmarshalling normalized live state", "err", err)
		return managedResource, nil
	}

	var unstructuredPL map[string]interface{}
	if err = json.Unmarshal(diffResult.PredictedLive, &unstructuredPL); err != nil {
		handler.logger.Errorw("error in unmarshalling predicted live state", "err", err)
		return managedResource, nil
	}

	managedResource.NormalizedLiveState = unstructured.Unstructured{Object: unstructuredNL}
	managedResource.PredictedLiveState = unstructured.Unstructured{Object: unstructuredPL}

	if !hasActionUpdate {
		managedResource, err = hideSecretsInManagedResource(*managedResource)
		if err != nil {
			handler.logger.Errorw("error in hiding secret values", "err", err)
			return nil, err
		}
	}
	return managedResource, nil
}

func (handler ManagedResourceServiceImpl) GetManagedResources(ctx context.Context, appId int, envId int, cdPipeline *pipelineConfig.Pipeline, deploymentConfig *bean3.DeploymentConfig) (drift.ManagedResourcesResponse, error) {
	// desired manifest
	desiredManifestObjects, err := handler.deploymentTemplateService.GetDesiredManifestForAppEnv(ctx, appId, envId)
	if err != nil {
		handler.logger.Errorw("error in getting desired manifest", "appId", appId, "envId", envId)
		return drift.ManagedResourcesResponse{}, err
	}

	// live manifest
	resourceTree, err := handler.resourceTreeService.FetchResourceTree(ctx, appId, envId, cdPipeline, deploymentConfig)
	if err != nil {
		handler.logger.Errorw("error in fetching resource tree", "appId", appId, "envId", envId)
		return drift.ManagedResourcesResponse{}, err
	}

	k8sAppDetail := AppView.AppDetailContainer{
		DeploymentDetailContainer: AppView.DeploymentDetailContainer{
			ClusterId: cdPipeline.Environment.ClusterId,
			Namespace: cdPipeline.Environment.Namespace,
		},
	}

	validRequest := handler.k8sCommonService.BuildK8sRequestBean(resourceTree, k8sAppDetail, "")
	liveManifestObjects, err := handler.k8sCommonService.GetManifestsByBatch(ctx, validRequest)
	if err != nil {
		handler.logger.Errorw("error in getting live manifests in batch", "appId", appId, "envId", envId)
		return drift.ManagedResourcesResponse{}, err
	}

	liveAndDesiredManifestObject, err := drift.BuildLiveAndDesiredManifestObject(liveManifestObjects, desiredManifestObjects)
	if err != nil {
		handler.logger.Errorw("error in building live and desired manifest object", "appId", appId, "envId", envId)
		return drift.ManagedResourcesResponse{}, err
	}

	// drift calculation
	managedResourcesResponse, err := buildManagedResourceResponse(resourceTree, liveAndDesiredManifestObject)
	return managedResourcesResponse, err
}

func hideSecretsInManagedResource(managedResource drift.ManagedResource) (*drift.ManagedResource, error) {
	var err error

	// Hide secrets in DesiredState
	modifiedDesiredState, err := k8sObjectsUtil.HideValuesIfSecret(&managedResource.DesiredState)
	if err != nil {
		return nil, fmt.Errorf("error in hiding secret values in DesiredState: %w", err)
	}

	// Hide secrets in LiveState
	modifiedLiveState, err := k8sObjectsUtil.HideValuesIfSecret(&managedResource.LiveState)
	if err != nil {
		return nil, fmt.Errorf("error in hiding secret values in LiveState: %w", err)
	}

	// Hide secrets in NormalizedLiveState
	modifiedNormalizedLiveState, err := k8sObjectsUtil.HideValuesIfSecret(&managedResource.NormalizedLiveState)
	if err != nil {
		return nil, fmt.Errorf("error in hiding secret values in NormalizedLiveState: %w", err)
	}

	// Hide secrets in PredictedLiveState
	modifiedPredictedLiveState, err := k8sObjectsUtil.HideValuesIfSecret(&managedResource.PredictedLiveState)
	if err != nil {
		return nil, fmt.Errorf("error in hiding secret values in PredictedLiveState: %w", err)
	}

	modifiedManagedResource := &drift.ManagedResource{
		Group:               managedResource.Group,
		Kind:                managedResource.Kind,
		Namespace:           managedResource.Namespace,
		Name:                managedResource.Name,
		ResourceVersion:     managedResource.ResourceVersion,
		DesiredState:        *modifiedDesiredState,
		LiveState:           *modifiedLiveState,
		NormalizedLiveState: *modifiedNormalizedLiveState,
		PredictedLiveState:  *modifiedPredictedLiveState,
		HasDrift:            managedResource.HasDrift,
	}

	return modifiedManagedResource, nil
}

func buildManagedResourceResponse(resourceTree map[string]interface{}, liveAndDesiredManifestObject map[string]drift.LiveAndDesiredManifestObject) (drift.ManagedResourcesResponse, error) {
	var managedResourcesResponse drift.ManagedResourcesResponse
	managedResources := make([]drift.ManagedResource, 0)

	nodes, ok := resourceTree["nodes"].([]interface{})
	if !ok {
		return managedResourcesResponse, fmt.Errorf("nodes not found or invalid format")
	}

	for _, node := range nodes {
		nodeMap, ok := node.(map[string]interface{})
		if !ok {
			continue
		}

		group, _ := nodeMap["group"].(string)
		kind, _ := nodeMap["kind"].(string)
		namespace, _ := nodeMap["namespace"].(string)
		name, _ := nodeMap["name"].(string)
		resourceVersion, _ := nodeMap["resourceVersion"].(string)

		key := fmt.Sprintf("%s-%s", kind, name)
		liveAndDesiredObj, exists := liveAndDesiredManifestObject[key]
		if !exists || liveAndDesiredObj.DesiredManifest == nil || liveAndDesiredObj.LiveManifest == nil {
			continue
		}

		desiredState := *liveAndDesiredObj.DesiredManifest
		liveState := *liveAndDesiredObj.LiveManifest

		params := drift.DefaultDiffConfigParams()
		diffResult, err := drift.Diff(&liveState, &desiredState, params)
		if err != nil {
			return managedResourcesResponse, err
		}

		var unstructuredNL map[string]interface{}
		if err = json.Unmarshal(diffResult.NormalizedLive, &unstructuredNL); err != nil {
			return managedResourcesResponse, err
		}

		var unstructuredPL map[string]interface{}
		if err = json.Unmarshal(diffResult.PredictedLive, &unstructuredPL); err != nil {
			return managedResourcesResponse, err
		}

		managedResource := &drift.ManagedResource{
			Group:               group,
			Kind:                kind,
			Namespace:           namespace,
			Name:                name,
			ResourceVersion:     resourceVersion,
			DesiredState:        desiredState,
			LiveState:           liveState,
			NormalizedLiveState: unstructured.Unstructured{Object: unstructuredNL},
			PredictedLiveState:  unstructured.Unstructured{Object: unstructuredPL},
			HasDrift:            diffResult.Modified,
		}

		managedResource, err = hideSecretsInManagedResource(*managedResource)
		if err != nil {
			return managedResourcesResponse, err
		}

		managedResources = append(managedResources, *managedResource)
	}

	managedResourcesResponse.Items = managedResources
	return managedResourcesResponse, nil
}
