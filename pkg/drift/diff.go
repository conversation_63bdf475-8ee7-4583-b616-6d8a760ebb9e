package drift

import (
	"github.com/argoproj/argo-cd/v2/pkg/apis/application/v1alpha1"
	argo "github.com/argoproj/argo-cd/v2/util/argo/diff"
	"github.com/argoproj/argo-cd/v2/util/argo/normalizers"
	appstatecache "github.com/argoproj/argo-cd/v2/util/cache"
	"github.com/argoproj/gitops-engine/pkg/diff"
	"gopkg.in/yaml.v2"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"log"
	"os"
)

// keeping copy here because v1alpha1.ResourceIgnoreDifferences does not support unmarshalling from YAML
type ResourceIgnoreDifferences struct {
	Group                 string   `json:"group,omitempty" yaml:"group,omitempty" protobuf:"bytes,1,opt,name=group"`
	Kind                  string   `json:"kind" yaml:"kind" protobuf:"bytes,2,opt,name=kind"`
	Name                  string   `json:"name,omitempty" yaml:"name,omitempty" protobuf:"bytes,3,opt,name=name"`
	Namespace             string   `json:"namespace,omitempty" yaml:"namespace,omitempty" protobuf:"bytes,4,opt,name=namespace"`
	JSONPointers          []string `json:"jsonPointers,omitempty" yaml:"jsonPointers,omitempty" protobuf:"bytes,5,opt,name=jsonPointers"`
	JQPathExpressions     []string `json:"jqPathExpressions,omitempty" yaml:"jqPathExpressions,omitempty" protobuf:"bytes,6,opt,name=jqPathExpressions"`
	ManagedFieldsManagers []string `json:"managedFieldsManagers,omitempty" yaml:"managedFieldsManagers,omitempty" protobuf:"bytes,7,opt,name=managedFieldsManagers"`
}

const defaultIgnoreDiff = `
- group: "*"
  kind: "*"
  jsonPointers:
    - "*/metadata"
    - "*/annotations"
    - "*/managedFields"
    - "/status"
- group: "apps"
  kind: "*"
  jsonPointers:
    - "/spec/template/spec/serviceAccount"
- group: ""
  kind: "Service"
  jsonPointers:
    - "/spec/ipFamilies"
    - "/spec/ipFamilyPolicy"
    - "/spec/clusterIP"
    - "/spec/clusterIPs"
`

var globalIgnoreDiff []ResourceIgnoreDifferences

func init() {
	// Initialize the globalIgnoreDiff variable once when the server starts
	err := yaml.UnmarshalStrict([]byte(defaultIgnoreDiff), &globalIgnoreDiff)
	if err != nil {
		log.Println("error while unmarshalling defaultIgnoreDiff:", err)
	}

	envIgnoreDiff := os.Getenv("DRIFT_IGNORE_DIFF")
	if envIgnoreDiff != "" {
		var additionalIgnores []ResourceIgnoreDifferences
		err := yaml.UnmarshalStrict([]byte(envIgnoreDiff), &additionalIgnores)
		if err == nil {
			globalIgnoreDiff = append(globalIgnoreDiff, additionalIgnores...)
		} else {
			log.Println("error while unmarshalling DRIFT_IGNORE_DIFF:", err)
		}
	}
}

type diffConfigParams struct {
	ignores        []v1alpha1.ResourceIgnoreDifferences
	overrides      map[string]v1alpha1.ResourceOverride
	label          string
	trackingMethod string
	noCache        bool
	ignoreRoles    bool
	appName        string
	stateCache     *appstatecache.Cache
}

func DefaultDiffConfigParams() *diffConfigParams {
	//type caste []ResourceIgnoreDifferences into []v1alpha1.ResourceIgnoreDifferences
	globalIgnoreDiffFinal := make([]v1alpha1.ResourceIgnoreDifferences, len(globalIgnoreDiff))
	for _, obj := range globalIgnoreDiff {
		globalIgnoreDiffFinal = append(globalIgnoreDiffFinal, v1alpha1.ResourceIgnoreDifferences(obj))
	}

	return &diffConfigParams{
		ignores:        globalIgnoreDiffFinal,
		overrides:      map[string]v1alpha1.ResourceOverride{},
		label:          "",
		trackingMethod: "",
		noCache:        true,
		ignoreRoles:    true,
		appName:        "",
		stateCache:     &appstatecache.Cache{},
	}
}

func diffConfig(params *diffConfigParams) (argo.DiffConfig, error) {
	return argo.NewDiffConfigBuilder().
		WithDiffSettings(params.ignores, params.overrides, params.ignoreRoles, normalizers.IgnoreNormalizerOpts{}).
		WithTracking(params.label, params.trackingMethod).
		WithNoCache().
		Build()
}

func Diff(live, desired *unstructured.Unstructured, params *diffConfigParams) (diff.DiffResult, error) {
	dc, err := diffConfig(params)
	if err != nil {
		return diff.DiffResult{}, err
	}
	return argo.StateDiff(live, desired, dc)
}
