/*
 * Copyright (c) 2024. Devtron Inc.
 */

package bean

const (
	HostUrlKey                       string = "url"
	API_SECRET_KEY                   string = "apiTokenSecret"
	ENFORCE_DEPLOYMENT_TYPE_CONFIG   string = "enforceDeploymentTypeConfig"
	CI_RUNTIME_ENV_VARS              string = "ciRuntimeEnvVars"
	CD_RUNTIME_ENV_VARS              string = "cdRuntimeEnvVars"
	PRIORITY_DEPLOYMENT_CONDITION    string = "priorityDeploymentCondition"
	BUILDX_K8S_DRIVER_OPTS_MIGRATION string = "buildxK8sDriverOptsMigration"
	LICENSE_KEY                      string = "licenseKey"
	LICENSE_CERT                     string = "licenseCert"
)

type AttributesDto struct {
	Id     int    `json:"id"`
	Key    string `json:"key,omitempty"`
	Value  string `json:"value,omitempty"`
	Active bool   `json:"active"`
	UserId int32  `json:"-"`
}
