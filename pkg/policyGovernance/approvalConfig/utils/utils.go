package utils

import (
	"github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/bean"
	"github.com/devtron-labs/devtron/util"
	"github.com/devtron-labs/devtron/util/feHelper"
	"golang.org/x/exp/maps"
)

func UserCanApprove(approvalInfo *bean.UserApprovalConfigDTO, userIdentifier string) bool {
	if approvalInfo == nil {
		return false
	}

	if approvalInfo.AnyUserApprovalInfo != nil {
		for _, userInfo := range approvalInfo.AnyUserApprovalInfo.ApproverList {
			if userInfo.CanApprove && userInfo.Identifier == userIdentifier {
				return true
			}
		}
	}

	if approvalInfo.SpecificUserApprovalInfo != nil {
		for _, userInfo := range approvalInfo.SpecificUserApprovalInfo.ApproverList {
			if userInfo.CanApprove && userInfo.Identifier == userIdentifier {
				return true
			}
		}
	}

	if approvalInfo.UserGroupsApprovalInfo != nil {
		for _, userGroupInfo := range approvalInfo.UserGroupsApprovalInfo.UserGroups {
			for _, userInfo := range userGroupInfo.ApproverList {
				if userInfo.CanApprove && userInfo.Identifier == userIdentifier {
					return true
				}
			}
		}
	}

	return false
}

// todo: add unit tests
func GetDtoFromConfigAndUserResponse(kind bean.ApprovalFor, approvalConfig *bean.UserApprovalConfig, metadataReq *bean.ApprovalUsersAndUserGroupsMetadataReq) *bean.UserApprovalConfigDTO {
	approvalConfigDto := &bean.UserApprovalConfigDTO{
		Kind: kind,
	}

	if approvalConfig.AnyApprovalApprovalConfig.Configured() {
		// any user approval info logic
		// if we have some users with approval access add them in the list of any approver info
		approvedUsersInfo := make([]*bean.UserApprovalStatusInfo, 0)
		if metadataReq.ApprovalAccessUsersData != nil {
			for approvedUser := range metadataReq.ApprovalAccessUsersData.UserEmails {
				hasApproved := false
				userDeleted := false
				if metadataReq.ApprovedUsersData != nil {
					if userResp, ok := metadataReq.ApprovedUsersData.UserResponses[approvedUser]; ok {
						if userResp.UserResponse == bean.APPROVED {
							hasApproved = true
						}
						userDeleted = userResp.UserDeleted
					}
				}
				approvedUsersInfo = append(approvedUsersInfo, &bean.UserApprovalStatusInfo{Identifier: approvedUser, HasApproved: hasApproved, CanApprove: true, UserDeleted: userDeleted})
			}
		}

		// now iterate through the approvals we already got
		if metadataReq.ApprovedUsersData != nil {
			for userEmail, userResp := range metadataReq.ApprovedUsersData.UserResponses {
				if userResp.UserResponse != bean.APPROVED {
					continue
				}

				if metadataReq.ApprovalAccessUsersData != nil && metadataReq.ApprovalAccessUsersData.UserEmails[userEmail] {
					// if this user exists in approval access users, then he is already added in the list
					continue
				} else {
					approvedUsersInfo = append(approvedUsersInfo, &bean.UserApprovalStatusInfo{Identifier: userEmail, HasApproved: true, CanApprove: false, UserDeleted: userResp.UserDeleted})
				}
			}
		}

		approvalConfigDto.AnyUserApprovalInfo = &bean.ApprovalUsersInfo{ApproverList: approvedUsersInfo}
		approvalConfigDto.AnyUserApprovalInfo.SetRuntimeApprovalCountInfo(approvalConfig.AnyApprovalApprovalConfig.RequiredCount)
	}

	if approvalConfig.SpecificApprovalConfig.Configured() {

		// specific user approval info logic
		specificUserInfos := make([]*bean.UserApprovalStatusInfo, 0, len(approvalConfig.SpecificApprovalConfig.SpecificUsers.Identifiers))
		for _, userIdentifier := range approvalConfig.SpecificApprovalConfig.SpecificUsers.Identifiers {
			hasApproved, canApprove, userDeleted := false, false, false
			if metadataReq.ApprovedUsersData != nil {
				if userResp, ok := metadataReq.ApprovedUsersData.UserResponses[userIdentifier]; ok {
					hasApproved = true
					userDeleted = userResp.UserDeleted
				}
			}

			if metadataReq.ApprovalAccessUsersData != nil {
				if _, ok := metadataReq.ApprovalAccessUsersData.UserEmails[userIdentifier]; ok {
					canApprove = true
				}
			}

			specificUserInfos = append(specificUserInfos, &bean.UserApprovalStatusInfo{Identifier: userIdentifier, HasApproved: hasApproved, CanApprove: canApprove, UserDeleted: userDeleted})
		}

		specificUsersInfo := &bean.ApprovalUsersInfo{
			ApproverList: specificUserInfos,
		}

		specificUsersInfo.SetRuntimeApprovalCountInfo(len(approvalConfig.SpecificApprovalConfig.SpecificUsers.Identifiers))

		// user group approval users info
		userGroupInfos := make([]*bean.UserGroupInfo, 0, len(approvalConfig.SpecificApprovalConfig.UserGroups))
		for _, userGroup := range approvalConfig.SpecificApprovalConfig.UserGroups {
			approvedUsersInfo := make([]*bean.UserApprovalStatusInfo, 0)

			if metadataReq.ApprovalAccessUsersData != nil {
				approvalAccessUserEmails := metadataReq.ApprovalAccessUsersData.UserGroupIdentifierVsUserEmailsMap[userGroup.Identifier]
				for approvedUser := range approvalAccessUserEmails {
					hasApproved := false
					userDeleted := false
					if metadataReq.ApprovedUsersData != nil {
						if userResp, ok := metadataReq.ApprovedUsersData.UserResponses[approvedUser]; ok {
							if userResp.UserResponse == bean.APPROVED {
								hasApproved = true
							}
							userDeleted = userResp.UserDeleted
						}
					}
					approvedUsersInfo = append(approvedUsersInfo, &bean.UserApprovalStatusInfo{Identifier: approvedUser, HasApproved: hasApproved, CanApprove: true, UserDeleted: userDeleted})
				}
			}

			// now iterate through the approvals we already got
			if metadataReq.ApprovedUsersData != nil {
				approvedUserResponses := metadataReq.ApprovedUsersData.UserGroupVsUsersList[userGroup.Identifier]
				for userEmail, userResp := range approvedUserResponses {
					if userResp.UserResponse != bean.APPROVED {
						continue
					}

					if metadataReq.ApprovalAccessUsersData != nil && metadataReq.ApprovalAccessUsersData.UserEmails[userEmail] {
						// if this user exists in approval access users, then he is already added in the list
						continue
					} else {
						// we need to add his approval response in the list
						approvedUsersInfo = append(approvedUsersInfo, &bean.UserApprovalStatusInfo{Identifier: userEmail, HasApproved: true, CanApprove: false, UserDeleted: userResp.UserDeleted})
					}
				}
			}

			userGroupInfo := &bean.UserGroupInfo{
				GroupIdentifier: userGroup.Identifier,
				GroupName:       metadataReq.UserGroupIdentifierNameMappings[userGroup.Identifier],
				ApprovalUsersInfo: &bean.ApprovalUsersInfo{
					ApproverList: approvedUsersInfo,
				},
			}

			userGroupInfo.SetRuntimeApprovalCountInfo(userGroup.RequiredCount)
			userGroupInfos = append(userGroupInfos, userGroupInfo)
		}

		userGroupsInfo := &bean.UserGroupsApprovalInfo{
			UserGroups: userGroupInfos,
		}
		userGroupsInfo.SetRuntimeApprovalCountInfo()

		approvalConfigDto.SpecificUserApprovalInfo = specificUsersInfo
		approvalConfigDto.UserGroupsApprovalInfo = userGroupsInfo
	}

	approvalConfigDto.SetRuntimeApprovalCountInfo()
	approvalConfigDto = setDeletedSuffixForDeletedUser(approvalConfigDto)
	return approvalConfigDto
}

// todo : add unit tests
func MergeUserApprovalConfigs(approvalConfigs []*bean.UserApprovalConfig) *bean.UserApprovalConfig {
	mergedApprovalConfig := &bean.UserApprovalConfig{}
	if len(approvalConfigs) == 0 {
		return mergedApprovalConfig
	}

	userGroupNameVsRequiredCountMap := make(map[string]int)
	uniqueSpecificUsersMap := make(map[string]bool)

	for _, userApprovalConfig := range approvalConfigs {
		if userApprovalConfig == nil {
			continue
		}
		if userApprovalConfig.AnyApprovalApprovalConfig.Configured() {
			if mergedApprovalConfig.AnyApprovalApprovalConfig == nil {
				mergedApprovalConfig.AnyApprovalApprovalConfig = &bean.AnyApprovalApprovalConfig{}
			}
			mergedApprovalConfig.AnyApprovalApprovalConfig.RequiredCount = util.MaxInt(mergedApprovalConfig.AnyApprovalApprovalConfig.RequiredCount, userApprovalConfig.AnyApprovalApprovalConfig.RequiredCount)
		}

		if userApprovalConfig.SpecificApprovalConfig.Configured() {
			if mergedApprovalConfig.SpecificApprovalConfig == nil {
				mergedApprovalConfig.SpecificApprovalConfig = &bean.SpecificApprovalConfig{}
			}
			for _, userGroup := range userApprovalConfig.SpecificApprovalConfig.UserGroups {
				userGroupNameVsRequiredCountMap[userGroup.Identifier] = util.MaxInt(userGroupNameVsRequiredCountMap[userGroup.Identifier], userGroup.RequiredCount)
			}

			for _, specificUser := range userApprovalConfig.SpecificApprovalConfig.SpecificUsers.Identifiers {
				uniqueSpecificUsersMap[specificUser] = true
			}
		}
	}

	if mergedApprovalConfig.SpecificApprovalConfig != nil {
		userGroups := make([]bean.UserGroupApprovalConfig, 0, len(userGroupNameVsRequiredCountMap))
		for userGroupIdentifier, requiredCount := range userGroupNameVsRequiredCountMap {
			userGroups = append(userGroups, bean.UserGroupApprovalConfig{Identifier: userGroupIdentifier, RequiredCount: requiredCount})
		}
		mergedApprovalConfig.SpecificApprovalConfig.UserGroups = userGroups
		mergedApprovalConfig.SpecificApprovalConfig.SpecificUsers = bean.SpecificUsersApprovalConfig{Identifiers: maps.Keys(uniqueSpecificUsersMap)}
	}

	// we need to set the type for backward compatability
	mergedApprovalConfig.SetType()
	return mergedApprovalConfig
}

func GetEligibleApproverConfig(approvalConfig *bean.UserApprovalConfig, approvalAccessUsersData *bean.ApprovalAccessUsersInfo, userGroupIdentifierVsNameMap map[string]string) *bean.EligibleApproversInfo {
	approvalConfigDto := &bean.EligibleApproversInfo{}

	if approvalConfig.AnyApprovalApprovalConfig.Configured() {
		// any user approval info logic
		if approvalAccessUsersData != nil {
			approvableUsersInfo := make([]*bean.UserApprovalStatusInfo, 0, len(approvalAccessUsersData.UserEmails))
			for approvedUser, _ := range approvalAccessUsersData.UserEmails {
				approvableUsersInfo = append(approvableUsersInfo, &bean.UserApprovalStatusInfo{Identifier: approvedUser, HasApproved: false, CanApprove: true})
			}
			approvalConfigDto.AnyUsers.ApproverList = approvableUsersInfo
		}
	}

	if approvalConfig.SpecificApprovalConfig.Configured() {

		// specific user approval info logic
		specificUserInfos := make([]*bean.UserApprovalStatusInfo, 0, len(approvalConfig.SpecificApprovalConfig.SpecificUsers.Identifiers))
		for _, userIdentifier := range approvalConfig.SpecificApprovalConfig.SpecificUsers.Identifiers {
			canApprove := false
			if approvalAccessUsersData != nil {
				canApprove = approvalAccessUsersData.UserEmails[userIdentifier]
			}
			specificUserInfos = append(specificUserInfos, &bean.UserApprovalStatusInfo{Identifier: userIdentifier, CanApprove: canApprove})
		}
		approvalConfigDto.SpecificUsers.ApproverList = specificUserInfos

		// user group approval users info
		userGroupInfos := make([]*bean.UserGroupInfo, 0, len(approvalConfig.SpecificApprovalConfig.UserGroups))
		for _, userGroup := range approvalConfig.SpecificApprovalConfig.UserGroups {
			approvedUsersList := make([]*bean.UserApprovalStatusInfo, 0)

			if approvalAccessUsersData != nil {
				for approvedUser, _ := range approvalAccessUsersData.UserGroupIdentifierVsUserEmailsMap[userGroup.Identifier] {
					approvedUsersList = append(approvedUsersList, &bean.UserApprovalStatusInfo{Identifier: approvedUser, HasApproved: false, CanApprove: true})
				}
			}
			userGroupInfo := &bean.UserGroupInfo{
				GroupIdentifier: userGroup.Identifier,
				GroupName:       userGroupIdentifierVsNameMap[userGroup.Identifier],
				ApprovalUsersInfo: &bean.ApprovalUsersInfo{
					ApproverList: approvedUsersList,
				},
			}
			userGroupInfos = append(userGroupInfos, userGroupInfo)
		}

		approvalConfigDto.UserGroups = userGroupInfos

	}

	return approvalConfigDto
}

func setDeletedSuffixForDeletedUser(userApprovalConfigDto *bean.UserApprovalConfigDTO) *bean.UserApprovalConfigDTO {
	if userApprovalConfigDto == nil {
		return userApprovalConfigDto
	}

	if userApprovalConfigDto.AnyUserApprovalInfo != nil {
		for i, approvalInfo := range userApprovalConfigDto.AnyUserApprovalInfo.ApproverList {
			approvalInfo.Identifier = feHelper.GetModifiedDataStrIfDeleted(approvalInfo.Identifier, approvalInfo.UserDeleted)
			userApprovalConfigDto.AnyUserApprovalInfo.ApproverList[i] = approvalInfo
		}
	}

	if userApprovalConfigDto.SpecificUserApprovalInfo != nil {
		for i, approvalInfo := range userApprovalConfigDto.SpecificUserApprovalInfo.ApproverList {
			approvalInfo.Identifier = feHelper.GetModifiedDataStrIfDeleted(approvalInfo.Identifier, approvalInfo.UserDeleted)
			userApprovalConfigDto.SpecificUserApprovalInfo.ApproverList[i] = approvalInfo
		}
	}

	if userApprovalConfigDto.UserGroupsApprovalInfo != nil {
		for i, groupInfo := range userApprovalConfigDto.UserGroupsApprovalInfo.UserGroups {
			for j, approvalInfo := range groupInfo.ApproverList {
				approvalInfo.Identifier = feHelper.GetModifiedDataStrIfDeleted(approvalInfo.Identifier, approvalInfo.UserDeleted)
				groupInfo.ApproverList[j] = approvalInfo
			}
			userApprovalConfigDto.UserGroupsApprovalInfo.UserGroups[i] = groupInfo
		}
	}

	return userApprovalConfigDto
}
