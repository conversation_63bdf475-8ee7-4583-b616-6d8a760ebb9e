package DAG

import (
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig/bean/pipeline"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig/mocks"
	bean2 "github.com/devtron-labs/devtron/pkg/build/pipeline/read/bean"
	"github.com/devtron-labs/devtron/pkg/pipeline/repository"
	mocks2 "github.com/devtron-labs/devtron/pkg/pipeline/repository/mocks"
	repository2 "github.com/devtron-labs/devtron/pkg/plugin/repository"
	mocks3 "github.com/devtron-labs/devtron/pkg/plugin/repository/mocks"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/plugin/alpha1/bean"
	"github.com/devtron-labs/devtron/util/sliceUtil"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.uber.org/zap"
	"testing"
)

type getDAGForCITestCase struct {
	name                     string
	ciPipelineIds            []int
	rootCiPipelineIds        []int
	ciPipelineMinOutputs     []*bean2.CiPipelineMin
	pipelineMinOutputs       []*pipeline.PipelineMin
	pipelineStageStepOutputs []*repository.PipelineStageStep
	refPluginIds             []int
	pluginMetaDataOutputs    []*repository2.PluginMetadata
	ciPipMaterialOutputs     []*pipelineConfig.CiPipelineMaterial
	expected                 map[int]*bean.Component
	expectError              bool
}

var tests = []getDAGForCITestCase{
	testCaseHandleValidLinkedCICase(),
	testCaseHandleValidBuildCICase(),
	testCaseHandleEmptyCDNodesCase(),
	testCaseReturnsEmptyMapForEmptyInput(),
	testCaseReturnsNilMapForEmptyInput(),
}

func TestPluginDAGUtilImpl_GetDAGForCI(t *testing.T) {
	// Initialize the logger
	logger, _ := zap.NewDevelopment()
	defer logger.Sync()
	sugar := logger.Sugar()
	for index := range tests {
		t.Run(tests[index].name, func(t *testing.T) {
			tt := tests[index]
			// Initialize the repositories (use mock or real implementations)
			ciPipelineRepository := mocks.NewCiPipelineRepository(t)
			pipelineRepository := mocks.NewPipelineRepository(t)
			pipelineStageRepository := mocks2.NewPipelineStageRepository(t)
			globalPluginRepository := mocks3.NewGlobalPluginRepository(t)
			ciPipelineMaterialRepository := mocks.NewCiPipelineMaterialRepository(t)

			// Create the PluginDAGUtilImpl instance
			impl := NewPluginDAGUtilImpl(ciPipelineRepository, pipelineRepository,
				pipelineStageRepository, globalPluginRepository, ciPipelineMaterialRepository, sugar)
			if len(tt.ciPipelineIds) != 0 {
				ciPipelineRepository.
					On("FindMinSelfAndParentAndSiblingAndChildrenCiPipelines", mock.MatchedBy(
						func(ciPipelineIds []int) bool {
							return sliceUtil.CompareTwoSlices(ciPipelineIds, tt.ciPipelineIds)
						}),
					).
					Return(tt.ciPipelineMinOutputs, nil).Once()
				pipelineRepository.On("FindWithAppAndEnvMinDataByCiPipelineIdsIn", mock.MatchedBy(
					func(ciPipelineIds []int) bool {
						return sliceUtil.CompareTwoSlices(ciPipelineIds, tt.ciPipelineIds)
					}),
				).
					Return(tt.pipelineMinOutputs, nil).Once()
				pipelineStageRepository.
					On("GetConfiguredPluginsForCIPipelines", mock.MatchedBy(
						func(ciPipelineIds []int) bool {
							return sliceUtil.CompareTwoSlices(ciPipelineIds, tt.rootCiPipelineIds)
						}),
					).
					Return(tt.pipelineStageStepOutputs, nil).Once()
				if len(tt.refPluginIds) != 0 {
					globalPluginRepository.
						On("GetMetaDataByPluginIds", mock.MatchedBy(
							func(refPluginId []int) bool {
								return sliceUtil.CompareTwoSlices(refPluginId, tt.refPluginIds)
							}),
						).
						Return(tt.pluginMetaDataOutputs, nil).Once()
				}
				ciPipelineMaterialRepository.
					On("GetByCiPipelineIdsExceptUnsetRegexBranch", mock.MatchedBy(
						func(ciPipelineIds []int) bool {
							return sliceUtil.CompareTwoSlices(ciPipelineIds, tt.rootCiPipelineIds)
						}),
					).
					Return(tt.ciPipMaterialOutputs, nil).Once()
			}
			result, err := impl.GetDAGForCI(tt.ciPipelineIds, nil)
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equalf(t, tt.expected, result, "expected: %v, got: %v", tt.expected, result)
			}
		})
	}
}
