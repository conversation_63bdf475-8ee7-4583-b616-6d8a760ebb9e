package util

import (
	repository2 "github.com/devtron-labs/devtron/pkg/pipeline/repository"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/plugin/alpha1/bean"
	"github.com/devtron-labs/devtron/util/sliceUtil"
	"golang.org/x/exp/maps"
	"k8s.io/apimachinery/pkg/util/sets"
	"time"
)

// GetConfiguredPluginMap and getCDPipelineConfiguredPluginMap can be merged into a single method
// TODO: support for returning version and plugin id data
func GetConfiguredPluginMap(configuredPluginsMap map[int][]*repository2.PipelineStageStep, refPluginIdVsParentPluginIdMap map[int]int) map[int]map[bean.PluginApplyStage][]int {
	// map of {ciPipelineId: map of {pluginIdStage : bool} }
	pipelineConfiguredPluginMap := make(map[int]map[bean.PluginApplyStage][]int)
	for pipelineId, configuredPlugins := range configuredPluginsMap {
		stageWiseConfiguredPluginMap := make(map[bean.PluginApplyStage][]int)
		for _, configuredPlugin := range configuredPlugins {
			//ciPipelineId := configuredPlugin.PipelineStage.CiPipelineId
			var pluginConfiguredInStage bean.PluginApplyStage
			switch configuredPlugin.PipelineStage.Type {
			case repository2.PIPELINE_STAGE_TYPE_PRE_CI:
				pluginConfiguredInStage = bean.PluginApplyStagePreCi
			case repository2.PIPELINE_STAGE_TYPE_POST_CI:
				pluginConfiguredInStage = bean.PluginApplyStagePostCi
			case repository2.PIPELINE_STAGE_TYPE_PRE_CD:
				pluginConfiguredInStage = bean.PluginApplyStagePreCd
			case repository2.PIPELINE_STAGE_TYPE_POST_CD:
				pluginConfiguredInStage = bean.PluginApplyStagePostCd
			}

			// pluginEntityIdStr can be plugin versions id from plugin_metadata table and parent plugin id from plugin_parent_metadata table
			// for now we want to apply policy on all versions of a plugin.
			if _, ok := stageWiseConfiguredPluginMap[pluginConfiguredInStage]; ok {
				if parentPluginId, exist := refPluginIdVsParentPluginIdMap[configuredPlugin.RefPluginId]; exist {
					stageWiseConfiguredPluginMap[pluginConfiguredInStage] = append(stageWiseConfiguredPluginMap[pluginConfiguredInStage], parentPluginId)
				}
			} else {
				if parentPluginId, exist := refPluginIdVsParentPluginIdMap[configuredPlugin.RefPluginId]; exist {
					stageWiseConfiguredPluginMap[pluginConfiguredInStage] = sliceUtil.GetSliceOf(parentPluginId)
				}
			}
		}
		pipelineConfiguredPluginMap[pipelineId] = stageWiseConfiguredPluginMap
	}
	return pipelineConfiguredPluginMap
}

func GetMandatoryPlugins(policies map[int]*bean.PluginPolicyDefinition, policyIdNameMap map[int]string, policyWithLinkedPipelines map[int]map[int]*bean.LinkedPipelineApiBean) map[bean.PluginApplyStage]map[int]*bean.RuleWithSource {
	stageWiseMandatoryPluginMap := map[bean.PluginApplyStage]map[int]*bean.RuleWithSource{
		bean.PluginApplyStagePostCi: make(map[int]*bean.RuleWithSource),
		bean.PluginApplyStagePreCi:  make(map[int]*bean.RuleWithSource),
		bean.PluginApplyStagePostCd: make(map[int]*bean.RuleWithSource),
		bean.PluginApplyStagePreCd:  make(map[int]*bean.RuleWithSource),
	}

	for policyId, policyDef := range policies {
		for _, mandatoryPlugin := range policyDef.Rules {
			if ruleWithSource, thisPluginIsAlreadyFoundAtThisStage := stageWiseMandatoryPluginMap[mandatoryPlugin.ApplyToStage][mandatoryPlugin.ParentPluginId]; thisPluginIsAlreadyFoundAtThisStage {
				// this plugin is already enforced by other policies before, so append the current policy as a contributor for this plugin
				policyIds := ruleWithSource.PolicyIds
				policyNames := ruleWithSource.PolicyIdentifiers
				policyIds = append(policyIds, policyId)
				policyNames = append(policyNames, policyIdNameMap[policyId])
				consequence := ruleWithSource.Consequence
				consequence = GetMoreOrSameSeverityConsequence(consequence, policyDef.Consequence)
				if policyWithLinkedPipelines != nil {
					if linkedCiPipelines, ok := policyWithLinkedPipelines[policyId]; ok {
						existingLinkedCiPipelines := ruleWithSource.LinkedPipelines
						ruleWithSource.LinkedPipelines = append(existingLinkedCiPipelines, maps.Values(linkedCiPipelines)...)
					}
				}
				ruleWithSource.PolicyIds = policyIds
				ruleWithSource.PolicyIdentifiers = policyNames
				ruleWithSource.Consequence = consequence
				stageWiseMandatoryPluginMap[mandatoryPlugin.ApplyToStage][mandatoryPlugin.ParentPluginId] = ruleWithSource
			} else {
				// this plugin is enforced by this policy for the first time
				newRuleWithSource := &bean.RuleWithSource{
					Rule:              mandatoryPlugin,
					PolicyIds:         sliceUtil.GetSliceOf(policyId),
					PolicyIdentifiers: sliceUtil.GetSliceOf(policyIdNameMap[policyId]),
					Consequence:       policyDef.Consequence,
				}
				if policyWithLinkedPipelines != nil {
					if linkedCiPipelines, ok := policyWithLinkedPipelines[policyId]; ok {
						newRuleWithSource.LinkedPipelines = maps.Values(linkedCiPipelines)
					}
				}
				stageWiseMandatoryPluginMap[mandatoryPlugin.ApplyToStage][mandatoryPlugin.ParentPluginId] = newRuleWithSource
			}
		}
	}
	return stageWiseMandatoryPluginMap
}

// GetMissingMandatoryPlugins returns the list of mandatory plugins that are not configured on the pipeline
// TODO: handle bean.PluginVersionTypeFix plugins verification here
// TODO: handle bean.PluginVersionTypeGreaterThanEqualTo plugins verification here
func GetMissingMandatoryPlugins(pluginsConfiguredOnThisStage []int, mandatoryPluginsForThisStage map[int]*bean.RuleWithSource) []*bean.RuleWithSource {
	if len(pluginsConfiguredOnThisStage) == 0 {
		return maps.Values(mandatoryPluginsForThisStage)
	}

	missingMandatoryPlugins := make([]*bean.RuleWithSource, 0, len(mandatoryPluginsForThisStage))
	configuredPluginMap := sets.NewInt(pluginsConfiguredOnThisStage...)
	for _, mandatoryPlugin := range mandatoryPluginsForThisStage {
		if mandatoryPlugin == nil {
			continue
		}
		if mandatoryPlugin.ApplyToVersion == bean.PluginVersionTypeAll {
			if !configuredPluginMap.Has(mandatoryPlugin.ParentPluginId) {
				missingMandatoryPlugins = append(missingMandatoryPlugins, mandatoryPlugin)
			}
		}
	}

	return missingMandatoryPlugins
}

func ComputeMostSevereConsequence(missingMandatoryPlugins []*bean.RuleWithSource) *bean.Consequence {
	consequence := &bean.Consequence{
		Action: bean.ConsequenceActionAllowForever,
	}
	for _, missingMandatoryPlugin := range missingMandatoryPlugins {
		consequence = GetMoreOrSameSeverityConsequence(consequence, missingMandatoryPlugin.Consequence)
	}

	return consequence
}

func IsConsequenceBlocking(consequence *bean.Consequence) bool {
	if consequence.Action == bean.ConsequenceActionBlock {
		return true
	}

	if consequence.Action == bean.ConsequenceActionAllowUntilTime {
		return consequence.MetadataField.Before(time.Now())
	}

	return false
}

// MergeSlice , make it generic
func MergeSlice(s1, s2 []*bean.RuleWithSource) []*bean.RuleWithSource {
	sl1, sl2 := len(s1), len(s2)
	finalSlice := make([]*bean.RuleWithSource, sl1+sl2)
	ptr := 0
	for _, val := range s1 {
		finalSlice[ptr] = val
		ptr++
	}
	for _, val := range s2 {
		finalSlice[ptr] = val
		ptr++
	}
	return finalSlice
}
