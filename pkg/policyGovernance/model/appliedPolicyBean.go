package model

import (
	appBean "github.com/devtron-labs/devtron/pkg/app/bean"
	"github.com/devtron-labs/devtron/pkg/bean/common/patchQuery"
	"github.com/devtron-labs/devtron/pkg/devtronResource/bean"
	"github.com/devtron-labs/devtron/pkg/resourceQualifiers/common"
)

type ProfileSelectorDto struct {
	Policies Profile `json:"policies" validate:"required, dive"`
	// Deprecated: use policies instead
	Profiles     []Profile       `json:"profiles"`
	Selectors    []Selector      `json:"selectors" validate:"required,min=1,dive"`
	ApplicableTo *ApplicableType `json:"applicableTo" validate:"required,dive"`
}

type PatchBulkProfileSelectorRequest struct {
	PoliciesToAdd    Profile `json:"policiesToAdd"`
	PoliciesToRemove Profile `json:"policiesToRemove"`
	//Deprecated: use PoliciesToAdd
	ProfilesToAdd []Profile `json:"profilesToAdd"`
	//Deprecated: use PoliciesToRemove
	ProfilesToRemove []Profile `json:"profilesToRemove"`
	Selectors        []int     `json:"selectors" validate:"required,min=1"`
}

type PatchProfileSelectorRequest struct {
	Policies        Profile              `json:"policies" validate:"required"`
	PolicyOperation patchQuery.Operation `json:"-"` // default is replace
	Selector        int                  `json:"selector" validate:"required"`
	ApplicableTo    *ApplicableType      `json:"applicableTo" validate:"required"`
}

type ProfileSelectorList struct {
	Policies *Profile `json:"policies"`
	// Deprecated: use policies instead
	Profiles     []*Profile      `json:"profiles"`
	Selector     *Selector       `json:"selector"`
	ApplicableTo *ApplicableType `json:"applicableTo" validate:"required,dive"`
}

type ProfileSelectorResponse struct {
	Count    int                    `json:"count"`
	DataList []*ProfileSelectorList `json:"dataList"`
}

type Profile struct {
	Type              PathVariablePolicyType `json:"type" validate:"oneof=plugin image-promotion deployment-window lock-configuration approval"`
	Identifiers       []string               `json:"identifiers,omitEmpty"`
	IdentifierDetails []*IdentifierDetails   `json:"identifierDetails,omitEmpty"`
}

type IdentifierDetails struct {
	Type       string `json:"type"`
	Id         int    `json:"id,omitEmpty"`
	Identifier string `json:"identifier,omitEmpty"`
}

type Selector struct {
	Category   common.ApplyCategory `json:"category" validate:"oneof=Specific Criteria Global"`
	Identifier int                  `json:"identifier"`
	Matches    []*Match             `json:"matches,omitempty" validate:"dive"`
	Note       bean.NoteBean        `json:"note,omitempty"`
}

type Match struct {
	ResourceFilterDto
	IdentifierNameToIdMap map[string]int `json:"-"`
}

type ResourceFilterDto struct {
	Kind        bean.DtResKind       `json:"kind" validate:"required"`
	Identifiers []string             `json:"identifiers,omitempty"` // Deprecated, instead use Attributes with key bean.Identifier. Maintained for backward compatibility
	Attributes  []*AttributeResponse `json:"attributes,omitempty" validate:"dive"`
}

func NewResourceFilterDto(kind bean.DtResKind) *ResourceFilterDto {
	return &ResourceFilterDto{
		Kind: kind,
	}
}

func (r *ResourceFilterDto) ToMatch() *Match {
	if r == nil {
		return &Match{}
	}
	return &Match{
		ResourceFilterDto: *r,
	}
}

func (r *ResourceFilterDto) ToSelectorsListResponse() *SelectorsListResponse {
	if r == nil {
		return &SelectorsListResponse{}
	}
	return &SelectorsListResponse{
		ResourceFilterDto: *r,
	}
}

func (r *ResourceFilterDto) WithAttributes(attributes ...*AttributeResponse) *ResourceFilterDto {
	for _, attribute := range attributes {
		if attribute != nil {
			r.Attributes = append(r.Attributes, attribute)
		}
	}
	return r
}

// Deprecated: WithIdentifier is deprecated, instead use WithAttributes with attribute key bean.Identifier. Maintained for backward compatibility
func (r *ResourceFilterDto) WithIdentifier(identifiers ...string) *ResourceFilterDto {
	r.Identifiers = append(r.Identifiers, identifiers...)
	return r
}

type SelectorsListResponse struct {
	ResourceFilterDto
}

type AttributeResponse struct {
	Key    bean.FilterCriteriaIdentifier `json:"key" validate:"required"`
	Values []string                      `json:"values"`
}

type PolicyAppEnvFilterRequest struct {
	appBean.AppEnvFilterRequest
	Policies                []string `json:"policies"`
	ApplicableTo            []string `json:"applicableTo"`
	PluginParentIdentifiers []string `json:"pluginParentIdentifiers"`
}

type ApplicableType struct {
	Type   ApplyPolicyType `json:"type"`
	Values []*ConfigValue  `json:"values,omitempty"`
}

type ConfigValue struct {
	Identifier string `json:"identifier"`
}
