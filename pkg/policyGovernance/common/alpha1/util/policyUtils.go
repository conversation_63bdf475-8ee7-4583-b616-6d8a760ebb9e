package util

import (
	"errors"
	"fmt"
	bean4 "github.com/devtron-labs/devtron/pkg/auth/user/bean"
	"github.com/devtron-labs/devtron/pkg/bean/common/patchQuery"
	"github.com/devtron-labs/devtron/pkg/devtronResource/bean"
	bean2 "github.com/devtron-labs/devtron/pkg/globalPolicy/bean"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/adapter"
	bean3 "github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/bean"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/model"
	"github.com/devtron-labs/devtron/pkg/resourceQualifiers/common"
	globalUtil "github.com/devtron-labs/devtron/util"
	"github.com/devtron-labs/devtron/util/sliceUtil"
	"slices"
	"strings"
)

func SimplifyMatchesForSelectors(Selectors []model.Selector) []model.Selector {
	result := make([]model.Selector, 0)
	for _, selector := range Selectors {
		if selector.Category == common.GLOBAL_CATEGORY {
			result = append(result, selector)
		} else if selector.Category == common.SPECIFIC_CATEGORY {
			var appNames, envNames []string
			var isBaseDeployment, isBaseConfiguration bool
			for _, match := range selector.Matches {
				identifiers := adapter.GetAttributeValuesByKey(match.Attributes, bean.Identifier)
				switch match.Kind {
				case bean.DevtronResourceDevtronApplicationFull:
					appNames = append(appNames, identifiers...)
					appNames = append(appNames, match.Identifiers...) // maintained for backward compatibility
				case bean.DevtronResourceEnvironment:
					envNames = append(envNames, identifiers...)
					envNames = append(envNames, match.Identifiers...) // maintained for backward compatibility
				case bean.DevtronResourceBaseTemplate:
					isBaseDeployment = true
				case bean.DevtronResourceBaseConfiguration:
					isBaseConfiguration = true
				}
			}

			if isBaseDeployment || isBaseConfiguration {
				for _, appName := range appNames {
					appMatch := adapter.NewMatch(
						bean.DevtronResourceDevtronApplicationFull,
						adapter.NewAttribute(bean.IdentifierKey, sliceUtil.GetSliceOf(appName)),
					)
					selector.Matches = []*model.Match{appMatch}
					if isBaseDeployment {
						baseDeploymentMatch := adapter.NewMatch(bean.DevtronResourceBaseTemplate)
						selector.Matches = append(selector.Matches, baseDeploymentMatch)
					}
					if isBaseConfiguration {
						baseConfigurationMatch := adapter.NewMatch(bean.DevtronResourceBaseConfiguration)
						selector.Matches = append(selector.Matches, baseConfigurationMatch)
					}

					result = append(result, selector)
				}
			} else {
				for _, appName := range appNames {
					for _, envName := range envNames {
						appMatch := adapter.NewMatch(
							bean.DevtronResourceDevtronApplicationFull,
							adapter.NewAttribute(bean.IdentifierKey, sliceUtil.GetSliceOf(appName)),
						)

						envMatch := adapter.NewMatch(
							bean.DevtronResourceEnvironment,
							adapter.NewAttribute(bean.IdentifierKey, sliceUtil.GetSliceOf(envName)),
						)

						selector.Matches = []*model.Match{appMatch, envMatch}
						result = append(result, selector)
					}
				}
			}

		} else {
			var appNames, envNames, clusterNames, projectNames, branchNames, branchRegexes []string
			var isBaseDeployment, isBaseConfiguration bool

			for _, match := range selector.Matches {
				identifiers := adapter.GetAttributeValuesByKey(match.Attributes, bean.Identifier)
				switch match.Kind {
				case bean.DevtronResourceDevtronApplicationFull:
					if len(appNames) == 0 {
						appNames = make([]string, 0)
					}
					appNames = append(appNames, identifiers...)
					appNames = append(appNames, match.Identifiers...) // maintained for backward compatibility
				case bean.DevtronResourceEnvironment:
					if len(envNames) == 0 {
						envNames = make([]string, 0)
					}
					envNames = append(envNames, identifiers...)
					envNames = append(envNames, match.Identifiers...) // maintained for backward compatibility
				case bean.DevtronResourceBaseTemplate:
					isBaseDeployment = true
				case bean.DevtronResourceBaseConfiguration:
					isBaseConfiguration = true
				case bean.DevtronResourceCluster:
					if len(clusterNames) == 0 {
						clusterNames = make([]string, 0)
					}
					clusterNames = append(clusterNames, identifiers...)
					clusterNames = append(clusterNames, match.Identifiers...) // maintained for backward compatibility
				case bean.DevtronResourceProject:
					if len(projectNames) == 0 {
						projectNames = make([]string, 0)
					}
					projectNames = append(projectNames, identifiers...)
					projectNames = append(projectNames, match.Identifiers...) // maintained for backward compatibility
				case bean.DevtronResourceCiPipeline:
					if len(branchNames) == 0 {
						branchNames = make([]string, 0)
					}
					branchNames = append(branchNames, adapter.GetAttributeValuesByKey(match.Attributes, bean.Branch)...)
					if len(branchRegexes) == 0 {
						branchRegexes = make([]string, 0)
					}
					branchRegexes = append(branchRegexes, adapter.GetAttributeValuesByKey(match.Attributes, bean.BranchRegex)...)
				}
			}

			var matches []*model.Match
			for _, appName := range appNames {
				appMatch := adapter.NewMatch(
					bean.DevtronResourceDevtronApplicationFull,
					adapter.NewAttribute(bean.IdentifierKey, sliceUtil.GetSliceOf(appName)),
				)
				matches = append(matches, appMatch)
			}

			for _, projectName := range projectNames {
				projectMatch := adapter.NewMatch(
					bean.DevtronResourceProject,
					adapter.NewAttribute(bean.IdentifierKey, sliceUtil.GetSliceOf(projectName)),
				)
				matches = append(matches, projectMatch)
			}

			if isBaseDeployment || isBaseConfiguration {
				if isBaseDeployment {
					baseDeploymentMatch := adapter.NewMatch(
						bean.DevtronResourceBaseTemplate,
					)
					matches = append(matches, baseDeploymentMatch)
				}

				if isBaseConfiguration {
					baseConfigurationMatch := adapter.NewMatch(
						bean.DevtronResourceBaseConfiguration,
					)
					matches = append(matches, baseConfigurationMatch)
				}

			} else {
				for _, envName := range envNames {
					envMatch := adapter.NewMatch(
						bean.DevtronResourceEnvironment,
						adapter.NewAttribute(bean.IdentifierKey, sliceUtil.GetSliceOf(envName)),
					)
					matches = append(matches, envMatch)
				}
				for _, clusterName := range clusterNames {
					clusterMatch := adapter.NewMatch(
						bean.DevtronResourceCluster,
						adapter.NewAttribute(bean.IdentifierKey, sliceUtil.GetSliceOf(clusterName)),
					)
					matches = append(matches, clusterMatch)
				}
			}
			if len(branchNames) != 0 || len(branchRegexes) != 0 {
				for _, branchName := range branchNames {
					ciPipelineMatch := adapter.NewMatch(
						bean.DevtronResourceCiPipeline,
						adapter.NewAttribute(bean.Branch, sliceUtil.GetSliceOf(branchName)),
					)
					matches = append(matches, ciPipelineMatch)
				}
				for _, branchRegex := range branchRegexes {
					ciPipelineMatch := adapter.NewMatch(
						bean.DevtronResourceCiPipeline,
						adapter.NewAttribute(bean.BranchRegex, sliceUtil.GetSliceOf(branchRegex)),
					)
					matches = append(matches, ciPipelineMatch)
				}
			}
			selector.Matches = matches
			result = append(result, selector)
		}
	}
	return result
}

func UpdateApplyRequestIfOldPayload(dto *model.ProfileSelectorDto) {
	if len(dto.Profiles) > 0 {
		dto.Policies = dto.Profiles[0]
	}
	if dto.ApplicableTo == nil || len(dto.ApplicableTo.Type) == 0 {
		dto.ApplicableTo = GetApplicableToForPolicyOf(&dto.Policies)
	}
}

func GetApplicableToForPolicyOf(policies *model.Profile) *model.ApplicableType {
	return &model.ApplicableType{
		Type:   model.ApplyPolicyType(policies.Type),
		Values: []*model.ConfigValue{},
	}
}

func GetApplyPolicyTypeForApplicableToAndPathVariablePolicyType(pathVariablePolicyType model.PathVariablePolicyType, applicableTo *model.ApplicableType) model.ApplyPolicyType {
	return GetApplyPolicyTypeForApplicableToAndGlobalPolicyType(model.PathPolicyTypeToGlobalPolicyTypeMap[pathVariablePolicyType], applicableTo)
}

// Below map is to be only used by GetApplyPolicyTypeForApplicableToAndGlobalPolicyType() func
var globalPolicyTypeToApplyPolicyMap = map[bean2.GlobalPolicyType]model.ApplyPolicyType{
	bean2.GLOBAL_POLICY_TYPE_IMAGE_PROMOTION_POLICY: model.APPLY_POLICY_IMAGE_PROMOTION,
	bean2.GLOBAL_POLICY_TYPE_DEPLOYMENT_WINDOW:      model.APPLY_POLICY_DEPLOYMENT_WINDOW,
	bean2.LOCK_CONFIGURATION:                        model.APPLY_POLICY_LOCK_CONFIGURATION,
	bean2.GLOBAL_POLICY_TYPE_PLUGIN:                 model.APPLY_POLICY_MANDATORY_PLUGIN,
}

// Below map is to be only used by GetPathVariableTypeForApplicableTo() func
var applyPolicyTypeToPathvariablePolicyMap = map[model.ApplyPolicyType]model.PathVariablePolicyType{
	model.APPLY_POLICY_IMAGE_PROMOTION:    model.ImagePromotion,
	model.APPLY_POLICY_DEPLOYMENT_WINDOW:  model.DeploymentWindow,
	model.APPLY_POLICY_LOCK_CONFIGURATION: model.LockConfiguration,
	model.APPLY_POLICY_MANDATORY_PLUGIN:   model.MandatoryPlugin,
}

func GetApplicableToForOperation(applicableToBeUpdated *model.ApplicableType, currentApplicableTo *model.ApplicableType, operation patchQuery.Operation) (*model.ApplicableType, error) {
	if applicableToBeUpdated == nil || currentApplicableTo == nil {
		return nil, errors.New("invalid payload of applicableTo")
	}
	if applicableToBeUpdated.Type != currentApplicableTo.Type {
		return nil, errors.New("type change of applicableTo is not supported")
	}
	if operation == patchQuery.Add {
		for _, applicableToValue := range applicableToBeUpdated.Values {
			if !checkIfApplicableToValueExists(currentApplicableTo.Values, applicableToValue) {
				currentApplicableTo.Values = append(currentApplicableTo.Values, applicableToValue)
			}
		}
		return currentApplicableTo, nil
	} else if operation == patchQuery.Remove {
		return nil, errors.New("Remove not supported yet!!")
	} else {
		// replace
		return applicableToBeUpdated, nil
	}
}

func checkIfApplicableToValueExists(applicableToValues []*model.ConfigValue, tobeCheckedValue *model.ConfigValue) bool {
	if len(applicableToValues) == 0 {
		return false
	}
	if tobeCheckedValue == nil {
		return true
	}
	for _, value := range applicableToValues {
		if value.Identifier == tobeCheckedValue.Identifier {
			return true
		}
	}
	return false

}

func GetPolicyIdentifiersForOperation(tobeUpdatedPolicyIdentifiers []int, currentPolicyIdentifiers []int, operation patchQuery.Operation) []int {
	if operation == patchQuery.Add {
		for _, policyIdentifierId := range tobeUpdatedPolicyIdentifiers {
			if !slices.Contains(currentPolicyIdentifiers, policyIdentifierId) {
				currentPolicyIdentifiers = append(currentPolicyIdentifiers, policyIdentifierId)
			}
		}
		return currentPolicyIdentifiers
	} else if operation == patchQuery.Remove {
		for _, policyIdToRemove := range tobeUpdatedPolicyIdentifiers {
			if slices.Contains(currentPolicyIdentifiers, policyIdToRemove) {
				idx := slices.Index(currentPolicyIdentifiers, policyIdToRemove)
				newIdentifiers := currentPolicyIdentifiers[0:idx]
				if idx+1 < len(currentPolicyIdentifiers) {
					newIdentifiers = append(newIdentifiers, currentPolicyIdentifiers[idx+1:]...)
				}
				currentPolicyIdentifiers = newIdentifiers
			}
		}
		return currentPolicyIdentifiers
	} else {
		// replace
		return tobeUpdatedPolicyIdentifiers
	}
}

func GetApplyPolicyTypeForApplicableToAndGlobalPolicyType(globalPolicyType bean2.GlobalPolicyType, applicableTo *model.ApplicableType) model.ApplyPolicyType {
	if globalPolicyType == bean2.GLOBAL_POLICY_TYPE_APPROVAL {
		return applicableTo.Type
	}
	return globalPolicyTypeToApplyPolicyMap[globalPolicyType]
}
func GetApplyPolicyTypeForPathVariablePolicyType(pathVariablePolicyType model.PathVariablePolicyType) []model.ApplyPolicyType {
	return GetApplyPolicyTypeForGlobalPolicyType(model.PathPolicyTypeToGlobalPolicyTypeMap[pathVariablePolicyType])
}

func GetApplyPolicyTypeForGlobalPolicyType(globalPolicyType bean2.GlobalPolicyType) []model.ApplyPolicyType {
	if globalPolicyType == bean2.GLOBAL_POLICY_TYPE_APPROVAL {
		return []model.ApplyPolicyType{model.APPLY_POLICY_APPROVAL_DEPLOYMENT, model.APPLY_POLICY_APPROVAL_CONFIGURATION}
	}
	return []model.ApplyPolicyType{globalPolicyTypeToApplyPolicyMap[globalPolicyType]}
}

func GetPathVariableTypeForApplicableTo(applicableTo *model.ApplicableType) model.PathVariablePolicyType {
	applyPolicyType := applicableTo.Type
	if applyPolicyType == model.APPLY_POLICY_APPROVAL_DEPLOYMENT || applyPolicyType == model.APPLY_POLICY_APPROVAL_CONFIGURATION {
		return model.Approval
	}
	return applyPolicyTypeToPathvariablePolicyMap[applyPolicyType]
}

func CheckIfApplicableToFilters(policyTypeDetail *model.ApplicableType, applicableToFilter []string) bool {
	if len(applicableToFilter) == 0 {
		return true
	}
	if policyTypeDetail == nil {
		return false
	}
	for _, applicableValue := range applicableToFilter {
		if applicableValue == bean3.APPROVAL_FOR_DEPLOYMENT.ToString() &&
			policyTypeDetail.Type == model.APPLY_POLICY_APPROVAL_DEPLOYMENT {
			return true
		} else {
			for _, policyDetailValue := range policyTypeDetail.Values {
				if policyDetailValue.Identifier == applicableValue {
					return true
				}
			}
		}
	}

	return false
}

func GetApplyPolicyTypeForFilterRequest(applicableTo []string, pathVariablePolicyType model.PathVariablePolicyType) []model.ApplyPolicyType {
	if len(applicableTo) > 0 {
		resp := make([]model.ApplyPolicyType, 0)
		for _, applicableToDetail := range applicableTo {
			if applicableToDetail == bean3.APPROVAL_FOR_CONFIGURATION_CM.ToString() ||
				applicableToDetail == bean3.APPROVAL_FOR_CONFIGURATION_CS.ToString() ||
				applicableToDetail == bean3.APPROVAL_FOR_CONFIGURATION_DT.ToString() {
				resp = append(resp, model.APPLY_POLICY_APPROVAL_CONFIGURATION)
			}
			if applicableToDetail == bean3.APPROVAL_FOR_DEPLOYMENT.ToString() {
				resp = append(resp, model.APPLY_POLICY_APPROVAL_DEPLOYMENT)
			}
		}
		return resp
	}
	return GetApplyPolicyTypeForPathVariablePolicyType(pathVariablePolicyType)
}

const PRE_COMPUTED_POLICY_IDS = "preComputedPolicyIds"

func ConvertInterfaceSliceToStringSlice(req interface{}) []string {
	interfaceSlice, ok := req.([]interface{})
	if !ok {
		return []string{}
	}
	stringSlice := make([]string, len(interfaceSlice))
	for i, v := range interfaceSlice {
		if str, ok := v.(string); ok {
			stringSlice[i] = str
		}
	}
	return stringSlice
}

// GetNewExceptionPolicyName returns unique exception policy names everytime it's called, for e.g.- exception-{someRandomString}
func GetNewExceptionPolicyName() string {
	return fmt.Sprintf("%s-%s", "exception", globalUtil.GetRandomStringOfGivenLength(5))
}

// RemoveInactiveStatus removes the " (inactive)" suffix from email strings
// Example: "<EMAIL> (inactive)" -> "<EMAIL>"
func RemoveInactiveStatus(email string) string {
	// Check if the string contains the inactive suffix
	inactiveSuffix := fmt.Sprintf(" %s", bean4.InactiveUserStatusPostFix)
	if idx := strings.Index(email, inactiveSuffix); idx != -1 {
		return email[:idx]
	}
	return email
}

func AppendInactivePostFixInEmail(email string) string {
	return fmt.Sprintf("%s %s", email, bean4.InactiveUserStatusPostFix)
}
