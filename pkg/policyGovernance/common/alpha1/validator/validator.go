package validator

import (
	"fmt"
	"github.com/devtron-labs/devtron/internal/util"
	"github.com/devtron-labs/devtron/pkg/bean/common/patchQuery"
	error2 "github.com/devtron-labs/devtron/pkg/policyGovernance/common/alpha1/error"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/model"
	"net/http"
)

func ValidateExceptionUsersRequest(apiVersion string, policyType model.PathVariablePolicyType) error {
	if len(apiVersion) == 0 {
		return util.NewApiError(http.StatusBadRequest, error2.EmptyApiVersionErr, error2.EmptyApiVersionErr)
	}
	// currently exception users is only supported in approval policy type, if req later remove this validation for multiple policyType
	isApprovalBypassFeatValidForPolicyType := isBypassMechanismValidForPolicyType(policyType)
	if !isApprovalBypassFeatValidForPolicyType {
		errMsg := fmt.Sprintf(error2.BypassApprovalFeatNotSupportedErr, policyType.ToString())
		return util.NewApiError(http.StatusBadRequest, errMsg, errMsg)
	}
	return nil
}

// isBypassMechanismValidForPolicyType returns if a policy type passed via param is currently supported by our system or not.
func isBypassMechanismValidForPolicyType(policyType model.PathVariablePolicyType) bool {
	return model.AllPolicyTypesSupportingBypass[policyType]
}

func ValidatePatchPayloadForExceptionUsersRequest(patchPayload *model.ExceptionUsersPatchPayload) (patchQuery.Operation, error) {
	// nil check for applicable to
	if patchPayload.ApplicableTo == nil {
		return "", util.NewApiError(http.StatusBadRequest, error2.InvalidApplicableToInPatchPayloadErr, error2.InvalidApplicableToInPatchPayloadErr)
	}
	var op patchQuery.Operation
	// verify if all operations in query are of same type, if not then throw err currently we only expect single operation type in patch payload
	for index, query := range patchPayload.PatchQuery {
		if index == 0 {
			op = query.Operation
			continue
		}
		if patchPayload.PatchQuery[index].Operation != patchPayload.PatchQuery[index-1].Operation {
			return "", util.NewApiError(http.StatusBadRequest, error2.InCompatibleOperationsInPatchQueryErr, error2.InCompatibleOperationsInPatchQueryErr)
		}
	}
	return op, nil
}
