/*
 * Copyright (c) 2024. Devtron Inc.
 */

package alpha1

import (
	"github.com/devtron-labs/devtron/pkg/app"
	"github.com/devtron-labs/devtron/pkg/auth/user"
	userrepo "github.com/devtron-labs/devtron/pkg/auth/user/repository"
	"github.com/devtron-labs/devtron/pkg/cluster"
	"github.com/devtron-labs/devtron/pkg/cluster/environment"
	dtResourceBean "github.com/devtron-labs/devtron/pkg/devtronResource/bean"
	dtResourceService "github.com/devtron-labs/devtron/pkg/devtronResource/read"
	"github.com/devtron-labs/devtron/pkg/globalPolicy"
	globalPolicyBean "github.com/devtron-labs/devtron/pkg/globalPolicy/bean"
	pluginService "github.com/devtron-labs/devtron/pkg/plugin"
	"github.com/devtron-labs/devtron/pkg/resourceQualifiers"
	"github.com/devtron-labs/devtron/pkg/sql"
	"github.com/devtron-labs/devtron/pkg/team/read"
	"go.uber.org/zap"
)

type CommonPolicyActionsService interface {
	// Policy Profiles CRUD Functions -----------------------------------

	CommonPolicyProfileService

	// Apply Profiles Functions -----------------------------------

	CommonPolicyApplyService

	// Migrate Profiles Functions ---------------------------------

	CommonPolicyDataMigrator
}

type CommonPolicyActionsServiceImpl struct {
	globalPolicyDataManager         globalPolicy.GlobalPolicyDataManager
	resourceQualifierMappingService resourceQualifiers.QualifierMappingService
	appService                      app.AppService
	environmentService              environment.EnvironmentService
	clusterService                  cluster.ClusterService
	globalPluginService             pluginService.GlobalPluginService
	logger                          *zap.SugaredLogger
	transactionManager              sql.TransactionWrapper
	criteriaQualifierMappingService resourceQualifiers.CriteriaQualifierMappingService
	userRepository                  userrepo.UserRepository
	handleOldDataFunc               map[globalPolicyBean.GlobalPolicyType]HandleOldDataFuncType
	crudEventPreOpValidators        map[globalPolicyBean.GlobalPolicyType][]HandlerPreOpPolicyCrud
	policyBeanToDtoConverters       map[globalPolicyBean.GlobalPolicyType]PolicyBeanToDtoConverter
	policyDefinitionTypeFunc        map[globalPolicyBean.GlobalPolicyType]PolicyDefinitionTypeFunc
	policyDtoToBeanConverters       map[globalPolicyBean.GlobalPolicyType]PolicyDtoToBeanConverter
	searchableIdMap                 map[dtResourceBean.DtResSearchableKeyName]int
	TeamReadService                 read.TeamReadService
	userService                     user.UserService
}

func NewCommonPolicyActionsServiceImpl(
	globalPolicyDataManager globalPolicy.GlobalPolicyDataManager,
	resourceQualifierMappingService resourceQualifiers.QualifierMappingService,
	appService app.AppService,
	environmentService environment.EnvironmentService,
	clusterService cluster.ClusterService,
	globalPluginService pluginService.GlobalPluginService,
	criteriaQualifierMappingService resourceQualifiers.CriteriaQualifierMappingService,
	userRepository userrepo.UserRepository,
	logger *zap.SugaredLogger, transactionManager sql.TransactionWrapper,
	devtronResourceSearchableKeyService dtResourceService.DevtronResourceSearchableKeyService,
	TeamReadService read.TeamReadService,
	userService user.UserService,
) *CommonPolicyActionsServiceImpl {
	crudEventValidators := make(map[globalPolicyBean.GlobalPolicyType][]HandlerPreOpPolicyCrud)
	policyBeanToDtoConverters := map[globalPolicyBean.GlobalPolicyType]PolicyBeanToDtoConverter{}
	policyDtoToBeanConverters := map[globalPolicyBean.GlobalPolicyType]PolicyDtoToBeanConverter{}
	policyDefinitionTypeFunc := make(map[globalPolicyBean.GlobalPolicyType]PolicyDefinitionTypeFunc)
	migrateOldDataFunc := make(map[globalPolicyBean.GlobalPolicyType]HandleOldDataFuncType)
	searchableIdMap := devtronResourceSearchableKeyService.GetAllSearchableKeyNameIdMap()
	impl := &CommonPolicyActionsServiceImpl{
		globalPolicyDataManager:         globalPolicyDataManager,
		resourceQualifierMappingService: resourceQualifierMappingService,
		logger:                          logger,
		appService:                      appService,
		environmentService:              environmentService,
		transactionManager:              transactionManager,
		crudEventPreOpValidators:        crudEventValidators,
		clusterService:                  clusterService,
		globalPluginService:             globalPluginService,
		criteriaQualifierMappingService: criteriaQualifierMappingService,
		userRepository:                  userRepository,
		searchableIdMap:                 searchableIdMap,
		policyBeanToDtoConverters:       policyBeanToDtoConverters,
		policyDtoToBeanConverters:       policyDtoToBeanConverters,
		policyDefinitionTypeFunc:        policyDefinitionTypeFunc,
		handleOldDataFunc:               migrateOldDataFunc,
		TeamReadService:                 TeamReadService,
		userService:                     userService,
	}
	return impl
}
