package alpha1

import (
	globalPolicyBean "github.com/devtron-labs/devtron/pkg/globalPolicy/bean"
	globalUtil "github.com/devtron-labs/devtron/util"
)

type CommonPolicyDataMigrator interface {
	// HandleOldData is used to handle old data for lock configuration legacy data
	HandleOldData(ctx *globalUtil.RequestCtx, policyType globalPolicyBean.GlobalPolicyType) error
}

func (impl *CommonPolicyActionsServiceImpl) HandleOldData(ctx *globalUtil.RequestCtx, policyType globalPolicyBean.GlobalPolicyType) error {
	if impl.handleOldDataFunc != nil && impl.handleOldDataFunc[policyType] != nil {
		return impl.handleOldDataFunc[policyType](ctx, policyType, impl)
	}
	return nil
}
