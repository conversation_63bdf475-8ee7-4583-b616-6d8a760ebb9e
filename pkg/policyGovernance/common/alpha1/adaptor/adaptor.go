package adaptor

import (
	globalPolicyBean "github.com/devtron-labs/devtron/pkg/globalPolicy/bean"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/model"
	"github.com/devtron-labs/devtron/pkg/resourceQualifiers"
	"github.com/devtron-labs/devtron/pkg/sql"
	"github.com/devtron-labs/devtron/util/rand"
	"time"
)

// RqmResourceTypeToApplyPolicyType converts resource_qualifier_mapping.resource_type to
// corresponding ApplyPolicyType when resource_type is for exception users policy
func RqmResourceTypeToApplyPolicyType(resourceType resourceQualifiers.ResourceType) model.ApplyPolicyType {
	switch resourceType {
	case resourceQualifiers.ApprovalConfiguration:
		return model.APPLY_POLICY_APPROVAL_CONFIGURATION
	case resourceQualifiers.ApprovalDeployment:
		return model.APPLY_POLICY_APPROVAL_DEPLOYMENT
		// cases to be added when exception user feat being used by other policy types
	}
	return model.APPLY_POLICY_APPROVAL_CONFIGURATION
}

// ApplyPolicyTypeToRqmResourceType converts ApplyPolicyType to corresponding
// resource_qualifier_mapping.resource_type where resource_type is for exception users policy
func ApplyPolicyTypeToRqmResourceType(applyPolicyType model.ApplyPolicyType) resourceQualifiers.ResourceType {
	switch applyPolicyType {
	case model.APPLY_POLICY_APPROVAL_CONFIGURATION:
		return resourceQualifiers.ApprovalConfiguration
	case model.APPLY_POLICY_APPROVAL_DEPLOYMENT:
		return resourceQualifiers.ApprovalDeployment
		// cases to be added when exception user feat being used by other policy types
	}
	return resourceQualifiers.ApprovalConfiguration
}

// GetRqmResourceTypeSliceByApplyPolicyType returns []resourceQualifiers.ResourceType by applyPolicyType
func GetRqmResourceTypeSliceByApplyPolicyType(applyPolicyType model.ApplyPolicyType) []resourceQualifiers.ResourceType {
	return []resourceQualifiers.ResourceType{ApplyPolicyTypeToRqmResourceType(applyPolicyType)}
}

func NewGlobalExceptionUserPolicyQualifierMappingDao(identifierKey int, resourceType resourceQualifiers.ResourceType, globalPolicyId int, userId int32) *resourceQualifiers.QualifierMapping {
	return &resourceQualifiers.QualifierMapping{
		ResourceType:   resourceType,
		IdentifierKey:  identifierKey,
		Active:         true,
		GlobalPolicyId: globalPolicyId,
		AuditLog: sql.AuditLog{
			CreatedOn: time.Now(),
			CreatedBy: userId,
			UpdatedOn: time.Now(),
			UpdatedBy: userId,
		},
	}
}

func NewExceptionTypeGlobalPolicyDataModel(policyJson string, userId int32, name string, policyVersion globalPolicyBean.GlobalPolicyVersion) *globalPolicyBean.GlobalPolicyDataModel {
	return &globalPolicyBean.GlobalPolicyDataModel{
		GlobalPolicyBaseModel: globalPolicyBean.GlobalPolicyBaseModel{
			Name:           name,
			Enabled:        true,
			PolicyOf:       globalPolicyBean.GLOBAL_POLICY_TYPE_EXCEPTION,
			PolicyVersion:  policyVersion,
			JsonData:       policyJson,
			Active:         true,
			UserId:         userId,
			PolicyRevision: rand.GetNewUUIDString(),
		},
	}
}
