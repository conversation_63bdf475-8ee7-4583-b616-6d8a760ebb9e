/*
 * Copyright (c) 2024. Devtron Inc.
 */

package v0

import (
	"fmt"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/model"
	"github.com/go-pg/pg"
	"github.com/stretchr/testify/assert"
	"slices"
	"testing"
)

func TestAddApplyEventObserver(t *testing.T) {
	cps := NewCommonPolicyActionsServiceV0(
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil)
	observerNames := []string{"observer1", "observer1"}
	observer1 := func(tx *pg.Tx, commaSeperatedAppEnvIds [][]int) error {
		return fmt.Errorf("%s", observerNames[0])
	}

	observer2 := func(tx *pg.Tx, commaSeperatedAppEnvIds [][]int) error {
		return fmt.Errorf("%s", observerNames[1])
	}

	added := cps.AddApplyEventObserver(model.ImagePromotion, observer1)
	assert.Equal(t, true, added)
	added = cps.AddApplyEventObserver(model.ImagePromotion, observer2)
	assert.Equal(t, true, added)
	observers := cps.applyEventObservers[model.ImagePromotion]
	assert.Equal(t, len(observers), len(observerNames))
	containsObserver1 := slices.ContainsFunc(observers, func(observer ApplyObserver) bool {
		err := observer(nil, nil)
		return observerNames[0] == err.Error()
	})
	assert.Equal(t, true, containsObserver1)

	containsObserver2 := slices.ContainsFunc(observers, func(observer ApplyObserver) bool {
		err := observer(nil, nil)
		return observerNames[1] == err.Error()
	})
	assert.Equal(t, true, containsObserver2)

}
