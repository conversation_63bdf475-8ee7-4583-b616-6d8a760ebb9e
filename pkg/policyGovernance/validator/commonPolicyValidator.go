package validator

import (
	"fmt"
	errorUtil "github.com/devtron-labs/devtron/internal/util"
	"github.com/devtron-labs/devtron/pkg/auth/authorisation/casbin"
	"github.com/devtron-labs/devtron/pkg/devtronResource/bean"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/adapter"
	bean2 "github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/bean"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/model"
	"github.com/devtron-labs/devtron/pkg/resourceQualifiers/common"
	"net/http"
	"regexp"
	"slices"
)

const UnsupportedProfileTypeErr = "Profile type provided is unsupported"
const MultiProfileTypeErr = "Multiple profile type is unsupported"
const MismatchedProfileTypeErr = "Selector kind mismatched with profile type"
const UnsupportedSelectorKindErr = "Selector kind is unsupported"
const MatchDoesntExistsErr = "Given match doesn't exists"
const EmptyProfileIdentifierErr = "Empty or Invalid Profile Identifiers type provided "
const UnsupportedSelectorCategoryErr = "Unsupported Selector Category type provided "
const EmptySelectorErr = "Empty Selectors provided "
const EmptyProfileErr = "Empty Profiles provided "
const InvalidSelectorIdentifierErr = "Empty or Invalid selector identifier provided "
const UnsupportedCiPipelineSelectorIdentifierErr = "Selector identifier is not supported for 'ci-pipeline' kind"
const BaseAndEnvKindBothExistsErr = "Env/Cluster and Base selector can't exist in a single selector "
const BaseAndApprovalDeploymentKindBothExistsErr = "Base Config can't be applied to Deployment"
const EmptySelectorMatchesErr = "Empty Selector Matches type provided "
const EmptySelectorMatchesIdentifierErr = "Empty Selector Matches Identifiers type provided "
const EmptySelectorMatchesBranchErr = "Empty Selector Matches provided for Branch or Branch Regex type "
const NameLengthDefined = "Length of name should be between 3 and 50 characters"
const InvalidApplicableToErr = "Invalid ApplicableTo option provided in payload"
const InvalidApplicableToAndPolicyTypeErr = "ApplicableTo and Policy type mismatch"
const InvalidApprovalDeploymentApplicableToErr = "Deployment approval don't support further classification"
const NoApprovalConfigurationApplicableToErr = "No further option provided for configuration approval"
const InvalidApprovalConfigurationApplicableToErr = "Invalid option provided for configuration approval"

var supportedDtResKind = []bean.DtResKind{
	bean.DevtronResourceDevtronApplicationFull,
	bean.DevtronResourceEnvironment,
	bean.DevtronResourceProject,
	bean.DevtronResourceCluster,
	bean.DevtronResourceBaseTemplate,
	bean.DevtronResourceBaseConfiguration,
	bean.DevtronResourceCiPipeline,
}

func ValidatePutOrPostRequestPayload(dto *model.ProfileSelectorDto, actionType string) (pathVariablePolicyType model.PathVariablePolicyType, err error) {
	if dto == nil {
		errMsg := "empty request body provided"
		return pathVariablePolicyType, errorUtil.DefaultApiError().WithHttpStatusCode(http.StatusBadRequest).WithUserMessage(errMsg).WithInternalMessage(errMsg)
	}
	pathVariablePolicyType, err = validateProfiles(dto.Policies)
	if err != nil {
		return pathVariablePolicyType, err
	}
	if pathVariablePolicyType != model.Approval && dto.ApplicableTo.Type.ToString() != pathVariablePolicyType.ToString() {
		return pathVariablePolicyType, errorUtil.DefaultApiError().WithHttpStatusCode(http.StatusBadRequest).WithInternalMessage(InvalidApplicableToAndPolicyTypeErr).WithUserMessage(InvalidApplicableToAndPolicyTypeErr)
	}
	err = validateSelectors(pathVariablePolicyType, dto.ApplicableTo.Type, dto.Selectors, actionType)
	if err != nil {
		return pathVariablePolicyType, err
	}
	err = validateApplicableTo(dto.ApplicableTo)
	if err != nil {
		return pathVariablePolicyType, err
	}
	return pathVariablePolicyType, nil
}
func validateApplicableTo(applicableTo *model.ApplicableType) error {
	if applicableTo == nil {
		return errorUtil.DefaultApiError().WithHttpStatusCode(http.StatusBadRequest).WithInternalMessage(InvalidApplicableToErr).WithUserMessage(InvalidApplicableToErr)
	}
	if applicableTo.Type == model.APPLY_POLICY_APPROVAL_DEPLOYMENT {
		if len(applicableTo.Values) > 0 {
			return errorUtil.DefaultApiError().WithHttpStatusCode(http.StatusBadRequest).WithInternalMessage(InvalidApprovalDeploymentApplicableToErr).WithUserMessage(InvalidApprovalDeploymentApplicableToErr)
		}
	} else if applicableTo.Type == model.APPLY_POLICY_APPROVAL_CONFIGURATION {
		if len(applicableTo.Values) == 0 {
			return errorUtil.DefaultApiError().WithHttpStatusCode(http.StatusBadRequest).WithInternalMessage(NoApprovalConfigurationApplicableToErr).WithUserMessage(NoApprovalConfigurationApplicableToErr)
		}
		for _, value := range applicableTo.Values {
			if value.Identifier != bean2.APPROVAL_FOR_CONFIGURATION_CM.ToString() &&
				value.Identifier != bean2.APPROVAL_FOR_CONFIGURATION_CS.ToString() &&
				value.Identifier != bean2.APPROVAL_FOR_CONFIGURATION_DT.ToString() {
				return errorUtil.DefaultApiError().WithHttpStatusCode(http.StatusBadRequest).WithInternalMessage(InvalidApprovalConfigurationApplicableToErr).WithUserMessage(InvalidApprovalConfigurationApplicableToErr)
			}
		}
	}
	return nil
}

func ValidatePatchRequestPayload(req *model.PatchBulkProfileSelectorRequest) error {
	if len(req.PoliciesToAdd.Type) == 0 && len(req.PoliciesToRemove.Type) == 0 {
		return errorUtil.DefaultApiError().WithHttpStatusCode(http.StatusBadRequest).WithInternalMessage(EmptyProfileErr).WithUserMessage(EmptyProfileErr)
	}
	if len(req.PoliciesToAdd.Type) > 0 {
		_, err := validateProfiles(req.PoliciesToAdd)
		return err
	}
	if len(req.PoliciesToRemove.Type) > 0 {
		_, err := validateProfiles(req.PoliciesToRemove)
		return err
	}
	if len(req.Selectors) == 0 {
		return errorUtil.DefaultApiError().WithHttpStatusCode(http.StatusBadRequest).WithInternalMessage(EmptySelectorErr).WithUserMessage(EmptySelectorErr)

	}
	return nil
}

func validateProfiles(profile model.Profile) (model.PathVariablePolicyType, error) {
	if profile.Type == "" {
		return "", errorUtil.DefaultApiError().WithHttpStatusCode(http.StatusBadRequest).WithInternalMessage(UnsupportedProfileTypeErr).WithUserMessage(UnsupportedProfileTypeErr)
	}
	if !slices.Contains(model.ExistingPolicyTypes, profile.Type) {
		return "", errorUtil.DefaultApiError().WithHttpStatusCode(http.StatusBadRequest).WithInternalMessage(UnsupportedProfileTypeErr).WithUserMessage(UnsupportedProfileTypeErr)
	}
	if len(profile.Identifiers) == 0 {
		return "", errorUtil.DefaultApiError().WithHttpStatusCode(http.StatusBadRequest).WithInternalMessage(EmptyProfileIdentifierErr).WithUserMessage(EmptyProfileIdentifierErr)
	}
	return profile.Type, nil
}

func validateSelectors(pathVariablePolicyType model.PathVariablePolicyType, applyPolicyType model.ApplyPolicyType, selectors []model.Selector, actionType string) error {
	if len(selectors) == 0 {
		return errorUtil.DefaultApiError().WithHttpStatusCode(http.StatusBadRequest).WithInternalMessage(EmptySelectorErr).WithUserMessage(EmptySelectorErr)

	}
	for _, selector := range selectors {
		if pathVariablePolicyType.IsMandatoryPluginPolicy() {
			if selector.Category == common.SPECIFIC_CATEGORY {
				return errorUtil.DefaultApiError().WithHttpStatusCode(http.StatusBadRequest).WithInternalMessage(UnsupportedSelectorCategoryErr).WithUserMessage(UnsupportedSelectorCategoryErr)
			}
		}
		if selector.Category == "" {
			return errorUtil.DefaultApiError().WithHttpStatusCode(http.StatusBadRequest).WithInternalMessage(UnsupportedSelectorCategoryErr).WithUserMessage(UnsupportedSelectorCategoryErr)
		}
		if actionType == casbin.ActionUpdate && selector.Identifier == 0 {
			return errorUtil.DefaultApiError().WithHttpStatusCode(http.StatusBadRequest).WithInternalMessage(InvalidSelectorIdentifierErr).WithUserMessage(InvalidSelectorIdentifierErr)
		}
		if selector.Category != common.GLOBAL_CATEGORY {
			matches := selector.Matches
			if len(matches) == 0 {
				return errorUtil.DefaultApiError().WithHttpStatusCode(http.StatusBadRequest).WithInternalMessage(EmptySelectorMatchesErr).WithUserMessage(EmptySelectorMatchesErr)
			}
			var envExists, baseExists bool
			for _, match := range matches {
				if match.Kind == "" || !slices.Contains(supportedDtResKind, match.Kind) {
					return errorUtil.DefaultApiError().WithHttpStatusCode(http.StatusBadRequest).WithInternalMessage(UnsupportedSelectorKindErr).WithUserMessage(UnsupportedSelectorKindErr)
				}
				if match.Kind != bean.DevtronResourceBaseTemplate && match.Kind != bean.DevtronResourceBaseConfiguration {
					if match.Kind == bean.DevtronResourceEnvironment || match.Kind == bean.DevtronResourceCluster {
						envExists = true
					}
					if match.Kind == bean.DevtronResourceCiPipeline {
						if !adapter.IsAttributeValuesPresentForKey(match.Attributes, bean.Branch) && !adapter.IsAttributeValuesPresentForKey(match.Attributes, bean.BranchRegex) {
							return errorUtil.DefaultApiError().WithHttpStatusCode(http.StatusBadRequest).WithInternalMessage(EmptySelectorMatchesBranchErr).WithUserMessage(EmptySelectorMatchesBranchErr)
						}
						if adapter.IsAttributeValuesPresentForKey(match.Attributes, bean.IdentifierKey) {
							return errorUtil.DefaultApiError().WithHttpStatusCode(http.StatusBadRequest).WithInternalMessage(UnsupportedCiPipelineSelectorIdentifierErr).WithUserMessage(UnsupportedCiPipelineSelectorIdentifierErr)
						}
						branchRegex := adapter.GetAttributeValuesByKey(match.Attributes, bean.BranchRegex)
						for _, regex := range branchRegex {
							_, err := regexp.Compile(regex)
							if err != nil {
								return errorUtil.DefaultApiError().
									WithHttpStatusCode(http.StatusBadRequest).
									WithInternalMessage(err.Error()).
									WithUserMessage(fmt.Sprintf("Invalid regex pattern '%s' provided for branch regex", regex))
							}
						}
					} else {
						// condition len(match.Identifiers) == 0 is maintained for backward compatibility
						if !adapter.IsAttributeValuesPresentForKey(match.Attributes, bean.IdentifierKey) && len(match.Identifiers) == 0 {
							return errorUtil.DefaultApiError().WithHttpStatusCode(http.StatusBadRequest).WithInternalMessage(EmptySelectorMatchesIdentifierErr).WithUserMessage(EmptySelectorMatchesIdentifierErr)
						}
					}
				} else {
					baseExists = true
					continue
				}
			}
			if envExists && baseExists {
				return errorUtil.DefaultApiError().WithHttpStatusCode(http.StatusBadRequest).WithInternalMessage(BaseAndEnvKindBothExistsErr).WithUserMessage(BaseAndEnvKindBothExistsErr)
			}
			if baseExists && applyPolicyType == model.APPLY_POLICY_APPROVAL_DEPLOYMENT {
				return errorUtil.DefaultApiError().WithHttpStatusCode(http.StatusBadRequest).WithInternalMessage(BaseAndApprovalDeploymentKindBothExistsErr).WithUserMessage(BaseAndApprovalDeploymentKindBothExistsErr)
			}
		} else {
			continue
		}
	}
	return nil
}

func ValidateName(name string) error {
	if len(name) < 3 || len(name) > 50 {
		return errorUtil.DefaultApiError().WithHttpStatusCode(http.StatusBadRequest).WithInternalMessage(NameLengthDefined).WithUserMessage(NameLengthDefined)
	}
	return nil
}
