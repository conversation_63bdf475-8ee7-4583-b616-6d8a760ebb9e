/*
 * Copyright (c) 2024. Devtron Inc.
 */

package action

import (
	"context"
	"errors"
	client2 "github.com/devtron-labs/devtron/client/events"
	"github.com/devtron-labs/devtron/internal/sql/repository"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/pkg/auth/user"
	bean3 "github.com/devtron-labs/devtron/pkg/auth/user/bean"
	bean4 "github.com/devtron-labs/devtron/pkg/bean"
	"github.com/devtron-labs/devtron/pkg/build/artifacts/imageTagging"
	"github.com/devtron-labs/devtron/pkg/deployment/trigger/devtronApps"
	"github.com/devtron-labs/devtron/pkg/deployment/trigger/devtronApps/bean"
	bean2 "github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/bean"
	read2 "github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/read"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/artifactApproval/read"
	model2 "github.com/devtron-labs/devtron/pkg/policyGovernance/model"
	"github.com/devtron-labs/devtron/util"
	util2 "github.com/devtron-labs/devtron/util/event"
	"go.uber.org/zap"
)

type ArtifactApprovalActionService interface {
	PerformDeploymentApprovalAction(ctx *util.RequestCtx, approvalActionRequest bean4.UserApprovalActionRequest) error
}

type ArtifactApprovalActionServiceImpl struct {
	logger                                  *zap.SugaredLogger
	userGroupService                        user.UserGroupService
	artifactApprovalDataReadService         read.ArtifactApprovalDataReadService
	cdHandlerService                        devtronApps.HandlerService
	eventClient                             client2.EventClient
	eventFactory                            client2.EventFactory
	imageTaggingService                     imageTagging.ImageTaggingService
	deploymentApprovalRepository            pipelineConfig.DeploymentApprovalRepository
	requestApprovalRepository               pipelineConfig.RequestApprovalUserdataRepository
	ciArtifactRepository                    repository.CiArtifactRepository
	pipelineRepository                      pipelineConfig.PipelineRepository
	approvalConfigurationEnforcementService read2.ApprovalPolicyReadService
}

func NewArtifactApprovalActionServiceImpl(logger *zap.SugaredLogger,
	userGroupService user.UserGroupService,
	artifactApprovalDataReadService read.ArtifactApprovalDataReadService,
	cdHandlerService devtronApps.HandlerService, eventClient client2.EventClient,
	eventFactory client2.EventFactory, imageTaggingService imageTagging.ImageTaggingService,
	deploymentApprovalRepository pipelineConfig.DeploymentApprovalRepository,
	requestApprovalRepository pipelineConfig.RequestApprovalUserdataRepository,
	ciArtifactRepository repository.CiArtifactRepository,
	approvalConfigurationEnforcementService read2.ApprovalPolicyReadService,
	pipelineRepository pipelineConfig.PipelineRepository) *ArtifactApprovalActionServiceImpl {
	return &ArtifactApprovalActionServiceImpl{
		logger:                                  logger,
		artifactApprovalDataReadService:         artifactApprovalDataReadService,
		cdHandlerService:                        cdHandlerService,
		eventClient:                             eventClient,
		eventFactory:                            eventFactory,
		imageTaggingService:                     imageTaggingService,
		deploymentApprovalRepository:            deploymentApprovalRepository,
		requestApprovalRepository:               requestApprovalRepository,
		ciArtifactRepository:                    ciArtifactRepository,
		pipelineRepository:                      pipelineRepository,
		userGroupService:                        userGroupService,
		approvalConfigurationEnforcementService: approvalConfigurationEnforcementService,
	}
}

func (impl *ArtifactApprovalActionServiceImpl) checkIfUserCanApprove(userId int32, userEmail string, approvalConfig *bean2.UserApprovalConfig) error {
	if approvalConfig.Type == bean2.NOT_CONFIGURED {
		return errors.New("approval config is not configured")
	}

	if approvalConfig.AnyApprovalApprovalConfig.Configured() {
		return nil
	}

	if approvalConfig.SpecificApprovalConfig.Configured() {
		isSpecificUser := false
		for _, specificUser := range approvalConfig.SpecificApprovalConfig.SpecificUsers.Identifiers {
			if specificUser == userEmail {
				isSpecificUser = true
			}
		}

		errMsg := "you are not an eligible approver"

		userGroupsMap, err := impl.userGroupService.GetByUserIds([]int32{userId})
		if err != nil {
			impl.logger.Errorw("error in fetching user groups", "userId", userId, "err", err)
			return err
		}

		isUserInRelevantGroup := false
		for _, policyUserGroup := range approvalConfig.GetUserGroupIdentifiers() {
			for _, userGroup := range userGroupsMap[userId] {
				if userGroup.Identifier == policyUserGroup {
					isUserInRelevantGroup = true
				}
			}
		}

		if canApprove := isSpecificUser || isUserInRelevantGroup; !canApprove {
			return errors.New(errMsg)
		}
	}
	return nil
}

func (impl *ArtifactApprovalActionServiceImpl) PerformDeploymentApprovalAction(ctx *util.RequestCtx, approvalActionRequest bean4.UserApprovalActionRequest) error {
	approvalActionType := approvalActionRequest.ActionType
	artifactId := approvalActionRequest.ArtifactId
	approvalRequestId := approvalActionRequest.ApprovalRequestId
	if approvalActionType == bean4.APPROVAL_APPROVE_ACTION {
		// fetch approval request data, same user should not be Approval requester
		approvalRequest, err := impl.deploymentApprovalRepository.FetchWithPipelineAndArtifactDetails(approvalRequestId)
		if err != nil {
			return &bean4.DeploymentApprovalValidationError{
				Err:           errors.New("failed to fetch approval request data"),
				ApprovalState: bean4.RequestCancelled,
			}
		}
		if approvalRequest.ArtifactDeploymentTriggered == true {
			return &bean4.DeploymentApprovalValidationError{
				Err:           errors.New("deployment has already been triggered for this request"),
				ApprovalState: bean4.AlreadyApproved,
			}
		}
		if approvalRequest.CreatedBy == ctx.GetUserId() {
			return errors.New("requester cannot be an approver")
		}

		// fetch artifact metadata, who triggered this build
		ciArtifact, err := impl.ciArtifactRepository.Get(artifactId)
		if err != nil {
			impl.logger.Errorw("error occurred while fetching workflow data for artifact", "artifactId", artifactId, "userId", ctx.GetUserId(), "err", err)
			return errors.New("failed to fetch workflow for artifact data")
		}

		if ciArtifact.CreatedBy == ctx.GetUserId() {
			return errors.New("user who triggered the build cannot be an approver")
		}

		userGroupsOfApprovingUser, err := impl.userGroupService.GetByUserIds([]int32{ctx.GetUserId()})
		if err != nil {
			impl.logger.Errorw("error in finding the user groups of user", "userId", ctx.GetUserId(), "err", err)
			return err
		}

		approvalConfigurationMap, err := impl.approvalConfigurationEnforcementService.GetConfigurationsByAppAndEnvId(model2.APPLY_POLICY_APPROVAL_DEPLOYMENT, approvalRequest.Pipeline.AppId, approvalRequest.Pipeline.EnvironmentId)
		if err != nil {
			return errors.New("error in fetching approval config, err : " + err.Error())
		}

		approvalConfiguration := approvalConfigurationMap[bean2.APPROVAL_FOR_DEPLOYMENT]

		approvalNodeConfigured := approvalConfiguration != nil
		if !approvalNodeConfigured {
			return errors.New("approval config is not configured")
		}

		var userGroupIdentifiersOfRequestedUser []string
		for _, ug := range userGroupsOfApprovingUser[ctx.GetUserId()] {
			userGroupIdentifiersOfRequestedUser = append(userGroupIdentifiersOfRequestedUser, ug.Identifier)
		}
		if !approvalConfiguration.UserEligibleToApprove(ctx.GetUserEmailId(), userGroupIdentifiersOfRequestedUser) {
			return errors.New("you are not an eligible approver")
		}

		deploymentApprovalData := &pipelineConfig.RequestApprovalUserData{
			ApprovalRequestId: approvalRequestId,
			UserId:            ctx.GetUserId(),
			UserResponse:      bean2.APPROVED,
		}
		deploymentApprovalData.CreatedBy = ctx.GetUserId()
		deploymentApprovalData.UpdatedBy = ctx.GetUserId()
		err = impl.requestApprovalRepository.SaveRequestApprovalUserData(deploymentApprovalData)
		if err != nil {
			impl.logger.Errorw("error occurred while saving user approval data", "approvalRequestId", approvalRequestId, "err", err)
			return &bean4.DeploymentApprovalValidationError{
				Err:           err,
				ApprovalState: bean4.AlreadyApproved,
			}
		}

		userApprovalMetadataRequest, err := impl.artifactApprovalDataReadService.PrepareApprovalMetadataRequest(ctx, approvalRequest.PipelineId)
		if err != nil {
			impl.logger.Errorw("error in getting user approval metadata", "pipelineId", approvalRequest.PipelineId, "err", err)
			return err
		}

		approvalDataForArtifacts, err := impl.artifactApprovalDataReadService.FetchApprovalDataForArtifacts(ctx, userApprovalMetadataRequest, []int{artifactId}, approvalRequest.PipelineId, approvalConfiguration)
		if err != nil {
			impl.logger.Errorw("error occurred while fetching approval data for artifacts", "artifactId", artifactId, "pipelineId", approvalRequest.PipelineId, "config", approvalConfiguration, "err", err)
			return nil
		}

		approvedData := approvalDataForArtifacts[artifactId]
		// trigger deployment if approved and pipeline type is automatic
		pipeline := approvalRequest.Pipeline
		if pipeline.TriggerType == pipelineConfig.TRIGGER_TYPE_AUTOMATIC {
			pipelineId := approvalRequest.PipelineId
			if approvedData != nil && approvedData.ApprovalRuntimeState == bean2.ApprovedApprovalState {
				// trigger deployment
				triggerRequest := bean.CdTriggerRequest{
					CdWf:        nil,
					Pipeline:    pipeline,
					Artifact:    approvalRequest.CiArtifact,
					TriggeredBy: bean3.SYSTEM_USER_ID,
					TriggerContext: bean.TriggerContext{
						Context: context.Background(),
					},
				}

				err = impl.cdHandlerService.TriggerAutomaticDeployment(triggerRequest)
				if err != nil {
					impl.logger.Errorw("error occurred while triggering deployment", "pipelineId", pipelineId, "artifactId", artifactId, "err", err)
					return errors.New("auto deployment failed, please try manually")
				}
			}
		}

	} else if approvalActionType == bean4.APPROVAL_REQUEST_ACTION {
		pipelineId := approvalActionRequest.PipelineId
		deploymentApprovalRequest := &pipelineConfig.DeploymentApprovalRequest{
			PipelineId: pipelineId,
			ArtifactId: artifactId,
			Active:     true,
		}
		deploymentApprovalRequest.CreatedBy = ctx.GetUserId()
		deploymentApprovalRequest.UpdatedBy = ctx.GetUserId()
		err := impl.deploymentApprovalRepository.Save(deploymentApprovalRequest)
		if err != nil {
			impl.logger.Errorw("error occurred while submitting approval request", "pipelineId", pipelineId, "artifactId", artifactId, "err", err)
			return err
		}
		approvalActionRequest.ApprovalRequestId = deploymentApprovalRequest.Id
		go impl.performNotificationApprovalAction(approvalActionRequest, ctx.GetUserId())

	} else {
		// fetch if cd wf runner is present then user cannot cancel the request, as deployment has been triggered already
		approvalRequest, err := impl.deploymentApprovalRepository.FetchById(approvalRequestId)
		if err != nil {
			return errors.New("failed to fetch approval request data")
		}
		if approvalRequest.CreatedBy != ctx.GetUserId() {
			return errors.New("request cannot be cancelled as not initiated by the same")
		}
		if approvalRequest.ArtifactDeploymentTriggered {
			return errors.New("request cannot be cancelled as deployment is already been made for this request")
		}
		approvalRequest.Active = false
		err = impl.deploymentApprovalRepository.Update(approvalRequest)
		if err != nil {
			impl.logger.Errorw("error occurred while updating approval request", "pipelineId", approvalRequest.PipelineId, "artifactId", artifactId, "err", err)
			return err
		}
	}
	return nil
}

func (impl *ArtifactApprovalActionServiceImpl) performNotificationApprovalAction(approvalActionRequest bean4.UserApprovalActionRequest, userId int32) {
	if len(approvalActionRequest.ApprovalNotificationConfig.EmailIds) == 0 {
		return
	}
	eventType := util2.Approval
	var events []client2.Event
	pipeline, err := impl.pipelineRepository.FindById(approvalActionRequest.PipelineId)
	if err != nil {
		impl.logger.Errorw("error occurred while updating approval request", "pipelineId", pipeline, "pipeline", pipeline, "err", err)
	}
	event, _ := impl.eventFactory.Build(eventType, &approvalActionRequest.PipelineId, approvalActionRequest.AppId, &pipeline.EnvironmentId, "")
	imageComment, imageTagNames, err := impl.imageTaggingService.GetImageTagsAndComment(approvalActionRequest.ArtifactId)
	if err != nil {
		impl.logger.Errorw("error in fetching tags and comment", "artifactId", approvalActionRequest.ArtifactId)
	}
	events = impl.eventFactory.BuildExtraApprovalData(event, approvalActionRequest, pipeline, userId, imageTagNames, imageComment.Comment)
	for _, evnt := range events {
		_, evtErr := impl.eventClient.WriteNotificationEvent(evnt)
		if evtErr != nil {
			impl.logger.Errorw("unable to send approval event", "error", evtErr)
		}
	}

}
