package scanTool

import (
	"github.com/devtron-labs/devtron/pkg/plugin"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/security/scanTool/repository"
	"github.com/devtron-labs/devtron/pkg/sql"
	"github.com/go-pg/pg"
	"go.uber.org/zap"
)

type ScanToolMetadataService interface {
	MarkToolAsActive(toolName, version string, tx *pg.Tx) error
	MarkOtherToolsInActive(toolName string, tx *pg.Tx, version string) error
	GetActiveTool() (*repository.ScanToolMetadata, error)
	ScanToolMetadataService_ent
}

type ScanToolMetadataServiceImpl struct {
	logger                     *zap.SugaredLogger
	scanToolMetadataRepository repository.ScanToolMetadataRepository
	// ent dependencies below
	globalPluginService plugin.GlobalPluginService
	transactionManager  sql.TransactionWrapper
}

func NewScanToolMetadataServiceImpl(logger *zap.SugaredLogger,
	scanToolMetadataRepository repository.ScanToolMetadataRepository,
	globalPluginService plugin.GlobalPluginService,
	transactionManager sql.TransactionWrapper,
) *ScanToolMetadataServiceImpl {
	return &ScanToolMetadataServiceImpl{
		logger:                     logger,
		scanToolMetadataRepository: scanToolMetadataRepository,
		globalPluginService:        globalPluginService,
		transactionManager:         transactionManager,
	}
}

func (impl *ScanToolMetadataServiceImpl) MarkToolAsActive(toolName, version string, tx *pg.Tx) error {
	return impl.scanToolMetadataRepository.MarkToolAsActive(toolName, version, tx)
}

func (impl *ScanToolMetadataServiceImpl) MarkOtherToolsInActive(toolName string, tx *pg.Tx, version string) error {
	return impl.scanToolMetadataRepository.MarkOtherToolsInActive(toolName, tx, version)
}

func (impl *ScanToolMetadataServiceImpl) GetActiveTool() (*repository.ScanToolMetadata, error) {
	return impl.scanToolMetadataRepository.FindActiveTool()
}
