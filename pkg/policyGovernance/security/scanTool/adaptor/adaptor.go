package adaptor

import (
	bean2 "github.com/devtron-labs/devtron/pkg/plugin/bean"
	"github.com/devtron-labs/devtron/pkg/plugin/repository"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/security/scanTool/bean"
	repository2 "github.com/devtron-labs/devtron/pkg/policyGovernance/security/scanTool/repository"
	"github.com/devtron-labs/devtron/pkg/sql"
	"time"
)

func GetPluginParentMetadataFromScanToolPluginMetadata(scanToolPluginMetadata *bean.ScanToolPluginMetadataDto, toolIconUrl string) *bean2.PluginParentMetadataDto {
	if scanToolPluginMetadata == nil {
		return &bean2.PluginParentMetadataDto{}
	}
	isExposed := false
	return &bean2.PluginParentMetadataDto{
		Name:             scanToolPluginMetadata.Name,
		PluginIdentifier: scanToolPluginMetadata.PluginIdentifier,
		Description:      scanToolPluginMetadata.Description,
		Type:             bean2.SHARED.ToString(),
		Icon:             toolIconUrl,
		PluginStageType:  repository.SCANNER_STAGE_TYPE,
		IsExposed:        &isExposed,
	}
}

func GetPluginMetadataDtoForScanTool(steps []*bean2.PluginStepsDto) *bean2.PluginMetadataDto {
	isExposed := false
	return &bean2.PluginMetadataDto{
		Tags:        []string{"Security"},
		PluginStage: repository.SCANNER_STAGE_TYPE,
		PluginSteps: steps,
		IsExposed:   &isExposed,
	}
}

func GetPluginVersionDetailForScanTool(pluginMetadata *bean2.PluginMetadataDto, scanToolPluginMetadata *bean.ScanToolPluginMetadataDto) *bean2.PluginsVersionDetail {
	return &bean2.PluginsVersionDetail{
		PluginMetadataDto: pluginMetadata,
		Version:           scanToolPluginMetadata.Version,
		IsLatest:          true,
		CreatedOn:         time.Now(),
	}

}
func GetPluginMetadataDtoFromScanToolPluginMetadataDto(registerScanToolDto *bean.RegisterScanToolsDto, steps []*bean2.PluginStepsDto) *bean2.PluginParentMetadataDto {
	pluginParentObj := GetPluginParentMetadataFromScanToolPluginMetadata(registerScanToolDto.ScanToolPluginMetadata, registerScanToolDto.ScanToolMetadata.ScanToolUrl)
	pluginMetadataDto := GetPluginMetadataDtoForScanTool(steps)
	pluginVersionDetail := GetPluginVersionDetailForScanTool(pluginMetadataDto, registerScanToolDto.ScanToolPluginMetadata)

	pluginParentObj.Versions = bean2.NewPluginVersions().WithDetailedPluginVersionData([]*bean2.PluginsVersionDetail{pluginVersionDetail})
	return pluginParentObj
}

func GetScanToolMetadataDbObjectFromDto(scanToolMetadata *bean.ScanToolsMetadataDto, pluginMetadataId int, userId int32) *repository2.ScanToolMetadata {
	if scanToolMetadata == nil {
		return &repository2.ScanToolMetadata{}
	}
	var scanTarget bean.ScanTargetType
	if len(scanToolMetadata.ScanTarget) > 0 {
		scanTarget = scanToolMetadata.ScanTarget
	} else {
		scanTarget = bean.ScanTargetTypeImage
	}

	return &repository2.ScanToolMetadata{
		Name:                     scanToolMetadata.Name,
		Version:                  scanToolMetadata.Version,
		ServerBaseUrl:            scanToolMetadata.ServerBaseUrl,
		ResultDescriptorTemplate: scanToolMetadata.ResultDescriptorTemplate,
		ScanTarget:               scanTarget,
		ToolMetaData:             scanToolMetadata.ToolMetaData,
		PluginId:                 pluginMetadataId,
		IsPreset:                 false,
		Url:                      scanToolMetadata.ScanToolUrl,
		AuditLog:                 sql.NewDefaultAuditLog(userId),
	}
}
