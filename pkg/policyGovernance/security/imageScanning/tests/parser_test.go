/*
 * Copyright (c) 2024. Devtron Inc.
 */

package tests

import (
	"encoding/json"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/security/imageScanning/helper/parser"
	"github.com/stretchr/testify/assert"
	"io/ioutil"
	"testing"
)

func loadData(t *testing.T, fileName string) string {

	jsonBytes, err := ioutil.ReadFile(fileName)
	if err != nil {
		t.Error(err)
	}

	return string(jsonBytes)

}

func TestParsing(t *testing.T) {
	t.Run("imageScan results", func(tt *testing.T) {
		jsonStr := loadData(t, "image_scan.json")
		vulns := parser.ParseImageScanResult(jsonStr)
		assert.NotNil(t, vulns)
	})

	t.Run("codeScan results", func(tt *testing.T) {
		jsonStr := loadData(t, "code_scan.json")
		misConfigurations := parser.ParseCodeScanResult(jsonStr)
		assert.NotNil(t, misConfigurations)
		jsonRes, err := json.Marshal(&misConfigurations)
		assert.NotNil(tt, jsonRes)
		assert.Nil(tt, err)
	})

	t.Run("ParseMisConfigurations", func(tt *testing.T) {
		jsonStr := loadData(t, "code_scan.json")
		exposedSecrets := parser.ParseK8sConfigScanResult(jsonStr, "")
		assert.NotNil(t, exposedSecrets)
	})
}
