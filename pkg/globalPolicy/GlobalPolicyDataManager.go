/*
 * Copyright (c) 2024. Devtron Inc.
 */

package globalPolicy

import (
	"context"
	"encoding/json"
	errorUtil "github.com/devtron-labs/devtron/internal/util"
	"github.com/devtron-labs/devtron/pkg/auth/user"
	bean3 "github.com/devtron-labs/devtron/pkg/auth/user/bean"
	"github.com/devtron-labs/devtron/pkg/globalPolicy/bean"
	"github.com/devtron-labs/devtron/pkg/globalPolicy/history"
	bean2 "github.com/devtron-labs/devtron/pkg/globalPolicy/history/bean"
	repository2 "github.com/devtron-labs/devtron/pkg/globalPolicy/history/repository"
	"github.com/devtron-labs/devtron/pkg/globalPolicy/repository"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/model"
	bean4 "github.com/devtron-labs/devtron/pkg/policyGovernance/plugin/alpha1/bean"
	"github.com/devtron-labs/devtron/util"
	"github.com/devtron-labs/devtron/util/sliceUtil"
	"github.com/go-pg/pg"
	"github.com/tidwall/gjson"
	"go.uber.org/zap"
	"maps"
	"slices"
	"time"
)

type GlobalPolicyDataManager interface {
	GetPolicyById(policyId int) (*bean.GlobalPolicyBaseModel, error)
	GetPolicyByNameAndType(policyType bean.GlobalPolicyType, policyVersion bean.GlobalPolicyVersion, policyName string) (*bean.GlobalPolicyBaseModel, error)
	GetPolicyByNames(policyType bean.GlobalPolicyType, policyVersion bean.GlobalPolicyVersion, policyNames []string) ([]*bean.GlobalPolicyBaseModel, error)
	GetPolicyByIds(policyIds []int) ([]*bean.GlobalPolicyBaseModel, error)
	GetPolicyDefinitionByIds(policyIds []int) ([]*bean.GlobalPolicyBaseModel, error)
	GetAllActiveByType(policyType bean.GlobalPolicyType, version bean.GlobalPolicyVersion) ([]*bean.GlobalPolicyBaseModel, error)
	GetPolicyMetadataByFields(policyIds []int, fields []*util.SearchableField) (map[int][]*util.SearchableField, error)
	GetPolicyByCriteria(policyType bean.GlobalPolicyType, policyVersion bean.GlobalPolicyVersion, policyNamePattern string, sortRequest *bean.SortByRequest) ([]*bean.GlobalPolicyBaseModel, error)
	GetPolicyIdByName(policyType bean.GlobalPolicyType, policyVersion bean.GlobalPolicyVersion, name string) (int, error)
	CheckIfNameExists(name string, policyType bean.GlobalPolicyType, policyVersion bean.GlobalPolicyVersion) (bool, error)
	GetAllActivePolicyIdsByType(policyType bean.GlobalPolicyType, version bean.GlobalPolicyVersion) ([]int, error)
	GetAllActivePoliciesByType(policyType bean.GlobalPolicyType, version bean.GlobalPolicyVersion) ([]*bean.GlobalPolicyBaseModel, error)
	GetAllV2PluginPolicyIdsWithPlugins(pluginIds []int) ([]int, error)
	GetAllActivePoliciesByMode(policyType bean.GlobalPolicyType, policyVersion bean.GlobalPolicyVersion, lite bool) ([]*bean.GlobalPolicyBaseModel, error)
	GetPoliciesByHistoryIds(historyIds []int) ([]*bean.GlobalPolicyBaseModel, error)
	GetPolicyHistoryIdsByPolicyIds(policyIds []int) ([]int, error)
	GetAllActivePoliciesCount() (map[string]int, error)
	GetGloballyAppliedPolicyByRqmResourceType(resourceType int) (*bean.GlobalPolicyBaseModel, error)
	IsUserAnExceptionByRqmResourceType(ctx context.Context, resourceType int, userMetadata *bean3.UserMetadata) (bool, error)

	CreatePolicy(globalPolicyDataModel *bean.GlobalPolicyDataModel, tx *pg.Tx) (*bean.GlobalPolicyDataModel, error)
	UpdatePolicy(globalPolicyDataModel *bean.GlobalPolicyDataModel, tx *pg.Tx) (*bean.GlobalPolicyDataModel, error)
	UpdatePolicyByName(tx *pg.Tx, PolicyName string, globalPolicyDataModel *bean.GlobalPolicyDataModel) (*bean.GlobalPolicyDataModel, error)
	DeletePolicyById(tx *pg.Tx, policyId int, userId int32) error
	DeletePolicyByName(tx *pg.Tx, policyType bean.GlobalPolicyType, policyVersion bean.GlobalPolicyVersion, policyName string, userId int32) error
}

type GlobalPolicyDataManagerImpl struct {
	logger                                *zap.SugaredLogger
	globalPolicyRepository                repository.GlobalPolicyRepository
	globalPolicyHistoryService            history.GlobalPolicyHistoryService
	globalPolicySearchableFieldRepository repository.GlobalPolicySearchableFieldRepository
	userGroupService                      user.UserGroupService
	userService                           user.UserService
}

func NewGlobalPolicyDataManagerImpl(logger *zap.SugaredLogger, globalPolicyRepository repository.GlobalPolicyRepository,
	globalPolicySearchableFieldRepository repository.GlobalPolicySearchableFieldRepository,
	globalPolicyHistoryService history.GlobalPolicyHistoryService,
	userGroupService user.UserGroupService,
	userService user.UserService,
) *GlobalPolicyDataManagerImpl {
	globalPolicyDataManager := &GlobalPolicyDataManagerImpl{
		logger:                                logger,
		globalPolicyRepository:                globalPolicyRepository,
		globalPolicyHistoryService:            globalPolicyHistoryService,
		globalPolicySearchableFieldRepository: globalPolicySearchableFieldRepository,
		userGroupService:                      userGroupService,
	}
	userService.RegisterListener(globalPolicyDataManager)
	return globalPolicyDataManager
}

// OnUserDelete is called for user or token being deleted and when users are being deleted in bulk
func (impl *GlobalPolicyDataManagerImpl) OnUserDelete(usersEmail []string, userIdForAuditLog int32) error {
	// OnUserDelete removes the specified user from all exception policies, when a user is deleted from the system.
	// this ensures that deleted users are no longer included in any policy exceptions.
	allExceptionUsersProfiles, err := impl.globalPolicyRepository.GetAllActiveByType(bean.GLOBAL_POLICY_TYPE_EXCEPTION, bean.GLOBAL_POLICY_VERSION_V1)
	if err != nil {
		impl.logger.Errorw("error in fetching all active exception users profiles", "err", err)
		return err
	}
	if len(allExceptionUsersProfiles) == 0 {
		return nil // no exception users profile found
	}
	usersToDeleteMap := sliceUtil.GetMapOf(usersEmail, true)
	newExceptionUsersProfileList, err := impl.buildExceptionProfilesWithoutDeletedUser(allExceptionUsersProfiles, usersToDeleteMap, userIdForAuditLog)
	if err != nil {
		impl.logger.Errorw("error in removing and returning new users exception config from exception users profiles", "err", err)
		return err
	}

	err = impl.globalPolicyRepository.UpdateInBulk(newExceptionUsersProfileList, nil)
	if err != nil {
		impl.logger.Errorw("error in updating exception users profiles in bulk for onUserDelete hook", "newExceptionUsersProfileList", newExceptionUsersProfileList, "err", err)
		return err
	}
	return nil
}

func (impl *GlobalPolicyDataManagerImpl) buildExceptionProfilesWithoutDeletedUser(allExceptionUsersProfiles []*repository.GlobalPolicy,
	usersToDeleteMap map[string]bool, userIdForAuditLog int32) ([]*repository.GlobalPolicy, error) {
	newExceptionUsersProfileList := make([]*repository.GlobalPolicy, 0, len(allExceptionUsersProfiles))
	for _, exceptionUsersProfile := range allExceptionUsersProfiles {
		exceptionUsersConfig := &model.ExceptionUsersConfig{}
		err := json.Unmarshal([]byte(exceptionUsersProfile.PolicyJson), exceptionUsersConfig)
		if err != nil {
			impl.logger.Errorw("error in fetching all active exception users profiles", "err", err)
			return nil, err
		}
		updatedSpecificUsersSlice := make([]*model.UsersIdentifierConfig, 0, len(exceptionUsersConfig.SpecificUsers))
		for _, specificUsers := range exceptionUsersConfig.SpecificUsers {
			if _, ok := usersToDeleteMap[specificUsers.Identifier]; !ok {
				updatedSpecificUsersSlice = append(updatedSpecificUsersSlice, specificUsers)
			}
		}
		exceptionUsersConfig.SpecificUsers = updatedSpecificUsersSlice
		newExceptionUsersConfigJson, err := json.Marshal(exceptionUsersConfig)
		if err != nil {
			impl.logger.Errorw("error in marshalling new exception users config", "exceptionUsersConfig", exceptionUsersConfig, "err", err)
			return nil, err
		}
		exceptionUsersProfile.PolicyJson = string(newExceptionUsersConfigJson)
		exceptionUsersProfile.UpdateAuditLog(userIdForAuditLog)
		newExceptionUsersProfileList = append(newExceptionUsersProfileList, exceptionUsersProfile)
	}
	return newExceptionUsersProfileList, nil
}

func (impl *GlobalPolicyDataManagerImpl) CreatePolicy(globalPolicyDataModel *bean.GlobalPolicyDataModel, rtx *pg.Tx) (*bean.GlobalPolicyDataModel, error) {
	var err error
	tx := rtx
	if rtx == nil {
		tx, err = impl.globalPolicyRepository.GetDbTransaction()
		if err != nil {
			impl.logger.Errorw("error in initiating transaction", "err", err)
			return nil, err
		}
	}
	// Rollback tx on error.
	defer func() {
		if rtx != nil {
			return
		}
		err = impl.globalPolicyRepository.RollBackTransaction(tx)
		if err != nil {
			impl.logger.Errorw("error in rolling back transaction", "err", err)
		}
	}()
	globalPolicy := impl.getGlobalPolicyDto(globalPolicyDataModel)
	err = impl.globalPolicyRepository.Create(globalPolicy, tx)
	if err != nil {
		impl.logger.Errorw("error, CreatePolicy", "err", err, "globalPolicy", globalPolicy)
		return nil, err
	}
	globalPolicyDataModel.Id = globalPolicy.Id
	searchableKeyEntriesTotal := impl.getSearchableKeyEntries(globalPolicyDataModel)
	err = impl.globalPolicySearchableFieldRepository.CreateInBatchWithTxn(searchableKeyEntriesTotal, tx)
	if err != nil {
		impl.logger.Errorw("error in creating global policy searchable fields entry", "err", err, "searchableKeyEntriesTotal", searchableKeyEntriesTotal)
		return nil, err
	}

	err = impl.globalPolicyHistoryService.CreateHistoryEntry(tx, globalPolicy, bean2.HISTORY_OF_ACTION_CREATE)
	if err != nil {
		impl.logger.Errorw("error in creating policy creation history", "policy", globalPolicy, "action", "create", "err", err)
		return nil, err
	}
	if rtx == nil {
		err = impl.globalPolicyRepository.CommitTransaction(tx)
		if err != nil {
			impl.logger.Errorw("error in committing transaction", "err", err)
			return globalPolicyDataModel, err
		}
	}

	globalPolicyDataModel.Id = globalPolicy.Id
	return globalPolicyDataModel, nil
}

func (impl *GlobalPolicyDataManagerImpl) UpdatePolicy(globalPolicyDataModel *bean.GlobalPolicyDataModel, rtx *pg.Tx) (*bean.GlobalPolicyDataModel, error) {
	var err error
	tx := rtx
	if rtx == nil {
		tx, err = impl.globalPolicyRepository.GetDbTransaction()
		if err != nil {
			impl.logger.Errorw("error in initiating transaction", "err", err)
			return nil, err
		}
	}
	// Rollback tx on error.
	defer func() {
		if rtx != nil {
			return
		}
		err = impl.globalPolicyRepository.RollBackTransaction(tx)
		if err != nil {
			impl.logger.Errorw("error in rolling back transaction", "err", err)
		}
	}()
	globalPolicy := impl.getGlobalPolicyDto(globalPolicyDataModel)
	globalPolicy.Id = globalPolicyDataModel.Id
	err = impl.globalPolicyRepository.Update(globalPolicy, tx)
	if err != nil {
		impl.logger.Errorw("error, UpdatePolicy", "err", err, "globalPolicy", globalPolicy)
		return nil, err
	}
	err = impl.globalPolicySearchableFieldRepository.DeleteByPolicyId(globalPolicy.Id, tx)
	if err != nil {
		impl.logger.Errorw("error in  deleting Policy Searchable key", "globalPolicyDataModel", globalPolicyDataModel, "err", err)
		return nil, err
	}
	searchableKeyEntriesTotal := impl.getSearchableKeyEntries(globalPolicyDataModel)
	err = impl.globalPolicySearchableFieldRepository.CreateInBatchWithTxn(searchableKeyEntriesTotal, tx)
	if err != nil {
		impl.logger.Errorw("error in creating global policy searchable fields entry", "err", err, "searchableKeyEntriesTotal", searchableKeyEntriesTotal)
		return nil, err
	}

	err = impl.globalPolicyHistoryService.CreateHistoryEntry(tx, globalPolicy, bean2.HISTORY_OF_ACTION_UPDATE)
	if err != nil {
		impl.logger.Errorw("error in creating policy creation history", "policy", globalPolicy, "action", "update", "err", err)
		return nil, err
	}
	if rtx == nil {
		err = impl.globalPolicyRepository.CommitTransaction(tx)
		if err != nil {
			impl.logger.Errorw("error in committing transaction", "err", err)
			return globalPolicyDataModel, err
		}
	}
	globalPolicyDataModel.Id = globalPolicy.Id
	return globalPolicyDataModel, nil
}

func (impl *GlobalPolicyDataManagerImpl) UpdatePolicyByName(tx *pg.Tx, PolicyName string, globalPolicyDataModel *bean.GlobalPolicyDataModel) (*bean.GlobalPolicyDataModel, error) {
	globalPolicy := impl.getGlobalPolicyDto(globalPolicyDataModel)
	globalPolicyId, err := impl.globalPolicyRepository.GetIdByName(globalPolicyDataModel.PolicyOf, globalPolicyDataModel.PolicyVersion, PolicyName)
	if err != nil {
		impl.logger.Errorw("error in getting policy id by name", "err", err, "PolicyName", PolicyName)
		return nil, err
	}

	globalPolicy.Id = globalPolicyId
	globalPolicy, err = impl.globalPolicyRepository.UpdatePolicyByName(PolicyName, globalPolicy, tx)
	if err != nil {
		impl.logger.Errorw("error, UpdatePolicy", "err", err, "globalPolicy", globalPolicy)
		return nil, err
	}
	err = impl.globalPolicySearchableFieldRepository.DeleteByPolicyId(globalPolicy.Id, tx)
	if err != nil {
		impl.logger.Errorw("error in  deleting Policy Searchable key", "globalPolicyDataModel", globalPolicyDataModel, "err", err)
		return nil, err
	}
	searchableKeyEntriesTotal := impl.getSearchableKeyEntries(globalPolicyDataModel)
	err = impl.globalPolicySearchableFieldRepository.CreateInBatchWithTxn(searchableKeyEntriesTotal, tx)
	if err != nil {
		impl.logger.Errorw("error in creating global policy searchable fields entry", "err", err, "searchableKeyEntriesTotal", searchableKeyEntriesTotal)
		return nil, err
	}
	err = impl.globalPolicyHistoryService.CreateHistoryEntry(tx, globalPolicy, bean2.HISTORY_OF_ACTION_UPDATE)
	if err != nil {
		impl.logger.Errorw("error in creating policy creation history", "policy", globalPolicy, "action", "update", "err", err)
		return nil, err
	}
	globalPolicyDataModel.Id = globalPolicy.Id
	return globalPolicyDataModel, nil
}

func (impl *GlobalPolicyDataManagerImpl) getSearchableKeyEntries(globalPolicyDataModel *bean.GlobalPolicyDataModel) []*repository.GlobalPolicySearchableField {
	searchableKeyEntriesTotal := make([]*repository.GlobalPolicySearchableField, 0)
	for _, field := range globalPolicyDataModel.SearchableFields {
		searchableKeyEntries := &repository.GlobalPolicySearchableField{
			GlobalPolicyId: globalPolicyDataModel.Id,
			IsRegex:        false,
			FieldName:      field.FieldName,
		}
		switch field.FieldType {
		case util.NumericType:
			searchableKeyEntries.ValueInt = field.FieldValue.(int)
		case util.StringType:
			searchableKeyEntries.Value = field.FieldValue.(string)
		case util.DateTimeType:
			searchableKeyEntries.ValueTimeStamp = field.FieldValue.(time.Time)
		}

		searchableKeyEntries.CreateAuditLog(globalPolicyDataModel.UserId)
		searchableKeyEntriesTotal = append(searchableKeyEntriesTotal, searchableKeyEntries)
	}
	return searchableKeyEntriesTotal
}

func (impl *GlobalPolicyDataManagerImpl) getGlobalPolicyDto(globalPolicyDataModel *bean.GlobalPolicyDataModel) *repository.GlobalPolicy {
	globalPolicy := &repository.GlobalPolicy{
		Name:        globalPolicyDataModel.Name,
		PolicyOf:    string(globalPolicyDataModel.PolicyOf),
		Version:     string(bean.GLOBAL_POLICY_VERSION_V1),
		Description: globalPolicyDataModel.Description,
		PolicyJson:  globalPolicyDataModel.JsonData,
		Enabled:     globalPolicyDataModel.Enabled,
		Deleted:     false,
	}
	if len(globalPolicyDataModel.PolicyVersion) > 0 {
		globalPolicy.Version = globalPolicyDataModel.PolicyVersion.ToString()
	}
	if len(globalPolicyDataModel.PolicyRevision) > 0 {
		globalPolicy.PolicyRevision = globalPolicyDataModel.PolicyRevision
	}
	globalPolicy.CreateAuditLog(globalPolicyDataModel.UserId)
	return globalPolicy
}

func (impl *GlobalPolicyDataManagerImpl) GetPolicyByNameAndType(policyType bean.GlobalPolicyType, policyVersion bean.GlobalPolicyVersion, policyName string) (*bean.GlobalPolicyBaseModel, error) {
	globalPolicy, err := impl.globalPolicyRepository.GetByName(policyType, policyVersion, policyName)
	if err != nil {
		impl.logger.Errorw("error in fetching global policy", "policyName", policyName, "policyVersion", policyVersion, "err", err)
		return nil, err
	}
	return globalPolicy.GetGlobalPolicyBaseModel(), nil
}

func (impl *GlobalPolicyDataManagerImpl) GetPolicyByNames(policyType bean.GlobalPolicyType, policyVersion bean.GlobalPolicyVersion, policyNames []string) ([]*bean.GlobalPolicyBaseModel, error) {
	globalPolicies, err := impl.globalPolicyRepository.GetByNames(policyType, policyVersion, policyNames)
	if err != nil {
		impl.logger.Errorw("error in fetching global policy",
			"policyNames", policyNames, "policyType", policyType, "policyVersion", policyVersion, "err", err)
		return nil, err
	}

	policyBaseModels := make([]*bean.GlobalPolicyBaseModel, 0, len(globalPolicies))
	for _, globalPolicy := range globalPolicies {
		policyBaseModels = append(policyBaseModels, globalPolicy.GetGlobalPolicyBaseModel())
	}
	return policyBaseModels, nil
}

func (impl *GlobalPolicyDataManagerImpl) GetPolicyMetadataByFields(policyIds []int, fields []*util.SearchableField) (map[int][]*util.SearchableField, error) {
	var policyIdToSearchableField map[int][]*util.SearchableField
	GlobalPolicySearchableFields, err := impl.globalPolicySearchableFieldRepository.GetSearchableFieldByIds(policyIds)
	if err != nil {
		impl.logger.Errorw("error in fetching GlobalPolicySearchableFields", "err", err)
		return nil, err
	}
	fieldNames := make(map[string]bool)
	for _, field := range fields {
		fieldNames[field.FieldName] = true
	}
	for _, searchableField := range GlobalPolicySearchableFields {
		if _, ok := fieldNames[searchableField.FieldName]; ok {
			fieldValue, fieldType := impl.setFieldValueAndType(searchableField)
			policyIdToSearchableField = impl.setPolicyIdToSearchableFieldMap(searchableField, fieldType, fieldValue)
		}
	}
	return policyIdToSearchableField, nil
}

func (impl *GlobalPolicyDataManagerImpl) setPolicyIdToSearchableFieldMap(searchableField *repository.GlobalPolicySearchableField, fieldType util.FieldType, fieldValue interface{}) map[int][]*util.SearchableField {
	policyIdToSearchableField := make(map[int][]*util.SearchableField, 0)
	if policyIdToSearchableField[searchableField.GlobalPolicyId] != nil {
		policyIdToSearchableField[searchableField.GlobalPolicyId] = append(policyIdToSearchableField[searchableField.GlobalPolicyId], &util.SearchableField{
			FieldName:  searchableField.FieldName,
			FieldType:  fieldType,
			FieldValue: fieldValue,
		})
	} else {
		policyIdToSearchableField[searchableField.GlobalPolicyId] = []*util.SearchableField{
			{
				FieldName:  searchableField.FieldName,
				FieldType:  fieldType,
				FieldValue: fieldValue,
			},
		}
	}
	return policyIdToSearchableField
}

func (impl *GlobalPolicyDataManagerImpl) setFieldValueAndType(searchableField *repository.GlobalPolicySearchableField) (interface{}, util.FieldType) {
	var fieldValue interface{}
	var fieldType util.FieldType
	if searchableField.Value != "" {
		fieldValue = searchableField.Value
		fieldType = util.StringType
	} else if searchableField.ValueInt != 0 {
		fieldValue = searchableField.ValueInt
		fieldType = util.NumericType
	} else if !searchableField.ValueTimeStamp.IsZero() {
		fieldValue = searchableField.ValueTimeStamp
		fieldType = util.DateTimeType

	}
	return fieldValue, fieldType
}

func (impl *GlobalPolicyDataManagerImpl) GetPolicyById(policyId int) (*bean.GlobalPolicyBaseModel, error) {
	globalPolicy, err := impl.globalPolicyRepository.GetById(policyId)
	if err != nil {
		impl.logger.Errorw("error in fetching global policy", "policyId", policyId, "err", err)
		return nil, err
	}
	return globalPolicy.GetGlobalPolicyBaseModel(), nil
}

func (impl *GlobalPolicyDataManagerImpl) GetAllActiveByType(policyType bean.GlobalPolicyType, version bean.GlobalPolicyVersion) ([]*bean.GlobalPolicyBaseModel, error) {
	policies, err := impl.globalPolicyRepository.GetAllActiveByType(policyType, version)
	if err != nil {
		return nil, err
	}
	baseModels := sliceUtil.NewSliceFromFuncExec(policies, func(policy *repository.GlobalPolicy) *bean.GlobalPolicyBaseModel {
		return policy.GetGlobalPolicyBaseModel()
	})

	return baseModels, nil
}

func (impl *GlobalPolicyDataManagerImpl) GetPolicyByIds(policyIds []int) ([]*bean.GlobalPolicyBaseModel, error) {
	GlobalPolicyBaseModels := make([]*bean.GlobalPolicyBaseModel, 0)
	if len(policyIds) == 0 {
		return GlobalPolicyBaseModels, nil
	}
	globalPolicies, err := impl.globalPolicyRepository.GetByIds(policyIds)
	if err != nil {
		impl.logger.Errorw("error in fetching global policy", "policyIds", policyIds, "err", err)
		return nil, err
	}
	for _, policy := range globalPolicies {
		GlobalPolicyBaseModels = append(GlobalPolicyBaseModels, policy.GetGlobalPolicyBaseModel())
	}
	return GlobalPolicyBaseModels, nil
}

func (impl *GlobalPolicyDataManagerImpl) GetPolicyDefinitionByIds(policyIds []int) ([]*bean.GlobalPolicyBaseModel, error) {
	GlobalPolicyBaseModels := make([]*bean.GlobalPolicyBaseModel, 0)
	if len(policyIds) == 0 {
		return GlobalPolicyBaseModels, nil
	}
	globalPolicies, err := impl.globalPolicyRepository.GetPolicyDefinitionByIds(policyIds)
	if err != nil {
		impl.logger.Errorw("error in fetching global policy", "policyIds", policyIds, "err", err)
		return nil, err
	}
	for _, policy := range globalPolicies {
		GlobalPolicyBaseModels = append(GlobalPolicyBaseModels, policy.GetGlobalPolicyBaseModel())
	}
	return GlobalPolicyBaseModels, nil
}

func (impl *GlobalPolicyDataManagerImpl) DeletePolicyById(tx *pg.Tx, policyId int, userId int32) error {
	globalPolicy, err := impl.globalPolicyRepository.GetById(policyId)
	if err != nil {
		impl.logger.Errorw("error in getting policy by id", "err", err, "policyId", policyId)
		return err
	}
	err = impl.globalPolicyRepository.DeletedById(tx, policyId, userId)
	if err != nil {
		impl.logger.Errorw("error in deleting policies", "err", err, "policyId", policyId)
		return err
	}

	err = impl.globalPolicyHistoryService.CreateHistoryEntry(tx, globalPolicy, bean2.HISTORY_OF_ACTION_CREATE)
	if err != nil {
		impl.logger.Errorw("error in creating policy action history", "policy", globalPolicy, "action", "delete", "err", err)
		return err
	}
	return nil
}

func (impl *GlobalPolicyDataManagerImpl) DeletePolicyByName(tx *pg.Tx, policyType bean.GlobalPolicyType, policyVersion bean.GlobalPolicyVersion, policyName string, userId int32) error {
	globalPolicy, err := impl.globalPolicyRepository.GetByName(policyType, policyVersion, policyName)
	if err != nil {
		impl.logger.Errorw("error in getting policy by name",
			"err", err, "policyName", policyName, "policyType", policyType, "policyVersion", policyVersion)
		return err
	}
	err = impl.globalPolicyRepository.DeletedByName(tx, policyType, policyVersion, policyName, userId)
	if err != nil {
		impl.logger.Errorw("error in deleting policies", "err", err, "policyName", policyName)
		return err
	}
	err = impl.globalPolicyHistoryService.CreateHistoryEntry(tx, globalPolicy, bean2.HISTORY_OF_ACTION_DELETE)
	if err != nil {
		impl.logger.Errorw("error in creating policy action history", "policy", globalPolicy, "action", "delete", "err", err)
		return err
	}
	return err

}

func (impl *GlobalPolicyDataManagerImpl) GetPolicyByCriteria(policyType bean.GlobalPolicyType, policyVersion bean.GlobalPolicyVersion, policyNamePattern string, sortRequest *bean.SortByRequest) ([]*bean.GlobalPolicyBaseModel, error) {
	policyNamePattern = "%" + policyNamePattern + "%"
	orderByName := false
	if sortRequest.SortByType == bean.GlobalPolicyColumnField {
		orderByName = true
	}

	globalPolicies, err := impl.globalPolicyRepository.GetByNameSearchKey(policyType, policyVersion, policyNamePattern, orderByName, sortRequest.SortOrderDesc)
	if err != nil {
		impl.logger.Errorw("error in getting global policy by name search string", "err", err)
		return nil, err
	}
	globalPolicyIdToDaoMap := impl.getGlobalPolicyIdToDaoMapping(globalPolicies)

	globalPolicyBaseModels := make([]*bean.GlobalPolicyBaseModel, 0)

	if sortRequest.SortByType == bean.GlobalPolicySearchableField {
		globalPolicySortedOrder, err2 := impl.getGlobalPolicySortedOrder(globalPolicies, sortRequest)
		if err2 != nil {
			impl.logger.Errorw("error in sorting globalPolicies", "sortRequest", sortRequest, "err", err)
			return globalPolicyBaseModels, err2
		}
		for _, globalPolicySearchableKeyModel := range globalPolicySortedOrder {
			policyId := globalPolicySearchableKeyModel.GlobalPolicyId
			policy := globalPolicyIdToDaoMap[policyId]
			globalPolicyBaseModels = append(globalPolicyBaseModels, policy.GetGlobalPolicyBaseModel())
		}
	} else {
		for _, policy := range globalPolicies {
			globalPolicyBaseModels = append(globalPolicyBaseModels, policy.GetGlobalPolicyBaseModel())
		}
	}

	return globalPolicyBaseModels, nil
}

func (impl *GlobalPolicyDataManagerImpl) getGlobalPolicyIdToDaoMapping(globalPolicies []*repository.GlobalPolicy) map[int]*repository.GlobalPolicy {
	globalPolicyIdToDaoMap := make(map[int]*repository.GlobalPolicy)
	for _, globalPolicy := range globalPolicies {
		globalPolicyIdToDaoMap[globalPolicy.Id] = globalPolicy
	}
	return globalPolicyIdToDaoMap
}

func getBaseModelFromHistories(globalPolicyHistories []*repository2.GlobalPolicyHistory) []*bean.GlobalPolicyBaseModel {
	globalBaseModels := make([]*bean.GlobalPolicyBaseModel, 0, len(globalPolicyHistories))
	for _, globalPolicyHistory := range globalPolicyHistories {
		globalBaseModels = append(globalBaseModels, &bean.GlobalPolicyBaseModel{
			Id:          globalPolicyHistory.GlobalPolicyId,
			PolicyOf:    bean.GlobalPolicyType(globalPolicyHistory.PolicyOf),
			Description: globalPolicyHistory.Description,
			Enabled:     globalPolicyHistory.Enabled,
			JsonData:    globalPolicyHistory.PolicyData,
		})
	}
	return globalBaseModels
}

func (impl *GlobalPolicyDataManagerImpl) getGlobalPolicySortedOrder(globalPolicies []*repository.GlobalPolicy, sortRequest *bean.SortByRequest) ([]*repository.GlobalPolicySearchableField, error) {
	globalPolicyIds := make([]int, 0)
	for _, globalPolicy := range globalPolicies {
		globalPolicyIds = append(globalPolicyIds, globalPolicy.Id)
	}
	globalPolicySortedOrder, err := impl.globalPolicySearchableFieldRepository.GetSortedPoliciesByPolicyKey(
		globalPolicyIds, sortRequest.SearchableField, sortRequest.SortOrderDesc)
	if err != nil {
		impl.logger.Errorw("error in sorting policies by policy key", "policyKey", sortRequest.SearchableField.FieldName)
		return nil, err
	}
	return globalPolicySortedOrder, nil
}

func (impl *GlobalPolicyDataManagerImpl) GetPolicyIdByName(policyType bean.GlobalPolicyType, policyVersion bean.GlobalPolicyVersion, name string) (int, error) {
	return impl.globalPolicyRepository.GetIdByName(policyType, policyVersion, name)
}

func (impl *GlobalPolicyDataManagerImpl) CheckIfNameExists(name string, policyType bean.GlobalPolicyType, policyVersion bean.GlobalPolicyVersion) (bool, error) {
	if policyVersion == bean.GLOBAL_POLICY_VERSION_ANY {
		return impl.globalPolicyRepository.CheckIfNameExists(name, policyType, "")
	}
	return impl.globalPolicyRepository.CheckIfNameExists(name, policyType, policyVersion)
}

func (impl *GlobalPolicyDataManagerImpl) GetAllActivePoliciesByType(policyType bean.GlobalPolicyType, version bean.GlobalPolicyVersion) ([]*bean.GlobalPolicyBaseModel, error) {
	models, err := impl.globalPolicyRepository.GetAllActiveByType(policyType, version)
	if err != nil {
		return nil, err
	}
	baseModels := make([]*bean.GlobalPolicyBaseModel, 0)
	for _, model := range models {
		baseModels = append(baseModels, model.GetGlobalPolicyBaseModel())
	}
	return baseModels, nil
}

func (impl *GlobalPolicyDataManagerImpl) GetAllActivePolicyIdsByType(policyType bean.GlobalPolicyType, version bean.GlobalPolicyVersion) ([]int, error) {
	models, err := impl.globalPolicyRepository.GetAllActiveByType(policyType, version)
	if err != nil {
		return nil, err
	}
	baseModelIds := make([]int, 0)
	for _, model := range models {
		baseModelIds = append(baseModelIds, model.Id)
	}
	return baseModelIds, nil
}

func (impl *GlobalPolicyDataManagerImpl) GetAllV2PluginPolicyIdsWithPlugins(pluginIds []int) ([]int, error) {
	policies, err := impl.globalPolicyRepository.GetAllByPolicyOfAndVersion(bean.GLOBAL_POLICY_TYPE_PLUGIN, bean.GLOBAL_POLICY_VERSION_V2)
	if err != nil {
		return nil, err
	}
	var pluginPolicyIds []int
	for _, policy := range policies {
		results := gjson.Get(policy.PolicyJson, bean4.PluginPolicyRules.String())
		if results.IsArray() {
			results.ForEach(func(_, value gjson.Result) bool {
				if value.IsObject() {
					pluginParentId := int(value.Get(bean4.RuleParentPluginId.String()).Int())
					if slices.Contains(pluginIds, pluginParentId) {
						pluginPolicyIds = append(pluginPolicyIds, policy.Id)
					}
				}
				return true
			})
		}
	}
	return pluginPolicyIds, nil
}

func (impl *GlobalPolicyDataManagerImpl) GetAllActivePoliciesByMode(policyType bean.GlobalPolicyType, policyVersion bean.GlobalPolicyVersion, lite bool) ([]*bean.GlobalPolicyBaseModel, error) {
	models, err := impl.globalPolicyRepository.GetAllActiveByTypeLiteMode(policyType, policyVersion, lite)
	if err != nil {
		return nil, err
	}
	baseModels := make([]*bean.GlobalPolicyBaseModel, 0)
	for _, model := range models {
		baseModels = append(baseModels, model.GetGlobalPolicyBaseModel())
	}
	return baseModels, nil

}

func (impl *GlobalPolicyDataManagerImpl) GetPoliciesByHistoryIds(historyIds []int) ([]*bean.GlobalPolicyBaseModel, error) {
	historyObjects, err := impl.globalPolicyHistoryService.GetByIds(historyIds)
	if err != nil {
		return nil, err
	}
	return getBaseModelFromHistories(historyObjects), nil
}

func (impl *GlobalPolicyDataManagerImpl) GetPolicyHistoryIdsByPolicyIds(policyIds []int) ([]int, error) {
	return impl.globalPolicyHistoryService.GetIdsByPolicyIds(policyIds)
}

func (impl *GlobalPolicyDataManagerImpl) GetAllActivePoliciesCount() (map[string]int, error) {
	return impl.globalPolicyRepository.GetAllPoliciesCount()
}

// GetGloballyAppliedPolicyByRqmResourceType fetches GlobalPolicyBaseModel by resourceType from rqm table
func (impl *GlobalPolicyDataManagerImpl) GetGloballyAppliedPolicyByRqmResourceType(resourceType int) (*bean.GlobalPolicyBaseModel, error) {
	model, err := impl.globalPolicyRepository.GetGloballyAppliedPolicyByRqmResourceType(resourceType)
	if err != nil {
		impl.logger.Errorw("error in getting globally applied policy by rqm resource type", "rqmResourceType", resourceType)
		// if not found then returns ErrNoRows
		return nil, err
	}
	return model.GetGlobalPolicyBaseModel(), nil
}

func (impl *GlobalPolicyDataManagerImpl) IsUserAnExceptionByRqmResourceType(ctx context.Context, resourceType int, userMetadata *bean3.UserMetadata) (bool, error) {
	globalPolicy, err := impl.GetGloballyAppliedPolicyByRqmResourceType(resourceType)
	if err != nil && !errorUtil.IsErrNoRows(err) {
		impl.logger.Errorw("error in getting globally applied policy by rqm resource type", "rqmResourceType", resourceType, "err", err)
		return false, err
	} else if errorUtil.IsErrNoRows(err) {
		// exception user profile not saved yet return as exception user false
		return false, nil
	}
	exceptionUsersEmailMap, enabledAtSuperAdminLevel, err := impl.extractExceptionUsersMap(globalPolicy.JsonData)
	if err != nil {
		impl.logger.Errorw("error in extracting current exceptions users map ", "rqmResourceType", resourceType, "err", err)
		return false, err
	}
	isUserException := exceptionUsersEmailMap[userMetadata.UserEmailId] || ((enabledAtSuperAdminLevel == true) && (userMetadata.IsUserSuperAdmin == true))

	return isUserException, nil
}

// extractExceptionUsersMap returns overall users email map(specific users email +userGroups users email) from globalPolicyJsonData
func (impl *GlobalPolicyDataManagerImpl) extractExceptionUsersMap(globalPolicyJsonData string) (map[string]bool, bool, error) {
	userEmailMap := make(map[string]bool)
	exceptionUserConfig := &model.ExceptionUsersConfig{}
	err := json.Unmarshal([]byte(globalPolicyJsonData), exceptionUserConfig)
	if err != nil {
		impl.logger.Errorw("error occurred in unMarshaling policy json for exception user", "policyJson", globalPolicyJsonData, "err", err)
		return nil, false, err
	}
	// filling specific users email in map
	for _, specificUser := range exceptionUserConfig.SpecificUsers {
		userEmailMap[specificUser.Identifier] = true
	}

	// filling user group users email in map, for that first fetch all user group to mail mappings
	userGroupIdentifiers := make([]string, 0, len(exceptionUserConfig.UserGroups))
	for _, userGroup := range exceptionUserConfig.UserGroups {
		userGroupIdentifiers = append(userGroupIdentifiers, userGroup.Identifier)
	}
	userGroupEmailMap, err := impl.userGroupService.GetUserMapByUserGroupIdentifiers(userGroupIdentifiers)
	if err != nil {
		impl.logger.Errorw("error occurred getting users mapping from user groups using user group identifiers ", "userGroupIdentifiers", userGroupIdentifiers, "err", err)
		return nil, false, err
	}
	maps.Copy(userEmailMap, userGroupEmailMap)

	return userEmailMap, exceptionUserConfig.EnabledAtSuperAdminLevel, nil
}
