package globalPolicy

import (
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	repository2 "github.com/devtron-labs/devtron/pkg/pipeline/repository"
)

type BlockedStateInternalRequestDTO struct {
	MandatoryPluginInternalRequest *MandatoryPluginFetchInternalRequestDTO
	ConfiguredPlugins              []*repository2.PipelineStageStep
	RefPluginIdVsParentPluginIdMap map[int]int
}

type MandatoryPluginFetchInternalRequestDTO struct {
	CiPipelineId                  int
	CiPipelineAppProj             []*pipelineConfig.CiPipelineAppProject
	CiPipelineIdToClusterEnvMap   map[int][]*pipelineConfig.CiPipelineEnvCluster
	BranchValues                  []string
	ToOnlyGetBlockedStatePolicies bool
}
