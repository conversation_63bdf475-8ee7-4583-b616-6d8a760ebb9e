package read

import (
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/internal/util"
	pipelineBean "github.com/devtron-labs/devtron/pkg/pipeline/bean"
	"github.com/devtron-labs/devtron/pkg/workflow/cd/adapter"
	"github.com/devtron-labs/devtron/pkg/workflow/cd/bean"
	"github.com/go-pg/pg"
	"go.uber.org/zap"
)

type CdWorkflowRunnerReadService interface {
	FindWorkflowRunnerById(wfrId int) (*bean.CdWorkflowRunnerDto, error)
	CheckIfWfrLatest(wfrId, pipelineId int) (isLatest bool, err error)
	FindPartialWorkflowRunnerWithPipelineIdByIds(wfrIds []int) ([]*bean.CdWorkflowRunnerDto, error)
	FindLatestWorkflowRunnerByAppAndEnvId(appId, envId int) (*bean.CdWorkflowRunnerDto, error)
	FetchLatestRunnerStatusMapForPipelineIds(pipelineIds []int, envId int) (map[int]string, error)
}

type CdWorkflowRunnerReadServiceImpl struct {
	logger               *zap.SugaredLogger
	cdWorkflowRepository pipelineConfig.CdWorkflowRepository
}

func NewCdWorkflowRunnerReadServiceImpl(logger *zap.SugaredLogger,
	cdWorkflowRepository pipelineConfig.CdWorkflowRepository) *CdWorkflowRunnerReadServiceImpl {
	return &CdWorkflowRunnerReadServiceImpl{
		logger:               logger,
		cdWorkflowRepository: cdWorkflowRepository,
	}
}

func (impl *CdWorkflowRunnerReadServiceImpl) FindWorkflowRunnerById(wfrId int) (*bean.CdWorkflowRunnerDto, error) {
	cdWfr, err := impl.cdWorkflowRepository.FindWorkflowRunnerById(wfrId)
	if err != nil {
		impl.logger.Errorw("error in getting cd workflow runner by id", "err", err, "id", wfrId)
		return nil, err
	}
	return adapter.ConvertCdWorkflowRunnerDbObjToDto(cdWfr), nil

}

func (impl *CdWorkflowRunnerReadServiceImpl) CheckIfWfrLatest(wfrId, pipelineId int) (isLatest bool, err error) {
	isLatest, err = impl.cdWorkflowRepository.IsLatestCDWfr(wfrId, pipelineId)
	if err != nil && err != pg.ErrNoRows {
		impl.logger.Errorw("err in checking latest cd workflow runner", "err", err)
		return false, err
	}
	return isLatest, nil
}

// FindPartialWorkflowRunnerWithPipelineIdByIds find basic details for cd workflow runner like status, id and workflow_type with pipeline id
func (impl *CdWorkflowRunnerReadServiceImpl) FindPartialWorkflowRunnerWithPipelineIdByIds(wfrIds []int) ([]*bean.CdWorkflowRunnerDto, error) {
	wfrs, err := impl.cdWorkflowRepository.FindPartialWorkflowRunnerWithPipelineIdByIds(wfrIds)
	if err != nil {
		impl.logger.Errorw("error, FindPartialWorkflowRunnerWithPipelineIdByIds", "wfrIds", wfrIds, "err", err)
		return nil, err
	}
	var wfrDtos []*bean.CdWorkflowRunnerDto
	for _, wfr := range wfrs {
		wfrDto := &bean.CdWorkflowRunnerDto{
			Id:           wfr.Id,
			Status:       wfr.Status,
			StartedOn:    wfr.StartedOn,
			WorkflowType: wfr.WorkflowType,
		}
		if wfr.CdWorkflow != nil {
			wfrDto.PipelineId = wfr.CdWorkflow.PipelineId
		}
		wfrDtos = append(wfrDtos, wfrDto)
	}
	return wfrDtos, err
}

// FindLatestWorkflowRunnerByAppAndEnvId gets latest wfr basic detail
func (impl *CdWorkflowRunnerReadServiceImpl) FindLatestWorkflowRunnerByAppAndEnvId(appId, envId int) (*bean.CdWorkflowRunnerDto, error) {
	wfr, err := impl.cdWorkflowRepository.FindLatestWfrByAppIdAndEnvironmentId(appId, envId)
	if err != nil {
		impl.logger.Errorw("error, FindPartialWorkflowRunnerWithPipelineIdByIds", "appId", appId, "envId", envId, "err", err)
		return nil, err
	}
	wfrDto := &bean.CdWorkflowRunnerDto{
		Id:           wfr.Id,
		Status:       wfr.Status,
		StartedOn:    wfr.StartedOn,
		WorkflowType: wfr.WorkflowType,
	}
	if wfr.CdWorkflow != nil {
		wfrDto.PipelineId = wfr.CdWorkflow.PipelineId
	}
	return wfrDto, nil
}

func (impl *CdWorkflowRunnerReadServiceImpl) FetchLatestRunnerStatusMapForPipelineIds(pipelineIds []int, envId int) (map[int]string, error) {
	deploymentStatusesMap := make(map[int]string)
	statusMap := make(map[int]string)
	result, err := impl.cdWorkflowRepository.FetchAllCdStagesLatestEntity(pipelineIds)
	if err != nil {
		impl.logger.Errorw("error in fetching pipelines", "pipelineIds", pipelineIds, "err", err)
		return deploymentStatusesMap, err
	}
	var wfrIds []int
	for _, item := range result {
		wfrIds = append(wfrIds, item.WfrId)
	}
	if len(wfrIds) > 0 {
		wfrList, err := impl.cdWorkflowRepository.FetchEnvAllCdStagesLatestEntityStatus(wfrIds, envId)
		if err != nil && !util.IsErrNoRows(err) {
			impl.logger.Errorw("error in fetching wfrList", "wfrIds", wfrIds, "err", err)
			return deploymentStatusesMap, err
		}
		for _, item := range wfrList {
			statusMap[item.Id] = item.Status
		}
	}

	for _, item := range result {
		if _, ok := deploymentStatusesMap[item.PipelineId]; !ok {
			if item.WorkflowType == pipelineBean.WorkflowTypeDeploy {
				deploymentStatusesMap[item.PipelineId] = statusMap[item.WfrId]
			}
		}
	}
	return deploymentStatusesMap, nil
}
