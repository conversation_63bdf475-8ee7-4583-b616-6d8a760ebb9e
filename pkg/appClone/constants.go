package appClone

const (
	MessageTargetPipelineExists = "Target pipeline already exists"
	MessageTargetVirtual        = "Environment of source pipeline is not virtual but environment of target pipeline is virtual"
	MessageSourceNotCI          = "The source pipeline is not positioned after image source in the workflow"
	MessageCloneSuccessful      = "Clone successful"
	MessageValidationSuccessful = "Validation successful"
	StatusSkipped               = "SKIPPED"
	StatusSuccessful            = "SUCCESS"
	StatusFailed                = "FAILED"
)
