/*
 * Copyright (c) 2024. Devtron Inc.
 */

package chart

import (
	"context"
	"errors"
	"fmt"
	"github.com/devtron-labs/devtron/internal/sql/repository/chartConfig"
	bean3 "github.com/devtron-labs/devtron/pkg/chart/bean"
	chartRefBean "github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/chartRef/bean"
	approvalConfigBeans "github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/bean"
	"github.com/devtron-labs/devtron/pkg/resourceQualifiers"
	ctxUtil "github.com/devtron-labs/devtron/util"
	"github.com/devtron-labs/devtron/util/sliceUtil"
	"github.com/go-pg/pg"
	"go.opentelemetry.io/otel"
	"slices"
)

type ChartServiceEnt interface {
	ValidateAppOverride(templateRequest *bean3.TemplateRequest, token string) (*bean3.TemplateResponse, error)

	HandlePostChartChangeOperations(ctx context.Context, request *bean3.PostChartChangeRequest) error
	ChangeApplicationTemplateChart(ctx *ctxUtil.RequestCtx, userId int32, request *bean3.TemplateRefChangeRequest) error
}

func (impl *ChartServiceImpl) ValidateAppOverride(templateRequest *bean3.TemplateRequest, token string) (*bean3.TemplateResponse, error) {
	template, err := impl.chartRepository.FindById(templateRequest.Id)
	if err != nil {
		impl.logger.Errorw("error in fetching chart config", "id", templateRequest.Id, "err", err)
		return nil, err
	}
	//STARTS
	impl.logger.Debug("now finally update request chart in db to latest and previous flag = false")

	// Handle Lock Configuration
	lockConfigErrorResponse, err := impl.lockedConfigService.HandleLockConfiguration(string(templateRequest.ValuesOverride), template.GlobalOverride, token, int(templateRequest.UserId), templateRequest.AppId, resourceQualifiers.BaseDeploymentTemplateInt)
	if err != nil {
		return nil, err
	}
	if lockConfigErrorResponse != nil {
		return &bean3.TemplateResponse{
			TemplateRequest:           templateRequest,
			LockValidateErrorResponse: lockConfigErrorResponse,
		}, nil
	}
	return &bean3.TemplateResponse{TemplateRequest: templateRequest, LockValidateErrorResponse: nil}, nil
}

// HandlePostChartChangeOperations - this method is called after a chart is changed for a pipeline.
// 1. delete all the unpublished drafts for the deployment templates of the pipeline.
// 2. verify the deployment strategy for a the cd_pipeline:
//   - if any current strategy is not supported for the requested chart_ref, delete it.
//   - if any strategy is left, then make it default.
//   - if there is no strategy, then add the default strategy of the requested chart_ref.
func (impl *ChartServiceImpl) HandlePostChartChangeOperations(ctx context.Context, request *bean3.PostChartChangeRequest) error {
	newCtx, span := otel.Tracer("orchestrator").Start(ctx, "ChartServiceImpl.HandlePostChartChangeOperations")
	defer span.End()
	tx, err := impl.chartRepository.StartTx()
	if err != nil {
		impl.logger.Errorw("error in starting the transaction", "err", err)
		return err
	}
	defer impl.chartRepository.RollbackTx(tx)
	err = impl.resourceProtectionService.DiscardDraftInTx(tx, approvalConfigBeans.APPROVAL_FOR_CONFIGURATION_DT, request.AppId, request.EnvId, request.UserId)
	if err != nil {
		impl.logger.Errorw("error in discarding draft hooks for app", "appId", request.AppId, "envId", request.EnvId, "err", err)
		return err
	}
	if request.IsPipelineStrategySupported() && request.PipelineId > 0 {
		latestChart, err := impl.chartRepository.FindLatestChartForAppByAppId(tx, request.AppId)
		if err != nil {
			impl.logger.Errorw("error in fetching chartModels", "appId", request.AppId, "err", err)
			return err
		}
		pipelineStrategies, err := impl.chartRefService.GetDeploymentStrategiesForChartRef(request.TargetChartRefId, latestChart.PipelineOverride)
		if err != nil {
			impl.logger.Errorw("error in getting deployment strategies", "chartRefId", request.TargetChartRefId, "err", err)
			return err
		}
		if len(pipelineStrategies) == 0 {
			impl.logger.Infow("no strategies supported for chart", "chartRefId", request.TargetChartRefId, "pipelineId", request.PipelineId)
			err = impl.pipelineConfigRepository.HardDeleteAllByPipelineId(tx, request.PipelineId)
			if err != nil {
				impl.logger.Errorw("error in deleting all the pipeline strategies for app", "pipelineId", request.PipelineId, "err", err)
				return err
			}
		} else {
			err = impl.changePipelineStrategies(newCtx, tx, request.UserId, request.PipelineId, pipelineStrategies)
			if err != nil {
				impl.logger.Errorw("error in changing pipeline strategies", "pipelineId", request.PipelineId, "err", err)
				return err
			}
		}
	}
	err = impl.chartRepository.CommitTx(tx)
	if err != nil {
		impl.logger.Errorw("error in committing the transaction", "err", err)
		return err
	}
	return nil
}

func (impl *ChartServiceImpl) createDefaultStrategyIfRequired(ctx context.Context, tx *pg.Tx, strategies []*chartConfig.PipelineStrategy,
	pipelineStrategies []chartRefBean.PipelineStrategy, userId int32) error {
	newCtx, span := otel.Tracer("orchestrator").Start(ctx, "ChartServiceImpl.createDefaultStrategyIfRequired")
	defer span.End()
	for _, strategy := range strategies {
		if strategy.Default && !slices.ContainsFunc(pipelineStrategies, func(pipelineStrategy chartRefBean.PipelineStrategy) bool {
			return pipelineStrategy.DeploymentTemplate == strategy.Strategy
		}) {
			pipelineModel, err := impl.pipelineRepository.FindById(strategy.PipelineId)
			if err != nil && !errors.Is(err, pg.ErrNoRows) {
				impl.logger.Errorw("error in fetching pipeline", "pipelineId", strategy.PipelineId, "err", err)
				return err
			} else if errors.Is(err, pg.ErrNoRows) {
				impl.logger.Warnw("no pipeline found for strategy", "strategy", strategy)
				continue
			}
			defaultStrategyIndex, exists := sliceUtil.Find(pipelineStrategies, func(pipelineStrategy chartRefBean.PipelineStrategy) bool {
				return pipelineStrategy.Default
			})
			if !exists {
				impl.logger.Errorw("no default strategy found for app", "pipelineId", strategy.PipelineId)
				return fmt.Errorf("no default strategy found for the chart")
			}
			if existingDefaultStrategyIndex, defaultFound := sliceUtil.Find(strategies, func(existingStrategy *chartConfig.PipelineStrategy) bool {
				return existingStrategy.Strategy == pipelineStrategies[defaultStrategyIndex].DeploymentTemplate &&
					strategy.PipelineId == existingStrategy.PipelineId
			}); !defaultFound {
				newDefaultStrategy := &chartConfig.PipelineStrategy{
					PipelineId: strategy.PipelineId,
					Strategy:   pipelineStrategies[defaultStrategyIndex].DeploymentTemplate,
					Config:     string(pipelineStrategies[defaultStrategyIndex].Config),
					Default:    true,
					Deleted:    false,
				}
				newDefaultStrategy.CreateAuditLog(userId)
				err := impl.pipelineConfigRepository.Save(newDefaultStrategy, tx)
				if err != nil {
					impl.logger.Errorw("error in saving pipeline strategy", "strategy", newDefaultStrategy, "err", err)
					return err
				}
				// creating history entry for strategy
				_, err = impl.pipelineStrategyHistoryService.CreatePipelineStrategyHistory(newCtx, newDefaultStrategy, pipelineModel.TriggerType, tx)
				if err != nil {
					impl.logger.Errorw("error in creating strategy history entry", "err", err)
					return err
				}
			} else {
				strategies[existingDefaultStrategyIndex].Default = true
				strategies[existingDefaultStrategyIndex].UpdateAuditLog(userId)
				err := impl.pipelineConfigRepository.Update(strategies[existingDefaultStrategyIndex], tx)
				if err != nil {
					impl.logger.Errorw("error in updating pipeline strategy", "strategy", strategies[existingDefaultStrategyIndex], "err", err)
					return err
				}
				// creating history entry for strategy
				_, err = impl.pipelineStrategyHistoryService.CreatePipelineStrategyHistory(newCtx, strategies[existingDefaultStrategyIndex], pipelineModel.TriggerType, tx)
				if err != nil {
					impl.logger.Errorw("error in creating strategy history entry", "err", err)
					return err
				}
				strategy.Default = false
				strategy.UpdateAuditLog(userId)
				err = impl.pipelineConfigRepository.Update(strategy, tx)
				if err != nil {
					impl.logger.Errorw("error in updating pipeline strategy", "strategy", strategy, "err", err)
					return err
				}
				// creating history entry for strategy
				_, err = impl.pipelineStrategyHistoryService.CreatePipelineStrategyHistory(newCtx, strategy, pipelineModel.TriggerType, tx)
				if err != nil {
					impl.logger.Errorw("error in creating strategy history entry", "err", err)
					return err
				}
			}
		}
	}
	return nil
}

func (impl *ChartServiceImpl) changePipelineStrategies(ctx context.Context, tx *pg.Tx, userId int32, pipelineId int, pipelineStrategies []chartRefBean.PipelineStrategy) error {
	newCtx, span := otel.Tracer("orchestrator").Start(ctx, "ChartServiceImpl.changePipelineStrategies")
	defer span.End()
	strategies, err := impl.pipelineConfigRepository.GetAllStrategyByPipelineId(pipelineId)
	if err != nil {
		impl.logger.Errorw("error in fetching pipeline strategies", "pipelineId", pipelineId, "err", err)
		return err
	}
	err = impl.createDefaultStrategyIfRequired(newCtx, tx, strategies, pipelineStrategies, userId)
	if err != nil {
		impl.logger.Errorw("error in creating default strategy", "strategies", strategies, "pipelineStrategies", pipelineStrategies, "err", err)
		return err
	}
	for _, strategy := range strategies {
		if !slices.ContainsFunc(pipelineStrategies, func(pipelineStrategy chartRefBean.PipelineStrategy) bool {
			return pipelineStrategy.DeploymentTemplate == strategy.Strategy
		}) {
			err = impl.pipelineConfigRepository.MarkAsDeleted(strategy, userId, tx)
			if err != nil {
				impl.logger.Errorw("error in deleting pipeline strategy", "strategy", strategy, "err", err)
				return err
			}
		}
	}
	return nil
}

func (impl *ChartServiceImpl) ChangeApplicationTemplateChart(ctx *ctxUtil.RequestCtx, userId int32, request *bean3.TemplateRefChangeRequest) error {
	newCtx, span := otel.Tracer("orchestrator").Start(ctx, "ChartServiceImpl.ChangeApplicationTemplateChart")
	defer span.End()
	template := request.AppTemplate
	if template.ChartRefId != request.TargetChartRefId {
		templateRequested, err := impl.chartReadService.GetByAppIdAndChartRefId(request.AppId, request.TargetChartRefId)
		if err != nil && !errors.Is(err, pg.ErrNoRows) {
			impl.logger.Errorw("error in fetching chart", "appId", request.AppId, "chartRefId", request.TargetChartRefId, "err", err)
			return err
		} else if errors.Is(err, pg.ErrNoRows) {
			template.ChartRefId = request.TargetChartRefId
			template.Id = 0
		} else {
			template.ChartRefId = templateRequested.ChartRefId
			template.Id = templateRequested.Id
			template.ChartRepositoryId = templateRequested.ChartRepositoryId
			template.RefChartTemplate = templateRequested.RefChartTemplate
			template.RefChartTemplateVersion = templateRequested.RefChartTemplateVersion
		}
	}
	template.Latest = true
	template.UserId = userId
	template.ValuesOverride = template.DefaultAppOverride
	if template.Id == 0 {
		_, err := impl.Create(*template, newCtx)
		if err != nil {
			impl.logger.Errorw("error in creating chart", "appId", request.AppId, "err", err)
			return err
		}
	} else {
		_, err := impl.UpdateAppOverride(newCtx, template, ctx.GetToken())
		if err != nil {
			impl.logger.Errorw("error in updating app override", "appId", request.AppId, "err", err)
			return err
		}
	}
	return nil
}
