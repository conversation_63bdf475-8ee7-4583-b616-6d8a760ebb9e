package read

import (
	apiGitOpsBean "github.com/devtron-labs/devtron/api/bean/gitOps"
	"github.com/devtron-labs/devtron/pkg/chart/adaptor"
	"github.com/devtron-labs/devtron/pkg/chart/bean"
	chartRepoRepository "github.com/devtron-labs/devtron/pkg/chartRepo/repository"
	"github.com/devtron-labs/devtron/pkg/deployment/common"
	"github.com/devtron-labs/devtron/pkg/deployment/gitOps/config"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deployedAppMetrics"
	bean3 "github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/chartRef/bean"
	chartRefRead "github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/chartRef/read"
	"go.uber.org/zap"
)

type ChartReadService interface {
	GetByAppIdAndChartRefId(appId int, chartRefId int) (chartTemplate *bean.TemplateRequest, err error)
	IsGitOpsRepoConfiguredForDevtronApps(appIds []int) (map[int]bool, error)
	FindLatestChartForAppByAppId(appId int) (chartTemplate *bean.TemplateRequest, err error)
	FindAppIdVsLatestChartNameForAppIds(appIds []int) (map[int]string, error)
	GetChartRefConfiguredForApp(appId int) (*bean3.ChartRefDto, error)
}

type ChartReadServiceImpl struct {
	logger                    *zap.SugaredLogger
	chartRepository           chartRepoRepository.ChartRepository
	deploymentConfigService   common.DeploymentConfigService
	deployedAppMetricsService deployedAppMetrics.DeployedAppMetricsService
	gitOpsConfigReadService   config.GitOpsConfigReadService
	ChartRefReadService       chartRefRead.ChartRefReadService
}

func NewChartReadServiceImpl(logger *zap.SugaredLogger,
	chartRepository chartRepoRepository.ChartRepository,
	deploymentConfigService common.DeploymentConfigService,
	deployedAppMetricsService deployedAppMetrics.DeployedAppMetricsService,
	gitOpsConfigReadService config.GitOpsConfigReadService,
	ChartRefReadService chartRefRead.ChartRefReadService) *ChartReadServiceImpl {
	return &ChartReadServiceImpl{
		logger:                    logger,
		chartRepository:           chartRepository,
		deploymentConfigService:   deploymentConfigService,
		deployedAppMetricsService: deployedAppMetricsService,
		gitOpsConfigReadService:   gitOpsConfigReadService,
		ChartRefReadService:       ChartRefReadService,
	}

}

func (impl *ChartReadServiceImpl) GetByAppIdAndChartRefId(appId int, chartRefId int) (chartTemplate *bean.TemplateRequest, err error) {
	chart, err := impl.chartRepository.FindChartByAppIdAndRefId(appId, chartRefId)
	if err != nil {
		impl.logger.Errorw("error in fetching chart ", "appId", appId, "err", err)
		return nil, err
	}
	isAppMetricsEnabled, err := impl.deployedAppMetricsService.GetMetricsFlagByAppId(appId)
	if err != nil {
		impl.logger.Errorw("error in fetching app-metrics", "appId", appId, "err", err)
		return nil, err
	}
	deploymentConfig, err := impl.deploymentConfigService.GetConfigForDevtronApps(nil, appId, 0)
	if err != nil {
		impl.logger.Errorw("error in fetching deployment config by appId", "appId", appId, "err", err)
		return nil, err
	}
	chartTemplate, err = adaptor.ChartAdaptor(chart, isAppMetricsEnabled, deploymentConfig)
	return chartTemplate, err
}

func (impl *ChartReadServiceImpl) IsGitOpsRepoConfiguredForDevtronApps(appIds []int) (map[int]bool, error) {
	gitOpsConfigStatus, err := impl.gitOpsConfigReadService.IsGitOpsConfigured()
	if err != nil {
		impl.logger.Errorw("error in fetching latest chart for app by appId")
		return nil, err
	}
	appIdRepoConfiguredMap := make(map[int]bool, len(appIds))
	for _, appId := range appIds {
		if !gitOpsConfigStatus.IsGitOpsConfiguredAndArgoCdInstalled() {
			appIdRepoConfiguredMap[appId] = false
		} else if !gitOpsConfigStatus.AllowCustomRepository {
			appIdRepoConfiguredMap[appId] = true
		} else {
			latestChartConfiguredInApp, err := impl.FindLatestChartForAppByAppId(appId)
			if err != nil {
				impl.logger.Errorw("error in fetching latest chart for app by appId")
				return nil, err
			}
			appIdRepoConfiguredMap[appId] = !apiGitOpsBean.IsGitOpsRepoNotConfigured(latestChartConfiguredInApp.GitRepoUrl)
		}
	}
	return appIdRepoConfiguredMap, nil
}

func (impl *ChartReadServiceImpl) FindLatestChartForAppByAppId(appId int) (chartTemplate *bean.TemplateRequest, err error) {
	chart, err := impl.chartRepository.FindLatestChartForAppByAppId(nil, appId)
	if err != nil {
		impl.logger.Errorw("error in fetching chart ", "appId", appId, "err", err)
		return nil, err
	}

	deploymentConfig, err := impl.deploymentConfigService.GetConfigForDevtronApps(nil, appId, 0)
	if err != nil {
		impl.logger.Errorw("error in fetching deployment config by appId", "appId", appId, "err", err)
		return nil, err
	}

	isAppMetricsEnabled, err := impl.deployedAppMetricsService.GetMetricsFlagByAppId(appId)
	if err != nil {
		impl.logger.Errorw("error in fetching app-metrics", "appId", appId, "err", err)
		return nil, err
	}
	chartTemplate, err = adaptor.ChartAdaptor(chart, isAppMetricsEnabled, deploymentConfig)
	return chartTemplate, err
}

func (impl *ChartReadServiceImpl) FindAppIdVsLatestChartNameForAppIds(appIds []int) (map[int]string, error) {
	appIdVsLatestChartNameMap := make(map[int]string, len(appIds))
	latestCharts, err := impl.chartRepository.FindLatestChartByAppIds(appIds)
	if err != nil {
		impl.logger.Errorw("error in finding latest chart by appId", "appIds", appIds, "err", err)
		return nil, err
	}
	chartRefIds := make([]int, 0, len(latestCharts))
	mapOfCharRefIdVsChart := make(map[int]*chartRepoRepository.Chart, len(latestCharts))
	for _, c := range latestCharts {
		chartRefIds = append(chartRefIds, c.ChartRefId)
		mapOfCharRefIdVsChart[c.ChartRefId] = c
	}
	chartRefs, err := impl.ChartRefReadService.FindByIds(chartRefIds)
	if err != nil {
		impl.logger.Errorw("error in finding chartRefs", "chartRefIds", chartRefIds, "appIds", appIds, "err", err)
		return nil, err
	}
	for _, chartRef := range chartRefs {
		appIdVsLatestChartNameMap[mapOfCharRefIdVsChart[chartRef.Id].AppId] = chartRef.Name
	}
	return appIdVsLatestChartNameMap, nil
}

func (impl *ChartReadServiceImpl) GetChartRefConfiguredForApp(appId int) (*bean3.ChartRefDto, error) {
	latestChart, err := impl.FindLatestChartForAppByAppId(appId)
	if err != nil {
		impl.logger.Errorw("error in finding latest chart by appId", "appId", appId, "err", err)
		return nil, nil
	}
	chartRef, err := impl.ChartRefReadService.FindById(latestChart.ChartRefId)
	if err != nil {
		impl.logger.Errorw("error in finding latest chart by appId", "appId", appId, "err", err)
		return nil, nil
	}
	return chartRef, nil
}
