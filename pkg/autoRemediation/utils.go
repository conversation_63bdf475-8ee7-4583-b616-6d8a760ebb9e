package autoRemediation

import (
	"encoding/json"
	"github.com/devtron-labs/devtron/pkg/autoRemediation/repository"
	"github.com/devtron-labs/devtron/pkg/autoRemediation/types"
	"go.uber.org/zap"
	"sort"
)

func GetTriggerDataFromJson(data string) (types.TriggerData, error) {
	var triggerResp types.TriggerData
	if err := json.Unmarshal([]byte(data), &triggerResp); err != nil {

		return types.TriggerData{}, err
	}
	return triggerResp, nil
}

func GetK8sResourcesFromGvks(gvks string) ([]*types.K8sResource, error) {
	var k8sResources []*types.K8sResource
	if err := json.Unmarshal([]byte(gvks), &k8sResources); err != nil {
		return nil, err
	}
	return k8sResources, nil
}

func ConvertDbTriggersToDtos(logger *zap.SugaredLogger, triggers []*repository.AutoRemediationTrigger) (map[int]types.Trigger, error) {
	watcherIdToTriggerData := make(map[int]types.Trigger)
	for _, trigger := range triggers {
		triggerResp, err := GetTriggerDataFromJson(trigger.Data)
		if err != nil {
			logger.Errorw("error in unmarshalling trigger data", "error", err)
			return map[int]types.Trigger{}, err
		}
		watcherIdToTriggerData[trigger.WatcherId] = types.Trigger{
			Id:             trigger.Id,
			IdentifierType: types.TriggerType(trigger.Type),
			Data: types.TriggerData{
				WebhookData:            triggerResp.WebhookData,
				RuntimeParameters:      triggerResp.RuntimeParameters,
				RuntimeParams:          triggerResp.GetRuntimeParams(),
				JobId:                  triggerResp.JobId,
				JobName:                triggerResp.JobName,
				PipelineId:             triggerResp.PipelineId,
				PipelineName:           triggerResp.PipelineName,
				ExecutionEnvironment:   triggerResp.ExecutionEnvironment,
				ExecutionEnvironmentId: triggerResp.ExecutionEnvironmentId,
				WorkflowId:             triggerResp.WorkflowId,
			},
		}
	}
	return watcherIdToTriggerData, nil
}

func sortByWatcherNameOrder(combinedData []WatcherTriggerData, watchersList []*repository.K8sEventWatcher) []WatcherTriggerData {
	sort.Slice(combinedData, func(i, j int) bool {
		indexI := indexOfWatcher(combinedData[i].Watcher.Id, watchersList)
		indexJ := indexOfWatcher(combinedData[j].Watcher.Id, watchersList)
		return indexI < indexJ
	})
	return combinedData
}

func sortByTime(combinedData []WatcherTriggerData, sortOrder string) {
	less := func(i, j int) bool {
		if sortOrder == "asc" {
			return combinedData[i].time.Before(combinedData[j].time)
		}
		return combinedData[i].time.After(combinedData[j].time)
	}
	sort.Slice(combinedData, less)
}
