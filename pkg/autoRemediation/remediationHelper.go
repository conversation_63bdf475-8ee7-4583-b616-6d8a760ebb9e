package autoRemediation

import (
	"encoding/json"
	"fmt"
	appRepository "github.com/devtron-labs/devtron/internal/sql/repository/app"
	"github.com/devtron-labs/devtron/internal/sql/repository/appWorkflow"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/pkg/autoRemediation/repository"
	"github.com/devtron-labs/devtron/pkg/autoRemediation/types"
	repository3 "github.com/devtron-labs/devtron/pkg/cluster/environment/repository"
	"github.com/devtron-labs/devtron/pkg/sql"
	"go.uber.org/zap"
	"time"
)

type RemediationHelper interface {
	GetAutoRemediationTriggers(triggers []*types.Trigger, watcherId int, userId int32) ([]*repository.AutoRemediationTrigger, error)
	GetStatusByExecutionIds(triggerExecutionIds []int) (map[int]string, error)
	GetIdentifierVsExecutionTimeMapForTriggers(watcherIdVsTrigger map[int]types.Trigger) (map[int]time.Time, error)
}

type RemediationHelperFactoryIF interface {
	GetRemediationHelper(triggerType types.TriggerType) RemediationHelper
}

type RemediationHelperFactory struct {
	webhookRemediationHelper    *WebhookRemediationHelper
	devtronJobRemediationHelper *DevtronJobRemediationHelper
}

func (fact *RemediationHelperFactory) GetRemediationHelper(triggerType types.TriggerType) RemediationHelper {
	if triggerType == types.WEBHOOK {
		return fact.webhookRemediationHelper
	}
	return fact.devtronJobRemediationHelper
}

// NewRemediationHelperFactory is full mode factory constructor
func NewRemediationHelperFactory(webhookRemediationHelper *WebhookRemediationHelper, devtronJobRemediationHelper *DevtronJobRemediationHelper) *RemediationHelperFactory {
	return &RemediationHelperFactory{
		webhookRemediationHelper:    webhookRemediationHelper,
		devtronJobRemediationHelper: devtronJobRemediationHelper,
	}
}

// NewRemediationHelperFactoryEA is EA only factory constructor
func NewRemediationHelperFactoryEA(webhookRemediationHelper *WebhookRemediationHelper) *RemediationHelperFactory {
	return &RemediationHelperFactory{
		webhookRemediationHelper: webhookRemediationHelper,
	}
}

func NewWebhookRemediationHelper(logger *zap.SugaredLogger, interceptedEventsRepository repository.InterceptedEventsRepository) *WebhookRemediationHelper {
	return &WebhookRemediationHelper{
		logger:                      logger,
		interceptedEventsRepository: interceptedEventsRepository,
	}
}

type WebhookRemediationHelper struct {
	logger                      *zap.SugaredLogger
	interceptedEventsRepository repository.InterceptedEventsRepository
}

func NewDevtronJobRemediationHelper(appRepository appRepository.AppRepository,
	ciPipelineRepository pipelineConfig.CiPipelineRepository,
	environmentRepository repository3.EnvironmentRepository,
	appWorkflowMappingRepository appWorkflow.AppWorkflowRepository,
	ciWorkflowRepository pipelineConfig.CiWorkflowRepository, logger *zap.SugaredLogger) *DevtronJobRemediationHelper {
	return &DevtronJobRemediationHelper{
		appRepository:                appRepository,
		ciPipelineRepository:         ciPipelineRepository,
		environmentRepository:        environmentRepository,
		appWorkflowMappingRepository: appWorkflowMappingRepository,
		ciWorkflowRepository:         ciWorkflowRepository,
		logger:                       logger,
	}
}

type DevtronJobRemediationHelper struct {
	appRepository                appRepository.AppRepository
	ciPipelineRepository         pipelineConfig.CiPipelineRepository
	environmentRepository        repository3.EnvironmentRepository
	appWorkflowMappingRepository appWorkflow.AppWorkflowRepository
	ciWorkflowRepository         pipelineConfig.CiWorkflowRepository
	logger                       *zap.SugaredLogger
}

func (webhookHelper *WebhookRemediationHelper) GetIdentifierVsExecutionTimeMapForTriggers(watcherIdVsTrigger map[int]types.Trigger) (map[int]time.Time, error) {
	triggerIds := make([]int, 0, len(watcherIdVsTrigger))
	for _, trigger := range watcherIdVsTrigger {
		if trigger.IsWebhookType() {
			triggerIds = append(triggerIds, trigger.Id)
		}
	}

	triggerIdVsLatestInterceptedEventExecutionTime := make(map[int]time.Time)
	triggerWiseLastExecutionTimes, err := webhookHelper.interceptedEventsRepository.GetLatestInterceptedEventsByTriggerIds(triggerIds)
	if err != nil {
		webhookHelper.logger.Errorw("error in getting latest intercepted events by trigger ids", "triggerIds", triggerIds, "err", err)
		return nil, err
	}

	for _, triggerWiseLastExecutionTime := range triggerWiseLastExecutionTimes {
		triggerIdVsLatestInterceptedEventExecutionTime[triggerWiseLastExecutionTime.TriggerId] = triggerWiseLastExecutionTime.UpdatedOn
	}
	return triggerIdVsLatestInterceptedEventExecutionTime, nil
}

func (webhookHelper *WebhookRemediationHelper) GetAutoRemediationTriggers(triggers []*types.Trigger, watcherId int, userId int32) ([]*repository.AutoRemediationTrigger, error) {
	var triggersResult []*repository.AutoRemediationTrigger
	for _, res := range triggers {
		jsonData, err := json.Marshal(res.Data)
		if err != nil {
			webhookHelper.logger.Errorw("error in marshalling trigger data ", "error", err)
			return triggersResult, err
		}
		triggerRes := &repository.AutoRemediationTrigger{
			WatcherId: watcherId,
			Type:      repository.WEBHOOK,
			Data:      string(jsonData),
			Active:    true,
			AuditLog:  sql.NewDefaultAuditLog(userId),
		}
		triggersResult = append(triggersResult, triggerRes)
	}
	return triggersResult, nil
}

// GetStatusByExecutionIds is not implemented for webhooks
func (webhookHelper *WebhookRemediationHelper) GetStatusByExecutionIds(triggerExecutionIds []int) (map[int]string, error) {
	return map[int]string{}, nil
}

func (devtronJobHelper *DevtronJobRemediationHelper) GetIdentifierVsExecutionTimeMapForTriggers(watcherIdVsTrigger map[int]types.Trigger) (map[int]time.Time, error) {
	jobPipelineIds := make([]int, 0)
	pipelineIdToTriggerTime := make(map[int]time.Time)
	for _, trigger := range watcherIdVsTrigger {
		if trigger.IsDevtronJobType() && trigger.Data.PipelineId > 0 {
			jobPipelineIds = append(jobPipelineIds, trigger.Data.PipelineId)
		}
	}
	if len(jobPipelineIds) != 0 {
		ciWorkflows, err := devtronJobHelper.ciWorkflowRepository.FindLastOneTriggeredWorkflowByCiIds(jobPipelineIds)
		if err != nil {
			devtronJobHelper.logger.Errorw("error in fetching last triggered workflow by ci ids", jobPipelineIds, "error", err)
			return nil, err
		}

		for _, workflow := range ciWorkflows {
			pipelineIdToTriggerTime[workflow.CiPipelineId] = workflow.StartedOn
		}

		return pipelineIdToTriggerTime, nil
	}
	return pipelineIdToTriggerTime, nil
}

func (devtronJobHelper *DevtronJobRemediationHelper) GetAutoRemediationTriggers(triggers []*types.Trigger, watcherId int, userId int32) ([]*repository.AutoRemediationTrigger, error) {
	jobInfo, err := devtronJobHelper.getJobEnvPipelineDetailsForWatcher(triggers)
	if err != nil {
		devtronJobHelper.logger.Errorw("error in retrieving details of trigger type job", "error", err)
		return nil, err
	}
	var triggersResult []*repository.AutoRemediationTrigger
	for _, res := range triggers {

		triggerData := types.TriggerData{
			RuntimeParameters: res.Data.RuntimeParameters,
			RuntimeParams:     res.Data.GetRuntimeParams(),
		}
		if jobInfo.displayNameToId[res.Data.JobName] != 0 && res.Data.PipelineName == "" {
			triggerData.JobId = jobInfo.displayNameToId[res.Data.JobName]
			triggerData.JobName = res.Data.JobName
		}
		jobId := jobInfo.displayNameToId[res.Data.JobName]
		key := fmt.Sprintf("%d_%s", jobId, res.Data.PipelineName)
		if jobInfo.displayNameToId[res.Data.JobName] != 0 && jobInfo.pipelineNameToId[key] != 0 && jobInfo.pipelineIdtoAppworkflow[jobInfo.pipelineNameToId[key]] != 0 {
			triggerData.JobId = jobInfo.displayNameToId[res.Data.JobName]
			triggerData.JobName = res.Data.JobName
			triggerData.PipelineId = jobInfo.pipelineNameToId[key]
			triggerData.PipelineName = res.Data.PipelineName
			triggerData.WorkflowId = jobInfo.pipelineIdtoAppworkflow[jobInfo.pipelineNameToId[key]]
		}
		triggerData.ExecutionEnvironment = res.Data.ExecutionEnvironment
		if jobInfo.envNameToId[res.Data.ExecutionEnvironment] != 0 {
			triggerData.ExecutionEnvironmentId = jobInfo.envNameToId[res.Data.ExecutionEnvironment]
		}
		jsonData, err := json.Marshal(triggerData)
		if err != nil {
			devtronJobHelper.logger.Errorw("error in marshalling trigger data ", "error", err)
			return triggersResult, err
		}
		triggerRes := &repository.AutoRemediationTrigger{
			WatcherId: watcherId,
			Type:      repository.DEVTRON_JOB,
			Data:      string(jsonData),
			Active:    true,
			AuditLog:  sql.NewDefaultAuditLog(userId),
		}
		triggersResult = append(triggersResult, triggerRes)
	}
	return triggersResult, nil
}

func (devtronJobHelper *DevtronJobRemediationHelper) GetStatusByExecutionIds(triggerExecutionIds []int) (map[int]string, error) {
	ciWorkflows, err := devtronJobHelper.ciWorkflowRepository.FindCiWorkflowGitTriggersByIds(triggerExecutionIds) // function should have been FindCiWorkflowByIds instead of FindCiWorkflowGitTriggersByIds
	if err != nil {
		devtronJobHelper.logger.Errorw("error in getting ci workflows", "triggerExecutionIds", triggerExecutionIds, "err", err)
		return nil, err
	}
	triggerExecutionIdToStatus := make(map[int]string)
	for _, workflow := range ciWorkflows {
		triggerExecutionIdToStatus[workflow.Id] = workflow.Status
	}
	return triggerExecutionIdToStatus, nil
}

type jobDetails struct {
	displayNameToId         map[string]int
	pipelineNameToId        map[string]int
	envNameToId             map[string]int
	pipelineIdtoAppworkflow map[int]int
}

func (devtronJobHelper *DevtronJobRemediationHelper) getJobEnvPipelineDetailsForWatcher(triggers []*types.Trigger) (*jobDetails, error) {
	var jobsDetails *jobDetails
	var jobNames, envNames, pipelineNames []string

	for _, trig := range triggers {
		jobNames = append(jobNames, trig.Data.JobName)
		envNames = append(envNames, trig.Data.ExecutionEnvironment)
		pipelineNames = append(pipelineNames, trig.Data.PipelineName)
	}
	var apps []*appRepository.AppDto
	var err error
	if len(jobNames) != 0 {
		apps, err = devtronJobHelper.appRepository.FetchAppByDisplayNamesForJobs(jobNames)
		if err != nil {
			devtronJobHelper.logger.Errorw("error in fetching apps", "jobNames", jobNames, "error", err)
			return jobsDetails, err
		}
	}
	var jobIds []int
	for _, app := range apps {
		jobIds = append(jobIds, app.Id)
	}
	var pipelines []*pipelineConfig.CiPipeline
	if len(jobIds) != 0 {
		pipelines, err = devtronJobHelper.ciPipelineRepository.FindByNames(pipelineNames, jobIds)
		if err != nil {
			devtronJobHelper.logger.Errorw("error in fetching pipelines", "pipelineNames", pipelineNames, "error", err)
			return jobsDetails, err
		}
	}

	var pipelinesId []int
	for _, pipeline := range pipelines {
		pipelinesId = append(pipelinesId, pipeline.Id)
	}
	var envs []*repository3.Environment
	if len(envNames) != 0 {
		envs, err = devtronJobHelper.environmentRepository.FindByNames(envNames)
		if err != nil {
			devtronJobHelper.logger.Errorw("error in fetching environment", "envNames", envNames, "error", err)
			return jobsDetails, err
		}
	}
	displayNameToId := make(map[string]int)
	for _, app := range apps {
		displayNameToId[app.DisplayName] = app.Id
	}

	// since pipeline name can be same across jobs, make unique-pipe by job-id_pipe-name
	pipelineNameToId := make(map[string]int)
	for _, pipeline := range pipelines {
		key := fmt.Sprintf("%d_%s", pipeline.AppId, pipeline.Name)
		pipelineNameToId[key] = pipeline.Id
	}
	var workflows []*appWorkflow.AppWorkflowMapping
	if len(pipelinesId) != 0 {
		workflows, err = devtronJobHelper.appWorkflowMappingRepository.FindWFCIMappingByCIPipelineIds(pipelinesId)
		if err != nil {
			devtronJobHelper.logger.Errorw("error in retrieving workflows for pipelineIds", pipelinesId, "error", err)
			return jobsDetails, err
		}
	}
	pipelineIdtoAppworkflow := make(map[int]int)
	for _, workflow := range workflows {
		pipelineIdtoAppworkflow[workflow.ComponentId] = workflow.AppWorkflowId
	}
	envNameToId := make(map[string]int)
	for _, env := range envs {
		envNameToId[env.Name] = env.Id
	}
	return &jobDetails{
		pipelineNameToId:        pipelineNameToId,
		displayNameToId:         displayNameToId,
		envNameToId:             envNameToId,
		pipelineIdtoAppworkflow: pipelineIdtoAppworkflow,
	}, nil

}
