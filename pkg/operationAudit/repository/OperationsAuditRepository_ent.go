package repository

import (
	"errors"
	"github.com/devtron-labs/devtron/pkg/operationAudit/bean"
	"github.com/devtron-labs/devtron/pkg/sql"
	"github.com/go-pg/pg"
	"go.uber.org/zap"
)

type OperationAuditRepository interface {
	SaveAudit(tx *pg.Tx, audit *OperationAudit) error
	BulkSaveAudits(tx *pg.Tx, audits []*OperationAudit) error
	GetAllAuditsForEntityAndEntityIds(entityType bean.EntityType, entityIds []int, operationType bean.OperationType, entityValueSchemaType bean.EntityValueSchemaType) ([]*OperationAudit, error)
}
type OperationAuditRepositoryImpl struct {
	dbConnection *pg.DB
	logger       *zap.SugaredLogger
}

func NewOperationAuditRepositoryImpl(dbConnection *pg.DB,
	logger *zap.SugaredLogger) *OperationAuditRepositoryImpl {
	return &OperationAuditRepositoryImpl{
		dbConnection: dbConnection,
		logger:       logger,
	}
}

type OperationAudit struct {
	TableName             struct{}                   `sql:"operation_audit" pg:",discard_unknown_columns"`
	Id                    int                        `sql:"id,pk"`
	EntityId              int                        `sql:"entity_id,notnull"`                // User Id or Role Group Id or any entity id
	EntityType            bean.EntityType            `sql:"entity_type,notnull"`              // user or role-group or etc
	OperationType         bean.OperationType         `sql:"operation_type,notnull"`           // create,update,delete
	EntityValueJson       string                     `sql:"entity_value_json,notnull"`        // create - permissions to be created with user, update - we will keep final updated permissions and delete will have operation as delete with existing permissions captured
	EntityValueSchemaType bean.EntityValueSchemaType `sql:"entity_value_schema_type,notnull"` // refer EntityValueSchemaType
	sql.AuditLog
}

func (repo *OperationAuditRepositoryImpl) SaveAudit(tx *pg.Tx, audit *OperationAudit) error {
	if tx == nil {
		err := repo.dbConnection.Insert(audit)
		if err != nil {
			repo.logger.Errorw("error in saving audit", "audit", audit, "err", err)
			return err
		}
	} else {
		err := tx.Insert(audit)
		if err != nil {
			repo.logger.Errorw("error in saving audit", "audit", audit, "err", err)
			return err
		}
	}
	return nil
}

func (repo *OperationAuditRepositoryImpl) BulkSaveAudits(tx *pg.Tx, audits []*OperationAudit) error {
	if len(audits) == 0 {
		return errors.New("no audits found")
	}
	if tx == nil {
		err := repo.dbConnection.Insert(&audits)
		if err != nil {
			return err
		}
	} else {
		err := tx.Insert(&audits)
		if err != nil {
			return err
		}
	}
	return nil
}
func (repo *OperationAuditRepositoryImpl) GetAllAuditsForEntityAndEntityIds(entityType bean.EntityType, entityIds []int, operationType bean.OperationType, entityValueSchemaType bean.EntityValueSchemaType) ([]*OperationAudit, error) {
	if len(entityIds) == 0 {
		return nil, errors.New("no entity ids found")
	}
	var audits []*OperationAudit
	err := repo.dbConnection.Model(&audits).
		Where("entity_type = ?", entityType).
		Where("entity_id in (?)", pg.In(entityIds)).
		Where("operation_type = ?", operationType).
		Where("entity_value_schema_type = ?", entityValueSchemaType).
		Select()
	return audits, err
}
