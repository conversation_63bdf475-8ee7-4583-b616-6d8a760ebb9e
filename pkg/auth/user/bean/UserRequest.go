/*
 * Copyright (c) 2020-2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package bean

import (
	"encoding/json"
	"github.com/devtron-labs/devtron/pkg/auth/userGroup/beans"
	"github.com/devtron-labs/devtron/pkg/sql"
	repository2 "github.com/devtron-labs/devtron/pkg/timeoutWindow/repository"
	"time"
)

type UserMinInfo struct {
	Id      int32  `json:"id"`
	EmailId string `json:"emailId"`
}

type UserRole struct {
	Id      int32  `json:"id" validate:"number"`
	EmailId string `json:"email_id" validate:"email"`
	Role    string `json:"role"`
}

type UserInfo struct {
	Id                      int32           `json:"id" validate:"number,not-system-admin-userid"`
	EmailId                 string          `json:"email_id" validate:"required,not-system-admin-user-email"` // TODO : have to migrate json key to emailId and also handle backward compatibility
	Roles                   []string        `json:"roles,omitempty"`
	AccessToken             string          `json:"access_token,omitempty"`
	RoleFilters             []RoleFilter    `json:"roleFilters"`
	AccessRoleFilters       []RoleFilter    `json:"accessRoleFilters,omitempty"`
	Groups                  []string        `json:"groups"` // this will be deprecated in future do not use
	SuperAdmin              bool            `json:"superAdmin,notnull"`
	CanManageAllAccess      bool            `json:"canManageAllAccess,omitempty"`
	UserRoleGroup           []UserRoleGroup `json:"userRoleGroups"` // role group with metadata , status and timeoutWindowExpression
	LastLoginTime           time.Time       `json:"lastLoginTime"`
	TimeoutWindowExpression time.Time       `json:"timeoutWindowExpression"`
	UserStatus              Status          `json:"userStatus"`
	// UserGroups is different from UserRoleGroup, UserGroups are the groups/teams user is part of
	UserGroups   []*beans.UserGroupDTO `json:"userGroups"`
	CreatedOn    time.Time             `json:"createdOn"`
	UpdatedOn    time.Time             `json:"updatedOn"`
	IsDeleted    bool                  `json:"isDeleted"`
	UserType     string                `json:"-"`
	LastUsedAt   time.Time             `json:"-"`
	LastUsedByIp string                `json:"-"`
	Exist        bool                  `json:"-"`
	UserId       int32                 `json:"-"` // created or modified user id
	EntityAudit  sql.AuditLog          `json:"-"`
}

func (userInfo *UserInfo) GetUserGroupNames() []string {
	userGrpNames := []string{}
	for _, grp := range userInfo.UserGroups {
		userGrpNames = append(userGrpNames, grp.Identifier)
	}
	return userGrpNames
}

func (ui *UserInfo) SetEntityAudit(auditLog sql.AuditLog) *UserInfo {
	ui.EntityAudit = auditLog
	return ui
}

type RoleGroup struct {
	Id                         int32        `json:"id" validate:"number"`
	Name                       string       `json:"name,omitempty"`
	Description                string       `json:"description,omitempty"`
	RoleFilters                []RoleFilter `json:"roleFilters"`
	AccessRoleFilters          []RoleFilter `json:"accessRoleFilters,omitempty"`
	SuperAdmin                 bool         `json:"superAdmin"`
	CanManageAllAccess         bool         `json:"canManageAllAccess"`
	HasAccessManagerPermission bool         `json:"hasAccessManagerPermission"`
	UserId                     int32        `json:"-"` // created or modified user id
	CasbinName                 string       `json:"-"` // for Internal Use
	EntityAudit                sql.AuditLog `json:"-"`
}

type RoleFilter struct {
	Entity      string `json:"entity"`
	Team        string `json:"team"`
	EntityName  string `json:"entityName"`
	Environment string `json:"environment"`
	Action      string `json:"action"`
	SubAction   string `json:"subAction"`
	Approver    bool   `json:"approver"`
	AccessType  string `json:"accessType"`

	Cluster   string `json:"cluster"`
	Namespace string `json:"namespace"`
	Group     string `json:"group"`
	Kind      string `json:"kind"`
	Resource  string `json:"resource"`
	Workflow  string `json:"workflow"`

	ReleaseTrack string `json:"releaseTrack"`
	Release      string `json:"release"`

	TimeoutWindowExpression time.Time `json:"timeoutWindowExpression"`
	Status                  Status    `json:"status"`
}

func (rf RoleFilter) GetTeam() string                       { return rf.Team }
func (rf RoleFilter) GetEntity() string                     { return rf.Entity }
func (rf RoleFilter) GetAction() string                     { return rf.Action }
func (rf RoleFilter) GetSubAction() string                  { return rf.SubAction }
func (rf RoleFilter) GetAccessType() string                 { return rf.AccessType }
func (rf RoleFilter) GetEnvironment() string                { return rf.Environment }
func (rf RoleFilter) GetCluster() string                    { return rf.Cluster }
func (rf RoleFilter) GetGroup() string                      { return rf.Group }
func (rf RoleFilter) GetKind() string                       { return rf.Kind }
func (rf RoleFilter) GetEntityName() string                 { return rf.EntityName }
func (rf RoleFilter) GetResource() string                   { return rf.Resource }
func (rf RoleFilter) GetWorkflow() string                   { return rf.Workflow }
func (rf RoleFilter) GetNamespace() string                  { return rf.Namespace }
func (rf RoleFilter) GetReleaseTrack() string               { return rf.ReleaseTrack }
func (rf RoleFilter) GetRelease() string                    { return rf.Release }
func (rf RoleFilter) GetTimeoutWindowExpression() time.Time { return rf.TimeoutWindowExpression }
func (rf RoleFilter) GetStatus() Status                     { return rf.Status }
func (rf RoleFilter) GetApprover() bool                     { return rf.Approver }
func (rf RoleFilter) GetTimeoutWindowConfiguration() *repository2.TimeoutWindowConfiguration {
	// unimplemented
	return nil
}

type Role struct {
	Id   int    `json:"id" validate:"number"`
	Role string `json:"role" validate:"required"`
}

type RoleData struct {
	Id          int    `json:"id" validate:"number"`
	Role        string `json:"role" validate:"required"`
	Entity      string `json:"entity"`
	Team        string `json:"team"`
	EntityName  string `json:"entityName"`
	Environment string `json:"environment"`
	Action      string `json:"action"`
	Approver    bool   `json:"approver"`
	AccessType  string `json:"accessType"`

	Cluster   string `json:"cluster"`
	Namespace string `json:"namespace"`
	Group     string `json:"group"`
	Kind      string `json:"kind"`
	Resource  string `json:"resource"`
}

type SSOLoginDto struct {
	Id                   int32           `json:"id"`
	Name                 string          `json:"name,omitempty"`
	Label                string          `json:"label,omitempty"`
	Url                  string          `json:"url,omitempty"`
	Config               json.RawMessage `json:"config,omitempty"`
	Active               bool            `json:"active"`
	GlobalAuthConfigType string          `json:"globalAuthConfigType"`
	UserId               int32           `json:"-"`
}

const (
	NOCHARTEXIST string = "NOCHARTEXIST"
)

type PolicyType int

const (
	POLICY_DIRECT             PolicyType = 1
	POLICY_GROUP              PolicyType = 1
	SUPERADMIN                           = "role:super-admin___"
	CanManageAllAccessRole               = "role:all-access-manager___"
	CanManageAllAccessJsonKey            = "canManageAllAccess"
	USER_TYPE_API_TOKEN                  = "apiToken"
	ACTION_SUPERADMIN                    = "super-admin"
)

type UserListingResponse struct {
	Users      []UserInfo `json:"users"`
	TotalCount int        `json:"totalCount"`
}

type RoleGroupListingResponse struct {
	RoleGroups []*RoleGroup `json:"roleGroups"`
	TotalCount int          `json:"totalCount"`
}

type Status string

const (
	Active          Status = "active"
	Inactive        Status = "inactive"
	TemporaryAccess Status = "temporaryAccess"
	Unknown         Status = "unknown"
)

type BulkStatusUpdateRequest struct {
	UserIds                 []int32         `json:"userIds",validate:"required"`
	Status                  Status          `json:"userStatus",validate:"required"'`
	TimeoutWindowExpression time.Time       `json:"timeoutWindowExpression"`
	ListingRequest          *ListingRequest `json:"listingRequest,omitempty"`
	LoggedInUserId          int32           `json:"-"`
}

type ActionResponse struct {
	Suceess bool `json:"suceess"`
}

type RestrictedGroup struct {
	Group                   string
	HasSuperAdminPermission bool
}

type ListingRequest struct {
	Status    []Status  `json:"status"`    // only being used for users
	SearchKey string    `json:"searchKey"` // this is used for searching groupName or email matching search key.
	SortOrder SortOrder `json:"sortOrder"`
	SortBy    SortBy    `json:"sortBy"`
	Offset    int       `json:"offset"`
	Size      int       `json:"size"`
	ShowAll   bool      `json:"showAll"`
	/* TypeToFetch specifies which user data should be retrieved (e.g., existing or removed or all users).
	By default, only existing users data are returned. */
	TypeToFetch TypeToFetch `json:"typeToFetch"`
	CurrentTime time.Time   `json:"-"` // for Internal Use
	CountCheck  bool        `json:"-"` // for Internal Use
	StatusType  StatusType  `json:"-"` // for Internal Use
}

type BulkDeleteRequest struct {
	Ids            []int32         `json:"ids"`
	ListingRequest *ListingRequest `json:"listingRequest,omitempty"`
	LoggedInUserId int32           `json:"-"`
}

type UserRoleGroup struct {
	RoleGroup               *RoleGroup `json:"roleGroup"`
	Status                  Status     `json:"status"`
	TimeoutWindowExpression time.Time  `json:"timeoutWindowExpression"`
}

type GroupPermissionsAuditDto struct {
	RoleGroupInfo *RoleGroup   `json:"roleGroupInfo,omitempty"`
	EntityAudit   sql.AuditLog `json:"entityAudit,omitempty"`
}

func NewGroupPermissionsAuditDto() *GroupPermissionsAuditDto {
	return &GroupPermissionsAuditDto{}
}

func (pa *GroupPermissionsAuditDto) WithRoleGroupInfo(roleGroupInfo *RoleGroup) *GroupPermissionsAuditDto {
	pa.RoleGroupInfo = roleGroupInfo
	return pa
}
func (pa *GroupPermissionsAuditDto) WithEntityAudit(entityAudit sql.AuditLog) *GroupPermissionsAuditDto {
	pa.EntityAudit = entityAudit
	return pa
}

type UserPermissionsAuditDto struct {
	UserInfo    *UserInfo    `json:"userInfo,omitempty"`
	EntityAudit sql.AuditLog `json:"entityAudit,omitempty"`
}

func NewUserPermissionsAuditDto() *UserPermissionsAuditDto {
	return &UserPermissionsAuditDto{}
}

func (pa *UserPermissionsAuditDto) WithUserInfo(userInfo *UserInfo) *UserPermissionsAuditDto {
	pa.UserInfo = userInfo
	return pa
}

func (pa *UserPermissionsAuditDto) WithEntityAudit(entityAudit sql.AuditLog) *UserPermissionsAuditDto {
	pa.EntityAudit = entityAudit
	return pa
}

type SelfRegisterDto struct {
	UserInfo *UserInfo
	//ent only fields
	GroupsFromClaims        []string
	GroupClaimsConfigActive bool
}

type TimeoutWindowConfigDto struct {
	TimeExpression        string
	ExpressionFormat      string
	TimeoutWindowConfigId int
}

type UserGroupMapDto struct {
	UserIdVsUserGroupMap map[int32][]*beans.UserGroupDTO
}
