/*
 * Copyright (c) 2024. Devtron Inc.
 */

package helper

import (
	"github.com/devtron-labs/devtron/pkg/auth/user/bean"
	bean2 "github.com/devtron-labs/devtron/pkg/timeoutWindow/repository/bean"
	"strconv"
	"strings"
	"time"
)

func GetCasbinFormattedTimeAndFormatFromStatusAndExpression(status bean.Status, timeoutWindowExpression time.Time) (string, string) {
	if status == bean.Active && timeoutWindowExpression.IsZero() {
		return "", ""
	} else if status == bean.Active && !timeoutWindowExpression.IsZero() {
		return strings.ToLower(timeoutWindowExpression.String()), strconv.Itoa(int(bean2.TimeStamp))
	} else if status == bean.Inactive {
		return strings.ToLower(timeoutWindowExpression.String()), strconv.Itoa(int(bean2.TimeZeroFormat))
	}
	return "", ""
}
