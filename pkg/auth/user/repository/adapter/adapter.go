/*
 * Copyright (c) 2024. Devtron Inc.
 */

package adapter

import (
	bean "github.com/devtron-labs/devtron/pkg/auth/user/bean"
	"github.com/devtron-labs/devtron/pkg/auth/user/repository"
	"github.com/devtron-labs/devtron/pkg/sql"
	"time"
)

func GetLastLoginTime(model repository.UserModel) time.Time {
	lastLoginTime := time.Time{}
	if model.UserAudit != nil {
		lastLoginTime = model.UserAudit.UpdatedOn
	}
	return lastLoginTime
}

func GetUserModelBasicAdapter(emailId, accessToken, userType string) *repository.UserModel {
	model := &repository.UserModel{
		EmailId:     emailId,
		AccessToken: accessToken,
		UserType:    userType,
	}
	return model
}

func GetUpdateRbacPolicyDataModel(id int, entity, accessType, roleName, policyData string, isPresentRole bool, userId int32) *repository.RbacPolicyData {
	return &repository.RbacPolicyData{
		Id:           id,
		Entity:       entity,
		AccessType:   accessType,
		Role:         roleName,
		PolicyData:   policyData,
		IsPresetRole: isPresentRole,
		Deleted:      false,
		AuditLog: sql.AuditLog{
			UpdatedOn: time.Now(),
			UpdatedBy: userId,
		},
	}
}

func GetUserRoleModelAdapter(userId, userLoggedInId int32, roleId int, twcConfigDto *bean.TimeoutWindowConfigDto) *repository.UserRoleModel {
	return &repository.UserRoleModel{
		UserId:                       userId,
		RoleId:                       roleId,
		TimeoutWindowConfigurationId: twcConfigDto.TimeoutWindowConfigId,
		AuditLog:                     sql.NewDefaultAuditLog(userLoggedInId),
	}
}

func GetRoleGroupRoleMappingModelAdapter(roleGroupId int32, roleId int, userId int32) *repository.RoleGroupRoleMapping {
	return &repository.RoleGroupRoleMapping{
		RoleGroupId: roleGroupId,
		RoleId:      roleId,
		AuditLog:    sql.NewDefaultAuditLog(userId),
	}
}

func GetRoleFilterFromRoleAdapter(role *repository.RoleModel) *bean.RoleFilter {
	return &bean.RoleFilter{
		EntityName:   role.EntityName,
		Entity:       role.Entity,
		Action:       role.Action,
		Environment:  role.Environment,
		Team:         role.Team,
		AccessType:   role.AccessType,
		Cluster:      role.Cluster,
		Namespace:    role.Namespace,
		Group:        role.Group,
		Kind:         role.Kind,
		Resource:     role.Resource,
		Workflow:     role.Workflow,
		Release:      role.Release,
		ReleaseTrack: role.ReleaseTrack,
		SubAction:    role.SubAction,
	}
}
