package user

import (
	"github.com/devtron-labs/devtron/pkg/auth/user/bean"
	"github.com/devtron-labs/devtron/pkg/auth/user/repository"
	"github.com/devtron-labs/devtron/pkg/auth/user/repository/adapter"
	"github.com/go-pg/pg"
	"go.uber.org/zap"
)

type RbacRoleAuditService interface {
	SaveAudit(tx *pg.Tx, req *bean.RbacAuditDto) error
}

type RbacRoleAuditServiceImpl struct {
	logger            *zap.SugaredLogger
	rbacRoleAuditRepo repository.RbacRoleAuditRepository
}

func NewRbacRoleAuditServiceImpl(logger *zap.SugaredLogger, rbacRoleAuditRepo repository.RbacRoleAuditRepository) *RbacRoleAuditServiceImpl {
	return &RbacRoleAuditServiceImpl{
		logger:            logger,
		rbacRoleAuditRepo: rbacRoleAuditRepo,
	}
}
func (impl *RbacRoleAuditServiceImpl) SaveAudit(tx *pg.Tx, req *bean.RbacAuditDto) error {
	model := adapter.BuildRbacRoleAuditModel(req.Role, req.Entity, req.AccessType, repository.RoleAuditActionType(req.AuditOperation), req.UserId, req.RoleData, req.PolicyData)
	err := impl.rbacRoleAuditRepo.Save(tx, model)
	if err != nil {
		impl.logger.Errorw("error encountered in SaveAudit", "model", model, "err", err)
		return err
	}
	return nil
}
