package user

import (
	bean3 "github.com/devtron-labs/devtron/pkg/auth/authorisation/casbin/bean"
	adapter3 "github.com/devtron-labs/devtron/pkg/auth/user/adapter"
	bean2 "github.com/devtron-labs/devtron/pkg/auth/user/bean"
	adapter2 "github.com/devtron-labs/devtron/pkg/auth/user/repository/adapter"
	"github.com/devtron-labs/devtron/pkg/operationAudit/adapter"
	bean5 "github.com/devtron-labs/devtron/pkg/operationAudit/bean"
	"github.com/devtron-labs/devtron/pkg/sql"
	"github.com/go-pg/pg"
	"time"
)

func HidePermissions(roleGroup *bean2.RoleGroup) {
	// setting empty role filters to hide permissions
	roleGroup.RoleFilters = make([]bean2.RoleFilter, 0)
	roleGroup.AccessRoleFilters = make([]bean2.RoleFilter, 0)
}

func (impl RoleGroupServiceImpl) bulkCreateAuditsForIds(tx *pg.Tx, roleGroupIds []int32, mapOfRoleGroupIdVsRoleGroupDetails map[int32]*bean2.RoleGroup, loggedInUserId int32, recordedTime time.Time) error {
	allOperationDtos := make([]*bean5.OperationAuditDto, 0, len(roleGroupIds))
	for _, id := range roleGroupIds {
		if roleGroupDetails, ok := mapOfRoleGroupIdVsRoleGroupDetails[id]; ok {
			dto, err := adapter.CreateOperationAuditDtoForRoleGroup(id, bean5.DeleteOperationType, bean2.NewGroupPermissionsAuditDto().WithRoleGroupInfo(roleGroupDetails).WithEntityAudit(
				sql.NewAuditLog(roleGroupDetails.EntityAudit.CreatedOn, recordedTime,
					roleGroupDetails.EntityAudit.CreatedBy, loggedInUserId)), loggedInUserId)
			if err != nil {
				impl.logger.Errorw("error in bulkCreateAuditsForIds", "err", err)
				return err
			}
			allOperationDtos = append(allOperationDtos, dto)
		}
	}
	if len(allOperationDtos) > 0 {
		err := impl.userOperationAuditService.BulkSaveAudits(tx, allOperationDtos)
		if err != nil {
			impl.logger.Errorw("error in bulkCreateAuditsForIds", "err", err)
			return err
		}
	}
	return nil
}

func (impl RoleGroupServiceImpl) CreateAndAddPolicesForManageAllAccess(tx *pg.Tx, userLoggedInId int32, roleGroupId int32, groupCasbinName string) ([]bean3.Policy, error) {
	policies := make([]bean3.Policy, 0)
	flag, err := impl.userAuthRepository.CreateRoleForAllAccessManagerIfNotExists(tx, userLoggedInId)
	if err != nil || flag == false {
		impl.logger.Errorw("error encountered in CreateAndAddPolicesForAllManagerAccess", "err", err)
		return nil, err
	}
	roleModel, err := impl.userAuthRepository.GetRoleByFilterForAllTypes(adapter3.BuildAllAccessManagerRoleFieldsDto())
	if err != nil {
		impl.logger.Errorw("error in getting role by filter for all Types for superAdmin", "err", err, "RoleGroupId", roleGroupId)
		return nil, err
	}
	if roleModel.Id > 0 {
		roleGroupMappingModel := adapter2.GetRoleGroupRoleMappingModelAdapter(roleGroupId, roleModel.Id, userLoggedInId)
		roleGroupMappingModel, err = impl.roleGroupRepository.CreateRoleGroupRoleMapping(roleGroupMappingModel, tx)
		if err != nil {
			impl.logger.Errorw("error in creating role group role mapping", "err", err, "RoleGroupId", roleGroupId)
			return nil, err
		}
		policies = append(policies, bean3.Policy{Type: "g", Sub: bean3.Subject(groupCasbinName), Obj: bean3.Object(roleModel.Role)})
	}
	return policies, nil
}
