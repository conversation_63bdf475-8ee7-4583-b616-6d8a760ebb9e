/*
 * Copyright (c) 2024. Devtron Inc.
 */

package trigger

import (
	"fmt"
	"github.com/devtron-labs/common-lib/imageScan/bean"
	bean6 "github.com/devtron-labs/devtron/enterprise/api/commonPolicyActions/bean"
	repository3 "github.com/devtron-labs/devtron/internal/sql/repository/dockerRegistry"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/internal/util"
	bean3 "github.com/devtron-labs/devtron/pkg/bean"
	"github.com/devtron-labs/devtron/pkg/bean/common"
	bean2 "github.com/devtron-labs/devtron/pkg/build/pipeline/bean"
	repository2 "github.com/devtron-labs/devtron/pkg/cluster/environment/repository"
	bean7 "github.com/devtron-labs/devtron/pkg/devtronResource/bean"
	"github.com/devtron-labs/devtron/pkg/featureFlag"
	"github.com/devtron-labs/devtron/pkg/featureFlag/model"
	pipeline2 "github.com/devtron-labs/devtron/pkg/pipeline"
	pipelineConfigBean "github.com/devtron-labs/devtron/pkg/pipeline/bean"
	"github.com/devtron-labs/devtron/pkg/pipeline/executors"
	"github.com/devtron-labs/devtron/pkg/pipeline/helper"
	"github.com/devtron-labs/devtron/pkg/pipeline/infraProviders/infraGetters"
	pipelineRepo "github.com/devtron-labs/devtron/pkg/pipeline/repository"
	"github.com/devtron-labs/devtron/pkg/pipeline/types"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/security/scanTool/repository"
	"github.com/devtron-labs/devtron/pkg/resourceQualifiers"
	"github.com/go-pg/pg"
	"net/http"
	"strconv"
	"strings"
)

func (impl *HandlerServiceImpl) updateRuntimeParamsForAutoCI(ciPipelineId int, runtimeParameters *common.RuntimeParameters) (*common.RuntimeParameters, error) {
	var err error
	// updating runtime params
	runtimeParameters, err = impl.pipelineHelper.UpdateEnvVarMapWithRuntimeParamsForAutoCI(ciPipelineId, runtimeParameters)
	if err != nil {
		impl.Logger.Errorw("err, updateEnvVarMapWithRuntimeParamsForAutoCI", "ciPipelineId", ciPipelineId,
			"runtimeParameters", runtimeParameters, "err", err)
		return nil, err
	}
	return runtimeParameters, nil
}

func (impl *HandlerServiceImpl) getRuntimeParamsForBuildingManualTriggerHashes(ciTriggerRequest bean3.CiTriggerRequest) *common.RuntimeParameters {
	if ciTriggerRequest.RuntimeParams == nil {
		ciTriggerRequest.RuntimeParams = common.NewRuntimeParameters()
	}
	ciTriggerRequest.RuntimeParams = ciTriggerRequest.RuntimeParams.MigrateEnvVariables()
	return ciTriggerRequest.RuntimeParams
}

// fetchImageScanExecutionMedium fetches medium of image scan execution, if current active tool contains pluginId
// then returns steps else rest, also returns the corresponding scanToolMetadata irrespective of executionMedium.
func (impl *HandlerServiceImpl) fetchImageScanExecutionMedium() (*repository.ScanToolMetadata, bean.ScanExecutionMedium, error) {
	executionMedium := bean.InHouse
	scanTool, err := impl.scanToolMetadataService.GetActiveTool()
	if err != nil {
		impl.Logger.Errorw("error in getting active scan tool", "err", err)
		return nil, executionMedium, err
	}
	if scanTool.PluginId > 0 {
		executionMedium = bean.External
	}
	return scanTool, executionMedium, nil
}

// fetchImageScanExecutionStepsForWfRequest fetches image scanning plugin steps data for wf request along with ref plugins
// used as tasks for that scan plugin fetches scanning steps data only for currently single active scan tool.
// This function returns an array of ImageScanningSteps. In the future, when support for scanning via multiple scan tools
// is introduced, only the code will require modification, leaving the workflowRequest schema unchanged.
func (impl *HandlerServiceImpl) fetchImageScanExecutionStepsForWfRequest(scanToolMetadata *repository.ScanToolMetadata) ([]*types.ImageScanningSteps, []*pipelineConfigBean.RefPluginObject, error) {
	scanningPluginObjs, err := impl.pipelineStageService.BuildRefPluginStepDataForWfRequest([]int{scanToolMetadata.PluginId})
	if err != nil {
		impl.Logger.Errorw("error in building step data by pluginId of scanning plugin for workflow request", "pluginId", scanToolMetadata.PluginId, "err", err)
		return nil, nil, err
	}
	imageScanningSteps := make([]*types.ImageScanningSteps, 0, len(scanningPluginObjs))
	scanToolPluginUsed, refPluginsUsed := helper.FetchScanToolPluginAndAllRefPluginsUsed(scanningPluginObjs)
	imageScanningSteps = append(imageScanningSteps, types.NewImageScanningStepsDto().WithScanToolId(scanToolMetadata.Id).WithSteps(scanToolPluginUsed.Steps))

	return imageScanningSteps, refPluginsUsed, nil
}

func getResourceIdentifierDetails(cdPipelineId int, appName string, ciBranchValues []string) *bean6.ResourceIdentifierDetails {
	return &bean6.ResourceIdentifierDetails{
		ApplicationKindCriteria: &bean7.FilterCriteriaDecoderMulti{
			Values: []string{appName},
		},
		CiPipelineKindCriteria: &bean7.FilterCriteriaDecoderMulti{
			ValueIntegers: []int{cdPipelineId},
		},
		Branches: ciBranchValues,
	}
}

func (impl *HandlerServiceImpl) checkIfCITriggerIsBlocked(pipeline *pipelineConfig.CiPipeline,
	ciMaterials []*pipelineConfig.CiPipelineMaterial, isJob bool) (bool, error) {
	ciBranchValues := make([]string, 0, len(ciMaterials))
	for _, ciMaterial := range ciMaterials {
		// ignore those materials which have inactive git material
		if ciMaterial == nil || ciMaterial.GitMaterial == nil || !ciMaterial.GitMaterial.Active {
			continue
		}
		ciBranchValues = append(ciBranchValues, ciMaterial.Value)
	}
	isCiTriggerBlocked := false
	if !isJob {
		offendingPluginInfo, err := impl.pluginPolicyV2.EnforcementInfoForResource(bean7.DevtronResourceCiPipeline, getResourceIdentifierDetails(pipeline.Id, pipeline.App.AppName, ciBranchValues))
		if err != nil {
			impl.Logger.Errorw("error in getting enforcement info for deployment stage", "err", err)
			return false, err
		}
		isCiTriggerBlocked = offendingPluginInfo.TriggerBlocked()
	}
	return isCiTriggerBlocked, nil
}

func (impl *HandlerServiceImpl) handleWFIfCITriggerIsBlocked(ciWorkflow *pipelineConfig.CiWorkflow) (*pipelineConfig.CiWorkflow, error) {
	impl.Logger.Errorw("cannot trigger pipeline, blocked by mandatory plugin policy", "ciPipelineId", ciWorkflow.CiPipelineId)
	errMsg := pipeline2.MandatoryPluginCiTriggerBlockError
	validationErr := util.NewApiError(http.StatusBadRequest, errMsg, errMsg)
	dbErr := impl.markCurrentCiWorkflowFailed(ciWorkflow, validationErr)
	if dbErr != nil {
		impl.Logger.Errorw("saving workflow error", "err", dbErr)
	}
	return &pipelineConfig.CiWorkflow{}, validationErr
}

func (impl *HandlerServiceImpl) checkArgoSetupRequirement(envModal *repository2.Environment) error {
	if !envModal.Cluster.CdArgoSetup {
		errMsg := fmt.Sprintf("in cluster setup is not configured for the cluster '%s'", envModal.Cluster.ClusterName)
		return util.NewApiError(http.StatusPreconditionFailed, errMsg, errMsg)
	}
	return nil
}

func (impl *HandlerServiceImpl) updateWorkflowRequestForDigestPull(pipelineId int, workflowRequest *types.WorkflowRequest) (*types.WorkflowRequest, error) {
	//check if we need to pull docker digest, pull digest only in case image scan plugin is configured in this pre-ci stage
	isPluginConfigured, err := impl.pipelineStageService.IsPluginConfiguredAtCiPipelineStageForDockerImagePull(pipelineId, pipelineRepo.PIPELINE_STAGE_TYPE_PRE_CI)
	if err != nil {
		impl.Logger.Errorw("error in checking IsPluginConfiguredAtCiPipelineStageForDockerImagePull", "ciPipelineId", pipelineId, "err", err)
		return nil, err
	}
	workflowRequest.ShouldPullDigest = isPluginConfigured
	return workflowRequest, nil
}

func (impl *HandlerServiceImpl) updateCIProjectDetailWithCloningMode(appId int, ciMaterial *pipelineConfig.CiPipelineMaterial,
	ciProjectDetail pipelineConfigBean.CiProjectDetails) (pipelineConfigBean.CiProjectDetails, error) {
	cloningMode, cloningModeErr := impl.featureFlagService.GetFeatureFlagValue(
		featureFlag.NewFeatureFlagHandlerRequest(model.CloningMode).
			WithAppId(appId))
	if cloningModeErr != nil {
		impl.Logger.Errorw("could not fetch feature flag value", "err", cloningModeErr)
		return ciProjectDetail, cloningModeErr
	}
	if executors.IsShallowClonePossible(ciMaterial, impl.config.GitProviders, cloningMode) {
		ciProjectDetail.CloningMode = executors.CloningModeShallow
	}
	return ciProjectDetail, nil
}

func (impl *HandlerServiceImpl) updateWorkflowRequestWithRemoteConnConf(dockerRegistry *repository3.DockerArtifactStore,
	workflowRequest *types.WorkflowRequest) (*types.WorkflowRequest, error) {
	registryConnectionConfig, err := impl.dockerRegistryConfig.GetRemoteConnectionConfigByDockerId(dockerRegistry.Id)
	if err != nil && err != pg.ErrNoRows {
		impl.Logger.Errorw("err in fetching connection config", "err", err, "dockerId", dockerRegistry.Id)
		return nil, err
	}
	workflowRequest.DockerRegistryConnectionConfig = registryConnectionConfig
	return workflowRequest, nil
}

func (impl *HandlerServiceImpl) updateWorkflowRequestWithEntSupportData(workflowRequest *types.WorkflowRequest) *types.WorkflowRequest {
	ciCacheResource, err := impl.ciCacheResourceSelector.GetAvailResource(workflowRequest.Scope, workflowRequest.AppLabels, workflowRequest.WorkflowId)
	if err != nil {
		// some error occurred skip the ciCacheResource and continue
		impl.Logger.Errorw("error in getting ci cache resource", "err", err)
	}
	if ciCacheResource != nil {
		workflowRequest.CiCacheResourceMap = ciCacheResource.GetMap()
	}

	enableSecretMasking, err := impl.featureFlagService.GetFeatureFlagBoolValueFor(
		featureFlag.NewFeatureFlagHandlerRequest(model.EnableSecretMasking).
			WithAppId(workflowRequest.AppId))
	if err != nil {
		workflowRequest.EnableSecretMasking = true
	} else {
		workflowRequest.EnableSecretMasking = enableSecretMasking
	}

	propagateLabelsInBuildxPod, err := impl.featureFlagService.GetFeatureFlagBoolValueFor(
		featureFlag.NewFeatureFlagHandlerRequest(model.PropagateLabelsInBuildxPod).
			WithAppId(workflowRequest.AppId))
	if err != nil {
		workflowRequest.PropagateLabelsInBuildxPod = false
	} else {
		workflowRequest.PropagateLabelsInBuildxPod = propagateLabelsInBuildxPod
	}
	return workflowRequest
}

func (impl *HandlerServiceImpl) updateWorkflowRequestWithBuildCacheData(workflowRequest *types.WorkflowRequest, scope resourceQualifiers.Scope) (*types.WorkflowRequest, error) {
	buildxCacheFeatureFlagValue, err := impl.featureFlagService.GetFeatureFlagValue(featureFlag.NewFeatureFlagHandlerRequest(model.BuildxCacheModeMin).WithAppId(scope.AppId))
	if err != nil {
		impl.Logger.Errorw("error in retrieving feature flag BuildxCacheModeMin value ", "err", err)
		return nil, err
	}
	workflowRequest.BuildxCacheModeMin, err = strconv.ParseBool(buildxCacheFeatureFlagValue)
	if err != nil {
		impl.Logger.Errorw("error in parsing buildxCacheModeMin value to bool ", "err", err)
		return nil, err
	}
	asyncBuildxCacheExportFeatureFlagValue, err := impl.featureFlagService.GetFeatureFlagValue(featureFlag.NewFeatureFlagHandlerRequest(model.AsyncBuildxCacheExport).WithAppId(scope.AppId))
	if err != nil {
		impl.Logger.Errorw("error in retrieving feature flag AsyncBuildxCacheExport value ", "err", err)
		return nil, err
	}
	workflowRequest.AsyncBuildxCacheExport, err = strconv.ParseBool(asyncBuildxCacheExportFeatureFlagValue)
	if err != nil {
		impl.Logger.Errorw("error in parsing AsyncBuildxCacheExport value to bool ", "err", err)
		return nil, err
	}
	return workflowRequest, nil
}

func (impl *HandlerServiceImpl) canSetK8sDriverData(workflowRequest *types.WorkflowRequest) bool {
	return impl.config != nil && workflowRequest.IsDevtronCI() && workflowRequest.CiBuildConfig != nil &&
		workflowRequest.CiBuildConfig.DockerBuildConfig != nil && workflowRequest.CiBuildConfig.DockerBuildConfig.TargetPlatform != ""
}

func (impl *HandlerServiceImpl) getK8sDriverOptions(workflowRequest *types.WorkflowRequest, targetPlatforms string) ([]map[string]string, error) {
	impl.Logger.Debugw("getting getK8sDriverOptions", "appId", workflowRequest.AppId)
	// parse the target Platforms into [] string, with the comma separated platforms, trimming spaces if any
	targetPlatformsList := strings.Split(targetPlatforms, ",")
	for i, p := range targetPlatformsList {
		targetPlatformsList[i] = strings.TrimSpace(p)
	}
	infraGetter, _ := impl.infraProvider.GetInfraProvider(workflowRequest.Type)
	// here we are making sure that No need of cm value need to parse after fetching the infraConfig for app,
	// if not found on app level then it will be returning fallback to global profile
	infraRequest := infraGetters.NewInfraRequest(workflowRequest.Scope).
		WithAppId(workflowRequest.AppId).
		WithEnvId(workflowRequest.EnvironmentId).
		WithPlatform(targetPlatformsList...)
	infraConfigs, err := infraGetter.GetConfigurationsByScopeAndTargetPlatforms(infraRequest)
	if err != nil {
		impl.Logger.Errorw("error in getting the infra configuration by scope", "infraRequest", infraRequest, "err", err)
		return nil, err
	}
	impl.Logger.Debugw("k8s driver opts config found", "appId", workflowRequest.AppId, "infraConfigs", infraConfigs)
	buildxK8sDriverOptions := make([]map[string]string, 0)
	for platform, infraConfig := range infraConfigs {
		buildxK8sDriverOptions = append(buildxK8sDriverOptions, map[string]string{
			"platform":      platform,
			"driverOptions": impl.infraConfigService.GetDriverOptions(infraConfig, workflowRequest.Namespace),
		})
	}
	err = infraGetter.SaveInfraConfigHistorySnapshot(workflowRequest.WorkflowId, workflowRequest.TriggeredBy, infraConfigs)
	if err != nil {
		impl.Logger.Errorw("error in saving infra config history snapshot", "err", err, "infraConfigs", infraConfigs)
	}
	return buildxK8sDriverOptions, nil
}

func (impl *HandlerServiceImpl) updateCIBuildConfig(ciBuildConfigBean *bean2.CiBuildConfigBean) *bean2.CiBuildConfigBean {
	defaultTargetPlatform := impl.config.DefaultTargetPlatform
	useBuildx := impl.config.UseBuildx
	if ciBuildConfigBean.DockerBuildConfig.TargetPlatform == "" && useBuildx {
		ciBuildConfigBean.DockerBuildConfig.TargetPlatform = defaultTargetPlatform
	}
	ciBuildConfigBean.DockerBuildConfig.UseBuildx = useBuildx
	ciBuildConfigBean.DockerBuildConfig.BuildxProvenanceMode = impl.config.BuildxProvenanceMode
	ciBuildConfigBean.DockerBuildConfig.BuildxDriverImage = impl.config.BuildxDriverImage
	return ciBuildConfigBean
}

func updateBuildPrePostStepDataReq(req *pipelineConfigBean.BuildPrePostStepDataRequest, trigger *types.CiTriggerRequest) *pipelineConfigBean.BuildPrePostStepDataRequest {
	return req.WithRuntimeParameters(trigger.RuntimeParameters)
}
