package enums

type TerraformStatusReason string

const (
	AccessDeniedReason                 TerraformStatusReason = "AccessDenied"
	ArtifactFailedReason               TerraformStatusReason = "ArtifactFailed"
	RetryLimitReachedReason            TerraformStatusReason = "RetryLimitReached"
	DeletionBlockedByDependants        TerraformStatusReason = "DeletionBlockedByDependantsReason"
	DependencyNotReadyReason           TerraformStatusReason = "DependencyNotReady"
	DriftDetectedReason                TerraformStatusReason = "DriftDetected"
	DriftDetectionFailedReason         TerraformStatusReason = "DriftDetectionFailed"
	HealthChecksFailedReason           TerraformStatusReason = "HealthChecksFailed"
	NoDriftReason                      TerraformStatusReason = "NoDrift"
	OutputsWritingFailedReason         TerraformStatusReason = "OutputsWritingFailed"
	PlannedNoChangesReason             TerraformStatusReason = "TerraformPlannedNoChanges"
	PlannedWithChangesReason           TerraformStatusReason = "TerraformPlannedWithChanges"
	PostPlanningWebhookFailedReason    TerraformStatusReason = "PostPlanningWebhookFailed"
	TFExecApplyFailedReason            TerraformStatusReason = "TFExecApplyFailed"
	TFExecApplySucceedReason           TerraformStatusReason = "TerraformAppliedSucceed"
	TFExecForceUnlockReason            TerraformStatusReason = "ForceUnlock"
	TFExecInitFailedReason             TerraformStatusReason = "TFExecInitFailed"
	TFExecLockHeldReason               TerraformStatusReason = "LockHeld"
	TFExecNewFailedReason              TerraformStatusReason = "TFExecNewFailed"
	TFExecOutputFailedReason           TerraformStatusReason = "TFExecOutputFailed"
	TFExecPlanFailedReason             TerraformStatusReason = "TFExecPlanFailed"
	TemplateGenerationFailedReason     TerraformStatusReason = "TemplateGenerationFailed"
	VarsGenerationFailedReason         TerraformStatusReason = "VarsGenerationFailed"
	WorkspaceSelectFailedReason        TerraformStatusReason = "SelectWorkspaceFailed"
	TerraformReasonAppliedSuccessfully TerraformStatusReason = "TerraformAppliedSucceed"
	TerraformApplyFailed               TerraformStatusReason = "TerraformAppliedFail"
	TerraformOutputsAvailable          TerraformStatusReason = "TerraformOutputsAvailable"
)

func (t TerraformStatusReason) String() string {
	return string(t)
}
