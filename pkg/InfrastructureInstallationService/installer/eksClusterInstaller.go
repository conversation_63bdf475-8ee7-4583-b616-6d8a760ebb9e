package installer

import (
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/eks"
	k8sUtil "github.com/devtron-labs/common-lib/utils/k8s"
	"github.com/devtron-labs/common-lib/utils/k8s/commonBean"
	"github.com/devtron-labs/devtron/internal/util"
	"github.com/devtron-labs/devtron/pkg/InfrastructureInstallationService/bean"
	"github.com/devtron-labs/devtron/pkg/InfrastructureInstallationService/enums"
	"github.com/devtron-labs/devtron/pkg/InfrastructureInstallationService/helper"
	"github.com/devtron-labs/devtron/pkg/InfrastructureInstallationService/repository"
	clusterService "github.com/devtron-labs/devtron/pkg/cluster"
	bean2 "github.com/devtron-labs/devtron/pkg/cluster/bean"
	repository2 "github.com/devtron-labs/devtron/pkg/cluster/environment/repository"
	clusterRepository "github.com/devtron-labs/devtron/pkg/cluster/repository"
	deleteService "github.com/devtron-labs/devtron/pkg/delete"
	globalUtil "github.com/devtron-labs/devtron/util"
	"github.com/go-pg/pg"
	"go.uber.org/zap"
	"gopkg.in/yaml.v2"
	v1 "k8s.io/api/core/v1"
	rbac "k8s.io/api/rbac/v1"
	errors3 "k8s.io/apimachinery/pkg/api/errors"
	v2 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"net/http"
)

type EKSClusterInstaller struct {
	logger                             *zap.SugaredLogger
	clusterRepository                  clusterRepository.ClusterRepository
	GlobalEnvVariables                 *globalUtil.GlobalEnvVariables
	k8sService                         k8sUtil.K8sService
	envVariables                       *globalUtil.EnvironmentVariables
	clusterService                     clusterService.ClusterService
	infrastructureDeploymentRepository repository.InfrastructureInstallationRepository
	deleteService                      deleteService.DeleteService
	environmentRepository              repository2.EnvironmentRepository
}

func NewEKSClusterInstaller(
	logger *zap.SugaredLogger,
	clusterRepository clusterRepository.ClusterRepository,
	k8sService k8sUtil.K8sService,
	envVariables *globalUtil.EnvironmentVariables,
	clusterService clusterService.ClusterService,
	infrastructureDeploymentRepository repository.InfrastructureInstallationRepository,
	deleteService deleteService.DeleteService,
	environmentRepository repository2.EnvironmentRepository) *EKSClusterInstaller {
	return &EKSClusterInstaller{
		logger:                             logger,
		clusterRepository:                  clusterRepository,
		GlobalEnvVariables:                 envVariables.GlobalEnvVariables,
		k8sService:                         k8sService,
		clusterService:                     clusterService,
		infrastructureDeploymentRepository: infrastructureDeploymentRepository,
		deleteService:                      deleteService,
		environmentRepository:              environmentRepository,
	}
}

func (impl *EKSClusterInstaller) ValidateRequest(request *bean.ProvisionRequest) error {
	clusterName, err := impl.GetInstallationName(request.ChartValues)
	if err != nil {
		impl.logger.Errorw("error in getting installation name", "error", err)
		return err
	}
	existingModel, err := impl.clusterRepository.FindOne(clusterName)
	if err != nil && !errors.Is(err, pg.ErrNoRows) {
		impl.logger.Error("error in getting cluster", "clusterName", clusterName, "err", err)
		return err
	}
	if existingModel.Id > 0 {
		impl.logger.Errorw("error on fetching cluster, duplicate", "name", clusterName)
		return fmt.Errorf("cluster already exists")
	}
	return nil
}

func (impl *EKSClusterInstaller) ValidateDeleteRequest(installationId int) error {
	installation, err := impl.infrastructureDeploymentRepository.GetById(installationId)
	if err != nil {
		impl.logger.Errorw("error in getting installation", "installationId", installationId, "err", err)
		return err
	}

	env, err := impl.environmentRepository.FindByClusterId(installation.InstalledEntityId)
	if err != nil && err != pg.ErrNoRows {
		impl.logger.Errorw("err in deleting cluster", "clusterId", installation.InstalledEntityId, "err", err)
		return err
	}
	if len(env) > 0 {
		impl.logger.Errorw("err in deleting cluster installation, found env in this cluster", "clusterName", installation.InstalledEntityId, "err", err)
		return &util.ApiError{HttpStatusCode: http.StatusBadRequest, UserMessage: " Please delete all environments before deleting this cluster installation"}
	}
	return nil
}

func (impl *EKSClusterInstaller) PostInstallSuccessfulOperations(ctx context.Context, installation *bean.Installation) error {

	infraDBConfig, err := impl.infrastructureDeploymentRepository.GetById(installation.InstallationId)
	if err != nil {
		impl.logger.Errorw("error in getting infrastructure deployment db config", "installationId", installation.InstallationId, "error", err)
		return err
	}
	if infraDBConfig.InstalledEntityId > 0 {
		//InstalledEntityId>0 indicates cluster is already saved in db
		return nil
	}

	name := installation.Name
	region, err := GetInstallationRegion(installation.Values)
	if err != nil {
		impl.logger.Errorw("error in getting installation region", "err", err)
		return err
	}

	infraConfig, err := helper.ParseInfraInstallationEnvConfig(impl.GlobalEnvVariables.InfraConfig)
	if err != nil {
		impl.logger.Errorw("error in parsing infrastructure deployment config", "err", err)
		return err
	}

	awsAccessKeyId, awsSecretAccessKey, err := impl.getAWSAccessKeyIdAndSecretKey(infraConfig.TerraformAwsCredsSecretName)
	if err != nil {
		impl.logger.Error("error in getting AWS access key and secret key", "err", err)
		return err
	}

	eksCluster, err := impl.getEksClusterData(awsAccessKeyId, awsSecretAccessKey, region, name)
	if err != nil {
		impl.logger.Errorw("error in getting eks cluster", "name", name, "err", err)
		return err
	}

	if eksCluster == nil || eksCluster.Cluster == nil || eksCluster.Cluster.CertificateAuthority == nil {
		return errors.New("eks cluster has no certificate authority")
	}

	encodedCAData := eksCluster.Cluster.CertificateAuthority.Data
	caData, err := base64.StdEncoding.DecodeString(*encodedCAData)
	if err != nil {
		impl.logger.Errorw("error in decoding cluster CA data", "name", name, "err", err)
		return err
	}

	newClusterClient, err := impl.getK8sClientForEKSCluster(ctx, eksCluster, caData, awsAccessKeyId, awsSecretAccessKey, region)
	if err != nil {
		impl.logger.Errorw("error in getting k8s client", "name", name, "err", err)
		return err
	}
	if newClusterClient == nil {
		return errors.New("error in building k8s client for remote cluster")
	}

	err = impl.CreateBearerToken(ctx, newClusterClient)
	if err != nil {
		impl.logger.Error("error in creating bearer token secret", "name", name, "err", err)
		return err
	}

	token, err := impl.GetBearerTokenFromSecret(ctx, newClusterClient)
	if err != nil {
		impl.logger.Error("error in getting bearer token from secret", "name", name, "err", err)
		return err
	}

	cluster := &bean2.ClusterBean{
		ClusterName: *eksCluster.Cluster.Name,
		ServerUrl:   *eksCluster.Cluster.Endpoint,
		Active:      true,
		IsProd:      false,
		Config: map[string]string{
			commonBean.BearerToken:              token,
			commonBean.CertificateAuthorityData: string(caData),
		},
	}

	dbConnection := impl.infrastructureDeploymentRepository.GetConnection()
	tx, err := dbConnection.Begin()
	if err != nil {
		impl.logger.Errorw("error in tx begin", "err", err)
		return err
	}
	defer func() {
		err = tx.Rollback()
		if err != nil {
			impl.logger.Errorw("error in tx rollback", "err", err)
			return
		}
	}()

	cluster, err = impl.clusterService.Save(ctx, tx, cluster, installation.RequestedBy)
	if err != nil && err.Error() != "cluster already exists" {
		impl.logger.Errorw("error in saving cluster", "name", name, "err", err)
		return err
	}

	result, err := impl.infrastructureDeploymentRepository.UpdateInstalledEntityIdIfNULL(tx, cluster.Id, installation.InstallationId)
	if err != nil {
		impl.logger.Errorw("error in updating installed entity", "clusterId", cluster.Id, "installationId", installation.InstallationId, "cluster", name, "err", err)
		return err
	}

	if result.RowsAffected() > 0 {
		err = tx.Commit()
		if err != nil {
			impl.logger.Errorw("error in commiting tx", "err", err)
			return err
		}
	}

	return err
}

func (impl *EKSClusterInstaller) getEksClusterData(awsAccessKeyId, awsSecretAccessKey, region string, name string) (*eks.DescribeClusterOutput, error) {

	eksClient, err := impl.getEksClient(awsAccessKeyId, awsSecretAccessKey, region, name)
	if err != nil {
		return nil, err
	}

	cluster, err := eksClient.DescribeCluster(&eks.DescribeClusterInput{Name: &name})
	if err != nil {
		impl.logger.Errorw("error in describing cluster", "name", name, "err", err)
		return nil, err
	}

	return cluster, nil
}

func (impl *EKSClusterInstaller) CreateBearerToken(ctx context.Context, newClusterClient *kubernetes.Clientset) error {

	// if any of the below call fails with error IsAlreadyExists
	//then we ignore the error because in that case we will use the already created token
	err := impl.CreateClusterRoleBinding(ctx, newClusterClient)
	if err != nil && !errors3.IsAlreadyExists(err) {
		impl.logger.Errorw("error in creating cluster role binding", "err", err)
		return err
	}

	err = impl.createServiceAccount(newClusterClient)
	if err != nil && !errors3.IsAlreadyExists(err) {
		impl.logger.Errorw("error in creating service account", "err", err)
		return err
	}

	err = impl.CreateBearerTokenSecretForServiceAccount(newClusterClient)
	if err != nil && !errors3.IsAlreadyExists(err) {
		impl.logger.Errorw("error in creating bearer token secret", "err", err)
		return err
	}

	return nil
}

func (impl *EKSClusterInstaller) getAWSAccessKeyIdAndSecretKey(secretName string) (string, string, error) {

	config, err := impl.k8sService.GetK8sInClusterRestConfig()
	if err != nil {
		impl.logger.Errorw("error in getting k8s config", "err", err)
		return "", "", err
	}

	k8sClient, err := impl.k8sService.GetCoreV1ClientByRestConfig(config)
	if err != nil {
		impl.logger.Errorw("error in getting k8s client", "err", err)
		return "", "", err
	}

	awsCredsSecret, err := impl.k8sService.GetSecret(enums.FluxSystemNs, secretName, k8sClient)
	if err != nil {
		impl.logger.Errorw("error in getting secret", "namespace", enums.FluxSystemNs, "secretName", secretName, "err", err)
		return "", "", err
	}

	awsAccessKeyId := string(awsCredsSecret.Data["AWS_ACCESS_KEY_ID"])
	awsSecretAccessKey := string(awsCredsSecret.Data["AWS_SECRET_ACCESS_KEY"])
	return awsAccessKeyId, awsSecretAccessKey, nil
}

func (impl *EKSClusterInstaller) getK8sClientForEKSCluster(ctx context.Context, cluster *eks.DescribeClusterOutput, caData []byte, awsAccessKeyId, awsSecretAccessKey, region string) (*kubernetes.Clientset, error) {

	clusterName := *cluster.Cluster.Name

	token, err := getEKSToken(ctx, clusterName, awsAccessKeyId, awsSecretAccessKey, region)
	if err != nil {
		impl.logger.Errorw("error in getting eks token", "clusterName", clusterName, "err", err)
		return nil, err
	}

	restConfig := &rest.Config{
		Host: *cluster.Cluster.Endpoint,
		TLSClientConfig: rest.TLSClientConfig{
			CAData: caData,
		},
		BearerToken: token,
	}

	newClusterClient, err := kubernetes.NewForConfig(restConfig)
	if err != nil {
		impl.logger.Errorw("error in creating remote cluster client", "err", err)
		return nil, err
	}
	return newClusterClient, nil
}

func (impl *EKSClusterInstaller) getEksClient(awsAccessKeyId string, awsSecretAccessKey string, region string, name string) (*eks.EKS, error) {
	var creds *credentials.Credentials
	creds = credentials.NewStaticCredentials(awsAccessKeyId, awsSecretAccessKey, "")

	sess, err := session.NewSession(&aws.Config{
		Region:      &region,
		Credentials: creds,
	})
	if err != nil {
		impl.logger.Errorw("error in creating session for extracting bearer token", "name", name, "region", region, "err", err)
		return nil, err
	}

	eksClient := eks.New(sess)
	return eksClient, nil
}

func (impl *EKSClusterInstaller) CreateClusterRoleBinding(ctx context.Context, newClusterClient *kubernetes.Clientset) error {
	clusterRoleBinding := &rbac.ClusterRoleBinding{
		ObjectMeta: v2.ObjectMeta{
			Name:      enums.ClusterRoleBindingNameForBearerToken,
			Namespace: enums.BearerTokenConfigApplyNs,
		},
		Subjects: []rbac.Subject{
			{
				Kind:      "ServiceAccount",
				Name:      enums.ServiceAccountNameForBearerToken,
				Namespace: enums.BearerTokenConfigApplyNs,
			},
		},
		RoleRef: rbac.RoleRef{
			APIGroup: "rbac.authorization.k8s.io",
			Kind:     "ClusterRole",
			Name:     "cluster-admin",
		},
	}
	_, err := newClusterClient.RbacV1().ClusterRoleBindings().Create(ctx, clusterRoleBinding, v2.CreateOptions{})
	if err != nil {
		impl.logger.Errorw("error in creating cluster role binding", "err", err)
		return err
	}
	return nil
}

func (impl *EKSClusterInstaller) createServiceAccount(newClusterClient *kubernetes.Clientset) error {
	serviceAccount := &v1.ServiceAccount{
		ObjectMeta: v2.ObjectMeta{
			Name: enums.ServiceAccountNameForBearerToken,
		},
	}
	_, err := newClusterClient.CoreV1().ServiceAccounts(enums.BearerTokenConfigApplyNs).Create(context.Background(), serviceAccount, v2.CreateOptions{})
	if err != nil {
		impl.logger.Errorw("error in creating service account", "err", err)
		return err
	}
	return nil
}

func (impl *EKSClusterInstaller) CreateBearerTokenSecretForServiceAccount(newClusterClient *kubernetes.Clientset) error {
	secret := &v1.Secret{
		ObjectMeta: v2.ObjectMeta{
			Name: enums.BearerTokenSecretName,
			Annotations: map[string]string{
				"kubernetes.io/service-account.name": enums.ServiceAccountNameForBearerToken,
			},
		},
		Type: v1.SecretTypeServiceAccountToken,
	}
	_, err := newClusterClient.CoreV1().Secrets(enums.BearerTokenConfigApplyNs).Create(context.Background(), secret, v2.CreateOptions{})
	if err != nil {
		impl.logger.Errorw("error in creating secret", "secretName", enums.BearerTokenSecretName, "err", err)
		return err
	}
	return nil
}

func (impl *EKSClusterInstaller) GetBearerTokenFromSecret(ctx context.Context, newClusterClient *kubernetes.Clientset) (string, error) {
	secret, err := newClusterClient.CoreV1().Secrets(enums.BearerTokenConfigApplyNs).Get(ctx, enums.BearerTokenSecretName, v2.GetOptions{})
	if err != nil {
		impl.logger.Errorw("error in getting secret", "name", enums.BearerTokenSecretName, "err", err)
		return "", err
	}
	if len(secret.Data) == 0 {
		return "", errors.New("empty secret found while extracting bearer token")
	}
	token := string(secret.Data["token"])
	if token == "" {
		return "", errors.New("no token found in secret")
	}
	return token, nil
}

func (impl *EKSClusterInstaller) GetInstallationName(chartValues string) (string, error) {
	var values map[string]interface{}
	err := yaml.Unmarshal([]byte(chartValues), &values)
	if err != nil {
		impl.logger.Errorw("error in unmarshaling chart values", "err", err)
		return "", err
	}
	return values["name"].(string), nil
}

func GetInstallationRegion(chartValues string) (string, error) {
	var values map[string]interface{}
	err := yaml.Unmarshal([]byte(chartValues), &values)
	if err != nil {
		return "", err
	}
	return values["region"].(string), nil
}

func (impl *EKSClusterInstaller) GetInstalledEntityType() enums.InstalledEntityType {
	return enums.InstalledEntityTypeCluster
}

func (impl *EKSClusterInstaller) PostDeleteSuccessfulOperations(ctx context.Context, installation *bean.Installation) error {

	err := impl.deleteService.DeleteCluster(&bean2.DeleteClusterBean{Id: installation.InstalledEntityId}, installation.RequestedBy)
	if err != nil && !errors.Is(err, pg.ErrNoRows) {
		impl.logger.Errorw("error in deleting cluster", "deleteClusterId", installation.InstalledEntityId, "err", err)
		return err
	}

	return nil
}
