package eventHandler

import (
	"context"
	"encoding/json"
	"errors"
	k8sUtil "github.com/devtron-labs/common-lib/utils/k8s"
	"github.com/devtron-labs/devtron/internal/util"
	bean2 "github.com/devtron-labs/devtron/pkg/InfrastructureInstallationService/bean"
	"github.com/devtron-labs/devtron/pkg/InfrastructureInstallationService/enums"
	bean3 "github.com/devtron-labs/devtron/pkg/InfrastructureInstallationService/events/bean"
	"github.com/devtron-labs/devtron/pkg/InfrastructureInstallationService/installer"
	repository2 "github.com/devtron-labs/devtron/pkg/InfrastructureInstallationService/repository"
	appStoreBean "github.com/devtron-labs/devtron/pkg/appStore/bean"
	"github.com/devtron-labs/devtron/pkg/appStore/installedApp/service/EAMode/deployment"
	"github.com/devtron-labs/devtron/pkg/cluster/bean"
	helper2 "github.com/devtron-labs/devtron/pkg/k8s/helper"
	"github.com/go-pg/pg"
	"go.uber.org/zap"
	"k8s.io/apimachinery/pkg/types"
	"net/http"
	"strings"
)

type InstallationEventHandler interface {
	HandlePostInstallSuccessEvent(ctx context.Context, installation *bean2.Installation) error
	HandlePostDeleteSuccessEvent(ctx context.Context, installation *bean2.Installation) error
	HandleInfrastructureHelmReleaseEvent(ctx context.Context, InfrastructureChartDeploymentEvent *bean3.InfrastructureChartDeploymentEvent) error
}

type InstallationEventHandlerImpl struct {
	logger                                       *zap.SugaredLogger
	infrastructureInstallationRepository         repository2.InfrastructureInstallationRepository
	installerFactory                             installer.InstallerFactory
	eaModeDeploymentService                      deployment.EAModeDeploymentService
	infrastructureInstallationVersionsRepository repository2.InfrastructureInstallationVersionsRepository
	k8sService                                   k8sUtil.K8sService
}

func NewInstallationEventHandlerImpl(
	logger *zap.SugaredLogger,
	infrastructureDeploymentRepository repository2.InfrastructureInstallationRepository,
	installerFactory installer.InstallerFactory,
	eaModeDeploymentService deployment.EAModeDeploymentService,
	infrastructureInstallationVersionsRepository repository2.InfrastructureInstallationVersionsRepository,
	k8sService k8sUtil.K8sService,
) (*InstallationEventHandlerImpl, error) {

	impl := &InstallationEventHandlerImpl{
		infrastructureInstallationRepository: infrastructureDeploymentRepository,
		logger:                               logger,
		installerFactory:                     installerFactory,
		eaModeDeploymentService:              eaModeDeploymentService,
		infrastructureInstallationVersionsRepository: infrastructureInstallationVersionsRepository,
		k8sService: k8sService,
	}

	return impl, nil
}

func (impl *InstallationEventHandlerImpl) HandlePostInstallSuccessEvent(ctx context.Context, installation *bean2.Installation) error {
	installer, err := impl.installerFactory.GetInstaller(installation.InstallationType)
	if err != nil {
		impl.logger.Errorw("error getting installer", "installation", installation.InstallationId, "error", err)
		return err
	}
	err = installer.PostInstallSuccessfulOperations(ctx, installation)
	if err != nil {
		impl.logger.Errorw("error posting installation", "installation", installation.InstallationId, "error", err)
		return err
	}
	return nil
}

func (impl *InstallationEventHandlerImpl) HandlePostDeleteSuccessEvent(ctx context.Context, installation *bean2.Installation) error {
	err := impl.DeleteFromDBAndHelmRelease(ctx, installation.InstallationId)
	if err != nil {
		impl.logger.Errorw("error deleting installation from db and helm release", "installationId", installation.InstallationId, "error", err)
		return err
	}
	installer, err := impl.installerFactory.GetInstaller(installation.InstallationType)
	if err != nil {
		impl.logger.Errorw("error getting installer", "installationId", installation.InstallationId, "error", err)
		return err
	}
	err = installer.PostDeleteSuccessfulOperations(ctx, installation)
	if err != nil {
		impl.logger.Errorw("error posting installation", "installationId", installation.InstallationId, "error", err)
		return err
	}
	return nil
}

func (impl *InstallationEventHandlerImpl) DeleteFromDBAndHelmRelease(ctx context.Context, installationId int) error {
	// update db entries to active=false

	infrastructureInstallation, err := impl.infrastructureInstallationRepository.GetById(installationId)
	if err != nil {
		if errors.Is(err, pg.ErrNoRows) {
			return &util.ApiError{
				HttpStatusCode:  http.StatusNotFound,
				InternalMessage: "installation not found",
			}
		}
		return err
	}

	dbConnection := impl.infrastructureInstallationRepository.GetConnection()
	tx, err := dbConnection.Begin()
	if err != nil {
		return err
	}

	err = impl.infrastructureInstallationRepository.Delete(tx, installationId)
	if err != nil {
		impl.logger.Errorw("error updating installation to active false", "installationId", installationId, "err", err)
		return err
	}

	err = impl.infrastructureInstallationVersionsRepository.MarkVersionsInactiveByInstallationId(tx, installationId)
	if err != nil {
		impl.logger.Errorw("error in marking existing versions inactive", "installationId", installationId, "err", err)
		return err
	}

	//delete helm app
	releaseDeleteRequest := &appStoreBean.InstallAppVersionDTO{
		AppName:   infrastructureInstallation.GetHelmReleaseName(),
		ClusterId: bean.DefaultClusterId,
		Namespace: enums.FluxSystemNs,
	}

	err = impl.eaModeDeploymentService.DeleteInstalledApp(ctx, "", "", releaseDeleteRequest, nil, tx)
	if err != nil {
		impl.logger.Errorw("error in deleting installation", "installationId", installationId, "err", err)
		return err
	}

	err = tx.Commit()
	if err != nil {
		return err
	}

	return nil
}

func (impl *InstallationEventHandlerImpl) HandleInfrastructureHelmReleaseEvent(ctx context.Context, infrastructureChartDeploymentEvent *bean3.InfrastructureChartDeploymentEvent) error {

	_, err := impl.infrastructureInstallationVersionsRepository.GetById(infrastructureChartDeploymentEvent.InfrastructureInstallationVersionId)
	if err != nil {
		// if there is error in getting version then this means that event is invalid
		impl.logger.Errorw("error in getting entry for requested version", "installationId", infrastructureChartDeploymentEvent.InfrastructureInstallationVersionId, "error", err)
		return err
	}

	infrastructureInstallationId := infrastructureChartDeploymentEvent.InfrastructureInstallationId

	versionsInApplyingState, err := impl.infrastructureInstallationVersionsRepository.FindByIdAndApplyStatus(infrastructureInstallationId, enums.ApplyStatusApplying)
	if err != nil {
		impl.logger.Errorw("error getting installation versions", "installationId", infrastructureInstallationId, "error", err)
		return err
	}
	if len(versionsInApplyingState) > 0 {
		return nil
	}

	//There is only one active in db at a particular time which means that latest version has active=true (this is ensured by marking older versions as inactive during install/upgrade/delete request)
	res, err := impl.infrastructureInstallationVersionsRepository.UpdateActiveVersionApplyStatus(infrastructureInstallationId, enums.ApplyStatusPending, enums.ApplyStatusApplying, infrastructureChartDeploymentEvent.RequestedBy)
	if err != nil {
		impl.logger.Errorw("error in updating status of latest version")
		return err
	}
	if res.RowsAffected() == 0 {
		impl.logger.Warnw("version already picked up by another worker", "installationId", infrastructureInstallationId)
		return errors.New("installation already picked by another worker")
	}

	versionsInApplyingState, err = impl.infrastructureInstallationVersionsRepository.FindByIdAndApplyStatus(infrastructureInstallationId, enums.ApplyStatusApplying)
	if err != nil {
		impl.logger.Errorw("error getting installation versions", "installationId", infrastructureInstallationId, "error", err)
		return err
	}
	var version *repository2.InfrastructureInstallationVersions
	for _, v := range versionsInApplyingState {
		version = v
		break

	}
	if version == nil {
		return errors.New("no installation version found in active state")
	}

	switch version.Action {
	case enums.ProvisionActionInstall, enums.ProvisionActionUpdate:
		err = impl.InstallOrUpgradeRelease(ctx, version)
		if err != nil {
			impl.logger.Errorw("error installing version", "installationId", infrastructureInstallationId, "error", err)
			err1 := impl.UpdateApplyStatus(version, enums.ApplyStatusFailed, err.Error())
			if err1 != nil {
				impl.logger.Errorw("error in updating apply status in install flow", "installationId", infrastructureInstallationId, "error", err1)
			}
			return err
		}
	case enums.ProvisionActionDelete:
		err = impl.patchAndDeleteTerraformObject(ctx, version)
		if err != nil {
			impl.logger.Errorw("error in patching terraform object", "installationId", infrastructureInstallationId, "err", err)
			err1 := impl.UpdateApplyStatus(version, enums.ApplyStatusFailed, err.Error())
			if err1 != nil {
				impl.logger.Errorw("error in updating apply status in install flow", "installationId", infrastructureInstallationId, "error", err1)
			}
			return err
		}
	}

	err1 := impl.UpdateApplyStatus(version, enums.ApplyStatusSucceeded, "Terraform configurations applied")
	if err1 != nil {
		impl.logger.Errorw("error in updating apply status in install flow", "installationId", infrastructureInstallationId, "error", err1)
		return err1
	}

	return nil
}

func (impl *InstallationEventHandlerImpl) InstallOrUpgradeRelease(ctx context.Context, version *repository2.InfrastructureInstallationVersions) error {
	installationConfig, err := repository2.UnmarshalInstallationConfig(version.InstallationConfig)
	if err != nil {
		impl.logger.Errorw("error getting installation config", "installationId", version.InfrastructureInstallationId, "error", err)
		return err
	}
	installAppVersionRequest := &appStoreBean.InstallAppVersionDTO{
		AppName:            version.GetHelmReleaseName(),
		AppStoreVersion:    installationConfig.AppStoreApplicationVersionId,
		DeploymentAppType:  util.PIPELINE_DEPLOYMENT_TYPE_HELM,
		ClusterId:          bean.DefaultClusterId,
		Namespace:          enums.FluxSystemNs,
		ValuesOverrideYaml: installationConfig.HelmValues,
	}
	switch version.Action {
	case enums.ProvisionActionInstall:
		_, err = impl.eaModeDeploymentService.InstallApp(installAppVersionRequest, nil, ctx, nil)
		if err != nil && !strings.Contains(err.Error(), "release already exists") {
			impl.logger.Errorw("error in installing cluster chart", "name", installAppVersionRequest.AppName, "err", err)
			return err
		}
	case enums.ProvisionActionUpdate:
		err := impl.eaModeDeploymentService.UpgradeDeployment(installAppVersionRequest, nil, 0, ctx)
		if err != nil {
			impl.logger.Errorw("error in updating installation config", "installationId", version.InfrastructureInstallationId, "err", err)
			return err
		}
	}
	return nil
}

func (impl *InstallationEventHandlerImpl) patchAndDeleteTerraformObject(ctx context.Context, infrastructureInstallationVersionModel *repository2.InfrastructureInstallationVersions) error {

	patchMap := map[string]interface{}{
		"spec": map[string]interface{}{
			"destroyResourcesOnDeletion": true,
		},
	}

	patchJson, err := json.Marshal(patchMap)
	if err != nil {
		return err
	}

	restConfig, err := impl.k8sService.GetK8sInClusterRestConfig()
	if err != nil {
		impl.logger.Errorw("error in getting k8s config", "err", err)
		return err
	}

	namespace := enums.FluxSystemNs

	gvr, err := impl.k8sService.GetGVRForCRD(restConfig, bean2.TerraformCRDName)
	if err != nil {
		impl.logger.Errorw("error in getting flux GVR", "err", err)
		return err
	}

	terraformResourceName := infrastructureInstallationVersionModel.GetHelmReleaseName()
	//terraform object name will be equal to helmReleaseName

	patchType := types.MergePatchType
	_, err = impl.k8sService.PatchResourceByGVR(ctx, restConfig, gvr, terraformResourceName, enums.FluxSystemNs, patchType, patchJson)
	if err != nil && !helper2.IsResourceNotFoundErr(err) {
		impl.logger.Errorw("error in getting resource manifest", "namespace", enums.FluxSystemNs, "name", terraformResourceName, "err", err)
		return err
	}

	err = impl.k8sService.DeleteResourceByGVR(ctx, restConfig, gvr, terraformResourceName, namespace, false)
	if err != nil && !helper2.IsResourceNotFoundErr(err) {
		impl.logger.Errorw("error in deleting terraform resource", "namespace", enums.FluxSystemNs, "terraformResourceName", terraformResourceName, "err", err)
		return err
	}

	return nil
}

func (impl *InstallationEventHandlerImpl) UpdateApplyStatus(version *repository2.InfrastructureInstallationVersions, applyStatus enums.ApplyStatus, applyStatusMessage string) error {
	version.ApplyStatus = applyStatus
	version.ApplyStatusMessage = applyStatusMessage
	version.UpdateAuditLog(version.CreatedBy)
	_, err := impl.infrastructureInstallationVersionsRepository.Update(version, nil)
	if err != nil {
		impl.logger.Errorw("error updating installation config apply status to failed ", "installationId", version.InfrastructureInstallationId, "error", err)
		return err
	}
	return nil
}
