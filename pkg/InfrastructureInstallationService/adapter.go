package InfrastructureInstallationService

import (
	"github.com/devtron-labs/devtron/pkg/InfrastructureInstallationService/bean"
	"github.com/devtron-labs/devtron/pkg/InfrastructureInstallationService/enums"
	"github.com/devtron-labs/devtron/pkg/InfrastructureInstallationService/repository"
)

func ConvertToInstallation(infrastructureInstallationVersion *repository.InfrastructureInstallationVersions) (*bean.Installation, error) {
	installationConfig, err := repository.UnmarshalInstallationConfig(infrastructureInstallationVersion.InstallationConfig)
	if err != nil {
		return nil, err
	}
	infrastructureDeployment := infrastructureInstallationVersion.InfrastructureInstallation
	installation := &bean.Installation{
		InstallationId:    infrastructureDeployment.Id,
		Name:              infrastructureDeployment.InstallationName,
		InstallationType:  infrastructureDeployment.InstallationType,
		RequestedBy:       infrastructureDeployment.CreatedBy,
		InstalledEntityId: infrastructureDeployment.InstalledEntityId,
		CreatedOn:         infrastructureDeployment.CreatedOn,
	}
	if installationConfig != nil {
		installation.Values = installationConfig.HelmValues
		installation.ValuesSchema = installationConfig.HelmValuesSchema
	}
	return installation, nil
}

func ConvertToUIStatus(installationStatus enums.InstallationStatus, action enums.ProvisionAction) enums.InstallationStatus {
	if installationStatus == enums.InstallationStatusManifestNotFound {
		switch action {
		case enums.ProvisionActionInstall:
			return enums.InstallationStatusCreating
		case enums.ProvisionActionDelete:
			return enums.InstallationStatusDeleted
		}
	} else if installationStatus == enums.TerraformStatusProcessing {
		//UI logic for InstallationStatusProcessing
		switch action {
		case enums.ProvisionActionInstall:
			installationStatus = enums.InstallationStatusCreating
		case enums.ProvisionActionUpdate:
			installationStatus = enums.InstallationStatusUpdating
		case enums.ProvisionActionDelete:
			installationStatus = enums.InstallationStatusDeleting
		}
	} else if installationStatus == enums.TerraformStatusSucceeded {
		switch action {
		case enums.ProvisionActionInstall:
			installationStatus = enums.InstallationStatusInstalled
		case enums.ProvisionActionUpdate:
			installationStatus = enums.InstallationStatusUpdated
		case enums.ProvisionActionDelete:
			installationStatus = enums.InstallationStatusDeleted
		}
	}
	return installationStatus
}
