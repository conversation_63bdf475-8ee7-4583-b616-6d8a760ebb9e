package configDiff

import (
	"context"
	"github.com/devtron-labs/devtron/enterprise/pkg/drafts/bean"
	"github.com/devtron-labs/devtron/pkg/bean/configMapBean"
	bean2 "github.com/devtron-labs/devtron/pkg/config/configDiff/bean"
	"github.com/devtron-labs/devtron/pkg/config/configDiff/helper"
	"github.com/devtron-labs/devtron/pkg/config/configDiff/utils"
	"github.com/devtron-labs/devtron/pkg/resourceQualifiers"
	util2 "github.com/devtron-labs/devtron/util"
)

func (impl *DeploymentConfigurationServiceImpl) getSecretDataForDraftOnly(ctx context.Context, appEnvAndClusterMetadata *bean2.AppEnvAndClusterMetadata, userId int32) (*bean2.SecretConfigMetadata, error) {
	secretDraftVersions, err := impl.configDraftService.GetDraftByAppEnvIdAndResourceType(ctx, appEnvAndClusterMetadata.AppId, appEnvAndClusterMetadata.EnvId, userId, bean.CSDraftResource)
	if err != nil {
		impl.logger.Errorw("error in getting drafts by appId and envId", "appEnvAndClusterMetadata", appEnvAndClusterMetadata, "err", err)
		return nil, err
	}
	unresolvedSecretDraftConfigDataSlice := make([]*configMapBean.ConfigData, 0, len(secretDraftVersions))
	resolvedSecretDraftConfigDataSlice := make([]*configMapBean.ConfigData, 0, len(secretDraftVersions))
	variableSnapshot := make(map[string]map[string]string)
	unresolvedSecretDraftConfigData, err := helper.GetConfigDataDtoFromDraftVersions(secretDraftVersions)
	if err != nil {
		impl.logger.Errorw("error in getting config data dto from secrets draft data  ", "appEnvAndClusterMetadata", appEnvAndClusterMetadata, "err", err)
		return nil, err
	}
	unresolvedSecretDraftConfigDataSlice = append(unresolvedSecretDraftConfigDataSlice, unresolvedSecretDraftConfigData...)
	resolvedSecretDraftConfigData, secretsVarSnapShot, err := impl.getResolvedDraftDataWithVariableSnapshot(ctx, secretDraftVersions, appEnvAndClusterMetadata)
	if err != nil {
		impl.logger.Errorw("error in getting resolved draft data and variable snapshot", "appEnvAndClusterMetadata", appEnvAndClusterMetadata, "err", err)
		return nil, err
	}
	resolvedSecretDraftConfigDataSlice = append(resolvedSecretDraftConfigDataSlice, resolvedSecretDraftConfigData...)
	variableSnapshot = secretsVarSnapShot

	return &bean2.SecretConfigMetadata{
		SecretsList: &configMapBean.SecretsList{ConfigData: unresolvedSecretDraftConfigDataSlice},
		SecretScopeVariableMetadata: &bean2.CmCsScopeVariableMetadata{
			ResolvedConfigData: resolvedSecretDraftConfigDataSlice,
			VariableSnapShot:   variableSnapshot,
		},
	}, nil
}

func (impl *DeploymentConfigurationServiceImpl) getResolvedDraftDataWithVariableSnapshot(ctx context.Context, secretDraftVersions []*bean.ConfigDraftResponse,
	appEnvAndClusterMetadata *bean2.AppEnvAndClusterMetadata) ([]*configMapBean.ConfigData, map[string]map[string]string, error) {

	variableSnapshot := make(map[string]map[string]string)
	resolvedSecretDraftConfigDataSlice := make([]*configMapBean.ConfigData, 0, len(secretDraftVersions))
	resolveTemplateVarRequest := helper.GetResolveTemplateVarRequest(appEnvAndClusterMetadata)
	for _, secretDraft := range secretDraftVersions {
		toResolveDecodedDraftData, err := impl.transformDraftSecretData(secretDraft.Data, util2.DecodeSecret)
		if err != nil {
			impl.logger.Errorw("error in decoding secret draft secret data", "appEnvAndClusterMetadata", appEnvAndClusterMetadata, "err", err)
			return nil, nil, err
		}
		resolvedTemplate, secretLevelSnapshot, err := impl.deploymentTemplateService.ResolveTemplateVariables(ctx, toResolveDecodedDraftData, resolveTemplateVarRequest)
		if err != nil {
			impl.logger.Errorw("error in getting resolved data for cm draft data ", "appEnvAndClusterMetadata", appEnvAndClusterMetadata, "err", err)
			return nil, nil, err
		}
		encodedSecret, err := impl.transformDraftSecretData(resolvedTemplate, util2.EncodeSecret)
		if err != nil {
			impl.logger.Errorw("error in decoding secret draft secret data", "appEnvAndClusterMetadata", appEnvAndClusterMetadata, "err", err)
			return nil, nil, err
		}
		if len(secretLevelSnapshot) > 0 {
			resolvedSecretConfigData, err := utils.ConvertToConfigDataDto(encodedSecret)
			if err != nil {
				impl.logger.Errorw("error in encoding secret draft secret data", "appEnvAndClusterMetadata", appEnvAndClusterMetadata, "err", err)
				return nil, nil, err
			}
			variableSnapshot[secretDraft.ResourceName] = secretLevelSnapshot
			resolvedSecretDraftConfigDataSlice = append(resolvedSecretDraftConfigDataSlice, resolvedSecretConfigData...)
		}
	}
	return resolvedSecretDraftConfigDataSlice, variableSnapshot, nil
}

func (impl *DeploymentConfigurationServiceImpl) getSecretDataForPublishedWithDraft(ctx context.Context, appEnvAndClusterMetadata *bean2.AppEnvAndClusterMetadata,
	systemMetadata *resourceQualifiers.SystemMetadata, userId int32) (*bean2.SecretConfigMetadata, error) {
	/*
		1. get draft only data
		2. get published only data
		3. if any resource name in published also exists in draft then  override the draft resource values in final response
	*/
	draftSecretConfigMetadata, err := impl.getSecretDataForDraftOnly(ctx, appEnvAndClusterMetadata, userId)
	if err != nil {
		impl.logger.Errorw("error in getting single secret data for draft only", "appEnvAndClusterMetadata", appEnvAndClusterMetadata, "err", err)
		return nil, err
	}
	publishedSecretConfigMetadata, err := impl.getSecretDataForPublishedOnly(ctx, appEnvAndClusterMetadata, systemMetadata)
	if err != nil {
		impl.logger.Errorw("error in getting single secret data for published only", "appEnvAndClusterMetadata", appEnvAndClusterMetadata, "err", err)
		return nil, err
	}
	mergedSecretConfig := helper.ReplaceCollidingDraftSecretsInPublished(publishedSecretConfigMetadata, draftSecretConfigMetadata)
	if err != nil {
		impl.logger.Errorw("error in replacing colliding draft secrets in published only secrets config list", "appEnvAndClusterMetadata", appEnvAndClusterMetadata, "err", err)
		return nil, err
	}
	return mergedSecretConfig, nil
}
