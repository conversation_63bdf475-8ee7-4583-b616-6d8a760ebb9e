/*
 * Copyright (c) 2024. Devtron Inc.
 */

package repository

import (
	"github.com/devtron-labs/devtron/internal/sql/repository/app"
	"github.com/devtron-labs/devtron/internal/sql/repository/chartConfig"
	"github.com/devtron-labs/devtron/internal/sql/repository/chartConfig/configMapRepository"
	api "github.com/devtron-labs/devtron/pkg/bulkAction/v1beta2/bulkEdit/bean"
	chartRepoRepository "github.com/devtron-labs/devtron/pkg/chartRepo/repository"
	"github.com/go-pg/pg"
	"github.com/go-pg/pg/orm"
)

type ChartRefMin struct {
	Id      int    `sql:"id,pk"`
	Version string `sql:"version,notnull"`
}

type ApplicationMin struct {
	Id       int    `sql:"id,pk"`
	AppName  string `sql:"app_name,notnull"`
	TeamName string `sql:"team_name,notnull"`
}

type EnvironmentMin struct {
	Id      int    `sql:"id,pk"`
	EnvName string `sql:"env_name,notnull"`
}

type BulkEditRepositoryEnt interface {
	FindAllImpactedApplicationsWithEnv(envIds []int) ([]*ApplicationMin, error)
	FindImpactedApplications(appNameIncludes, appNameExcludes, teamNameIncludes, teamNameExcludes []string) ([]*ApplicationMin, error)
	FindApplicationMinById(chartId int) (*ApplicationMin, error)
	FindApplicationMinByChartId(chartId int) (*ApplicationMin, error)
	FindImpactedEnvironments(envNameIncludes, envNameExcludes []string, envType api.EnvIdentifierType) ([]*EnvironmentMin, error)
	FindImpactedChartRefIds(chartName *string, isCustomChart *bool) ([]*ChartRefMin, error)

	FindImpactedAppsForEnvTemplates(appIds, chartRefIds []int, envId int) ([]*app.App, error)
	FindImpactedAppsForBaseTemplates(appIds, chartRefIds []int) ([]*app.App, error)
	FindImpactedCsForEnvConfig(appIds []int, envId int) ([]*configMapRepository.ConfigMapEnvModel, error)
	FindImpactedCsForBaseConfig(appIds []int) ([]*configMapRepository.ConfigMapAppModel, error)
	FindImpactedCmForEnvConfig(appIds []int, envId int) ([]*configMapRepository.ConfigMapEnvModel, error)
	FindImpactedCmForBaseConfig(appIds []int) ([]*configMapRepository.ConfigMapAppModel, error)

	FindBulkChartsByAppIds(appIds []int) ([]*chartRepoRepository.Chart, error)
	FindBulkChartsByAppIdsAndEnvId(appIds []int, envId int) ([]*chartConfig.EnvConfigOverride, error)
	FindBulkCmAppModelsByAppIds(appIds []int) ([]*configMapRepository.ConfigMapAppModel, error)
	FindBulkCmAppModelsByAppIdsAndEnvId(appIds []int, envId int) ([]*configMapRepository.ConfigMapEnvModel, error)
	FindBulkCsAppModelsByAppIds(appIds []int) ([]*configMapRepository.ConfigMapAppModel, error)
	FindBulkCsAppModelsByAppIdsAndEnvId(appIds []int, envId int) ([]*configMapRepository.ConfigMapEnvModel, error)
}

func (repositoryImpl BulkEditRepositoryImpl) FindImpactedApplications(appNameIncludes, appNameExcludes, teamNameIncludes, teamNameExcludes []string) ([]*ApplicationMin, error) {
	var applicationMins []*ApplicationMin
	q := repositoryImpl.dbConnection.
		Model().
		Table("app").
		ColumnExpr("app.id AS id").
		ColumnExpr("app.app_name AS app_name").
		ColumnExpr("team.name AS team_name").
		Join("INNER JOIN team ON app.team_id = team.id").
		Where("app.active = ?", true).
		Where("team.active = ?", true).
		Where("app.app_type = ?", 0)
	q = appendBuildAppNameQuery(q, appNameIncludes, appNameExcludes)
	q = appendBuildTeamNameQuery(q, teamNameIncludes, teamNameExcludes)
	err := q.Select(&applicationMins)
	return applicationMins, err
}

func (repositoryImpl BulkEditRepositoryImpl) FindAllImpactedApplicationsWithEnv(envIds []int) ([]*ApplicationMin, error) {
	var applicationMins []*ApplicationMin
	if len(envIds) == 0 {
		return applicationMins, nil // No environments provided, return empty slice
	}
	err := repositoryImpl.dbConnection.
		Model().
		Table("pipeline").
		ColumnExpr("app.id AS id").
		ColumnExpr("app.app_name AS app_name").
		ColumnExpr("team.name AS team_name").
		Join("INNER JOIN app ON app.id = pipeline.app_id").
		Join("INNER JOIN team ON app.team_id = team.id").
		Where("app.active = ?", true).
		Where("team.active = ?", true).
		Where("pipeline.deleted = ?", false).
		Where("pipeline.environment_id IN (?)", pg.In(envIds)).
		Order("app.app_name ASC").
		Group("app.id", "app.app_name", "team.name").
		Select(&applicationMins)
	return applicationMins, err
}

func (repositoryImpl BulkEditRepositoryImpl) FindApplicationMinById(chartId int) (*ApplicationMin, error) {
	applicationMin := &ApplicationMin{}
	err := repositoryImpl.dbConnection.
		Model().
		Table("app").
		ColumnExpr("app.id AS id").
		ColumnExpr("app.app_name AS app_name").
		ColumnExpr("team.name AS team_name").
		Join("INNER JOIN team ON app.team_id = team.id").
		Where("app.id = ?", chartId).
		Where("app.active = ?", true).
		Where("team.active = ?", true).
		Select(&applicationMin)
	return applicationMin, err
}

func (repositoryImpl BulkEditRepositoryImpl) FindApplicationMinByChartId(chartId int) (*ApplicationMin, error) {
	applicationMin := &ApplicationMin{}
	err := repositoryImpl.dbConnection.
		Model().
		Table("app").
		ColumnExpr("app.id AS id").
		ColumnExpr("app.app_name AS app_name").
		ColumnExpr("team.name AS team_name").
		Join("INNER JOIN charts ch ON app.id = ch.app_id").
		Join("INNER JOIN team ON app.team_id = team.id").
		Where("ch.id = ?", chartId).
		Where("app.active = ?", true).
		Where("team.active = ?", true).
		Where("ch.latest = ?", true).
		Select(&applicationMin)
	return applicationMin, err
}

func (repositoryImpl BulkEditRepositoryImpl) FindImpactedEnvironments(envNameIncludes, envNameExcludes []string, envType api.EnvIdentifierType) ([]*EnvironmentMin, error) {
	var ids []*EnvironmentMin
	q := repositoryImpl.dbConnection.
		Model().
		Table("environment").
		ColumnExpr("environment.id AS id").
		ColumnExpr("environment.environment_name AS env_name").
		Where("environment.active = ?", true)
	q = appendBuildEnvNameQuery(q, envNameIncludes, envNameExcludes)
	q = appendBuildEnvTypeQuery(q, envType)
	err := q.Select(&ids)
	return ids, err
}

func (repositoryImpl BulkEditRepositoryImpl) FindImpactedChartRefIds(chartName *string, isCustomChart *bool) ([]*ChartRefMin, error) {
	var chartRefMins []*ChartRefMin
	q := repositoryImpl.dbConnection.
		Model().
		Table("chart_ref").
		ColumnExpr("chart_ref.id AS id").
		ColumnExpr("chart_ref.version AS version").
		Where("chart_ref.active = ?", true)
	q = appendBuildChartNameQuery(q, chartName)
	q = appendBuildCustomChartQuery(q, isCustomChart)
	err := q.Select(&chartRefMins)
	return chartRefMins, err
}

func (repositoryImpl BulkEditRepositoryImpl) FindImpactedAppsForEnvTemplates(appIds, chartRefIds []int, envId int) ([]*app.App, error) {
	var apps []*app.App
	q := repositoryImpl.dbConnection.
		Model(&apps).Join("INNER JOIN charts ON app.id = charts.app_id").
		Join("INNER JOIN chart_env_config_override ON charts.id = chart_env_config_override.chart_id").
		Where("app.active = ?", true).
		Where("chart_env_config_override.target_environment = ? ", envId).
		Where("chart_env_config_override.latest = ?", true)
	q = appendBuildAppIdsQuery(q, appIds)
	q = appendBuildChartRefIdsQuery(q, chartRefIds)
	err := q.Select()
	return apps, err
}

func (repositoryImpl BulkEditRepositoryImpl) FindImpactedAppsForBaseTemplates(appIds, chartRefIds []int) ([]*app.App, error) {
	var apps []*app.App
	q := repositoryImpl.dbConnection.
		Model(&apps).Join("INNER JOIN charts ON app.id = charts.app_id").
		Where("app.active = ?", true).
		Where("charts.latest = ?", true)
	q = appendBuildAppIdsQuery(q, appIds)
	q = appendBuildChartRefIdsQuery(q, chartRefIds)
	err := q.Select()
	return apps, err
}

func (repositoryImpl BulkEditRepositoryImpl) FindImpactedCsForEnvConfig(appIds []int, envId int) ([]*configMapRepository.ConfigMapEnvModel, error) {
	var CmAndSecretEnvModel []*configMapRepository.ConfigMapEnvModel
	q := repositoryImpl.dbConnection.
		Model(&CmAndSecretEnvModel).Join("INNER JOIN app ON app.id = config_map_env_model.app_id").
		Where("app.active = ?", true).
		Where("config_map_env_model.environment_id = ? ", envId)
	q = appendBuildAppIdsQuery(q, appIds)
	err := q.Select()
	return CmAndSecretEnvModel, err
}

func (repositoryImpl BulkEditRepositoryImpl) FindImpactedCsForBaseConfig(appIds []int) ([]*configMapRepository.ConfigMapAppModel, error) {
	var CmAndSecretAppModel []*configMapRepository.ConfigMapAppModel
	q := repositoryImpl.dbConnection.
		Model(&CmAndSecretAppModel).Join("INNER JOIN app ON app.id = config_map_app_model.app_id").
		Where("app.active = ?", true)
	q = appendBuildAppIdsQuery(q, appIds)
	err := q.Select()
	return CmAndSecretAppModel, err
}

func (repositoryImpl BulkEditRepositoryImpl) FindImpactedCmForEnvConfig(appIds []int, envId int) ([]*configMapRepository.ConfigMapEnvModel, error) {
	var CmAndSecretEnvModel []*configMapRepository.ConfigMapEnvModel
	q := repositoryImpl.dbConnection.
		Model(&CmAndSecretEnvModel).Join("INNER JOIN app ON app.id = config_map_env_model.app_id").
		Where("app.active = ?", true).
		Where("config_map_env_model.environment_id = ? ", envId)
	q = appendBuildAppIdsQuery(q, appIds)
	err := q.Select()
	return CmAndSecretEnvModel, err
}

func (repositoryImpl BulkEditRepositoryImpl) FindImpactedCmForBaseConfig(appIds []int) ([]*configMapRepository.ConfigMapAppModel, error) {
	var CmAndSecretAppModel []*configMapRepository.ConfigMapAppModel
	q := repositoryImpl.dbConnection.
		Model(&CmAndSecretAppModel).Join("INNER JOIN app ON app.id = config_map_app_model.app_id").
		Where("app.active = ?", true)
	q = appendBuildAppIdsQuery(q, appIds)
	err := q.Select()
	return CmAndSecretAppModel, err
}

func (repositoryImpl BulkEditRepositoryImpl) FindBulkChartsByAppIds(appIds []int) ([]*chartRepoRepository.Chart, error) {
	var charts []*chartRepoRepository.Chart
	q := repositoryImpl.dbConnection.
		Model(&charts).
		// select every column expect for `reference_chart`
		Column("chart.id").Column("chart.app_id").Column("chart.chart_repo_id").Column("chart.chart_name").
		Column("chart.chart_version").Column("chart.chart_repo").Column("chart.chart_repo_url").Column("chart.values_yaml").
		Column("chart.global_override").Column("chart.release_override").Column("chart.pipeline_override").
		Column("chart.status").Column("chart.active").Column("chart.git_repo_url").Column("chart.chart_location").
		Column("chart.reference_template").Column("chart.image_descriptor_template").Column("chart.chart_ref_id").
		Column("chart.latest").Column("chart.previous").Column("chart.is_basic_view_locked").
		Column("chart.current_view_editor").Column("chart.is_custom_repository").Column("chart.created_on").
		Column("chart.created_by").Column("chart.updated_on").Column("chart.updated_by").
		Join("INNER JOIN app ON app.id = app_id").
		Where("app.active = ?", true).
		Where("latest = ?", true)
	q = appendBuildAppIdsQuery(q, appIds)
	err := q.Select()
	return charts, err
}

func (repositoryImpl BulkEditRepositoryImpl) FindBulkChartsByAppIdsAndEnvId(appIds []int, envId int) ([]*chartConfig.EnvConfigOverride, error) {
	var charts []*chartConfig.EnvConfigOverride
	q := repositoryImpl.dbConnection.
		Model().
		TableExpr("chart_env_config_override AS env_config_override").
		Column("env_config_override.*").
		ColumnExpr("chart.id AS chart__id").
		ColumnExpr("chart.app_id AS chart__app_id").
		ColumnExpr("chart.chart_name AS chart__chart_name").
		ColumnExpr("chart.chart_version AS chart__chart_version").
		Join("INNER JOIN charts AS chart ON env_config_override.chart_id = chart.id").
		Join("INNER JOIN app ON app.id = chart.app_id").
		Where("app.active = ?", true).
		Where("env_config_override.target_environment = ?", envId).
		Where("env_config_override.latest = ?", true)
	q = appendBuildAppIdsQuery(q, appIds)
	err := q.Select(&charts)
	return charts, err
}

func (repositoryImpl BulkEditRepositoryImpl) FindBulkCmAppModelsByAppIds(appIds []int) ([]*configMapRepository.ConfigMapAppModel, error) {
	var CmAndSecretAppModel []*configMapRepository.ConfigMapAppModel
	q := repositoryImpl.dbConnection.
		Model(&CmAndSecretAppModel).Join("INNER JOIN app ON app.id = config_map_app_model.app_id").
		Where("app.active = ?", true)
	q = appendBuildAppIdsQuery(q, appIds)
	err := q.Select()
	return CmAndSecretAppModel, err
}

func (repositoryImpl BulkEditRepositoryImpl) FindBulkCmAppModelsByAppIdsAndEnvId(appIds []int, envId int) ([]*configMapRepository.ConfigMapEnvModel, error) {
	var CmAndSecretEnvModel []*configMapRepository.ConfigMapEnvModel
	q := repositoryImpl.dbConnection.
		Model(&CmAndSecretEnvModel).Join("INNER JOIN app ON app.id = config_map_env_model.app_id").
		Where("app.active = ?", true).
		Where("config_map_env_model.environment_id = ? ", envId)
	q = appendBuildAppIdsQuery(q, appIds)
	err := q.Select()
	return CmAndSecretEnvModel, err
}

func (repositoryImpl BulkEditRepositoryImpl) FindBulkCsAppModelsByAppIds(appIds []int) ([]*configMapRepository.ConfigMapAppModel, error) {
	var CmAndSecretAppModel []*configMapRepository.ConfigMapAppModel
	q := repositoryImpl.dbConnection.
		Model(&CmAndSecretAppModel).Join("INNER JOIN app ON app.id = config_map_app_model.app_id").
		Where("app.active = ?", true)
	q = appendBuildAppIdsQuery(q, appIds)
	err := q.Select()
	return CmAndSecretAppModel, err
}

func (repositoryImpl BulkEditRepositoryImpl) FindBulkCsAppModelsByAppIdsAndEnvId(appIds []int, envId int) ([]*configMapRepository.ConfigMapEnvModel, error) {
	var CmAndSecretEnvModel []*configMapRepository.ConfigMapEnvModel
	q := repositoryImpl.dbConnection.
		Model(&CmAndSecretEnvModel).Join("INNER JOIN app ON app.id = config_map_env_model.app_id").
		Where("app.active = ?", true).
		Where("config_map_env_model.environment_id = ? ", envId)
	q = appendBuildAppIdsQuery(q, appIds)
	err := q.Select()
	return CmAndSecretEnvModel, err
}

func appendBuildAppIdsQuery(q *orm.Query, appIds []int) *orm.Query {
	if len(appIds) != 0 {
		q = q.Where("app.id IN (?)", pg.In(appIds))
	}
	return q
}

func appendBuildChartRefIdsQuery(q *orm.Query, chartRefIds []int) *orm.Query {
	if len(chartRefIds) != 0 {
		q = q.Where("charts.chart_ref_id IN (?)", pg.In(chartRefIds))
	}
	return q
}

func appendBuildTeamNameQuery(q *orm.Query, teamNameIncludes, teamNameExcludes []string) *orm.Query {
	if len(teamNameIncludes) != 0 {
		q = q.Where("team.name LIKE ANY (array[?])", pg.In(teamNameIncludes))
	}
	if len(teamNameExcludes) != 0 {
		q = q.Where("team.name NOT LIKE ALL (array[?])", pg.In(teamNameExcludes))
	}
	return q
}

func appendBuildEnvNameQuery(q *orm.Query, envNameIncludes, envNameExcludes []string) *orm.Query {
	if len(envNameIncludes) != 0 {
		q = q.Where("environment.environment_name LIKE ANY (array[?])", pg.In(envNameIncludes))
	}
	if len(envNameExcludes) != 0 {
		q = q.Where("environment.environment_name NOT LIKE ALL (array[?])", pg.In(envNameExcludes))
	}
	return q
}

func appendBuildChartNameQuery(q *orm.Query, chartName *string) *orm.Query {
	if chartName != nil {
		q = q.Where("chart_ref.name = ?", chartName)
	}
	return q
}

func appendBuildEnvTypeQuery(q *orm.Query, envType api.EnvIdentifierType) *orm.Query {
	switch envType {
	case api.Prod:
		return q.Where("environment.default = ?", true)
	case api.NonProd:
		return q.WhereGroup(
			func(q *orm.Query) (*orm.Query, error) {
				q = q.WhereOr("environment.default = ?", false)
				q = q.WhereOr("environment.default IS NULL")
				return q, nil
			})
	default:
		return q
	}
}

func appendBuildCustomChartQuery(q *orm.Query, isCustomChart *bool) *orm.Query {
	if isCustomChart != nil {
		q = q.Where("chart_ref.user_uploaded = ?", *isCustomChart)
	}
	return q
}
