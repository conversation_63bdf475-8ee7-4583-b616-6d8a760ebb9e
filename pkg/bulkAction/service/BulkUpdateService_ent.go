/*
 * Copyright (c) 2024. Devtron Inc.
 */

package service

import (
	"context"
	"errors"
	"fmt"
	apiBean "github.com/devtron-labs/devtron/api/bean"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/internal/util"
	userBean "github.com/devtron-labs/devtron/pkg/auth/user/bean"
	"github.com/devtron-labs/devtron/pkg/bulkAction/bean"
	bulkEditV1Beta1 "github.com/devtron-labs/devtron/pkg/bulkAction/v1beta2/bulkEdit"
	"github.com/devtron-labs/devtron/pkg/bulkAction/v1beta2/bulkEdit/adapter"
	api "github.com/devtron-labs/devtron/pkg/bulkAction/v1beta2/bulkEdit/bean"
	approvalBean "github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/bean"
	ctxUtil "github.com/devtron-labs/devtron/util"
	globalUtil "github.com/devtron-labs/devtron/util/expressionEvaluator"
	"github.com/go-pg/pg"
	"net/http"
)

type BulkUpdateServiceEnt interface {
	// GetBulkEditConfigV1Beta2 is used by the V1Beta2 API to get the bulk edit configuration.
	// It returns a list of api.BulkEditConfig objects and an error if any occurs.
	GetBulkEditConfigV1Beta2(apiVersion, kind string) (response *api.BulkEditConfig, err error)
	// DryRunBulkEditV1Beta2 performs a dry run of the bulk edit operation without making any changes.
	// It takes a bulkEditRequest of type api.BulkEditSpec and returns an api.ImpactedObjects response and an error if any occurs.
	DryRunBulkEditV1Beta2(bulkEditRequest api.BulkEditSpec) (impactedObjectsResponse *api.ImpactedObjects, resourceScopes *api.ResourceScopes, err error)
	// BulkEditV1Beta2 handles the bulk edit operation for applications.
	// It takes a bulkEditRequest of type api.BulkEditSpec and userMetadata, returning a response of type api.BulkEditResponse and an error if any occurs.
	BulkEditV1Beta2(ctx *ctxUtil.RequestCtx, bulkEditRequest api.BulkEditSpec, resourceScopes *api.ResourceScopes, userMetadata *userBean.UserMetadata) (response *api.BulkEditResponse, err error)
}

type BulkUpdateServiceEntImpl struct {
	bulkEditV1Beta1Service bulkEditV1Beta1.Service
}

func NewBulkUpdateServiceEntImpl(
	bulkEditV1Beta1Service bulkEditV1Beta1.Service,
) *BulkUpdateServiceEntImpl {
	return &BulkUpdateServiceEntImpl{
		bulkEditV1Beta1Service: bulkEditV1Beta1Service,
	}
}

func (impl BulkUpdateServiceImpl) isEnvDTConfigUpdateAllowed(ctx context.Context, appId, envId int, userMetadata *userBean.UserMetadata) error {
	return impl.bulkEditV1Beta1Service.CanUpdateConfigResource(ctx, approvalBean.APPROVAL_FOR_CONFIGURATION_DT, appId, envId, userMetadata)
}

func (impl BulkUpdateServiceImpl) isBaseDTConfigUpdateAllowed(ctx context.Context, appId int, userMetadata *userBean.UserMetadata) error {
	return impl.bulkEditV1Beta1Service.CanUpdateConfigResource(ctx, approvalBean.APPROVAL_FOR_CONFIGURATION_DT, appId, approvalBean.BASE_CONFIG_ENV_ID, userMetadata)
}

func (impl BulkUpdateServiceImpl) isEnvCMConfigUpdateAllowed(ctx context.Context, appId, envId int, userMetadata *userBean.UserMetadata) error {
	return impl.bulkEditV1Beta1Service.CanUpdateConfigResource(ctx, approvalBean.APPROVAL_FOR_CONFIGURATION_CM, appId, envId, userMetadata)
}

func (impl BulkUpdateServiceImpl) isBaseCMConfigUpdateAllowed(ctx context.Context, appId int, userMetadata *userBean.UserMetadata) error {
	return impl.bulkEditV1Beta1Service.CanUpdateConfigResource(ctx, approvalBean.APPROVAL_FOR_CONFIGURATION_CM, appId, approvalBean.BASE_CONFIG_ENV_ID, userMetadata)
}

func (impl BulkUpdateServiceImpl) isEnvCSConfigUpdateAllowed(ctx context.Context, appId, envId int, userMetadata *userBean.UserMetadata) error {
	return impl.bulkEditV1Beta1Service.CanUpdateConfigResource(ctx, approvalBean.APPROVAL_FOR_CONFIGURATION_CS, appId, envId, userMetadata)
}

func (impl BulkUpdateServiceImpl) isBaseCSConfigUpdateAllowed(ctx context.Context, appId int, userMetadata *userBean.UserMetadata) error {
	return impl.bulkEditV1Beta1Service.CanUpdateConfigResource(ctx, approvalBean.APPROVAL_FOR_CONFIGURATION_CS, appId, approvalBean.BASE_CONFIG_ENV_ID, userMetadata)
}

func (impl BulkUpdateServiceImpl) BulkHibernateV1(ctx context.Context, request *bean.BulkApplicationForEnvironmentPayload, checkAuthForBulkActions func(token, appObject, envObject string) bool,
	userMetadata *userBean.UserMetadata) (*bean.BulkApplicationHibernateUnhibernateForEnvironmentResponse, error) {
	return impl.bulkHibernateCommon(request, ctx, ctxUtil.GetTokenFromContext(ctx), checkAuthForBulkActions, impl.deployedAppService.StopStartAppV1, userMetadata)
}

func (impl BulkUpdateServiceImpl) getLatestEligibleImage(ctx *ctxUtil.RequestCtx, pipeline *pipelineConfig.Pipeline, userMetadata *userBean.UserMetadata) (int, bool, error) {
	artifactSatisfied := false
	impl.logger.Infow("getting getLatestEligibleImage", "pipelineId", pipeline.Id)
	offset := 0
	artifactsListingFilterOptions := &apiBean.ArtifactsListFilterOptions{
		Limit:        20,
		Offset:       0,
		SearchString: "",
	}
	// taking a call where images are not found in top 100 artifacts will return not deployed.
	for offset < 100 && !artifactSatisfied {
		// setting off set
		artifactsListingFilterOptions.Offset = offset
		// increasing offset for next call
		offset += 20

		artifactResponse, err := impl.pipelineBuilder.RetrieveArtifactsByCDPipelineV2(ctx, pipeline, apiBean.CD_WORKFLOW_TYPE_DEPLOY, artifactsListingFilterOptions, false, userMetadata)
		if err != nil {
			impl.logger.Errorw("service err, GetArtifactsByCDPipeline", "err", err, "cdPipelineId", pipeline.Id)
			return 0, false, err
		}
		artifacts := artifactResponse.CiArtifacts
		if len(artifacts) == 0 {
			impl.logger.Infow("getting getLatestEligibleImage, no artifacts found, skipping this", "pipelineId", pipeline.Id)
			//there is no artifacts found for this pipeline, skip cd trigger
			break
		}
		for _, arti := range artifacts {
			// fetch latest approved artifact in case of approval node configured
			if arti.FilterState != globalUtil.ALLOW {
				impl.logger.Debugw("getting getLatestEligibleImage, filter state is not allowed", "pipelineId", pipeline.Id, "artifactId", arti.Id)
				if arti.FilterState == globalUtil.ERROR {
					impl.logger.Errorw("service err, GetArtifactsByCDPipeline, error in evaluating filter expression", "cdPipelineId", pipeline.Id, "artifactId", arti.Id)
				}
				continue
			} else {
				impl.logger.Infow("getting getLatestEligibleImage, found the artifact", "pipelineId", pipeline.Id, "artifactId", arti.Id)
				artifactSatisfied = true
				return arti.Id, artifactSatisfied, nil
			}
		}
	}
	return 0, false, nil
}

func (impl BulkUpdateServiceImpl) getLatestImage(ctx *ctxUtil.RequestCtx, pipeline *pipelineConfig.Pipeline, userMetadata *userBean.UserMetadata) (int, bool, error) {
	artifactsListingFilterOptions := &apiBean.ArtifactsListFilterOptions{
		Limit:        10,
		Offset:       0,
		SearchString: "",
	}
	artifactResponse, err := impl.pipelineBuilder.RetrieveArtifactsByCDPipelineV2(ctx, pipeline, apiBean.CD_WORKFLOW_TYPE_DEPLOY, artifactsListingFilterOptions, false, userMetadata)
	if err != nil {
		impl.logger.Errorw("service err, GetArtifactsByCDPipeline", "err", err, "cdPipelineId", pipeline.Id)
		return 0, false, err
	}
	artifacts := artifactResponse.CiArtifacts
	if len(artifacts) == 0 {
		impl.logger.Infow("getting getLatestImageAndSetResponseIfNotFound, no artifacts found, skipping this", "pipelineId", pipeline.Id)
		//there is no artifacts found for this pipeline, skip cd trigger
		return 0, false, nil
	}
	artifact := artifacts[0]
	// fetch latest approved artifact in case of approval node configured
	if artifact.FilterState != globalUtil.ALLOW {
		impl.logger.Infow("getting getLatestImageAndSetResponseIfNotFound, filter state is not allowed", "pipelineId", pipeline.Id, "artifactId", artifact.Id)
		if artifact.FilterState == globalUtil.ERROR {
			impl.logger.Errorw("service err, GetArtifactsByCDPipeline, error in evaluating filter expression", "cdPipelineId", pipeline.Id)
		}
		return 0, false, nil
	}
	impl.logger.Infow("getting getLatestImageAndSetResponseIfNotFound, found the artifact", "pipelineId", pipeline.Id, "artifactId", artifact.Id)
	return artifact.Id, true, nil
}

func (impl BulkUpdateServiceImpl) GetBulkEditConfigV1Beta2(apiVersion, kind string) (response *api.BulkEditConfig, err error) {
	response, err = impl.bulkEditV1Beta1Service.GetBulkEditConfig(apiVersion, kind)
	if err != nil && !errors.Is(err, pg.ErrNoRows) {
		impl.logger.Errorw("error in fetching bulk edit config", "apiVersion", apiVersion, "kind", kind, "err", err)
		return response, err
	} else if errors.Is(err, pg.ErrNoRows) {
		impl.logger.Errorw("no bulk edit config found", "apiVersion", apiVersion, "kind", kind)
		errMsg := fmt.Sprintf("no bulk edit config found for '%s/%s'", apiVersion, kind)
		return response, util.NewApiError(http.StatusBadRequest, errMsg, errMsg)
	}
	return response, nil
}

// DryRunBulkEditV1Beta2 performs a dry run of the bulk edit operation without making any changes.
func (impl BulkUpdateServiceImpl) DryRunBulkEditV1Beta2(bulkEditRequest api.BulkEditSpec) (impactedObjectsResponse *api.ImpactedObjects, resourceScopes *api.ResourceScopes, err error) {
	impactedObjectsResponse = &api.ImpactedObjects{}
	resourceScopes = api.NewResourceScopes()
	appScopes, err := impl.bulkEditV1Beta1Service.GetApplicationScopes(bulkEditRequest.Selectors)
	if err != nil {
		impl.logger.Errorw("error in fetching impacted applications", "err", err)
		return impactedObjectsResponse, resourceScopes, err
	}
	envScopes, err := impl.bulkEditV1Beta1Service.GetEnvironmentScopes(bulkEditRequest.Selectors)
	if err != nil {
		impl.logger.Errorw("error in fetching impacted applications", "err", err)
		return impactedObjectsResponse, resourceScopes, err
	}
	if len(appScopes) == 0 {
		// If no application scopes are found, fetch all application scopes
		appScopes, err = impl.bulkEditV1Beta1Service.GetAllApplicationScopesWithEnv(envScopes)
		if err != nil {
			impl.logger.Errorw("error in fetching impacted applications", "err", err)
			return impactedObjectsResponse, resourceScopes, err
		}
	}
	if len(appScopes) == 0 && len(envScopes) == 0 {
		impl.logger.Errorw("no impacted applications or environments found", "appScopes", appScopes, "envScopes", envScopes)
		errMsg := fmt.Sprintf("No impacted applications or environments found for the given selectors. Please check the selectors and try again.")
		return impactedObjectsResponse, resourceScopes, util.NewApiError(http.StatusBadRequest, errMsg, errMsg)
	}
	deploymentTemplateImpactedObjects, dtScopes, err := impl.getImpactedDeploymentTemplateObjs(bulkEditRequest, appScopes, envScopes)
	if err != nil {
		impl.logger.Errorw("error in fetching impacted deployment templates", "err", err)
		return impactedObjectsResponse, resourceScopes, err
	}
	resourceScopes = resourceScopes.WithDtScopes(dtScopes)
	configMapImpactedObjects, configMapScopes, err := impl.getImpactedConfigMapObjs(bulkEditRequest, appScopes, envScopes)
	if err != nil {
		impl.logger.Errorw("error in fetching impacted config maps", "err", err)
		return impactedObjectsResponse, resourceScopes, err
	}
	resourceScopes = resourceScopes.WithConfigMapScopes(configMapScopes)
	secretImpactedObjects, secretScopes, err := impl.getImpactedSecretObjs(bulkEditRequest, appScopes, envScopes)
	if err != nil {
		impl.logger.Errorw("error in fetching impacted secrets", "err", err)
		return impactedObjectsResponse, resourceScopes, err
	}
	resourceScopes = resourceScopes.WithSecretScopes(secretScopes)
	impactedObjectsResponse.DeploymentTemplate = deploymentTemplateImpactedObjects
	impactedObjectsResponse.ConfigMap = configMapImpactedObjects
	impactedObjectsResponse.Secret = secretImpactedObjects
	return impactedObjectsResponse, resourceScopes, nil
}

// BulkEditV1Beta2 handles the bulk edit operation for applications.
func (impl BulkUpdateServiceImpl) BulkEditV1Beta2(ctx *ctxUtil.RequestCtx, bulkEditRequest api.BulkEditSpec, resourceScopes *api.ResourceScopes, userMetadata *userBean.UserMetadata) (response *api.BulkEditResponse, err error) {
	bulkUpdateResponse := &api.BulkEditResponse{}
	deploymentTemplateSpec, err := bulkEditRequest.AsBulkEditDeploymentTemplateSpec()
	if err != nil {
		impl.logger.Errorw("error in converting bulk edit request to deployment template spec", "err", err)
		return bulkUpdateResponse, err
	}
	if deploymentTemplateSpec.DeploymentTemplate != nil {
		dtBulkEditResponse := &api.DeploymentTemplateResponse{}
		for _, dtScope := range resourceScopes.GetDtScopes() {
			if dtScope.GetEnvId() == 0 {
				// For Base Deployment Template
				dtBulkEditResponse = impl.bulkEditV1Beta1Service.BulkEditBaseDeploymentTemplate(ctx, dtScope,
					&deploymentTemplateSpec.DeploymentTemplate.Spec, userMetadata, dtBulkEditResponse)
			} else {
				// For Environment Deployment Template
				dtBulkEditResponse = impl.bulkEditV1Beta1Service.BulkEditEnvDeploymentTemplate(ctx, dtScope,
					&deploymentTemplateSpec.DeploymentTemplate.Spec, userMetadata, dtBulkEditResponse)
			}
		}
		if len(dtBulkEditResponse.Failure) == 0 && len(dtBulkEditResponse.Successful) != 0 {
			dtBulkEditResponse.Message = append(dtBulkEditResponse.Message, api.AllApplicationsUpdated)
		}
		bulkUpdateResponse.DeploymentTemplate = dtBulkEditResponse
	}
	configMapSpec, err := bulkEditRequest.AsBulkEditConfigMapSpec()
	if err != nil {
		impl.logger.Errorw("error in converting bulk edit request to config maps spec", "err", err)
		return nil, err
	}
	if configMapSpec.ConfigMap != nil {
		configMapBulkEditResponse := &api.CmCsResponse{}
		for _, configMapScope := range resourceScopes.GetConfigMapScopes() {
			if configMapScope.GetEnvId() == 0 {
				// For Base ConfigMap
				configMapBulkEditResponse = impl.bulkEditV1Beta1Service.BulkEditBaseConfigMap(ctx, configMapScope,
					&configMapSpec.ConfigMap.Spec, userMetadata, configMapBulkEditResponse)
			} else {
				// For Environment ConfigMap
				configMapBulkEditResponse = impl.bulkEditV1Beta1Service.BulkEditEnvConfigMap(ctx, configMapScope,
					&configMapSpec.ConfigMap.Spec, userMetadata, configMapBulkEditResponse)
			}
		}
		if len(configMapBulkEditResponse.Failure) == 0 && len(configMapBulkEditResponse.Successful) != 0 {
			configMapBulkEditResponse.Message = append(configMapBulkEditResponse.Message, api.AllApplicationsUpdated)
		}
		bulkUpdateResponse.ConfigMap = configMapBulkEditResponse
	}
	secretSpec, err := bulkEditRequest.AsBulkEditSecretSpec()
	if err != nil {
		impl.logger.Errorw("error in converting bulk edit request to secret spec", "err", err)
		return nil, err
	}
	if secretSpec.Secret != nil {
		secretBulkEditResponse := &api.CmCsResponse{}
		for _, secretScope := range resourceScopes.GetSecretScopes() {
			if secretScope.GetEnvId() == 0 {
				// For Base Secret
				secretBulkEditResponse = impl.bulkEditV1Beta1Service.BulkEditBaseSecret(ctx, secretScope,
					&secretSpec.Secret.Spec, userMetadata, secretBulkEditResponse)
			} else {
				// For Environment Secret
				secretBulkEditResponse = impl.bulkEditV1Beta1Service.BulkEditEnvSecret(ctx, secretScope,
					&secretSpec.Secret.Spec, userMetadata, secretBulkEditResponse)
			}
		}
		if len(secretBulkEditResponse.Failure) == 0 && len(secretBulkEditResponse.Successful) != 0 {
			secretBulkEditResponse.Message = append(secretBulkEditResponse.Message, api.AllApplicationsUpdated)
		}
		bulkUpdateResponse.Secret = secretBulkEditResponse
	}
	return bulkUpdateResponse, nil
}

// getImpactedDeploymentTemplateObjs fetches the impacted deployment template objects based on the bulk edit request and application/environment scopes.
func (impl BulkUpdateServiceImpl) getImpactedDeploymentTemplateObjs(bulkEditRequest api.BulkEditSpec, appScopes []*api.ApplicationScope,
	envScopes []*api.EnvironmentScope) (deploymentTemplateImpactedObjects []api.ImpactedDeploymentTemplate, dtScopes []*api.DeploymentTemplateScope, err error) {
	// For Deployment Template
	deploymentTemplateImpactedObjects = make([]api.ImpactedDeploymentTemplate, 0)
	deploymentTemplateSpec, err := bulkEditRequest.AsBulkEditDeploymentTemplateSpec()
	if err != nil {
		impl.logger.Errorw("error in converting bulk edit request to deployment template spec", "err", err)
		return deploymentTemplateImpactedObjects, dtScopes, err
	}
	if deploymentTemplateSpec.DeploymentTemplate != nil {
		dtScope, err := impl.bulkEditV1Beta1Service.GetDeploymentTemplateScope(deploymentTemplateSpec.DeploymentTemplate.Spec.Match, appScopes)
		if err != nil {
			impl.logger.Errorw("error in fetching impacted applications for deployment template spec", "err", err)
			return deploymentTemplateImpactedObjects, dtScopes, err
		}
		if dtScope.GetIncludeBaseConfig() {
			// For Base Deployment Template
			impactedBaseDeploymentTemplates, err := impl.bulkEditV1Beta1Service.GetImpactedDeploymentTemplates(dtScope)
			if err != nil {
				impl.logger.Errorw("error in fetching impacted base deployment templates", "dtScope", dtScope, "err", err)
				return deploymentTemplateImpactedObjects, dtScopes, err
			}
			deploymentTemplateImpactedObjects = append(deploymentTemplateImpactedObjects, impactedBaseDeploymentTemplates...)
			dtScopes = append(dtScopes, dtScope)
		}
		for _, envScope := range envScopes {
			envDtScope := dtScope.DeepCopy().ForEnvScope(envScope)
			// For Environment Deployment Template
			impactedBaseDeploymentTemplates, err := impl.bulkEditV1Beta1Service.GetImpactedDeploymentTemplates(envDtScope)
			if err != nil {
				impl.logger.Errorw("error in fetching impacted env deployment templates", "envDtScope", envDtScope, "err", err)
				return deploymentTemplateImpactedObjects, dtScopes, err
			}
			deploymentTemplateImpactedObjects = append(deploymentTemplateImpactedObjects, impactedBaseDeploymentTemplates...)
			dtScopes = append(dtScopes, envDtScope)
		}
	}
	return deploymentTemplateImpactedObjects, dtScopes, err
}

// getImpactedConfigMapObjs fetches the impacted config map objects based on the bulk edit request and application/environment scopes.
func (impl BulkUpdateServiceImpl) getImpactedConfigMapObjs(bulkEditRequest api.BulkEditSpec, appScopes []*api.ApplicationScope,
	envScopes []*api.EnvironmentScope) (configMapImpactedObjects []api.ImpactedCmCs, configMapScopes []*api.ConfigScope, err error) {
	// For ConfigMap
	configMapSpec, err := bulkEditRequest.AsBulkEditConfigMapSpec()
	if err != nil {
		impl.logger.Errorw("error in converting bulk edit request to config maps spec", "err", err)
		return configMapImpactedObjects, configMapScopes, err
	}
	if configMapSpec.ConfigMap != nil {
		configMapImpactedObjects, configMapScopes, err = impl.getImpactedConfigObjs(api.ConfigMapType, configMapSpec.ConfigMap.Spec, appScopes, envScopes)
		if err != nil {
			impl.logger.Errorw("error in fetching impacted config maps", "err", err)
			return configMapImpactedObjects, configMapScopes, err
		}
	}
	return configMapImpactedObjects, configMapScopes, nil
}

// getImpactedSecretObjs fetches the impacted secret objects based on the bulk edit request and application/environment scopes.
func (impl BulkUpdateServiceImpl) getImpactedSecretObjs(bulkEditRequest api.BulkEditSpec, appScopes []*api.ApplicationScope,
	envScopes []*api.EnvironmentScope) (secretImpactedObjects []api.ImpactedCmCs, secretScopes []*api.ConfigScope, err error) {
	// For Secret
	secretSpec, err := bulkEditRequest.AsBulkEditSecretSpec()
	if err != nil {
		impl.logger.Errorw("error in converting bulk edit request to secret spec", "err", err)
		return secretImpactedObjects, secretScopes, err
	}
	if secretSpec.Secret != nil {
		secretImpactedObjects, secretScopes, err = impl.getImpactedConfigObjs(api.SecretType, secretSpec.Secret.Spec, appScopes, envScopes)
		if err != nil {
			impl.logger.Errorw("error in fetching impacted secrets", "err", err)
			return secretImpactedObjects, secretScopes, err
		}
	}
	return secretImpactedObjects, secretScopes, nil
}

func (impl BulkUpdateServiceImpl) getImpactedConfigObjs(configType api.ConfigType, configSpec api.CmCsSpec, appScopes []*api.ApplicationScope,
	envScopes []*api.EnvironmentScope) (configImpactedObjects []api.ImpactedCmCs, configScopes []*api.ConfigScope, err error) {
	configImpactedObjects = make([]api.ImpactedCmCs, 0)
	configScope := impl.bulkEditV1Beta1Service.GetConfigScope(configSpec.Match, appScopes)
	if configScope.GetIncludeBaseConfig() {
		if configSpec.Operation.Action == api.CmCsOperationActionCreate {
			impactedBaseConfigMaps := adapter.GetImpactedCmCsFromAppScopes(configSpec.Operation.Value, appScopes)
			configImpactedObjects = append(configImpactedObjects, impactedBaseConfigMaps...)
		} else {
			// For Base ConfigMap
			if configType == api.ConfigMapType {
				impactedBaseConfigMaps, err := impl.bulkEditV1Beta1Service.GetImpactedConfigMaps(configScope)
				if err != nil {
					impl.logger.Errorw("error in fetching impacted base config maps", "configScope", configScope, "err", err)
					return configImpactedObjects, configScopes, err
				}
				configImpactedObjects = append(configImpactedObjects, impactedBaseConfigMaps...)
			} else if configType == api.SecretType {
				impactedBaseSecrets, err := impl.bulkEditV1Beta1Service.GetImpactedSecrets(configScope)
				if err != nil {
					impl.logger.Errorw("error in fetching impacted base secret", "configScope", configScope, "err", err)
					return configImpactedObjects, configScopes, err
				}
				configImpactedObjects = append(configImpactedObjects, impactedBaseSecrets...)
			}
		}
		configScopes = append(configScopes, configScope)
	}
	for _, envScope := range envScopes {
		envConfigScope := configScope.DeepCopy().ForEnvScope(envScope)
		if configSpec.Operation.Action == api.CmCsOperationActionCreate {
			impactedEnvConfigMaps := adapter.GetImpactedCmCsFromAppEnvScopes(configSpec.Operation.Value, appScopes, envScope)
			configImpactedObjects = append(configImpactedObjects, impactedEnvConfigMaps...)
		} else {
			// For Environment ConfigMap
			if configType == api.ConfigMapType {
				impactedEnvConfigMaps, err := impl.bulkEditV1Beta1Service.GetImpactedConfigMaps(envConfigScope)
				if err != nil {
					impl.logger.Errorw("error in fetching impacted env config maps", "envConfigScope", envConfigScope, "err", err)
					return configImpactedObjects, configScopes, err
				}
				configImpactedObjects = append(configImpactedObjects, impactedEnvConfigMaps...)
			} else if configType == api.SecretType {
				impactedEnvSecrets, err := impl.bulkEditV1Beta1Service.GetImpactedSecrets(envConfigScope)
				if err != nil {
					impl.logger.Errorw("error in fetching impacted env secret", "envConfigScope", envConfigScope, "err", err)
					return configImpactedObjects, configScopes, err
				}
				configImpactedObjects = append(configImpactedObjects, impactedEnvSecrets...)
			}
		}
		configScopes = append(configScopes, envConfigScope)
	}
	return configImpactedObjects, configScopes, err
}
