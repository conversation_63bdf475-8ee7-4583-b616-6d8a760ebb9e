package service

import (
	"context"
	bean2 "github.com/devtron-labs/devtron/pkg/auth/user/bean"
	"github.com/devtron-labs/devtron/pkg/bulkAction/bean"
	"github.com/devtron-labs/devtron/util"
)

func (impl BulkUpdateServiceImpl) BulkHibernateV1(ctx context.Context, request *bean.BulkApplicationForEnvironmentPayload, checkAuthForBulkActions func(token, appObject, envObject string) bool,
	userMetadata *bean2.UserMetadata) (*bean.BulkApplicationHibernateUnhibernateForEnvironmentResponse, error) {
	return impl.bulkHibernateCommon(request, ctx, util.GetTokenFromContext(ctx), checkAuthForBulkActions, impl.deployedAppService.StopStartAppV1, userMetadata)
}
