/*
 * Copyright (c) 2024. Devtron Inc.
 */

package api

import "strings"

func (i *EnvIdentifierType) String() string {
	if i == nil {
		return ""
	}
	return string(*i)
}

func (i *EnvIdentifierType) IsValid() bool {
	if i == nil {
		return false
	}
	switch *i {
	case Prod, NonProd:
		return true
	}
	return false
}

func (b *BulkEdit) GetApiVersion() string {
	if b == nil {
		return ""
	}
	return strings.TrimPrefix(strings.ToLower(string(b.ApiVersion)), "batch/")
}

func (b *BulkEdit) GetKind() string {
	if b == nil {
		return ""
	}
	return strings.ToLower(string(b.Kind))
}

type ConfigType string

const (
	ConfigMapType ConfigType = "ConfigMap"
	SecretType    ConfigType = "Secret"
)
