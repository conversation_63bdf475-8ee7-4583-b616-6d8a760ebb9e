// Package api provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.2-0.20250511160408-c8cf342fd5ea DO NOT EDIT.
package api

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/oapi-codegen/runtime"
)

// Defines values for BulkEditApiVersion.
const (
	BulkEditApiVersionBatchV1Beta2 BulkEditApiVersion = "batch/v1beta2"
)

// Defines values for BulkEditKind.
const (
	BulkEditKindApplication  BulkEditKind = "application"
	BulkEditKindApplication1 BulkEditKind = "Application"
)

// Defines values for ChartVersionPropertiesOperator.
const (
	EQUAL        ChartVersionPropertiesOperator = "EQUAL"
	GREATER      ChartVersionPropertiesOperator = "GREATER"
	GREATEREQUAL ChartVersionPropertiesOperator = "GREATER_EQUAL"
	LESS         ChartVersionPropertiesOperator = "LESS"
	LESSEQUAL    ChartVersionPropertiesOperator = "LESS_EQUAL"
)

// Defines values for CmCsOperationAction.
const (
	CmCsOperationActionCreate CmCsOperationAction = "create"
	CmCsOperationActionDelete CmCsOperationAction = "delete"
	CmCsOperationActionUpdate CmCsOperationAction = "update"
)

// Defines values for CmCsOperationField.
const (
	Data CmCsOperationField = "data"
)

// Defines values for DeploymentTemplateOperationAction.
const (
	DeploymentTemplateOperationActionUpdate DeploymentTemplateOperationAction = "update"
)

// Defines values for DeploymentTemplateOperationField.
const (
	Values  DeploymentTemplateOperationField = "values"
	Version DeploymentTemplateOperationField = "version"
)

// Defines values for EnvIdentifierType.
const (
	NonProd EnvIdentifierType = "non-prod"
	Prod    EnvIdentifierType = "prod"
)

// Defines values for TemplateApiVersion.
const (
	TemplateApiVersionBatchV1Beta2 TemplateApiVersion = "batch/v1beta2"
)

// Defines values for TemplateKind.
const (
	TemplateKindApplication  TemplateKind = "application"
	TemplateKindApplication1 TemplateKind = "Application"
)

// AppEnvDetail defines model for AppEnvDetail.
type AppEnvDetail struct {
	AppName string `json:"appName" validate:"required"`
	EnvName string `json:"envName,omitempty"`
	Message string `json:"message" validate:"required"`
}

// AppIdentifier defines model for AppIdentifier.
type AppIdentifier struct {
	Excludes *NameIncludesExcludes `json:"excludes,omitempty" validate:"dive"`
	Includes *NameIncludesExcludes `json:"includes,omitempty" validate:"dive"`
}

// AppIdentifierDto defines model for AppIdentifierDto.
type AppIdentifierDto struct {
	App *AppIdentifier `json:"app,omitempty" validate:"dive"`
}

// BulkEdit Input Script for bulk edit
type BulkEdit struct {
	ApiVersion BulkEditApiVersion `json:"apiVersion" validate:"required,oneof=batch/v1beta2"`
	Kind       BulkEditKind       `json:"kind" validate:"required,oneof=application Application"`
	Spec       BulkEditSpec       `json:"spec" validate:"dive"`
}

// BulkEditApiVersion defines model for BulkEdit.ApiVersion.
type BulkEditApiVersion string

// BulkEditKind defines model for BulkEdit.Kind.
type BulkEditKind string

// BulkEditConfig Configuration of different Bulk Edit GVKs
type BulkEditConfig struct {
	// Gvk Resource GVK from URL path, i.e., {apiVersion} & {kind}
	Gvk string `json:"gvk" validate:"required,min=1,max=250"`

	// Readme Readme for the bulk edit operation, explaining how to use the API and its parameters.
	Readme string `json:"readme" validate:"required"`

	// Schema JSON schema for the bulk edit operation, defining the structure of the request body.
	Schema    map[string]interface{} `json:"schema" validate:"required"`
	Templates []Template             `json:"templates,omitempty" validate:"dive"`
}

// BulkEditConfigMapSpec defines model for BulkEditConfigMapSpec.
type BulkEditConfigMapSpec struct {
	ConfigMap *ConfigMap `json:"configMap,omitempty" validate:"dive"`
}

// BulkEditDeploymentTemplateSpec defines model for BulkEditDeploymentTemplateSpec.
type BulkEditDeploymentTemplateSpec struct {
	DeploymentTemplate *DeploymentTemplate `json:"deploymentTemplate,omitempty" validate:"dive"`
}

// BulkEditResponse defines model for BulkEditResponse.
type BulkEditResponse struct {
	ConfigMap          *CmCsResponse               `json:"configMap,omitempty" validate:"dive"`
	DeploymentTemplate *DeploymentTemplateResponse `json:"deploymentTemplate,omitempty" validate:"dive"`
	Secret             *CmCsResponse               `json:"secret,omitempty" validate:"dive"`
}

// BulkEditSecretSpec defines model for BulkEditSecretSpec.
type BulkEditSecretSpec struct {
	Secret *Secret `json:"secret,omitempty" validate:"dive"`
}

// BulkEditSpec defines model for BulkEditSpec.
type BulkEditSpec struct {
	Selectors Selectors `json:"selectors" validate:"dive"`
	union     json.RawMessage
}

// ChartVersionProperties defines model for ChartVersionProperties.
type ChartVersionProperties struct {
	Operator ChartVersionPropertiesOperator `json:"operator,omitempty" validate:"omitempty,oneof=EQUAL GREATER LESS GREATER_EQUAL LESS_EQUAL"`
	Value    string                         `json:"value,omitempty"`
}

// ChartVersionPropertiesOperator defines model for ChartVersionProperties.Operator.
type ChartVersionPropertiesOperator string

// CmCsOperation defines model for CmCsOperation.
type CmCsOperation struct {
	Action    CmCsOperationAction `json:"action" validate:"oneof=create update delete"`
	Field     CmCsOperationField  `json:"field,omitempty" validate:"omitempty,oneof=data"`
	PatchJson string              `json:"patchJson,omitempty"`
	Value     string              `json:"value,omitempty"`
}

// CmCsOperationAction defines model for CmCsOperation.Action.
type CmCsOperationAction string

// CmCsOperationField defines model for CmCsOperation.Field.
type CmCsOperationField string

// CmCsProperties defines model for CmCsProperties.
type CmCsProperties struct {
	Excludes *NameIncludesExcludes `json:"excludes,omitempty" validate:"dive"`

	// IncludeBaseConfig Indicates whether to include the base config map or secret configuration.
	IncludeBaseConfig bool                  `json:"include-base-config,omitempty"`
	Includes          *NameIncludesExcludes `json:"includes,omitempty" validate:"dive"`
}

// CmCsResponse defines model for CmCsResponse.
type CmCsResponse struct {
	Failure    []ObjectsWithAppEnvDetail `json:"failure"`
	Message    []string                  `json:"message"`
	Successful []ObjectsWithAppEnvDetail `json:"successful"`
}

// CmCsSpec defines model for CmCsSpec.
type CmCsSpec struct {
	Match     *CmCsProperties `json:"match,omitempty" validate:"dive"`
	Operation CmCsOperation   `json:"operation" validate:"dive"`
}

// ConfigMap defines model for ConfigMap.
type ConfigMap struct {
	Spec CmCsSpec `json:"spec" validate:"dive"`
}

// DeploymentChartProperties defines model for DeploymentChartProperties.
type DeploymentChartProperties struct {
	Custom  *bool                   `json:"custom,omitempty"`
	Name    *string                 `json:"name,omitempty"`
	Version *ChartVersionProperties `json:"version,omitempty" validate:"dive"`
}

// DeploymentTemplate defines model for DeploymentTemplate.
type DeploymentTemplate struct {
	Spec DeploymentTemplateSpec `json:"spec" validate:"dive"`
}

// DeploymentTemplateOperation defines model for DeploymentTemplateOperation.
type DeploymentTemplateOperation struct {
	Action       DeploymentTemplateOperationAction `json:"action" validate:"oneof=update"`
	ChartVersion string                            `json:"chartVersion,omitempty"`
	Field        DeploymentTemplateOperationField  `json:"field" validate:"omitempty,oneof=values version"`
	PatchJson    string                            `json:"patchJson,omitempty"`
}

// DeploymentTemplateOperationAction defines model for DeploymentTemplateOperation.Action.
type DeploymentTemplateOperationAction string

// DeploymentTemplateOperationField defines model for DeploymentTemplateOperation.Field.
type DeploymentTemplateOperationField string

// DeploymentTemplateProperties defines model for DeploymentTemplateProperties.
type DeploymentTemplateProperties struct {
	Chart *DeploymentChartProperties `json:"chart,omitempty" validate:"dive"`

	// IncludeBaseConfig Indicates whether to include the base deployment template configuration.
	IncludeBaseConfig bool `json:"include-base-config,omitempty"`
}

// DeploymentTemplateResponse defines model for DeploymentTemplateResponse.
type DeploymentTemplateResponse struct {
	Failure    []AppEnvDetail `json:"failure"`
	Message    []string       `json:"message"`
	Successful []AppEnvDetail `json:"successful"`
}

// DeploymentTemplateSpec defines model for DeploymentTemplateSpec.
type DeploymentTemplateSpec struct {
	Match     *DeploymentTemplateProperties `json:"match,omitempty" validate:"dive"`
	Operation DeploymentTemplateOperation   `json:"operation" validate:"dive"`
}

// EnvIdentifier defines model for EnvIdentifier.
type EnvIdentifier struct {
	Excludes *NameIncludesExcludes `json:"excludes,omitempty" validate:"dive"`
	Includes *NameIncludesExcludes `json:"includes,omitempty" validate:"dive"`
	Type     EnvIdentifierType     `json:"type,omitempty" validate:"omitempty,oneof=prod non-prod"`
}

// EnvIdentifierType defines model for EnvIdentifier.Type.
type EnvIdentifierType string

// EnvIdentifierDto defines model for EnvIdentifierDto.
type EnvIdentifierDto struct {
	Env *EnvIdentifier `json:"env,omitempty" validate:"dive"`
}

// ImpactedCmCs defines model for ImpactedCmCs.
type ImpactedCmCs struct {
	AppId   int      `json:"-"`
	AppName string   `json:"appName" validate:"required"`
	EnvId   int      `json:"-"`
	EnvName string   `json:"envName,omitempty"`
	Names   []string `json:"names" validate:"dive,min=1"`
}

// ImpactedDeploymentTemplate defines model for ImpactedDeploymentTemplate.
type ImpactedDeploymentTemplate struct {
	AppId   int    `json:"-"`
	AppName string `json:"appName" validate:"required"`
	EnvId   int    `json:"-"`
	EnvName string `json:"envName,omitempty"`
}

// ImpactedObjects defines model for ImpactedObjects.
type ImpactedObjects struct {
	ConfigMap          []ImpactedCmCs               `json:"configMap,omitempty"`
	DeploymentTemplate []ImpactedDeploymentTemplate `json:"deploymentTemplate,omitempty"`
	Secret             []ImpactedCmCs               `json:"secret,omitempty"`
}

// MatchIdentifier defines model for MatchIdentifier.
type MatchIdentifier struct {
	union json.RawMessage
}

// Metadata defines model for Metadata.
type Metadata struct {
	// CreatedBy User who created the template
	CreatedBy string `json:"createdBy,omitempty"`

	// CreatedOn Timestamp when the template was created
	CreatedOn time.Time `json:"createdOn,omitempty"`

	// Deprecated Indicates if the template is deprecated
	Deprecated bool `json:"deprecated,omitempty"`

	// DeprecatedOn Timestamp when the template was deprecated
	DeprecatedOn time.Time `json:"deprecatedOn,omitempty"`

	// Description Description of the template
	Description string `json:"description,omitempty" validate:"required,min=1,max=350"`

	// ModifiedBy User who modified the template
	ModifiedBy string `json:"modifiedBy,omitempty"`

	// ModifiedOn Timestamp when the template was modified
	ModifiedOn time.Time `json:"modifiedOn,omitempty"`

	// Name Name of the template
	Name string `json:"name" validate:"required,min=3,max=250"`

	// Version Semantic version of the template
	Version string `json:"version" validate:"required,min=1,max=250"`
}

// NameIdentifier defines model for NameIdentifier.
type NameIdentifier struct {
	Excludes *NameIncludesExcludes `json:"excludes,omitempty" validate:"dive"`
	Includes *NameIncludesExcludes `json:"includes,omitempty" validate:"dive"`
}

// NameIncludesExcludes defines model for NameIncludesExcludes.
type NameIncludesExcludes struct {
	Names []string `json:"names" validate:"dive,min=1"`
}

// ObjectsWithAppEnvDetail defines model for ObjectsWithAppEnvDetail.
type ObjectsWithAppEnvDetail struct {
	AppName string   `json:"appName" validate:"required"`
	EnvName string   `json:"envName,omitempty"`
	Message string   `json:"message" validate:"required"`
	Names   []string `json:"names"`
}

// ProjectIdentifierDto defines model for ProjectIdentifierDto.
type ProjectIdentifierDto struct {
	Project *NameIdentifier `json:"project,omitempty" validate:"dive"`
}

// Secret defines model for Secret.
type Secret struct {
	Spec CmCsSpec `json:"spec" validate:"dive"`
}

// Selectors defines model for Selectors.
type Selectors struct {
	Match MatchIdentifier `json:"match" validate:"dive"`
}

// Template defines model for Template.
type Template struct {
	ApiVersion TemplateApiVersion `json:"apiVersion" validate:"required,oneof=batch/v1beta2"`
	Kind       TemplateKind       `json:"kind" validate:"required,oneof=application Application"`
	Metadata   Metadata           `json:"metadata" validate:"dive"`

	// Readme Readme for the bulk edit operation, explaining how to use the API and its parameters.
	Readme string `json:"readme" validate:"required"`

	// Schema JSON schema for the bulk edit operation, defining the structure of the request body.
	Schema map[string]interface{} `json:"schema" validate:"required"`
	Spec   BulkEditSpec           `json:"spec" validate:"dive"`
}

// TemplateApiVersion defines model for Template.ApiVersion.
type TemplateApiVersion string

// TemplateKind defines model for Template.Kind.
type TemplateKind string

// ApplyBulkEditRequestJSONRequestBody defines body for ApplyBulkEditRequest for application/json ContentType.
type ApplyBulkEditRequestJSONRequestBody = BulkEdit

// DryRunBulkEditRequestJSONRequestBody defines body for DryRunBulkEditRequest for application/json ContentType.
type DryRunBulkEditRequestJSONRequestBody = BulkEdit

// AsBulkEditDeploymentTemplateSpec returns the union data inside the BulkEditSpec as a BulkEditDeploymentTemplateSpec
func (t BulkEditSpec) AsBulkEditDeploymentTemplateSpec() (BulkEditDeploymentTemplateSpec, error) {
	var body BulkEditDeploymentTemplateSpec
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromBulkEditDeploymentTemplateSpec overwrites any union data inside the BulkEditSpec as the provided BulkEditDeploymentTemplateSpec
func (t *BulkEditSpec) FromBulkEditDeploymentTemplateSpec(v BulkEditDeploymentTemplateSpec) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeBulkEditDeploymentTemplateSpec performs a merge with any union data inside the BulkEditSpec, using the provided BulkEditDeploymentTemplateSpec
func (t *BulkEditSpec) MergeBulkEditDeploymentTemplateSpec(v BulkEditDeploymentTemplateSpec) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsBulkEditConfigMapSpec returns the union data inside the BulkEditSpec as a BulkEditConfigMapSpec
func (t BulkEditSpec) AsBulkEditConfigMapSpec() (BulkEditConfigMapSpec, error) {
	var body BulkEditConfigMapSpec
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromBulkEditConfigMapSpec overwrites any union data inside the BulkEditSpec as the provided BulkEditConfigMapSpec
func (t *BulkEditSpec) FromBulkEditConfigMapSpec(v BulkEditConfigMapSpec) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeBulkEditConfigMapSpec performs a merge with any union data inside the BulkEditSpec, using the provided BulkEditConfigMapSpec
func (t *BulkEditSpec) MergeBulkEditConfigMapSpec(v BulkEditConfigMapSpec) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsBulkEditSecretSpec returns the union data inside the BulkEditSpec as a BulkEditSecretSpec
func (t BulkEditSpec) AsBulkEditSecretSpec() (BulkEditSecretSpec, error) {
	var body BulkEditSecretSpec
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromBulkEditSecretSpec overwrites any union data inside the BulkEditSpec as the provided BulkEditSecretSpec
func (t *BulkEditSpec) FromBulkEditSecretSpec(v BulkEditSecretSpec) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeBulkEditSecretSpec performs a merge with any union data inside the BulkEditSpec, using the provided BulkEditSecretSpec
func (t *BulkEditSpec) MergeBulkEditSecretSpec(v BulkEditSecretSpec) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

func (t BulkEditSpec) MarshalJSON() ([]byte, error) {
	b, err := t.union.MarshalJSON()
	if err != nil {
		return nil, err
	}
	object := make(map[string]json.RawMessage)
	if t.union != nil {
		err = json.Unmarshal(b, &object)
		if err != nil {
			return nil, err
		}
	}

	object["selectors"], err = json.Marshal(t.Selectors)
	if err != nil {
		return nil, fmt.Errorf("error marshaling 'selectors': %w", err)
	}

	b, err = json.Marshal(object)
	return b, err
}

func (t *BulkEditSpec) UnmarshalJSON(b []byte) error {
	err := t.union.UnmarshalJSON(b)
	if err != nil {
		return err
	}
	object := make(map[string]json.RawMessage)
	err = json.Unmarshal(b, &object)
	if err != nil {
		return err
	}

	if raw, found := object["selectors"]; found {
		err = json.Unmarshal(raw, &t.Selectors)
		if err != nil {
			return fmt.Errorf("error reading 'selectors': %w", err)
		}
	}

	return err
}

// AsProjectIdentifierDto returns the union data inside the MatchIdentifier as a ProjectIdentifierDto
func (t MatchIdentifier) AsProjectIdentifierDto() (ProjectIdentifierDto, error) {
	var body ProjectIdentifierDto
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromProjectIdentifierDto overwrites any union data inside the MatchIdentifier as the provided ProjectIdentifierDto
func (t *MatchIdentifier) FromProjectIdentifierDto(v ProjectIdentifierDto) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeProjectIdentifierDto performs a merge with any union data inside the MatchIdentifier, using the provided ProjectIdentifierDto
func (t *MatchIdentifier) MergeProjectIdentifierDto(v ProjectIdentifierDto) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsAppIdentifierDto returns the union data inside the MatchIdentifier as a AppIdentifierDto
func (t MatchIdentifier) AsAppIdentifierDto() (AppIdentifierDto, error) {
	var body AppIdentifierDto
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromAppIdentifierDto overwrites any union data inside the MatchIdentifier as the provided AppIdentifierDto
func (t *MatchIdentifier) FromAppIdentifierDto(v AppIdentifierDto) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeAppIdentifierDto performs a merge with any union data inside the MatchIdentifier, using the provided AppIdentifierDto
func (t *MatchIdentifier) MergeAppIdentifierDto(v AppIdentifierDto) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsEnvIdentifierDto returns the union data inside the MatchIdentifier as a EnvIdentifierDto
func (t MatchIdentifier) AsEnvIdentifierDto() (EnvIdentifierDto, error) {
	var body EnvIdentifierDto
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromEnvIdentifierDto overwrites any union data inside the MatchIdentifier as the provided EnvIdentifierDto
func (t *MatchIdentifier) FromEnvIdentifierDto(v EnvIdentifierDto) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeEnvIdentifierDto performs a merge with any union data inside the MatchIdentifier, using the provided EnvIdentifierDto
func (t *MatchIdentifier) MergeEnvIdentifierDto(v EnvIdentifierDto) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

func (t MatchIdentifier) MarshalJSON() ([]byte, error) {
	b, err := t.union.MarshalJSON()
	return b, err
}

func (t *MatchIdentifier) UnmarshalJSON(b []byte) error {
	err := t.union.UnmarshalJSON(b)
	return err
}
