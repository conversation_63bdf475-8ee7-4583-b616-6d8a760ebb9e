/*
 * Copyright (c) 2024. Devtron Inc.
 */

package api

import (
	"golang.org/x/exp/maps"
)

// BulkEditScope defines the scope for bulk edit operations, including application IDs and environment ID.
type BulkEditScope struct {
	appDetailsMap     map[int]*ApplicationScope
	envId             int
	envName           string
	includeBaseConfig bool
}

// NewBulkEditScope initializes a new BulkEditScope instance with default values.
func NewBulkEditScope() *BulkEditScope {
	return &BulkEditScope{}
}

// GetAppName returns the name of the application associated with the given appId from the BulkEditScope.
func (b *BulkEditScope) GetAppName(appId int) string {
	appDetails, exists := b.GetAppDetails(appId)
	if !exists {
		return ""
	}
	return appDetails.AppName
}

// GetAppDetails returns the application details for a given appId from the BulkEditScope.
func (b *BulkEditScope) GetAppDetails(appId int) (*ApplicationScope, bool) {
	if b == nil || len(b.appDetailsMap) == 0 {
		return nil, false
	}
	appDetails, exists := b.appDetailsMap[appId]
	return appDetails, exists
}

// GetEnvId returns the ID of the environment associated with the BulkEditScope.
func (b *BulkEditScope) GetEnvId() int {
	if b == nil {
		return 0
	}
	return b.envId
}

// GetEnvName returns the name of the environment associated with the BulkEditScope.
func (b *BulkEditScope) GetEnvName() string {
	if b == nil {
		return ""
	}
	return b.envName
}

// GetIncludeBaseConfig returns whether the base configuration should be included in the bulk edit scope.
func (b *BulkEditScope) GetIncludeBaseConfig() bool {
	if b == nil {
		return false
	}
	return b.includeBaseConfig
}

// GetAppIds returns a slice of application IDs present in the BulkEditScope.
func (b *BulkEditScope) GetAppIds() []int {
	if b == nil || len(b.appDetailsMap) == 0 {
		return make([]int, 0)
	}
	return maps.Keys(b.appDetailsMap)
}

// SetIncludeBaseConfig sets whether the base configuration should be included in the BulkEditScope.
func (b *BulkEditScope) SetIncludeBaseConfig(includeBaseConfig bool) *BulkEditScope {
	if b == nil {
		return nil
	}
	b.includeBaseConfig = includeBaseConfig
	return b
}

// SetAppDetails sets the application details for the BulkEditScope, allowing it to be used in bulk edit operations.
func (b *BulkEditScope) SetAppDetails(appId int, applicationScope *ApplicationScope) *BulkEditScope {
	if b == nil {
		return nil
	}
	if b.appDetailsMap == nil {
		b.appDetailsMap = make(map[int]*ApplicationScope)
	}
	if applicationScope != nil {
		b.appDetailsMap[appId] = applicationScope
	}
	return b
}

// DeepCopyInto creates a deep copy of the BulkEditScope into the provided out parameter.
func (b *BulkEditScope) DeepCopyInto(out *BulkEditScope) {
	if b == nil || out == nil {
		return
	}
	out.appDetailsMap = make(map[int]*ApplicationScope)
	maps.Copy(out.appDetailsMap, b.appDetailsMap)
	out.envId = b.envId
	out.envName = b.envName
	out.includeBaseConfig = b.includeBaseConfig
}

// DeepCopy creates a deep copy of the BulkEditScope.
func (b *BulkEditScope) DeepCopy() *BulkEditScope {
	if b == nil {
		return nil
	}
	out := new(BulkEditScope)
	b.DeepCopyInto(out)
	return out
}

// DeploymentTemplateScope defines the scope for bulk edit operations related to deployment templates,
type DeploymentTemplateScope struct {
	*BulkEditScope
	ChartRefIds []int
}

// SetAppDetails sets the application details for the DeploymentTemplateScope, allowing it to be used in bulk edit operations.
func (d *DeploymentTemplateScope) SetAppDetails(appId int, applicationScope *ApplicationScope) *DeploymentTemplateScope {
	if d == nil {
		return nil
	}
	if d.BulkEditScope == nil {
		d.BulkEditScope = &BulkEditScope{}
	}
	d.BulkEditScope = d.BulkEditScope.SetAppDetails(appId, applicationScope)
	return d
}

// DeepCopyInto creates a deep copy of the DeploymentTemplateScope into the provided out parameter.
func (d *DeploymentTemplateScope) DeepCopyInto(out *DeploymentTemplateScope) {
	if d == nil || out == nil {
		return
	}
	out.BulkEditScope = d.BulkEditScope.DeepCopy()
	out.ChartRefIds = append([]int{}, d.ChartRefIds...)
}

// DeepCopy creates a deep copy of the DeploymentTemplateScope.
func (d *DeploymentTemplateScope) DeepCopy() *DeploymentTemplateScope {
	if d == nil {
		return nil
	}
	out := new(DeploymentTemplateScope)
	d.DeepCopyInto(out)
	return out
}

// ForEnvScope sets the environment scope for the DeploymentTemplateScope, allowing it to be used in bulk edit operations.
func (d *DeploymentTemplateScope) ForEnvScope(envScope *EnvironmentScope) *DeploymentTemplateScope {
	if d.BulkEditScope == nil || envScope == nil {
		return d
	}
	d.envId = envScope.EnvId
	d.envName = envScope.EnvName
	return d
}

// ConfigScope defines the scope for bulk edit operations related to configurations, config-maps, and secrets.
type ConfigScope struct {
	*BulkEditScope
	ConfigNameIncludes []string
	ConfigNameExcludes []string
}

// SetAppDetails sets the application details for the ConfigScope, allowing it to be used in bulk edit operations.
func (s *ConfigScope) SetAppDetails(appId int, applicationScope *ApplicationScope) *ConfigScope {
	if s == nil {
		return nil
	}
	if s.BulkEditScope == nil {
		s.BulkEditScope = &BulkEditScope{}
	}
	s.BulkEditScope = s.BulkEditScope.SetAppDetails(appId, applicationScope)
	return s
}

// DeepCopyInto creates a deep copy of the ConfigScope into the provided out parameter.
func (s *ConfigScope) DeepCopyInto(out *ConfigScope) {
	if s == nil || out == nil {
		return
	}
	out.BulkEditScope = s.BulkEditScope.DeepCopy()
	out.ConfigNameIncludes = append([]string{}, s.ConfigNameIncludes...)
	out.ConfigNameExcludes = append([]string{}, s.ConfigNameExcludes...)
}

// DeepCopy creates a deep copy of the ConfigScope.
func (s *ConfigScope) DeepCopy() *ConfigScope {
	if s == nil {
		return nil
	}
	out := new(ConfigScope)
	s.DeepCopyInto(out)
	return out
}

// ForEnvScope sets the environment scope for the ConfigScope, allowing it to be used in bulk edit operations.
func (s *ConfigScope) ForEnvScope(envScope *EnvironmentScope) *ConfigScope {
	if s.BulkEditScope == nil || envScope == nil {
		return s
	}
	s.envId = envScope.EnvId
	s.envName = envScope.EnvName
	return s
}

// ApplicationScope defines the impacted applications for bulk edit operations.
type ApplicationScope struct {
	AppId    int
	AppName  string
	TeamName string
}

// EnvironmentScope defines the impacted environments for bulk edit operations.
type EnvironmentScope struct {
	EnvId   int
	EnvName string
}

// ResourceScopes aggregates different types of scopes for bulk edit operations, including deployment templates, config-maps, and secrets.
type ResourceScopes struct {
	dtScopes        []*DeploymentTemplateScope
	configMapScopes []*ConfigScope
	secretScopes    []*ConfigScope
}

// NewResourceScopes initializes a new ResourceScopes instance with empty slices for each scope type.
func NewResourceScopes() *ResourceScopes {
	return &ResourceScopes{}
}

func (r *ResourceScopes) GetDtScopes() []*DeploymentTemplateScope {
	if r == nil {
		return nil
	}
	return r.dtScopes
}

func (r *ResourceScopes) GetConfigMapScopes() []*ConfigScope {
	if r == nil {
		return nil
	}
	return r.configMapScopes
}

func (r *ResourceScopes) GetSecretScopes() []*ConfigScope {
	if r == nil {
		return nil
	}
	return r.secretScopes
}

// WithDtScopes adds deployment template scopes to the ResourceScopes instance.
func (r *ResourceScopes) WithDtScopes(dtScopes []*DeploymentTemplateScope) *ResourceScopes {
	if r == nil {
		return nil
	}
	r.dtScopes = append(r.dtScopes, dtScopes...)
	return r
}

// WithConfigMapScopes adds config-map scopes to the ResourceScopes instance.
func (r *ResourceScopes) WithConfigMapScopes(configMapScopes []*ConfigScope) *ResourceScopes {
	if r == nil {
		return nil
	}
	r.configMapScopes = append(r.configMapScopes, configMapScopes...)
	return r
}

// WithSecretScopes adds secret scopes to the ResourceScopes instance.
func (r *ResourceScopes) WithSecretScopes(secretScopes []*ConfigScope) *ResourceScopes {
	if r == nil {
		return nil
	}
	r.secretScopes = append(r.secretScopes, secretScopes...)
	return r
}
