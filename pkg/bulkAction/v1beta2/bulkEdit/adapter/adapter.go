/*
 * Copyright (c) 2024. Devtron Inc.
 */

package adapter

import (
	"encoding/json"
	"github.com/devtron-labs/devtron/pkg/bulkAction/repository"
	api "github.com/devtron-labs/devtron/pkg/bulkAction/v1beta2/bulkEdit/bean"
	"github.com/devtron-labs/devtron/pkg/bulkAction/v1beta2/bulkEdit/util"
	"github.com/devtron-labs/devtron/util/sliceUtil"
)

func GetBulkEditConfigResponse(config *repository.BulkEditConfig) (*api.BulkEditConfig, error) {
	configSchema := make(map[string]interface{})
	err := json.Unmarshal([]byte(config.Schema), &configSchema)
	if err != nil {
		return nil, err
	}
	return &api.BulkEditConfig{
		Gvk:    util.GetBulkEditGVK(config.ApiVersion, config.Kind),
		Readme: config.Readme,
		Schema: configSchema,
	}, nil
}

func GetApplicationScope(applications *repository.ApplicationMin) *api.ApplicationScope {
	return &api.ApplicationScope{
		AppId:    applications.Id,
		AppName:  applications.AppName,
		TeamName: applications.TeamName,
	}
}

func GetApplicationScopes(applications []*repository.ApplicationMin) []*api.ApplicationScope {
	appScopes := make([]*api.ApplicationScope, 0)
	for _, app := range applications {
		appScopes = append(appScopes, GetApplicationScope(app))
	}
	return appScopes
}

func GetEnvironmentScopes(environments []*repository.EnvironmentMin) []*api.EnvironmentScope {
	envScopes := make([]*api.EnvironmentScope, 0)
	for _, env := range environments {
		envScopes = append(envScopes, &api.EnvironmentScope{
			EnvId:   env.Id,
			EnvName: env.EnvName,
		})
	}
	return envScopes
}

func GetBulkEditScope(applications []*api.ApplicationScope) *api.BulkEditScope {
	bulkEditScope := api.NewBulkEditScope()
	for _, app := range applications {
		bulkEditScope = bulkEditScope.SetAppDetails(app.AppId, app)
	}
	return bulkEditScope
}

func GetObjectsWithAppEnvDetail(appName, envName string, names []string, message string) api.ObjectsWithAppEnvDetail {
	return api.ObjectsWithAppEnvDetail{
		AppName: appName,
		EnvName: envName,
		Names:   names,
		Message: message,
	}
}

func GetObjectsWithAppDetail(appName string, names []string, message string) api.ObjectsWithAppEnvDetail {
	return GetObjectsWithAppEnvDetail(appName, "", names, message)
}

func GetAppEnvDetail(appName, envName string, message string) api.AppEnvDetail {
	return api.AppEnvDetail{
		AppName: appName,
		EnvName: envName,
		Message: message,
	}
}

func GetAppDetail(appName string, message string) api.AppEnvDetail {
	return GetAppEnvDetail(appName, "", message)
}

func GetImpactedCmCsFromAppScopes(newConfigName string, appScopes []*api.ApplicationScope) []api.ImpactedCmCs {
	impactedCmCsObjects := make([]api.ImpactedCmCs, 0)
	for _, appScope := range appScopes {
		impactedBaseSecret := api.ImpactedCmCs{
			AppId:   appScope.AppId,
			AppName: appScope.AppName,
			Names:   sliceUtil.GetSliceOf(newConfigName),
		}
		impactedCmCsObjects = append(impactedCmCsObjects, impactedBaseSecret)
	}
	return impactedCmCsObjects
}
func GetImpactedCmCsFromAppEnvScopes(newConfigName string, appScopes []*api.ApplicationScope, envScope *api.EnvironmentScope) []api.ImpactedCmCs {
	impactedCmCsObjects := make([]api.ImpactedCmCs, 0)
	for _, appScope := range appScopes {
		impactedBaseSecret := api.ImpactedCmCs{
			AppId:   appScope.AppId,
			AppName: appScope.AppName,
			EnvId:   envScope.EnvId,
			EnvName: envScope.EnvName,
			Names:   sliceUtil.GetSliceOf(newConfigName),
		}
		impactedCmCsObjects = append(impactedCmCsObjects, impactedBaseSecret)
	}
	return impactedCmCsObjects
}
