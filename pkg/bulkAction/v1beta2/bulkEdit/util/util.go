/*
 * Copyright (c) 2024. Devtron Inc.
 */

package util

import (
	"encoding/json"
	"github.com/Masterminds/semver/v3"
	api "github.com/devtron-labs/devtron/pkg/bulkAction/v1beta2/bulkEdit/bean"
	dtResourceHelper "github.com/devtron-labs/devtron/pkg/devtronResource/helper"
	"github.com/xeipuuv/gojsonschema"
	"regexp"
	"strings"
)

// ValidateVersion Verify that version is a Version, and error out if it is not.
func ValidateVersion(ver string) (*semver.Version, error) {
	return semver.NewVersion(ver)
}

func CompareVersions(v1, v2 *semver.Version, operator api.ChartVersionPropertiesOperator) bool {
	switch operator {
	case api.EQUAL:
		return v1.Equal(v2)
	case api.GREATER:
		return v1.GreaterThan(v2)
	case api.GREATEREQUAL:
		return v1.GreaterThanEqual(v2)
	case api.LESS:
		return v1.LessThan(v2)
	case api.LESSEQUAL:
		return v1.LessThanEqual(v2)
	default:
		return false
	}
}

func GetBulkEditGVK(apiVersion, kind string) string {
	// GVK format is "apiVersion.kind"
	return apiVersion + "/" + kind
}

func ValidateSchemaAndObjectData(schema map[string]interface{}, object api.BulkEdit) (*gojsonschema.Result, error) {
	schemaBytes, err := json.Marshal(schema)
	if err != nil {
		return nil, err
	}
	objectBytes, err := json.Marshal(object)
	if err != nil {
		return nil, err
	}
	return dtResourceHelper.ValidateSchemaAndObjectData(string(schemaBytes), string(objectBytes))
}

// likeToRegexPattern Converts SQL LIKE pattern to a Go regex pattern
func likeToRegexPattern(pattern string) string {
	var sb strings.Builder
	sb.WriteString("^") // match from start

	for i := 0; i < len(pattern); i++ {
		ch := pattern[i]
		if ch == '%' {
			sb.WriteString(".*")
		} else {
			// Escape regex special characters
			if strings.ContainsRune(`.+*?()|[]{}^$`, rune(ch)) {
				sb.WriteByte('\\')
			}
			sb.WriteByte(ch)
		}
	}
	sb.WriteString("$") // match till end
	return sb.String()
}

// MatchSQLLike checks if any of the data matches the SQL LIKE pattern.
func MatchSQLLike(data, pattern string) (matched bool) {
	regexPattern := likeToRegexPattern(pattern)
	re, err := regexp.Compile(regexPattern)
	if err != nil {
		return matched
	}
	return re.MatchString(data)
}

// IsAnySQLPatternsMatched checks if the data matches any of the SQL LIKE patterns provided in the slice.
func IsAnySQLPatternsMatched(data string, patterns []string) (matched bool) {
	for _, pattern := range patterns {
		if MatchSQLLike(data, pattern) {
			return true
		}
	}
	return false
}

func ShouldIncludeCmCsObject(configName string, scope *api.ConfigScope) bool {
	if len(scope.ConfigNameIncludes) == 0 && len(scope.ConfigNameExcludes) == 0 {
		// If no includes or excludes are specified, include the config (cm/cs) by default
		return true
	} else if len(scope.ConfigNameIncludes) == 0 {
		// If no includes are specified, but excludes are specified, exclude the config (cm/cs) if it matches any exclude pattern
		return !IsAnySQLPatternsMatched(configName, scope.ConfigNameExcludes)
	} else if len(scope.ConfigNameExcludes) == 0 {
		// If no excludes are specified, but includes are specified, include the config (cm/cs) if it matches any include pattern
		return IsAnySQLPatternsMatched(configName, scope.ConfigNameIncludes)
	} else {
		// If both includes and excludes are specified, include the config (cm/cs) if it matches any include pattern and does not match any exclude pattern
		return IsAnySQLPatternsMatched(configName, scope.ConfigNameIncludes) &&
			!IsAnySQLPatternsMatched(configName, scope.ConfigNameExcludes)
	}
}
