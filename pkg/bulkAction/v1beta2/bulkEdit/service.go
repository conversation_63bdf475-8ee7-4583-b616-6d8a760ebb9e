/*
 * Copyright (c) 2024. Devtron Inc.
 */

package bulkEdit

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/devtron-labs/devtron/enterprise/pkg/protect"
	"github.com/devtron-labs/devtron/internal/sql/models"
	"github.com/devtron-labs/devtron/internal/sql/repository/app"
	"github.com/devtron-labs/devtron/internal/sql/repository/chartConfig"
	"github.com/devtron-labs/devtron/internal/sql/repository/chartConfig/configMapRepository"
	internalUtil "github.com/devtron-labs/devtron/internal/util"
	userBean "github.com/devtron-labs/devtron/pkg/auth/user/bean"
	"github.com/devtron-labs/devtron/pkg/bean/configMapBean"
	bulkEditErr "github.com/devtron-labs/devtron/pkg/bulkAction/errors"
	"github.com/devtron-labs/devtron/pkg/bulkAction/repository"
	v1beta2Adapter "github.com/devtron-labs/devtron/pkg/bulkAction/v1beta2/bulkEdit/adapter"
	api "github.com/devtron-labs/devtron/pkg/bulkAction/v1beta2/bulkEdit/bean"
	v1beta2Util "github.com/devtron-labs/devtron/pkg/bulkAction/v1beta2/bulkEdit/util"
	chartService "github.com/devtron-labs/devtron/pkg/chart"
	chartsBean "github.com/devtron-labs/devtron/pkg/chart/bean"
	chartRepoRepository "github.com/devtron-labs/devtron/pkg/chartRepo/repository"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/configMapAndSecret"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deployedAppMetrics"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate"
	dtAdapter "github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/adapter"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/chartRef"
	dtValidator "github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/validator"
	"github.com/devtron-labs/devtron/pkg/globalPolicy"
	"github.com/devtron-labs/devtron/pkg/pipeline"
	historyRepo "github.com/devtron-labs/devtron/pkg/pipeline/history/repository"
	approvalBean "github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/bean"
	alpha1PolicyAdaptor "github.com/devtron-labs/devtron/pkg/policyGovernance/common/alpha1/adaptor"
	policyModel "github.com/devtron-labs/devtron/pkg/policyGovernance/model"
	"github.com/devtron-labs/devtron/pkg/variables"
	variableRepo "github.com/devtron-labs/devtron/pkg/variables/repository"
	ctxUtil "github.com/devtron-labs/devtron/util"
	jsonUtil "github.com/devtron-labs/devtron/util/json"
	"github.com/devtron-labs/devtron/util/sliceUtil"
	jsonpatch "github.com/evanphx/json-patch"
	"github.com/go-pg/pg"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
	"go.uber.org/zap"
	"net/http"
)

type Service interface {
	// GetBulkEditConfig retrieves the configuration for bulk edit operations.
	GetBulkEditConfig(apiVersion, kind string) (response *api.BulkEditConfig, err error)
	// GetAllApplicationScopesWithEnv retrieves the list of all applications within the provided environment scopes.
	GetAllApplicationScopesWithEnv(envScopes []*api.EnvironmentScope) (appScopes []*api.ApplicationScope, err error)
	// GetApplicationScopes retrieves the list of applications impacted by the given selectors.
	GetApplicationScopes(selectors api.Selectors) (appScopes []*api.ApplicationScope, err error)
	// GetEnvironmentScopes retrieves the list of environments impacted by the given selectors.
	GetEnvironmentScopes(selectors api.Selectors) (envScopes []*api.EnvironmentScope, err error)
	// GetConfigScope retrieves the configuration names impacted by the given match, applications, and environment.
	GetConfigScope(match *api.CmCsProperties, applications []*api.ApplicationScope) (configScope *api.ConfigScope)
	// GetDeploymentTemplateScope retrieves the chart references impacted by the given match and applications.
	GetDeploymentTemplateScope(match *api.DeploymentTemplateProperties, applications []*api.ApplicationScope) (dtScope *api.DeploymentTemplateScope, err error)

	// GetImpactedSecrets retrieves the impacted secrets based on the provided scope.
	GetImpactedSecrets(scope *api.ConfigScope) ([]api.ImpactedCmCs, error)
	// GetImpactedConfigMaps retrieves the impacted config maps based on the provided scope.
	GetImpactedConfigMaps(scope *api.ConfigScope) ([]api.ImpactedCmCs, error)
	// GetImpactedDeploymentTemplates retrieves the impacted deployment templates based on the provided scope.
	GetImpactedDeploymentTemplates(scope *api.DeploymentTemplateScope) ([]api.ImpactedDeploymentTemplate, error)

	// CanUpdateConfigResource checks if the user has permission to update the specified configuration resource.
	CanUpdateConfigResource(ctx context.Context, resourceKind approvalBean.ApprovalFor, appId, envId int, userMetadata *userBean.UserMetadata) error

	// BulkEditBaseDeploymentTemplate performs the bulk update of base deployment templates based on the provided scope and spec.
	BulkEditBaseDeploymentTemplate(ctx *ctxUtil.RequestCtx, dtScope *api.DeploymentTemplateScope, dtSpec *api.DeploymentTemplateSpec,
		userMetadata *userBean.UserMetadata, dtBulkEditResponse *api.DeploymentTemplateResponse) *api.DeploymentTemplateResponse
	// BulkEditEnvDeploymentTemplate performs the bulk update of environment-specific deployment templates based on the provided scope and spec.
	BulkEditEnvDeploymentTemplate(ctx *ctxUtil.RequestCtx, dtScope *api.DeploymentTemplateScope, dtSpec *api.DeploymentTemplateSpec,
		userMetadata *userBean.UserMetadata, dtBulkEditResponse *api.DeploymentTemplateResponse) *api.DeploymentTemplateResponse
	// BulkEditBaseConfigMap performs the bulk update of base config maps based on the provided scope and spec.
	BulkEditBaseConfigMap(ctx *ctxUtil.RequestCtx, configMapScope *api.ConfigScope, configMapSpec *api.CmCsSpec,
		userMetadata *userBean.UserMetadata, configMapBulkEditResponse *api.CmCsResponse) *api.CmCsResponse
	// BulkEditEnvConfigMap performs the bulk update of environment-specific config maps based on the provided scope and spec.
	BulkEditEnvConfigMap(ctx *ctxUtil.RequestCtx, configMapScope *api.ConfigScope, configMapSpec *api.CmCsSpec,
		userMetadata *userBean.UserMetadata, configMapBulkEditResponse *api.CmCsResponse) *api.CmCsResponse
	// BulkEditBaseSecret performs the bulk update of base secrets based on the provided scope and spec.
	BulkEditBaseSecret(ctx *ctxUtil.RequestCtx, secretScope *api.ConfigScope, secretSpec *api.CmCsSpec,
		userMetadata *userBean.UserMetadata, secretBulkEditResponse *api.CmCsResponse) *api.CmCsResponse
	// BulkEditEnvSecret performs the bulk update of environment-specific secrets based on the provided scope and spec.
	BulkEditEnvSecret(ctx *ctxUtil.RequestCtx, secretScope *api.ConfigScope, secretSpec *api.CmCsSpec,
		userMetadata *userBean.UserMetadata, secretBulkEditResponse *api.CmCsResponse) *api.CmCsResponse
}

type ServiceImpl struct {
	logger                              *zap.SugaredLogger
	configMapService                    pipeline.ConfigMapService
	chartService                        chartService.ChartService
	propertiesConfigService             pipeline.PropertiesConfigService
	deploymentTemplateValidationService dtValidator.DeploymentTemplateValidationService
	chartRefDbReadService               chartRef.ChartRefService
	resourceProtectionService           protect.ResourceProtectionService
	globalPolicyDataManager             globalPolicy.GlobalPolicyDataManager
	deployedAppMetricsService           deployedAppMetrics.DeployedAppMetricsService
	deploymentTemplateHistoryService    deploymentTemplate.DeploymentTemplateHistoryService
	scopedVariableManager               variables.ScopedVariableManager
	configMapHistoryService             configMapAndSecret.ConfigMapHistoryService
	appRepository                       app.AppRepository
	bulkEditRepository                  repository.BulkEditRepository
}

func NewServiceImpl(
	logger *zap.SugaredLogger,
	configMapService pipeline.ConfigMapService,
	chartService chartService.ChartService,
	propertiesConfigService pipeline.PropertiesConfigService,
	deploymentTemplateValidationService dtValidator.DeploymentTemplateValidationService,
	chartRefDbReadService chartRef.ChartRefService,
	resourceProtectionService protect.ResourceProtectionService,
	globalPolicyDataManager globalPolicy.GlobalPolicyDataManager,
	deployedAppMetricsService deployedAppMetrics.DeployedAppMetricsService,
	deploymentTemplateHistoryService deploymentTemplate.DeploymentTemplateHistoryService,
	scopedVariableManager variables.ScopedVariableManager,
	configMapHistoryService configMapAndSecret.ConfigMapHistoryService,
	appRepository app.AppRepository,
	bulkEditRepository repository.BulkEditRepository,
) *ServiceImpl {
	return &ServiceImpl{
		logger:                              logger,
		configMapService:                    configMapService,
		chartService:                        chartService,
		propertiesConfigService:             propertiesConfigService,
		deploymentTemplateValidationService: deploymentTemplateValidationService,
		chartRefDbReadService:               chartRefDbReadService,
		resourceProtectionService:           resourceProtectionService,
		globalPolicyDataManager:             globalPolicyDataManager,
		deployedAppMetricsService:           deployedAppMetricsService,
		deploymentTemplateHistoryService:    deploymentTemplateHistoryService,
		scopedVariableManager:               scopedVariableManager,
		configMapHistoryService:             configMapHistoryService,
		appRepository:                       appRepository,
		bulkEditRepository:                  bulkEditRepository,
	}
}

func (impl *ServiceImpl) GetBulkEditConfig(apiVersion, kind string) (response *api.BulkEditConfig, err error) {
	bulkEditConfig, err := impl.bulkEditRepository.FindBulkEditConfig(apiVersion, kind)
	if err != nil {
		impl.logger.Errorw("error in fetching batch operation example", "err", err)
		return response, err
	}
	return v1beta2Adapter.GetBulkEditConfigResponse(bulkEditConfig)
}

func (impl *ServiceImpl) GetAllApplicationScopesWithEnv(envScopes []*api.EnvironmentScope) (appScopes []*api.ApplicationScope, err error) {
	if len(envScopes) == 0 {
		impl.logger.Warn("no environment scopes provided, returning empty application scopes")
		return appScopes, nil
	}
	envIds := sliceUtil.NewSliceFromFuncExec(envScopes, func(envScope *api.EnvironmentScope) int {
		return envScope.EnvId
	})
	applicationMins, err := impl.bulkEditRepository.FindAllImpactedApplicationsWithEnv(envIds)
	if err != nil {
		impl.logger.Errorw("error in fetching all impacted applications", "err", err)
		return appScopes, err
	}
	return v1beta2Adapter.GetApplicationScopes(applicationMins), nil
}

func (impl *ServiceImpl) GetApplicationScopes(selectors api.Selectors) (appScopes []*api.ApplicationScope, err error) {
	appNameIncludes, appNameExcludes := make([]string, 0), make([]string, 0)
	teamNameIncludes, teamNameExcludes := make([]string, 0), make([]string, 0)
	appIdentifier, err := selectors.Match.AsAppIdentifierDto()
	if err != nil {
		impl.logger.Errorw("error in converting match to appIdentifier", "matchRequest", selectors.Match, "err", err)
		return appScopes, err
	}
	if appIdentifier.App != nil {
		if appIdentifier.App.Includes != nil {
			appNameIncludes = appIdentifier.App.Includes.Names
		}
		if appIdentifier.App.Excludes != nil {
			appNameExcludes = appIdentifier.App.Excludes.Names
		}
	}
	projectIdentifier, err := selectors.Match.AsProjectIdentifierDto()
	if err != nil {
		impl.logger.Errorw("error in converting match to projectIdentifier", "matchRequest", selectors.Match, "err", err)
		return appScopes, err
	}
	if projectIdentifier.Project != nil {
		if projectIdentifier.Project.Includes != nil {
			teamNameIncludes = append(teamNameIncludes, projectIdentifier.Project.Includes.Names...)
		}
		if projectIdentifier.Project.Excludes != nil {
			teamNameExcludes = append(teamNameExcludes, projectIdentifier.Project.Excludes.Names...)
		}
	}
	if len(appNameIncludes) == 0 && len(appNameExcludes) == 0 &&
		len(teamNameIncludes) == 0 && len(teamNameExcludes) == 0 {
		impl.logger.Infow("no app or team names provided in selectors, returning empty list")
		return appScopes, nil
	}
	applicationMins, err := impl.bulkEditRepository.FindImpactedApplications(appNameIncludes, appNameExcludes,
		teamNameIncludes, teamNameExcludes)
	if err != nil {
		impl.logger.Errorw("error in fetching impacted applications", "err", err)
		return appScopes, err
	}
	if len(applicationMins) == 0 {
		impl.logger.Infow("no impacted applications found for the provided selectors", "selectors", selectors)
		errMsg := fmt.Sprintf("no impacted applications found for the provided application includes: %v, application excludes: %v, team includes: %v, team excludes: %v",
			appNameIncludes, appNameExcludes, teamNameIncludes, teamNameExcludes)
		return appScopes, internalUtil.NewApiError(http.StatusBadRequest, errMsg, errMsg)
	}
	return v1beta2Adapter.GetApplicationScopes(applicationMins), nil
}

func (impl *ServiceImpl) GetEnvironmentScopes(selectors api.Selectors) (envScopes []*api.EnvironmentScope, err error) {
	envNameIncludes, envNameExcludes := make([]string, 0), make([]string, 0)
	envIdentifier, err := selectors.Match.AsEnvIdentifierDto()
	if err != nil {
		impl.logger.Errorw("error in converting match to envIdentifier", "matchRequest", selectors.Match, "err", err)
		return envScopes, err
	}
	if envIdentifier.Env != nil {
		if envIdentifier.Env.Includes != nil {
			envNameIncludes = envIdentifier.Env.Includes.Names
		}
		if envIdentifier.Env.Excludes != nil {
			envNameExcludes = envIdentifier.Env.Excludes.Names
		}
	}
	if len(envNameIncludes) == 0 && len(envNameExcludes) == 0 &&
		(envIdentifier.Env == nil || !envIdentifier.Env.Type.IsValid()) {
		impl.logger.Warn("no env names or type provided in selectors, returning empty list")
		return envScopes, nil
	}
	envMins, err := impl.bulkEditRepository.FindImpactedEnvironments(envNameIncludes, envNameExcludes, envIdentifier.Env.Type)
	if err != nil {
		impl.logger.Errorw("error in fetching impacted environments", "err", err)
		return envScopes, err
	}
	if len(envMins) == 0 {
		impl.logger.Infow("no impacted environments found for the provided selectors", "selectors", selectors)
		errMsg := fmt.Sprintf("no impacted environments found for the provided environment includes: %v, environment excludes: %v, environment type: %q",
			envNameIncludes, envNameExcludes, envIdentifier.Env.Type)
		return envScopes, internalUtil.NewApiError(http.StatusBadRequest, errMsg, errMsg)
	}
	return v1beta2Adapter.GetEnvironmentScopes(envMins), nil
}

func (impl *ServiceImpl) GetDeploymentTemplateScope(match *api.DeploymentTemplateProperties, applications []*api.ApplicationScope) (dtScope *api.DeploymentTemplateScope, err error) {
	bulkEditScope := v1beta2Adapter.GetBulkEditScope(applications)
	if match != nil {
		bulkEditScope = bulkEditScope.SetIncludeBaseConfig(match.IncludeBaseConfig)
	}
	dtScope = &api.DeploymentTemplateScope{
		BulkEditScope: bulkEditScope,
	}
	if match == nil || match.Chart == nil || (match.Chart.Name == nil &&
		match.Chart.Custom == nil && match.Chart.Version == nil) {
		impl.logger.Debug("no chart name or custom chart provided in match, returning empty scope")
		return dtScope, nil
	}
	chartRefMins, err := impl.bulkEditRepository.FindImpactedChartRefIds(match.Chart.Name, match.Chart.Custom)
	if err != nil {
		impl.logger.Errorw("error in fetching impacted chart refs", "err", err)
		return dtScope, err
	}
	if match.Chart.Version != nil {
		targetVersion, err := v1beta2Util.ValidateVersion(match.Chart.Version.Value)
		if err != nil {
			impl.logger.Errorw("error in parsing version", "version", match.Chart.Version, "err", err)
			errMsg := fmt.Sprintf("invalid chart version %q: %v", match.Chart.Version.Value, err)
			return dtScope, internalUtil.NewApiError(http.StatusBadRequest, errMsg, errMsg)
		}
		filteredChartRefs := make([]*repository.ChartRefMin, 0)
		filteredChartRefs = sliceUtil.Filter(filteredChartRefs, chartRefMins, func(chartRefMin *repository.ChartRefMin) bool {
			version, err := v1beta2Util.ValidateVersion(chartRefMin.Version)
			if err != nil {
				impl.logger.Errorw("error in parsing version", "version", chartRefMin.Version, "err", err)
				return false
			}
			return v1beta2Util.CompareVersions(targetVersion, version, match.Chart.Version.Operator)
		})
		dtScope.ChartRefIds = sliceUtil.NewSliceFromFuncExec(filteredChartRefs, func(chartRefMin *repository.ChartRefMin) int {
			return chartRefMin.Id
		})
		return dtScope, nil
	}
	return dtScope, nil
}

func (impl *ServiceImpl) GetConfigScope(match *api.CmCsProperties, applications []*api.ApplicationScope) (configScope *api.ConfigScope) {
	bulkEditScope := v1beta2Adapter.GetBulkEditScope(applications)
	if match != nil {
		bulkEditScope = bulkEditScope.SetIncludeBaseConfig(match.IncludeBaseConfig)
	}
	configScope = &api.ConfigScope{
		BulkEditScope: bulkEditScope,
	}
	if match != nil {
		if match.Includes != nil {
			configScope.ConfigNameIncludes = match.Includes.Names
		}
		if match.Excludes != nil {
			configScope.ConfigNameExcludes = match.Excludes.Names
		}
	}
	return configScope
}

func (impl *ServiceImpl) GetImpactedSecrets(scope *api.ConfigScope) ([]api.ImpactedCmCs, error) {
	if scope == nil {
		impl.logger.Infow("config scope is empty, returning empty list")
		return make([]api.ImpactedCmCs, 0), nil
	}
	var secretModels []configMapRepository.ConfigModel
	if scope.GetEnvId() > 0 {
		secretEnvModels, err := impl.bulkEditRepository.FindImpactedCsForEnvConfig(scope.GetAppIds(), scope.GetEnvId())
		if err != nil {
			impl.logger.Errorw("error in fetching bulk app model for global", "envId", scope.GetEnvId(), "err", err)
			return nil, err
		}
		secretModels = sliceUtil.NewSliceFromFuncExec(secretEnvModels, func(model *configMapRepository.ConfigMapEnvModel) configMapRepository.ConfigModel {
			return model
		})
	} else {
		secretAppModels, err := impl.bulkEditRepository.FindImpactedCsForBaseConfig(scope.GetAppIds())
		if err != nil {
			impl.logger.Errorw("error in fetching bulk app model for global", "err", err)
			return make([]api.ImpactedCmCs, 0), err
		}
		secretModels = sliceUtil.NewSliceFromFuncExec(secretAppModels, func(model *configMapRepository.ConfigMapAppModel) configMapRepository.ConfigModel {
			return model
		})
	}
	secretImpactedObjects := make([]api.ImpactedCmCs, 0, len(secretModels))
	for _, secretAppModel := range secretModels {
		var finalSecretNames []string
		existingSecretNames := gjson.Get(secretAppModel.GetSecretData(), "secrets.#.name")
		for _, secretName := range existingSecretNames.Array() {
			// Filter the config map models based on the includes and excludes patterns
			if v1beta2Util.ShouldIncludeCmCsObject(secretName.String(), scope) {
				finalSecretNames = append(finalSecretNames, secretName.String())
			}
		}
		if len(finalSecretNames) != 0 {
			appDetailsById, _ := impl.appRepository.FindById(secretAppModel.GetAppId())
			secretImpactedObject := api.ImpactedCmCs{
				AppId:   secretAppModel.GetAppId(),
				AppName: appDetailsById.AppName,
				Names:   finalSecretNames,
			}
			if scope.GetEnvId() > 0 {
				secretImpactedObject.EnvId = scope.GetEnvId()
				secretImpactedObject.EnvName = scope.GetEnvName()
			}
			secretImpactedObjects = append(secretImpactedObjects, secretImpactedObject)
		}
	}
	return secretImpactedObjects, nil
}

func (impl *ServiceImpl) GetImpactedConfigMaps(scope *api.ConfigScope) ([]api.ImpactedCmCs, error) {
	if scope == nil {
		impl.logger.Infow("config scope is empty, returning empty list")
		return make([]api.ImpactedCmCs, 0), nil
	}
	var configMapModels []configMapRepository.ConfigModel
	if scope.GetEnvId() > 0 {
		configMapEnvModels, err := impl.bulkEditRepository.FindImpactedCmForEnvConfig(scope.GetAppIds(), scope.GetEnvId())
		if err != nil {
			impl.logger.Errorw("error in fetching bulk app model for global", "envId", scope.GetEnvId(), "err", err)
			return make([]api.ImpactedCmCs, 0), err
		}
		configMapModels = sliceUtil.NewSliceFromFuncExec(configMapEnvModels, func(model *configMapRepository.ConfigMapEnvModel) configMapRepository.ConfigModel {
			return model
		})
	} else {
		configMapAppModels, err := impl.bulkEditRepository.FindImpactedCmForBaseConfig(scope.GetAppIds())
		if err != nil {
			impl.logger.Errorw("error in fetching bulk app model for global", "err", err)
			return make([]api.ImpactedCmCs, 0), err
		}
		configMapModels = sliceUtil.NewSliceFromFuncExec(configMapAppModels, func(model *configMapRepository.ConfigMapAppModel) configMapRepository.ConfigModel {
			return model
		})
	}
	configMapImpactedObjects := make([]api.ImpactedCmCs, 0, len(configMapModels))
	for _, configMapAppModel := range configMapModels {
		var finalConfigMapNames []string
		configMapNames := gjson.Get(configMapAppModel.GetConfigMapData(), "maps.#.name")
		for _, configMapName := range configMapNames.Array() {
			// Filter the config map models based on the includes and excludes patterns
			if v1beta2Util.ShouldIncludeCmCsObject(configMapName.String(), scope) {
				finalConfigMapNames = append(finalConfigMapNames, configMapName.String())
			}
		}
		if len(finalConfigMapNames) != 0 {
			appDetailsById, err := impl.appRepository.FindById(configMapAppModel.GetAppId())
			if err != nil {
				impl.logger.Errorw("error in fetching app details by id", "err", err, "appId", configMapAppModel.GetAppId())
				return nil, err
			}
			configMapImpactedObject := api.ImpactedCmCs{
				AppId:   configMapAppModel.GetAppId(),
				AppName: appDetailsById.AppName,
				Names:   finalConfigMapNames,
			}
			if scope.GetEnvId() > 0 {
				configMapImpactedObject.EnvId = scope.GetEnvId()
				configMapImpactedObject.EnvName = scope.GetEnvName()
			}
			configMapImpactedObjects = append(configMapImpactedObjects, configMapImpactedObject)
		}
	}
	return configMapImpactedObjects, nil
}

func (impl *ServiceImpl) GetImpactedDeploymentTemplates(scope *api.DeploymentTemplateScope) ([]api.ImpactedDeploymentTemplate, error) {
	if scope == nil {
		impl.logger.Infow("deployment template scope is empty, returning empty list")
		return make([]api.ImpactedDeploymentTemplate, 0), nil
	}
	var impactedAppObjects []*app.App
	var err error
	if scope.GetEnvId() > 0 {
		impactedAppObjects, err = impl.bulkEditRepository.FindImpactedAppsForEnvTemplates(scope.GetAppIds(), scope.ChartRefIds, scope.GetEnvId())
		if err != nil {
			impl.logger.Errorw("error in fetching bulk app names for env", "envId", scope.GetEnvId(), "err", err)
			return nil, err
		}
	} else {
		impactedAppObjects, err = impl.bulkEditRepository.FindImpactedAppsForBaseTemplates(scope.GetAppIds(), scope.ChartRefIds)
		if err != nil {
			impl.logger.Errorw("error in fetching bulk app names for global", "err", err)
			return make([]api.ImpactedDeploymentTemplate, 0), err
		}
	}
	deploymentTemplateImpactedObjects := make([]api.ImpactedDeploymentTemplate, 0, len(impactedAppObjects))
	for _, appModel := range impactedAppObjects {
		deploymentTemplateImpactedObject := api.ImpactedDeploymentTemplate{
			AppId:   appModel.Id,
			AppName: appModel.AppName,
		}
		if scope.GetEnvId() > 0 {
			deploymentTemplateImpactedObject.EnvId = scope.GetEnvId()
			deploymentTemplateImpactedObject.EnvName = scope.GetEnvName()
		}
		deploymentTemplateImpactedObjects = append(deploymentTemplateImpactedObjects, deploymentTemplateImpactedObject)
	}
	return deploymentTemplateImpactedObjects, nil
}

func (impl *ServiceImpl) CanUpdateConfigResource(ctx context.Context, resourceKind approvalBean.ApprovalFor, appId, envId int, userMetadata *userBean.UserMetadata) error {
	configProtectionEnabled, err := impl.configProtectionEnabled(ctx, resourceKind, appId, envId, userMetadata)
	if err != nil {
		impl.logger.Errorw("error in getting if config protection is enabled or not", "resourceKind", resourceKind, "appId", appId, "envId", envId, "err", err)
		return err
	}
	if configProtectionEnabled {
		impl.logger.Warn("not applying json patch, config protection enabled")
		return bulkEditErr.ErrConfigProtectionEnabled
	}
	return nil
}

func (impl *ServiceImpl) BulkEditEnvDeploymentTemplate(ctx *ctxUtil.RequestCtx, dtScope *api.DeploymentTemplateScope, dtSpec *api.DeploymentTemplateSpec,
	userMetadata *userBean.UserMetadata, dtBulkEditResponse *api.DeploymentTemplateResponse) *api.DeploymentTemplateResponse {
	if dtScope == nil || dtScope.GetEnvId() == 0 {
		return dtBulkEditResponse
	}
	switch dtSpec.Operation.Action {
	case api.DeploymentTemplateOperationActionUpdate:
		return impl.updateEnvDeploymentTemplate(ctx, dtScope, dtSpec, userMetadata, dtBulkEditResponse)
	default:
		message := fmt.Sprintf("Invalid operation action %q for deployment template bulk edit, please check and try again", dtSpec.Operation.Action)
		dtBulkEditResponse.Message = append(dtBulkEditResponse.Message, message)
		return dtBulkEditResponse
	}
}

func (impl *ServiceImpl) BulkEditBaseDeploymentTemplate(ctx *ctxUtil.RequestCtx, dtScope *api.DeploymentTemplateScope, dtSpec *api.DeploymentTemplateSpec,
	userMetadata *userBean.UserMetadata, dtBulkEditResponse *api.DeploymentTemplateResponse) *api.DeploymentTemplateResponse {
	if dtScope == nil || !dtScope.GetIncludeBaseConfig() {
		return dtBulkEditResponse
	}
	switch dtSpec.Operation.Action {
	case api.DeploymentTemplateOperationActionUpdate:
		return impl.updateBaseDeploymentTemplate(ctx, dtScope, dtSpec, userMetadata, dtBulkEditResponse)
	default:
		message := fmt.Sprintf("Invalid operation action %q for deployment template bulk edit, please check and try again", dtSpec.Operation.Action)
		dtBulkEditResponse.Message = append(dtBulkEditResponse.Message, message)
		return dtBulkEditResponse
	}
}

func (impl *ServiceImpl) BulkEditEnvConfigMap(ctx *ctxUtil.RequestCtx, configMapScope *api.ConfigScope, configMapSpec *api.CmCsSpec,
	userMetadata *userBean.UserMetadata, configMapBulkEditResponse *api.CmCsResponse) *api.CmCsResponse {
	if configMapScope == nil || configMapScope.GetEnvId() == 0 {
		return configMapBulkEditResponse
	}
	switch configMapSpec.Operation.Action {
	case api.CmCsOperationActionCreate:
		return impl.createEnvConfigMap(ctx, configMapScope, configMapSpec, userMetadata, configMapBulkEditResponse)
	case api.CmCsOperationActionUpdate:
		return impl.updateEnvConfigMap(ctx, configMapScope, configMapSpec, userMetadata, configMapBulkEditResponse)
	case api.CmCsOperationActionDelete:
		return impl.deleteEnvConfigMap(ctx, configMapScope, configMapSpec, userMetadata, configMapBulkEditResponse)
	default:
		message := fmt.Sprintf("Invalid operation action %q for config map bulk edit, please check and try again", configMapSpec.Operation.Action)
		configMapBulkEditResponse.Message = append(configMapBulkEditResponse.Message, message)
		return configMapBulkEditResponse
	}
}

func (impl *ServiceImpl) BulkEditBaseConfigMap(ctx *ctxUtil.RequestCtx, configMapScope *api.ConfigScope, configMapSpec *api.CmCsSpec,
	userMetadata *userBean.UserMetadata, configMapBulkEditResponse *api.CmCsResponse) *api.CmCsResponse {
	if configMapScope == nil || !configMapScope.GetIncludeBaseConfig() {
		return configMapBulkEditResponse
	}
	switch configMapSpec.Operation.Action {
	case api.CmCsOperationActionCreate:
		return impl.createBaseConfigMap(ctx, configMapScope, configMapSpec, userMetadata, configMapBulkEditResponse)
	case api.CmCsOperationActionUpdate:
		return impl.updateBaseConfigMap(ctx, configMapScope, configMapSpec, userMetadata, configMapBulkEditResponse)
	case api.CmCsOperationActionDelete:
		return impl.deleteBaseConfigMap(ctx, configMapScope, configMapSpec, userMetadata, configMapBulkEditResponse)
	default:
		message := fmt.Sprintf("Invalid operation action %q for config map bulk edit, please check and try again", configMapSpec.Operation.Action)
		configMapBulkEditResponse.Message = append(configMapBulkEditResponse.Message, message)
		return configMapBulkEditResponse
	}
}

func (impl *ServiceImpl) BulkEditEnvSecret(ctx *ctxUtil.RequestCtx, secretScope *api.ConfigScope, secretSpec *api.CmCsSpec,
	userMetadata *userBean.UserMetadata, secretBulkEditResponse *api.CmCsResponse) *api.CmCsResponse {
	if secretScope == nil || secretScope.GetEnvId() == 0 {
		return secretBulkEditResponse
	}
	switch secretSpec.Operation.Action {
	case api.CmCsOperationActionCreate:
		return impl.createEnvSecret(ctx, secretScope, secretSpec, userMetadata, secretBulkEditResponse)
	case api.CmCsOperationActionUpdate:
		return impl.updateEnvSecret(ctx, secretScope, secretSpec, userMetadata, secretBulkEditResponse)
	case api.CmCsOperationActionDelete:
		return impl.deleteEnvSecret(ctx, secretScope, secretSpec, userMetadata, secretBulkEditResponse)
	default:
		message := fmt.Sprintf("Invalid operation action %q for secret bulk edit, please check and try again", secretSpec.Operation.Action)
		secretBulkEditResponse.Message = append(secretBulkEditResponse.Message, message)
		return secretBulkEditResponse
	}
}

func (impl *ServiceImpl) BulkEditBaseSecret(ctx *ctxUtil.RequestCtx, secretScope *api.ConfigScope, secretSpec *api.CmCsSpec,
	userMetadata *userBean.UserMetadata, secretBulkEditResponse *api.CmCsResponse) *api.CmCsResponse {
	if secretScope == nil || !secretScope.GetIncludeBaseConfig() {
		return secretBulkEditResponse
	}
	switch secretSpec.Operation.Action {
	case api.CmCsOperationActionCreate:
		return impl.createBaseSecret(ctx, secretScope, secretSpec, userMetadata, secretBulkEditResponse)
	case api.CmCsOperationActionUpdate:
		return impl.updateBaseSecret(ctx, secretScope, secretSpec, userMetadata, secretBulkEditResponse)
	case api.CmCsOperationActionDelete:
		return impl.deleteBaseSecret(ctx, secretScope, secretSpec, userMetadata, secretBulkEditResponse)
	default:
		message := fmt.Sprintf("Invalid operation action %q for secret bulk edit, please check and try again", secretSpec.Operation.Action)
		secretBulkEditResponse.Message = append(secretBulkEditResponse.Message, message)
		return secretBulkEditResponse
	}
}

func (impl *ServiceImpl) getApplicationScopeByChartId(chartId int) (*api.ApplicationScope, error) {
	appDetailsByChart, err := impl.bulkEditRepository.FindApplicationMinByChartId(chartId)
	if err != nil {
		impl.logger.Errorw("error in fetching app details by chart id", "chartId", chartId, "err", err)
		return nil, err
	}
	return v1beta2Adapter.GetApplicationScope(appDetailsByChart), nil
}

func (impl *ServiceImpl) updateConfigScopeFor(appId int, configMapScope *api.ConfigScope) (*api.ConfigScope, error) {
	newAppScope, err := impl.getApplicationScopeById(appId)
	if err != nil {
		impl.logger.Errorw("error in fetching app details by app id", "appId", appId, "err", err)
		return nil, err
	}
	configMapScope = configMapScope.SetAppDetails(appId, newAppScope)
	return configMapScope, nil
}

func (impl *ServiceImpl) getApplicationScopeById(appId int) (*api.ApplicationScope, error) {
	appDetailsByChart, err := impl.bulkEditRepository.FindApplicationMinById(appId)
	if err != nil {
		impl.logger.Errorw("error in fetching app details by app id", "appId", appId, "err", err)
		return nil, err
	}
	return v1beta2Adapter.GetApplicationScope(appDetailsByChart), nil
}

func (impl *ServiceImpl) updateEnvDeploymentTemplate(ctx *ctxUtil.RequestCtx, dtScope *api.DeploymentTemplateScope, dtSpec *api.DeploymentTemplateSpec,
	userMetadata *userBean.UserMetadata, dtBulkEditResponse *api.DeploymentTemplateResponse) *api.DeploymentTemplateResponse {
	switch dtSpec.Operation.Field {
	case api.Values:
		return impl.patchEnvDeploymentTemplate(ctx, dtScope, dtSpec, userMetadata, dtBulkEditResponse)
	case api.Version:
		return impl.bumpEnvDeploymentTemplateVersion(ctx, dtScope, dtSpec, userMetadata, dtBulkEditResponse)
	default:
		message := fmt.Sprintf("Invalid operation field %q for deployment template bulk edit, please check and try again", dtSpec.Operation.Field)
		dtBulkEditResponse.Message = append(dtBulkEditResponse.Message, message)
		return dtBulkEditResponse
	}
}

func (impl *ServiceImpl) bumpEnvDeploymentTemplateVersion(ctx *ctxUtil.RequestCtx, dtScope *api.DeploymentTemplateScope, dtSpec *api.DeploymentTemplateSpec,
	userMetadata *userBean.UserMetadata, dtBulkEditResponse *api.DeploymentTemplateResponse) *api.DeploymentTemplateResponse {
	for _, appId := range dtScope.GetAppIds() {
		if _, ok := dtScope.GetAppDetails(appId); !ok {
			// If app details are not present in the scope, fetch them
			// Ideally, this should not happen as the scope should have been constructed with all necessary app details
			newAppScope, err := impl.getApplicationScopeByChartId(appId)
			if err != nil {
				impl.logger.Errorw("error in fetching app details by app id", "appId", appId, "err", err)
				message := fmt.Sprintf("Error in fetching app details for appId: %d, error: %s", appId, err.Error())
				dtBulkEditResponse.Message = append(dtBulkEditResponse.Message, message)
				continue
			}
			dtScope = dtScope.SetAppDetails(appId, newAppScope)
		}
		appName := dtScope.GetAppName(appId)
		envConfigProperties, err := impl.chartService.GetLatestEnvironmentProperties(appId, dtScope.GetEnvId())
		if err != nil && !errors.Is(err, pg.ErrNoRows) {
			impl.logger.Errorw("error in fetching latest environment properties", "appId", appId, "envId", dtScope.GetEnvId(), "err", err)
			errMsg := fmt.Sprintf("Unable to fetch latest environment properties for %q; error: %s", appName, err.Error())
			dtBulkEditResponse.Failure = append(dtBulkEditResponse.Failure, v1beta2Adapter.GetAppEnvDetail(appName, dtScope.GetEnvName(), errMsg))
			continue
		}
		if envConfigProperties == nil || !envConfigProperties.IsOverride {
			continue
		}
		currentChartRef, err := impl.chartRefDbReadService.FindById(envConfigProperties.ChartRefId)
		if err != nil {
			impl.logger.Errorw("error in fetching current chart ref for app", "chartRefId", envConfigProperties.ChartRefId, "err", err)
			errMsg := fmt.Sprintf("Unable to fetch current chart ref for %q; error: %s", appName, err.Error())
			dtBulkEditResponse.Failure = append(dtBulkEditResponse.Failure, v1beta2Adapter.GetAppEnvDetail(appName, dtScope.GetEnvName(), errMsg))
			continue
		}
		targetChartRef, err := impl.chartRefDbReadService.FindByVersionAndName(dtSpec.Operation.ChartVersion, currentChartRef.Name)
		if err != nil && !errors.Is(err, pg.ErrNoRows) {
			impl.logger.Errorw("error in fetching target chart ref by version and name", "version", dtSpec.Operation.ChartVersion, "name", currentChartRef.Name, "err", err)
			errMsg := fmt.Sprintf("Unable to fetch target chart ref for %q with version %q; error: %s", appName, dtSpec.Operation.ChartVersion, err.Error())
			dtBulkEditResponse.Failure = append(dtBulkEditResponse.Failure, v1beta2Adapter.GetAppEnvDetail(appName, dtScope.GetEnvName(), errMsg))
			continue
		} else if errors.Is(err, pg.ErrNoRows) {
			// If the chart ref is not found, it means the version is not available in the system
			// We should not proceed with the update
			errMsg := fmt.Sprintf("Chart version %q not found for app %q, please check and try again", dtSpec.Operation.ChartVersion, appName)
			dtBulkEditResponse.Failure = append(dtBulkEditResponse.Failure, v1beta2Adapter.GetAppEnvDetail(appName, dtScope.GetEnvName(), errMsg))
			continue
		}
		if validationErr := impl.isEnvDTConfigUpdateAllowed(ctx, appId, dtScope.GetEnvId(), userMetadata); validationErr != nil {
			dtBulkEditResponse.Failure = append(dtBulkEditResponse.Failure, v1beta2Adapter.GetAppEnvDetail(appName, dtScope.GetEnvName(), validationErr.Error()))
			continue
		}
		request := &chartsBean.ChartRefChangeRequest{
			AppId:            appId,
			EnvId:            dtScope.GetEnvId(),
			TargetChartRefId: targetChartRef.Id,
		}
		var envMetrics bool
		envConfigProperties, envMetrics, err = impl.deploymentTemplateValidationService.ValidateChangeChartRefRequest(ctx, envConfigProperties, request)
		if err != nil {
			impl.logger.Errorw("error in validating change chart ref request", "appId", appId, "envId", dtScope.GetEnvId(), "err", err)
			errMsg := fmt.Sprintf("Validation error in updating env deployment template override for application %q and environment %q; error: %s", appName, dtScope.GetEnvName(), err.Error())
			dtBulkEditResponse.Failure = append(dtBulkEditResponse.Failure, v1beta2Adapter.GetAppEnvDetail(appName, dtScope.GetEnvName(), errMsg))
			continue
		}
		envConfigProperties.UserId = ctx.GetUserId()
		if envConfigProperties.MergeStrategy == models.MERGE_STRATEGY_PATCH {
			envConfigProperties.EnvOverrideValues = envConfigProperties.EnvOverridePatchValues
			envConfigProperties.EnvOverridePatchValues = nil
		}
		request.EnvConfigProperties = envConfigProperties
		request.EnvMetrics = envMetrics
		request.UserId = ctx.GetUserId()
		_, err = impl.propertiesConfigService.ChangeChartRefForEnvConfigOverride(ctx, request, ctx.GetUserId(), ctx.GetToken())
		if err != nil {
			impl.logger.Errorw("error in updating env deployment template override version", "appId", appId, "envId", dtScope.GetEnvId(), "err", err)
			errMsg := fmt.Sprintf("Unable to update env deployment template override version for %q; error: %s", appName, err.Error())
			dtBulkEditResponse.Failure = append(dtBulkEditResponse.Failure, v1beta2Adapter.GetAppEnvDetail(appName, dtScope.GetEnvName(), errMsg))
			continue
		}
		bulkEditSuccessResponse := v1beta2Adapter.GetAppEnvDetail(appName, dtScope.GetEnvName(), api.UpdatedEnvOverride)
		dtBulkEditResponse.Successful = append(dtBulkEditResponse.Successful, bulkEditSuccessResponse)
	}
	return dtBulkEditResponse
}

func (impl *ServiceImpl) patchEnvDeploymentTemplate(ctx *ctxUtil.RequestCtx, dtScope *api.DeploymentTemplateScope, dtSpec *api.DeploymentTemplateSpec,
	userMetadata *userBean.UserMetadata, dtBulkEditResponse *api.DeploymentTemplateResponse) *api.DeploymentTemplateResponse {
	dtPatchJson := []byte(dtSpec.Operation.PatchJson)
	dtPatch, err := jsonpatch.DecodePatch(dtPatchJson)
	if err != nil {
		impl.logger.Errorw("error in decoding JSON patch", "dtPatchJson", dtPatchJson, "err", err)
		dtBulkEditResponse.Message = append(dtBulkEditResponse.Message, "The patch string you entered seems wrong, please check and try again")
		return dtBulkEditResponse
	}
	envId := dtScope.GetEnvId()
	envCharts, err := impl.bulkEditRepository.FindBulkChartsByAppIdsAndEnvId(dtScope.GetAppIds(), envId)
	if err != nil {
		impl.logger.Errorw("error in fetching charts(for env) by app name substring", "err", err)
		dtBulkEditResponse.Message = append(dtBulkEditResponse.Message, fmt.Sprintf("Unable to bulk update apps for envId = %d , %s", envId, err.Error()))
		return dtBulkEditResponse
	}
	if len(envCharts) == 0 {
		dtBulkEditResponse.Message = append(dtBulkEditResponse.Message, fmt.Sprintf("No matching deployment templates for environmet %q", dtScope.GetEnvName()))
		return dtBulkEditResponse
	}
	dtBulkEditResponse = impl.bulkPatchEnvDeploymentTemplate(ctx, dtScope, envCharts, dtPatch, userMetadata, dtBulkEditResponse)
	return dtBulkEditResponse
}

func (impl *ServiceImpl) updateBaseDeploymentTemplate(ctx *ctxUtil.RequestCtx, dtScope *api.DeploymentTemplateScope, dtSpec *api.DeploymentTemplateSpec,
	userMetadata *userBean.UserMetadata, dtBulkEditResponse *api.DeploymentTemplateResponse) *api.DeploymentTemplateResponse {
	switch dtSpec.Operation.Field {
	case api.Values:
		return impl.patchBaseDeploymentTemplate(ctx, dtScope, dtSpec, userMetadata, dtBulkEditResponse)
	case api.Version:
		return impl.bumpBaseDeploymentTemplateVersion(ctx, dtScope, dtSpec, userMetadata, dtBulkEditResponse)
	default:
		message := fmt.Sprintf("Invalid operation field %q for deployment template bulk edit, please check and try again", dtSpec.Operation.Field)
		dtBulkEditResponse.Message = append(dtBulkEditResponse.Message, message)
		return dtBulkEditResponse
	}
}

func (impl *ServiceImpl) bumpBaseDeploymentTemplateVersion(ctx *ctxUtil.RequestCtx, dtScope *api.DeploymentTemplateScope, dtSpec *api.DeploymentTemplateSpec,
	userMetadata *userBean.UserMetadata, dtBulkEditResponse *api.DeploymentTemplateResponse) *api.DeploymentTemplateResponse {
	for _, appId := range dtScope.GetAppIds() {
		if _, ok := dtScope.GetAppDetails(appId); !ok {
			// If app details are not present in the scope, fetch them
			// Ideally, this should not happen as the scope should have been constructed with all necessary app details
			newAppScope, err := impl.getApplicationScopeByChartId(appId)
			if err != nil {
				impl.logger.Errorw("error in fetching app details by app id", "appId", appId, "err", err)
				message := fmt.Sprintf("Error in fetching app details for appId: %d, error: %s", appId, err.Error())
				dtBulkEditResponse.Message = append(dtBulkEditResponse.Message, message)
				continue
			}
			dtScope = dtScope.SetAppDetails(appId, newAppScope)
		}
		appName := dtScope.GetAppName(appId)
		currentChartRef, err := impl.chartRefDbReadService.FetchInfoOfChartConfiguredInApp(appId)
		if err != nil {
			impl.logger.Errorw("error in fetching current chart ref for app", "appId", appId, "err", err)
			errMsg := fmt.Sprintf("Unable to fetch current chart ref for %q; error: %s", appName, err.Error())
			dtBulkEditResponse.Failure = append(dtBulkEditResponse.Failure, v1beta2Adapter.GetAppDetail(appName, errMsg))
			continue
		}
		targetChartRef, err := impl.chartRefDbReadService.FindByVersionAndName(dtSpec.Operation.ChartVersion, currentChartRef.Name)
		if err != nil && !errors.Is(err, pg.ErrNoRows) {
			impl.logger.Errorw("error in fetching target chart ref by version and name", "version", dtSpec.Operation.ChartVersion, "name", currentChartRef.Name, "err", err)
			errMsg := fmt.Sprintf("Unable to fetch target chart ref for %q with version %q; error: %s", appName, dtSpec.Operation.ChartVersion, err.Error())
			dtBulkEditResponse.Failure = append(dtBulkEditResponse.Failure, v1beta2Adapter.GetAppDetail(appName, errMsg))
			continue
		} else if errors.Is(err, pg.ErrNoRows) {
			// If the chart ref is not found, it means the version is not available in the system
			// We should not proceed with the update
			errMsg := fmt.Sprintf("Chart version %q not found for app %q, please check and try again", dtSpec.Operation.ChartVersion, appName)
			dtBulkEditResponse.Failure = append(dtBulkEditResponse.Failure, v1beta2Adapter.GetAppDetail(appName, errMsg))
			continue
		}
		if validationErr := impl.isBaseDTConfigUpdateAllowed(ctx, appId, userMetadata); validationErr != nil {
			dtBulkEditResponse.Failure = append(dtBulkEditResponse.Failure, v1beta2Adapter.GetAppDetail(appName, validationErr.Error()))
			continue
		}
		request := &chartsBean.TemplateRefChangeRequest{
			AppId:            appId,
			TargetChartRefId: targetChartRef.Id,
		}
		request, err = impl.deploymentTemplateValidationService.ValidateAndGetTemplateChangeRefRequest(request)
		if err != nil {
			impl.logger.Errorw("error in validating and getting template change ref request", "request", request, "err", err)
			errMsg := fmt.Sprintf("Validation error in updating base deployment template for %q; error: %s", appName, err.Error())
			dtBulkEditResponse.Failure = append(dtBulkEditResponse.Failure, v1beta2Adapter.GetAppDetail(appName, errMsg))
			continue
		}
		err = impl.chartService.ChangeApplicationTemplateChart(ctx, ctx.GetUserId(), request)
		if err != nil {
			impl.logger.Errorw("service err, ChangeApplicationTemplateChart", "request", request, "err", err)
			errMsg := fmt.Sprintf("Unable to update base deployment template version for %q; error: %s", appName, err.Error())
			dtBulkEditResponse.Failure = append(dtBulkEditResponse.Failure, v1beta2Adapter.GetAppDetail(appName, errMsg))
			continue
		}
		bulkEditSuccessResponse := v1beta2Adapter.GetAppDetail(appName, api.UpdatedBaseConfig)
		dtBulkEditResponse.Successful = append(dtBulkEditResponse.Successful, bulkEditSuccessResponse)
	}
	return dtBulkEditResponse
}

func (impl *ServiceImpl) patchBaseDeploymentTemplate(ctx *ctxUtil.RequestCtx, dtScope *api.DeploymentTemplateScope, dtSpec *api.DeploymentTemplateSpec,
	userMetadata *userBean.UserMetadata, dtBulkEditResponse *api.DeploymentTemplateResponse) *api.DeploymentTemplateResponse {
	dtPatchJson := []byte(dtSpec.Operation.PatchJson)
	dtPatch, err := jsonpatch.DecodePatch(dtPatchJson)
	if err != nil {
		impl.logger.Errorw("error in decoding JSON patch", "dtPatchJson", dtPatchJson, "err", err)
		dtBulkEditResponse.Message = append(dtBulkEditResponse.Message, "The patch string you entered seems wrong, please check and try again")
		return dtBulkEditResponse
	}
	charts, err := impl.bulkEditRepository.FindBulkChartsByAppIds(dtScope.GetAppIds())
	if err != nil {
		impl.logger.Errorw("error in fetching charts by app ids", "appIds", dtScope.GetAppIds(), "err", err)
		message := fmt.Sprintf("Unable to get base deployment templates, error: %s", err.Error())
		dtBulkEditResponse.Message = append(dtBulkEditResponse.Message, message)
		return dtBulkEditResponse
	}
	if len(charts) == 0 {
		dtBulkEditResponse.Message = append(dtBulkEditResponse.Message, "No matching deployment templates to be updated at base level")
		return dtBulkEditResponse
	}
	dtBulkEditResponse = impl.bulkPatchBaseDeploymentTemplate(ctx, dtScope, charts, dtPatch, userMetadata, dtBulkEditResponse)
	return dtBulkEditResponse
}

func (impl *ServiceImpl) bulkPatchBaseDeploymentTemplate(ctx *ctxUtil.RequestCtx, dtScope *api.DeploymentTemplateScope, charts []*chartRepoRepository.Chart,
	dtPatch jsonpatch.Patch, userMetadata *userBean.UserMetadata, dtBulkEditResponse *api.DeploymentTemplateResponse) *api.DeploymentTemplateResponse {
	for _, chart := range charts {
		appId := chart.AppId
		if _, ok := dtScope.GetAppDetails(appId); !ok {
			// If app details are not present in the scope, fetch them
			// Ideally, this should not happen as the scope should have been constructed with all necessary app details
			newAppScope, err := impl.getApplicationScopeByChartId(chart.Id)
			if err != nil {
				impl.logger.Errorw("error in fetching app details by chart id", "chartId", chart.Id, "err", err)
				message := fmt.Sprintf("Error in fetching app details for appId: %d, error: %s", appId, err.Error())
				dtBulkEditResponse.Message = append(dtBulkEditResponse.Message, message)
				continue
			}
			dtScope = dtScope.SetAppDetails(appId, newAppScope)
		}
		appName := dtScope.GetAppName(appId)
		if validationErr := impl.isBaseDTConfigUpdateAllowed(ctx, appId, userMetadata); validationErr != nil {
			dtBulkEditResponse.Failure = append(dtBulkEditResponse.Failure, v1beta2Adapter.GetAppDetail(appName, validationErr.Error()))
			continue
		}
		modifiedValuesYml, err := jsonUtil.ApplyJsonPatch(dtPatch, chart.Values)
		if err != nil {
			impl.logger.Errorw("error in applying JSON patch to chart.Values", "err", err)
			bulkEditFailedResponse := v1beta2Adapter.GetAppDetail(appName, fmt.Sprintf("Error in applying JSON patch : %s", err.Error()))
			dtBulkEditResponse.Failure = append(dtBulkEditResponse.Failure, bulkEditFailedResponse)
			continue
		}
		modifiedGlobalOverrideYml, err := jsonUtil.ApplyJsonPatch(dtPatch, chart.GlobalOverride)
		if err != nil {
			impl.logger.Errorw("error in applying JSON patch to GlobalOverride", "err", err)
			bulkEditFailedResponse := v1beta2Adapter.GetAppDetail(appName, fmt.Sprintf("Error in applying JSON patch : %s", err.Error()))
			dtBulkEditResponse.Failure = append(dtBulkEditResponse.Failure, bulkEditFailedResponse)
			continue
		}
		err = impl.bulkEditRepository.BulkUpdateChartsValuesYamlAndGlobalOverrideById(chart.Id, modifiedValuesYml, modifiedGlobalOverrideYml)
		if err != nil {
			impl.logger.Errorw("error in bulk updating charts", "err", err)
			bulkEditFailedResponse := v1beta2Adapter.GetAppDetail(appName, fmt.Sprintf("Error in updating in db : %s", err.Error()))
			dtBulkEditResponse.Failure = append(dtBulkEditResponse.Failure, bulkEditFailedResponse)
			continue
		}
		bulkEditSuccessResponse := v1beta2Adapter.GetAppDetail(appName, api.UpdatedBaseConfig)
		dtBulkEditResponse.Successful = append(dtBulkEditResponse.Successful, bulkEditSuccessResponse)
		chart.GlobalOverride = modifiedGlobalOverrideYml
		chart.Values = modifiedValuesYml
		impl.updateBaseDeploymentTemplateHistory(chart)
	}
	return dtBulkEditResponse
}

func (impl *ServiceImpl) bulkPatchEnvDeploymentTemplate(ctx *ctxUtil.RequestCtx, dtScope *api.DeploymentTemplateScope, envCharts []*chartConfig.EnvConfigOverride,
	dtPatch jsonpatch.Patch, userMetadata *userBean.UserMetadata, dtBulkEditResponse *api.DeploymentTemplateResponse) *api.DeploymentTemplateResponse {
	for _, envChart := range envCharts {
		appId := envChart.Chart.AppId
		if _, ok := dtScope.GetAppDetails(appId); !ok {
			// If app details are not present in the scope, fetch them
			// Ideally, this should not happen as the scope should have been constructed with all necessary app details
			newAppScope, err := impl.getApplicationScopeByChartId(envChart.ChartId)
			if err != nil {
				impl.logger.Errorw("error in fetching app details by chart id", "chartId", envChart.ChartId, "err", err)
				message := fmt.Sprintf("Error in fetching app details for appId: %d, error: %s", appId, err.Error())
				dtBulkEditResponse.Message = append(dtBulkEditResponse.Message, message)
				continue
			}
			dtScope = dtScope.SetAppDetails(appId, newAppScope)
		}
		appName := dtScope.GetAppName(appId)
		if validationErr := impl.isEnvDTConfigUpdateAllowed(ctx, appId, envChart.TargetEnvironment, userMetadata); validationErr != nil {
			dtBulkEditResponse.Failure = append(dtBulkEditResponse.Failure, v1beta2Adapter.GetAppEnvDetail(appName, dtScope.GetEnvName(), validationErr.Error()))
			continue
		}
		modified, err := jsonUtil.ApplyJsonPatch(dtPatch, envChart.EnvOverrideValues)
		if err != nil {
			impl.logger.Errorw("error in applying JSON patch", "err", err)
			bulkEditFailedResponse := v1beta2Adapter.GetAppEnvDetail(appName, dtScope.GetEnvName(), fmt.Sprintf("Error in applying JSON patch : %s", err.Error()))
			dtBulkEditResponse.Failure = append(dtBulkEditResponse.Failure, bulkEditFailedResponse)
			continue
		}
		err = impl.bulkEditRepository.BulkUpdateChartsEnvYamlOverrideById(envChart.Id, modified)
		if err != nil {
			impl.logger.Errorw("error in bulk updating charts", "err", err)
			bulkEditFailedResponse := v1beta2Adapter.GetAppEnvDetail(appName, dtScope.GetEnvName(), fmt.Sprintf("Error in updating in db : %s", err.Error()))
			dtBulkEditResponse.Failure = append(dtBulkEditResponse.Failure, bulkEditFailedResponse)
			continue
		}
		bulkEditSuccessResponse := v1beta2Adapter.GetAppEnvDetail(appName, dtScope.GetEnvName(), api.UpdatedEnvOverride)
		dtBulkEditResponse.Successful = append(dtBulkEditResponse.Successful, bulkEditSuccessResponse)
		envChart.EnvOverrideValues = modified
		impl.updateEnvDeploymentTemplateHistory(envChart)
	}
	return dtBulkEditResponse
}

func (impl *ServiceImpl) deleteEnvConfigMap(ctx *ctxUtil.RequestCtx, configMapScope *api.ConfigScope, configMapSpec *api.CmCsSpec,
	userMetadata *userBean.UserMetadata, configMapBulkEditResponse *api.CmCsResponse) *api.CmCsResponse {
	for _, appId := range configMapScope.GetAppIds() {
		if _, ok := configMapScope.GetAppDetails(appId); !ok {
			// If app details are not present in the scope, fetch them
			// Ideally, this should not happen as the scope should have been constructed with all necessary app details
			var err error
			configMapScope, err = impl.updateConfigScopeFor(appId, configMapScope)
			if err != nil {
				impl.logger.Errorw("error in fetching app details by app id", "appId", appId, "err", err)
				message := fmt.Sprintf("Error in fetching app details for appId: %d, error: %s", appId, err.Error())
				configMapBulkEditResponse.Message = append(configMapBulkEditResponse.Message, message)
				continue
			}
		}
		appName := configMapScope.GetAppName(appId)
		configMapEnvModels, err := impl.bulkEditRepository.FindBulkCmAppModelsByAppIdsAndEnvId(configMapScope.GetAppIds(), configMapScope.GetEnvId())
		if err != nil {
			impl.logger.Errorw("error in fetching bulk app model for env", "err", err)
			message := fmt.Sprintf("Unable to get env level configmaps for %q, error: %s", configMapScope.GetEnvName(), err.Error())
			configMapBulkEditResponse.Message = append(configMapBulkEditResponse.Message, message)
			return configMapBulkEditResponse
		}
		if len(configMapEnvModels) == 0 {
			configMapBulkEditResponse.Message = append(configMapBulkEditResponse.Message, fmt.Sprintf("No matching configmaps to be updated for environment %q", configMapScope.GetEnvName()))
			return configMapBulkEditResponse
		}
		// Check if the user has permission to update the config map for the given app and env
		if validationErr := impl.isEnvCMConfigUpdateAllowed(ctx, appId, configMapScope.GetEnvId(), userMetadata); validationErr != nil {
			configMapBulkEditResponse.Failure = append(configMapBulkEditResponse.Failure, v1beta2Adapter.GetObjectsWithAppEnvDetail(appName, configMapScope.GetEnvName(), []string{}, validationErr.Error()))
			continue
		}
		failed := false
		for _, configMapEnvModel := range configMapEnvModels {
			_, err = impl.configMapService.CMEnvironmentDelete(configMapSpec.Operation.Value, configMapEnvModel.Id, userMetadata.UserId)
			if err != nil {
				impl.logger.Errorw("error in deleting configmap for env", "appId", appId, "envId", configMapScope.GetEnvId(), "err", err)
				message := fmt.Sprintf("Unable to delete configmap for application %q in environment %q, error: %s", appName, configMapScope.GetEnvName(), err.Error())
				configMapBulkEditResponse.Failure = append(configMapBulkEditResponse.Failure, v1beta2Adapter.GetObjectsWithAppEnvDetail(appName, configMapScope.GetEnvName(), []string{}, message))
				failed = true
				continue
			}
		}
		if !failed {
			configMapBulkEditResponse.Successful = append(configMapBulkEditResponse.Successful,
				v1beta2Adapter.GetObjectsWithAppEnvDetail(appName, configMapScope.GetEnvName(),
					sliceUtil.GetSliceOf(configMapSpec.Operation.Value), api.DeletedEnvOverride))
		}
	}
	return configMapBulkEditResponse
}

func (impl *ServiceImpl) deleteBaseConfigMap(ctx *ctxUtil.RequestCtx, configMapScope *api.ConfigScope, configMapSpec *api.CmCsSpec,
	userMetadata *userBean.UserMetadata, configMapBulkEditResponse *api.CmCsResponse) *api.CmCsResponse {
	for _, appId := range configMapScope.GetAppIds() {
		if _, ok := configMapScope.GetAppDetails(appId); !ok {
			// If app details are not present in the scope, fetch them
			// Ideally, this should not happen as the scope should have been constructed with all necessary app details
			var err error
			configMapScope, err = impl.updateConfigScopeFor(appId, configMapScope)
			if err != nil {
				impl.logger.Errorw("error in fetching app details by app id", "appId", appId, "err", err)
				message := fmt.Sprintf("Error in fetching app details for appId: %d, error: %s", appId, err.Error())
				configMapBulkEditResponse.Message = append(configMapBulkEditResponse.Message, message)
				continue
			}
		}
		appName := configMapScope.GetAppName(appId)
		configMapEnvModels, err := impl.bulkEditRepository.FindBulkCmAppModelsByAppIds(configMapScope.GetAppIds())
		if err != nil {
			impl.logger.Errorw("error in fetching bulk app model for env", "err", err)
			message := fmt.Sprintf("Unable to get app level configmaps for %q, error: %s", configMapScope.GetEnvName(), err.Error())
			configMapBulkEditResponse.Message = append(configMapBulkEditResponse.Message, message)
			return configMapBulkEditResponse
		}
		if len(configMapEnvModels) == 0 {
			configMapBulkEditResponse.Message = append(configMapBulkEditResponse.Message, fmt.Sprintf("No matching configmaps to be updated"))
			return configMapBulkEditResponse
		}
		// Check if the user has permission to update the config map for the given app
		if validationErr := impl.isBaseCMConfigUpdateAllowed(ctx, appId, userMetadata); validationErr != nil {
			configMapBulkEditResponse.Failure = append(configMapBulkEditResponse.Failure, v1beta2Adapter.GetObjectsWithAppDetail(appName, []string{}, validationErr.Error()))
			continue
		}
		failed := false
		for _, configMapEnvModel := range configMapEnvModels {
			_, err = impl.configMapService.CMEnvironmentDelete(configMapSpec.Operation.Value, configMapEnvModel.Id, userMetadata.UserId)
			if err != nil {
				impl.logger.Errorw("error in deleting configmap for env", "appId", appId, "err", err)
				message := fmt.Sprintf("Unable to delete configmap for application %q, error: %s", appName, err.Error())
				configMapBulkEditResponse.Failure = append(configMapBulkEditResponse.Failure, v1beta2Adapter.GetObjectsWithAppDetail(appName, []string{}, message))
				failed = true
				continue
			}
		}
		if !failed {
			configMapBulkEditResponse.Successful = append(configMapBulkEditResponse.Successful,
				v1beta2Adapter.GetObjectsWithAppDetail(appName, sliceUtil.GetSliceOf(configMapSpec.Operation.Value),
					api.DeletedBaseConfig))
		}
	}
	return configMapBulkEditResponse
}

func (impl *ServiceImpl) createEnvConfigMap(ctx *ctxUtil.RequestCtx, configMapScope *api.ConfigScope, configMapSpec *api.CmCsSpec,
	userMetadata *userBean.UserMetadata, configMapBulkEditResponse *api.CmCsResponse) *api.CmCsResponse {
	for _, appId := range configMapScope.GetAppIds() {
		if _, ok := configMapScope.GetAppDetails(appId); !ok {
			// If app details are not present in the scope, fetch them
			// Ideally, this should not happen as the scope should have been constructed with all necessary app details
			var err error
			configMapScope, err = impl.updateConfigScopeFor(appId, configMapScope)
			if err != nil {
				impl.logger.Errorw("error in fetching app details by app id", "appId", appId, "err", err)
				message := fmt.Sprintf("Error in fetching app details for appId: %d, error: %s", appId, err.Error())
				configMapBulkEditResponse.Message = append(configMapBulkEditResponse.Message, message)
				continue
			}
		}
		appName := configMapScope.GetAppName(appId)
		// Check if the user has permission to update the config map for the given app and env
		if validationErr := impl.isEnvCMConfigUpdateAllowed(ctx, appId, configMapScope.GetEnvId(), userMetadata); validationErr != nil {
			configMapBulkEditResponse.Failure = append(configMapBulkEditResponse.Failure, v1beta2Adapter.GetObjectsWithAppEnvDetail(appName, configMapScope.GetEnvName(), []string{}, validationErr.Error()))
			continue
		}
		configData := &configMapBean.ConfigData{}
		err := json.Unmarshal([]byte(configMapSpec.Operation.Value), configData)
		if err != nil {
			impl.logger.Errorw("error in unmarshalling config data", "appId", appId, "envId", configMapScope.GetEnvId(), "err", err)
			message := fmt.Sprintf("Invalid config data for application %q and environment %q, error: %s", appName, configMapScope.GetEnvName(), err.Error())
			configMapBulkEditResponse.Message = append(configMapBulkEditResponse.Message, message)
			continue
		}
		configDataRequest := &configMapBean.ConfigDataRequest{
			AppId:         appId,
			EnvironmentId: configMapScope.GetEnvId(),
			ConfigData:    sliceUtil.GetSliceOf(configData),
			UserId:        userMetadata.UserId,
		}
		_, err = impl.configMapService.CMEnvironmentAddUpdate(configDataRequest)
		if err != nil {
			impl.logger.Errorw("error in creating configmap for env", "appId", appId, "envId", configMapScope.GetEnvId(), "err", err)
			message := fmt.Sprintf("Unable to create configmap for application %q in environmet %q, error: %s", appName, configMapScope.GetEnvName(), err.Error())
			configMapBulkEditResponse.Failure = append(configMapBulkEditResponse.Failure, v1beta2Adapter.GetObjectsWithAppEnvDetail(appName, configMapScope.GetEnvName(), []string{}, message))
		} else {
			configMapBulkEditResponse.Successful = append(configMapBulkEditResponse.Successful, v1beta2Adapter.GetObjectsWithAppEnvDetail(appName, configMapScope.GetEnvName(), sliceUtil.GetSliceOf(configData.Name), api.CreatedEnvOverride))
		}
	}
	return configMapBulkEditResponse
}

func (impl *ServiceImpl) createBaseConfigMap(ctx *ctxUtil.RequestCtx, configMapScope *api.ConfigScope, configMapSpec *api.CmCsSpec,
	userMetadata *userBean.UserMetadata, configMapBulkEditResponse *api.CmCsResponse) *api.CmCsResponse {
	for _, appId := range configMapScope.GetAppIds() {
		if _, ok := configMapScope.GetAppDetails(appId); !ok {
			// If app details are not present in the scope, fetch them
			// Ideally, this should not happen as the scope should have been constructed with all necessary app details
			var err error
			configMapScope, err = impl.updateConfigScopeFor(appId, configMapScope)
			if err != nil {
				impl.logger.Errorw("error in fetching app details by app id", "appId", appId, "err", err)
				message := fmt.Sprintf("Error in fetching app details for appId: %d, error: %s", appId, err.Error())
				configMapBulkEditResponse.Message = append(configMapBulkEditResponse.Message, message)
				continue
			}
		}
		appName := configMapScope.GetAppName(appId)
		// Check if the user has permission to update the config map for the given app
		if validationErr := impl.isBaseCMConfigUpdateAllowed(ctx, appId, userMetadata); validationErr != nil {
			configMapBulkEditResponse.Failure = append(configMapBulkEditResponse.Failure, v1beta2Adapter.GetObjectsWithAppDetail(appName, []string{}, validationErr.Error()))
			continue
		}
		configData := &configMapBean.ConfigData{}
		err := json.Unmarshal([]byte(configMapSpec.Operation.Value), configData)
		if err != nil {
			impl.logger.Errorw("error in unmarshalling config data", "appId", appId, "err", err)
			message := fmt.Sprintf("Invalid config data for application %q, error: %s", appName, err.Error())
			configMapBulkEditResponse.Message = append(configMapBulkEditResponse.Message, message)
			continue
		}
		configDataRequest := &configMapBean.ConfigDataRequest{
			AppId:      appId,
			ConfigData: sliceUtil.GetSliceOf(configData),
			UserId:     userMetadata.UserId,
		}
		_, err = impl.configMapService.CMGlobalAddUpdate(configDataRequest)
		if err != nil {
			impl.logger.Errorw("error in creating configmap", "appId", appId, "err", err)
			message := fmt.Sprintf("Unable to create configmap for application %q, error: %s", appName, err.Error())
			configMapBulkEditResponse.Failure = append(configMapBulkEditResponse.Failure, v1beta2Adapter.GetObjectsWithAppDetail(appName, []string{}, message))
		} else {
			configMapBulkEditResponse.Successful = append(configMapBulkEditResponse.Successful, v1beta2Adapter.GetObjectsWithAppDetail(appName, sliceUtil.GetSliceOf(configData.Name), api.CreatedBaseConfig))
		}
	}
	return configMapBulkEditResponse
}

func (impl *ServiceImpl) updateEnvConfigMap(ctx *ctxUtil.RequestCtx, configMapScope *api.ConfigScope, configMapSpec *api.CmCsSpec,
	userMetadata *userBean.UserMetadata, configMapBulkEditResponse *api.CmCsResponse) *api.CmCsResponse {
	switch configMapSpec.Operation.Field {
	case api.Data:
		return impl.patchEnvConfigMap(ctx, configMapScope, configMapSpec, userMetadata, configMapBulkEditResponse)
	default:
		message := fmt.Sprintf("Invalid operation field %q for configmap bulk edit, please check and try again", configMapSpec.Operation.Field)
		configMapBulkEditResponse.Message = append(configMapBulkEditResponse.Message, message)
	}
	return configMapBulkEditResponse
}

func (impl *ServiceImpl) updateBaseConfigMap(ctx *ctxUtil.RequestCtx, configMapScope *api.ConfigScope, configMapSpec *api.CmCsSpec,
	userMetadata *userBean.UserMetadata, configMapBulkEditResponse *api.CmCsResponse) *api.CmCsResponse {
	switch configMapSpec.Operation.Field {
	case api.Data:
		return impl.patchBaseConfigMap(ctx, configMapScope, configMapSpec, userMetadata, configMapBulkEditResponse)
	default:
		message := fmt.Sprintf("Invalid operation field %q for config map bulk edit, please check and try again", configMapSpec.Operation.Field)
		configMapBulkEditResponse.Message = append(configMapBulkEditResponse.Message, message)
		return configMapBulkEditResponse
	}
}

func (impl *ServiceImpl) patchEnvConfigMap(ctx *ctxUtil.RequestCtx, configMapScope *api.ConfigScope, configMapSpec *api.CmCsSpec,
	userMetadata *userBean.UserMetadata, configMapBulkEditResponse *api.CmCsResponse) *api.CmCsResponse {
	envId := configMapScope.GetEnvId()
	configMapEnvModels, err := impl.bulkEditRepository.FindBulkCmAppModelsByAppIdsAndEnvId(configMapScope.GetAppIds(), envId)
	if err != nil {
		impl.logger.Errorw("error in fetching bulk app model for env", "err", err)
		message := fmt.Sprintf("Unable to get env level configmaps for %q, error: %s", configMapScope.GetEnvName(), err.Error())
		configMapBulkEditResponse.Message = append(configMapBulkEditResponse.Message, message)
		return configMapBulkEditResponse
	}
	if len(configMapEnvModels) == 0 {
		configMapBulkEditResponse.Message = append(configMapBulkEditResponse.Message, fmt.Sprintf("No matching configmaps to be updated for environment %q", configMapScope.GetEnvName()))
		return configMapBulkEditResponse
	}
	configMapBulkEditResponse = impl.bulkPatchEnvConfigMap(ctx, configMapScope, configMapEnvModels, configMapSpec, userMetadata, configMapBulkEditResponse)
	return configMapBulkEditResponse
}

func (impl *ServiceImpl) patchBaseConfigMap(ctx *ctxUtil.RequestCtx, configMapScope *api.ConfigScope, configMapSpec *api.CmCsSpec,
	userMetadata *userBean.UserMetadata, configMapBulkEditResponse *api.CmCsResponse) *api.CmCsResponse {
	configMapAppModels, err := impl.bulkEditRepository.FindBulkCmAppModelsByAppIds(configMapScope.GetAppIds())
	if err != nil {
		impl.logger.Errorw("error in fetching bulk app model for global", "err", err)
		message := fmt.Sprintf("Unable to get app level config map for global, error: %s", err.Error())
		configMapBulkEditResponse.Message = append(configMapBulkEditResponse.Message, message)
		return configMapBulkEditResponse
	}
	if len(configMapAppModels) == 0 {
		configMapBulkEditResponse.Message = append(configMapBulkEditResponse.Message, "No matching configmaps to be updated at base level")
		return configMapBulkEditResponse
	}
	configMapBulkEditResponse = impl.bulkPatchBaseConfigMap(ctx, configMapScope, configMapAppModels, configMapSpec, userMetadata, configMapBulkEditResponse)
	return configMapBulkEditResponse
}

func (impl *ServiceImpl) bulkPatchBaseConfigMap(ctx *ctxUtil.RequestCtx, configMapScope *api.ConfigScope, configMapAppModels []*configMapRepository.ConfigMapAppModel,
	configMapSpec *api.CmCsSpec, userMetadata *userBean.UserMetadata, cmBulkEditResponse *api.CmCsResponse) *api.CmCsResponse {
	for _, configMapAppModel := range configMapAppModels {
		appId := configMapAppModel.AppId
		if _, ok := configMapScope.GetAppDetails(appId); !ok {
			// If app details are not present in the scope, fetch them
			// Ideally, this should not happen as the scope should have been constructed with all necessary app details
			var err error
			configMapScope, err = impl.updateConfigScopeFor(appId, configMapScope)
			if err != nil {
				impl.logger.Errorw("error in fetching app details by app id", "appId", appId, "err", err)
				message := fmt.Sprintf("Error in fetching app details for appId: %d, error: %s", appId, err.Error())
				cmBulkEditResponse.Message = append(cmBulkEditResponse.Message, message)
				continue
			}
		}
		appName := configMapScope.GetAppName(appId)
		if validationErr := impl.isBaseCMConfigUpdateAllowed(ctx, appId, userMetadata); validationErr != nil {
			cmBulkEditResponse.Failure = append(cmBulkEditResponse.Failure, v1beta2Adapter.GetObjectsWithAppDetail(appName, []string{}, validationErr.Error()))
			continue
		}
		configMapNames := gjson.Get(configMapAppModel.ConfigMapData, "maps.#.name")
		messageCmNamesMap := make(map[string][]string)
		for i, configMapName := range configMapNames.Array() {
			// Filter the config map models based on the includes and excludes patterns
			if v1beta2Util.ShouldIncludeCmCsObject(configMapName.String(), configMapScope) {
				configMapPatchJsonString := configMapSpec.Operation.PatchJson
				keyNames := gjson.Get(configMapPatchJsonString, "#.path")
				for j, keyName := range keyNames.Array() {
					configMapPatchJsonString, _ = sjson.Set(configMapPatchJsonString, fmt.Sprintf("%d.path", j), fmt.Sprintf("/maps/%d/data%s", i, keyName.String()))
				}
				configMapPatchJson := []byte(configMapPatchJsonString)
				configMapPatch, err := jsonpatch.DecodePatch(configMapPatchJson)
				if err != nil {
					impl.logger.Errorw("error in decoding JSON patch", "err", err)
					if _, ok := messageCmNamesMap["The patch string you entered seems wrong, please check and try again"]; !ok {
						messageCmNamesMap["The patch string you entered seems wrong, please check and try again"] = []string{configMapName.String()}
					} else {
						messageCmNamesMap["The patch string you entered seems wrong, please check and try again"] = append(messageCmNamesMap["The patch string you entered seems wrong, please check and try again"], configMapName.String())
					}
				} else {
					modified, err := jsonUtil.ApplyJsonPatch(configMapPatch, configMapAppModel.ConfigMapData)
					if err != nil {
						impl.logger.Errorw("error in applying JSON patch", "err", err)
						if _, ok := messageCmNamesMap[fmt.Sprintf("Error in applying JSON patch : %s", err.Error())]; !ok {
							messageCmNamesMap[fmt.Sprintf("Error in applying JSON patch : %s", err.Error())] = []string{configMapName.String()}
						} else {
							messageCmNamesMap[fmt.Sprintf("Error in applying JSON patch : %s", err.Error())] = append(messageCmNamesMap[fmt.Sprintf("Error in applying JSON patch : %s", err.Error())], configMapName.String())
						}
					} else {
						configMapAppModel.ConfigMapData = modified
						if _, ok := messageCmNamesMap[api.UpdatedBaseConfig]; !ok {
							messageCmNamesMap[api.UpdatedBaseConfig] = []string{configMapName.String()}
						} else {
							messageCmNamesMap[api.UpdatedBaseConfig] = append(messageCmNamesMap[api.UpdatedBaseConfig], configMapName.String())
						}
					}
				}
			}
		}
		if _, ok := messageCmNamesMap[api.UpdatedBaseConfig]; ok {
			err := impl.bulkEditRepository.BulkUpdateConfigMapDataForGlobalById(configMapAppModel.Id, configMapAppModel.ConfigMapData)
			if err != nil {
				impl.logger.Errorw("error in bulk updating charts", "err", err)
				messageCmNamesMap[fmt.Sprintf("Error in updating in db : %s", err.Error())] = messageCmNamesMap[api.UpdatedBaseConfig]
				delete(messageCmNamesMap, api.UpdatedBaseConfig)
			}
			// creating history for config map history
			err = impl.configMapHistoryService.CreateHistoryFromAppLevelConfig(configMapAppModel, historyRepo.CONFIGMAP_TYPE)
			if err != nil {
				impl.logger.Errorw("error in creating entry for configmap history", "err", err)
			}
		}
		if len(messageCmNamesMap) != 0 {
			for key, value := range messageCmNamesMap {
				bulkEditResponse := v1beta2Adapter.GetObjectsWithAppDetail(appName, value, key)
				if key == api.UpdatedBaseConfig {
					cmBulkEditResponse.Successful = append(cmBulkEditResponse.Successful, bulkEditResponse)
				} else {
					cmBulkEditResponse.Failure = append(cmBulkEditResponse.Failure, bulkEditResponse)
				}
			}
		}
	}
	return cmBulkEditResponse
}

func (impl *ServiceImpl) bulkPatchEnvConfigMap(ctx *ctxUtil.RequestCtx, configMapScope *api.ConfigScope, configMapEnvModels []*configMapRepository.ConfigMapEnvModel,
	configMapSpec *api.CmCsSpec, userMetadata *userBean.UserMetadata, cmBulkEditResponse *api.CmCsResponse) *api.CmCsResponse {
	for _, configMapEnvModel := range configMapEnvModels {
		appId := configMapEnvModel.AppId
		if _, ok := configMapScope.GetAppDetails(appId); !ok {
			// If app details are not present in the scope, fetch them
			// Ideally, this should not happen as the scope should have been constructed with all necessary app details
			var err error
			configMapScope, err = impl.updateConfigScopeFor(appId, configMapScope)
			if err != nil {
				impl.logger.Errorw("error in fetching app details by app id", "appId", appId, "err", err)
				message := fmt.Sprintf("Error in fetching app details for appId: %d, error: %s", appId, err.Error())
				cmBulkEditResponse.Message = append(cmBulkEditResponse.Message, message)
				continue
			}
		}
		appName := configMapScope.GetAppName(appId)
		if validationErr := impl.isEnvCMConfigUpdateAllowed(ctx, appId, configMapScope.GetEnvId(), userMetadata); validationErr != nil {
			cmBulkEditResponse.Failure = append(cmBulkEditResponse.Failure, v1beta2Adapter.GetObjectsWithAppEnvDetail(appName, configMapScope.GetEnvName(), []string{}, validationErr.Error()))
			continue
		}
		configMapNames := gjson.Get(configMapEnvModel.ConfigMapData, "maps.#.name")
		messageCmNamesMap := make(map[string][]string)
		for i, configMapName := range configMapNames.Array() {
			// Filter the config map models based on the includes and excludes patterns
			if v1beta2Util.ShouldIncludeCmCsObject(configMapName.String(), configMapScope) {
				configMapPatchJsonString := configMapSpec.Operation.PatchJson
				keyNames := gjson.Get(configMapPatchJsonString, "#.path")
				for j, keyName := range keyNames.Array() {
					configMapPatchJsonString, _ = sjson.Set(configMapPatchJsonString, fmt.Sprintf("%d.path", j), fmt.Sprintf("/maps/%d/data%s", i, keyName.String()))
				}
				configMapPatchJson := []byte(configMapPatchJsonString)
				configMapPatch, err := jsonpatch.DecodePatch(configMapPatchJson)
				if err != nil {
					impl.logger.Errorw("error in decoding JSON patch", "err", err)
					if _, ok := messageCmNamesMap["The patch string you entered seems wrong, please check and try again"]; !ok {
						messageCmNamesMap["The patch string you entered seems wrong, please check and try again"] = []string{configMapName.String()}
					} else {
						messageCmNamesMap["The patch string you entered seems wrong, please check and try again"] = append(messageCmNamesMap["The patch string you entered seems wrong, please check and try again"], configMapName.String())
					}
				} else {
					modified, err := jsonUtil.ApplyJsonPatch(configMapPatch, configMapEnvModel.ConfigMapData)
					if err != nil {
						impl.logger.Errorw("error in applying JSON patch", "err", err)
						if _, ok := messageCmNamesMap[fmt.Sprintf("Error in applying JSON patch : %s", err.Error())]; !ok {
							messageCmNamesMap[fmt.Sprintf("Error in applying JSON patch : %s", err.Error())] = []string{configMapName.String()}
						} else {
							messageCmNamesMap[fmt.Sprintf("Error in applying JSON patch : %s", err.Error())] = append(messageCmNamesMap[fmt.Sprintf("Error in applying JSON patch : %s", err.Error())], configMapName.String())
						}
					} else {
						configMapEnvModel.ConfigMapData = modified
						if _, ok := messageCmNamesMap[api.UpdatedEnvOverride]; !ok {
							messageCmNamesMap[api.UpdatedEnvOverride] = []string{configMapName.String()}
						} else {
							messageCmNamesMap[api.UpdatedEnvOverride] = append(messageCmNamesMap[api.UpdatedEnvOverride], configMapName.String())
						}
					}
				}
			}
		}
		if _, ok := messageCmNamesMap[api.UpdatedEnvOverride]; ok {
			err := impl.bulkEditRepository.BulkUpdateConfigMapDataForEnvById(configMapEnvModel.Id, configMapEnvModel.ConfigMapData)
			if err != nil {
				impl.logger.Errorw("error in bulk updating charts", "err", err)
				messageCmNamesMap[fmt.Sprintf("Error in updating in db : %s", err.Error())] = messageCmNamesMap[api.UpdatedEnvOverride]
				delete(messageCmNamesMap, api.UpdatedEnvOverride)
			}
			// creating history for config map history
			err = impl.configMapHistoryService.CreateHistoryFromEnvLevelConfig(configMapEnvModel, historyRepo.CONFIGMAP_TYPE)
			if err != nil {
				impl.logger.Errorw("error in creating entry for configmap history", "err", err)
			}
		}
		if len(messageCmNamesMap) != 0 {
			for key, value := range messageCmNamesMap {
				bulkEditResponse := v1beta2Adapter.GetObjectsWithAppEnvDetail(appName, configMapScope.GetEnvName(), value, key)
				if key == api.UpdatedEnvOverride {
					cmBulkEditResponse.Successful = append(cmBulkEditResponse.Successful, bulkEditResponse)
				} else {
					cmBulkEditResponse.Failure = append(cmBulkEditResponse.Failure, bulkEditResponse)
				}
			}
		}
	}
	return cmBulkEditResponse
}

func (impl *ServiceImpl) deleteEnvSecret(ctx *ctxUtil.RequestCtx, secretScope *api.ConfigScope, secretSpec *api.CmCsSpec,
	userMetadata *userBean.UserMetadata, secretBulkEditResponse *api.CmCsResponse) *api.CmCsResponse {
	for _, appId := range secretScope.GetAppIds() {
		if _, ok := secretScope.GetAppDetails(appId); !ok {
			// If app details are not present in the scope, fetch them
			// Ideally, this should not happen as the scope should have been constructed with all necessary app details
			var err error
			secretScope, err = impl.updateConfigScopeFor(appId, secretScope)
			if err != nil {
				impl.logger.Errorw("error in fetching app details by app id", "appId", appId, "err", err)
				message := fmt.Sprintf("Error in fetching app details for appId: %d, error: %s", appId, err.Error())
				secretBulkEditResponse.Message = append(secretBulkEditResponse.Message, message)
				continue
			}
		}
		appName := secretScope.GetAppName(appId)
		secretEnvModels, err := impl.bulkEditRepository.FindBulkCsAppModelsByAppIdsAndEnvId(secretScope.GetAppIds(), secretScope.GetEnvId())
		if err != nil {
			impl.logger.Errorw("error in fetching bulk app model for env", "err", err)
			message := fmt.Sprintf("Unable to get env level secrets for %q, error: %s", secretScope.GetEnvName(), err.Error())
			secretBulkEditResponse.Message = append(secretBulkEditResponse.Message, message)
			continue
		}
		if len(secretEnvModels) == 0 {
			secretBulkEditResponse.Message = append(secretBulkEditResponse.Message, fmt.Sprintf("No matching secrets to be updated for environment %q", secretScope.GetEnvName()))
			continue
		}
		// Check if the user has permission to update the secret for the given app and env
		if validationErr := impl.isEnvCSConfigUpdateAllowed(ctx, appId, secretScope.GetEnvId(), userMetadata); validationErr != nil {
			secretBulkEditResponse.Failure = append(secretBulkEditResponse.Failure, v1beta2Adapter.GetObjectsWithAppEnvDetail(appName, secretScope.GetEnvName(), []string{}, validationErr.Error()))
			continue
		}
		failed := false
		for _, secretEnvModel := range secretEnvModels {
			_, err = impl.configMapService.CSEnvironmentDelete(secretSpec.Operation.Value, secretEnvModel.Id, userMetadata.UserId)
			if err != nil {
				impl.logger.Errorw("error in deleting secret for env", "appId", appId, "envId", secretScope.GetEnvId(), "err", err)
				message := fmt.Sprintf("Unable to delete secret for application %q in environment %q, error: %s", appName, secretScope.GetEnvName(), err.Error())
				secretBulkEditResponse.Failure = append(secretBulkEditResponse.Failure, v1beta2Adapter.GetObjectsWithAppEnvDetail(appName, secretScope.GetEnvName(), []string{}, message))
				failed = true
				continue
			}
		}
		if !failed {
			secretBulkEditResponse.Successful = append(secretBulkEditResponse.Successful,
				v1beta2Adapter.GetObjectsWithAppEnvDetail(appName, secretScope.GetEnvName(),
					sliceUtil.GetSliceOf(secretSpec.Operation.Value), api.DeletedEnvOverride))
		}
	}
	return secretBulkEditResponse
}

func (impl *ServiceImpl) deleteBaseSecret(ctx *ctxUtil.RequestCtx, secretScope *api.ConfigScope, secretSpec *api.CmCsSpec,
	userMetadata *userBean.UserMetadata, secretBulkEditResponse *api.CmCsResponse) *api.CmCsResponse {
	for _, appId := range secretScope.GetAppIds() {
		if _, ok := secretScope.GetAppDetails(appId); !ok {
			// If app details are not present in the scope, fetch them
			// Ideally, this should not happen as the scope should have been constructed with all necessary app details
			var err error
			secretScope, err = impl.updateConfigScopeFor(appId, secretScope)
			if err != nil {
				impl.logger.Errorw("error in fetching app details by app id", "appId", appId, "err", err)
				message := fmt.Sprintf("Error in fetching app details for appId: %d, error: %s", appId, err.Error())
				secretBulkEditResponse.Message = append(secretBulkEditResponse.Message, message)
				continue
			}
		}
		appName := secretScope.GetAppName(appId)
		secretEnvModels, err := impl.bulkEditRepository.FindBulkCsAppModelsByAppIds(secretScope.GetAppIds())
		if err != nil {
			impl.logger.Errorw("error in fetching bulk app model for env", "err", err)
			message := fmt.Sprintf("Unable to get app level secrets for %q, error: %s", secretScope.GetEnvName(), err.Error())
			secretBulkEditResponse.Message = append(secretBulkEditResponse.Message, message)
			continue
		}
		if len(secretEnvModels) == 0 {
			secretBulkEditResponse.Message = append(secretBulkEditResponse.Message, fmt.Sprintf("No matching secrets to be updated"))
			continue
		}
		// Check if the user has permission to update the secret for the given app
		if validationErr := impl.isBaseCSConfigUpdateAllowed(ctx, appId, userMetadata); validationErr != nil {
			secretBulkEditResponse.Failure = append(secretBulkEditResponse.Failure, v1beta2Adapter.GetObjectsWithAppDetail(appName, []string{}, validationErr.Error()))
			continue
		}
		failed := false
		for _, secretEnvModel := range secretEnvModels {
			_, err = impl.configMapService.CSEnvironmentDelete(secretSpec.Operation.Value, secretEnvModel.Id, userMetadata.UserId)
			if err != nil {
				impl.logger.Errorw("error in deleting secret for env", "appId", appId, "err", err)
				message := fmt.Sprintf("Unable to delete secret for application %q, error: %s", appName, err.Error())
				secretBulkEditResponse.Failure = append(secretBulkEditResponse.Failure, v1beta2Adapter.GetObjectsWithAppDetail(appName, []string{}, message))
				failed = true
				continue
			}
		}
		if !failed {
			secretBulkEditResponse.Successful = append(secretBulkEditResponse.Successful,
				v1beta2Adapter.GetObjectsWithAppDetail(appName, sliceUtil.GetSliceOf(secretSpec.Operation.Value),
					api.DeletedBaseConfig))
		}
	}
	return secretBulkEditResponse
}

func (impl *ServiceImpl) createEnvSecret(ctx *ctxUtil.RequestCtx, secretScope *api.ConfigScope, secretSpec *api.CmCsSpec,
	userMetadata *userBean.UserMetadata, secretBulkEditResponse *api.CmCsResponse) *api.CmCsResponse {
	for _, appId := range secretScope.GetAppIds() {
		if _, ok := secretScope.GetAppDetails(appId); !ok {
			// If app details are not present in the scope, fetch them
			// Ideally, this should not happen as the scope should have been constructed with all necessary app details
			var err error
			secretScope, err = impl.updateConfigScopeFor(appId, secretScope)
			if err != nil {
				impl.logger.Errorw("error in fetching app details by app id", "appId", appId, "err", err)
				message := fmt.Sprintf("Error in fetching app details for appId: %d, error: %s", appId, err.Error())
				secretBulkEditResponse.Message = append(secretBulkEditResponse.Message, message)
				continue
			}
		}
		appName := secretScope.GetAppName(appId)
		// Check if the user has permission to update the secret for the given app and env
		if validationErr := impl.isEnvCSConfigUpdateAllowed(ctx, appId, secretScope.GetEnvId(), userMetadata); validationErr != nil {
			secretBulkEditResponse.Failure = append(secretBulkEditResponse.Failure, v1beta2Adapter.GetObjectsWithAppEnvDetail(appName, secretScope.GetEnvName(), []string{}, validationErr.Error()))
			continue
		}
		configData := &configMapBean.ConfigData{}
		err := json.Unmarshal([]byte(secretSpec.Operation.Value), configData)
		if err != nil {
			impl.logger.Errorw("error in unmarshalling config data", "appId", appId, "envId", secretScope.GetEnvId(), "err", err)
			message := fmt.Sprintf("Invalid config data for application %q and environment %q, error: %s", appName, secretScope.GetEnvName(), err.Error())
			secretBulkEditResponse.Message = append(secretBulkEditResponse.Message, message)
			continue
		}
		configDataRequest := &configMapBean.ConfigDataRequest{
			AppId:         appId,
			EnvironmentId: secretScope.GetEnvId(),
			ConfigData:    sliceUtil.GetSliceOf(configData),
			UserId:        userMetadata.UserId,
		}
		_, err = impl.configMapService.CSEnvironmentAddUpdate(configDataRequest)
		if err != nil {
			impl.logger.Errorw("error in creating secret for env", "appId", appId, "envId", secretScope.GetEnvId(), "err", err)
			message := fmt.Sprintf("Unable to create secret for application %q in environmet %q, error: %s", appName, secretScope.GetEnvName(), err.Error())
			secretBulkEditResponse.Failure = append(secretBulkEditResponse.Failure, v1beta2Adapter.GetObjectsWithAppEnvDetail(appName, secretScope.GetEnvName(), []string{}, message))
			continue
		}
		secretBulkEditResponse.Successful = append(secretBulkEditResponse.Successful, v1beta2Adapter.GetObjectsWithAppEnvDetail(appName, secretScope.GetEnvName(), sliceUtil.GetSliceOf(configData.Name), api.CreatedEnvOverride))
	}
	return secretBulkEditResponse
}

func (impl *ServiceImpl) createBaseSecret(ctx *ctxUtil.RequestCtx, secretScope *api.ConfigScope, secretSpec *api.CmCsSpec,
	userMetadata *userBean.UserMetadata, secretBulkEditResponse *api.CmCsResponse) *api.CmCsResponse {
	for _, appId := range secretScope.GetAppIds() {
		if _, ok := secretScope.GetAppDetails(appId); !ok {
			// If app details are not present in the scope, fetch them
			// Ideally, this should not happen as the scope should have been constructed with all necessary app details
			var err error
			secretScope, err = impl.updateConfigScopeFor(appId, secretScope)
			if err != nil {
				impl.logger.Errorw("error in fetching app details by app id", "appId", appId, "err", err)
				message := fmt.Sprintf("Error in fetching app details for appId: %d, error: %s", appId, err.Error())
				secretBulkEditResponse.Message = append(secretBulkEditResponse.Message, message)
				continue
			}
		}
		appName := secretScope.GetAppName(appId)
		// Check if the user has permission to update the secret map for the given app
		if validationErr := impl.isBaseCSConfigUpdateAllowed(ctx, appId, userMetadata); validationErr != nil {
			secretBulkEditResponse.Failure = append(secretBulkEditResponse.Failure, v1beta2Adapter.GetObjectsWithAppDetail(appName, []string{}, validationErr.Error()))
			continue
		}
		configData := &configMapBean.ConfigData{}
		err := json.Unmarshal([]byte(secretSpec.Operation.Value), configData)
		if err != nil {
			impl.logger.Errorw("error in unmarshalling config data", "appId", appId, "err", err)
			message := fmt.Sprintf("Invalid config data for application %q, error: %s", appName, err.Error())
			secretBulkEditResponse.Message = append(secretBulkEditResponse.Message, message)
			continue
		}
		configDataRequest := &configMapBean.ConfigDataRequest{
			AppId:      appId,
			ConfigData: sliceUtil.GetSliceOf(configData),
			UserId:     userMetadata.UserId,
		}
		_, err = impl.configMapService.CSGlobalAddUpdate(configDataRequest)
		if err != nil {
			impl.logger.Errorw("error in creating secret", "appId", appId, "err", err)
			message := fmt.Sprintf("Unable to create secret for application %q, error: %s", appName, err.Error())
			secretBulkEditResponse.Failure = append(secretBulkEditResponse.Failure, v1beta2Adapter.GetObjectsWithAppDetail(appName, []string{}, message))
			continue
		}
		secretBulkEditResponse.Successful = append(secretBulkEditResponse.Successful, v1beta2Adapter.GetObjectsWithAppDetail(appName, sliceUtil.GetSliceOf(configData.Name), api.CreatedBaseConfig))
	}
	return secretBulkEditResponse
}

func (impl *ServiceImpl) updateEnvSecret(ctx *ctxUtil.RequestCtx, secretScope *api.ConfigScope, secretSpec *api.CmCsSpec,
	userMetadata *userBean.UserMetadata, secretBulkEditResponse *api.CmCsResponse) *api.CmCsResponse {
	switch secretSpec.Operation.Field {
	case api.Data:
		return impl.patchEnvSecret(ctx, secretScope, secretSpec, userMetadata, secretBulkEditResponse)
	default:
		message := fmt.Sprintf("Invalid operation field %q for secret bulk edit, please check and try again", secretSpec.Operation.Field)
		secretBulkEditResponse.Message = append(secretBulkEditResponse.Message, message)
	}
	return secretBulkEditResponse
}

func (impl *ServiceImpl) updateBaseSecret(ctx *ctxUtil.RequestCtx, secretScope *api.ConfigScope, secretSpec *api.CmCsSpec,
	userMetadata *userBean.UserMetadata, secretBulkEditResponse *api.CmCsResponse) *api.CmCsResponse {
	switch secretSpec.Operation.Field {
	case api.Data:
		return impl.patchBaseSecret(ctx, secretScope, secretSpec, userMetadata, secretBulkEditResponse)
	default:
		message := fmt.Sprintf("Invalid operation field %q for secret bulk edit, please check and try again", secretSpec.Operation.Field)
		secretBulkEditResponse.Message = append(secretBulkEditResponse.Message, message)
		return secretBulkEditResponse
	}
}

func (impl *ServiceImpl) patchEnvSecret(ctx *ctxUtil.RequestCtx, secretScope *api.ConfigScope, secretSpec *api.CmCsSpec,
	userMetadata *userBean.UserMetadata, secretBulkEditResponse *api.CmCsResponse) *api.CmCsResponse {
	envId := secretScope.GetEnvId()
	secretEnvModels, err := impl.bulkEditRepository.FindBulkCsAppModelsByAppIdsAndEnvId(secretScope.GetAppIds(), envId)
	if err != nil {
		impl.logger.Errorw("error in fetching bulk app model for env", "err", err)
		message := fmt.Sprintf("Unable to get env level secrets for %q, error: %s", secretScope.GetEnvName(), err.Error())
		secretBulkEditResponse.Message = append(secretBulkEditResponse.Message, message)
		return secretBulkEditResponse
	}
	if len(secretEnvModels) == 0 {
		secretBulkEditResponse.Message = append(secretBulkEditResponse.Message, fmt.Sprintf("No matching secrets to be updated for environmet %q", secretScope.GetEnvName()))
		return secretBulkEditResponse
	}
	secretBulkEditResponse = impl.bulkPatchEnvSecret(ctx, secretScope, secretEnvModels, secretSpec, userMetadata, secretBulkEditResponse)
	return secretBulkEditResponse
}

func (impl *ServiceImpl) patchBaseSecret(ctx *ctxUtil.RequestCtx, secretScope *api.ConfigScope, secretSpec *api.CmCsSpec,
	userMetadata *userBean.UserMetadata, secretBulkEditResponse *api.CmCsResponse) *api.CmCsResponse {
	secretAppModels, err := impl.bulkEditRepository.FindBulkCsAppModelsByAppIds(secretScope.GetAppIds())
	if err != nil {
		impl.logger.Errorw("error in fetching bulk app model for global", "err", err)
		message := fmt.Sprintf("Unable to get app level secrets, error: %s", err.Error())
		secretBulkEditResponse.Message = append(secretBulkEditResponse.Message, message)
		return secretBulkEditResponse
	}
	if len(secretAppModels) == 0 {
		secretBulkEditResponse.Message = append(secretBulkEditResponse.Message, "No matching secrets to be updated at base level")
		return secretBulkEditResponse
	}
	secretBulkEditResponse = impl.bulkPatchBaseSecret(ctx, secretScope, secretAppModels, secretSpec, userMetadata, secretBulkEditResponse)
	return secretBulkEditResponse
}

func (impl *ServiceImpl) bulkPatchBaseSecret(ctx *ctxUtil.RequestCtx, secretScope *api.ConfigScope, secretAppModels []*configMapRepository.ConfigMapAppModel,
	secretSpec *api.CmCsSpec, userMetadata *userBean.UserMetadata, csBulkEditResponse *api.CmCsResponse) *api.CmCsResponse {
	for _, secretAppModel := range secretAppModels {
		appId := secretAppModel.AppId
		if _, ok := secretScope.GetAppDetails(appId); !ok {
			// If app details are not present in the scope, fetch them
			// Ideally, this should not happen as the scope should have been constructed with all necessary app details
			var err error
			secretScope, err = impl.updateConfigScopeFor(appId, secretScope)
			if err != nil {
				impl.logger.Errorw("error in fetching app details by app id", "appId", appId, "err", err)
				message := fmt.Sprintf("Error in fetching app details for appId: %d, error: %s", appId, err.Error())
				csBulkEditResponse.Message = append(csBulkEditResponse.Message, message)
				continue
			}
		}
		appName := secretScope.GetAppName(appId)
		if validationErr := impl.isBaseCSConfigUpdateAllowed(ctx, appId, userMetadata); validationErr != nil {
			csBulkEditResponse.Failure = append(csBulkEditResponse.Failure, v1beta2Adapter.GetObjectsWithAppDetail(appName, []string{}, validationErr.Error()))
			continue
		}
		secretNames := gjson.Get(secretAppModel.SecretData, "secrets.#.name")
		messageSecretNamesMap := make(map[string][]string)
		for i, secretName := range secretNames.Array() {
			// Filter the config map models based on the includes and excludes patterns
			if v1beta2Util.ShouldIncludeCmCsObject(secretName.String(), secretScope) {
				secretPatchJsonString := secretSpec.Operation.PatchJson
				keyNames := gjson.Get(secretPatchJsonString, "#.path")
				for j, keyName := range keyNames.Array() {
					secretPatchJsonString, _ = sjson.Set(secretPatchJsonString, fmt.Sprintf("%d.path", j), fmt.Sprintf("/secrets/%d/data%s", i, keyName.String()))
				}
				//updating values to their base64 equivalent, on secret save/update operation this logic is implemented on FE
				values := gjson.Get(secretPatchJsonString, "#.value")
				for j, value := range values.Array() {
					base64EncodedValue := base64.StdEncoding.EncodeToString([]byte(value.String()))
					secretPatchJsonString, _ = sjson.Set(secretPatchJsonString, fmt.Sprintf("%d.value", j), base64EncodedValue)
				}
				secretPatchJson := []byte(secretPatchJsonString)
				secretPatch, err := jsonpatch.DecodePatch(secretPatchJson)
				if err != nil {
					impl.logger.Errorw("error in decoding JSON patch", "err", err)
					if _, ok := messageSecretNamesMap["The patch string you entered seems wrong, please check and try again"]; !ok {
						messageSecretNamesMap["The patch string you entered seems wrong, please check and try again"] = []string{secretName.String()}
					} else {
						messageSecretNamesMap["The patch string you entered seems wrong, please check and try again"] = append(messageSecretNamesMap["The patch string you entered seems wrong, please check and try again"], secretName.String())
					}
				} else {
					modified, err := jsonUtil.ApplyJsonPatch(secretPatch, secretAppModel.SecretData)
					if err != nil {
						impl.logger.Errorw("error in applying JSON patch", "err", err)
						if _, ok := messageSecretNamesMap[fmt.Sprintf("Error in applying JSON patch : %s", err.Error())]; !ok {
							messageSecretNamesMap[fmt.Sprintf("Error in applying JSON patch : %s", err.Error())] = []string{secretName.String()}
						} else {
							messageSecretNamesMap[fmt.Sprintf("Error in applying JSON patch : %s", err.Error())] = append(messageSecretNamesMap[fmt.Sprintf("Error in applying JSON patch : %s", err.Error())], secretName.String())
						}
					} else {
						secretAppModel.SecretData = modified
						if _, ok := messageSecretNamesMap[api.UpdatedBaseConfig]; !ok {
							messageSecretNamesMap[api.UpdatedBaseConfig] = []string{secretName.String()}
						} else {
							messageSecretNamesMap[api.UpdatedBaseConfig] = append(messageSecretNamesMap[api.UpdatedBaseConfig], secretName.String())
						}
					}
				}
			}
		}
		if _, ok := messageSecretNamesMap[api.UpdatedBaseConfig]; ok {
			err := impl.bulkEditRepository.BulkUpdateSecretDataForGlobalById(secretAppModel.Id, secretAppModel.SecretData)
			if err != nil {
				impl.logger.Errorw("error in bulk updating secrets", "err", err)
				messageSecretNamesMap[fmt.Sprintf("Error in updating in db : %s", err.Error())] = messageSecretNamesMap[api.UpdatedBaseConfig]
				delete(messageSecretNamesMap, api.UpdatedBaseConfig)
			}
			// creating history for config map history
			err = impl.configMapHistoryService.CreateHistoryFromAppLevelConfig(secretAppModel, historyRepo.SECRET_TYPE)
			if err != nil {
				impl.logger.Errorw("error in creating entry for secret history", "err", err)
			}
		}
		if len(messageSecretNamesMap) != 0 {
			for key, value := range messageSecretNamesMap {
				bulkEditResponse := v1beta2Adapter.GetObjectsWithAppDetail(appName, value, key)
				if key == api.UpdatedBaseConfig {
					csBulkEditResponse.Successful = append(csBulkEditResponse.Successful, bulkEditResponse)
				} else {
					csBulkEditResponse.Failure = append(csBulkEditResponse.Failure, bulkEditResponse)
				}
			}
		}
	}
	return csBulkEditResponse
}

func (impl *ServiceImpl) bulkPatchEnvSecret(ctx *ctxUtil.RequestCtx, secretScope *api.ConfigScope, secretEnvModels []*configMapRepository.ConfigMapEnvModel,
	secretSpec *api.CmCsSpec, userMetadata *userBean.UserMetadata, csBulkEditResponse *api.CmCsResponse) *api.CmCsResponse {
	for _, secretEnvModel := range secretEnvModels {
		appId := secretEnvModel.AppId
		if _, ok := secretScope.GetAppDetails(appId); !ok {
			// If app details are not present in the scope, fetch them
			// Ideally, this should not happen as the scope should have been constructed with all necessary app details
			var err error
			secretScope, err = impl.updateConfigScopeFor(appId, secretScope)
			if err != nil {
				impl.logger.Errorw("error in fetching app details by app id", "appId", appId, "err", err)
				message := fmt.Sprintf("Error in fetching app details for appId: %d, error: %s", appId, err.Error())
				csBulkEditResponse.Message = append(csBulkEditResponse.Message, message)
				continue
			}
		}
		appName := secretScope.GetAppName(appId)
		if validationErr := impl.isEnvCSConfigUpdateAllowed(ctx, appId, secretScope.GetEnvId(), userMetadata); validationErr != nil {
			csBulkEditResponse.Failure = append(csBulkEditResponse.Failure, v1beta2Adapter.GetObjectsWithAppEnvDetail(appName, secretScope.GetEnvName(), []string{}, validationErr.Error()))
			continue
		}
		secretNames := gjson.Get(secretEnvModel.SecretData, "secrets.#.name")
		messageSecretNamesMap := make(map[string][]string)
		for i, secretName := range secretNames.Array() {
			// Filter the config map models based on the includes and excludes patterns
			if v1beta2Util.ShouldIncludeCmCsObject(secretName.String(), secretScope) {
				secretPatchJsonString := secretSpec.Operation.PatchJson
				keyNames := gjson.Get(secretPatchJsonString, "#.path")
				for j, keyName := range keyNames.Array() {
					secretPatchJsonString, _ = sjson.Set(secretPatchJsonString, fmt.Sprintf("%d.path", j), fmt.Sprintf("/secrets/%d/data%s", i, keyName.String()))
				}
				//updating values to their base64 equivalent, on secret save/update operation this logic is implemented on FE
				values := gjson.Get(secretPatchJsonString, "#.value")
				for j, value := range values.Array() {
					base64EncodedValue := base64.StdEncoding.EncodeToString([]byte(value.String()))
					secretPatchJsonString, _ = sjson.Set(secretPatchJsonString, fmt.Sprintf("%d.value", j), base64EncodedValue)
				}
				secretPatchJson := []byte(secretPatchJsonString)
				secretPatch, err := jsonpatch.DecodePatch(secretPatchJson)
				if err != nil {
					impl.logger.Errorw("error in decoding JSON patch", "err", err)
					if _, ok := messageSecretNamesMap["The patch string you entered seems wrong, please check and try again"]; !ok {
						messageSecretNamesMap["The patch string you entered seems wrong, please check and try again"] = []string{secretName.String()}
					} else {
						messageSecretNamesMap["The patch string you entered seems wrong, please check and try again"] = append(messageSecretNamesMap["The patch string you entered seems wrong, please check and try again"], secretName.String())
					}
				} else {
					modified, err := jsonUtil.ApplyJsonPatch(secretPatch, secretEnvModel.SecretData)
					if err != nil {
						impl.logger.Errorw("error in applying JSON patch", "err", err)
						if _, ok := messageSecretNamesMap[fmt.Sprintf("Error in applying JSON patch : %s", err.Error())]; !ok {
							messageSecretNamesMap[fmt.Sprintf("Error in applying JSON patch : %s", err.Error())] = []string{secretName.String()}
						} else {
							messageSecretNamesMap[fmt.Sprintf("Error in applying JSON patch : %s", err.Error())] = append(messageSecretNamesMap[fmt.Sprintf("Error in applying JSON patch : %s", err.Error())], secretName.String())
						}
					} else {
						secretEnvModel.SecretData = modified
						if _, ok := messageSecretNamesMap[api.UpdatedEnvOverride]; !ok {
							messageSecretNamesMap[api.UpdatedEnvOverride] = []string{secretName.String()}
						} else {
							messageSecretNamesMap[api.UpdatedEnvOverride] = append(messageSecretNamesMap[api.UpdatedEnvOverride], secretName.String())
						}
					}
				}
			}
		}
		if _, ok := messageSecretNamesMap[api.UpdatedEnvOverride]; ok {
			err := impl.bulkEditRepository.BulkUpdateSecretDataForEnvById(secretEnvModel.Id, secretEnvModel.SecretData)
			if err != nil {
				impl.logger.Errorw("error in bulk updating charts", "err", err)
				messageSecretNamesMap[fmt.Sprintf("Error in updating in db : %s", err.Error())] = messageSecretNamesMap[api.UpdatedEnvOverride]
				delete(messageSecretNamesMap, api.UpdatedEnvOverride)
			}
			// creating history for config map history
			err = impl.configMapHistoryService.CreateHistoryFromEnvLevelConfig(secretEnvModel, historyRepo.SECRET_TYPE)
			if err != nil {
				impl.logger.Errorw("error in creating entry for secret history", "err", err)
			}
		}
		if len(messageSecretNamesMap) != 0 {
			for key, value := range messageSecretNamesMap {
				bulkEditResponse := v1beta2Adapter.GetObjectsWithAppEnvDetail(appName, secretScope.GetEnvName(), value, key)
				if key == api.UpdatedEnvOverride {
					csBulkEditResponse.Successful = append(csBulkEditResponse.Successful, bulkEditResponse)
				} else {
					csBulkEditResponse.Failure = append(csBulkEditResponse.Failure, bulkEditResponse)
				}
			}
		}
	}
	return csBulkEditResponse
}

func (impl *ServiceImpl) updateEnvDeploymentTemplateHistory(envChart *chartConfig.EnvConfigOverride) {
	// creating history entry for deployment template
	isAppMetricsEnabled, err := impl.deployedAppMetricsService.GetMetricsFlagForAPipelineByAppIdAndEnvId(envChart.Chart.AppId, envChart.TargetEnvironment)
	if err != nil {
		impl.logger.Errorw("error, GetMetricsFlagForAPipelineByAppIdAndEnvId", "err", err, "appId", envChart.Chart.AppId, "envId", envChart.TargetEnvironment)
		return
	}
	chartEnvDTO := dtAdapter.EnvOverrideDBToDTO(envChart)
	err = impl.deploymentTemplateHistoryService.CreateDeploymentTemplateHistoryFromEnvOverrideTemplate(chartEnvDTO, nil, isAppMetricsEnabled, 0)
	if err != nil {
		impl.logger.Errorw("error in creating entry for env deployment template history", "err", err, "envOverride", envChart)
	}
	// VARIABLE_MAPPING_UPDATE
	err = impl.scopedVariableManager.ExtractAndMapVariables(envChart.EnvOverrideValues, envChart.Id, variableRepo.EntityTypeDeploymentTemplateEnvLevel, envChart.UpdatedBy, nil)
	if err != nil {
		impl.logger.Errorw("error in creating entry for env deployment template history", "envOverrideId", envChart.Id, "err", err)
	}
	return
}

func (impl *ServiceImpl) updateBaseDeploymentTemplateHistory(chart *chartRepoRepository.Chart) {
	// creating history entry for deployment template
	appLevelAppMetricsEnabled, err := impl.deployedAppMetricsService.GetMetricsFlagByAppId(chart.AppId)
	if err != nil {
		impl.logger.Errorw("error in getting app level metrics app level", "error", err, "appId", chart.AppId)
		return
	}
	err = impl.deploymentTemplateHistoryService.CreateDeploymentTemplateHistoryFromGlobalTemplate(chart, nil, appLevelAppMetricsEnabled)
	if err != nil {
		impl.logger.Errorw("error in creating entry for deployment template history", "err", err, "chart", chart)
	}
	// VARIABLE_MAPPING_UPDATE
	// NOTE: this flow is doesn't have the user info, therefore updated by is being set to the last updated by
	err = impl.scopedVariableManager.ExtractAndMapVariables(chart.GlobalOverride, chart.Id, variableRepo.EntityTypeDeploymentTemplateAppLevel, chart.UpdatedBy, nil)
	if err != nil {
		impl.logger.Errorw("error in creating entry for deployment template history", "chartId", chart.Id, "err", err)
	}
	return
}

func (impl *ServiceImpl) isEnvDTConfigUpdateAllowed(ctx context.Context, appId, envId int, userMetadata *userBean.UserMetadata) error {
	return impl.CanUpdateConfigResource(ctx, approvalBean.APPROVAL_FOR_CONFIGURATION_DT, appId, envId, userMetadata)
}

func (impl *ServiceImpl) isBaseDTConfigUpdateAllowed(ctx context.Context, appId int, userMetadata *userBean.UserMetadata) error {
	return impl.CanUpdateConfigResource(ctx, approvalBean.APPROVAL_FOR_CONFIGURATION_DT, appId, approvalBean.BASE_CONFIG_ENV_ID, userMetadata)
}

func (impl *ServiceImpl) isEnvCMConfigUpdateAllowed(ctx context.Context, appId, envId int, userMetadata *userBean.UserMetadata) error {
	return impl.CanUpdateConfigResource(ctx, approvalBean.APPROVAL_FOR_CONFIGURATION_CM, appId, envId, userMetadata)
}

func (impl *ServiceImpl) isBaseCMConfigUpdateAllowed(ctx context.Context, appId int, userMetadata *userBean.UserMetadata) error {
	return impl.CanUpdateConfigResource(ctx, approvalBean.APPROVAL_FOR_CONFIGURATION_CM, appId, approvalBean.BASE_CONFIG_ENV_ID, userMetadata)
}

func (impl *ServiceImpl) isEnvCSConfigUpdateAllowed(ctx context.Context, appId, envId int, userMetadata *userBean.UserMetadata) error {
	return impl.CanUpdateConfigResource(ctx, approvalBean.APPROVAL_FOR_CONFIGURATION_CS, appId, envId, userMetadata)
}

func (impl *ServiceImpl) isBaseCSConfigUpdateAllowed(ctx context.Context, appId int, userMetadata *userBean.UserMetadata) error {
	return impl.CanUpdateConfigResource(ctx, approvalBean.APPROVAL_FOR_CONFIGURATION_CS, appId, approvalBean.BASE_CONFIG_ENV_ID, userMetadata)
}

func (impl *ServiceImpl) configProtectionEnabled(ctx context.Context, resourceKind approvalBean.ApprovalFor, appId int, envId int, userMetadata *userBean.UserMetadata) (bool, error) {
	isResourceProtectionEnabled := impl.resourceProtectionService.ResourceProtectionEnabled(resourceKind, appId, envId, true, false)
	if isResourceProtectionEnabled {
		isUserException, err := impl.globalPolicyDataManager.IsUserAnExceptionByRqmResourceType(ctx, alpha1PolicyAdaptor.ApplyPolicyTypeToRqmResourceType(policyModel.APPLY_POLICY_APPROVAL_CONFIGURATION).ToInt(), userMetadata)
		if err != nil {
			impl.logger.Errorw("error in checking if a user is exception user or not by policy type ", "policyType", policyModel.APPLY_POLICY_APPROVAL_CONFIGURATION, "err", err)
			return false, err
		} else if !isUserException {
			return true, nil
		}
	}
	return false, nil
}
