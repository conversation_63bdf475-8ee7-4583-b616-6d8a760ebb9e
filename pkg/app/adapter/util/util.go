package util

import (
	"encoding/base64"
	"fmt"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/credentials/ec2rolecreds"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/ecr"
	"github.com/devtron-labs/common-lib/utils/registry"
	"github.com/devtron-labs/devtron/pkg/app/bean"
	"strings"
)

func ExtractCredentialsForRegistry(config *bean.ContainerRegistryConfig) (string, string, error) {
	username := config.Username
	pwd := config.Password
	if (config.RegistryType == registry.REGISTRY_TYPE_GCR.String() || config.RegistryType == registry.REGISTRY_TYPE_ARTIFACT_REGISTRY.String()) && username == registry.JSON_KEY_USERNAME {
		if strings.HasPrefix(pwd, "'") {
			pwd = pwd[1:]
		}
		if strings.HasSuffix(pwd, "'") {
			pwd = pwd[:len(pwd)-1]
		}
	}
	if config.RegistryType == registry.DOCKER_REGISTRY_TYPE_ECR.String() {
		accessKey, secretKey := config.AccessKey, config.SecretKey
		var creds *credentials.Credentials

		if len(config.AccessKey) == 0 || len(config.AccessKey) == 0 {
			sess, err := session.NewSession(&aws.Config{
				Region: &config.AwsRegion,
			})
			if err != nil {
				fmt.Println("error in creating AWS client %w ", err)
				return "", "", err
			}
			creds = ec2rolecreds.NewCredentials(sess)
		} else {
			creds = credentials.NewStaticCredentials(accessKey, secretKey, "")
		}
		sess, err := session.NewSession(&aws.Config{
			Region:      &config.AwsRegion,
			Credentials: creds,
		})
		if err != nil {
			fmt.Println("error in creating AWS client %w ", err)
			return "", "", err
		}
		svc := ecr.New(sess)
		input := &ecr.GetAuthorizationTokenInput{}
		authData, err := svc.GetAuthorizationToken(input)
		if err != nil {
			fmt.Println("error in creating AWS client %w ", err)
			return "", "", err
		}
		// decode token
		token := authData.AuthorizationData[0].AuthorizationToken
		decodedToken, err := base64.StdEncoding.DecodeString(*token)
		if err != nil {
			fmt.Println("error in creating AWS client %w ", err)
			return "", "", err
		}
		credsSlice := strings.Split(string(decodedToken), ":")
		username = credsSlice[0]
		pwd = credsSlice[1]

	}
	return username, pwd, nil
}
