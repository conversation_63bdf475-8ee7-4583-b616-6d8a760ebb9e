package appDetails

import (
	"context"
	"github.com/devtron-labs/devtron/api/helm-app/gRPC"
	"github.com/devtron-labs/devtron/api/helm-app/service/adapter"
	"github.com/devtron-labs/devtron/client/scoop"
	"github.com/devtron-labs/devtron/pkg/cluster/read"
	"go.uber.org/zap"
)

type ResourceTreeServiceV2 interface {
	FetchResourceTree(ctx context.Context, clusterId int, parentObjects []*gRPC.ObjectIdentifier, useFallBack bool, namespace string) (*gRPC.ResourceTreeResponse, error)
}

type ResourceTreeServiceV2Impl struct {
	logger             *zap.SugaredLogger
	scoopClientGetter  scoop.ScoopClientGetter
	helmAppClient      gRPC.HelmAppClient
	clusterReadService read.ClusterReadService
}

func NewResourceTreeServiceV2Impl(logger *zap.SugaredLogger,
	scoopClientGetter scoop.ScoopClientGetter,
	helmAppClient gRPC.HelmAppClient,
	clusterReadService read.ClusterReadService) *ResourceTreeServiceV2Impl {
	return &ResourceTreeServiceV2Impl{
		logger:             logger,
		scoopClientGetter:  scoopClientGetter,
		helmAppClient:      helmAppClient,
		clusterReadService: clusterReadService,
	}
}

func (impl *ResourceTreeServiceV2Impl) FetchResourceTree(ctx context.Context, clusterId int, parentObjects []*gRPC.ObjectIdentifier, useFallBack bool, namespace string) (*gRPC.ResourceTreeResponse, error) {
	scoopConfig, err := impl.scoopClientGetter.GetScoopCacheConfig(clusterId)
	if err != nil {
		impl.logger.Errorw("error in getting scoop client config", "clusterId", clusterId, "err", err)
		return nil, err
	}
	clusterObj, err := impl.clusterReadService.FindById(clusterId)
	if err != nil {
		impl.logger.Errorw("error in fetching cluster detail", "clusterId", clusterId, "err", err)
		return nil, err
	}
	resourceTreeReq := &gRPC.GetResourceTreeRequest{
		ObjectIdentifiers: parentObjects,
		CacheConfig:       scoopConfig,
		PreferCache:       true,
		UseFallBack:       useFallBack,
		ClusterConfig:     adapter.ConvertClusterBeanToClusterConfig(clusterObj),
		Namespace:         namespace,
	}

	resp, err := impl.helmAppClient.BuildResourceTreeUsingParentObjects(ctx, resourceTreeReq)
	if err != nil {
		impl.logger.Errorw("Error in BuildResourceTreeUsingParentObjects", "payload", resourceTreeReq, "err", err)
		return nil, err
	}
	return resp, nil
}
