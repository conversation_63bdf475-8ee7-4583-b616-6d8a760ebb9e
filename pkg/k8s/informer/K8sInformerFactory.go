/*
 * Copyright (c) 2024. Devtron Inc.
 */

package informer

import (
	"github.com/devtron-labs/common-lib-private/utils/k8s"
	k8s2 "github.com/devtron-labs/common-lib/utils/k8s"
	bean3 "github.com/devtron-labs/common-lib/utils/remoteConnection/bean"
	remoteConnectionBean "github.com/devtron-labs/devtron/pkg/remoteConnection/bean"
	"sync"
	"time"

	"github.com/devtron-labs/devtron/api/bean"
	"go.uber.org/zap"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	kubeinformers "k8s.io/client-go/informers"
	"k8s.io/client-go/tools/cache"
)

func NewGlobalMapClusterNamespace() sync.Map {
	var globalMapClusterNamespace sync.Map
	return globalMapClusterNamespace
}

type K8sInformerFactoryImpl struct {
	logger                    *zap.SugaredLogger
	globalMapClusterNamespace sync.Map // {"cluster1":{"ns1":true","ns2":true"}}
	informerStopper           map[string]chan struct{}
	k8sUtil                   *k8s.K8sUtilExtended
}

type K8sInformerFactory interface {
	GetLatestNamespaceListGroupByCLuster() map[string]map[string]*NamespaceMetadata
	BuildInformer(clusterInfo []*bean.ClusterInfo)
	CleanNamespaceInformer(clusterName string)
	DeleteClusterFromCache(clusterName string)
}

func NewK8sInformerFactoryImpl(logger *zap.SugaredLogger, globalMapClusterNamespace sync.Map, k8sUtil *k8s.K8sUtilExtended) *K8sInformerFactoryImpl {
	informerFactory := &K8sInformerFactoryImpl{
		logger:                    logger,
		globalMapClusterNamespace: globalMapClusterNamespace,
		k8sUtil:                   k8sUtil,
	}
	informerFactory.informerStopper = make(map[string]chan struct{})
	return informerFactory
}

func (impl *K8sInformerFactoryImpl) GetLatestNamespaceListGroupByCLuster() map[string]map[string]*NamespaceMetadata {
	copiedClusterNamespaces := make(map[string]map[string]*NamespaceMetadata)
	impl.globalMapClusterNamespace.Range(func(key, value interface{}) bool {
		clusterName := key.(string)
		allNamespaces := value.(*sync.Map)
		namespaceMap := make(map[string]*NamespaceMetadata)
		allNamespaces.Range(func(nsKey, nsValue interface{}) bool {
			namespaceMap[nsKey.(string)] = nsValue.(*NamespaceMetadata)
			return true
		})
		copiedClusterNamespaces[clusterName] = namespaceMap
		return true
	})
	return copiedClusterNamespaces
}

func (impl *K8sInformerFactoryImpl) BuildInformer(clusterInfo []*bean.ClusterInfo) {
	for _, info := range clusterInfo {
		clusterConfig := &k8s2.ClusterConfig{
			ClusterId:             info.ClusterId,
			ClusterName:           info.ClusterName,
			BearerToken:           info.BearerToken,
			Host:                  info.ServerUrl,
			InsecureSkipTLSVerify: info.InsecureSkipTLSVerify,
			KeyData:               info.KeyData,
			CertData:              info.CertData,
			CAData:                info.CAData,
		}

		if info.RemoteConnectionConfig != nil && info.RemoteConnectionConfig.ConnectionMethod != remoteConnectionBean.RemoteConnectionMethodDirect {
			connectionConfig := &bean3.RemoteConnectionConfigBean{
				RemoteConnectionConfigId: info.RemoteConnectionConfig.RemoteConnectionConfigId,
				ConnectionMethod:         bean3.RemoteConnectionMethod(info.RemoteConnectionConfig.ConnectionMethod),
			}
			if info.RemoteConnectionConfig.ProxyConfig != nil && string(info.RemoteConnectionConfig.ConnectionMethod) == string(bean3.RemoteConnectionMethodProxy) {
				connectionConfig.ProxyConfig = &bean3.ProxyConfig{
					ProxyUrl: info.RemoteConnectionConfig.ProxyConfig.ProxyUrl,
				}
			}
			if info.RemoteConnectionConfig.SSHTunnelConfig != nil && string(info.RemoteConnectionConfig.ConnectionMethod) == string(bean3.RemoteConnectionMethodSSH) {
				connectionConfig.SSHTunnelConfig = &bean3.SSHTunnelConfig{
					SSHServerAddress: info.RemoteConnectionConfig.SSHTunnelConfig.SSHServerAddress,
					SSHUsername:      info.RemoteConnectionConfig.SSHTunnelConfig.SSHUsername,
					SSHPassword:      info.RemoteConnectionConfig.SSHTunnelConfig.SSHPassword,
					SSHAuthKey:       info.RemoteConnectionConfig.SSHTunnelConfig.SSHAuthKey,
				}
			}
			clusterConfig.RemoteConnectionConfig = connectionConfig
		}
		impl.buildInformerAndNamespaceList(info.ClusterName, clusterConfig)
	}
	return
}

func (impl *K8sInformerFactoryImpl) buildInformerAndNamespaceList(clusterName string, clusterConfig *k8s2.ClusterConfig) sync.Map {
	allNamespaces := sync.Map{}
	impl.globalMapClusterNamespace.Store(clusterName, &allNamespaces)

	_, _, clusterClient, err := impl.k8sUtil.GetK8sConfigAndClients(clusterConfig)
	if err != nil {
		impl.logger.Errorw("error in getting k8s clientset", "err", err, "clusterName", clusterConfig.ClusterName)
		return impl.globalMapClusterNamespace
	}

	informerFactory := kubeinformers.NewSharedInformerFactoryWithOptions(clusterClient, time.Minute)
	nsInformer := informerFactory.Core().V1().Namespaces()
	stopper := make(chan struct{})

	nsInformer.Informer().AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc: func(obj interface{}) {
			if mobject, ok := obj.(metav1.Object); ok {
				value, _ := impl.globalMapClusterNamespace.Load(clusterName)
				allNamespaces := value.(*sync.Map)
				allNamespaces.Store(mobject.GetName(), &NamespaceMetadata{
					ResourceVersion: mobject.GetResourceVersion(),
					Labels:          mobject.GetLabels(),
				})
			}
		},
		UpdateFunc: func(oldObj, newObj interface{}) {
			if newObject, ok := newObj.(metav1.Object); ok {
				value, _ := impl.globalMapClusterNamespace.Load(clusterName)
				allNamespaces := value.(*sync.Map)
				allNamespaces.Store(newObject.GetName(), &NamespaceMetadata{
					ResourceVersion: newObject.GetResourceVersion(),
					Labels:          newObject.GetLabels(),
				})
			}
		},
		DeleteFunc: func(obj interface{}) {
			if object, ok := obj.(metav1.Object); ok {
				value, _ := impl.globalMapClusterNamespace.Load(clusterName)
				allNamespaces := value.(*sync.Map)
				allNamespaces.Delete(object.GetName())
			}
		},
	})

	informerFactory.Start(stopper)
	impl.informerStopper[clusterName] = stopper
	return impl.globalMapClusterNamespace
}

func (impl *K8sInformerFactoryImpl) CleanNamespaceInformer(clusterName string) {
	stopper := impl.informerStopper[clusterName]
	if stopper != nil {
		close(stopper)
		delete(impl.informerStopper, clusterName)
	}
	return
}

func (impl *K8sInformerFactoryImpl) DeleteClusterFromCache(clusterName string) {
	impl.CleanNamespaceInformer(clusterName)
	impl.globalMapClusterNamespace.Delete(clusterName)
	return
}
