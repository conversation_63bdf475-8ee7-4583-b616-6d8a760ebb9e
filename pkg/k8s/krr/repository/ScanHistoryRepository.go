package repository

import (
	"github.com/devtron-labs/devtron/pkg/sql"
	"github.com/go-pg/pg"
	"time"
)

type KrrScanHistoryRepository interface {
	GetByRequestIds(requestIds []int) ([]*KrrScanHistory, error)
	GetLastScannedOnTime(clusterId int) (time.Time, error)
	sql.TransactionWrapper
}

type KrrScanHistoryRepositoryImpl struct {
	dbConnection *pg.DB
	*sql.TransactionUtilImpl
}

func NewKrrScanHistoryRepositoryImpl(dbConnection *pg.DB) *KrrScanHistoryRepositoryImpl {
	return &KrrScanHistoryRepositoryImpl{
		dbConnection:        dbConnection,
		TransactionUtilImpl: sql.NewTransactionUtilImpl(dbConnection),
	}
}

type KrrScanHistory struct {
	tableName               struct{}  `sql:"krr_scan_history" pg:",discard_unknown_columns"`
	Id                      int       `sql:"id,pk"`
	KrrScanRequestId        int       `sql:"krr_scan_request,notnull"`
	Container               string    `sql:"container,notnull"`
	ScannedOn               time.Time `sql:"scanned_on,notnull"`
	ScannedBy               int       `sql:"scanned_by"`
	ResourceType            string    `sql:"resource_type"` // CPU / MEMORY
	RecommendedStrategyName string    `sql:"recommended_strategy_name"`
	RecommendedRequest      float64   `sql:"recommended_request"`
	RecommendedLimits       float64   `sql:"recommended_limits"`
	Unit                    string    `sql:"unit"`
	Evaluation              string    `sql:"evaluation"`     // JSON as string
	Recommendation          string    `sql:"recommendation"` // JSON as string
	sql.AuditLog
}

func (impl *KrrScanHistoryRepositoryImpl) GetByRequestIds(requestIds []int) ([]*KrrScanHistory, error) {
	if len(requestIds) == 0 {
		return make([]*KrrScanHistory, 0), nil
	}
	var models []*KrrScanHistory
	maxScannedOnQuery := impl.dbConnection.Model(&models).
		ColumnExpr("MAX(id) AS id").
		Where("krr_scan_request IN (?)", pg.In(requestIds)).
		Group("krr_scan_request", "container", "resource_type")
	err := impl.dbConnection.Model(&models).
		Column("krr_scan_history.*").
		Where("id IN (?)", maxScannedOnQuery).
		Select()
	return models, err
}

func (impl *KrrScanHistoryRepositoryImpl) GetLastScannedOnTime(clusterId int) (time.Time, error) {
	var model KrrScanHistory
	err := impl.dbConnection.Model().
		Table("krr_scan_history").
		ColumnExpr("MAX(scanned_on) AS scanned_on").
		Join("INNER JOIN krr_scan_request ksr").
		JoinOn("ksr.id = krr_scan_history.krr_scan_request").
		Where("ksr.cluster_id = ?", clusterId).
		Select(&model)
	return model.ScannedOn, err
}
