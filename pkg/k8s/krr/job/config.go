package job

import (
	"github.com/caarlos0/env/v6"
	"github.com/devtron-labs/devtron/pkg/sql"
	"strconv"
)

// CATEGORY=KRR_SYNC_JOB_CONFIG
type KRRAdditionalConfig struct {
	DockerImage                  string  `env:"KRR_DOCKER_IMAGE" envDefault:"" description:"Docker image for KRR sync job"`
	SyncJobResourcesObj          string  `env:"KRR_SYNC_JOB_RESOURCES_OBJ" envDefault:"{}" description:"KRR sync job resources object in JSON format"`
	EvaluateStrategies           bool    `env:"KRR_EVALUATE_STRATEGIES" envDefault:"true" description:"Flag to evaluate strategies during KRR sync job"`
	MatrixTimeRange              string  `env:"KRR_MATRIX_TIME_RANGE" envDefault:"120h" description:"Time range for Prometheus matrix queries in KRR sync job, e.g., '120h' for 120 hours"`
	MatrixStepInterval           string  `env:"KRR_MATRIX_STEP_INTERVAL" envDefault:"5m" description:"Step interval for Prometheus matrix queries in KRR sync job, e.g., '5m' for 5 minutes"`
	IgnoreCpuThrottling          bool    `env:"KRR_IGNORE_CPU_THROTTLING" envDefault:"false" description:"Flag to ignore CPU throttling during KRR sync job"`
	IgnoreOOM                    bool    `env:"KRR_IGNORE_OOM" envDefault:"false" description:"Flag to ignore OOM (Out of Memory) events during KRR sync job"`
	MinCPURequest                float64 `env:"MIN_CPU_REQUEST" envDefault:"0.1" description:"Minimum CPU request to consider in recommendations (in cores, 0 means no minimum)"`
	MinMemRequest                float64 `env:"MIN_MEMORY_REQUEST" envDefault:"1.0" description:"Minimum memory request to consider in recommendations (in MiB, 0 means no minimum)"`
	KRRSyncServiceAccount        string  `env:"KRR_SYNC_SERVICE_ACCOUNT" envDefault:"krr-sync" description:"Service account for KRR sync job"`
	WorkloadListParallelismLimit int     `env:"PARALLELISM_LIMIT_FOR_TAG_PROCESSING" envDefault:"1" description:"Parallelism limit for workload list processing in KRR sync job"`
	SyncJobShutDownWaitDuration  int     `env:"KRR_SYNC_JOB_SHUTDOWN_WAIT_DURATION" envDefault:"120" description:"Duration in seconds to wait for KRR sync job shutdown before force termination, default is 60 seconds"`
	HTTPPort                     int     `env:"KRR_SYNC_JOB_HTTP_PORT" envDefault:"8080" description:"Port for Prometheus matrix queries in KRR sync job, default is 8080"`
	LogLevel                     int     `env:"KRR_LOG_LEVEL" envDefault:"-1" description:"Log level for KRR sync job, default is -1 (debug level), 0 (info), 1 (warn), 2 (error)"`
	KrrCronSchedule              string  `env:"KRR_SYNC_JOB_CRON_SCHEDULE" envDefault:"0 0 * * *" description:"Cron schedule for KRR sync job, default is '0 0 * * *' (daily at midnight)"`
	KrrActiveDeadlineSeconds     int64   `env:"KRR_ACTIVE_DEADLINE_SECONDS" envDefault:"15000" description:"Active deadline seconds for KRR sync job, default is 15000 seconds (~4 hours)"`
}

func GetKRRAdditionalConfig() (*KRRAdditionalConfig, error) {
	var krrAdditionalConfig KRRAdditionalConfig
	err := env.Parse(&krrAdditionalConfig)
	if err != nil {
		return nil, err
	}
	return &krrAdditionalConfig, nil
}

type KRRSyncConfig struct {
	DbConfig            sql.Config
	KRRAdditionalConfig *KRRAdditionalConfig
	ClusterId           ClusterIdentifier
}

type ClusterIdentifier string

func NewClusterIdentifier(clusterId *int) ClusterIdentifier {
	if clusterId == nil || *clusterId <= 0 {
		return ClusterIdentifierTypeAll
	}
	return ClusterIdentifier(strconv.Itoa(*clusterId))
}

const (
	ClusterIdentifierTypeAll ClusterIdentifier = "*"
)
