package job

import (
	"bytes"
	"github.com/devtron-labs/devtron/pkg/sql"
	"text/template"
)

func getManualKRRSyncJobByteArr(sqlConfig *sql.Config, krrAdditionalConfig *KRRAdditionalConfig, clusterId ClusterIdentifier) ([]byte, error) {
	configValues := KRRSyncConfig{
		DbConfig: sql.Config{
			Addr:                    sqlConfig.Addr,
			Database:                sqlConfig.Database,
			User:                    sqlConfig.User,
			Password:                sqlConfig.Password,
			PgQueryMonitoringConfig: sqlConfig.PgQueryMonitoringConfig,
		},
		KRRAdditionalConfig: krrAdditionalConfig,
		ClusterId:           clusterId,
	}
	temp := template.New("manualKrrSyncJobByteArr")
	temp, _ = temp.Parse(`{"apiVersion": "batch/v1",
  "kind": "Job",
  "metadata": {
    "labels": {
       "app": "krr-manual-sync-job",
       "component": "devtron"
    },
    "name": "krr-manual-sync-job",
    "namespace": "devtroncd"
  },
  "spec": {
    "template": {
      "metadata": {
        "labels": {
          "app": "krr-manual-sync-job",
          "component": "devtron"
        }
      }, 
      "spec": {
		"serviceAccount": "{{.KRRAdditionalConfig.KRRSyncServiceAccount}}",
        "containers": [
          {
            "name": "krr-sync",
            "image": "{{.KRRAdditionalConfig.DockerImage}}",
 			"ports": [
              {
                "containerPort": 8080,
                "name": "metrics",
                "protocol": "TCP"
              }
            ],
			{{if .KRRAdditionalConfig.SyncJobResourcesObj}}
			"resources": {{.KRRAdditionalConfig.SyncJobResourcesObj}},
            {{end}}
            "env": [
              {
                "name": "CLUSTER_ID",
                "value": "{{.ClusterId}}"
              },
			  {
                "name": "PG_ADDR",
                "value": "{{.DbConfig.Addr}}"
              },
              {
                "name": "PG_DATABASE",
                "value": "{{.DbConfig.Database}}"
              },
              {
                "name": "PG_USER",
                "value": "{{.DbConfig.User}}"
              },
              {
                "name": "PG_PASSWORD",
                "value": "{{.DbConfig.Password}}"
              },
			  {				
				"name": "EVALUATE_STRATEGIES",
	 			"value": "{{.KRRAdditionalConfig.EvaluateStrategies}}"
			  },
			  {
				"name": "TIME_RANGE",
	 			"value": "{{.KRRAdditionalConfig.MatrixTimeRange}}"
			  },
			  {
				"name": "STEP_INTERVAL",
	 			"value": "{{.KRRAdditionalConfig.MatrixStepInterval}}"
			  },
			  {
				"name": "IGNORE_CPU_THROTTLING",
	 			"value": "{{.KRRAdditionalConfig.IgnoreCpuThrottling}}"
			  },
			  {
				"name": "IGNORE_OOM",
	 			"value": "{{.KRRAdditionalConfig.IgnoreOOM}}"
			  },
			  {
				"name": "MIN_CPU_REQUEST",
	 			"value": "{{.KRRAdditionalConfig.MinCPURequest}}"
			  },
			  {				
				"name": "MIN_MEMORY_REQUEST",
	 			"value": "{{.KRRAdditionalConfig.MinMemRequest}}"
			  },
              {
				"name": "WORKLOAD_LIST_PARALLELISM_LIMIT",
     			"value": "{{.KRRAdditionalConfig.WorkloadListParallelismLimit}}"
              },
			  {
				"name": "KRR_SYNC_SHUTDOWN_WAIT_DURATION",
     			"value": "{{.KRRAdditionalConfig.SyncJobShutDownWaitDuration}}"
              },
			  {
				"name": "KRR_SYNC_JOB_HTTP_PORT",
	 			"value": "{{.KRRAdditionalConfig.HTTPPort}}"
			  },
			  {
				"name": "LOG_LEVEL",
				"value": "{{.KRRAdditionalConfig.LogLevel}}"
			  }
            ]
          }
        ],
        "restartPolicy": "OnFailure"
      }
    },
    "backoffLimit": 4,
    "activeDeadlineSeconds": {{.KRRAdditionalConfig.KrrActiveDeadlineSeconds}}
  }
}`)

	var manualKrrSyncJobBufferBytes bytes.Buffer
	if err := temp.Execute(&manualKrrSyncJobBufferBytes, configValues); err != nil {
		return nil, err
	}
	manualKrrSyncJobByteArr := []byte(manualKrrSyncJobBufferBytes.String())
	return manualKrrSyncJobByteArr, nil
}
