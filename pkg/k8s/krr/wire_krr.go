package krr

import (
	"github.com/devtron-labs/devtron/pkg/k8s/krr/job"
	"github.com/devtron-labs/devtron/pkg/k8s/krr/read"
	repository2 "github.com/devtron-labs/devtron/pkg/k8s/krr/repository"
	"github.com/google/wire"
)

var WireSet = wire.NewSet(
	repository2.NewKrrScanHistoryRepositoryImpl,
	wire.Bind(new(repository2.KrrScanHistoryRepository), new(*repository2.KrrScanHistoryRepositoryImpl)),
	repository2.NewKrrScanRequestRepositoryImpl,
	wire.Bind(new(repository2.KrrScanRequestRepository), new(*repository2.KrrScanRequestRepositoryImpl)),
	read.NewKRRScanReadServiceImpl,
	wire.Bind(new(read.KRRScanService), new(*read.KRRScanServiceImpl)),
	job.NewTriggerKrrJobImpl,
	wire.Bind(new(job.TriggerKrrJob), new(*job.TriggerKrrJobImpl)),
	NewServiceImpl,
	wire.Bind(new(Service), new(*ServiceImpl)),
)
