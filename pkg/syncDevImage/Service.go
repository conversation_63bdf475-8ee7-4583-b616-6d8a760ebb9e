package syncDevImage

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/credentials/ec2rolecreds"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/ecr"
	"github.com/devtron-labs/common-lib/utils/k8s"
	bean2 "github.com/devtron-labs/devtron/api/bean"
	"github.com/devtron-labs/devtron/internal/sql/repository"
	bean5 "github.com/devtron-labs/devtron/pkg/auth/user/bean"
	"github.com/devtron-labs/devtron/pkg/bean"
	"github.com/devtron-labs/devtron/pkg/build/trigger"
	"github.com/devtron-labs/devtron/pkg/cluster"
	repository3 "github.com/devtron-labs/devtron/pkg/cluster/environment/repository"
	repository2 "github.com/devtron-labs/devtron/pkg/cluster/repository"
	"github.com/devtron-labs/devtron/pkg/deployment/trigger/devtronApps"
	bean3 "github.com/devtron-labs/devtron/pkg/deployment/trigger/devtronApps/bean"
	"github.com/devtron-labs/devtron/pkg/k8s/application"
	bean4 "github.com/devtron-labs/devtron/pkg/k8s/bean"
	"github.com/devtron-labs/devtron/pkg/pipeline"
	"github.com/devtron-labs/devtron/pkg/pipeline/types"
	"github.com/devtron-labs/devtron/pkg/pipeline/workflowStatus"
	"github.com/devtron-labs/devtron/pkg/sql"
	"github.com/devtron-labs/devtron/pkg/terminal"
	"github.com/devtron-labs/devtron/util"
	"go.uber.org/zap"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"log"
	"strings"
	"time"
)

type Service interface {
	SyncDevImage(payload Payload, userMetadata *bean5.UserMetadata) error
}

type ServiceImpl struct {
	logger                     *zap.SugaredLogger
	cdPipelineConfigService    pipeline.CdPipelineConfigService
	ciService                  pipeline.CiService
	ciArtifactRepository       repository.CiArtifactRepository
	workFlowStageStatusService workflowStatus.WorkFlowStageStatusService
	cdHandlerService           devtronApps.HandlerService
	k8sService                 k8s.K8sService
	clusterService             cluster.ClusterService
	envRepo                    repository3.EnvironmentRepository
	k8sApplicationService      application.K8sApplicationService
	terminalSessionHandler     terminal.TerminalSessionHandler
	ciHandlerService           trigger.HandlerService
}

func NewServiceImpl(logger *zap.SugaredLogger, cdPipelineConfigService pipeline.CdPipelineConfigService, ciService pipeline.CiService,
	ciArtifactRepository repository.CiArtifactRepository, workFlowStageStatusService workflowStatus.WorkFlowStageStatusService,
	cdHandlerService devtronApps.HandlerService, k8sService k8s.K8sService, k8sApplicationService application.K8sApplicationService,
	clusterService cluster.ClusterService, envRepo repository3.EnvironmentRepository, terminalSessionHandler terminal.TerminalSessionHandler,
	ciHandlerService trigger.HandlerService) Service {
	return &ServiceImpl{
		logger:                     logger,
		cdPipelineConfigService:    cdPipelineConfigService,
		ciService:                  ciService,
		ciArtifactRepository:       ciArtifactRepository,
		workFlowStageStatusService: workFlowStageStatusService,
		cdHandlerService:           cdHandlerService,
		k8sService:                 k8sService,
		k8sApplicationService:      k8sApplicationService,
		clusterService:             clusterService,
		envRepo:                    envRepo,
		terminalSessionHandler:     terminalSessionHandler,
		ciHandlerService:           ciHandlerService,
	}
}

type Payload struct {
	AppId            int    `json:"appId"`
	EnvId            int    `json:"envId"`
	NodeName         string `json:"nodeName"`
	DeployedImageTag string `json:"deployedImageTag"`
	UserId           int32  `json:"-"`
}

func (impl *ServiceImpl) SyncDevImage(payload Payload, userMetadata *bean5.UserMetadata) error {

	appId := payload.AppId
	envId := payload.EnvId
	cdPipelines, err := impl.cdPipelineConfigService.GetCdPipelinesByAppAndEnv(appId, envId, "")
	if err != nil {
		return err
	}
	pipelines := cdPipelines.Pipelines
	if len(pipelines) == 0 {
		return errors.New("no pipeline found")
	}
	cdPipelineObj := pipelines[0]
	ciPipelineId := cdPipelineObj.CiPipelineId
	trigger := &types.CiTriggerRequest{PipelineId: ciPipelineId, TriggeredBy: payload.UserId}
	_, ciWorkflow, workflowRequest, err := impl.ciHandlerService.StartCiWorkflowAndPrepareWfRequest(trigger)
	if err != nil {
		return err
	}
	//deployedImageTag, err := impl.fetchDeployedImageTag(cdPipelineObj)
	//if err != nil {
	//	return err
	//}
	startTime := time.Now()
	newImageTag, newImageDigest, err := impl.triggerImageBuild(payload, workflowRequest)
	if err != nil {
		return err
	}
	elapsedTime := time.Since(startTime)
	ciWfId := ciWorkflow.Id
	impl.logger.Infow("build made successfully, going to update ci workflow", "time", elapsedTime, "ciWorkflow", ciWfId)
	ciWorkflow.Status = string(v1alpha1.NodeSucceeded)
	ciWorkflow.FinishedOn = time.Now()
	err = impl.ciService.UpdateCiWorkflowWithStage(ciWorkflow)
	if err != nil {
		impl.logger.Errorw("update wf failed", "id", ciWfId, "err", err)
		return err
	}
	now := time.Now()
	materialJson, _ := json.Marshal(make([]repository.CiMaterialInfo, 0))
	buildArtifact := &repository.CiArtifact{
		Image:        newImageTag,
		ImageDigest:  newImageDigest,
		MaterialInfo: string(materialJson),
		DataSource:   repository.WEBHOOK,
		PipelineId:   ciPipelineId,
		WorkflowId:   &ciWfId,
		AuditLog:     sql.AuditLog{CreatedBy: payload.UserId, UpdatedBy: payload.UserId, CreatedOn: now, UpdatedOn: now},
	}
	if err = impl.ciArtifactRepository.Save(buildArtifact); err != nil {
		impl.logger.Errorw("error in saving material", "err", err)
		return err
	}

	// trigger deployment
	valuesOverrideRequest := &bean2.ValuesOverrideRequest{
		PipelineId:     cdPipelineObj.Id,
		AppId:          appId,
		CiArtifactId:   buildArtifact.Id,
		UserId:         payload.UserId,
		CdWorkflowType: bean2.CD_WORKFLOW_TYPE_DEPLOY,
	}
	triggerContext := bean3.TriggerContext{
		Context:     context.Background(),
		TriggerType: bean3.Manual,
	}
	_, _, _, err = impl.cdHandlerService.ManualCdTrigger(triggerContext, valuesOverrideRequest, userMetadata)
	return err
}

func (impl *ServiceImpl) triggerImageBuild(payload Payload, workflowRequest *types.WorkflowRequest) (string, string, error) {
	//newImageTag := existingImageTag + "-patch2"
	newImageTag := payload.DeployedImageTag + "-patch"
	targetNode := payload.NodeName
	dockerCredentials := &DockerCredentials{
		DockerUsername:     workflowRequest.DockerUsername,
		DockerPassword:     workflowRequest.DockerPassword,
		AwsRegion:          workflowRequest.AwsRegion,
		AccessKey:          workflowRequest.AccessKey,
		SecretKey:          workflowRequest.SecretKey,
		DockerRegistryURL:  workflowRequest.DockerRegistryURL,
		DockerRegistryType: workflowRequest.DockerRegistryType,
		CredentialsType:    workflowRequest.CredentialsType,
	}
	username, pwd, hostName, err := impl.getDockerLoginCred(dockerCredentials)
	if err != nil {
		return "", "", err
	}
	if hostName == "docker.io" {
		hostName = ""
	}
	envMap := make(map[string]string)
	envMap["IMAGE_TAG"] = payload.DeployedImageTag
	envMap["NEW_IMAGE_TAG"] = newImageTag
	envMap["USERNAME"] = username
	envMap["PASSWORD"] = pwd
	envMap["HOSTNAME"] = hostName

	environment, err := impl.envRepo.FindById(payload.EnvId)
	if err != nil {
		return "", "", err
	}
	outputStr, errStr, err := impl.executeRemoteCommand(context.Background(), targetNode, "devtron-demo", envMap, environment.Cluster)
	impl.logger.Infow("output of command", "op", outputStr, "errStr", errStr, "err", err)
	if err != nil {
		return "", "", err
	}
	newImageDigest := impl.extractImageDigest(outputStr)
	return newImageTag, newImageDigest, nil
}

type DockerCredentials struct {
	DockerUsername, DockerPassword, AwsRegion, AccessKey, SecretKey, DockerRegistryURL, DockerRegistryType, CredentialsType string
}

const DOCKER_REGISTRY_TYPE_ECR = "ecr"
const DOCKER_REGISTRY_TYPE_DOCKERHUB = "docker-hub"
const DOCKER_REGISTRY_TYPE_OTHER = "other"
const REGISTRY_TYPE_ARTIFACT_REGISTRY = "artifact-registry"
const REGISTRY_TYPE_GCR = "gcr"
const JSON_KEY_USERNAME = "_json_key"

func (impl *ServiceImpl) getDockerLoginCred(dockerCredentials *DockerCredentials) (string, string, string, error) {
	username := dockerCredentials.DockerUsername
	pwd := dockerCredentials.DockerPassword
	if dockerCredentials.DockerRegistryType == DOCKER_REGISTRY_TYPE_ECR {
		accessKey, secretKey := dockerCredentials.AccessKey, dockerCredentials.SecretKey
		//fmt.Printf("accessKey %s, secretKey %s\n", accessKey, secretKey)

		var creds *credentials.Credentials

		if len(dockerCredentials.AccessKey) == 0 || len(dockerCredentials.SecretKey) == 0 {
			//fmt.Println("empty accessKey or secretKey")
			sess, err := session.NewSession(&aws.Config{
				Region: &dockerCredentials.AwsRegion,
			})
			if err != nil {
				log.Println(err)
				return "", "", "", err
			}
			creds = ec2rolecreds.NewCredentials(sess)
		} else {
			creds = credentials.NewStaticCredentials(accessKey, secretKey, "")
		}
		sess, err := session.NewSession(&aws.Config{
			Region:      &dockerCredentials.AwsRegion,
			Credentials: creds,
		})
		if err != nil {
			log.Println(err)
			return "", "", "", err
		}
		svc := ecr.New(sess)
		input := &ecr.GetAuthorizationTokenInput{}
		authData, err := svc.GetAuthorizationToken(input)
		if err != nil {
			log.Println(err)
			return "", "", "", err
		}
		// decode token
		token := authData.AuthorizationData[0].AuthorizationToken
		decodedToken, err := base64.StdEncoding.DecodeString(*token)
		if err != nil {
			log.Println(err)
			return "", "", "", err
		}
		credsSlice := strings.Split(string(decodedToken), ":")
		username = credsSlice[0]
		pwd = credsSlice[1]

	} else if (dockerCredentials.DockerRegistryType == REGISTRY_TYPE_GCR || dockerCredentials.DockerRegistryType == REGISTRY_TYPE_ARTIFACT_REGISTRY) && username == JSON_KEY_USERNAME {
		// for gcr and artifact registry password is already saved as string in DB
		if strings.HasPrefix(pwd, "'") {
			pwd = pwd[1:]
		}
		if strings.HasSuffix(pwd, "'") {
			pwd = pwd[:len(pwd)-1]
		}
	}
	host := dockerCredentials.DockerRegistryURL
	return username, pwd, host, nil
	//dockerLogin := fmt.Sprintf("docker login -u '%s' -p '%s' '%s' ", username, pwd, host)
	//awsLoginCmd := impl.GetCommandToExecute(dockerLogin)
	//err := util.RunCommand(awsLoginCmd)
	//if err != nil {
	//	log.Println(err)
	//	return err
	//}
	//log.Println("Docker login successful with username ", username, " on docker registry URL ", dockerCredentials.DockerRegistryURL)
	//return nil
}

func (impl *ServiceImpl) executeRemoteCommand(ctx context.Context, nodeName string, namespace string, envParams map[string]string, clusterDto *repository2.Cluster) (string, string, error) {
	clusterId := clusterDto.Id
	podName, err := impl.extractPodName(clusterId, nodeName)
	if err != nil {
		return "", "", err
	}

	envStr := ""
	for envKey, envValue := range envParams {
		envStr = envStr + "export " + envKey + "=" + envValue + ";"
	}

	executeOnly := "./devtron-dev-ci.sh"
	commands := []string{"/bin/bash", "-c", envStr + executeOnly}
	buf := &bytes.Buffer{}
	req := &terminal.TerminalSessionRequest{
		PodName:   podName,
		Namespace: namespace,
		ClusterId: clusterId,
	}
	errBuf, err := impl.terminalSessionHandler.RunCmdInRemotePodWithPipeOutWriter(req, commands, buf)
	return buf.String(), errBuf.String(), err
}

func (impl *ServiceImpl) extractImageDigest(outputStr string) string {
	shaPrefix := "config-sha256:"
	shaPrefixLen := len(shaPrefix)
	digestStartIndex := strings.Index(outputStr, shaPrefix) + shaPrefixLen
	remainingStr := outputStr[digestStartIndex:]
	digestEndIndex := strings.Index(remainingStr, ":")
	return outputStr[digestStartIndex : digestStartIndex+digestEndIndex]
}

func (impl *ServiceImpl) extractPodName(clusterId int, nodeName string) (string, error) {
	filterExpr := fmt.Sprintf("self.spec.nodeName=='%s'", nodeName)
	resourceRequestBean := &bean4.ResourceRequestBean{
		K8sRequest: &k8s.K8sRequestBean{
			ResourceIdentifier: k8s.ResourceIdentifier{
				GroupVersionKind: schema.GroupVersionKind{
					Group:   "",
					Version: "v1",
					Kind:    "Pod",
				},
			},
		},
		ClusterId:     clusterId,
		Filter:        filterExpr,
		LabelSelector: []string{"name=kb-daemonset-app"},
	}
	resourceList, err := impl.k8sApplicationService.GetResourceList(context.Background(), "", resourceRequestBean, func(token string, clusterName string, request bean4.ResourceRequestBean, casbinAction string) bool {
		return true
	})
	if err != nil {
		return "", err
	}
	if len(resourceList.Data) > 0 {
		resourceData := resourceList.Data[0]
		return resourceData["name"].(string), nil
	}
	return "", errors.New("no pod found")
}

func (impl *ServiceImpl) fetchDeployedImageTag(cdPipelineObj *bean.CDPipelineConfigObject) (string, error) {
	cdMaterialsRequest := &bean2.CdNodeMaterialParams{}
	listingFilterOptions := util.ListingFilterOptions{Limit: 1}
	cdMaterialsRequest = cdMaterialsRequest.WithCDPipelineId(cdPipelineObj.Id).WithListingFilterOptions(listingFilterOptions)

	artifactEntities, _, err := impl.ciArtifactRepository.FindDeployedArtifactsOnPipeline(cdMaterialsRequest)
	if err != nil {
		return "", err
	}
	if len(artifactEntities) > 0 {
		return artifactEntities[0].Image, nil
	}
	return "", errors.New("no recent images found")
}
