package beans

import (
	"github.com/devtron-labs/devtron/internal/sql/repository/appWorkflow"
	"github.com/devtron-labs/devtron/pkg/bean"
	"github.com/devtron-labs/devtron/pkg/build/pipeline/bean/common"
	"github.com/devtron-labs/devtron/util"
)

type PipelineConverter interface {
	Validate(ctx *util.RequestCtx, request *BulkSwitchCiReq) ([]*CiPipelineConversionResponseItem, error)
	Switch(ctx *util.RequestCtx, request *BulkSwitchCiReq) ([]*CiPipelineConversionResponseItem, error)
	Type() common.PipelineType
}

type SwitchPipelineSourceData struct {
	PipelineId   int                 `json:"pipelineId"`
	PipelineType common.PipelineType `json:"pipelineType"`
	*SourceMetadata
}

type SourceMetadata struct {
	appWorkflowId int
	appId         int
}

func (sourceMeta *SourceMetadata) WithAppWorkflowId(appWorkflowId int) *SourceMetadata {
	sourceMeta.appWorkflowId = appWorkflowId
	return sourceMeta
}

func (sourceMeta *SourceMetadata) WithAppId(appId int) *SourceMetadata {
	sourceMeta.appId = appId
	return sourceMeta
}

func (SourceMetadata *SourceMetadata) AppId() int {
	return SourceMetadata.appId
}

func (SourceMetadata *SourceMetadata) AppWorkflowId() int {
	return SourceMetadata.appWorkflowId
}

func ComponentType(pipelineType common.PipelineType) string {
	if pipelineType == common.EXTERNAL {
		return appWorkflow.WEBHOOK
	}
	return appWorkflow.CIPIPELINE
}

type BulkSwitchCiReq struct {
	SourcePipelineData      []*SwitchPipelineSourceData `json:"sourcePipelines"`
	DestinationPipelineData *DestinationPipelineData    `json:"destinationPipelineData"`
}

func (request *DestinationPipelineData) GetCiPipeline(gitMaterialId int, sourceConfig *bean.SourceTypeConfig) *bean.CiPipeline {
	pipeline := &bean.CiPipeline{
		Active:       true,
		Name:         "ci-pipeline-" + util.Generate(5),
		PipelineType: common.CI_BUILD,
		CiMaterial:   []*bean.CiMaterial{{GitMaterialId: gitMaterialId, Source: sourceConfig}},
		DockerArgs:   make(map[string]string),
		// todo: add workflow cache config
	}
	pipeline.IsManual = request.CustomConfigurations.IsManual
	pipeline.ScanEnabled = request.CustomConfigurations.ScanEnabled
	return pipeline
}

type DestinationPipelineData struct {
	PipelineType         common.PipelineType          `json:"pipelineType"`
	CustomConfigurations *CustomPipelineConfiguration `json:"customConfigurations"`
}

type CustomPipelineConfiguration struct {
	*LinkedCdCustomConfiguration
	*CiBuildCustomConfiguration
}

type CiBuildCustomConfiguration struct {
	IsManual         bool                  `json:"isManual"`
	ScanEnabled      bool                  `json:"scanEnabled,notnull"`
	CiMaterialSource bean.SourceTypeConfig `json:"ciMaterialSource"`
}

type CiPipelineConversionResponseItem struct {
	SourcePipelineId   int                 `json:"sourcePipelineId"`   // Required field
	SourcePipelineType common.PipelineType `json:"sourcePipelineType"` // Required field
	Status             Status              `json:"status"`             // Required field, should match enum values
	StatusDetails      string              `json:"statusDetails,omitempty"`
}

type Status string

const (
	SUCCESS = "Success"
	FAIL    = "Fail"
)
