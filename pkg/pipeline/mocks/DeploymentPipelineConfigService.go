// Code generated by mockery v2.32.0. DO NOT EDIT.

package mocks

import (
	apibean "github.com/devtron-labs/devtron/api/bean"
	bean "github.com/devtron-labs/devtron/pkg/bean"
	repositorybean "github.com/devtron-labs/devtron/pkg/cluster/environment/bean"
	commonbean "github.com/devtron-labs/devtron/pkg/deployment/gitOps/common/bean"

	context "context"

	mock "github.com/stretchr/testify/mock"

	pipelineConfig "github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"

	resourceGroup "github.com/devtron-labs/devtron/pkg/resourceGroup"
)

// CdPipelineConfigService is an autogenerated mock type for the CdPipelineConfigService type
type CdPipelineConfigService struct {
	mock.Mock
}

// CreateCdPipelines provides a mock function with given fields: cdPipelines, ctx
func (_m *CdPipelineConfigService) CreateCdPipelines(cdPipelines *bean.CdPipelines, ctx context.Context) (*bean.CdPipelines, error) {
	ret := _m.Called(cdPipelines, ctx)

	var r0 *bean.CdPipelines
	var r1 error
	if rf, ok := ret.Get(0).(func(*bean.CdPipelines, context.Context) (*bean.CdPipelines, error)); ok {
		return rf(cdPipelines, ctx)
	}
	if rf, ok := ret.Get(0).(func(*bean.CdPipelines, context.Context) *bean.CdPipelines); ok {
		r0 = rf(cdPipelines, ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bean.CdPipelines)
		}
	}

	if rf, ok := ret.Get(1).(func(*bean.CdPipelines, context.Context) error); ok {
		r1 = rf(cdPipelines, ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DeleteACDAppCdPipelineWithNonCascade provides a mock function with given fields: _a0, ctx, forceDelete, userId
func (_m *CdPipelineConfigService) DeleteACDAppCdPipelineWithNonCascade(_a0 *pipelineConfig.Pipeline, ctx context.Context, forceDelete bool, userId int32) error {
	ret := _m.Called(_a0, ctx, forceDelete, userId)

	var r0 error
	if rf, ok := ret.Get(0).(func(*pipelineConfig.Pipeline, context.Context, bool, int32) error); ok {
		r0 = rf(_a0, ctx, forceDelete, userId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteCdPipeline provides a mock function with given fields: _a0, ctx, deleteAction, acdDelete, userId
func (_m *CdPipelineConfigService) DeleteCdPipeline(_a0 *pipelineConfig.Pipeline, ctx context.Context, deleteAction int, acdDelete bool, userId int32) (*bean.AppDeleteResponseDTO, error) {
	ret := _m.Called(_a0, ctx, deleteAction, acdDelete, userId)

	var r0 *bean.AppDeleteResponseDTO
	var r1 error
	if rf, ok := ret.Get(0).(func(*pipelineConfig.Pipeline, context.Context, int, bool, int32) (*bean.AppDeleteResponseDTO, error)); ok {
		return rf(_a0, ctx, deleteAction, acdDelete, userId)
	}
	if rf, ok := ret.Get(0).(func(*pipelineConfig.Pipeline, context.Context, int, bool, int32) *bean.AppDeleteResponseDTO); ok {
		r0 = rf(_a0, ctx, deleteAction, acdDelete, userId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bean.AppDeleteResponseDTO)
		}
	}

	if rf, ok := ret.Get(1).(func(*pipelineConfig.Pipeline, context.Context, int, bool, int32) error); ok {
		r1 = rf(_a0, ctx, deleteAction, acdDelete, userId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DeleteHelmTypePipelineDeploymentApp provides a mock function with given fields: ctx, forceDelete, _a2
func (_m *CdPipelineConfigService) DeleteHelmTypePipelineDeploymentApp(ctx context.Context, forceDelete bool, _a2 *pipelineConfig.Pipeline) error {
	ret := _m.Called(ctx, forceDelete, _a2)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, bool, *pipelineConfig.Pipeline) error); ok {
		r0 = rf(ctx, forceDelete, _a2)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ExtractParentMetaDataByPipeline provides a mock function with given fields: _a0, stage
func (_m *CdPipelineConfigService) ExtractParentMetaDataByPipeline(_a0 *pipelineConfig.Pipeline, stage apibean.WorkflowType) (int, apibean.WorkflowType, int, error) {
	ret := _m.Called(_a0, stage)

	var r0 int
	var r1 apibean.WorkflowType
	var r2 int
	var r3 error
	if rf, ok := ret.Get(0).(func(*pipelineConfig.Pipeline, apibean.WorkflowType) (int, apibean.WorkflowType, int, error)); ok {
		return rf(_a0, stage)
	}
	if rf, ok := ret.Get(0).(func(*pipelineConfig.Pipeline, apibean.WorkflowType) int); ok {
		r0 = rf(_a0, stage)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(*pipelineConfig.Pipeline, apibean.WorkflowType) apibean.WorkflowType); ok {
		r1 = rf(_a0, stage)
	} else {
		r1 = ret.Get(1).(apibean.WorkflowType)
	}

	if rf, ok := ret.Get(2).(func(*pipelineConfig.Pipeline, apibean.WorkflowType) int); ok {
		r2 = rf(_a0, stage)
	} else {
		r2 = ret.Get(2).(int)
	}

	if rf, ok := ret.Get(3).(func(*pipelineConfig.Pipeline, apibean.WorkflowType) error); ok {
		r3 = rf(_a0, stage)
	} else {
		r3 = ret.Error(3)
	}

	return r0, r1, r2, r3
}

// FindActiveByAppIdAndEnvNames provides a mock function with given fields: appId, envNames
func (_m *CdPipelineConfigService) FindActiveByAppIdAndEnvNames(appId int, envNames []string) ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called(appId, envNames)

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int, []string) ([]*pipelineConfig.Pipeline, error)); ok {
		return rf(appId, envNames)
	}
	if rf, ok := ret.Get(0).(func(int, []string) []*pipelineConfig.Pipeline); ok {
		r0 = rf(appId, envNames)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int, []string) error); ok {
		r1 = rf(appId, envNames)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAppAndEnvDetailsByListFilter provides a mock function with given fields: filter
func (_m *CdPipelineConfigService) FindAppAndEnvDetailsByListFilter(filter pipelineConfig.CdPipelineListFilter) ([]pipelineConfig.CdPipelineMetaData, error) {
	ret := _m.Called(filter)

	var r0 []pipelineConfig.CdPipelineMetaData
	var r1 error
	if rf, ok := ret.Get(0).(func(pipelineConfig.CdPipelineListFilter) ([]pipelineConfig.CdPipelineMetaData, error)); ok {
		return rf(filter)
	}
	if rf, ok := ret.Get(0).(func(pipelineConfig.CdPipelineListFilter) []pipelineConfig.CdPipelineMetaData); ok {
		r0 = rf(filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]pipelineConfig.CdPipelineMetaData)
		}
	}

	if rf, ok := ret.Get(1).(func(pipelineConfig.CdPipelineListFilter) error); ok {
		r1 = rf(filter)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAppAndEnvDetailsByPipelineId provides a mock function with given fields: cdPipelineId
func (_m *CdPipelineConfigService) FindAppAndEnvDetailsByPipelineId(cdPipelineId int) (*pipelineConfig.Pipeline, error) {
	ret := _m.Called(cdPipelineId)

	var r0 *pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*pipelineConfig.Pipeline, error)); ok {
		return rf(cdPipelineId)
	}
	if rf, ok := ret.Get(0).(func(int) *pipelineConfig.Pipeline); ok {
		r0 = rf(cdPipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(cdPipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAppAndEnvironmentAndProjectByPipelineIds provides a mock function with given fields: pipelineIds
func (_m *CdPipelineConfigService) FindAppAndEnvironmentAndProjectByPipelineIds(pipelineIds []int) ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called(pipelineIds)

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*pipelineConfig.Pipeline, error)); ok {
		return rf(pipelineIds)
	}
	if rf, ok := ret.Get(0).(func([]int) []*pipelineConfig.Pipeline); ok {
		r0 = rf(pipelineIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(pipelineIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByIdsIn provides a mock function with given fields: ids
func (_m *CdPipelineConfigService) FindByIdsIn(ids []int) ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called(ids)

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*pipelineConfig.Pipeline, error)); ok {
		return rf(ids)
	}
	if rf, ok := ret.Get(0).(func([]int) []*pipelineConfig.Pipeline); ok {
		r0 = rf(ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindCdPipelinesByAppAndEnv provides a mock function with given fields: appId, envId, envName
func (_m *CdPipelineConfigService) FindCdPipelinesByAppAndEnv(appId int, envId int, envName string) (*bean.CDPipelineMinConfig, error) {
	ret := _m.Called(appId, envId, envName)

	var r0 *bean.CDPipelineMinConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int, string) (*bean.CDPipelineMinConfig, error)); ok {
		return rf(appId, envId, envName)
	}
	if rf, ok := ret.Get(0).(func(int, int, string) *bean.CDPipelineMinConfig); ok {
		r0 = rf(appId, envId, envName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bean.CDPipelineMinConfig)
		}
	}

	if rf, ok := ret.Get(1).(func(int, int, string) error); ok {
		r1 = rf(appId, envId, envName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindCdPipelinesByIds provides a mock function with given fields: cdPipelineIds
func (_m *CdPipelineConfigService) FindCdPipelinesByIds(cdPipelineIds []int) ([]*bean.CDPipelineMinConfig, error) {
	ret := _m.Called(cdPipelineIds)

	var r0 []*bean.CDPipelineMinConfig
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*bean.CDPipelineMinConfig, error)); ok {
		return rf(cdPipelineIds)
	}
	if rf, ok := ret.Get(0).(func([]int) []*bean.CDPipelineMinConfig); ok {
		r0 = rf(cdPipelineIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*bean.CDPipelineMinConfig)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(cdPipelineIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindPipelineById provides a mock function with given fields: cdPipelineId
func (_m *CdPipelineConfigService) FindPipelineById(cdPipelineId int) (*pipelineConfig.Pipeline, error) {
	ret := _m.Called(cdPipelineId)

	var r0 *pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*pipelineConfig.Pipeline, error)); ok {
		return rf(cdPipelineId)
	}
	if rf, ok := ret.Get(0).(func(int) *pipelineConfig.Pipeline); ok {
		r0 = rf(cdPipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(cdPipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindPipelineByIds provides a mock function with given fields: cdPipelineIds
func (_m *CdPipelineConfigService) FindPipelineByIds(cdPipelineIds []int) ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called(cdPipelineIds)

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*pipelineConfig.Pipeline, error)); ok {
		return rf(cdPipelineIds)
	}
	if rf, ok := ret.Get(0).(func([]int) []*pipelineConfig.Pipeline); ok {
		r0 = rf(cdPipelineIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(cdPipelineIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllCdPipelinesAndEnvDataLite provides a mock function with given fields: appId
func (_m *CdPipelineConfigService) GetAllCdPipelinesAndEnvDataLite(appId int) ([]*bean.CdPipelineEnvDataResponseDto, error) {
	ret := _m.Called(appId)

	var r0 []*bean.CdPipelineEnvDataResponseDto
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]*bean.CdPipelineEnvDataResponseDto, error)); ok {
		return rf(appId)
	}
	if rf, ok := ret.Get(0).(func(int) []*bean.CdPipelineEnvDataResponseDto); ok {
		r0 = rf(appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*bean.CdPipelineEnvDataResponseDto)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetBulkActionImpactedPipelines provides a mock function with given fields: dto
func (_m *CdPipelineConfigService) GetBulkActionImpactedPipelines(dto *bean.CdBulkActionRequestDto) ([]*pipelineConfig.Pipeline, error) {
	ret := _m.Called(dto)

	var r0 []*pipelineConfig.Pipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(*bean.CdBulkActionRequestDto) ([]*pipelineConfig.Pipeline, error)); ok {
		return rf(dto)
	}
	if rf, ok := ret.Get(0).(func(*bean.CdBulkActionRequestDto) []*pipelineConfig.Pipeline); ok {
		r0 = rf(dto)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.Pipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(*bean.CdBulkActionRequestDto) error); ok {
		r1 = rf(dto)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCdComponentDetails provides a mock function with given fields: appId
func (_m *CdPipelineConfigService) GetCdComponentDetails(appId int) (map[int]*bean.CdComponentDetails, error) {
	ret := _m.Called(appId)

	var r0 map[int]*bean.CdComponentDetails
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (map[int]*bean.CdComponentDetails, error)); ok {
		return rf(appId)
	}
	if rf, ok := ret.Get(0).(func(int) map[int]*bean.CdComponentDetails); ok {
		r0 = rf(appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[int]*bean.CdComponentDetails)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCdPipelineById provides a mock function with given fields: pipelineId
func (_m *CdPipelineConfigService) GetCdPipelineById(pipelineId int) (*bean.CDPipelineConfigObject, error) {
	ret := _m.Called(pipelineId)

	var r0 *bean.CDPipelineConfigObject
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*bean.CDPipelineConfigObject, error)); ok {
		return rf(pipelineId)
	}
	if rf, ok := ret.Get(0).(func(int) *bean.CDPipelineConfigObject); ok {
		r0 = rf(pipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bean.CDPipelineConfigObject)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(pipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCdPipelinesByAppAndEnv provides a mock function with given fields: appId, envId, envName
func (_m *CdPipelineConfigService) GetCdPipelinesByAppAndEnv(appId int, envId int, envName string) (*bean.CdPipelines, error) {
	ret := _m.Called(appId, envId, envName)

	var r0 *bean.CdPipelines
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int, string) (*bean.CdPipelines, error)); ok {
		return rf(appId, envId, envName)
	}
	if rf, ok := ret.Get(0).(func(int, int, string) *bean.CdPipelines); ok {
		r0 = rf(appId, envId, envName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bean.CdPipelines)
		}
	}

	if rf, ok := ret.Get(1).(func(int, int, string) error); ok {
		r1 = rf(appId, envId, envName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCdPipelinesByEnvironment provides a mock function with given fields: request, token
func (_m *CdPipelineConfigService) GetCdPipelinesByEnvironment(request resourceGroup.ResourceGroupingRequest, token string) (*bean.CdPipelines, error) {
	ret := _m.Called(request, token)

	var r0 *bean.CdPipelines
	var r1 error
	if rf, ok := ret.Get(0).(func(resourceGroup.ResourceGroupingRequest, string) (*bean.CdPipelines, error)); ok {
		return rf(request, token)
	}
	if rf, ok := ret.Get(0).(func(resourceGroup.ResourceGroupingRequest, string) *bean.CdPipelines); ok {
		r0 = rf(request, token)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bean.CdPipelines)
		}
	}

	if rf, ok := ret.Get(1).(func(resourceGroup.ResourceGroupingRequest, string) error); ok {
		r1 = rf(request, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCdPipelinesByEnvironmentMin provides a mock function with given fields: request, token
func (_m *CdPipelineConfigService) GetCdPipelinesByEnvironmentMin(request resourceGroup.ResourceGroupingRequest, token string) ([]*bean.CDPipelineConfigObject, error) {
	ret := _m.Called(request, token)

	var r0 []*bean.CDPipelineConfigObject
	var r1 error
	if rf, ok := ret.Get(0).(func(resourceGroup.ResourceGroupingRequest, string) ([]*bean.CDPipelineConfigObject, error)); ok {
		return rf(request, token)
	}
	if rf, ok := ret.Get(0).(func(resourceGroup.ResourceGroupingRequest, string) []*bean.CDPipelineConfigObject); ok {
		r0 = rf(request, token)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*bean.CDPipelineConfigObject)
		}
	}

	if rf, ok := ret.Get(1).(func(resourceGroup.ResourceGroupingRequest, string) error); ok {
		r1 = rf(request, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCdPipelinesForApp provides a mock function with given fields: appId
func (_m *CdPipelineConfigService) GetCdPipelinesForApp(appId int) (*bean.CdPipelines, error) {
	ret := _m.Called(appId)

	var r0 *bean.CdPipelines
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*bean.CdPipelines, error)); ok {
		return rf(appId)
	}
	if rf, ok := ret.Get(0).(func(int) *bean.CdPipelines); ok {
		r0 = rf(appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bean.CdPipelines)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetEnvironmentByCdPipelineId provides a mock function with given fields: pipelineId
func (_m *CdPipelineConfigService) GetEnvironmentByCdPipelineId(pipelineId int) (int, error) {
	ret := _m.Called(pipelineId)

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (int, error)); ok {
		return rf(pipelineId)
	}
	if rf, ok := ret.Get(0).(func(int) int); ok {
		r0 = rf(pipelineId)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(pipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetEnvironmentListForAutocompleteFilter provides a mock function with given fields: envName, clusterIds, offset, size, token, checkAuthBatch, ctx
func (_m *CdPipelineConfigService) GetEnvironmentListForAutocompleteFilter(envName string, clusterIds []int, offset int, size int, token string, checkAuthBatch func(string, []string, []string) (map[string]bool, map[string]bool), ctx context.Context) (*repositorybean.ResourceGroupingResponse, error) {
	ret := _m.Called(envName, clusterIds, offset, size, token, checkAuthBatch, ctx)

	var r0 *repositorybean.ResourceGroupingResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(string, []int, int, int, string, func(string, []string, []string) (map[string]bool, map[string]bool), context.Context) (*repositorybean.ResourceGroupingResponse, error)); ok {
		return rf(envName, clusterIds, offset, size, token, checkAuthBatch, ctx)
	}
	if rf, ok := ret.Get(0).(func(string, []int, int, int, string, func(string, []string, []string) (map[string]bool, map[string]bool), context.Context) *repositorybean.ResourceGroupingResponse); ok {
		r0 = rf(envName, clusterIds, offset, size, token, checkAuthBatch, ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repositorybean.ResourceGroupingResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(string, []int, int, int, string, func(string, []string, []string) (map[string]bool, map[string]bool), context.Context) error); ok {
		r1 = rf(envName, clusterIds, offset, size, token, checkAuthBatch, ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetPipelineEnvironmentsForApplication provides a mock function with given fields: appId
func (_m *CdPipelineConfigService) GetPipelineEnvironmentsForApplication(appId int) ([]int, error) {
	ret := _m.Called(appId)

	var r0 []int
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]int, error)); ok {
		return rf(appId)
	}
	if rf, ok := ret.Get(0).(func(int) []int); ok {
		r0 = rf(appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetTriggerViewCdPipelinesForApp provides a mock function with given fields: appId
func (_m *CdPipelineConfigService) GetTriggerViewCdPipelinesForApp(appId int) (*bean.CdPipelines, error) {
	ret := _m.Called(appId)

	var r0 *bean.CdPipelines
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*bean.CdPipelines, error)); ok {
		return rf(appId)
	}
	if rf, ok := ret.Get(0).(func(int) *bean.CdPipelines); ok {
		r0 = rf(appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bean.CdPipelines)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// IsGitOpsRequiredForCD provides a mock function with given fields: pipelineCreateRequest
func (_m *CdPipelineConfigService) IsGitOpsRequiredForCD(pipelineCreateRequest *bean.CdPipelines) bool {
	ret := _m.Called(pipelineCreateRequest)

	var r0 bool
	if rf, ok := ret.Get(0).(func(*bean.CdPipelines) bool); ok {
		r0 = rf(pipelineCreateRequest)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// MarkGitOpsDevtronAppsDeletedWhereArgoAppIsDeleted provides a mock function with given fields: appId, envId, acdToken, _a3
func (_m *CdPipelineConfigService) MarkGitOpsDevtronAppsDeletedWhereArgoAppIsDeleted(appId int, envId int, acdToken string, _a3 *pipelineConfig.Pipeline) (bool, error) {
	ret := _m.Called(appId, envId, acdToken, _a3)

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int, string, *pipelineConfig.Pipeline) (bool, error)); ok {
		return rf(appId, envId, acdToken, _a3)
	}
	if rf, ok := ret.Get(0).(func(int, int, string, *pipelineConfig.Pipeline) bool); ok {
		r0 = rf(appId, envId, acdToken, _a3)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(int, int, string, *pipelineConfig.Pipeline) error); ok {
		r1 = rf(appId, envId, acdToken, _a3)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// PatchCdPipelines provides a mock function with given fields: cdPipelines, ctx
func (_m *CdPipelineConfigService) PatchCdPipelines(cdPipelines *bean.CDPatchRequest, ctx context.Context) (*bean.CdPipelines, error) {
	ret := _m.Called(cdPipelines, ctx)

	var r0 *bean.CdPipelines
	var r1 error
	if rf, ok := ret.Get(0).(func(*bean.CDPatchRequest, context.Context) (*bean.CdPipelines, error)); ok {
		return rf(cdPipelines, ctx)
	}
	if rf, ok := ret.Get(0).(func(*bean.CDPatchRequest, context.Context) *bean.CdPipelines); ok {
		r0 = rf(cdPipelines, ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bean.CdPipelines)
		}
	}

	if rf, ok := ret.Get(1).(func(*bean.CDPatchRequest, context.Context) error); ok {
		r1 = rf(cdPipelines, ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// PerformBulkActionOnCdPipelines provides a mock function with given fields: dto, impactedPipelines, ctx, dryRun, userId
func (_m *CdPipelineConfigService) PerformBulkActionOnCdPipelines(dto *bean.CdBulkActionRequestDto, impactedPipelines []*pipelineConfig.Pipeline, ctx context.Context, dryRun bool, userId int32) ([]*bean.CdBulkActionResponseDto, error) {
	ret := _m.Called(dto, impactedPipelines, ctx, dryRun, userId)

	var r0 []*bean.CdBulkActionResponseDto
	var r1 error
	if rf, ok := ret.Get(0).(func(*bean.CdBulkActionRequestDto, []*pipelineConfig.Pipeline, context.Context, bool, int32) ([]*bean.CdBulkActionResponseDto, error)); ok {
		return rf(dto, impactedPipelines, ctx, dryRun, userId)
	}
	if rf, ok := ret.Get(0).(func(*bean.CdBulkActionRequestDto, []*pipelineConfig.Pipeline, context.Context, bool, int32) []*bean.CdBulkActionResponseDto); ok {
		r0 = rf(dto, impactedPipelines, ctx, dryRun, userId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*bean.CdBulkActionResponseDto)
		}
	}

	if rf, ok := ret.Get(1).(func(*bean.CdBulkActionRequestDto, []*pipelineConfig.Pipeline, context.Context, bool, int32) error); ok {
		r1 = rf(dto, impactedPipelines, ctx, dryRun, userId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RegisterInACD provides a mock function with given fields: ctx, chartGitAttr, userId
func (_m *CdPipelineConfigService) RegisterInACD(ctx context.Context, chartGitAttr *commonbean.ChartGitAttribute, userId int32) error {
	ret := _m.Called(ctx, chartGitAttr, userId)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *commonbean.ChartGitAttribute, int32) error); ok {
		r0 = rf(ctx, chartGitAttr, userId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RetrieveParentDetails provides a mock function with given fields: pipelineId
func (_m *CdPipelineConfigService) RetrieveParentDetails(pipelineId int) (int, apibean.WorkflowType, error) {
	ret := _m.Called(pipelineId)

	var r0 int
	var r1 apibean.WorkflowType
	var r2 error
	if rf, ok := ret.Get(0).(func(int) (int, apibean.WorkflowType, error)); ok {
		return rf(pipelineId)
	}
	if rf, ok := ret.Get(0).(func(int) int); ok {
		r0 = rf(pipelineId)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(int) apibean.WorkflowType); ok {
		r1 = rf(pipelineId)
	} else {
		r1 = ret.Get(1).(apibean.WorkflowType)
	}

	if rf, ok := ret.Get(2).(func(int) error); ok {
		r2 = rf(pipelineId)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// NewCdPipelineConfigService creates a new instance of CdPipelineConfigService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewCdPipelineConfigService(t interface {
	mock.TestingT
	Cleanup(func())
}) *CdPipelineConfigService {
	mock := &CdPipelineConfigService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
