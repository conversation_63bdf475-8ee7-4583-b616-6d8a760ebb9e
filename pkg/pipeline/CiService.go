/*
 * Copyright (c) 2020-2024. Devtron Inc.
 */

package pipeline

import (
	"github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	"github.com/caarlos0/env"
	bean9 "github.com/devtron-labs/devtron/api/bean"
	client "github.com/devtron-labs/devtron/client/events"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig/bean/workflow/cdWorkflow"
	buildBean "github.com/devtron-labs/devtron/pkg/build/pipeline/bean"
	"github.com/devtron-labs/devtron/pkg/featureFlag/model"
	"github.com/devtron-labs/devtron/pkg/pipeline/types"
	"github.com/devtron-labs/devtron/pkg/pipeline/workflowStatus"
	"github.com/devtron-labs/devtron/pkg/sql"
	"github.com/devtron-labs/devtron/pkg/workflow/workflowStatusLatest"
	util3 "github.com/devtron-labs/devtron/util"
	util2 "github.com/devtron-labs/devtron/util/event"
	"go.uber.org/zap"
)

const (
	MandatoryPluginCiTriggerBlockError = "ci trigger request blocked, mandatory plugins not configured"
)

type CiService interface {
	WriteCITriggerEvent(trigger *types.CiTriggerRequest, workflowRequest *types.WorkflowRequest)
	WriteCIFailEvent(ciWorkflow *pipelineConfig.CiWorkflow)
	SaveCiWorkflowWithStage(wf *pipelineConfig.CiWorkflow) error
	UpdateCiWorkflowWithStage(wf *pipelineConfig.CiWorkflow) error
}
type BuildxCacheFlags struct {
	BuildxCacheModeMin     bool `env:"BUILDX_CACHE_MODE_MIN" envDefault:"false" description:"To set build cache mode to minimum in buildx"`
	AsyncBuildxCacheExport bool `env:"ASYNC_BUILDX_CACHE_EXPORT" envDefault:"false" description:"To enable async container image cache export"`
}

type CiServiceImpl struct {
	Logger                      *zap.SugaredLogger
	workflowStageStatusService  workflowStatus.WorkFlowStageStatusService
	eventClient                 client.EventClient
	eventFactory                client.EventFactory
	config                      *types.CiConfig
	ciWorkflowRepository        pipelineConfig.CiWorkflowRepository
	ciPipelineRepository        pipelineConfig.CiPipelineRepository
	transactionManager          sql.TransactionWrapper
	workflowStatusLatestService workflowStatusLatest.WorkflowStatusLatestService
}

func NewCiServiceImpl(Logger *zap.SugaredLogger,
	workflowStageStatusService workflowStatus.WorkFlowStageStatusService, eventClient client.EventClient,
	eventFactory client.EventFactory,
	ciWorkflowRepository pipelineConfig.CiWorkflowRepository,
	ciPipelineRepository pipelineConfig.CiPipelineRepository,
	transactionManager sql.TransactionWrapper,
	workflowStatusLatestService workflowStatusLatest.WorkflowStatusLatestService,
) (*CiServiceImpl, error) {
	buildxCacheFlags := &BuildxCacheFlags{}
	err := env.Parse(buildxCacheFlags)
	if err != nil {
		Logger.Infow("error occurred while parsing BuildxCacheFlags env,so setting BuildxCacheModeMin and AsyncBuildxCacheExport to default value", "err", err)
	}
	model.SetDefaultBuildxCacheValues(buildxCacheFlags.BuildxCacheModeMin, buildxCacheFlags.AsyncBuildxCacheExport)
	// func to set default variables for buildx cache flags
	cis := &CiServiceImpl{
		Logger:                      Logger,
		workflowStageStatusService:  workflowStageStatusService,
		eventClient:                 eventClient,
		eventFactory:                eventFactory,
		ciWorkflowRepository:        ciWorkflowRepository,
		ciPipelineRepository:        ciPipelineRepository,
		transactionManager:          transactionManager,
		workflowStatusLatestService: workflowStatusLatestService,
	}
	config, err := types.GetCiConfig()
	if err != nil {
		return nil, err
	}
	cis.config = config
	return cis, nil
}

func (impl *CiServiceImpl) WriteCITriggerEvent(trigger *types.CiTriggerRequest, workflowRequest *types.WorkflowRequest) {
	impl.Logger.Debugw("triggering notification for ci trigger event", "pipelineId", workflowRequest.PipelineId, "ciWorkflowId", workflowRequest.WorkflowId)
	event, _ := impl.eventFactory.Build(util2.Trigger, &workflowRequest.PipelineId, workflowRequest.AppId, nil, util2.CI)
	material := &buildBean.MaterialTriggerInfo{}

	material.GitTriggers = trigger.CommitHashes

	event.UserId = int(trigger.TriggeredBy)
	event.CiWorkflowRunnerId = workflowRequest.WorkflowId
	event = impl.eventFactory.BuildExtraCIData(event, material)

	_, evtErr := impl.eventClient.WriteNotificationEvent(event)
	if evtErr != nil {
		impl.Logger.Errorw("error in writing event", "err", evtErr)
	}
}

func (impl *CiServiceImpl) WriteCIFailEvent(ciWorkflow *pipelineConfig.CiWorkflow) {
	event, _ := impl.eventFactory.Build(util2.Fail, &ciWorkflow.CiPipelineId, ciWorkflow.CiPipeline.AppId, nil, util2.CI)
	material := &buildBean.MaterialTriggerInfo{}
	material.GitTriggers = ciWorkflow.GitTriggers
	event.CiWorkflowRunnerId = ciWorkflow.Id
	event.UserId = int(ciWorkflow.TriggeredBy)
	event = impl.eventFactory.BuildExtraCIData(event, material)
	event.CiArtifactId = 0
	_, evtErr := impl.eventClient.WriteNotificationEvent(event)
	if evtErr != nil {
		impl.Logger.Errorw("error in writing event", "err", evtErr)
	}
}

func (impl *CiServiceImpl) SaveCiWorkflowWithStage(wf *pipelineConfig.CiWorkflow) error {
	// implementation
	tx, err := impl.transactionManager.StartTx()
	if err != nil {
		impl.Logger.Errorw("error in starting transaction to save default configurations", "workflowName", wf.Name, "error", err)
		return err
	}

	defer func() {
		dbErr := impl.transactionManager.RollbackTx(tx)
		if dbErr != nil && dbErr.Error() != util3.SqlAlreadyCommitedErrMsg {
			impl.Logger.Errorw("error in rolling back transaction", "workflowName", wf.Name, "error", dbErr)
		}
	}()

	if impl.config.EnableWorkflowExecutionStage {
		wf.Status = cdWorkflow.WorkflowWaitingToStart
		wf.PodStatus = string(v1alpha1.NodePending)
	}

	err = impl.ciWorkflowRepository.SaveWorkFlowWithTx(wf, tx)
	if err != nil {
		impl.Logger.Errorw("error in saving workflow", "payload", wf, "error", err)
		return err
	}

	err = impl.workflowStageStatusService.SaveWorkflowStages(wf.Id, bean9.CI_WORKFLOW_TYPE.String(), wf.Name, tx)
	if err != nil {
		impl.Logger.Errorw("error in saving workflow stages", "workflowName", wf.Name, "error", err)
		return err
	}

	// Get appId from CI pipeline (not from workflow to avoid transaction issues)
	ciPipeline, err := impl.ciPipelineRepository.FindById(wf.CiPipelineId)
	if err != nil {
		impl.Logger.Errorw("error in fetching ci pipeline for appId", "err", err, "ciPipelineId", wf.CiPipelineId)
		return err
	}
	appId := ciPipeline.AppId

	err = impl.workflowStatusLatestService.SaveCiWorkflowStatusLatest(tx, wf.CiPipelineId, appId, wf.Id, wf.TriggeredBy)
	if err != nil {
		impl.Logger.Errorw("error in saving ci workflow status latest", "err", err, "pipelineId", wf.CiPipelineId, "workflowId", wf.Id)
		return err
	}

	err = impl.transactionManager.CommitTx(tx)
	if err != nil {
		impl.Logger.Errorw("error in committing transaction", "workflowName", wf.Name, "error", err)
		return err
	}

	return nil

}

func (impl *CiServiceImpl) UpdateCiWorkflowWithStage(wf *pipelineConfig.CiWorkflow) error {
	// implementation
	tx, err := impl.transactionManager.StartTx()
	if err != nil {
		impl.Logger.Errorw("error in starting transaction to save default configurations", "workflowName", wf.Name, "error", err)
		return err
	}

	defer func() {
		dbErr := impl.transactionManager.RollbackTx(tx)
		if dbErr != nil && dbErr.Error() != util3.SqlAlreadyCommitedErrMsg {
			impl.Logger.Errorw("error in rolling back transaction", "workflowName", wf.Name, "error", dbErr)
		}
	}()

	wf.Status, wf.PodStatus, err = impl.workflowStageStatusService.UpdateWorkflowStages(wf.Id, bean9.CI_WORKFLOW_TYPE.String(), wf.Name, wf.Status, wf.PodStatus, wf.Message, wf.PodName, tx)
	if err != nil {
		impl.Logger.Errorw("error in updating workflow stages", "workflowName", wf.Name, "error", err)
		return err
	}

	err = impl.ciWorkflowRepository.UpdateWorkFlowWithTx(wf, tx)
	if err != nil {
		impl.Logger.Errorw("error in saving workflow", "payload", wf, "error", err)
		return err
	}

	err = impl.transactionManager.CommitTx(tx)
	if err != nil {
		impl.Logger.Errorw("error in committing transaction", "workflowName", wf.Name, "error", err)
		return err
	}

	return nil

}
