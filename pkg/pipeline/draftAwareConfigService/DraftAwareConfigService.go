package draftAwareConfigService

import (
	"context"
	"github.com/devtron-labs/devtron/enterprise/pkg/drafts"
	"github.com/devtron-labs/devtron/internal/util"
	userBean "github.com/devtron-labs/devtron/pkg/auth/user/bean"
	"github.com/devtron-labs/devtron/pkg/bean/configMapBean"
	chartService "github.com/devtron-labs/devtron/pkg/chart"
	bean3 "github.com/devtron-labs/devtron/pkg/chart/bean"
	"github.com/devtron-labs/devtron/pkg/globalPolicy"
	"github.com/devtron-labs/devtron/pkg/pipeline"
	"github.com/devtron-labs/devtron/pkg/pipeline/bean"
	"github.com/devtron-labs/devtron/pkg/pipeline/constants"
	bean2 "github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/bean"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/common/alpha1/adaptor"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/model"
	"go.uber.org/zap"
	"net/http"
)

type DraftAwareConfigMapService interface {
	// below methods operate on cm creation and updation

	CMGlobalAddUpdate(ctx context.Context, configMapRequest *configMapBean.ConfigDataRequest, userMetadata *userBean.UserMetadata) (*configMapBean.ConfigDataRequest, error)
	CMEnvironmentAddUpdate(ctx context.Context, configMapRequest *configMapBean.ConfigDataRequest, userMetadata *userBean.UserMetadata) (*configMapBean.ConfigDataRequest, error)
	// below methods operate on cm deletion

	CMGlobalDelete(ctx context.Context, name string, deleteReq *configMapBean.ConfigDataRequest, userMetadata *userBean.UserMetadata) (bool, error)
	CMEnvironmentDelete(ctx context.Context, name string, deleteReq *configMapBean.ConfigDataRequest, userMetadata *userBean.UserMetadata) (bool, error)
}

type DraftAwareSecretService interface {
	// below methods operate on cm creation and updation

	CSGlobalAddUpdate(ctx context.Context, configMapRequest *configMapBean.ConfigDataRequest, userMetadata *userBean.UserMetadata) (*configMapBean.ConfigDataRequest, error)
	CSEnvironmentAddUpdate(ctx context.Context, configMapRequest *configMapBean.ConfigDataRequest, userMetadata *userBean.UserMetadata) (*configMapBean.ConfigDataRequest, error)
	// below methods operate on cm deletion

	CSGlobalDelete(ctx context.Context, name string, deleteReq *configMapBean.ConfigDataRequest, userMetadata *userBean.UserMetadata) (bool, error)
	CSEnvironmentDelete(ctx context.Context, name string, deleteReq *configMapBean.ConfigDataRequest, userMetadata *userBean.UserMetadata) (bool, error)
}

type DraftAwareDeploymentTemplateService interface {
	// below methods operate on deployment template

	// Create here is used for publishing base deployment template while saving dt for the first time.
	Create(ctx context.Context, templateRequest bean3.TemplateRequest, userMetadata *userBean.UserMetadata) (*bean3.TemplateResponse, error)
	// UpdateAppOverride here is used for updating base deployment template.
	UpdateAppOverride(ctx context.Context, templateRequest *bean3.TemplateRequest, token string, userMetadata *userBean.UserMetadata) (*bean3.TemplateResponse, error)
	// UpdateEnvironmentProperties here is used for updating and saving deployment template at env override level
	UpdateEnvironmentProperties(ctx context.Context, propertiesRequest *bean.EnvironmentProperties, token string, userMetadata *userBean.UserMetadata) (*bean.EnvironmentUpdateResponse, error)
	// ResetEnvironmentProperties method handles flow when a user deletes the deployment template env override.
	ResetEnvironmentProperties(ctx context.Context, propertiesRequest *bean.EnvironmentProperties, userMetadata *userBean.UserMetadata) (bool, error)
	// CreateEnvironmentPropertiesAndBaseIfNeeded is utilized when the deployment template chart version is updated and saved
	CreateEnvironmentPropertiesAndBaseIfNeeded(ctx context.Context, environmentProperties *bean.EnvironmentProperties, userMetadata *userBean.UserMetadata) (*bean.EnvironmentUpdateResponse, error)
}

type DraftAwareConfigService interface {
	DraftAwareConfigMapService
	DraftAwareSecretService
	DraftAwareDeploymentTemplateService
}
type DraftAwareConfigServiceImpl struct {
	logger                  *zap.SugaredLogger
	configMapService        pipeline.ConfigMapService
	chartService            chartService.ChartService
	propertiesConfigService pipeline.PropertiesConfigService
	configDraftService      drafts.ConfigDraftService
	globalPolicyDataManager globalPolicy.GlobalPolicyDataManager
}

func NewDraftAwareResourceServiceImpl(logger *zap.SugaredLogger,
	configMapService pipeline.ConfigMapService,
	chartService chartService.ChartService,
	propertiesConfigService pipeline.PropertiesConfigService,
	configDraftService drafts.ConfigDraftService,
	globalPolicyDataManager globalPolicy.GlobalPolicyDataManager,
) *DraftAwareConfigServiceImpl {
	return &DraftAwareConfigServiceImpl{
		logger:                  logger,
		configMapService:        configMapService,
		chartService:            chartService,
		propertiesConfigService: propertiesConfigService,
		configDraftService:      configDraftService,
		globalPolicyDataManager: globalPolicyDataManager,
	}
}

func (impl *DraftAwareConfigServiceImpl) CMGlobalAddUpdate(ctx context.Context, configMapRequest *configMapBean.ConfigDataRequest, userMetadata *userBean.UserMetadata) (*configMapBean.ConfigDataRequest, error) {
	var err error
	var resp *configMapBean.ConfigDataRequest
	if !configMapRequest.IsExpressEdit {
		// if isExpressEdit flag is false then approval config is not applied and user is following normal lifecycle of saving config
		resp, err = impl.configMapService.CMGlobalAddUpdate(configMapRequest)
		if err != nil {
			impl.logger.Errorw("error in CMGlobalAddUpdate", "configMapRequest", configMapRequest, "err", err)
			return nil, err
		}
	} else {
		isUserException, err := impl.globalPolicyDataManager.IsUserAnExceptionByRqmResourceType(ctx, adaptor.ApplyPolicyTypeToRqmResourceType(model.APPLY_POLICY_APPROVAL_CONFIGURATION).ToInt(), userMetadata)
		if err != nil {
			impl.logger.Errorw("error in checking if a user is exception user or not by policy type ", "policyType", model.APPLY_POLICY_APPROVAL_CONFIGURATION, "err", err)
			return nil, err
		}
		if isUserException {
			resp, err = impl.configMapService.CMGlobalAddUpdate(configMapRequest)
			if err != nil {
				impl.logger.Errorw("error in CMGlobalAddUpdate", "configMapRequest", configMapRequest, "err", err)
				return nil, err
			}
			var resourceName string
			if len(configMapRequest.ConfigData) > 0 && configMapRequest.ConfigData[0] != nil {
				resourceName = configMapRequest.ConfigData[0].Name
			}
			configProperty := &configMapBean.ConfigNameAndType{
				Name: resourceName,
				Type: configMapBean.CM,
			}
			err := impl.performExpressEditActionsOnCmCsForExceptionUser(ctx, configProperty, configMapRequest)
			if err != nil {
				impl.logger.Errorw("error in performing express edit operations for cm", "configMapRequest", configMapRequest, "error", err)
				return nil, err
			}
		} else {
			return nil, util.NewApiError(http.StatusConflict, constants.NotAnExceptionUserErr, constants.NotAnExceptionUserErr)
		}
	}

	return resp, nil
}

func (impl *DraftAwareConfigServiceImpl) CMEnvironmentAddUpdate(ctx context.Context, configMapRequest *configMapBean.ConfigDataRequest, userMetadata *userBean.UserMetadata) (*configMapBean.ConfigDataRequest, error) {
	var err error
	var resp *configMapBean.ConfigDataRequest
	if !configMapRequest.IsExpressEdit {
		// if isExpressEdit flag is false then approval config is not applied and user is following normal lifecycle of saving config
		resp, err = impl.configMapService.CMEnvironmentAddUpdate(configMapRequest)
		if err != nil {
			impl.logger.Errorw("error in CMEnvironmentAddUpdate", "configMapRequest", configMapRequest, "err", err)
			return nil, err
		}
	} else {
		isUserException, err := impl.globalPolicyDataManager.IsUserAnExceptionByRqmResourceType(ctx, adaptor.ApplyPolicyTypeToRqmResourceType(model.APPLY_POLICY_APPROVAL_CONFIGURATION).ToInt(), userMetadata)
		if err != nil {
			impl.logger.Errorw("error in checking if a user is exception user or not by policy type ", "policyType", model.APPLY_POLICY_APPROVAL_CONFIGURATION, "err", err)
			return nil, err
		}
		if isUserException {
			resp, err = impl.configMapService.CMEnvironmentAddUpdate(configMapRequest)
			if err != nil {
				impl.logger.Errorw("error in CMEnvironmentAddUpdate", "configMapRequest", configMapRequest, "err", err)
				return nil, err
			}
			var resourceName string
			if len(configMapRequest.ConfigData) > 0 && configMapRequest.ConfigData[0] != nil {
				resourceName = configMapRequest.ConfigData[0].Name
			}
			configProperty := &configMapBean.ConfigNameAndType{
				Name: resourceName,
				Type: configMapBean.CM,
			}
			err := impl.performExpressEditActionsOnCmCsForExceptionUser(ctx, configProperty, configMapRequest)
			if err != nil {
				impl.logger.Errorw("error in performing express edit operations for cm", "configMapRequest", configMapRequest, "error", err)
				return nil, err
			}
		} else {
			return nil, util.NewApiError(http.StatusConflict, constants.NotAnExceptionUserErr, constants.NotAnExceptionUserErr)
		}
	}

	return resp, nil
}

func (impl *DraftAwareConfigServiceImpl) CSGlobalAddUpdate(ctx context.Context, configMapRequest *configMapBean.ConfigDataRequest, userMetadata *userBean.UserMetadata) (*configMapBean.ConfigDataRequest, error) {
	var err error
	var resp *configMapBean.ConfigDataRequest
	if !configMapRequest.IsExpressEdit {
		// if isExpressEdit flag is false then approval config is not applied and user is following normal lifecycle of saving config
		resp, err = impl.configMapService.CSGlobalAddUpdate(configMapRequest)
		if err != nil {
			impl.logger.Errorw("error in CSGlobalAddUpdate", "err", err)
			return nil, err
		}
	} else {
		isUserException, err := impl.globalPolicyDataManager.IsUserAnExceptionByRqmResourceType(ctx, adaptor.ApplyPolicyTypeToRqmResourceType(model.APPLY_POLICY_APPROVAL_CONFIGURATION).ToInt(), userMetadata)
		if err != nil {
			impl.logger.Errorw("error in checking if a user is exception user or not by policy type ", "policyType", model.APPLY_POLICY_APPROVAL_CONFIGURATION, "err", err)
			return nil, err
		}
		if isUserException {
			resp, err = impl.configMapService.CSGlobalAddUpdate(configMapRequest)
			if err != nil {
				impl.logger.Errorw("error in CSGlobalAddUpdate", "err", err)
				return nil, err
			}
			var resourceName string
			if len(configMapRequest.ConfigData) > 0 && configMapRequest.ConfigData[0] != nil {
				resourceName = configMapRequest.ConfigData[0].Name
			}
			configProperty := &configMapBean.ConfigNameAndType{
				Name: resourceName,
				Type: configMapBean.CS,
			}
			err := impl.performExpressEditActionsOnCmCsForExceptionUser(ctx, configProperty, configMapRequest)
			if err != nil {
				impl.logger.Errorw("error in performing express edit operations for cm", "appId", configMapRequest.AppId, "envId", configMapRequest.EnvironmentId, "error", err)
				return nil, err
			}
		} else {
			return nil, util.NewApiError(http.StatusConflict, constants.NotAnExceptionUserErr, constants.NotAnExceptionUserErr)
		}
	}

	return resp, nil
}

func (impl *DraftAwareConfigServiceImpl) CSEnvironmentAddUpdate(ctx context.Context, configMapRequest *configMapBean.ConfigDataRequest, userMetadata *userBean.UserMetadata) (*configMapBean.ConfigDataRequest, error) {
	var err error
	var resp *configMapBean.ConfigDataRequest
	if !configMapRequest.IsExpressEdit {
		// if isExpressEdit flag is false then approval config is not applied and user is following normal lifecycle of saving config
		resp, err = impl.configMapService.CSEnvironmentAddUpdate(configMapRequest)
		if err != nil {
			impl.logger.Errorw("error in CSEnvironmentAddUpdate", "err", err)
			return nil, err
		}
	} else {
		isUserException, err := impl.globalPolicyDataManager.IsUserAnExceptionByRqmResourceType(ctx, adaptor.ApplyPolicyTypeToRqmResourceType(model.APPLY_POLICY_APPROVAL_CONFIGURATION).ToInt(), userMetadata)
		if err != nil {
			impl.logger.Errorw("error in checking if a user is exception user or not by policy type ", "policyType", model.APPLY_POLICY_APPROVAL_CONFIGURATION, "err", err)
			return nil, err
		}
		if isUserException {
			resp, err = impl.configMapService.CSEnvironmentAddUpdate(configMapRequest)
			if err != nil {
				impl.logger.Errorw("error in CSEnvironmentAddUpdate", "err", err)
				return nil, err
			}
			var resourceName string
			if len(configMapRequest.ConfigData) > 0 && configMapRequest.ConfigData[0] != nil {
				resourceName = configMapRequest.ConfigData[0].Name
			}
			configProperty := &configMapBean.ConfigNameAndType{
				Name: resourceName,
				Type: configMapBean.CS,
			}
			err := impl.performExpressEditActionsOnCmCsForExceptionUser(ctx, configProperty, configMapRequest)
			if err != nil {
				impl.logger.Errorw("error in performing express edit operations for cm", "appId", configMapRequest.AppId, "envId", configMapRequest.EnvironmentId, "error", err)
				return nil, err
			}
		} else {
			return nil, util.NewApiError(http.StatusConflict, constants.NotAnExceptionUserErr, constants.NotAnExceptionUserErr)
		}
	}
	return resp, nil

}

func (impl *DraftAwareConfigServiceImpl) CMGlobalDelete(ctx context.Context, name string, deleteReq *configMapBean.ConfigDataRequest, userMetadata *userBean.UserMetadata) (bool, error) {
	var err error
	var resp bool
	if !deleteReq.IsExpressEdit {
		// if isExpressEdit flag is false then approval config is not applied and user is following normal lifecycle of saving config
		resp, err = impl.configMapService.CMGlobalDelete(name, deleteReq.Id, deleteReq.UserId)
		if err != nil {
			impl.logger.Errorw("service err, CMGlobalDelete", "appId", deleteReq.AppId, "id", deleteReq.Id, "name", name, "err", err)
			return false, err
		}
	} else {
		isUserException, err := impl.globalPolicyDataManager.IsUserAnExceptionByRqmResourceType(ctx, adaptor.ApplyPolicyTypeToRqmResourceType(model.APPLY_POLICY_APPROVAL_CONFIGURATION).ToInt(), userMetadata)
		if err != nil {
			impl.logger.Errorw("error in checking if a user is exception user or not by policy type ", "policyType", model.APPLY_POLICY_APPROVAL_CONFIGURATION, "err", err)
			return false, err
		}
		if isUserException {
			resp, err = impl.configMapService.CMGlobalDelete(name, deleteReq.Id, deleteReq.UserId)
			if err != nil {
				impl.logger.Errorw("service err, CMGlobalDelete", "appId", deleteReq.AppId, "id", deleteReq.Id, "name", name, "err", err)
				return false, err
			}
			configProperty := &configMapBean.ConfigNameAndType{
				Name: name,
				Type: configMapBean.CM,
			}
			err := impl.performExpressEditActionsOnCmCsForExceptionUser(ctx, configProperty, deleteReq)
			if err != nil {
				impl.logger.Errorw("error in performing express edit operations for cm", "appId", deleteReq.AppId, "resourceName", name, "error", err)
				return false, err
			}
		} else {
			return false, util.NewApiError(http.StatusConflict, constants.NotAnExceptionUserErr, constants.NotAnExceptionUserErr)
		}
	}
	return resp, nil
}

func (impl *DraftAwareConfigServiceImpl) CMEnvironmentDelete(ctx context.Context, name string, deleteReq *configMapBean.ConfigDataRequest, userMetadata *userBean.UserMetadata) (bool, error) {
	var err error
	var resp bool
	if !deleteReq.IsExpressEdit {
		// if isExpressEdit flag is false then approval config is not applied and user is following normal lifecycle of saving config
		resp, err = impl.configMapService.CMEnvironmentDelete(name, deleteReq.Id, deleteReq.UserId)
		if err != nil {
			impl.logger.Errorw("service err, CMEnvironmentDelete", "appId", deleteReq.AppId, "envId", deleteReq.EnvironmentId, "id", deleteReq.Id, "err", err)
			return false, err
		}
	} else {
		isUserException, err := impl.globalPolicyDataManager.IsUserAnExceptionByRqmResourceType(ctx, adaptor.ApplyPolicyTypeToRqmResourceType(model.APPLY_POLICY_APPROVAL_CONFIGURATION).ToInt(), userMetadata)
		if err != nil {
			impl.logger.Errorw("error in checking if a user is exception user or not by policy type ", "policyType", model.APPLY_POLICY_APPROVAL_CONFIGURATION, "err", err)
			return false, err
		}
		if isUserException {
			resp, err = impl.configMapService.CMEnvironmentDelete(name, deleteReq.Id, deleteReq.UserId)
			if err != nil {
				impl.logger.Errorw("service err, CMEnvironmentDelete", "appId", deleteReq.AppId, "envId", deleteReq.EnvironmentId, "id", deleteReq.Id, "err", err)
				return false, err
			}
			configProperty := &configMapBean.ConfigNameAndType{
				Name: name,
				Type: configMapBean.CM,
			}
			err := impl.performExpressEditActionsOnCmCsForExceptionUser(ctx, configProperty, deleteReq)
			if err != nil {
				impl.logger.Errorw("error in performing express edit operations for cm", "appId", deleteReq.AppId, "resourceName", name, "error", err)
				return false, err
			}
		} else {
			return false, util.NewApiError(http.StatusConflict, constants.NotAnExceptionUserErr, constants.NotAnExceptionUserErr)
		}
	}

	return resp, nil
}

func (impl *DraftAwareConfigServiceImpl) CSGlobalDelete(ctx context.Context, name string, deleteReq *configMapBean.ConfigDataRequest, userMetadata *userBean.UserMetadata) (bool, error) {
	var err error
	var resp bool
	if !deleteReq.IsExpressEdit {
		// if isExpressEdit flag is false then approval config is not applied and user is following normal lifecycle of saving config
		resp, err = impl.configMapService.CSGlobalDelete(name, deleteReq.Id, deleteReq.UserId)
		if err != nil {
			impl.logger.Errorw("service err, CSGlobalDelete", "appId", deleteReq.AppId, "id", deleteReq.Id, "name", name, "err", err)
			return false, err
		}
	} else {
		isUserException, err := impl.globalPolicyDataManager.IsUserAnExceptionByRqmResourceType(ctx, adaptor.ApplyPolicyTypeToRqmResourceType(model.APPLY_POLICY_APPROVAL_CONFIGURATION).ToInt(), userMetadata)
		if err != nil {
			impl.logger.Errorw("error in checking if a user is exception user or not by policy type ", "policyType", model.APPLY_POLICY_APPROVAL_CONFIGURATION, "err", err)
			return false, err
		}
		if isUserException {
			resp, err = impl.configMapService.CSGlobalDelete(name, deleteReq.Id, deleteReq.UserId)
			if err != nil {
				impl.logger.Errorw("service err, CSGlobalDelete", "appId", deleteReq.AppId, "id", deleteReq.Id, "name", name, "err", err)
				return false, err
			}
			configProperty := &configMapBean.ConfigNameAndType{
				Name: name,
				Type: configMapBean.CS,
			}
			err := impl.performExpressEditActionsOnCmCsForExceptionUser(ctx, configProperty, deleteReq)
			if err != nil {
				impl.logger.Errorw("error in performing express edit operations for cm", "appId", deleteReq.AppId, "resourceName", name, "error", err)
				return false, err
			}
		} else {
			return false, util.NewApiError(http.StatusConflict, constants.NotAnExceptionUserErr, constants.NotAnExceptionUserErr)
		}
	}
	return resp, nil
}

func (impl *DraftAwareConfigServiceImpl) CSEnvironmentDelete(ctx context.Context, name string, deleteReq *configMapBean.ConfigDataRequest, userMetadata *userBean.UserMetadata) (bool, error) {
	var err error
	var resp bool
	if !deleteReq.IsExpressEdit {
		// if isExpressEdit flag is false then approval config is not applied and user is following normal lifecycle of saving config
		resp, err = impl.configMapService.CSEnvironmentDelete(name, deleteReq.Id, deleteReq.UserId)
		if err != nil {
			impl.logger.Errorw("service err, CSEnvironmentDelete", "appId", deleteReq.AppId, "id", deleteReq.Id, "name", name, "err", err)
			return false, err
		}
	} else {
		isUserException, err := impl.globalPolicyDataManager.IsUserAnExceptionByRqmResourceType(ctx, adaptor.ApplyPolicyTypeToRqmResourceType(model.APPLY_POLICY_APPROVAL_CONFIGURATION).ToInt(), userMetadata)
		if err != nil {
			impl.logger.Errorw("error in checking if a user is exception user or not by policy type ", "policyType", model.APPLY_POLICY_APPROVAL_CONFIGURATION, "err", err)
			return false, err
		}
		if isUserException {
			resp, err = impl.configMapService.CSEnvironmentDelete(name, deleteReq.Id, deleteReq.UserId)
			if err != nil {
				impl.logger.Errorw("service err, CSEnvironmentDelete", "appId", deleteReq.AppId, "id", deleteReq.Id, "name", name, "err", err)
				return false, err
			}
			configProperty := &configMapBean.ConfigNameAndType{
				Name: name,
				Type: configMapBean.CS,
			}
			err := impl.performExpressEditActionsOnCmCsForExceptionUser(ctx, configProperty, deleteReq)
			if err != nil {
				impl.logger.Errorw("error in performing express edit operations for cm", "appId", deleteReq.AppId, "resourceName", name, "error", err)
				return false, err
			}
		} else {
			return false, util.NewApiError(http.StatusConflict, constants.NotAnExceptionUserErr, constants.NotAnExceptionUserErr)
		}
	}
	return resp, nil
}

func (impl *DraftAwareConfigServiceImpl) Create(ctx context.Context, templateRequest bean3.TemplateRequest, userMetadata *userBean.UserMetadata) (*bean3.TemplateResponse, error) {
	var err error
	var resp *bean3.TemplateResponse
	if !templateRequest.IsExpressEdit {
		// if isExpressEdit flag is false then approval config is not applied and user is following normal lifecycle of saving config
		resp, err = impl.chartService.Create(templateRequest, ctx)
		if err != nil {
			impl.logger.Errorw("error in creating base deployment template", "appId", templateRequest.AppId, "err", err)
			return nil, err
		}
	} else {
		isUserException, err := impl.globalPolicyDataManager.IsUserAnExceptionByRqmResourceType(ctx, adaptor.ApplyPolicyTypeToRqmResourceType(model.APPLY_POLICY_APPROVAL_CONFIGURATION).ToInt(), userMetadata)
		if err != nil {
			impl.logger.Errorw("error in checking if a user is exception user or not by policy type ", "policyType", model.APPLY_POLICY_APPROVAL_CONFIGURATION, "err", err)
			return nil, err
		}
		if isUserException {
			resp, err = impl.chartService.Create(templateRequest, ctx)
			if err != nil {
				impl.logger.Errorw("error in creating base deployment template", "appId", templateRequest.AppId, "err", err)
				return nil, err
			}
			err = impl.performExpressEditOperationsForDeploymentTemplate(templateRequest.AppId, bean2.BASE_CONFIG_ENV_ID, templateRequest.ResourceName)
			if err != nil {
				impl.logger.Errorw("error in performing express edit actions if user is exception", "err", err)
				return nil, err
			}
		} else {
			return nil, util.NewApiError(http.StatusConflict, constants.NotAnExceptionUserErr, constants.NotAnExceptionUserErr)
		}
	}
	return resp, nil
}

func (impl *DraftAwareConfigServiceImpl) UpdateAppOverride(ctx context.Context, templateRequest *bean3.TemplateRequest, token string, userMetadata *userBean.UserMetadata) (*bean3.TemplateResponse, error) {
	var err error
	var resp *bean3.TemplateResponse
	if !templateRequest.IsExpressEdit {
		// if isExpressEdit flag is false then approval config is not applied and user is following normal lifecycle of saving config
		resp, err = impl.chartService.UpdateAppOverride(ctx, templateRequest, token)
		if err != nil {
			impl.logger.Errorw("error in updating base deployment template", "chartId", templateRequest.Id, "appId", templateRequest.AppId, "err", err)
			return nil, err
		}
	} else {
		isUserException, err := impl.globalPolicyDataManager.IsUserAnExceptionByRqmResourceType(ctx, adaptor.ApplyPolicyTypeToRqmResourceType(model.APPLY_POLICY_APPROVAL_CONFIGURATION).ToInt(), userMetadata)
		if err != nil {
			impl.logger.Errorw("error in checking if a user is exception user or not by policy type ", "policyType", model.APPLY_POLICY_APPROVAL_CONFIGURATION, "err", err)
			return nil, err
		}
		if isUserException {
			resp, err = impl.chartService.UpdateAppOverride(ctx, templateRequest, token)
			if err != nil {
				impl.logger.Errorw("error in updating base deployment template", "chartId", templateRequest.Id, "appId", templateRequest.AppId, "err", err)
				return nil, err
			}
			err = impl.performExpressEditOperationsForDeploymentTemplate(templateRequest.AppId, bean2.BASE_CONFIG_ENV_ID, templateRequest.ResourceName)
			if err != nil {
				impl.logger.Errorw("error in performing express edit actions if user is exception", "err", err)
				return nil, err
			}
		} else {
			return nil, util.NewApiError(http.StatusConflict, constants.NotAnExceptionUserErr, constants.NotAnExceptionUserErr)
		}
	}
	return resp, nil
}

func (impl *DraftAwareConfigServiceImpl) UpdateEnvironmentProperties(ctx context.Context, propertiesRequest *bean.EnvironmentProperties, token string, userMetadata *userBean.UserMetadata) (*bean.EnvironmentUpdateResponse, error) {
	var err error
	var resp *bean.EnvironmentUpdateResponse
	if !propertiesRequest.IsExpressEdit {
		// if isExpressEdit flag is false then approval config is not applied and user is following normal lifecycle of saving config
		resp, err = impl.propertiesConfigService.UpdateEnvironmentProperties(propertiesRequest.AppId, propertiesRequest.EnvironmentId, propertiesRequest, token, propertiesRequest.UserId)
		if err != nil {
			impl.logger.Errorw("error in creating/updating env level deployment template", "appId", propertiesRequest.AppId, "envId", propertiesRequest.EnvironmentId, "err", err)
			return nil, err
		}
	} else {
		isUserException, err := impl.globalPolicyDataManager.IsUserAnExceptionByRqmResourceType(ctx, adaptor.ApplyPolicyTypeToRqmResourceType(model.APPLY_POLICY_APPROVAL_CONFIGURATION).ToInt(), userMetadata)
		if err != nil {
			impl.logger.Errorw("error in checking if a user is exception user or not by policy type ", "policyType", model.APPLY_POLICY_APPROVAL_CONFIGURATION, "err", err)
			return nil, err
		}
		if isUserException {
			resp, err = impl.propertiesConfigService.UpdateEnvironmentProperties(propertiesRequest.AppId, propertiesRequest.EnvironmentId, propertiesRequest, token, propertiesRequest.UserId)
			if err != nil {
				impl.logger.Errorw("error in creating/updating env level deployment template", "appId", propertiesRequest.AppId, "envId", propertiesRequest.EnvironmentId, "err", err)
				return nil, err
			}
			err = impl.performExpressEditOperationsForDeploymentTemplate(propertiesRequest.AppId, propertiesRequest.EnvironmentId, propertiesRequest.ResourceName)
			if err != nil {
				impl.logger.Errorw("error in performing express edit actions if user is exception", "err", err)
				return nil, err
			}
		} else {
			return nil, util.NewApiError(http.StatusConflict, constants.NotAnExceptionUserErr, constants.NotAnExceptionUserErr)
		}
	}

	return resp, nil
}

func (impl *DraftAwareConfigServiceImpl) ResetEnvironmentProperties(ctx context.Context, propertiesRequest *bean.EnvironmentProperties, userMetadata *userBean.UserMetadata) (bool, error) {
	var err error
	var isSuccess bool
	if !propertiesRequest.IsExpressEdit {
		// if isExpressEdit flag is false then approval config is not applied and user is following normal lifecycle of saving config
		isSuccess, err = impl.propertiesConfigService.ResetEnvironmentProperties(propertiesRequest.Id, propertiesRequest.UserId)
		if err != nil {
			impl.logger.Errorw("service err, ResetEnvironmentProperties", "chartEnvConfigOverrideId", propertiesRequest.Id, "userId", propertiesRequest.UserId, "err", err)
			return false, err
		}
	} else {
		isUserException, err := impl.globalPolicyDataManager.IsUserAnExceptionByRqmResourceType(ctx, adaptor.ApplyPolicyTypeToRqmResourceType(model.APPLY_POLICY_APPROVAL_CONFIGURATION).ToInt(), userMetadata)
		if err != nil {
			impl.logger.Errorw("error in checking if a user is exception user or not by policy type ", "policyType", model.APPLY_POLICY_APPROVAL_CONFIGURATION, "err", err)
			return false, err
		}
		if isUserException {
			isSuccess, err = impl.propertiesConfigService.ResetEnvironmentProperties(propertiesRequest.Id, propertiesRequest.UserId)
			if err != nil {
				impl.logger.Errorw("service err, ResetEnvironmentProperties", "chartEnvConfigOverrideId", propertiesRequest.Id, "userId", propertiesRequest.UserId, "err", err)
				return false, err
			}
			err = impl.performExpressEditOperationsForDeploymentTemplate(propertiesRequest.AppId, propertiesRequest.EnvironmentId, propertiesRequest.ResourceName)
			if err != nil {
				impl.logger.Errorw("error in performing express edit actions if user is exception", "err", err)
				return false, err
			}
		} else {
			return false, util.NewApiError(http.StatusConflict, constants.NotAnExceptionUserErr, constants.NotAnExceptionUserErr)
		}
	}
	return isSuccess, nil
}

func (impl *DraftAwareConfigServiceImpl) CreateEnvironmentPropertiesAndBaseIfNeeded(ctx context.Context, environmentProperties *bean.EnvironmentProperties, userMetadata *userBean.UserMetadata) (*bean.EnvironmentUpdateResponse, error) {
	var err error
	var resp *bean.EnvironmentUpdateResponse
	if !environmentProperties.IsExpressEdit {
		// if isExpressEdit flag is false then approval config is not applied and user is following normal lifecycle of saving config
		resp, err = impl.propertiesConfigService.CreateEnvironmentPropertiesAndBaseIfNeeded(ctx, environmentProperties.AppId, environmentProperties)
		if err != nil {
			impl.logger.Errorw("error, CreateEnvironmentPropertiesAndBaseIfNeeded", "appId", environmentProperties.AppId, "req", environmentProperties, "err", err)
			return nil, err
		}
	} else {
		isUserException, err := impl.globalPolicyDataManager.IsUserAnExceptionByRqmResourceType(ctx, adaptor.ApplyPolicyTypeToRqmResourceType(model.APPLY_POLICY_APPROVAL_CONFIGURATION).ToInt(), userMetadata)
		if err != nil {
			impl.logger.Errorw("error in checking if a user is exception user or not by policy type ", "policyType", model.APPLY_POLICY_APPROVAL_CONFIGURATION, "err", err)
			return nil, err
		}
		if isUserException {
			resp, err = impl.propertiesConfigService.CreateEnvironmentPropertiesAndBaseIfNeeded(ctx, environmentProperties.AppId, environmentProperties)
			if err != nil {
				impl.logger.Errorw("error, CreateEnvironmentPropertiesAndBaseIfNeeded", "appId", environmentProperties.AppId, "req", environmentProperties, "err", err)
				return nil, err
			}
			err = impl.performExpressEditOperationsForDeploymentTemplate(environmentProperties.AppId, environmentProperties.EnvironmentId, environmentProperties.ResourceName)
			if err != nil {
				impl.logger.Errorw("error in performing express edit actions if user is exception", "err", err)
				return nil, err
			}
		} else {
			return nil, util.NewApiError(http.StatusConflict, constants.NotAnExceptionUserErr, constants.NotAnExceptionUserErr)
		}
	}

	return resp, nil
}
