/*
 * Copyright (c) 2020-2024. Devtron Inc.
 */

package pipeline

import (
	"errors"
	"fmt"
	bean3 "github.com/devtron-labs/devtron/api/restHandler/app/pipeline/configure/bean"
	"github.com/devtron-labs/devtron/internal/util"
	bean2 "github.com/devtron-labs/devtron/pkg/bean"
	chartRepoRepository "github.com/devtron-labs/devtron/pkg/chartRepo/repository"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/chartRef"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/chartRef/bean"
	"github.com/go-pg/pg"
	"go.uber.org/zap"
	"net/http"
)

type DevtronAppStrategyService interface {
	//FetchCDPipelineStrategy : Retrieve CDPipelineStrategy for given appId
	FetchCDPipelineStrategy(appId int) (PipelineStrategiesResponse, error)

	// FetchDefaultCDPipelineStrategy :
	// TODO: uncomment this after code has been refactored as this function doesnt contain any logic to fetch strategy
	FetchDefaultCDPipelineStrategy(appId int, envId int) (bean.PipelineStrategy, error)
	FetchAllCDPipelineStrategiesForPipelineIds(pipelineIds []int, authorisedPipelineIdsMap map[int]bool) ([]*bean3.BulkDeploymentStrategyGetResponse, error)
}

type DevtronAppStrategyServiceImpl struct {
	logger                                          *zap.SugaredLogger
	chartRepository                                 chartRepoRepository.ChartRepository
	globalStrategyMetadataChartRefMappingRepository chartRepoRepository.GlobalStrategyMetadataChartRefMappingRepository
	ciCdPipelineOrchestrator                        CiCdPipelineOrchestrator
	cdPipelineConfigService                         CdPipelineConfigService
	chartRefService                                 chartRef.ChartRefService
}

func NewDevtronAppStrategyServiceImpl(
	logger *zap.SugaredLogger,
	chartRepository chartRepoRepository.ChartRepository,
	globalStrategyMetadataChartRefMappingRepository chartRepoRepository.GlobalStrategyMetadataChartRefMappingRepository,
	ciCdPipelineOrchestrator CiCdPipelineOrchestrator,
	cdPipelineConfigService CdPipelineConfigService,
	chartRefService chartRef.ChartRefService,
) *DevtronAppStrategyServiceImpl {
	return &DevtronAppStrategyServiceImpl{
		logger:          logger,
		chartRepository: chartRepository,
		globalStrategyMetadataChartRefMappingRepository: globalStrategyMetadataChartRefMappingRepository,
		ciCdPipelineOrchestrator:                        ciCdPipelineOrchestrator,
		cdPipelineConfigService:                         cdPipelineConfigService,
		chartRefService:                                 chartRefService,
	}
}

func (impl *DevtronAppStrategyServiceImpl) FetchCDPipelineStrategy(appId int) (PipelineStrategiesResponse, error) {
	pipelineStrategiesResponse := PipelineStrategiesResponse{}
	chart, err := impl.chartRepository.FindLatestChartForAppByAppId(appId)
	if err != nil && !errors.Is(err, pg.ErrNoRows) {
		impl.logger.Errorw("error in fetching chart for app", "appId", appId, "err", err)
		return pipelineStrategiesResponse, err
	}
	if chart.Id == 0 {
		return pipelineStrategiesResponse, fmt.Errorf("no chart configured")
	}
	pipelineStrategies, err := impl.chartRefService.GetDeploymentStrategiesForChartRef(chart.ChartRefId, chart.PipelineOverride)
	if err != nil {
		impl.logger.Errorw("error in fetching deployment strategies for chart ref", "chartRefId", chart.ChartRefId, "err", err)
		return pipelineStrategiesResponse, err
	}
	pipelineStrategiesResponse.PipelineStrategy = pipelineStrategies
	return pipelineStrategiesResponse, nil
}

func (impl *DevtronAppStrategyServiceImpl) FetchDefaultCDPipelineStrategy(appId int, envId int) (bean.PipelineStrategy, error) {
	pipelineStrategy := bean.PipelineStrategy{}
	cdPipelines, err := impl.ciCdPipelineOrchestrator.GetCdPipelinesForAppAndEnv(appId, envId, "")
	if err != nil || (cdPipelines.Pipelines) == nil || len(cdPipelines.Pipelines) == 0 {
		return pipelineStrategy, err
	}
	cdPipelineId := cdPipelines.Pipelines[0].Id

	cdPipeline, err := impl.cdPipelineConfigService.GetCdPipelineById(cdPipelineId)
	if err != nil {
		return pipelineStrategy, nil
	}
	pipelineStrategy.DeploymentTemplate = cdPipeline.DeploymentTemplate
	pipelineStrategy.Default = true
	return pipelineStrategy, nil
}

func (impl *DevtronAppStrategyServiceImpl) FetchAllCDPipelineStrategies(appId int, envId int) ([]bean2.Strategy, error) {
	pipelineStrategies := make([]bean2.Strategy, 0)
	cdPipelines, err := impl.ciCdPipelineOrchestrator.GetCdPipelinesForAppAndEnv(appId, envId, "")
	if err != nil || (cdPipelines.Pipelines) == nil || len(cdPipelines.Pipelines) == 0 {
		impl.logger.Errorw("error in fetching cd pipelines for app and env", "appId", appId, "envId", envId, "err", err)
		return pipelineStrategies, err
	}
	cdPipelineId := cdPipelines.Pipelines[0].Id
	cdPipeline, err := impl.cdPipelineConfigService.GetCdPipelineById(cdPipelineId)
	if err != nil {
		impl.logger.Errorw("error in fetching cd pipeline by id", "cdPipelineId", cdPipelineId, "err", err)
		return nil, err
	}
	return cdPipeline.Strategies, nil
}

func (impl *DevtronAppStrategyServiceImpl) FetchAllCDPipelineStrategiesForPipelineIds(pipelineIds []int, authorisedPipelineIdsMap map[int]bool) ([]*bean3.BulkDeploymentStrategyGetResponse, error) {
	responses := make([]*bean3.BulkDeploymentStrategyGetResponse, 0, len(pipelineIds))
	// Filter out authorized pipeline IDs
	authorizedPipelineIds := make([]int, 0, len(pipelineIds))

	for _, pipelineId := range pipelineIds {
		if isAuthorized, exists := authorisedPipelineIdsMap[pipelineId]; exists && isAuthorized {
			authorizedPipelineIds = append(authorizedPipelineIds, pipelineId)
		} else {
			responses = append(responses, &bean3.BulkDeploymentStrategyGetResponse{
				PipelineId: pipelineId,
				Error:      util.GetApiErrorAdapter(http.StatusForbidden, "403", "Not authorised", "unauthorised user"),
			})
		}
	}

	// If no authorized pipelines, return early
	if len(authorizedPipelineIds) == 0 {
		return responses, nil
	}
	pipelineIdVsStrategies, err := impl.cdPipelineConfigService.GetPipelineStrategiesByPipelineIds(authorizedPipelineIds)
	if err != nil {
		impl.logger.Errorw("error in fetching strategies", "authorizedPipelineIds", authorizedPipelineIds, "err", err)
		return nil, err
	}
	for _, pipelineId := range authorizedPipelineIds {
		strategies, exists := pipelineIdVsStrategies[pipelineId]
		if !exists {
			responses = append(responses, &bean3.BulkDeploymentStrategyGetResponse{
				PipelineId: pipelineId,
				Error:      util.GetApiErrorAdapter(http.StatusNotFound, "404", "Not found", "not found"),
			})
			continue
		}
		responses = append(responses, &bean3.BulkDeploymentStrategyGetResponse{
			PipelineId: pipelineId,
			Strategy:   strategies,
		})
	}
	return responses, nil
}
