/*
 * Copyright (c) 2020-2024. Devtron Inc.
 */

package pipeline

import (
	"context"
	"encoding/json"
	errors2 "errors"
	"fmt"
	"github.com/devtron-labs/devtron/enterprise/pkg/lockConfiguration"
	"github.com/devtron-labs/devtron/internal/util"
	"github.com/devtron-labs/devtron/internal/util/configUtil"
	bean7 "github.com/devtron-labs/devtron/pkg/auth/user/bean"
	chartService "github.com/devtron-labs/devtron/pkg/chart"
	bean5 "github.com/devtron-labs/devtron/pkg/chart/bean"
	"github.com/devtron-labs/devtron/pkg/cluster/environment/repository"
	"github.com/devtron-labs/devtron/pkg/deployment/common"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deployedAppMetrics"
	bean2 "github.com/devtron-labs/devtron/pkg/deployment/manifest/deployedAppMetrics/bean"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/adapter"
	bean4 "github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/bean"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/read"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/validator"
	"github.com/devtron-labs/devtron/pkg/pipeline/bean"
	bean3 "github.com/devtron-labs/devtron/pkg/resourceQualifiers"
	"github.com/devtron-labs/devtron/pkg/variables"
	repository5 "github.com/devtron-labs/devtron/pkg/variables/repository"
	globalUtil "github.com/devtron-labs/devtron/util"
	"go.opentelemetry.io/otel"
	"net/http"
	"time"

	chartRepoRepository "github.com/devtron-labs/devtron/pkg/chartRepo/repository"
	"github.com/devtron-labs/devtron/pkg/sql"

	"github.com/devtron-labs/devtron/internal/sql/models"
	"github.com/devtron-labs/devtron/internal/sql/repository/chartConfig"
	"github.com/go-pg/pg"
	"github.com/juju/errors"
	"go.uber.org/zap"
)

type PropertiesConfigService interface {
	CreateEnvironmentPropertiesAndBaseIfNeeded(ctx context.Context, appId int, environmentProperties *bean.EnvironmentProperties) (*bean.EnvironmentUpdateResponse, error)
	CreateEnvironmentProperties(ctx context.Context, appId int, propertiesRequest *bean.EnvironmentProperties) (*bean.EnvironmentUpdateResponse, error)
	UpdateEnvironmentProperties(appId int, envId int, propertiesRequest *bean.EnvironmentProperties, token string, userId int32) (*bean.EnvironmentUpdateResponse, error)
	//create environment entry for each new environment
	CreateIfRequired(ctx context.Context, request *bean.EnvironmentOverrideCreateInternalDTO, tx *pg.Tx) (*bean4.EnvConfigOverride, bool, error)
	GetEnvironmentProperties(appId, environmentId int, chartRefId int) (environmentPropertiesResponse *bean.EnvironmentPropertiesResponse, err error)
	//
	GetAppIdByChartEnvId(chartEnvId int) (*bean4.EnvConfigOverride, error)
	GetLatestEnvironmentProperties(appId, environmentId int) (*bean.EnvironmentProperties, error)
	ResetEnvironmentProperties(id int, userId int32) (bool, error)
	CreateEnvironmentPropertiesWithNamespace(ctx context.Context, appId int, environmentProperties *bean.EnvironmentProperties) (*bean.EnvironmentProperties, error)
	//
	FetchEnvProperties(appId, envId, chartRefId int) (*bean4.EnvConfigOverride, error)
	ChangeChartRefForEnvConfigOverride(ctx context.Context, request *bean5.ChartRefChangeRequest, userId int32, token string) (*bean.EnvironmentUpdateResponse, error)

	PropertiesConfigServiceEnt
}
type PropertiesConfigServiceImpl struct {
	logger                              *zap.SugaredLogger
	envConfigRepo                       chartConfig.EnvConfigOverrideRepository
	chartRepo                           chartRepoRepository.ChartRepository
	mergeUtil                           configUtil.MergeUtil
	environmentRepository               repository.EnvironmentRepository
	deploymentTemplateHistoryService    deploymentTemplate.DeploymentTemplateHistoryService
	scopedVariableManager               variables.ScopedVariableManager
	lockedConfigService                 lockConfiguration.LockConfigurationService
	deployedAppMetricsService           deployedAppMetrics.DeployedAppMetricsService
	deploymentTemplateValidationService validator.DeploymentTemplateValidationService
	envConfigOverrideReadService        read.EnvConfigOverrideService
	deploymentConfigService             common.DeploymentConfigService
	chartService                        chartService.ChartService
}

func NewPropertiesConfigServiceImpl(logger *zap.SugaredLogger,
	envConfigRepo chartConfig.EnvConfigOverrideRepository,
	chartRepo chartRepoRepository.ChartRepository,
	mergeUtil configUtil.MergeUtil,
	environmentRepository repository.EnvironmentRepository,
	deploymentTemplateHistoryService deploymentTemplate.DeploymentTemplateHistoryService,
	scopedVariableManager variables.ScopedVariableManager,
	lockedConfigService lockConfiguration.LockConfigurationService,
	deployedAppMetricsService deployedAppMetrics.DeployedAppMetricsService,
	deploymentTemplateValidationService validator.DeploymentTemplateValidationService,
	envConfigOverrideReadService read.EnvConfigOverrideService,
	deploymentConfigService common.DeploymentConfigService,
	chartService chartService.ChartService,
) *PropertiesConfigServiceImpl {
	return &PropertiesConfigServiceImpl{
		logger:                              logger,
		envConfigRepo:                       envConfigRepo,
		chartRepo:                           chartRepo,
		environmentRepository:               environmentRepository,
		deploymentTemplateHistoryService:    deploymentTemplateHistoryService,
		scopedVariableManager:               scopedVariableManager,
		lockedConfigService:                 lockedConfigService,
		deployedAppMetricsService:           deployedAppMetricsService,
		deploymentTemplateValidationService: deploymentTemplateValidationService,
		envConfigOverrideReadService:        envConfigOverrideReadService,
		mergeUtil:                           mergeUtil,
		deploymentConfigService:             deploymentConfigService,
		chartService:                        chartService,
	}
}

func (impl *PropertiesConfigServiceImpl) GetEnvironmentProperties(appId, environmentId int, chartRefId int) (environmentPropertiesResponse *bean.EnvironmentPropertiesResponse, err error) {
	environmentPropertiesResponse = &bean.EnvironmentPropertiesResponse{}
	env, err := impl.environmentRepository.FindById(environmentId)
	if err != nil {
		impl.logger.Errorw("error in finding env by id", "err", err)
		return nil, err
	}
	if len(env.Namespace) > 0 {
		environmentPropertiesResponse.Namespace = env.Namespace
	}

	// step 1
	envOverride, err := impl.envConfigOverrideReadService.ActiveEnvConfigOverride(appId, environmentId)
	if err != nil {
		impl.logger.Errorw("error in finding ActiveEnvConfigOverride", "err", err)
		return nil, err
	}
	environmentProperties := &bean.EnvironmentProperties{}
	if envOverride.Id > 0 {
		r := json.RawMessage("{}")
		rPatch := json.RawMessage("{}")
		if envOverride.IsOverride {
			environmentPropertiesResponse.IsOverride = true
			if envOverride.MergeStrategy == models.MERGE_STRATEGY_PATCH {
				err = rPatch.UnmarshalJSON([]byte(envOverride.EnvOverridePatchValues))
				if err != nil {
					impl.logger.Errorw("error in unmarshalling patch values", "err", err)
					return nil, err
				}
			}
		}
		err = r.UnmarshalJSON([]byte(envOverride.EnvOverrideValues))
		if err != nil {
			return nil, err
		}

		environmentProperties = &bean.EnvironmentProperties{
			//Id:                envOverride.Id,
			Status:            envOverride.Status,
			EnvOverrideValues: r,
			ManualReviewed:    envOverride.ManualReviewed,
			Active:            envOverride.Active,
			Namespace:         env.Namespace,
			Description:       env.Description,
			EnvironmentId:     environmentId,
			EnvironmentName:   env.Name,
			Latest:            envOverride.Latest,
			//ChartRefId:        chartRefId,
			IsOverride:             envOverride.IsOverride,
			IsBasicViewLocked:      envOverride.IsBasicViewLocked,
			CurrentViewEditor:      envOverride.CurrentViewEditor,
			MergeStrategy:          envOverride.MergeStrategy,
			EnvOverridePatchValues: rPatch,
		}
		if chartRefId == 0 && envOverride.Chart != nil {
			environmentProperties.ChartRefId = envOverride.Chart.ChartRefId
		}

		if environmentPropertiesResponse.Namespace == "" {
			environmentPropertiesResponse.Namespace = envOverride.Namespace
		}
	}
	ecOverride, err := impl.envConfigOverrideReadService.FindChartByAppIdAndEnvIdAndChartRefId(appId, environmentId, chartRefId)
	if err != nil && !errors.IsNotFound(err) {
		impl.logger.Errorw("error in finding FindChartByAppIdAndEnvIdAndChartRefId", "err", err)
		return nil, err
	}
	if errors.IsNotFound(err) {
		environmentProperties.Id = 0
		environmentProperties.IsOverride = false
		if chartRefId > 0 {
			environmentProperties.ChartRefId = chartRefId
		}
	} else {
		environmentProperties.Id = ecOverride.Id
		environmentProperties.Latest = ecOverride.Latest
		environmentProperties.IsOverride = ecOverride.IsOverride
		environmentProperties.ChartRefId = chartRefId
		environmentProperties.ManualReviewed = ecOverride.ManualReviewed
		environmentProperties.Status = ecOverride.Status
		environmentProperties.Namespace = ecOverride.Namespace
		environmentProperties.Active = ecOverride.Active
		environmentProperties.IsBasicViewLocked = ecOverride.IsBasicViewLocked
		environmentProperties.CurrentViewEditor = ecOverride.CurrentViewEditor
		if chartRefId == 0 && ecOverride.Chart != nil {
			environmentProperties.ChartRefId = ecOverride.Chart.ChartRefId
		}
	}
	environmentPropertiesResponse.ChartRefId = chartRefId
	environmentPropertiesResponse.EnvironmentConfig = *environmentProperties

	//setting global config
	chart, err := impl.chartRepo.FindLatestChartForAppByAppId(appId)
	if err != nil {
		impl.logger.Errorw("error in finding FindLatestChartForAppByAppId", "appId", appId, "err", err)
		return nil, err
	}
	if chart != nil && chart.Id > 0 {
		globalOverride := []byte(chart.GlobalOverride)
		environmentPropertiesResponse.GlobalConfig = globalOverride
		environmentPropertiesResponse.GlobalChartRefId = chart.ChartRefId
		if !environmentPropertiesResponse.IsOverride {
			environmentPropertiesResponse.EnvironmentConfig.IsBasicViewLocked = chart.IsBasicViewLocked
			environmentPropertiesResponse.EnvironmentConfig.CurrentViewEditor = chart.CurrentViewEditor
		}
	}
	isAppMetricsEnabled, err := impl.deployedAppMetricsService.GetMetricsFlagForAPipelineByAppIdAndEnvId(appId, environmentId)
	if err != nil {
		impl.logger.Errorw("error, GetMetricsFlagForAPipelineByAppIdAndEnvId", "err", err, "appId", appId, "envId", environmentId)
		return nil, err
	}
	environmentPropertiesResponse.AppMetrics = &isAppMetricsEnabled

	externalReleaseType, err := impl.deploymentConfigService.GetExternalReleaseType(appId, environmentId)
	if err != nil {
		impl.logger.Errorw("error in getting deployment config by appId and envId", "appId", appId, "envId", environmentId, "err", err)
		return nil, err
	}
	if len(externalReleaseType) != 0 {
		environmentPropertiesResponse.EnvironmentConfig.MigratedFrom = &externalReleaseType
	}
	return environmentPropertiesResponse, nil
}

func (impl *PropertiesConfigServiceImpl) FetchEnvProperties(appId, envId, chartRefId int) (*bean4.EnvConfigOverride, error) {
	return impl.envConfigOverrideReadService.GetByAppIdEnvIdAndChartRefId(appId, envId, chartRefId)
}

func (impl *PropertiesConfigServiceImpl) CreateEnvironmentPropertiesAndBaseIfNeeded(ctx context.Context, appId int, envConfigProperties *bean.EnvironmentProperties) (*bean.EnvironmentUpdateResponse, error) {
	createResp, err := impl.CreateEnvironmentProperties(ctx, appId, envConfigProperties)
	if err != nil {
		if err.Error() == bean7.NOCHARTEXIST {
			appMetrics := false
			if envConfigProperties.AppMetrics != nil {
				appMetrics = *envConfigProperties.AppMetrics
			}
			templateRequest := bean5.TemplateRequest{
				AppId:               appId,
				ChartRefId:          envConfigProperties.ChartRefId,
				ValuesOverride:      globalUtil.GetEmptyJSON(),
				UserId:              envConfigProperties.UserId,
				IsAppMetricsEnabled: appMetrics,
			}
			_, err = impl.chartService.CreateChartFromEnvOverride(ctx, templateRequest)
			if err != nil {
				impl.logger.Errorw("service err, EnvConfigOverrideCreate", "err", err, "payload", envConfigProperties)
				return nil, err
			}
			createResp, err = impl.CreateEnvironmentProperties(ctx, appId, envConfigProperties)
			if err != nil {
				impl.logger.Errorw("service err, EnvConfigOverrideCreate", "err", err, "payload", envConfigProperties)
				return nil, err
			}
		} else {
			impl.logger.Errorw("service err, EnvConfigOverrideCreate", "err", err, "payload", envConfigProperties)
			return nil, err
		}
	}
	return createResp, nil
}

func (impl *PropertiesConfigServiceImpl) CreateEnvironmentProperties(ctx context.Context, appId int, environmentProperties *bean.EnvironmentProperties) (*bean.EnvironmentUpdateResponse, error) {
	newCtx, span := otel.Tracer("orchestrator").Start(ctx, "PropertiesConfigServiceImpl.CreateEnvironmentProperties")
	defer span.End()
	if len(environmentProperties.MergeStrategy) == 0 {
		environmentProperties.MergeStrategy = models.MERGE_STRATEGY_REPLACE
	}

	if environmentProperties.MergeStrategy == models.MERGE_STRATEGY_REPLACE &&
		globalUtil.IsEmptyJSON(environmentProperties.EnvOverrideValues) {
		return nil, fmt.Errorf("env override values cannot be empty for replace strategy")
	}

	chart, err := impl.chartRepo.FindChartByAppIdAndRefId(appId, environmentProperties.ChartRefId)
	if err != nil && !errors2.Is(err, pg.ErrNoRows) {
		return nil, err
	} else if errors2.Is(err, pg.ErrNoRows) {
		impl.logger.Errorw("create new chart set latest=false", "a", "b")
		return nil, fmt.Errorf("NOCHARTEXIST")
	}

	externalReleaseType, err := impl.deploymentConfigService.GetExternalReleaseType(chart.AppId, environmentProperties.EnvironmentId)
	if err != nil {
		impl.logger.Errorw("error in getting deployment config by appId and envId", "appId", chart.AppId, "envId", environmentProperties.EnvironmentId, "err", err)
		return nil, err
	}
	if externalReleaseType.IsArgoApplication() {
		return nil, util.NewApiError(http.StatusConflict,
			"chart version change is not allowed for external argo application",
			"chart version change is not allowed for external argo application")
	}

	emptyJSON, err := globalUtil.GetEmptyJsonObjectString()
	if err != nil {
		impl.logger.Errorw("error in getting empty json object", "err", err)
		return nil, err
	}

	existingOverrideValues := emptyJSON

	//TODO: known issue here that when first time env override is created lock-config is not checked. Need product to get involve in this
	envOverrideExisting, err := impl.envConfigOverrideReadService.FindLatestChartForAppByAppIdAndEnvId(appId, environmentProperties.EnvironmentId)
	if err != nil && !errors.IsNotFound(err) {
		return nil, err
	}
	if envOverrideExisting != nil {

		existingOverrideValues = envOverrideExisting.GetDBOverrideValuesByMergeStrategy()

		if environmentProperties.SaveEligibleChanges {
			//VARIABLE_RESOLVE
			scope := bean3.Scope{
				AppId:     appId,
				EnvId:     environmentProperties.EnvironmentId,
				ClusterId: environmentProperties.ClusterId,
			}

			lockedChangesRevertedValues, err := impl.lockedConfigService.RevertChangesInLockedFields(string(environmentProperties.EnvOverrideValues), existingOverrideValues, newCtx.Value("token").(string), int(environmentProperties.UserId), appId, environmentProperties.EnvironmentId)
			if err != nil {
				impl.logger.Errorw("error in syncing locked changes of old config and request config", "appId", appId, "envId", environmentProperties.EnvironmentId, "err", err)
				return nil, err
			}
			environmentProperties.EnvOverrideValues = json.RawMessage(lockedChangesRevertedValues)
			values, _, err := impl.envConfigOverrideReadService.GetRuntimeValueForUnsavedEnvOverrideResolved(environmentProperties.MergeStrategy, chart, chart.AppId, string(environmentProperties.EnvOverrideValues), scope)
			if err != nil {
				impl.logger.Errorw("error in getting values for deployment template validation", "appId", appId, "err", err)
			}
			validate, err2 := impl.deploymentTemplateValidationService.DeploymentTemplateValidateWithoutResolution(context.Background(), json.RawMessage(values), environmentProperties.ChartRefId)
			if !validate {
				return nil, err2
			}
		}

		lockConfigErrorResponse, err := impl.lockedConfigService.HandleLockConfiguration(string(environmentProperties.EnvOverrideValues), existingOverrideValues, newCtx.Value("token").(string), int(environmentProperties.UserId), appId, environmentProperties.EnvironmentId)
		if err != nil {
			return nil, err
		}
		if lockConfigErrorResponse != nil {
			return &bean.EnvironmentUpdateResponse{
				EnvironmentProperties:     environmentProperties,
				LockValidateErrorResponse: lockConfigErrorResponse,
			}, nil
		}
	}

	chart.GlobalOverride = string(environmentProperties.EnvOverrideValues)
	appMetrics := false
	if environmentProperties.AppMetrics != nil {
		appMetrics = *environmentProperties.AppMetrics
	}

	overrideCreateRequest := &bean.EnvironmentOverrideCreateInternalDTO{
		Chart:               chart,
		EnvironmentId:       environmentProperties.EnvironmentId,
		UserId:              environmentProperties.UserId,
		ManualReviewed:      environmentProperties.ManualReviewed,
		ChartStatus:         models.CHARTSTATUS_SUCCESS,
		IsOverride:          true,
		IsAppMetricsEnabled: appMetrics,
		IsBasicViewLocked:   environmentProperties.IsBasicViewLocked,
		Namespace:           environmentProperties.Namespace,
		CurrentViewEditor:   environmentProperties.CurrentViewEditor,
		MergeStrategy:       environmentProperties.MergeStrategy,
	}
	envOverride, appMetrics, err := impl.CreateIfRequired(newCtx, overrideCreateRequest, nil)
	if err != nil {
		return nil, err
	}
	environmentProperties.AppMetrics = &appMetrics
	r := json.RawMessage{}
	err = r.UnmarshalJSON([]byte(envOverride.EnvOverrideValues))
	if err != nil {
		return nil, err
	}
	env, err := impl.environmentRepository.FindById(environmentProperties.EnvironmentId)
	if err != nil {
		return nil, err
	}
	environmentProperties = &bean.EnvironmentProperties{
		Id:                envOverride.Id,
		Status:            envOverride.Status,
		EnvOverrideValues: r,
		ManualReviewed:    envOverride.ManualReviewed,
		Active:            envOverride.Active,
		Namespace:         env.Namespace,
		EnvironmentId:     environmentProperties.EnvironmentId,
		EnvironmentName:   env.Name,
		Latest:            envOverride.Latest,
		ChartRefId:        environmentProperties.ChartRefId,
		IsOverride:        envOverride.IsOverride,
		MergeStrategy:     envOverride.MergeStrategy,
	}
	if err != nil {
		impl.logger.Errorw("chart version parsing", "err", err, "chartVersion", chart.ChartVersion)
		return nil, err
	}

	return &bean.EnvironmentUpdateResponse{
		EnvironmentProperties: environmentProperties,
	}, nil
}

func (impl *PropertiesConfigServiceImpl) UpdateEnvironmentProperties(appId int, envId int, propertiesRequest *bean.EnvironmentProperties, token string, userId int32) (*bean.EnvironmentUpdateResponse, error) {
	//check if exists
	if len(propertiesRequest.MergeStrategy) == 0 {
		propertiesRequest.MergeStrategy = models.MERGE_STRATEGY_REPLACE
	}

	if propertiesRequest.MergeStrategy == models.MERGE_STRATEGY_REPLACE &&
		globalUtil.IsEmptyJSON(propertiesRequest.EnvOverrideValues) {
		return nil, fmt.Errorf("env override values cannot be empty for replace strategy")
	}

	oldEnvOverride, err := impl.envConfigOverrideReadService.GetByIdIncludingInactive(propertiesRequest.Id)
	if err != nil {
		return nil, err
	}

	env, err := impl.environmentRepository.FindById(oldEnvOverride.TargetEnvironment)
	if err != nil {
		return nil, err
	}
	//FIXME add check for restricted NS also like (kube-system, devtron, monitoring, etc)
	if env.Namespace != "" && env.Namespace != propertiesRequest.Namespace {
		return nil, fmt.Errorf("environment is restricted to namespace: %s only, cant deploy to: %s", env.Namespace, propertiesRequest.Namespace)
	}

	existingOverrideValues := oldEnvOverride.GetDBOverrideValuesByMergeStrategy()

	var envOverrideExisting *bean4.EnvConfigOverride
	if !oldEnvOverride.Latest {
		envOverrideExisting, err = impl.envConfigOverrideReadService.FindLatestChartForAppByAppIdAndEnvId(appId, oldEnvOverride.TargetEnvironment)
		if err != nil && !errors.IsNotFound(err) {
			return nil, err
		}
		if envOverrideExisting != nil {
			existingOverrideValues = envOverrideExisting.GetDBOverrideValuesByMergeStrategy()
		} else {
			emptyJSON, err := globalUtil.GetEmptyJsonObjectString()
			if err != nil {
				impl.logger.Errorw("error in getting empty json object", "err", err)
				return nil, err
			}
			existingOverrideValues = emptyJSON
		}
	}
	// TODO look on this at manager level

	envOverrideBytesToStoreInDb := string(propertiesRequest.EnvOverrideValues)
	if propertiesRequest.SaveEligibleChanges {

		lockedChangesRevertedValues, err := impl.lockedConfigService.RevertChangesInLockedFields(envOverrideBytesToStoreInDb, existingOverrideValues, token, int(propertiesRequest.UserId), appId, envId)
		if err != nil {
			impl.logger.Errorw("error in syncing locked changes of old config and request config", "appId", appId, "envId", envId, "err", err)
			return nil, err
		}
		envOverrideBytesToStoreInDb = lockedChangesRevertedValues
		//VARIABLE_RESOLVE
		scope := bean3.Scope{
			AppId:     appId,
			EnvId:     envId,
			ClusterId: propertiesRequest.ClusterId,
		}
		valuesForValidation, _, err := impl.envConfigOverrideReadService.GetRuntimeValueForUnsavedEnvOverrideResolved(propertiesRequest.MergeStrategy, nil, appId, envOverrideBytesToStoreInDb, scope)
		if err != nil {
			impl.logger.Errorw("error in getting values for validation", "appId", appId, "envId", envId, "err", err)
			return nil, err
		}
		// validation of deployment template validate
		// validation of deployment template validate
		chartRefId := propertiesRequest.ChartRefId
		validate, err2 := impl.deploymentTemplateValidationService.DeploymentTemplateValidateWithoutResolution(context.Background(), json.RawMessage(valuesForValidation), chartRefId)
		if !validate {
			return nil, err2
		}
	}

	// Handle Lock Configuration
	lockConfigErrorResponse, err := impl.lockedConfigService.HandleLockConfiguration(envOverrideBytesToStoreInDb, existingOverrideValues, token, int(propertiesRequest.UserId), appId, envId)
	if err != nil {
		return nil, err
	}
	if lockConfigErrorResponse != nil {
		return &bean.EnvironmentUpdateResponse{
			EnvironmentProperties:     propertiesRequest,
			LockValidateErrorResponse: lockConfigErrorResponse,
		}, nil
	}

	if envOverrideExisting != nil {
		envOverrideExisting.Latest = false
		envOverrideExisting.IsOverride = false
		envOverrideExisting.UpdatedOn = time.Now()
		envOverrideExisting.UpdatedBy = userId

		envOverrideExistingDBObj := adapter.EnvOverrideDTOToDB(envOverrideExisting)
		envOverrideExistingDBObj, err = impl.envConfigRepo.Update(envOverrideExistingDBObj)
		if err != nil {
			return nil, err
		}
	}
	overrideDbObj := &chartConfig.EnvConfigOverride{
		Active:            propertiesRequest.Active,
		Id:                propertiesRequest.Id,
		ChartId:           oldEnvOverride.ChartId,
		Status:            propertiesRequest.Status,
		ManualReviewed:    propertiesRequest.ManualReviewed,
		Namespace:         propertiesRequest.Namespace,
		TargetEnvironment: propertiesRequest.EnvironmentId,
		IsBasicViewLocked: propertiesRequest.IsBasicViewLocked,
		CurrentViewEditor: propertiesRequest.CurrentViewEditor,
		MergeStrategy:     propertiesRequest.MergeStrategy,
		AuditLog:          sql.AuditLog{UpdatedBy: propertiesRequest.UserId, UpdatedOn: time.Now()},
	}
	overrideDbObj.EnvOverrideValues = envOverrideBytesToStoreInDb
	overrideDbObj.Latest = true
	overrideDbObj.IsOverride = propertiesRequest.IsOverride
	impl.logger.Debugw("updating environment override ", "value", overrideDbObj)
	err = impl.envConfigRepo.UpdateProperties(overrideDbObj)

	if oldEnvOverride.Namespace != overrideDbObj.Namespace {
		return nil, fmt.Errorf("namespace name update not supported")
	}

	if err != nil {
		impl.logger.Errorw("chart version parsing", "err", err)
		return nil, err
	}

	chart, err := impl.chartRepo.FindById(overrideDbObj.ChartId)
	if err != nil {
		impl.logger.Errorw("error in chartRefRepository.FindById", "chartRefId", chart.ChartRefId, "err", err)
		return nil, err
	}
	err = impl.deploymentConfigService.UpdateChartLocationInDeploymentConfig(appId, overrideDbObj.TargetEnvironment, chart.ChartRefId, userId, chart.ChartVersion)
	if err != nil {
		impl.logger.Errorw("error in UpdateChartLocationInDeploymentConfig", "appId", appId, "envId", overrideDbObj.TargetEnvironment, "err", err)
		return nil, err
	}

	isAppMetricsEnabled := false
	if propertiesRequest.AppMetrics != nil {
		isAppMetricsEnabled = *propertiesRequest.AppMetrics
	}
	envLevelMetricsUpdateReq := &bean2.DeployedAppMetricsRequest{
		EnableMetrics: isAppMetricsEnabled,
		AppId:         appId,
		EnvId:         oldEnvOverride.TargetEnvironment,
		ChartRefId:    oldEnvOverride.Chart.ChartRefId,
		UserId:        propertiesRequest.UserId,
	}
	err = impl.deployedAppMetricsService.CreateOrUpdateAppOrEnvLevelMetrics(context.Background(), envLevelMetricsUpdateReq)
	if err != nil {
		impl.logger.Errorw("error, CheckAndUpdateAppOrEnvLevelMetrics", "err", err, "req", envLevelMetricsUpdateReq)
		return nil, err
	}

	overrideOverrideDTO := adapter.EnvOverrideDBToDTO(overrideDbObj)
	//creating history
	err = impl.deploymentTemplateHistoryService.CreateDeploymentTemplateHistoryFromEnvOverrideTemplate(overrideOverrideDTO, nil, isAppMetricsEnabled, 0)
	if err != nil {
		impl.logger.Errorw("error in creating entry for env deployment template history", "err", err, "envOverride", overrideOverrideDTO)
		return nil, err
	}
	//VARIABLE_MAPPING_UPDATE
	err = impl.scopedVariableManager.ExtractAndMapVariables(overrideOverrideDTO.EnvOverrideValues, overrideDbObj.Id, repository5.EntityTypeDeploymentTemplateEnvLevel, overrideOverrideDTO.CreatedBy, nil)
	if err != nil {
		return nil, err
	}
	return &bean.EnvironmentUpdateResponse{
		EnvironmentProperties: propertiesRequest,
	}, nil
}

func (impl *PropertiesConfigServiceImpl) CreateIfRequired(ctx context.Context, request *bean.EnvironmentOverrideCreateInternalDTO, tx *pg.Tx) (*bean4.EnvConfigOverride, bool, error) {
	newCtx, span := otel.Tracer("orchestrator").Start(ctx, "PropertiesConfigServiceImpl.CreateIfRequired")
	defer span.End()
	chart := request.Chart
	environmentId := request.EnvironmentId
	userId := request.UserId
	manualReviewed := request.ManualReviewed
	chartStatus := request.ChartStatus
	isOverride := request.IsOverride
	isAppMetricsEnabled := request.IsAppMetricsEnabled
	IsBasicViewLocked := request.IsBasicViewLocked
	namespace := request.Namespace
	CurrentViewEditor := request.CurrentViewEditor

	env, err := impl.environmentRepository.FindById(environmentId)
	if err != nil {
		return nil, request.IsAppMetricsEnabled, err
	}

	if env != nil && len(env.Namespace) > 0 {
		namespace = env.Namespace
	}

	if isOverride { //case of override, to do app metrics operation
		envLevelMetricsUpdateReq := &bean2.DeployedAppMetricsRequest{
			EnableMetrics: isAppMetricsEnabled,
			AppId:         chart.AppId,
			EnvId:         environmentId,
			ChartRefId:    chart.ChartRefId,
			UserId:        userId,
		}
		err = impl.deployedAppMetricsService.CreateOrUpdateAppOrEnvLevelMetrics(newCtx, envLevelMetricsUpdateReq)
		if err != nil {
			impl.logger.Errorw("error, CheckAndUpdateAppOrEnvLevelMetrics", "err", err, "req", envLevelMetricsUpdateReq)
			return nil, isAppMetricsEnabled, err
		}
		//updating metrics flag because it might be possible that the chartRef used was not supported and that could have override the metrics flag got in request
		isAppMetricsEnabled = envLevelMetricsUpdateReq.EnableMetrics
	}

	envOverride, err := impl.envConfigOverrideReadService.GetByChartAndEnvironment(chart.Id, environmentId)
	if err != nil && !errors.IsNotFound(err) {
		return nil, isAppMetricsEnabled, err
	}
	if errors.IsNotFound(err) {
		if isOverride {
			// before creating new entry, remove previous one from latest tag
			envOverrideExisting, err := impl.envConfigOverrideReadService.FindLatestChartForAppByAppIdAndEnvId(chart.AppId, environmentId)
			if err != nil && !errors.IsNotFound(err) {
				return nil, isAppMetricsEnabled, err
			}
			if envOverrideExisting != nil {
				envOverrideExisting.Latest = false
				envOverrideExisting.UpdatedOn = time.Now()
				envOverrideExisting.UpdatedBy = userId
				envOverrideExisting.IsOverride = isOverride
				envOverrideExisting.IsBasicViewLocked = IsBasicViewLocked
				envOverrideExisting.CurrentViewEditor = CurrentViewEditor
				//maintaining backward compatibility for while
				envOverrideDBObj := adapter.EnvOverrideDTOToDB(envOverrideExisting)
				if tx != nil {
					envOverrideDBObj, err = impl.envConfigRepo.UpdateWithTxn(envOverrideDBObj, tx)
				} else {
					envOverrideDBObj, err = impl.envConfigRepo.Update(envOverrideDBObj)
				}
				if err != nil {
					return nil, isAppMetricsEnabled, err
				}
			}
		}

		impl.logger.Debugw("env config not found creating new ", "chart", chart.Id, "env", environmentId)
		//create new
		envOverrideDBObj := &chartConfig.EnvConfigOverride{
			Active:            true,
			ManualReviewed:    manualReviewed,
			Status:            chartStatus,
			TargetEnvironment: environmentId,
			ChartId:           chart.Id,
			AuditLog:          sql.AuditLog{UpdatedBy: userId, UpdatedOn: time.Now(), CreatedOn: time.Now(), CreatedBy: userId},
			Namespace:         namespace,
			IsOverride:        isOverride,
			IsBasicViewLocked: IsBasicViewLocked,
			CurrentViewEditor: CurrentViewEditor,
		}
		if isOverride {
			if len(request.MergeStrategy) == 0 {
				envOverrideDBObj.MergeStrategy = models.MERGE_STRATEGY_REPLACE
			} else {
				envOverrideDBObj.MergeStrategy = request.MergeStrategy
			}
			envOverrideDBObj.EnvOverrideValues = chart.GlobalOverride
			envOverrideDBObj.Latest = true
		} else {
			emptyJSON, err := globalUtil.GetEmptyJsonObjectString()
			if err != nil {
				impl.logger.Errorw("error in getting empty json object", "err", err)
				return nil, isAppMetricsEnabled, err
			}
			envOverrideDBObj.EnvOverrideValues = emptyJSON
		}
		//maintaining backward compatibility for while
		if tx != nil {
			err = impl.envConfigRepo.SaveWithTxn(envOverrideDBObj, tx)
		} else {
			err = impl.envConfigRepo.Save(envOverrideDBObj)
		}
		if err != nil {
			impl.logger.Errorw("error in creating envconfig", "data", envOverride, "error", err)
			return nil, isAppMetricsEnabled, err
		}
		envOverrideDBObj.Chart = chart
		envOverride = adapter.EnvOverrideDBToDTO(envOverrideDBObj)

		err = impl.deploymentConfigService.UpdateChartLocationInDeploymentConfig(chart.AppId, envOverride.TargetEnvironment, chart.ChartRefId, userId, envOverride.Chart.ChartVersion)
		if err != nil {
			impl.logger.Errorw("error in UpdateChartLocationInDeploymentConfig", "appId", chart.AppId, "envId", envOverride.TargetEnvironment, "err", err)
			return nil, isAppMetricsEnabled, err
		}

		err = impl.deploymentTemplateHistoryService.CreateDeploymentTemplateHistoryFromEnvOverrideTemplate(envOverride, tx, isAppMetricsEnabled, 0)
		if err != nil {
			impl.logger.Errorw("error in creating entry for env deployment template history", "err", err, "envOverride", envOverride)
			return nil, isAppMetricsEnabled, err
		}

		//VARIABLE_MAPPING_UPDATE
		if envOverride.EnvOverrideValues != "{}" {
			err = impl.scopedVariableManager.ExtractAndMapVariables(envOverride.EnvOverrideValues, envOverride.Id, repository5.EntityTypeDeploymentTemplateEnvLevel, envOverride.CreatedBy, tx)
			if err != nil {
				return nil, isAppMetricsEnabled, err
			}
		}
	}
	return envOverride, isAppMetricsEnabled, nil
}

func (impl *PropertiesConfigServiceImpl) GetAppIdByChartEnvId(chartEnvId int) (*bean4.EnvConfigOverride, error) {
	envOverride, err := impl.envConfigOverrideReadService.GetByIdIncludingInactive(chartEnvId)
	if err != nil {
		impl.logger.Error("error fetching override config", "err", err)
		return nil, err
	}
	return envOverride, nil
}

func (impl *PropertiesConfigServiceImpl) GetLatestEnvironmentProperties(appId, environmentId int) (environmentProperties *bean.EnvironmentProperties, err error) {
	env, err := impl.environmentRepository.FindById(environmentId)
	if err != nil {
		return nil, err
	}
	// step 1
	envOverride, err := impl.envConfigOverrideReadService.ActiveEnvConfigOverride(appId, environmentId)
	if err != nil {
		return nil, err
	}
	if envOverride.Id == 0 {
		//return nil, errors.New("No env config exists with tag latest for given appId and envId")
		impl.logger.Warnw("No env config exists with tag latest for given appId and envId", "envId", environmentId)
	} else {
		r := json.RawMessage("{}")
		rPatch := json.RawMessage("{}")
		if envOverride.IsOverride {
			if envOverride.MergeStrategy == models.MERGE_STRATEGY_PATCH {
				err = rPatch.UnmarshalJSON([]byte(envOverride.EnvOverridePatchValues))
				if err != nil {
					impl.logger.Errorw("error in unmarshalling patch values", "err", err)
					return nil, err
				}
			}
		}
		err = r.UnmarshalJSON([]byte(envOverride.EnvOverrideValues))
		if err != nil {
			return nil, err
		}
		environmentProperties = &bean.EnvironmentProperties{
			Id:                     envOverride.Id,
			Status:                 envOverride.Status,
			EnvOverrideValues:      r,
			ManualReviewed:         envOverride.ManualReviewed,
			Active:                 envOverride.Active,
			Namespace:              env.Namespace,
			Description:            env.Description,
			EnvironmentId:          environmentId,
			EnvironmentName:        env.Name,
			Latest:                 envOverride.Latest,
			ChartRefId:             envOverride.Chart.ChartRefId,
			IsOverride:             envOverride.IsOverride,
			IsBasicViewLocked:      envOverride.IsBasicViewLocked,
			CurrentViewEditor:      envOverride.CurrentViewEditor,
			MergeStrategy:          envOverride.MergeStrategy,
			EnvOverridePatchValues: rPatch,
		}
	}
	return environmentProperties, nil
}

func (impl *PropertiesConfigServiceImpl) ResetEnvironmentProperties(id int, userId int32) (bool, error) {
	envOverride, err := impl.envConfigOverrideReadService.GetByIdIncludingInactive(id)
	if err != nil {
		return false, err
	}
	envOverride.EnvOverrideValues = "{}"
	envOverride.MergeStrategy = ""
	envOverride.IsOverride = false
	envOverride.Latest = false
	impl.logger.Infow("reset environment override ", "value", envOverride)

	envOverrideDBObj := adapter.EnvOverrideDTOToDB(envOverride)
	err = impl.envConfigRepo.UpdateProperties(envOverrideDBObj)
	if err != nil {
		impl.logger.Warnw("error in update envOverride", "envOverrideId", id)
	}
	err = impl.deployedAppMetricsService.DeleteEnvLevelMetricsIfPresent(envOverride.Chart.AppId, envOverride.TargetEnvironment)
	if err != nil {
		impl.logger.Errorw("error, DeleteEnvLevelMetricsIfPresent", "err", err, "appId", envOverride.Chart.AppId, "envId", envOverride.TargetEnvironment)
		return false, err
	}

	chart, err := impl.chartRepo.FindLatestChartForAppByAppId(envOverride.Chart.AppId)
	if err != nil {
		impl.logger.Errorw("error in chartRefRepository.FindById", "chartRefId", envOverride.Chart.ChartRefId, "err", err)
		return false, err
	}
	err = impl.deploymentConfigService.UpdateChartLocationInDeploymentConfig(envOverride.Chart.AppId, envOverride.TargetEnvironment, chart.ChartRefId, userId, chart.ChartVersion)
	if err != nil {
		impl.logger.Errorw("error in UpdateChartLocationInDeploymentConfig", "appId", envOverride.Chart.AppId, "envId", envOverride.TargetEnvironment, "err", err)
		return false, err
	}

	// VARIABLES
	err = impl.scopedVariableManager.RemoveMappedVariables(envOverride.Id, repository5.EntityTypeDeploymentTemplateEnvLevel, envOverride.UpdatedBy, nil)
	if err != nil {
		return false, err
	}
	return true, nil
}

func (impl *PropertiesConfigServiceImpl) CreateEnvironmentPropertiesWithNamespace(ctx context.Context, appId int, environmentProperties *bean.EnvironmentProperties) (*bean.EnvironmentProperties, error) {
	newCtx, span := otel.Tracer("orchestrator").Start(ctx, "PropertiesConfigServiceImpl.CreateEnvironmentPropertiesWithNamespace")
	defer span.End()
	chart, err := impl.chartRepo.FindChartByAppIdAndRefId(appId, environmentProperties.ChartRefId)
	if err != nil && !errors2.Is(err, pg.ErrNoRows) {
		return nil, err
	}
	if errors2.Is(err, pg.ErrNoRows) {
		impl.logger.Warnw("no chart found this ref id", "refId", environmentProperties.ChartRefId)
		chart, err = impl.chartRepo.FindLatestChartForAppByAppId(appId)
		if err != nil && !errors2.Is(err, pg.ErrNoRows) {
			return nil, err
		}
	}

	var envOverride *bean4.EnvConfigOverride
	if environmentProperties.Id == 0 {
		chart.GlobalOverride = "{}"
		appMetrics := false
		if environmentProperties.AppMetrics != nil {
			appMetrics = *environmentProperties.AppMetrics
		}
		overrideCreateRequest := &bean.EnvironmentOverrideCreateInternalDTO{
			Chart:               chart,
			EnvironmentId:       environmentProperties.EnvironmentId,
			UserId:              environmentProperties.UserId,
			ManualReviewed:      environmentProperties.ManualReviewed,
			ChartStatus:         models.CHARTSTATUS_SUCCESS,
			IsOverride:          false,
			IsAppMetricsEnabled: appMetrics,
			IsBasicViewLocked:   environmentProperties.IsBasicViewLocked,
			Namespace:           environmentProperties.Namespace,
			CurrentViewEditor:   environmentProperties.CurrentViewEditor,
			MergeStrategy:       environmentProperties.MergeStrategy,
		}
		envOverride, appMetrics, err = impl.CreateIfRequired(newCtx, overrideCreateRequest, nil)
		if err != nil {
			return nil, err
		}
		environmentProperties.AppMetrics = &appMetrics
	} else {
		envOverride, err = impl.envConfigOverrideReadService.GetByIdIncludingInactive(environmentProperties.Id)
		if err != nil {
			impl.logger.Errorw("error in fetching envOverride", "err", err)
		}
		envOverride.Namespace = environmentProperties.Namespace
		envOverride.UpdatedBy = environmentProperties.UserId
		envOverride.IsBasicViewLocked = environmentProperties.IsBasicViewLocked
		envOverride.CurrentViewEditor = environmentProperties.CurrentViewEditor
		envOverride.UpdatedOn = time.Now()
		impl.logger.Debugw("updating environment override ", "value", envOverride)
		err = impl.envConfigRepo.UpdateProperties(adapter.EnvOverrideDTOToDB(envOverride))
	}

	r := json.RawMessage{}
	err = r.UnmarshalJSON([]byte(envOverride.EnvOverrideValues))
	if err != nil {
		return nil, err
	}
	env, err := impl.environmentRepository.FindById(environmentProperties.EnvironmentId)
	if err != nil {
		return nil, err
	}
	environmentProperties = &bean.EnvironmentProperties{
		Id:                envOverride.Id,
		Status:            envOverride.Status,
		EnvOverrideValues: r,
		ManualReviewed:    envOverride.ManualReviewed,
		Active:            envOverride.Active,
		Namespace:         env.Namespace,
		EnvironmentId:     environmentProperties.EnvironmentId,
		EnvironmentName:   env.Name,
		Latest:            envOverride.Latest,
		ChartRefId:        environmentProperties.ChartRefId,
		IsOverride:        envOverride.IsOverride,
		ClusterId:         env.ClusterId,
	}
	return environmentProperties, nil
}

func (impl *PropertiesConfigServiceImpl) ChangeChartRefForEnvConfigOverride(ctx context.Context, request *bean5.ChartRefChangeRequest, userId int32, token string) (*bean.EnvironmentUpdateResponse, error) {
	newCtx, span := otel.Tracer("orchestrator").Start(ctx, "PropertiesConfigServiceImpl.ChangeChartRefForEnvConfigOverride")
	defer span.End()
	envConfigPropertiesOld, err := impl.FetchEnvProperties(request.AppId, request.EnvId, request.TargetChartRefId)
	if err != nil && !errors2.Is(err, pg.ErrNoRows) {
		impl.logger.Errorw("service err, ChangeChartRef", "err", err, "payload", request)
		return nil, fmt.Errorf("could not fetch env properties. error: %v", err)
	} else if errors2.Is(err, pg.ErrNoRows) {
		createResp, err := impl.createEnvConfigOverrideWithChart(newCtx, request, userId)
		if err != nil {
			impl.logger.Errorw("service err, ChangeChartRef", "err", err, "payload", request)
			return nil, err
		}
		return createResp, nil
	}
	envConfigProperties := request.EnvConfigProperties
	envConfigProperties.Id = envConfigPropertiesOld.Id
	createResp, err := impl.UpdateEnvironmentProperties(request.AppId, request.EnvId, envConfigProperties, token, userId)
	if err != nil {
		impl.logger.Errorw("service err, EnvConfigOverrideUpdate", "err", err, "payload", envConfigProperties)
		return nil, fmt.Errorf("could not update env override, error: %v", err)
	}
	return createResp, nil
}

func (impl *PropertiesConfigServiceImpl) createEnvConfigOverrideWithChart(ctx context.Context, request *bean5.ChartRefChangeRequest, userId int32) (*bean.EnvironmentUpdateResponse, error) {
	newCtx, span := otel.Tracer("orchestrator").Start(ctx, "PropertiesConfigServiceImpl.createEnvConfigOverrideWithChart")
	defer span.End()
	createResp, err := impl.CreateEnvironmentProperties(newCtx, request.AppId, request.EnvConfigProperties)
	if err != nil && err.Error() != bean7.NOCHARTEXIST {
		impl.logger.Errorw("service err, EnvConfigOverrideCreate", "err", err, "payload", request)
		return nil, fmt.Errorf("could not create env override, error: %v", err)
	} else if err != nil && err.Error() == bean7.NOCHARTEXIST {
		appMetrics := false
		if request.EnvConfigProperties.AppMetrics != nil {
			appMetrics = request.EnvMetrics
		}
		templateRequest := bean5.TemplateRequest{
			AppId:               request.AppId,
			ChartRefId:          request.TargetChartRefId,
			ValuesOverride:      globalUtil.GetEmptyJSON(),
			UserId:              userId,
			IsAppMetricsEnabled: appMetrics,
		}
		_, err := impl.chartService.CreateChartFromEnvOverride(newCtx, templateRequest)
		if err != nil {
			impl.logger.Errorw("service err, CreateChartFromEnvOverride", "err", err, "payload", request)
			return nil, fmt.Errorf("could not create chart from env override, error: %v", err)
		}
		return impl.CreateEnvironmentProperties(newCtx, request.AppId, request.EnvConfigProperties)
	}
	return createResp, nil
}
