/*
 * Copyright (c) 2020-2024. Devtron Inc.
 */

package repository

import (
	"github.com/go-pg/pg"
)

type TeamRepositoryEnt interface {
	FindAllActiveTeamIds() ([]int, error)
	FindByNames(teams []string) ([]*Team, error)
}

func (impl TeamRepositoryImpl) FindAllActiveTeamIds() ([]int, error) {
	var teamIds []int
	err := impl.dbConnection.Model((*Team)(nil)).Column("id").
		Where("active = ?", true).Select(&teamIds)
	return teamIds, err
}

func (impl TeamRepositoryImpl) FindByNames(teams []string) ([]*Team, error) {
	var objects []*Team
	err := impl.dbConnection.Model(&objects).Where("active = ?", true).Where("name in (?)", pg.In(teams)).Select()
	return objects, err
}
