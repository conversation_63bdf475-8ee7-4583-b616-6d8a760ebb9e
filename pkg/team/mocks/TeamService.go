// Code generated by mockery v2.43.0. DO NOT EDIT.

package mocks

import (
	team "github.com/devtron-labs/devtron/pkg/team"
	mock "github.com/stretchr/testify/mock"
)

// TeamService is an autogenerated mock type for the TeamService type
type TeamService struct {
	mock.Mock
}

// Create provides a mock function with given fields: request
func (_m *TeamService) Create(request *team.TeamRequest) (*team.TeamRequest, error) {
	ret := _m.Called(request)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 *team.TeamRequest
	var r1 error
	if rf, ok := ret.Get(0).(func(*team.TeamRequest) (*team.TeamRequest, error)); ok {
		return rf(request)
	}
	if rf, ok := ret.Get(0).(func(*team.TeamRequest) *team.TeamRequest); ok {
		r0 = rf(request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*team.TeamRequest)
		}
	}

	if rf, ok := ret.Get(1).(func(*team.TeamRequest) error); ok {
		r1 = rf(request)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Delete provides a mock function with given fields: request
func (_m *TeamService) Delete(request *team.TeamRequest) error {
	ret := _m.Called(request)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*team.TeamRequest) error); ok {
		r0 = rf(request)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// FetchAllActive provides a mock function with given fields:
func (_m *TeamService) FetchAllActive() ([]team.TeamRequest, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for FetchAllActive")
	}

	var r0 []team.TeamRequest
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]team.TeamRequest, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []team.TeamRequest); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]team.TeamRequest)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchForAutocomplete provides a mock function with given fields:
func (_m *TeamService) FetchForAutocomplete() ([]team.TeamRequest, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for FetchForAutocomplete")
	}

	var r0 []team.TeamRequest
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]team.TeamRequest, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []team.TeamRequest); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]team.TeamRequest)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchOne provides a mock function with given fields: id
func (_m *TeamService) FetchOne(id int) (*team.TeamRequest, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for FetchOne")
	}

	var r0 *team.TeamRequest
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*team.TeamRequest, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) *team.TeamRequest); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*team.TeamRequest)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAllActiveTeamIds provides a mock function with given fields:
func (_m *TeamService) FindAllActiveTeamIds() ([]int, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for FindAllActiveTeamIds")
	}

	var r0 []int
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]int, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []int); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByIds provides a mock function with given fields: ids
func (_m *TeamService) FindByIds(ids []*int) ([]*team.TeamBean, error) {
	ret := _m.Called(ids)

	if len(ret) == 0 {
		panic("no return value specified for FindByIds")
	}

	var r0 []*team.TeamBean
	var r1 error
	if rf, ok := ret.Get(0).(func([]*int) ([]*team.TeamBean, error)); ok {
		return rf(ids)
	}
	if rf, ok := ret.Get(0).(func([]*int) []*team.TeamBean); ok {
		r0 = rf(ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*team.TeamBean)
		}
	}

	if rf, ok := ret.Get(1).(func([]*int) error); ok {
		r1 = rf(ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByTeamName provides a mock function with given fields: teamName
func (_m *TeamService) FindByTeamName(teamName string) (*team.TeamRequest, error) {
	ret := _m.Called(teamName)

	if len(ret) == 0 {
		panic("no return value specified for FindByTeamName")
	}

	var r0 *team.TeamRequest
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*team.TeamRequest, error)); ok {
		return rf(teamName)
	}
	if rf, ok := ret.Get(0).(func(string) *team.TeamRequest); ok {
		r0 = rf(teamName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*team.TeamRequest)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(teamName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByTeamNames provides a mock function with given fields: teamNames
func (_m *TeamService) FindByTeamNames(teamNames []string) ([]*team.Team, error) {
	ret := _m.Called(teamNames)

	if len(ret) == 0 {
		panic("no return value specified for FindByTeamNames")
	}

	var r0 []*team.Team
	var r1 error
	if rf, ok := ret.Get(0).(func([]string) ([]*team.Team, error)); ok {
		return rf(teamNames)
	}
	if rf, ok := ret.Get(0).(func([]string) []*team.Team); ok {
		r0 = rf(teamNames)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*team.Team)
		}
	}

	if rf, ok := ret.Get(1).(func([]string) error); ok {
		r1 = rf(teamNames)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Update provides a mock function with given fields: request
func (_m *TeamService) Update(request *team.TeamRequest) (*team.TeamRequest, error) {
	ret := _m.Called(request)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 *team.TeamRequest
	var r1 error
	if rf, ok := ret.Get(0).(func(*team.TeamRequest) (*team.TeamRequest, error)); ok {
		return rf(request)
	}
	if rf, ok := ret.Get(0).(func(*team.TeamRequest) *team.TeamRequest); ok {
		r0 = rf(request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*team.TeamRequest)
		}
	}

	if rf, ok := ret.Get(1).(func(*team.TeamRequest) error); ok {
		r1 = rf(request)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewTeamService creates a new instance of TeamService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTeamService(t interface {
	mock.TestingT
	Cleanup(func())
}) *TeamService {
	mock := &TeamService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
