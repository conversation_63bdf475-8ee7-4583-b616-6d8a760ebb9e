/*
 * Copyright (c) 2024. Devtron Inc.
 */

package devtronResource

import (
	"context"
	"encoding/json"
	"fmt"
	bean3 "github.com/devtron-labs/devtron/api/bean"
	apiBean "github.com/devtron-labs/devtron/api/devtronResource/bean"
	bean5 "github.com/devtron-labs/devtron/client/argocdServer/bean"
	repository2 "github.com/devtron-labs/devtron/internal/sql/repository"
	appRepository "github.com/devtron-labs/devtron/internal/sql/repository/app"
	"github.com/devtron-labs/devtron/internal/sql/repository/appWorkflow"
	helper2 "github.com/devtron-labs/devtron/internal/sql/repository/helper"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig/bean/workflow/cdWorkflow"
	"github.com/devtron-labs/devtron/internal/util"
	userBean "github.com/devtron-labs/devtron/pkg/auth/user/bean"
	serviceBean "github.com/devtron-labs/devtron/pkg/bean"
	"github.com/devtron-labs/devtron/pkg/bean/common/patchQuery"
	bean6 "github.com/devtron-labs/devtron/pkg/deployment/common/bean"
	"github.com/devtron-labs/devtron/pkg/devtronResource/adapter"
	"github.com/devtron-labs/devtron/pkg/devtronResource/bean"
	"github.com/devtron-labs/devtron/pkg/devtronResource/helper"
	"github.com/devtron-labs/devtron/pkg/devtronResource/repository"
	adapter5 "github.com/devtron-labs/devtron/pkg/devtronResource/repository/adapter"
	bean9 "github.com/devtron-labs/devtron/pkg/devtronResource/repository/bean"
	util3 "github.com/devtron-labs/devtron/pkg/devtronResource/util"
	pipelineStageBean "github.com/devtron-labs/devtron/pkg/pipeline/bean"
	bean4 "github.com/devtron-labs/devtron/pkg/policyGovernance/devtronResource/release/bean"
	"github.com/devtron-labs/devtron/pkg/sql"
	util2 "github.com/devtron-labs/devtron/util"
	"github.com/go-pg/pg"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
	"golang.org/x/exp/maps"
	"golang.org/x/exp/slices"
	"math"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"
)

func (impl *DevtronResourceServiceImpl) handleReleaseDependencyUpdateRequest(req *bean.DtResObjInternalBean,
	existingObj *repository.DevtronResourceObject) {
	//getting dependencies of existingObj
	existingDependencies, err := impl.getDepsInternalBeanInObjectDataFromJsonString(existingObj.DevtronResourceSchemaId, existingObj.ObjectData, false)
	if err != nil {
		impl.logger.Errorw("error, getDependenciesInObjectDataFromJsonString", "err", err, "existingObj", existingObj)
		//TODO: handler error
		return
	}
	//we need to get parent dependency of release from existing list and add it to update req
	//and config of dependencies already present since FE does not send them in the request

	mapOfExistingDeps := make(map[string]int) //map of "id-schemaId" and index of dependency
	existingDepParentTypeIndex := 0           //index of parent type dependency in existing dependencies
	var reqDependenciesMaxIndex float64

	for i, dep := range existingDependencies {
		if dep.TypeOfDependency == bean.DevtronResourceDependencyTypeParent {
			existingDepParentTypeIndex = i
		}
		if dep.TypeOfDependency != bean.DevtronResourceDependencyTypeLevel { //not including level since we will be relying on new levels in request
			mapOfExistingDeps[helper.GetKeyForADependencyMap(dep.Id, dep.DevtronResourceSchemaId)] = i
		}
	}

	//updating config
	for i := range req.Dependencies {
		dep := req.Dependencies[i]
		reqDependenciesMaxIndex = math.Max(reqDependenciesMaxIndex, float64(dep.Index))
		if existingDepIndex, ok := mapOfExistingDeps[helper.GetKeyForADependencyMap(dep.Id, dep.DevtronResourceSchemaId)]; ok {
			//get config from existing dep and update in request dep config
			dep.Config = existingDependencies[existingDepIndex].Config
			if dep.Config != nil && dep.Config.ArtifactConfig != nil && dep.Config.ArtifactConfig.ArtifactId > 0 {
				dep.ChildInheritance = []*bean.ChildInheritance{{ResourceId: impl.dtResReadService.GetDtResourcesByKindMap()[bean.DevtronResourceCdPipeline.ToString()].Id, Selector: adapter.GetDefaultCdPipelineSelector()}}
			}
		}
	}
	//adding parent config in request dependencies
	existingParentDep := existingDependencies[existingDepParentTypeIndex]
	existingParentDep.Index = int(reqDependenciesMaxIndex + 1) //updating index of parent index
	req.Dependencies = append(req.Dependencies, existingParentDep)
	marshaledDependencies, err := json.Marshal(req.Dependencies)
	if err != nil {
		impl.logger.Errorw("error in marshaling dependencies", "err", err, "request", req)
		//TODO: handle error
		return
	}
	req.ObjectData = string(marshaledDependencies)
}

func (impl *DevtronResourceServiceImpl) updateReleaseConfigStatusForGetApiResourceObj(resourceSchema *repository.DevtronResourceSchema,
	existingResourceObject *repository.DevtronResourceObject, resourceObject *bean.DtResObjGetAPIBean, internalDescriptorBean *bean.DtResObjectInternalDescBean) (err error) {
	//checking if resource object exists
	if existingResourceObject != nil && existingResourceObject.Id > 0 {
		//getting metadata out of this object
		internalDescriptorBean.Id, _ = helper.GetResourceObjectIdAndType(existingResourceObject)
		internalDescriptorBean.Name = gjson.Get(existingResourceObject.ObjectData, bean.ResourceObjectNamePath).String()
		status, exist := getReleaseStatusFromObjectData(existingResourceObject.ObjectData)
		if exist {
			resourceObject.ReleaseStatus = &bean.ReleaseStatusApiBean{
				Status:   status,
				Comment:  gjson.Get(existingResourceObject.ObjectData, bean.ReleaseResourceConfigStatusCommentPath).String(),
				IsLocked: gjson.Get(existingResourceObject.ObjectData, bean.ReleaseResourceConfigStatusIsLockedPath).Bool(),
			}
		}
	}
	return nil
}

func getReleaseStatusFromObjectData(objectData string) (bean.ReleaseStatus, bool) {
	var status bean.ReleaseStatus
	exist := gjson.Get(objectData, bean.ReleaseResourceConfigStatusPath).Exists()
	if exist {
		configStatus := bean.ReleaseConfigStatus(gjson.Get(objectData, bean.ReleaseResourceConfigStatusStatusPath).String())
		rolloutStatus := bean.ReleaseRolloutStatus(gjson.Get(objectData, bean.ReleaseResourceRolloutStatusPath).String())
		switch configStatus {
		case bean.DraftReleaseConfigStatus:
			status = bean.DraftReleaseStatus
		case bean.HoldReleaseConfigStatus:
			status = bean.HoldReleaseStatus
		case bean.RescindReleaseConfigStatus:
			status = bean.RescindReleaseStatus
		case bean.CorruptedReleaseConfigStatus:
			status = bean.CorruptedReleaseStatus
		case bean.ReadyForReleaseConfigStatus:
			switch rolloutStatus {
			case bean.PartiallyDeployedReleaseRolloutStatus:
				status = bean.PartiallyReleasedReleaseStatus
			case bean.CompletelyDeployedReleaseRolloutStatus:
				status = bean.CompletelyReleasedReleaseRolloutStatus
			default:
				status = bean.ReadyForReleaseStatus
			}
		default:
			status = bean.CorruptedReleaseStatus
		}

	}
	return status, exist
}

func (impl *DevtronResourceServiceImpl) updateReleaseNoteForGetApiResourceObj(resourceSchema *repository.DevtronResourceSchema,
	existingResourceObject *repository.DevtronResourceObject, resourceObject *bean.DtResObjGetAPIBean, internalDescriptorBean *bean.DtResObjectInternalDescBean) (err error) {
	//checking if resource object exists
	if existingResourceObject != nil && existingResourceObject.Id > 0 {
		//getting metadata out of this object
		internalDescriptorBean.Id, _ = helper.GetResourceObjectIdAndType(existingResourceObject)
		internalDescriptorBean.Name = gjson.Get(existingResourceObject.ObjectData, bean.ResourceObjectNamePath).String()
		if gjson.Get(existingResourceObject.ObjectData, bean.ReleaseResourceObjectReleaseNotePath).Exists() {
			resourceObject.Overview.Note = &bean.NoteBean{
				Value: gjson.Get(existingResourceObject.ObjectData, bean.ReleaseResourceObjectReleaseNotePath).String(),
			}
			var audit *repository.DevtronResourceObjectAudit
			var err error
			audit, err = impl.dtResObjAuditRepository.FindLatestAuditByOpPath(existingResourceObject.Id, bean.ReleaseResourceObjectReleaseNotePath)
			if err != nil || audit == nil || audit.Id == 0 {
				impl.logger.Warnw("error in getting audit by path", "err", err, "objectId", existingResourceObject.Id, "path", bean.ReleaseResourceObjectReleaseNotePath)
				//it might be possible that if audit is not found then these field's data is populated through clone action, getting its audit
				audit, err = impl.dtResObjAuditRepository.FindLatestAuditByOpType(existingResourceObject.Id, repository.AuditOperationTypeClone)
				if err != nil {
					impl.logger.Errorw("error in getting audit by type", "err", err, "objectId", existingResourceObject.Id, "opType", repository.AuditOperationTypeClone)
				}
			}
			if audit != nil && audit.Id >= 0 {
				userSchema, err := impl.dtResReadService.GetUserSchemaDataById(audit.UpdatedBy)
				if err != nil {
					impl.logger.Errorw("error in getting user schema, updateReleaseNoteInResourceObj", "err", err, "userId", audit.UpdatedBy)
				} else if userSchema == nil {
					userSchema = &bean.UserSchema{} //setting not null since possible that updatedBy user is deleted and could not be found now which can break UI
				}
				resourceObject.Overview.Note.UpdatedOn = audit.UpdatedOn
				resourceObject.Overview.Note.UpdatedBy = userSchema
			}
		}
	}
	return nil
}

func (impl *DevtronResourceServiceImpl) updateDepStatusForGetApiResourceObj(resourceSchema *repository.DevtronResourceSchema,
	existingResourceObject *repository.DevtronResourceObject, resourceObject *bean.DtResObjGetAPIBean, internalDescriptorBean *bean.DtResObjectInternalDescBean) (err error) {
	//checking if resource object exists
	if existingResourceObject != nil && existingResourceObject.Id > 0 {
		artifactStatus := adapter.GetReleasePolicyDepArtifactStatusFromResObjData(existingResourceObject.ObjectData)
		depLen := adapter.GetReleaseDepLenFromResObjData(existingResourceObject.ObjectData)
		isAllImagesSelected := artifactStatus == bean4.PolicyDependencyArtifactStatusAllSelected
		isDepPresent := depLen > 0
		resourceObject.DtResObjBasicDataBean.DepStatus = adapter.BuildDepStatus(isAllImagesSelected, isDepPresent)
	}
	return nil
}

func (impl *DevtronResourceServiceImpl) updateTargetForGetApiResObj(resourceSchema *repository.DevtronResourceSchema,
	existingResourceObject *repository.DevtronResourceObject, resourceObject *bean.DtResObjGetAPIBean, internalDescriptorBean *bean.DtResObjectInternalDescBean) (err error) {
	if existingResourceObject != nil && existingResourceObject.Id > 0 {
		targetKeyResult := gjson.Get(existingResourceObject.ObjectData, bean.ReleaseTargetPathKey)
		if targetKeyResult.Exists() {
			targetString := gjson.Get(existingResourceObject.ObjectData, bean.ReleaseTargetPathKey).String()
			var targetSchema bean.TargetSchema
			err := json.Unmarshal([]byte(targetString), &targetSchema)
			if err != nil {
				impl.logger.Errorw("error in unmarshalling targte schema", "targetString", targetString, "err", err)
				return err
			}
			if targetSchema.AreAllTargeted {
				resourceObject.DtResObjBasicDataBean.Target = adapter.BuildTarget(true, nil)
			} else {
				// assumption is target is right now installations
				installationIds := make([]int, len(targetSchema.Entities))
				for i, installationEntity := range targetSchema.Entities {
					installationIds[i] = installationEntity.Id
				}
				// will fetch installation identifiers from ids and also if any release as been triggered in that installation.
				installationIdentifiers, err := impl.dtResObjectRepository.GetIdentifiersByIds(installationIds)
				if err != nil {
					impl.logger.Errorw("error in updateTargetForGetApiResObj", "installationIds", installationIds, "err", err)
					return err
				}
				mapOfInstallationIdentifierVsIsReleased, err := impl.getMapOfInstallationIdentifierVsTriggeredForRelease(adapter.BuildIdAndSchemaId(existingResourceObject.Id, existingResourceObject.DevtronResourceSchemaId), nil)
				if err != nil {
					impl.logger.Errorw("error in updateTargetForGetApiResObj", "err", err)
					return err
				}
				missingIdentifiers, err := impl.handleMissingReleasedInstallationIfNotPresent(mapOfInstallationIdentifierVsIsReleased, installationIdentifiers)
				if err != nil {
					impl.logger.Errorw("error in updateTargetForGetApiResObj", "err", err)
					return err
				}
				finalIdentifiers := append(installationIdentifiers, missingIdentifiers...)
				if len(missingIdentifiers) > 0 {
					// we patch released installation only when missing found, in this case not updating status, will be updated automatically.
					_, err = impl.patchTargetAndSaveObj(finalIdentifiers, adapter.BuildIdAndSchemaId(existingResourceObject.Id, existingResourceObject.DevtronResourceSchemaId))
					if err != nil {
						impl.logger.Errorw("error in updateTargetForGetApiResObj", "finalIdentifiers", finalIdentifiers, "err", err)
						return err
					}
				}

				resourceObject.DtResObjBasicDataBean.Target = adapter.BuildTarget(false, adapter.BuildTargetInstallation(finalIdentifiers))
			}
		} else {
			// if key does not exist(old data) we set areAllTargeted to true
			resourceObject.DtResObjBasicDataBean.Target = adapter.BuildTarget(true, nil)
		}
	} else {
		return util.GetApiErrorAdapter(http.StatusNotFound, "404", bean.ResourceObjectNotFound, bean.ResourceObjectNotFound)
	}
	return nil
}

func findMissingReleasedInstallations(mapOfInstallationIdentifierVsIsReleased map[string]bool, installationIdentifiers []string) []string {
	missingInstallations := make([]string, 0, 0)
	for identifier, released := range mapOfInstallationIdentifierVsIsReleased {
		if released && !slices.Contains(installationIdentifiers, identifier) {
			missingInstallations = append(missingInstallations, identifier)
		}
	}
	return missingInstallations
}

func (impl *DevtronResourceServiceImpl) handleMissingReleasedInstallationIfNotPresent(mapOfInstallationIdentifierVsIsReleased map[string]bool, installationIdentifiers []string) ([]string, error) {
	// this function is used to patch the missing installations in the release object
	// if the installation is not present in the release object, then we will add it to the release object
	// and update the release object in the database
	// this function is called after getting the missing installations from the map of installation identifier vs triggered for release
	// we will get all the installations from the database and then check if the installation is present in the map or not
	// if not present, then we will add it to the release object and update the release object in the database
	missingInstallationIdentifier := findMissingReleasedInstallations(mapOfInstallationIdentifierVsIsReleased, installationIdentifiers)
	finalIdentifiers := make([]string, 0, len(missingInstallationIdentifier))
	if len(missingInstallationIdentifier) > 0 {
		installationSchema, err := impl.dtResReadService.GetInstallationSchema()
		if err != nil {
			impl.logger.Errorw("error in getting installation schema", "err", err)
			return nil, err
		}
		// find the installation objects as there can be deleted objs as well
		dtResObjs, err := impl.dtResObjectRepository.FindByIdentifiersAndSchemaId(missingInstallationIdentifier, installationSchema.Id)
		if err != nil {
			impl.logger.Errorw("error in getting installation ids", "missingInstallationIdentifier", missingInstallationIdentifier, "installationSchemaId", installationSchema.Id, "err", err)
			return nil, err
		}
		if len(dtResObjs) > 0 {
			for _, dtResObj := range dtResObjs {
				finalIdentifiers = append(finalIdentifiers, dtResObj.Identifier)
			}
		}
	}
	return finalIdentifiers, nil
}

func (impl *DevtronResourceServiceImpl) getReleaseOverviewForDtResObj(existingObject *repository.DevtronResourceObject) (*bean.DtResObjGetAPIBean, error) {
	releaseData := &bean.DtResObjGetAPIBean{
		DtResObjDescApiBean:   &bean.DtResObjDescApiBean{},
		DtResObjBasicDataBean: &bean.DtResObjBasicDataBean{},
	}
	childInternalDescriptorBean := adapter.NewDtResObjectInternalDescBean()
	childInternalDescriptorBean.SchemaId = existingObject.DevtronResourceSchemaId
	//get schema
	resourceSchema := impl.dtResReadService.GetDtResourcesSchemaByIdMap()[existingObject.DevtronResourceSchemaId]
	err := impl.updateReleaseOverviewDataForGetApiResourceObj(resourceSchema, existingObject, releaseData, childInternalDescriptorBean)
	if err != nil {
		impl.logger.Errorw("error in getting overview data", "err", err)
		return nil, err
	}

	releaseData.DtResObjDescApiBean = adapter.BuildDtDescBeanFromInternalBean(childInternalDescriptorBean)
	return releaseData, nil

}

func (impl *DevtronResourceServiceImpl) updateReleaseOverviewDataForGetApiResourceObj(resourceSchema *repository.DevtronResourceSchema,
	existingResourceObject *repository.DevtronResourceObject, resourceObject *bean.DtResObjGetAPIBean, internalDescriptorBean *bean.DtResObjectInternalDescBean) (err error) {
	//checking if resource object exists
	if existingResourceObject != nil && existingResourceObject.Id > 0 {
		//getting metadata out of this object
		internalDescriptorBean.Id, internalDescriptorBean.IdType = helper.GetResourceObjectIdAndType(existingResourceObject)
		internalDescriptorBean.Name = gjson.Get(existingResourceObject.ObjectData, bean.ResourceObjectNamePath).String()
		if gjson.Get(existingResourceObject.ObjectData, bean.ResourceObjectOverviewPath).Exists() {
			resourceObject.Overview = &bean.ResOverview{
				Description:    gjson.Get(existingResourceObject.ObjectData, bean.ResourceObjectDescriptionPath).String(),
				ReleaseVersion: gjson.Get(existingResourceObject.ObjectData, bean.ReleaseResourceObjectReleaseVersionPath).String(),
				CreatedBy: &bean.UserSchema{
					Id:   int32(gjson.Get(existingResourceObject.ObjectData, bean.ResourceObjectCreatedByIdPath).Int()),
					Name: gjson.Get(existingResourceObject.ObjectData, bean.ResourceObjectCreatedByNamePath).String(),
					Icon: gjson.Get(existingResourceObject.ObjectData, bean.ResourceObjectCreatedByIconPath).Bool(),
				},
				FirstReleasedOn: gjson.Get(existingResourceObject.ObjectData, bean.ReleaseResourceObjectFirstReleasedOnPath).Time(),
			}
			resourceObject.Overview.CreatedOn, err = helper.GetCreatedOnTime(existingResourceObject.ObjectData)
			if err != nil {
				impl.logger.Errorw("error in time conversion", "err", err)
				return err
			}
			resourceObject.Overview.Tags = helper.GetOverviewTags(existingResourceObject.ObjectData)
		}
	}
	// get parent config data for overview component
	err = impl.updateParentConfigInResourceObj(resourceSchema, existingResourceObject, resourceObject, internalDescriptorBean)
	if err != nil {
		impl.logger.Errorw("error in getting note", "err", err)
		return err
	}
	return nil
}

func (impl *DevtronResourceServiceImpl) updateCompleteReleaseDataForGetApiResourceObj(resourceSchema *repository.DevtronResourceSchema,
	existingResourceObject *repository.DevtronResourceObject, resourceObject *bean.DtResObjGetAPIBean, internalDescriptorBean *bean.DtResObjectInternalDescBean) (err error) {
	err = impl.updateReleaseOverviewDataForGetApiResourceObj(resourceSchema, existingResourceObject, resourceObject, internalDescriptorBean)
	if err != nil {
		impl.logger.Errorw("error in getting overview data", "err", err)
		return err
	}
	err = impl.updateReleaseConfigStatusForGetApiResourceObj(resourceSchema, existingResourceObject, resourceObject, internalDescriptorBean)
	if err != nil {
		impl.logger.Errorw("error in getting config status data", "err", err)
		return err
	}
	err = impl.updateReleaseNoteForGetApiResourceObj(resourceSchema, existingResourceObject, resourceObject, internalDescriptorBean)
	if err != nil {
		impl.logger.Errorw("error in getting note", "err", err)
		return err
	}
	err = impl.updateCatalogDataForGetApiResObj(resourceSchema, existingResourceObject, resourceObject, internalDescriptorBean)
	if err != nil {
		impl.logger.Errorw("error in getting catalogue data", "err", err)
		return err
	}
	err = impl.updateDepStatusForGetApiResourceObj(resourceSchema, existingResourceObject, resourceObject, internalDescriptorBean)
	if err != nil {
		impl.logger.Errorw("error in getting dep status data", "err", err)
		return err
	}
	err = impl.updateTargetForGetApiResObj(resourceSchema, existingResourceObject, resourceObject, internalDescriptorBean)
	if err != nil {
		impl.logger.Errorw("error in getting target data", "err", err)
		return err
	}
	return nil
}

func (impl *DevtronResourceServiceImpl) updateOverviewAndConfigStatusDataForGetApiResourceObj(resourceSchema *repository.DevtronResourceSchema,
	existingResourceObject *repository.DevtronResourceObject, resourceObject *bean.DtResObjGetAPIBean, internalDescriptorBean *bean.DtResObjectInternalDescBean) (err error) {
	err = impl.updateReleaseOverviewDataForGetApiResourceObj(resourceSchema, existingResourceObject, resourceObject, internalDescriptorBean)
	if err != nil {
		impl.logger.Errorw("error in getting overview data", "err", err)
		return err
	}
	err = impl.updateReleaseConfigStatusForGetApiResourceObj(resourceSchema, existingResourceObject, resourceObject, internalDescriptorBean)
	if err != nil {
		impl.logger.Errorw("error in getting config status data", "err", err)
		return err
	}
	return nil
}

func validateCreateReleaseRequest(reqBean *bean.DtResObjCreateReqBean) error {
	if reqBean.Overview != nil {
		err := helper.CheckIfReleaseVersionIsValid(reqBean.Overview.ReleaseVersion)
		if err != nil {
			return err
		}
	}
	if reqBean.ParentConfig == nil {
		return util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.ResourceParentConfigNotFound, bean.ResourceParentConfigNotFound)
	}
	if reqBean.ParentConfig.Id == 0 && len(reqBean.ParentConfig.Identifier) == 0 {
		return util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidResourceParentConfigData, bean.InvalidResourceParentConfigData)
	}
	if reqBean.ParentConfig.ResourceKind != bean.DevtronResourceReleaseTrack {
		return util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidResourceParentConfigKind, bean.InvalidResourceParentConfigKind)
	}
	return nil
}

func (impl *DevtronResourceServiceImpl) populateDefaultValuesForCreateReleaseRequest(reqBean *bean.DtResObjCreateReqBean, userId int32) (err error) {
	if reqBean.Overview != nil && reqBean.Overview.CreatedBy == nil {
		createdByDetails, err := impl.dtResReadService.GetUserSchemaDataById(userId)
		// considering the user details are already verified; this error indicates to an internal db error.
		if err != nil {
			impl.logger.Errorw("error encountered in populateDefaultValuesForCreateReleaseRequest", "userId", userId, "err", err)
			return err
		}
		reqBean.Overview.CreatedBy = createdByDetails
		reqBean.Overview.CreatedOn = time.Now()
	}
	if len(reqBean.Name) == 0 {
		// setting default name for kind release if not provided by the user
		reqBean.Name = helper.GetDefaultReleaseNameIfNotProvided(reqBean)
	}
	if len(reqBean.Identifier) == 0 {
		reqBean.Identifier, err = impl.getIdentifierForCreateReleaseRequest(reqBean.DtResObjDescApiBean, reqBean.ParentConfig, reqBean.Overview.ReleaseVersion)
		if err != nil {
			impl.logger.Errorw("error encountered in populateDefaultValuesForCreateReleaseRequest", "err", err)
			return err
		}
	}
	return nil
}

func (impl *DevtronResourceServiceImpl) getIdentifierForCreateReleaseRequest(descriptorBean *bean.DtResObjDescApiBean,
	parentConfig *bean.ResourceIdentifier, releaseVersion string) (identifier string, err error) {
	return impl.getIdentifierForReleaseByParentDescriptorBean(releaseVersion, parentConfig)
}

func (impl *DevtronResourceServiceImpl) getIdentifierForReleaseByParentDescriptorBean(releaseVersion string, parentConfig *bean.ResourceIdentifier) (string, error) {
	// identifier for release is {releaseTrackName-version}
	if len(parentConfig.Identifier) != 0 {
		// identifier for parent is same as name for release-track,
		return fmt.Sprintf("%s-%s", parentConfig.Identifier, releaseVersion), nil
	} else if (parentConfig.Id) != 0 {
		// sending user id as 0 as we are just getting data will not used.
		internalDescriptorBean := adapter.BuildDtResObjIntDescBean(adapter.BuildDtResObjCommonDescBean(parentConfig.ResourceKind.ToString(), parentConfig.ResourceSubKind.ToString(), parentConfig.ResourceVersion.ToString(), parentConfig.Id, "", ""), parentConfig.SchemaId, nil, 0)
		_, existingObject, err := impl.getResourceSchemaAndExistingObject(internalDescriptorBean)
		if err != nil {
			impl.logger.Errorw("error encountered in getIdentifierForCreateReleaseRequest", "id", parentConfig.Id, "err", err)
			return "", err
		}
		return fmt.Sprintf("%s-%s", existingObject.Identifier, releaseVersion), nil
	}
	return "", util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidParentConfigIdOrIdentifier, bean.InvalidParentConfigIdOrIdentifier)
}
func (impl *DevtronResourceServiceImpl) updateUserProvidedDataInReleaseObj(objectData string, reqBean *bean.DtResObjInternalBean) (string, error) {
	var err error
	isConfigStatusPresentInExistingObj := len(gjson.Get(objectData, bean.ReleaseResourceConfigStatusStatusPath).String()) > 0
	if reqBean.ConfigStatus == nil && !isConfigStatusPresentInExistingObj {
		reqBean.ConfigStatus = &bean.ReleaseConfigSchema{
			Status: bean.DraftReleaseConfigStatus.ToString(),
		}
		objectData, err = sjson.Set(objectData, bean.ReleaseResourceConfigStatusPath, reqBean.ConfigStatus)
		if err != nil {
			impl.logger.Errorw("error in setting id in schema", "err", err, "request", reqBean)
			return objectData, err
		}
	}
	if reqBean.Overview != nil {
		objectData, err = impl.setReleaseOverviewFieldsInObjectData(objectData, reqBean.Overview)
		if err != nil {
			impl.logger.Errorw("error in setting overview data in schema", "err", err, "request", reqBean)
			return objectData, err
		}
	}

	return objectData, nil
}

func (impl *DevtronResourceServiceImpl) addReleaseParentDepsToChildResDeps(parentResourceObject *repository.DevtronResourceObject) ([]*bean.DtResDepJsonBean, []*repository.DevtronResourceObjectDependencyRelations, float64, error) {
	// getting all dependencies of parent(release-track) to add in release
	allDeps, err := impl.getSpecificDepsInternalBeanInObjectDataFromJsonString(parentResourceObject.DevtronResourceSchemaId, parentResourceObject.ObjectData,
		[]bean.DtResDepType{bean.DevtronResourceDependencyTypeUpstream, bean.DevtronResourceDependencyTypeLevel})
	if err != nil {
		impl.logger.Errorw("error encountered in addReleaseParentDepsToChildResDeps", "err", err, "id", parentResourceObject.Id)
		return nil, nil, 0, err
	}
	appNameVsAppMap, err := impl.appCrudService.FindLatestAppWithProjectByNamesIncludingDeleted(util3.GetIdentifiersFromDepJsonForApps(allDeps))
	if err != nil {
		impl.logger.Errorw("error encountered in addReleaseParentDepsToChildResDeps", "err", err)
		return nil, nil, 0, err
	}

	allDepRelations := make([]*repository.DevtronResourceObjectDependencyRelations, 0, len(allDeps))
	var maxIndex float64
	for _, dep := range allDeps {
		// updating id in runtime as release-track do not have id propagate to handle delete cases
		if val, ok := appNameVsAppMap[dep.Identifier]; ok {
			dep.Id = val.Id
		}
		// setting this as false as release do not have child propagation
		dep.ToPropagateOnChild = false
		if dep.TypeOfDependency == bean.DevtronResourceDependencyTypeLevel {
			// setting this as true as release track order is being followed
			dep.IsLevelPropagatedFromParent = true
		}
		allDepRelations = append(allDepRelations, &repository.DevtronResourceObjectDependencyRelations{
			DependencyObjectId:      dep.Id,
			DependencyDtResSchemaId: dep.DevtronResourceSchemaId,
			TypeOfDependency:        dep.TypeOfDependency.ToString(),
		})
		maxIndex = math.Max(maxIndex, float64(dep.Index))
	}

	return allDeps, allDepRelations, maxIndex, nil
}

func (impl *DevtronResourceServiceImpl) setReleaseOverviewFieldsInObjectData(objectData string, overview *bean.ResOverview) (string, error) {
	var err error
	if overview.ReleaseVersion != "" {
		objectData, err = sjson.Set(objectData, bean.ReleaseResourceObjectReleaseVersionPath, overview.ReleaseVersion)
		if err != nil {
			impl.logger.Errorw("error in setting description in schema", "err", err, "overview", overview)
			return objectData, err
		}
	}
	if overview.Description != "" {
		objectData, err = sjson.Set(objectData, bean.ResourceObjectDescriptionPath, overview.Description)
		if err != nil {
			impl.logger.Errorw("error in setting description in schema", "err", err, "overview", overview)
			return objectData, err
		}
	}
	if overview.CreatedBy != nil && overview.CreatedBy.Id > 0 {
		objectData, err = sjson.Set(objectData, bean.ResourceObjectCreatedByPath, overview.CreatedBy)
		if err != nil {
			impl.logger.Errorw("error in setting createdBy in schema", "err", err, "overview", overview)
			return objectData, err
		}
		objectData, err = sjson.Set(objectData, bean.ResourceObjectCreatedOnPath, overview.CreatedOn)
		if err != nil {
			impl.logger.Errorw("error in setting createdOn in schema", "err", err, "overview", overview)
			return objectData, err
		}
	}
	if overview.Note != nil {
		objectData, err = sjson.Set(objectData, bean.ReleaseResourceObjectReleaseNotePath, overview.Note.Value)
		if err != nil {
			impl.logger.Errorw("error in setting description in schema", "err", err, "overview", overview)
			return objectData, err
		}
	}
	if overview.Tags != nil {
		objectData, err = sjson.Set(objectData, bean.ResourceObjectTagsPath, overview.Tags)
		if err != nil {
			impl.logger.Errorw("error in setting description in schema", "err", err, "overview", overview)
			return objectData, err
		}
	}
	return objectData, nil
}

func (impl *DevtronResourceServiceImpl) buildIdentifierForReleaseResourceObj(object *repository.DevtronResourceObject) (string, error) {
	releaseVersion := gjson.Get(object.ObjectData, bean.ReleaseResourceObjectReleaseVersionPath).String()
	_, releaseTrackObject, err := impl.getParentConfigVariablesFromDeps(object.ObjectData)
	if err != nil {
		impl.logger.Errorw("error in getting release track object", "err", err, "resourceObjectId", object.Id)
		return "", err
	}
	releaseTrackName := gjson.Get(releaseTrackObject.ObjectData, bean.ResourceObjectNamePath).String()
	return fmt.Sprintf("%s-%s", releaseTrackName, releaseVersion), nil
}

func (impl *DevtronResourceServiceImpl) listRelease(resourceObjects []*repository.DevtronResourceObject,
	resObjIdChildObjsMap map[int][]*repository.DevtronResourceObject, isLite bool, authorisedMapObjDto apiBean.GetObjListAuthorisedDtoMap, filterCriteriaCategoriseReq *bean.FilterCriteriaCategoriseReq) ([]*bean.DtResObjGetAPIBean, error) {
	//sorting release objects on basis of created time, need to be maintained from query after sort options introduction
	sort.Slice(resourceObjects, func(i, j int) bool {
		return resourceObjects[i].CreatedOn.After(resourceObjects[j].CreatedOn)
	})
	resp := make([]*bean.DtResObjGetAPIBean, 0, len(resourceObjects))
	authorisedReleases := authorisedMapObjDto[bean.DevtronResourceRelease.ToString()]
	for i := range resourceObjects {
		resourceData := adapter.BuildDtResObjGetAPIBean()
		internalDescriptorBean := adapter.NewDtResObjectInternalDescBean()
		resourceObject := resourceObjects[i]
		if _, ok := authorisedReleases[resourceObject.Id]; !ok {
			//not authorised, skipping
			continue
		}
		resourceSchema := impl.dtResReadService.GetDtResourcesSchemaByIdMap()[resourceObject.DevtronResourceSchemaId]
		if !isLite {
			err := impl.updateCompleteReleaseDataForGetApiResourceObj(resourceSchema, resourceObject, resourceData, internalDescriptorBean)
			if err != nil {
				impl.logger.Errorw("error in getting detailed resource data", "resourceObjectId", resourceObjects[i].Id, "err", err)
				return nil, err
			}
		} else {
			err := impl.updateOverviewAndConfigStatusDataForGetApiResourceObj(resourceSchema, resourceObject, resourceData, internalDescriptorBean)
			if err != nil {
				impl.logger.Errorw("error in getting overview data", "err", err)
				return nil, err
			}
		}
		resourceData.DtResObjDescApiBean = adapter.BuildDtDescBeanFromInternalBean(internalDescriptorBean)
		resp = append(resp, resourceData)
	}
	return resp, nil
}

func (impl *DevtronResourceServiceImpl) getFilteredReleaseObjectsForReleaseTrackIds(resourceObjects []*repository.DevtronResourceObject, releaseTrackIds []int) ([]*repository.DevtronResourceObject, error) {
	releaseTrackSchema, err := impl.dtResReadService.GetReleaseTrackSchema()
	if err != nil {
		impl.logger.Errorw("error in getting releaseTrack schema", "err", err)
		return nil, err
	}
	return impl.getParentFilteredObjects(releaseTrackIds, releaseTrackSchema.Id, resourceObjects)
}

// handleDeleteAppDepCreationForRelease updating old object ids for release in case app name got created , we need to update appId , handling has been done here, in future can be moved.
// also updates metadata obj or found appId
func (impl *DevtronResourceServiceImpl) handleDeleteAppDepCreationForRelease(dtResObj *repository.DevtronResourceObject, dependencies []*bean.DtResDepJsonBean, inactiveAppNames []string, mapOfAppIdVsActiveBean map[int]*bean.IdNameAndActiveBean, appMetadataObj map[int]interface{}) (err error) {
	activeAppNameVsAppMap := make(map[string]*appRepository.App)
	if len(inactiveAppNames) > 0 {
		// here we have find apps which are created with same app name which are inactive
		activeAppNameVsAppMap, err = impl.appCrudService.FindActiveAppNameVsApp(inactiveAppNames)
		if err != nil {
			impl.logger.Errorw("error encountered in handleDeleteAppDepCreationForRelease", "inactiveAppNames", inactiveAppNames, "err", err)
			return err
		}
	}
	if len(activeAppNameVsAppMap) > 0 {
		// Starting a transaction
		tx, err := impl.dtResObjDepsRelationsRepository.StartTx()
		if err != nil {
			impl.logger.Errorw("error encountered in handleDeleteAppDepCreationForRelease", "err", err)
			return err
		}
		defer impl.dtResObjDepsRelationsRepository.RollbackTx(tx)

		dtApplicationSchema, err := impl.dtResReadService.GetDtApplicationSchema()
		if err != nil {
			impl.logger.Errorw("error in handleDeleteAppDepCreationForRelease", "err", err)
			return err
		}
		status, exist := getReleaseStatusFromObjectData(dtResObj.ObjectData)
		updateConfigToNil := false
		if exist && (status == bean.DraftReleaseStatus || status == bean.ReadyForReleaseStatus) {
			updateConfigToNil = true
		}
		allRelationModels := make([]*repository.DevtronResourceObjectDependencyRelations, 0, len(dependencies))
		relationsModelsToBeUpdated := false
		for _, dep := range dependencies {
			if dep.TypeOfDependency == bean.DevtronResourceDependencyTypeUpstream && dep.DevtronResourceSchemaId == dtApplicationSchema.Id {
				activeBean := mapOfAppIdVsActiveBean[dep.Id]
				if app, ok := activeAppNameVsAppMap[activeBean.Name]; ok && !activeBean.Active {
					dep.Id = app.Id
					relationsModelsToBeUpdated = true
					// setting previous patched image and configurations to nil(requirement)
					if updateConfigToNil {
						dep.Config = nil
					}
					mapOfAppIdVsActiveBean[app.Id] = adapter.BuildIdNameAndActiveBean(app.Id, app.AppName, app.Active)
					appMetadataObj[app.Id] = &struct {
						AppName        string `json:"appName"`
						AppId          int    `json:"appId"`
						AppDescription string `json:"appDescription"`
					}{
						AppName:        app.AppName,
						AppId:          app.Id,
						AppDescription: app.Description,
					}
				}
				if dep.Config != nil && dep.Config.ArtifactConfig != nil && dep.Config.ArtifactConfig.ArtifactId > 0 {
					dep.ChildInheritance = []*bean.ChildInheritance{{ResourceId: impl.dtResReadService.GetDtResourcesByKindMap()[bean.DevtronResourceCdPipeline.ToString()].Id, Selector: adapter.GetDefaultCdPipelineSelector()}}
				}
			}

			allRelationModels = append(allRelationModels, &repository.DevtronResourceObjectDependencyRelations{
				ComponentObjectId:       dtResObj.Id,
				ComponentDtResSchemaId:  dtResObj.DevtronResourceSchemaId,
				DependencyObjectId:      dep.Id,
				DependencyDtResSchemaId: dep.DevtronResourceSchemaId,
				TypeOfDependency:        dep.TypeOfDependency.ToString(),
				AuditLog: sql.AuditLog{
					CreatedOn: time.Now(),
					CreatedBy: 1, // as system is updating deps relation , this is a special case when an deleted app is created again
					UpdatedOn: time.Now(),
					UpdatedBy: 1, // as system is updating deps relation , this is a special case when an deleted app is created again
				},
			})
		}
		dtResObj.ObjectData, err = sjson.Set(dtResObj.ObjectData, bean.ResourceObjectDependenciesPath, dependencies)
		if err != nil {
			impl.logger.Errorw("error in handleDeleteAppDepCreationForRelease", "err", err, "dependencies", dependencies)
			return err
		}
		// system updating so user id is being sent as 1.
		dtResObj.UpdateAuditLog(1)
		err = impl.dtResObjectRepository.Update(tx, dtResObj)
		if err != nil {
			impl.logger.Errorw("error encountered in handleDeleteAppDepCreationForRelease", "err", err)
			return err
		}
		if relationsModelsToBeUpdated {
			err = impl.dtResObjDepsRelationsRepository.DeleteAllForComponentIdAndComponentSchemaId(tx, dtResObj.Id, dtResObj.DevtronResourceSchemaId)
			if err != nil {
				impl.logger.Errorw("error encountered in handleDeleteAppDepCreationForRelease", "err", err, "id", dtResObj.Id)
				return err
			}

			if len(allRelationModels) > 0 {
				err = impl.dtResObjDepsRelationsRepository.SaveInBatch(tx, allRelationModels)
				if err != nil {
					impl.logger.Errorw("error, handleDeleteAppDepCreationForRelease", "err", err)
					return err
				}
			}
		}
		// committing transaction
		err = impl.dtResObjDepsRelationsRepository.CommitTx(tx)
		if err != nil {
			impl.logger.Errorw("error in commiting transaction in handleDeleteAppDepCreationForRelease", "err", err)
			return err
		}
	}

	return nil

}

func (impl *DevtronResourceServiceImpl) getReleaseDependenciesData(resourceObject *repository.DevtronResourceObject, filterDependencyTypes []bean.DtResDepType, dependenciesInfo []string, isLite bool) ([]*bean.DtResDepBean, error) {
	dependenciesOfParent, err := impl.getDepsInternalBeanInObjectDataFromJsonString(resourceObject.DevtronResourceSchemaId, resourceObject.ObjectData, isLite)
	if err != nil {
		impl.logger.Errorw("error in getting dependencies from json object", "err", err)
		return nil, err
	}
	appIdsToGetMetadata := helper.GetDependencyOldObjectIdsForSpecificType(dependenciesOfParent, bean.DevtronResourceDependencyTypeUpstream)
	dependencyFilterKeys, err := impl.getFilterKeysFromDependenciesInfo(dependenciesInfo)
	if err != nil {
		return nil, err
	}
	appMetadataObj, mapOfAppIdAndActiveBean, inactiveAppNames, err := impl.getMapOfAppIncludingDeletedMetadata(appIdsToGetMetadata)
	if err != nil {
		return nil, err
	}
	// updating old object id(app id ) in case where app got deleted and app got created with same name, release has dep id which has to be updated.
	err = impl.handleDeleteAppDepCreationForRelease(resourceObject, dependenciesOfParent, inactiveAppNames, mapOfAppIdAndActiveBean, appMetadataObj)
	if err != nil {
		impl.logger.Errorw("error encountered in getReleaseDependenciesData", "err", err)
		return nil, err
	}
	metadataObj := &bean.DependencyMetaDataBean{
		MapOfAppsMetadata: appMetadataObj,
	}
	dependenciesWithMetaData := impl.getFilteredDepsWithMetaData(dependenciesOfParent, filterDependencyTypes, dependencyFilterKeys, metadataObj, mapOfAppIdAndActiveBean)
	return dependenciesWithMetaData, nil
}

func (impl *DevtronResourceServiceExtendedImpl) updateReleaseDependencyConfigInternalDataInObj(configDataJsonObj string, configData *bean.DepConfigInternalBean, isLite bool) error {
	if configData.DtAppDepJsonConfigBean == nil {
		configData.DtAppDepJsonConfigBean = &bean.DtAppDepJsonConfigBean{}
	}
	sourceReleaseConfig := gjson.Get(configDataJsonObj, bean.ReleaseResourceArtifactSourceReleaseConfigPath).String()
	sourceReleaseConfigObj := adapter.NewDtResObjSrcConfigInternalDescBean()
	if len(sourceReleaseConfig) != 0 {
		err := json.Unmarshal([]byte(sourceReleaseConfig), sourceReleaseConfigObj)
		if err != nil {
			impl.logger.Errorw("error encountered in un-marshaling sourceReleaseConfig", "sourceReleaseConfig", sourceReleaseConfig, "err", err)
			return err
		}
		if sourceReleaseConfigObj.Id > 0 { //it might be possible that the id is not present as dependencies creation will save empty values
			obj, err := impl.getExistingDtObj(sourceReleaseConfigObj.Id, 0, sourceReleaseConfigObj.DevtronResourceSchemaId, sourceReleaseConfigObj.Identifier)
			if err != nil {
				impl.logger.Errorw("error encountered in updateReleaseDependencyConfigInternalDataInObj", "sourceReleaseConfigObjId", sourceReleaseConfigObj.Id, "err", err)
				return err
			}
			sourceReleaseConfigObj.ReleaseVersion = gjson.Get(obj.ObjectData, bean.ReleaseResourceObjectReleaseVersionPath).String()
			sourceReleaseConfigObj.Name = gjson.Get(obj.ObjectData, bean.ResourceObjectNamePath).String()
		}
	}
	sourceAppWfId := int(gjson.Get(configDataJsonObj, bean.ReleaseResourceArtifactSourceAppWfIdPath).Int())
	artifactId := int(gjson.Get(configDataJsonObj, bean.ReleaseResourceDependencyConfigArtifactIdKey).Int())

	if !isLite {
		// getting artifact git commit data and image at runtime by artifact id instead of setting this schema, this has to be modified when commit source is also kept in schema (eg ci trigger is introduced)
		artifact, err := impl.ciArtifactRepository.Get(artifactId)
		if err != nil && err != pg.ErrNoRows {
			impl.logger.Errorw("error encountered in updateReleaseDependencyConfigInternalDataInObj", "artifactId", artifactId, "err", err)
			return err
		}
		configData.ReleaseInstruction = gjson.Get(configDataJsonObj, bean.ReleaseResourceDependencyConfigReleaseInstructionKey).String()
		configData.CiWorkflowId = int(gjson.Get(configDataJsonObj, bean.ReleaseResourceDependencyConfigCiWorkflowKey).Int())
		gitCommitData := make([]bean.GitCommitData, 0)
		if artifactId > 0 {
			materialInfo, err := artifact.GetMaterialInfo()
			if err != nil {
				impl.logger.Errorw("error encountered in updateReleaseDependencyConfigInternalDataInObj", "artifactId", artifact.Id, "err", err)
				return err
			}

			for _, material := range materialInfo {
				for _, modification := range material.Modifications {
					gitCommitData = append(gitCommitData, adapter.BuildGitCommit(modification.Author, modification.Branch, modification.Message, modification.ModifiedTime, modification.Revision, modification.Tag, adapter.BuildWebHookMaterialInfo(modification.WebhookData.Id, modification.WebhookData.EventActionType, modification.WebhookData.Data), material.Material.GitConfiguration.URL))
				}
			}
		}
		configData.ArtifactConfig = &bean.ArtifactConfigJsonBean{
			ArtifactId:          artifactId,
			Image:               artifact.Image,
			RegistryType:        gjson.Get(configDataJsonObj, bean.ReleaseResourceDependencyConfigRegistryTypeKey).String(),
			RegistryName:        gjson.Get(configDataJsonObj, bean.ReleaseResourceDependencyConfigRegistryNameKey).String(),
			SourceAppWorkflowId: sourceAppWfId,
			CommitSource:        gitCommitData,
			SourceReleaseConfig: sourceReleaseConfigObj,
		}
	} else { //adding basic artifact config data for liter for internal calls
		configData.ArtifactConfig = &bean.ArtifactConfigJsonBean{
			ArtifactId:          artifactId,
			SourceReleaseConfig: sourceReleaseConfigObj,
			SourceAppWorkflowId: sourceAppWfId,
		}
	}
	return nil
}

func (impl *DevtronResourceServiceImpl) getFilterKeysFromDependenciesInfo(dependenciesInfo []string) ([]string, error) {
	dependencyFilterKeys := make([]bean.FilterKeyObject, len(dependenciesInfo))
	for _, dependencyInfo := range dependenciesInfo {
		resourceIdentifier, err := helper.DecodeDependencyInfoString(dependencyInfo)
		if err != nil {
			return nil, err
		}
		_, dependencyResourceSchema, err := impl.dtResReadService.GetResAndSchemaFromResType(adapter.BuildDtResTypeInternalReq(resourceIdentifier.ResourceKind.ToString(),
			resourceIdentifier.ResourceSubKind.ToString(), resourceIdentifier.ResourceVersion.ToString()))
		if err != nil {
			impl.logger.Errorw("error in getFilterKeysFromDependenciesInfo", "kind", resourceIdentifier.ResourceKind, "version", resourceIdentifier.ResourceVersion, "err", err)
			return nil, err
		}
		var identifierString string
		if resourceIdentifier.Identifier != bean.AllIdentifierQueryString {
			f := getFuncToGetResIdAndIdTypeFromIdentifier(resourceIdentifier.ResourceKind.ToString(),
				resourceIdentifier.ResourceSubKind.ToString(), resourceIdentifier.ResourceVersion.ToString())
			if f == nil {
				err = util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidQueryDependencyInfo, bean.InvalidQueryDependencyInfo)
				return nil, err
			}
			id, _, err := f(impl, resourceIdentifier)
			if err != nil {
				if util.IsErrNoRows(err) {
					err = util.GetApiErrorAdapter(http.StatusNotFound, "404", bean.InvalidQueryDependencyInfo, bean.InvalidQueryDependencyInfo)
				}
				return nil, err
			}
			identifierString = strconv.Itoa(id)
		} else {
			identifierString = bean.AllIdentifierQueryString
		}
		dependencyFilterKey := helper.GetFilterKeyObjectFromId(dependencyResourceSchema.Id, identifierString)
		if !slices.Contains(dependencyFilterKeys, dependencyFilterKey) {
			dependencyFilterKeys = append(dependencyFilterKeys, dependencyFilterKey)
		}
	}
	return dependencyFilterKeys, nil
}

func getReleaseConfigOptionsFilterCriteriaData(query *apiBean.GetConfigOptionsQueryParams) (appWorkflowId int, releaseTrackFilter *bean.FilterCriteriaDecoder, err error) {
	for _, filterCriteria := range query.FilterCriteria {
		criteriaDecoder, err := util3.DecodeFilterCriteriaString(filterCriteria, false)
		if err != nil {
			return appWorkflowId, nil, err
		}
		switch criteriaDecoder.Kind {
		case bean.DevtronResourceAppWorkflow:
			if criteriaDecoder.Type != bean.IdQueryString {
				return appWorkflowId, nil, fmt.Errorf("invalid filterCriteria: AppWorkflow")
			}
			appWorkflowId, err = strconv.Atoi(criteriaDecoder.Value)
			if err != nil {
				return appWorkflowId, nil, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidFilterCriteria, bean.InvalidFilterCriteria)
			}
		case bean.DevtronResourceReleaseTrack:
			if criteriaDecoder.Type == bean.IdQueryString {
				releaseTrackId, err := strconv.Atoi(criteriaDecoder.Value)
				if err != nil {
					return appWorkflowId, nil, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidFilterCriteria, bean.InvalidFilterCriteria)
				}
				criteriaDecoder.ValueInt = releaseTrackId
			}
			releaseTrackFilter = criteriaDecoder
		default:
			return appWorkflowId, nil, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidFilterCriteria, bean.InvalidFilterCriteria)
		}
	}
	return appWorkflowId, releaseTrackFilter, nil
}

func getReleaseConfigOptionsSearchKeyData(query *apiBean.GetConfigOptionsQueryParams) (searchOption bean.SearchKeyOptions, err error) {
	searchDecoder, err := util3.DecodeSearchKeyString(query.SearchKey)
	if err != nil {
		return searchOption, err
	}
	if searchDecoder.SearchBy == bean.ArtifactTag {
		searchOption.ArtifactTag = searchDecoder.Value
	} else if searchDecoder.SearchBy == bean.ImageTag {
		searchOption.ImageTag = searchDecoder.Value
	} else if searchDecoder.SearchBy == bean.ReleaseNameOrVersion {
		searchOption.ReleaseNameOrVersion = searchDecoder.Value
	} else {
		return searchOption, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidSearchKey, bean.InvalidSearchKey)
	}
	return searchOption, nil
}

func (impl *DevtronResourceServiceImpl) updateReleaseDependencyChildObjectsInObj(dependencyString string) ([]*bean.ChildObject, error) {
	childObjects := make([]*bean.ChildObject, 0)
	oldObjectId := int(gjson.Get(dependencyString, bean.IdKey).Int())
	childInheritance, err := getChildInheritanceData(dependencyString)
	if err != nil {
		return nil, err
	}
	envs, err := impl.getEnvironmentsForApplicationDependency(childInheritance, oldObjectId)
	if err != nil {
		impl.logger.Errorw("error encountered in updateReleaseDependencyChildObjectsInObj", "id", oldObjectId, "err", err)
		return nil, err
	}
	if len(envs) > 0 {
		childObject := adapter.BuildChildObject(envs, bean.EnvironmentChildObjectType)
		childObjects = append(childObjects, childObject)
	}
	return childObjects, nil
}

func (impl *DevtronResourceServiceImpl) updateReleaseDependencyChildObjectsInObjWithMappingConfig(dependencyString string, envIdVsInstallationMappingConfig map[int]*bean.InstallationMappingConfigInternalBean, mapOfInstallationIdentifierVsIsReleased map[string]bool) ([]*bean.ChildObject, error) {
	childObjects := make([]*bean.ChildObject, 0)
	oldObjectId := int(gjson.Get(dependencyString, bean.IdKey).Int())
	childInheritance, err := getChildInheritanceData(dependencyString)
	if err != nil {
		return nil, err
	}
	envs, err := impl.getEnvironmentsWithMappingConfigForApplicationDependency(childInheritance, oldObjectId, envIdVsInstallationMappingConfig, mapOfInstallationIdentifierVsIsReleased)
	if err != nil {
		impl.logger.Errorw("error encountered in updateReleaseDependencyChildObjectsInObjWithMappingConfig", "id", oldObjectId, "err", err)
		return nil, err
	}
	if len(envs) > 0 {
		childObject := adapter.BuildChildObject(envs, bean.EnvironmentChildObjectType)
		childObjects = append(childObjects, childObject)
	}
	return childObjects, nil
}

func (impl *DevtronResourceServiceImpl) getPipelineIdVsPipelineMapAndTenantInfo(tasks []*bean.Task) (map[int]*pipelineConfig.Pipeline, error) {
	pipelineIds := make([]int, 0, len(tasks))
	for _, task := range tasks {
		pipelineIds = append(pipelineIds, task.PipelineId)
	}

	pipelineIdVsPipelineMap, err := impl.getPipelineIdPipelineMapForPipelineIds(pipelineIds)
	if err != nil {
		impl.logger.Errorw("error encountered in checkIfTenantAndInstallationExists", "pipelineIds", pipelineIds, "err", err)
		return nil, err
	}

	return pipelineIdVsPipelineMap, nil
}

// checkIfTenantAndInstallationExists handled concurrency cases where tenant or installation are deleted in parallel.
func (impl *DevtronResourceServiceImpl) checkIfTenantAndInstallationExists(req *bean.DtResTaskExecutionInternalBean, pipelineIdVsPipelineMap map[int]*pipelineConfig.Pipeline, envIdVsInstallationMappingConfig map[int]*bean.InstallationMappingConfigInternalBean, mapOfTenantInstallationIdKeyVsEnvs map[string]*bean.EnvIdsAndDeploymentConfig) error {
	if req.TaskType == bean.BundledTaskType {
		for _, task := range req.Tasks {
			tenantInstallationIdKey, err := impl.getIdentifierForInstallationByParentDescriptorBean(task.InstallationId, &bean.ResourceIdentifier{Identifier: task.TenantId})
			if err != nil {
				return err
			}
			if cfg, ok := mapOfTenantInstallationIdKeyVsEnvs[tenantInstallationIdKey]; ok {
				for _, envId := range cfg.EnvIds {
					if _, ok := envIdVsInstallationMappingConfig[envId]; !ok {
						return util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.TenantInstallationEnvMappingNotFoundMessage, bean.TenantInstallationEnvMappingNotFoundMessage)
					}
				}
			} else {
				return util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.TenantInstallationMappingNotAvailableMessage, bean.TenantInstallationMappingNotAvailableMessage)
			}
		}
	} else {
		for _, task := range req.Tasks {
			pipeline := pipelineIdVsPipelineMap[task.PipelineId]
			if pipeline == nil {
				return util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.PipelineNotAvailableMessage, bean.PipelineNotAvailableMessage)
			}
			envId := pipeline.EnvironmentId
			if _, ok := envIdVsInstallationMappingConfig[envId]; !ok {
				return util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.TenantInstallationEnvMappingNotFoundMessage, bean.TenantInstallationEnvMappingNotFoundMessage)
			}
		}
	}
	return nil

}

func (impl *DevtronResourceServiceExtendedImpl) updateArtifactIdAndReturnIdsForDependencies(devtronResourceSchemaId int, objectData string, appVsArtifactIdMap map[int]int) ([]int, map[int]*bean.DepDetail, map[int]*bean.DtResDepJsonBean, error) {
	dependenciesResult := gjson.Get(objectData, bean.ResourceObjectDependenciesPath)
	dependenciesResultArr := dependenciesResult.Array()
	artifactsIds := make([]int, 0, len(dependenciesResultArr))
	appIdVsDrSchemaDetail := make(map[int]*bean.DepDetail)
	appIdVsDepBean := make(map[int]*bean.DtResDepJsonBean)
	kind, subKind, version, err := helper.GetKindSubKindAndVersionOfResourceBySchemaId(devtronResourceSchemaId,
		impl.dtResReadService.GetDtResourcesSchemaByIdMap(), impl.dtResReadService.GetDtResourcesByIdMap())
	if err != nil {
		return artifactsIds, appIdVsDrSchemaDetail, nil, err
	}
	parentResourceType := &bean.DtResTypeReq{
		ResourceKind:    bean.DtResKind(kind),
		ResourceSubKind: bean.DtResKind(subKind),
		ResourceVersion: bean.DtResVersion(version),
	}
	for _, dependencyResult := range dependenciesResultArr {
		dependencyBean, err := impl.getDepInternalBeanFromJsonString(parentResourceType, dependencyResult.String(), false)
		if err != nil {
			return artifactsIds, appIdVsDrSchemaDetail, nil, err
		}
		// currently checking schema id to be of kind devtron-applicationas we create images in application only currently.
		if helper.IsApplicationDependency(dependencyBean.DtResTypeInternalReq) {
			appIdVsDepBean[dependencyBean.Id] = dependencyBean
			//updating artifact id here
			appVsArtifactIdMap[dependencyBean.Id] = dependencyBean.Config.ArtifactConfig.ArtifactId
			// this is new map to get id, id-type, devtronResourceId,devtronSchemaId,kept it here as we need to calculate for given app ids
			appIdVsDrSchemaDetail[dependencyBean.Id] = adapter.BuildDepDetail(dependencyBean.Id, dependencyBean.IdType, dependencyBean.DevtronResourceSchemaId)
			artifactsIds = append(artifactsIds, dependencyBean.Config.ArtifactConfig.ArtifactId)
		}
	}
	return artifactsIds, appIdVsDrSchemaDetail, appIdVsDepBean, nil
}

func (impl *DevtronResourceServiceImpl) getPipelineIdPipelineMapForPipelineIds(pipelineIds []int) (map[int]*pipelineConfig.Pipeline, error) {
	pipelineIdByPipelineMap := make(map[int]*pipelineConfig.Pipeline, len(pipelineIds))
	cdPipelines, err := impl.pipelineRepository.FindByIdsIn(pipelineIds)
	if err != nil {
		impl.logger.Errorw("error encountered in GetAppIdVsAppDetailsMapForAppIds", "err", err)
		return pipelineIdByPipelineMap, err
	}
	for _, pipeline := range cdPipelines {
		pipelineIdByPipelineMap[pipeline.Id] = pipeline
	}
	return pipelineIdByPipelineMap, nil

}
func (impl *DevtronResourceServiceImpl) getPipelineBasicDataMapForPipelineIds(pipelineIds []int) (map[int]*pipelineConfig.Pipeline, error) {
	pipelineIdByPipelineMap := make(map[int]*pipelineConfig.Pipeline, len(pipelineIds))
	cdPipelines, err := impl.pipelineRepository.FindBasicDataByIdsIn(pipelineIds)
	if err != nil {
		impl.logger.Errorw("error encountered in GetAppIdVsAppDetailsMapForAppIds", "err", err)
		return pipelineIdByPipelineMap, err
	}
	for _, pipeline := range cdPipelines {
		pipelineIdByPipelineMap[pipeline.Id] = pipeline
	}
	return pipelineIdByPipelineMap, nil

}

// getReleaseDeploymentInfoForDependenciesFromMap gets every data point for dependencies in CdPipelineReleaseInfo object from map
func (impl *DevtronResourceServiceImpl) getReleaseDeploymentInfoForDependenciesFromMap(dependencies []*bean.DtResDepBean, pipelineIdAppIdKeyVsReleaseInfo map[string]*bean.CdPipelineReleaseInfo) ([]*bean.CdPipelineReleaseInfo, error) {
	pipelinesInfo := make([]*bean.CdPipelineReleaseInfo, 0)
	appIds := make([]int, 0, len(dependencies))
	for _, dependency := range dependencies {
		// not checking child inheritance as callee has already filtered all child inheritance case.
		appIds = append(appIds, dependency.OldObjectId)
	}
	// getting info from pipelineAppIdVsReleaseInfoMap if app id is in appIds calculated from level dependencies
	for key, info := range pipelineIdAppIdKeyVsReleaseInfo {
		appId, err := helper.GetAppIdFromPipelineIdAppIdKey(key)
		if err != nil {
			impl.logger.Errorw("error encountered in getReleaseDeploymentInfoForDependenciesFromMap", "key", key, "err", err)
			return nil, err
		}
		if slices.Contains(appIds, appId) {
			pipelinesInfo = append(pipelinesInfo, info)
		}
	}

	return pipelinesInfo, nil
}

// getReleaseDeploymentInfoWithTaskRunAllowedFromMap gets every data point for dependencies in CdPipelineReleaseInfo object from map with task run allowed computation
// STEPS for computation
// (levelTenInsKeyMapVsTaskRunAllowedOfCurrentLevel) stores task run allowed for particular level-tenant-installation for the current stage.(is deployment allowed for that stage)
// (levelTenInsKeyMapVsTaskRunAllowed) stores computation for particular level-tenant-installation computation where all stages successfully deployed or not stages for next stages
// (allTenantsMap) stores all tenants-installation
// (key)=="level-tenantId-InstallationId"
// (levelToPreviousLevelMap) has previous level for every level, (if value is -1) then its first release
// STEP 1:
//
//	 Firstly We check if the particular tenant-installation exists, if does not exist or previous level is -1(first stage),
//		we store levelTenInsKeyMapVsTaskRunAllowedOfCurrentLevel to be true, and levelTenInsKeyMapVsTaskRunAllowed for previous level to be true.
//	 Case where tenant occurred for the first time on 3rd level
//
// STEP 2:
//
//	 We check if particular key exist in levelTenInsKeyMapVsTaskRunAllowed , if yes tenant installation have already encountered in this stage,
//		so we will do && operation of current value , current iteration status should be (completed).
//
// STEP 3:
//
//	 If not present , first time occurred in this stage, we check for its previous level in levelTenInsKeyMapVsTaskRunAllowed,
//		if exist will do && operation of previous stage value , current iteration status should be (completed).
//
// STEP 4:
//
//	 Just storing previous level value in levelTenInsKeyMapVsTaskRunAllowedOfCurrentLevel in current level,
//		as condition for deploy on this current stage is previous level all should all be deployed.so used levelTenInsKeyMapVsTaskRunAllowed with previous level value.
//
// STEP 5:
//
//	marking this tenant installation in allTenantsMap to true.
//
// STEP 6:
//
//	  CASES handling: after all iterations , iterating in all tenants map , and checking if any tenant is not present in this level,
//		so propagating its previous level value in current level, as there can be cases where tenants are present in 1st and 3rd level,
//		its values will be propagated to 2nd level as well in this map by this step.
func (impl *DevtronResourceServiceImpl) getReleaseDeploymentInfoWithTaskRunAllowedFromMap(dependencies []*bean.DtResDepBean, pipelineIdAppIdKeyVsReleaseInfo map[string]*bean.CdPipelineReleaseInfo,
	levelTenInsKeyMapVsTaskRunAllowed map[string]bool, allTenantsMap map[string]bool,
	appIdVsLevelIdMap map[int]int, levelToPreviousLevelMap map[int]int,
	levelTenInsKeyMapVsTaskRunAllowedOfCurrentLevel map[string]bool, level int) ([]*bean.CdPipelineReleaseInfo, error) {

	pipelinesInfo := make([]*bean.CdPipelineReleaseInfo, 0)
	appIds := make([]int, 0, len(dependencies))
	for _, dependency := range dependencies {
		// not checking child inheritance as callee has already filtered all child inheritance case.
		appIds = append(appIds, dependency.OldObjectId)
		appIdVsLevelIdMap[dependency.OldObjectId] = level

	}
	currentLevel := level
	previousLevel := levelToPreviousLevelMap[currentLevel]
	// getting info from pipelineAppIdVsReleaseInfoMap if app id is in appIds calculated from level dependencies
	for key, info := range pipelineIdAppIdKeyVsReleaseInfo {
		appId, err := helper.GetAppIdFromPipelineIdAppIdKey(key)
		if err != nil {
			impl.logger.Errorw("error encountered in getReleaseDeploymentInfoForDependenciesFromMap", "key", key, "err", err)
			return nil, err
		}
		if slices.Contains(appIds, appId) {
			pipelinesInfo = append(pipelinesInfo, info)
			if info.MappingConfig == nil {
				continue
			}
			// if previous level is -1 (first stage) marking previous level to be deployable (true)
			//and current level for current level map to be true as current level is dependent on previous stage.
			_, exist := allTenantsMap[helper.GetKeyForTenantAndInstallationId(info.MappingConfig.TenantId, info.MappingConfig.InstallationId)]
			if previousLevel == -1 || !exist {
				levelTenInsKeyMapVsTaskRunAllowedOfCurrentLevel[helper.GetKeyForLevelAndTenantInstallationId(currentLevel, info.MappingConfig.TenantId, info.MappingConfig.InstallationId)] = true
				levelTenInsKeyMapVsTaskRunAllowed[helper.GetKeyForLevelAndTenantInstallationId(previousLevel, info.MappingConfig.TenantId, info.MappingConfig.InstallationId)] = true
			}

			if val, ok := levelTenInsKeyMapVsTaskRunAllowed[helper.GetKeyForLevelAndTenantInstallationId(currentLevel, info.MappingConfig.TenantId, info.MappingConfig.InstallationId)]; ok {
				// if current level and tenant key already exist , value should be set as current value and current info value should be succeeded
				levelTenInsKeyMapVsTaskRunAllowed[helper.GetKeyForLevelAndTenantInstallationId(currentLevel, info.MappingConfig.TenantId, info.MappingConfig.InstallationId)] = val && info.ReleaseDeploymentStatus == bean.Completed
			} else if val2, ok2 := levelTenInsKeyMapVsTaskRunAllowed[helper.GetKeyForLevelAndTenantInstallationId(previousLevel, info.MappingConfig.TenantId, info.MappingConfig.InstallationId)]; ok2 {
				// if not present in map means this is the first an app has occurred in a stage which means current info value should be succeeded and previous level should be succeeded
				levelTenInsKeyMapVsTaskRunAllowed[helper.GetKeyForLevelAndTenantInstallationId(currentLevel, info.MappingConfig.TenantId, info.MappingConfig.InstallationId)] = val2 && info.ReleaseDeploymentStatus == bean.Completed
			}
			// updating value in task run allowed current level map with previous level value(dependent on previous level)
			levelTenInsKeyMapVsTaskRunAllowedOfCurrentLevel[helper.GetKeyForLevelAndTenantInstallationId(currentLevel, info.MappingConfig.TenantId, info.MappingConfig.InstallationId)] =
				levelTenInsKeyMapVsTaskRunAllowed[helper.GetKeyForLevelAndTenantInstallationId(previousLevel, info.MappingConfig.TenantId, info.MappingConfig.InstallationId)]

			// marking tenant to true (storing all encountered tenants)
			allTenantsMap[helper.GetKeyForTenantAndInstallationId(info.MappingConfig.TenantId, info.MappingConfig.InstallationId)] = true

		}
	}
	for k, _ := range allTenantsMap {
		if _, ok := levelTenInsKeyMapVsTaskRunAllowed[helper.GetKeyForLevelTenantInstallationIdKey(level, k)]; !ok {
			//assuming it will always exists as we are propagating very value here in previous level
			levelTenInsKeyMapVsTaskRunAllowed[helper.GetKeyForLevelTenantInstallationIdKey(level, k)] = levelTenInsKeyMapVsTaskRunAllowed[helper.GetKeyForLevelTenantInstallationIdKey(previousLevel, k)]
		}
	}
	for _, info := range pipelinesInfo {
		if info.MappingConfig == nil {
			info.TaskRunAllowed = false
		} else if val, ok := levelTenInsKeyMapVsTaskRunAllowedOfCurrentLevel[helper.GetKeyForLevelAndTenantInstallationId(currentLevel, info.MappingConfig.TenantId, info.MappingConfig.InstallationId)]; ok {
			info.TaskRunAllowed = val
		}
	}
	return pipelinesInfo, nil
}

func (impl *DevtronResourceServiceImpl) getEnvironmentsForApplicationDependency(childInheritance []*bean.ChildInheritance, appId int) ([]*bean.CdPipelineEnvironment, error) {
	// iterating in every inheritance and getting child inheritances(for eg cd) and getting corresponding details) for now it is ["*"] we will fetch all cd (env) for that dependency
	envs := make([]*bean.CdPipelineEnvironment, 0)
	findAll := false
	for _, inheritance := range childInheritance {
		// collecting selectors here currently only ["all"] is present so will find all env names for an app but can be modified in future
		findAll = slices.Contains(inheritance.Selector, bean.DefaultCdPipelineSelector)
	}
	if findAll {
		pipelines, err := impl.pipelineRepository.FindEnvNameAndIdByAppId(appId)
		if err != nil {
			impl.logger.Errorw("error encountered in getEnvironmentsForApplicationDependency", "err", err)
			return envs, err
		}

		deploymentConfigMap := make(map[bean6.UniqueDeploymentConfigIdentifier]*bean6.DeploymentConfig, 0)
		for _, p := range pipelines {
			deploymentConfig, err := impl.deploymentConfigService.GetConfigForDevtronApps(p.AppId, p.EnvironmentId)
			if err != nil {
				impl.logger.Errorw("error in getting deployment config by appId and envId", "appId", p.AppId, "envId", p.EnvironmentId, "err", err)
				return envs, err
			}
			deploymentConfigMap[bean6.GetConfigUniqueIdentifier(deploymentConfig.AppId, deploymentConfig.EnvironmentId)] = deploymentConfig
		}

		for _, pipeline := range pipelines {
			deploymentConfig := deploymentConfigMap[bean6.GetConfigUniqueIdentifier(pipeline.AppId, pipeline.EnvironmentId)]
			env := adapter.BuildCdPipelineEnvironmentBasicData(pipeline.Environment.Name, pipeline.DeploymentAppType, pipeline.EnvironmentId, pipeline.Id, nil, deploymentConfig)
			envs = append(envs, env)
		}
	} else {
		// if specific, will have to find corresponding ids and get the details
		// currently we don't store specific childInheritance
	}

	return envs, nil
}

func (impl *DevtronResourceServiceImpl) getEnvironmentsWithMappingConfigForApplicationDependency(childInheritance []*bean.ChildInheritance, appId int, envIdVsMappingConfig map[int]*bean.InstallationMappingConfigInternalBean, mapOfInstallationIdentifierVsIsReleased map[string]bool) ([]*bean.CdPipelineEnvironment, error) {
	// iterating in every inheritance and getting child inheritances(for eg cd) and getting corresponding details) for now it is ["*"] we will fetch all cd (env) for that dependency
	envs := make([]*bean.CdPipelineEnvironment, 0)
	findAll := false
	for _, inheritance := range childInheritance {
		// collecting selectors here currently only ["all"] is present so will find all env names for an app but can be modified in future
		findAll = slices.Contains(inheritance.Selector, bean.DefaultCdPipelineSelector)
	}
	// doing this as we show target installations by showing all pipelines even if application was not selected( we patch child inheritance on image patch)
	if len(childInheritance) == 0 {
		findAll = true
	}
	if findAll {
		pipelines, err := impl.pipelineRepository.FindEnvNameAndIdByAppId(appId)
		if err != nil {
			impl.logger.Errorw("error encountered in getEnvironmentsWithMappingConfigForApplicationDependency", "err", err)
			return envs, err
		}
		if envIdVsMappingConfig == nil {
			envIdVsMappingConfig = make(map[int]*bean.InstallationMappingConfigInternalBean)
		}
		if mapOfInstallationIdentifierVsIsReleased == nil {
			mapOfInstallationIdentifierVsIsReleased = make(map[string]bool)
		}
		for _, pipeline := range pipelines {
			deploymentConfig, err := impl.deploymentConfigService.GetConfigForDevtronApps(appId, pipeline.EnvironmentId)
			if err != nil {
				impl.logger.Errorw("error in getting deployment config", "appId", appId, "envId", pipeline.EnvironmentId, "err", err)
				return nil, err
			}
			env := adapter.BuildCdPipelineEnvironmentBasicData(pipeline.Environment.Name, pipeline.DeploymentAppType, pipeline.EnvironmentId, pipeline.Id, adapter.BuildInstallationMappingConfigFromInternalBean(envIdVsMappingConfig[pipeline.EnvironmentId]), deploymentConfig)
			env.InstallationMappingConfig.IsReleaseTriggered = mapOfInstallationIdentifierVsIsReleased[env.InstallationMappingConfig.InstallationIdentifier]
			envs = append(envs, env)
		}
	} else {
		// if specific, will have to find corresponding ids and get the details
		// currently we don't store specific childInheritance
	}

	return envs, nil
}

func (impl *DevtronResourceServiceImpl) getDecodingRequestFromDecodeFilters(filterCriteria []string) (*bean.FilterConditionInternalBean, error) {
	// filters decoding
	filterConditionReq := bean.NewFilterConditionInternalBean()
	var err error
	// filters decoding
	if len(filterCriteria) > 0 {
		// setting filter values from filters
		filterConditionReq, err = impl.getFilterConditionBeanFromDecodingFilters(filterCriteria)
		if err != nil {
			impl.logger.Errorw("error encountered in fetchReleaseTaskRunInfoWithFilters", "err", err)
			return nil, err
		}

		// finding application/devtron-application schema id for filters check in dependency
		applicationSchema, err := impl.dtResReadService.GetDtApplicationSchema()
		if err != nil {
			impl.logger.Errorw("error in ListResourceObjectByKindAndVersion", "err", err)
			return nil, err
		}
		filterConditionReq.AppDevtronResourceSchemaId = applicationSchema.Id
	} else {
		filterConditionReq.RequestWithoutFilters = true
	}
	return filterConditionReq, nil
}

// setFilterConditionInRequest decodes the filters provided and sets teh filters in request for further processing
func (impl *DevtronResourceServiceImpl) getFilterConditionBeanFromDecodingFilters(filterCriteria []string) (*bean.FilterConditionInternalBean, error) {
	filterReq, isEmptyResponseType, err := impl.getFilterConditionFromFilterCriteria(filterCriteria)
	if err != nil {
		impl.logger.Errorw("error encountered in setFilterConditionInRequest", "err", err)
		return nil, err
	}

	return adapter.BuildFilterConditionInternalBean(filterReq.AppIdsFilters, filterReq.EnvIdsFilters, filterReq.ReleaseDeploymentStatus, filterReq.StageWiseDeploymentStatus, filterReq.TenantIdVSInstallationId, filterReq.TenantIdentifierVSInstallationIdentifier, isEmptyResponseType, filterReq.ReleaseChannelIdentifiers), nil
}

// getOnlyLevelDataForTaskInfo gets only level data with task run allowed operation.(signifies lite mode)
func (impl *DevtronResourceServiceImpl) getOnlyLevelDataForTaskInfo(objectData string, pipelineIdAppIdKeyVsReleaseInfo map[string]*bean.CdPipelineReleaseInfo, levelIndex int) ([]bean.DtReleaseTaskRunInfo, error) {
	response := make([]bean.DtReleaseTaskRunInfo, 0)
	levelDependencies := impl.getLevelDependenciesFromObjectData(objectData, levelIndex)
	for _, levelDependency := range levelDependencies {
		dtReleaseTaskRunInfo := bean.DtReleaseTaskRunInfo{
			Level: levelDependency.Index,
		}
		response = append(response, dtReleaseTaskRunInfo)
	}
	return response, nil
}

func (impl *DevtronResourceServiceImpl) isEachAppDeployedOnAtLeastOneEnvWithMap(appIds []int, pipelineIdAppIdKeyVsReleaseInfo map[string]*bean.CdPipelineReleaseInfo) (bool, error) {
	appIdToSuccessCriteriaFlag := make(map[int]bool, len(appIds))
	for key, info := range pipelineIdAppIdKeyVsReleaseInfo {
		appId, err := helper.GetAppIdFromPipelineIdAppIdKey(key)
		if err != nil {
			impl.logger.Errorw("error encountered in IsEachAppDeployedOnAtLeastOneEnvWithMap", "key", key, "err", err)
			return false, err
		}
		if appIdToSuccessCriteriaFlag[appId] {
			continue
		}
		// if this appId from map is not in provided appIds continue as we don't need to calculate for thsi appId
		if !slices.Contains(appIds, appId) {
			continue
		}
		// if not of (deployment exist and status is succeeded)
		if info.ExistingStages.Deploy && !helper.IsStatusSucceeded(info.DeployStatus) {
			continue
		}
		// if not of (pre exist and status is succeeded)
		if info.ExistingStages.Pre && !helper.IsStatusSucceeded(info.PreStatus) {
			continue
		}
		// if not of (post exist and status is succeeded)
		if info.ExistingStages.Post && !helper.IsStatusSucceeded(info.PostStatus) {
			continue
		}
		appIdToSuccessCriteriaFlag[appId] = true
	}
	if len(appIds) != len(appIdToSuccessCriteriaFlag) {
		return false, nil
	}
	return true, nil
}

// getLevelDataWithDependenciesForTaskInfo get level wise data with dependencies in it, supports level Index key if not 0 get level of that index with its dependencies
func (impl *DevtronResourceServiceImpl) getLevelDataWithDependenciesForTaskInfo(req *bean.FilterConditionInternalBean, objectData, rsIdentifier string, levelIndex int, pipelineIdAppIdKeyVsReleaseInfo map[string]*bean.CdPipelineReleaseInfo) ([]bean.DtReleaseTaskRunInfo, error) {
	response := make([]bean.DtReleaseTaskRunInfo, 0)
	var levelToAppDependenciesMap map[int][]*bean.DtResDepBean

	levelDependencies := impl.getLevelDependenciesFromObjectData(objectData, levelIndex)
	applicationFilterCondition := bean.NewDependencyFilterCondition().
		WithFilterByTypes(bean.DevtronResourceDependencyTypeUpstream).
		WithFilterByDependentOnIndex(levelIndex).
		WithFilterByIdAndSchemaId(req.AppIds, req.AppDevtronResourceSchemaId).
		WithChildInheritance()
	applicationDependencies := GetDepsBeanFromObjectData(objectData, applicationFilterCondition)
	levelToAppDependenciesMap = adapter.MapDepsByDependentOnIndex(applicationDependencies)

	for _, levelDependency := range levelDependencies {
		dtReleaseTaskRunInfo := bean.DtReleaseTaskRunInfo{
			Level: levelDependency.Index,
		}
		dependencies := make([]*bean.CdPipelineReleaseInfo, 0)
		if levelToAppDependenciesMap != nil && levelToAppDependenciesMap[levelDependency.Index] != nil {
			dependencyBean, err := impl.getReleaseDeploymentInfoForDependenciesFromMap(levelToAppDependenciesMap[levelDependency.Index], pipelineIdAppIdKeyVsReleaseInfo)
			if err != nil {
				impl.logger.Errorw("error encountered in fetchReleaseTaskRunInfo", "rsIdentifier", rsIdentifier, "stage", levelDependency.Index, "err", err)
				return nil, err
			}
			// applying filters for rollout , env and deployment status (app ids filters is already handled, one level while fetching applicationDependencies
			dependencyBean = impl.applyFiltersToDependencies(req, dependencyBean)
			dependencies = append(dependencies, dependencyBean...)
		}
		dtReleaseTaskRunInfo.Dependencies = dependencies

		response = append(response, dtReleaseTaskRunInfo)
	}
	return response, nil
}

// getLevelDataWithDependenciesWithTaskRunAllowedForTaskInfo get level wise data with dependencies in it, supports level Index key if not 0 get level of that index with its dependencies
func (impl *DevtronResourceServiceImpl) getLevelDataWithDependenciesWithTaskRunAllowedForTaskInfo(req *bean.FilterConditionInternalBean, objectData, rsIdentifier string, levelIndex int, pipelineIdAppIdKeyVsReleaseInfo map[string]*bean.CdPipelineReleaseInfo) ([]bean.DtReleaseTaskRunInfo, map[string]bool, map[int]int, error) {
	response := make([]bean.DtReleaseTaskRunInfo, 0)
	var levelToAppDependenciesMap map[int][]*bean.DtResDepBean
	// getting all levels here for overall computation
	levelDependencies := impl.getLevelDependenciesFromObjectData(objectData, 0)
	applicationFilterCondition := bean.NewDependencyFilterCondition().
		WithFilterByTypes(bean.DevtronResourceDependencyTypeUpstream).
		WithChildInheritance()
	applicationDependencies := GetDepsBeanFromObjectData(objectData, applicationFilterCondition)
	levelToAppDependenciesMap = adapter.MapDepsByDependentOnIndex(applicationDependencies)
	levelToPreviousLevelMap := getPreviousLevelDependencyForAllDependencies(levelDependencies)
	// this map stores values corresponding to actual state of level, which is being used for net level computation
	levelTenantKeyMapVsTaskRunAllowed := make(map[string]bool)
	// this map store values corresponding to current level, for a tenant , is actual operation allowed on that level for that tenant
	levelTenantKeyMapVsTaskRunAllowedOfCurrentLevel := make(map[string]bool)
	allTenantsMap := make(map[string]bool)
	appIdVsLevelIdMap := make(map[int]int)

	for _, levelDependency := range levelDependencies {
		dtReleaseTaskRunInfo := bean.DtReleaseTaskRunInfo{
			Level: levelDependency.Index,
		}
		allDependencies := levelToAppDependenciesMap[levelDependency.Index]
		dependencies := make([]*bean.CdPipelineReleaseInfo, 0)
		if levelToAppDependenciesMap != nil && levelToAppDependenciesMap[levelDependency.Index] != nil {
			dependencyBean, err := impl.getReleaseDeploymentInfoWithTaskRunAllowedFromMap(allDependencies, pipelineIdAppIdKeyVsReleaseInfo, levelTenantKeyMapVsTaskRunAllowed, allTenantsMap, appIdVsLevelIdMap, levelToPreviousLevelMap, levelTenantKeyMapVsTaskRunAllowedOfCurrentLevel, levelDependency.Index)
			if err != nil {
				impl.logger.Errorw("error encountered in fetchReleaseTaskRunInfo", "rsIdentifier", rsIdentifier, "stage", levelDependency.Index, "err", err)
				return nil, nil, nil, err
			}
			// applying filters for rollout , env and deployment status (app ids filters is already handled, one level while fetching applicationDependencies
			dependencyBean = impl.applyFiltersToDependencies(req, dependencyBean)
			dependencies = append(dependencies, dependencyBean...)
		}
		dtReleaseTaskRunInfo.Dependencies = dependencies
		if levelDependency.Index == levelIndex {
			response = append(response, dtReleaseTaskRunInfo)
		} else if levelIndex == 0 {
			response = append(response, dtReleaseTaskRunInfo)
		}
	}
	return response, levelTenantKeyMapVsTaskRunAllowedOfCurrentLevel, appIdVsLevelIdMap, nil
}

func (impl *DevtronResourceServiceImpl) applyFiltersToDependencies(req *bean.FilterConditionInternalBean, cdPipelineReleaseInfo []*bean.CdPipelineReleaseInfo) []*bean.CdPipelineReleaseInfo {
	if req.RequestWithoutFilters {
		return cdPipelineReleaseInfo
	}
	updatedCdPipelineReleaseInfo := make([]*bean.CdPipelineReleaseInfo, 0, len(cdPipelineReleaseInfo))
	if req.IsEmptyResponseType {
		// return empty as to handle delete or non exists cases in app and env
		return updatedCdPipelineReleaseInfo
	}
	for _, info := range cdPipelineReleaseInfo {
		if len(req.AppIds) != 0 && !slices.Contains(req.AppIds, info.AppId) {
			//continue in case app id filters len is greater than 0 and does not contain app id.
			continue
		}
		if len(req.EnvIds) != 0 && !slices.Contains(req.EnvIds, info.EnvId) {
			//continue in case env id filters len is greater than 0 and does not contain env id.
			continue
		}
		if len(req.ReleaseDeploymentStatus) != 0 && !slices.Contains(req.ReleaseDeploymentStatus, info.ReleaseDeploymentStatus.ToString()) {
			//continue in case ReleaseDeploymentStatus filters len is greater than 0 and does not contain ReleaseDeploymentStatus.
			continue
		}
		//pre
		if values, ok := req.StageWiseDeploymentStatus[bean3.CD_WORKFLOW_TYPE_PRE]; ok && len(values) > 0 {
			if !info.ExistingStages.Pre || !slices.Contains(values, info.PreStatus) {
				//continue in case pre stage filters ln is greater than 0 and does not contain pre status of info.
				continue
			}

		}
		//deploy
		if values, ok := req.StageWiseDeploymentStatus[bean3.CD_WORKFLOW_TYPE_DEPLOY]; ok && len(values) > 0 {
			if !info.ExistingStages.Deploy || !slices.Contains(values, info.DeployStatus) {
				//continue in case deploy stage filters ln is greater than 0 and does not contain deploy status of info.
				continue
			}
		}
		//post
		if values, ok := req.StageWiseDeploymentStatus[bean3.CD_WORKFLOW_TYPE_POST]; ok && len(values) > 0 {
			if !info.ExistingStages.Post || !slices.Contains(values, info.PostStatus) {
				//continue in case post stage filters ln is greater than 0 and does not contain post status of info.
				continue
			}
		}
		//tenant filters
		if len(req.TenantIdentifierVsInstallationIdentifiersMap) > 0 {
			if values, ok := req.TenantIdentifierVsInstallationIdentifiersMap[info.MappingConfig.TenantId]; ok && !slices.Contains(values, info.MappingConfig.InstallationId) {
				continue
			} else if !ok {
				continue
			}
		}
		// release channel filter, initialised to not subscribed, for handling not subscribed filter
		releaseChannelId := bean.NotSubscribedReleaseChannelIdentifier
		if info.MappingConfig != nil && len(info.MappingConfig.ReleaseChannelId) > 0 {
			releaseChannelId = info.MappingConfig.ReleaseChannelId
		}
		if len(req.ReleaseChannelIdentifiers) > 0 && !slices.Contains(req.ReleaseChannelIdentifiers, releaseChannelId) {
			//continue in case release channel filters len is greater than 0 and does not contain release channel identifiers.
			continue
		}

		updatedCdPipelineReleaseInfo = append(updatedCdPipelineReleaseInfo, info)
	}

	return updatedCdPipelineReleaseInfo
}

func (impl *DevtronResourceServiceImpl) getLevelDependenciesFromObjectData(objectData string, levelIndex int) []*bean.DtResDepBean {
	levelFilterCondition := bean.NewDependencyFilterCondition().
		WithFilterByTypes(bean.DevtronResourceDependencyTypeLevel)
	if levelIndex != 0 {
		levelFilterCondition = levelFilterCondition.WithFilterByIndexes(levelIndex)
	}
	return GetDepsBeanFromObjectData(objectData, levelFilterCondition)
}

func processInstallationDeploymentCount(tenantInstallationIdVsRolloutStatusesMap map[string][]bean.ReleaseDeploymentStatus) *bean.InstallationDeploymentStatusCount {
	completedCount := 0
	yetToTriggerCount := 0
	failedCount := 0
	ongoingCount := 0

	for _, val := range tenantInstallationIdVsRolloutStatusesMap {
		rolloutStatus := helper.CalculateRolloutStatusFromSlice(val)
		if rolloutStatus == bean.Completed {
			completedCount = completedCount + 1
		}
		if rolloutStatus == bean.YetToTrigger {
			yetToTriggerCount = yetToTriggerCount + 1
		}
		if rolloutStatus == bean.Failed {
			failedCount = failedCount + 1
		}
		if rolloutStatus == bean.Ongoing {
			ongoingCount = ongoingCount + 1
		}
	}
	return adapter.BuildInstallationDeploymentStatusCount(ongoingCount, yetToTriggerCount, failedCount, completedCount)
}

func getStageWiseAndTotalTenantsFromPipelineInfo(pipelinesInfo []*bean.CdPipelineReleaseInfo) (*bean.StageWiseStatusCount, int) {
	preStatusVsCountMap := make(map[string]int, len(pipelinesInfo))
	deployStatusVsCountMap := make(map[string]int, len(pipelinesInfo))
	postStatusVsCountMap := make(map[string]int, len(pipelinesInfo))
	tenantInstallationVsRolloutStatusesMap := make(map[string][]bean.ReleaseDeploymentStatus, len(pipelinesInfo))
	for _, pipeline := range pipelinesInfo {
		if pipeline.ExistingStages.Pre {
			preStatusVsCountMap[pipeline.PreStatus] = preStatusVsCountMap[pipeline.PreStatus] + 1
		}
		if pipeline.ExistingStages.Post {
			postStatusVsCountMap[pipeline.PostStatus] = postStatusVsCountMap[pipeline.PostStatus] + 1
		}
		if pipeline.ExistingStages.Deploy {
			// cd pipeline will always exist added this check intentionally for validation
			deployStatusVsCountMap[pipeline.DeployStatus] = deployStatusVsCountMap[pipeline.DeployStatus] + 1
		}
		if pipeline.MappingConfig != nil {
			tenantInstallationVsRolloutStatusesMap[helper.GetKeyForTenantAndInstallationId(pipeline.MappingConfig.TenantId, pipeline.MappingConfig.InstallationId)] = append(tenantInstallationVsRolloutStatusesMap[helper.GetKeyForTenantAndInstallationId(pipeline.MappingConfig.TenantId, pipeline.MappingConfig.InstallationId)], pipeline.ReleaseDeploymentStatus)
		}
	}
	preStatusCount := processPreOrPostDeploymentVsCountMapForResponse(preStatusVsCountMap)
	deployStatusCount := processDeploymentVsCountMapForResponse(deployStatusVsCountMap)
	postStatusCount := processPreOrPostDeploymentVsCountMapForResponse(postStatusVsCountMap)
	return adapter.BuildStageWiseStatusCount(preStatusCount, deployStatusCount, postStatusCount), len(tenantInstallationVsRolloutStatusesMap)
}

func getReleaseDeploymentStatusCountFromPipelineInfo(pipelinesInfo []*bean.CdPipelineReleaseInfo, totalLenOfPipelines int, totalInstallation int) (*bean.InstallationDeploymentStatusCount, *bean.TotalAndFilteredCount, *bean.TotalAndFilteredCount) {
	tenantInstallationIdKeyVsRolloutStatusesMap := make(map[string][]bean.ReleaseDeploymentStatus, len(pipelinesInfo))
	for _, pipeline := range pipelinesInfo {
		if pipeline.MappingConfig != nil {
			tenantInstallationIdKeyVsRolloutStatusesMap[helper.GetKeyForTenantAndInstallationId(pipeline.MappingConfig.TenantId, pipeline.MappingConfig.InstallationId)] = append(tenantInstallationIdKeyVsRolloutStatusesMap[helper.GetKeyForTenantAndInstallationId(pipeline.MappingConfig.TenantId, pipeline.MappingConfig.InstallationId)], pipeline.ReleaseDeploymentStatus)
		}
	}
	releaseDeploymentCount := processInstallationDeploymentCount(tenantInstallationIdKeyVsRolloutStatusesMap)
	mappingConfigCount := adapter.BuildTotalAndFilteredCount(totalInstallation, len(tenantInstallationIdKeyVsRolloutStatusesMap))
	deploymentsCount := adapter.BuildTotalAndFilteredCount(totalLenOfPipelines, len(pipelinesInfo))
	return releaseDeploymentCount, mappingConfigCount, deploymentsCount
}
func processPreOrPostDeploymentVsCountMapForResponse(preOrPostStatusVsCountMap map[string]int) *bean.PrePostStatusCount {
	notTriggered := 0
	failed := 0
	inProgress := 0
	succeeded := 0
	others := 0
	if val, ok := preOrPostStatusVsCountMap[pipelineStageBean.NotTriggered]; ok {
		notTriggered = val
	}
	if val, ok := preOrPostStatusVsCountMap[cdWorkflow.WorkflowFailed]; ok {
		failed = val
	}
	if val, ok := preOrPostStatusVsCountMap[cdWorkflow.WorkflowAborted]; ok {
		failed = failed + val
	}
	if val, ok := preOrPostStatusVsCountMap[cdWorkflow.WorkflowCancel]; ok {
		failed = failed + val
	}
	if val, ok := preOrPostStatusVsCountMap[bean5.Degraded]; ok {
		failed = failed + val
	}
	if val, ok := preOrPostStatusVsCountMap[bean.Error]; ok {
		failed = failed + val
	}
	if val, ok := preOrPostStatusVsCountMap[cdWorkflow.WorkflowInProgress]; ok {
		inProgress = val
	}
	if val, ok := preOrPostStatusVsCountMap[cdWorkflow.WorkflowStarting]; ok {
		inProgress = inProgress + val
	}
	if val, ok := preOrPostStatusVsCountMap[bean.RunningStatus]; ok {
		inProgress = inProgress + val
	}
	if val, ok := preOrPostStatusVsCountMap[cdWorkflow.WorkflowSucceeded]; ok {
		succeeded = val
	}
	if val, ok := preOrPostStatusVsCountMap[bean5.Healthy]; ok {
		succeeded = succeeded + val
	}
	if val, ok := preOrPostStatusVsCountMap[bean.Unknown]; ok {
		others = val
	}
	if val, ok := preOrPostStatusVsCountMap[bean.Missing]; ok {
		others = others + val
	}
	return adapter.BuildPreOrPostDeploymentCount(notTriggered, failed, succeeded, inProgress, others)

}

func processDeploymentVsCountMapForResponse(deployStatusVsCountMap map[string]int) *bean.DeploymentCount {
	notTriggered := 0
	failed := 0
	inProgress := 0
	succeeded := 0
	queued := 0
	others := 0
	if val, ok := deployStatusVsCountMap[pipelineStageBean.NotTriggered]; ok {
		notTriggered = val
	}
	if val, ok := deployStatusVsCountMap[cdWorkflow.WorkflowFailed]; ok {
		failed = val
	}
	if val, ok := deployStatusVsCountMap[cdWorkflow.WorkflowAborted]; ok {
		failed = failed + val
	}
	if val, ok := deployStatusVsCountMap[cdWorkflow.WorkflowCancel]; ok {
		failed = failed + val
	}
	if val, ok := deployStatusVsCountMap[bean5.Degraded]; ok {
		failed = failed + val
	}
	if val, ok := deployStatusVsCountMap[bean.Error]; ok {
		failed = failed + val
	}
	if val, ok := deployStatusVsCountMap[cdWorkflow.WorkflowInProgress]; ok {
		inProgress = val
	}
	if val, ok := deployStatusVsCountMap[cdWorkflow.WorkflowInitiated]; ok {
		inProgress = inProgress + val
	}
	if val, ok := deployStatusVsCountMap[bean.RunningStatus]; ok {
		inProgress = inProgress + val
	}
	if val, ok := deployStatusVsCountMap[cdWorkflow.WorkflowStarting]; ok {
		inProgress = inProgress + val
	}
	if val, ok := deployStatusVsCountMap[cdWorkflow.WorkflowSucceeded]; ok {
		succeeded = val
	}
	if val, ok := deployStatusVsCountMap[bean5.Healthy]; ok {
		succeeded = succeeded + val
	}
	if val, ok := deployStatusVsCountMap[cdWorkflow.WorkflowInQueue]; ok {
		queued = val
	}
	if val, ok := deployStatusVsCountMap[bean.Unknown]; ok {
		others = val
	}
	if val, ok := deployStatusVsCountMap[bean.Missing]; ok {
		others = others + val
	}
	if val, ok := deployStatusVsCountMap[cdWorkflow.WorkflowUnableToFetchState]; ok {
		others = others + val
	}
	if val, ok := deployStatusVsCountMap[cdWorkflow.WorkflowTimedOut]; ok {
		others = others + val
	}
	return adapter.BuildDeploymentCount(notTriggered, failed, succeeded, queued, inProgress, others)

}

// markRolloutStatusBasedOnStatusFromMap get allApplicationDependencies if not provided.
// If all the applications in all stages are deployed to their respective environments, checks from map
// Mark the rollout status -> bean.CompletelyDeployedReleaseRolloutStatus
func (impl *DevtronResourceServiceImpl) markRolloutStatusBasedOnStatusFromMap(existingResourceObject *repository.DevtronResourceObject, pipelineIdAppIdKeyVsReleaseInfo map[string]*bean.CdPipelineReleaseInfo, allApplicationDependencies []*bean.DtResDepBean) (err error) {
	rolloutStatus := bean.ReleaseRolloutStatus(gjson.Get(existingResourceObject.ObjectData, bean.ReleaseResourceRolloutStatusPath).String())
	if (rolloutStatus.IsCompletelyDeployed() || rolloutStatus.IsPartiallyDeployed()) && len(allApplicationDependencies) != 0 {
		appIds := make([]int, 0, len(allApplicationDependencies))
		// ignoring child inheritance check here as callee has already handled it( GET call will be deprecated in future not handling for that)
		for _, dependency := range allApplicationDependencies {
			appIds = append(appIds, dependency.OldObjectId)
		}
		isReleaseCompleted, err := impl.isAppsDeployedOnAllEnvWithRunnerFromMap(appIds, pipelineIdAppIdKeyVsReleaseInfo)
		if err != nil {
			impl.logger.Errorw("error encountered in markRolloutStatusIfAllDependenciesGotSucceedFromMap", "appIds", appIds, "err", err)
			return err
		}

		if isReleaseCompleted {
			// updated existing object (rollout status to completely deployed)
			err = impl.updateRolloutStatusInExistingObject(existingResourceObject,
				bean.CompletelyDeployedReleaseRolloutStatus, 1, time.Now())
			if err != nil {
				impl.logger.Errorw("error encountered in markRolloutStatusIfAllDependenciesGotSucceedFromMap", "err", err)
				return err
			}
		} else {
			err = impl.updateRolloutStatusInExistingObject(existingResourceObject, bean.PartiallyDeployedReleaseRolloutStatus, 1, time.Now())
			if err != nil {
				impl.logger.Errorw("error encountered in markRolloutStatusIfAllDependenciesGotSucceedFromMap", "err", err)
				return err
			}
		}
	}
	return nil
}
func (impl *DevtronResourceServiceImpl) setRolloutStatusInObjectDataBasedOnStatus(objectData string, pipelineIdAppIdKeyVsReleaseInfo map[string]*bean.CdPipelineReleaseInfo, allApplicationDependencies []*bean.DtResDepBean) (string, error) {
	rolloutStatus := bean.ReleaseRolloutStatus(gjson.Get(objectData, bean.ReleaseResourceRolloutStatusPath).String())
	if (rolloutStatus.IsCompletelyDeployed() || rolloutStatus.IsPartiallyDeployed()) && len(allApplicationDependencies) != 0 {
		appIds := make([]int, 0, len(allApplicationDependencies))
		// ignoring child inheritance check here as callee has already handled it( GET call will be deprecated in future not handling for that)
		for _, dependency := range allApplicationDependencies {
			appIds = append(appIds, dependency.OldObjectId)
		}
		isReleaseCompleted, err := impl.isAppsDeployedOnAllEnvWithRunnerFromMap(appIds, pipelineIdAppIdKeyVsReleaseInfo)
		if err != nil {
			impl.logger.Errorw("error encountered in markRolloutStatusIfAllDependenciesGotSucceedFromMap", "appIds", appIds, "err", err)
			return "", err
		}

		if isReleaseCompleted {
			// updated existing object (rollout status to completely deployed)
			objectData, err = helper.PatchResourceObjectDataAtAPath(objectData, bean.ReleaseResourceRolloutStatusPath, bean.CompletelyDeployedReleaseRolloutStatus)
			if err != nil {
				impl.logger.Errorw("error encountered in updateRolloutStatusInExistingObject", "err", err)
				return "", err
			}
		} else {
			objectData, err = helper.PatchResourceObjectDataAtAPath(objectData, bean.ReleaseResourceRolloutStatusPath, bean.PartiallyDeployedReleaseRolloutStatus)
			if err != nil {
				impl.logger.Errorw("error encountered in updateRolloutStatusInExistingObject", "err", err)
				return "", err
			}
		}
	}
	return objectData, nil
}

// isAppsDeployedOnAllEnvWithRunnerFromMap checks from map that every app is deployed on every stage successfully from
func (impl *DevtronResourceServiceImpl) isAppsDeployedOnAllEnvWithRunnerFromMap(appIds []int, pipelineIdAppIdKeyVsReleaseInfo map[string]*bean.CdPipelineReleaseInfo) (bool, error) {
	for key, info := range pipelineIdAppIdKeyVsReleaseInfo {
		appId, err := helper.GetAppIdFromPipelineIdAppIdKey(key)
		if err != nil {
			impl.logger.Errorw("error encountered in isAppsDeployedOnAllEnvWithRunnerFromMap", "key", key, "err", err)
			return false, err
		}
		// if this appId from map is not in provided appIds continue as we don't need to calculate for thsi appId
		if !slices.Contains(appIds, appId) {
			continue
		}
		// if not of (deployment exist and status is succeeded)
		if info.ExistingStages.Deploy && !helper.IsStatusSucceeded(info.DeployStatus) {
			return false, nil
		}
		// if not of (pre exist and status is succeeded)
		if info.ExistingStages.Pre && !helper.IsStatusSucceeded(info.PreStatus) {
			return false, nil
		}
		// if not of (post exist and status is succeeded)
		if info.ExistingStages.Post && !helper.IsStatusSucceeded(info.PostStatus) {
			return false, nil
		}
	}
	return true, nil
}

// getAppIdsAndEnvIdsFromFilterCriteria decodes filters and return app ids ,envIds, deployment status with stage, rollout Status, gets ids for identifiers if identifiers are given
func (impl *DevtronResourceServiceImpl) getFilterConditionFromFilterCriteria(filters []string) (*bean.FilterCriteriaReq, bool, error) {
	// filters decoding
	filterDecodeReq, err := util3.DecodeFiltersForDeployAndRolloutStatus(filters)
	if err != nil {
		impl.logger.Errorw("error encountered in getAppIdsAndEnvIdsFromFilterCriteria", "err", err, "filters", filters)
		return nil, false, err
	}
	// evaluating app ids from app identifiers as we are maintaining and processing everything in ids
	if len(filterDecodeReq.AppIdentifierFilters) > 0 {
		appIds, err := impl.appRepository.FindIdsByNamesAndAppType(filterDecodeReq.AppIdentifierFilters, helper2.CustomApp)
		if err != nil {
			impl.logger.Errorw("error encountered in getAppIdsAndEnvIdsFromFilterCriteria", "err", err, "appIdentifierFilters", filterDecodeReq.AppIdentifierFilters)
			return nil, false, err
		}
		filterDecodeReq.AppIdsFilters = append(filterDecodeReq.AppIdsFilters, appIds...)
	}
	if len(filterDecodeReq.EnvIdentifierFilters) > 0 {
		envIds, err := impl.envService.FindIdsByNames(filterDecodeReq.EnvIdentifierFilters)
		if err != nil {
			impl.logger.Errorw("error encountered in getAppIdsAndEnvIdsFromFilterCriteria", "err", err, "envIdentifierFilters", filterDecodeReq.EnvIdentifierFilters)
			return nil, false, err
		}
		filterDecodeReq.EnvIdsFilters = append(filterDecodeReq.EnvIdsFilters, envIds...)
	}
	if len(filterDecodeReq.ReleaseChannelIdsFilters) > 0 {
		releaseChannelIdentifiers, err := impl.dtResObjectRepository.GetIdentifiersByIds(filterDecodeReq.ReleaseChannelIdsFilters)
		if err != nil {
			impl.logger.Errorw("error encountered in getAppIdsAndEnvIdsFromFilterCriteria", "err", err, "envIdentifierFilters", filterDecodeReq.EnvIdentifierFilters)
			return nil, false, err
		}
		filterDecodeReq.ReleaseChannelIdentifiers = append(filterDecodeReq.ReleaseChannelIdentifiers, releaseChannelIdentifiers...)
	}
	return filterDecodeReq, checkIfEmptyResponseFilters(filterDecodeReq.AppIdentifierFilters, filterDecodeReq.AppIdsFilters, filterDecodeReq.EnvIdentifierFilters, filterDecodeReq.EnvIdsFilters), err
}

// CheckIfEmptyResponseFilters checking this as app and env can be deleted or not existing and we calculate ids from identifiers which can results in empty slice which can result in all results in response
func checkIfEmptyResponseFilters(appIdentifierFilters []string, appIds []int, envIdentifierFilters []string, envIds []int) bool {
	if len(appIdentifierFilters) > 0 && len(appIds) == 0 {
		return true
	}
	if len(envIdentifierFilters) > 0 && len(envIds) == 0 {
		return true
	}
	return false
}

// getAllApplicationDependenciesFromObjectData gets all upstream dependencies from object data json
func getAllApplicationDependenciesFromObjectData(objectData string) []*bean.DtResDepBean {
	applicationFilterCondition := bean.NewDependencyFilterCondition().
		WithFilterByTypes(bean.DevtronResourceDependencyTypeUpstream).
		WithChildInheritance()

	return GetDepsBeanFromObjectData(objectData, applicationFilterCondition)
}

func getPreviousLevelDependency(levelDependencies []*bean.DtResDepBean, currentIndex int) int {
	previousIndex := 0
	for _, levelDependency := range levelDependencies {
		if currentIndex > levelDependency.Index && levelDependency.Index > previousIndex {
			previousIndex = levelDependency.Index
		}
	}
	return previousIndex
}

// getPreviousLevelDependencyForAllDependencies gets a map with levelIndex vs its previous level by sorting the array
func getPreviousLevelDependencyForAllDependencies(levelDependencies []*bean.DtResDepBean) map[int]int {
	levelToPreviousLevelMap := make(map[int]int, len(levelDependencies))
	// sorting level dependencies to maintain stage order and computation, also used in computation
	sort.Slice(levelDependencies, func(i, j int) bool {
		return levelDependencies[i].Index < levelDependencies[j].Index
	})
	// initially setting -1 for first element
	previousLevel := -1
	for i := 0; i < len(levelDependencies); i++ {
		if i > 0 {
			previousLevel = levelDependencies[i-1].Index
		}
		levelToPreviousLevelMap[levelDependencies[i].Index] = previousLevel
	}
	return levelToPreviousLevelMap
}

func (impl *DevtronResourceServiceExtendedImpl) performReleaseResourcePatchOperation(tx *pg.Tx, updatedObjectData string, existingObject *repository.DevtronResourceObject, queries []bean.PatchQuery, userId int32) (*bean.SuccessResponse, string, []string, error) {
	var err error
	auditPaths := make([]string, 0, len(queries))
	patchContainsConfigOrLockStatusQuery := false
	newObjectData := updatedObjectData //will be using this for patches and mutation since we need old object for policy check
	releaseIdAndSchemaId := adapter.BuildIdAndSchemaId(existingObject.Id, existingObject.DevtronResourceSchemaId)
	for _, query := range queries {
		newObjectData, err = impl.patchQueryForReleaseObject(newObjectData, query, releaseIdAndSchemaId)
		if err != nil {
			impl.logger.Errorw("error in patch operation, release track", "err", err, "objectData", "query", query)
			return nil, "", nil, err
		}
		auditPaths = append(auditPaths, bean.PatchQueryPathAuditPathMap[query.Path])
		if query.Path == bean.ReleaseStatusQueryPath || query.Path == bean.ReleaseLockQueryPath {
			patchContainsConfigOrLockStatusQuery = true
		}
	}
	toPerformStatusPolicyCheck := patchContainsConfigOrLockStatusQuery //keeping policy flag different as in future it can not solely depend on status and lock
	if toPerformStatusPolicyCheck {
		newObjectData, err = impl.checkReleasePatchPolicyAndAutoAction(existingObject.ObjectData, newObjectData, releaseIdAndSchemaId)
		if err != nil {
			impl.logger.Errorw("error, checkReleasePatchPolicyAndAutoAction", "err", err, "objectData", updatedObjectData, "newObjectData", newObjectData)
			return nil, "", nil, err
		}
	}
	successResp := adapter.GetSuccessPassResponse()
	if patchContainsConfigOrLockStatusQuery {
		successResp = getSuccessResponseForReleaseStatusOrLockPatch(existingObject.ObjectData, newObjectData)
	}
	return successResp, newObjectData, auditPaths, nil
}

func getSuccessResponseForReleaseStatusOrLockPatch(oldObjectData, newObjectData string) *bean.SuccessResponse {
	oldConfigStatus := bean.ReleaseConfigStatus(gjson.Get(oldObjectData, bean.ReleaseResourceConfigStatusStatusPath).String())
	newConfigStatus := bean.ReleaseConfigStatus(gjson.Get(newObjectData, bean.ReleaseResourceConfigStatusStatusPath).String())
	oldLockStatus := gjson.Get(oldObjectData, bean.ReleaseResourceConfigStatusIsLockedPath).Bool()
	newLockStatus := gjson.Get(newObjectData, bean.ReleaseResourceConfigStatusIsLockedPath).Bool()
	configStatusChanged := oldConfigStatus != newConfigStatus
	lockStatusChanged := oldLockStatus != newLockStatus
	if configStatusChanged && lockStatusChanged {
		return adapter.GetReleaseConfigAndLockStatusChangeSuccessResponse(newConfigStatus, newLockStatus)
	} else if configStatusChanged {
		return adapter.GetReleaseConfigStatusChangeSuccessResponse(newConfigStatus)
	} else if lockStatusChanged {
		return adapter.GetReleaseLockStatusChangeSuccessResponse(newLockStatus)
	}
	return adapter.GetSuccessPassResponse()
}

func getReleaseStatusChangePolicyErrResponse(oldObjectData, newObjectData string) error {
	oldConfigStatus := bean.ReleaseConfigStatus(gjson.Get(oldObjectData, bean.ReleaseResourceConfigStatusStatusPath).String())
	newConfigStatus := bean.ReleaseConfigStatus(gjson.Get(newObjectData, bean.ReleaseResourceConfigStatusStatusPath).String())
	oldDepAppCount, oldDepArtifactStatus := adapter.GetReleaseAppCountAndDepArtifactStatusFromResObjData(oldObjectData)
	if newConfigStatus == bean.ReadyForReleaseConfigStatus && oldConfigStatus == bean.DraftReleaseConfigStatus {
		if oldDepAppCount == 0 {
			return util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.ReleaseStatusReadyForReleaseNoAppErrMessage, bean.InvalidPatchOperation)
		} else if oldDepArtifactStatus != bean.AllSelectedDependencyArtifactStatus { // app present but no or partial images selected
			return util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.ReleaseStatusPatchErrMessage, bean.ReleaseStatusReadyForReleaseNoOrPartialImageErrMessage)
		}
	}
	return util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidPatchOperation, bean.InvalidPatchOperation)
}

func (impl *DevtronResourceServiceExtendedImpl) patchQueryForReleaseObject(objectData string, query bean.PatchQuery, releaseIdAndSchemaId *bean.IdAndSchemaId) (string, error) {
	var err error
	switch query.Path {
	case bean.DescriptionQueryPath:
		objectData, err = helper.PatchResourceObjectDataAtAPath(objectData, bean.ResourceObjectDescriptionPath, query.Value)
	case bean.ReleaseStatusQueryPath:
		objectData, err = impl.patchConfigStatus(objectData, query.Value)
	case bean.ReleaseNoteQueryPath:
		objectData, err = helper.PatchResourceObjectDataAtAPath(objectData, bean.ReleaseResourceObjectReleaseNotePath, query.Value)
	case bean.TagsQueryPath:
		objectData, err = helper.PatchResourceObjectDataAtAPath(objectData, bean.ResourceObjectTagsPath, query.Value)
	case bean.ReleaseLockQueryPath:
		objectData, err = helper.PatchResourceObjectDataAtAPath(objectData, bean.ReleaseResourceConfigStatusIsLockedPath, query.Value)
	case bean.NameQueryPath:
		namePatchValue := ""
		if nameStr, ok := query.Value.(string); !ok || len(nameStr) == 0 {
			namePatchValue = gjson.Get(objectData, bean.ReleaseResourceObjectReleaseVersionPath).String()
		} else {
			namePatchValue = nameStr
		}
		objectData, err = helper.PatchResourceObjectDataAtAPath(objectData, bean.ResourceObjectNamePath, namePatchValue)
	case bean.ReleaseTargetQueryPath:
		interfaceSlice, err := util2.ValidateArrayOfInterface(query.Value)
		if err != nil {
			return "", util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidTargetInstallationFoundMessage, bean.InvalidTargetInstallationFoundMessage)
		}
		if targetIdentifiers, err := util2.ArrayOfInterfaceToStringSlice(interfaceSlice); err != nil {
			return "", util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidTargetInstallationFoundMessage, bean.InvalidTargetInstallationFoundMessage)
		} else {
			objectData, err = impl.patchTargetForRelease(objectData, targetIdentifiers, releaseIdAndSchemaId)
			if err != nil {
				impl.logger.Errorw("error encountered in patchTargetForRelease", "targetIdentifiers", targetIdentifiers, "err", err)
				return "", err
			}
		}

	default:
		err = util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.PatchPathNotSupportedError, bean.PatchPathNotSupportedError)
	}
	return objectData, err
}

func (impl *DevtronResourceServiceImpl) patchTargetInObjectData(objectData string, targetIdentifiers []string, releaseIdAndSchemaId *bean.IdAndSchemaId) (string, error) {
	// get ids and devtronSchemaId from Identifier
	installationSchema, err := impl.dtResReadService.GetInstallationSchema()
	if err != nil {
		impl.logger.Errorw("error encountered in patchTargetForRelease", "err", err)
		return "", err
	}
	ids, err := impl.dtResObjectRepository.GetIdsByIdentifiers(targetIdentifiers, installationSchema.Id)
	if err != nil {
		impl.logger.Errorw("error encountered in patchTargetForRelease", "targetIdentifiers", targetIdentifiers, "err", err)
		return "", err
	}
	// create the target schema and patch through json set.
	entities := adapter.BuildIdAndSchemaIdInBulk(ids, installationSchema.Id)
	releaseTarget := adapter.BuildReleaseTargetSchema(false, entities)
	objectData, err = helper.PatchResourceObjectDataAtAPath(objectData, bean.ReleaseTargetPathKey, releaseTarget)
	if err != nil {
		impl.logger.Errorw("error in patchTargetForRelease", "releaseTarget", releaseTarget, "err", err)
		return "", err
	}
	return objectData, nil
}

func (impl *DevtronResourceServiceExtendedImpl) patchTargetForRelease(objectData string, targetIdentifiers []string, releaseIdAndSchemaId *bean.IdAndSchemaId) (string, error) {
	objectData, err := impl.patchTargetInObjectData(objectData, targetIdentifiers, releaseIdAndSchemaId)
	if err != nil {
		impl.logger.Errorw("error in patchTargetForRelease", "releaseIdAndSchemaId", releaseIdAndSchemaId, "err", err)
		return "", err
	}
	// change the status based on current state by calculating it live.
	objectData, err = impl.markRolloutStatusBasedOnTargetInObjectData(objectData, releaseIdAndSchemaId)
	if err != nil {
		impl.logger.Errorw("error in patchTargetForRelease", "releaseIdAndSchemaId", releaseIdAndSchemaId, "err", err)
		return "", err
	}
	return objectData, nil
}

func (impl *DevtronResourceServiceImpl) patchTargetAndSaveObj(targetIdentifiers []string, releaseIdAndSchemaId *bean.IdAndSchemaId) (string, error) {
	releaseObject, err := impl.dtResObjectRepository.FindByIdAndSchemaId(releaseIdAndSchemaId.Id, releaseIdAndSchemaId.DevtronResourceSchemaId)
	if err != nil {
		impl.logger.Errorw("error encountered in patchTargetAndSaveObj", "err", err)
		return "", err
	}
	updatedObjectData, err := impl.patchTargetInObjectData(releaseObject.ObjectData, targetIdentifiers, releaseIdAndSchemaId)
	if err != nil {
		impl.logger.Errorw("error encountered in patchTargetAndSaveObj", "err", err)
		return "", err
	}
	releaseObject.ObjectData = updatedObjectData
	releaseObject.UpdatedBy = 1
	releaseObject.UpdatedOn = time.Now()
	err = impl.dtResObjectRepository.Update(nil, releaseObject)
	if err != nil {
		impl.logger.Errorw("error encountered in patchTargetAndSaveObj", "err", err)
		return "", err
	}
	return updatedObjectData, nil
}

func (impl *DevtronResourceServiceImpl) patchConfigStatus(objectData string, value interface{}) (string, error) {
	data, err := json.Marshal(value)
	if err != nil {
		impl.logger.Errorw("error encountered in patchConfigStatus", "value", value, "err", err)
		return objectData, err
	}
	var configStatus bean.ConfigStatus
	err = json.Unmarshal(data, &configStatus)
	if err != nil {
		impl.logger.Errorw("error encountered in patchConfigStatus", "value ", value, "err", err)
		return objectData, err
	}
	releaseConfigSchema := adapter.BuildConfigStatusSchemaData(&configStatus)
	if (releaseConfigSchema.Status == bean.HoldReleaseConfigStatus.ToString() || releaseConfigSchema.Status == bean.RescindReleaseConfigStatus.ToString()) &&
		len(releaseConfigSchema.Comment) == 0 {
		return objectData, util.GetApiErrorAdapter(http.StatusBadRequest, "400",
			bean.ReleaseStatusHoldOrRescindPatchNoCommentErrMessage, bean.ReleaseStatusHoldOrRescindPatchNoCommentErrMessage)
	}
	objectData, err = helper.PatchResourceObjectDataAtAPath(objectData, bean.ReleaseResourceConfigStatusCommentPath, releaseConfigSchema.Comment)
	if err != nil {
		return objectData, err
	}
	objectData, err = helper.PatchResourceObjectDataAtAPath(objectData, bean.ReleaseResourceConfigStatusStatusPath, releaseConfigSchema.Status)

	return objectData, err
}

func (impl *DevtronResourceServiceImpl) validateReleaseDelete(object *repository.DevtronResourceObject) (bool, error) {
	if object == nil || object.Id == 0 {
		return false, util.GetApiErrorAdapter(http.StatusNotFound, "404", bean.ResourceDoesNotExistMessage, bean.ResourceDoesNotExistMessage)
	}
	//getting release rollout status
	rolloutStatus := bean.ReleaseRolloutStatus(gjson.Get(object.ObjectData, bean.ReleaseResourceRolloutStatusPath).String())
	return !rolloutStatus.IsPartiallyDeployed() &&
		!rolloutStatus.IsCompletelyDeployed(), nil
}

func (impl *DevtronResourceServiceImpl) updateRolloutStatusInExistingObject(existingObject *repository.DevtronResourceObject,
	rolloutStatus bean.ReleaseRolloutStatus, triggeredBy int32, triggeredTime time.Time) error {
	newObjectData, err := helper.PatchResourceObjectDataAtAPath(existingObject.ObjectData, bean.ReleaseResourceRolloutStatusPath, rolloutStatus)
	if err != nil {
		impl.logger.Errorw("error encountered in updateRolloutStatusInExistingObject", "err", err)
		return err
	}
	//updating final object data in resource object
	existingObject.ObjectData = newObjectData
	existingObject.UpdatedBy = triggeredBy
	existingObject.UpdatedOn = triggeredTime
	// made it not transaction as we need to commit transaction for cd workflow runners as event has already been sent to nats.
	err = impl.dtResObjectRepository.Update(nil, existingObject)
	if err != nil {
		// made this non-blocking as only roll out status change was not completed but deployment has been triggered as events has been published
		impl.logger.Errorw("error encountered in executeDeploymentsForDependencies", "err", err, "newObjectData", newObjectData)
	}
	impl.dtResObjectAuditService.SaveAudit(existingObject, repository.AuditOperationTypeUpdate, []string{bean.ReleaseResourceRolloutStatusPath})
	return nil
}

func (impl *DevtronResourceServiceImpl) setDefaultValueAndValidateForReleaseClone(req *bean.DtResObjCloneReqInternalBean,
	parentConfig *bean.ResourceIdentifier) error {
	err := helper.CheckIfReleaseVersionIsValid(req.Overview.ReleaseVersion)
	if err != nil {
		return err
	}
	identifier, err := impl.getIdentifierForReleaseByParentDescriptorBean(req.Overview.ReleaseVersion, parentConfig)
	if err != nil {
		impl.logger.Errorw("error, getIdentifierForReleaseByParentDescriptorBean", "err", err, "parentConfig", parentConfig, "releaseVersion", req.Overview.ReleaseVersion)
		return err
	}
	req.Identifier = identifier
	if len(req.Name) == 0 {
		req.Name = req.Overview.ReleaseVersion
	}
	return nil
}

func (impl *DevtronResourceServiceImpl) getPathUpdateMapForReleaseClone(req *bean.DtResObjCloneReqInternalBean,
	createdOn time.Time) (map[string]interface{}, error) {
	userObj, err := impl.userRepository.GetById(req.UserId)
	if err != nil {
		impl.logger.Errorw("error in getting user", "err", err, "userId", req.UserId)
		return nil, err
	}
	replaceDataMap := map[string]interface{}{
		bean.ResourceObjectIdPath:                     req.Id,                      //reset Id
		bean.ResourceObjectIdentifierPath:             req.Identifier,              //reset identifier
		bean.ResourceObjectNamePath:                   req.Name,                    //reset name
		bean.ResourceObjectTagsPath:                   req.Overview.Tags,           //reset tags
		bean.ReleaseResourceObjectReleaseVersionPath:  req.Overview.ReleaseVersion, //reset releaseVersion
		bean.ResourceObjectDescriptionPath:            req.Overview.Description,    //reset description
		bean.ReleaseResourceObjectReleaseNotePath:     "",                          //reset note
		bean.ResourceObjectCreatedByIdPath:            userObj.Id,                  //reset created by
		bean.ResourceObjectCreatedByNamePath:          userObj.EmailId,
		bean.ResourceObjectCreatedOnPath:              createdOn,                       //reset created on
		bean.ReleaseResourceConfigStatusPath:          bean.DefaultReleaseConfigStatus, //reset config status
		bean.ReleaseResourceRolloutStatusPath:         bean.DefaultRolloutStatus,       //reset rollout status
		bean.ReleaseResourceObjectFirstReleasedOnPath: time.Time{},                     //reset first release on time
	}
	return replaceDataMap, nil
}

func (impl *DevtronResourceServiceImpl) getReleaseOverviewDescriptorBeanFromObject(object *repository.DevtronResourceObject) *bean.DtResObjOverviewDescBean {
	resp := &bean.DtResObjOverviewDescBean{
		DtResObjDescApiBean: &bean.DtResObjDescApiBean{
			Kind:        bean.DevtronResourceRelease.ToString(),
			Version:     bean.DevtronResourceVersionAlpha1.ToString(),
			OldObjectId: object.Id,
			Identifier:  object.Identifier,
			Name:        gjson.Get(object.ObjectData, bean.ResourceObjectNamePath).String(),
		},
	}
	if gjson.Get(object.ObjectData, bean.ResourceObjectOverviewPath).Exists() {
		resp.ResOverview = &bean.ResOverview{
			ReleaseVersion: gjson.Get(object.ObjectData, bean.ReleaseResourceObjectReleaseVersionPath).String(),
		}
	}
	return resp
}

func (impl *DevtronResourceServiceImpl) getDepOptionsForRelease(req *bean.DtResObjDescApiBean, query *apiBean.GetDependencyOptionsQueryParams) ([]*bean.DepOptions, error) {
	var filterByWorkflow *bean.FilterCriteriaDecoder
	releaseTrackObject, err := impl.getParentResourceObjectByIdOrIdentifier(req, query)
	if err != nil {
		impl.logger.Errorw("error encountered in getDepOptionsForRelease", "id", query.Id, "identifier", query.Identifier, "err", err)
		return nil, err
	}
	appIdentifierToPropagateOnChildMap := impl.getAppIdentifierWithToPropagateOnChildMap(releaseTrackObject.ObjectData)
	appNames := maps.Keys(appIdentifierToPropagateOnChildMap)

	response := make([]*bean.DepOptions, 0, len(appNames))
	if !query.FetchWithChildInheritance {
		projectWiseAppList, err := impl.findProjectWiseAppsWithToPropagateOnChild(appNames, appIdentifierToPropagateOnChildMap)
		if err != nil {
			impl.logger.Errorw("error encountered in getDepOptionsForRelease", "err", err)
			return nil, err
		}
		response = append(response, &bean.DepOptions{
			ResourceKind:       helper.BuildExtendedResourceKindUsingKindAndSubKind(bean.DevtronResourceApplication.ToString(), bean.DevtronResourceDevtronApplication.ToString()),
			ResourceVersion:    bean.DevtronResourceVersion1,
			ProjectWiseAppList: projectWiseAppList,
		})
	} else {
		return nil, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.NotSupportedRequestDepOptionRelease, bean.NotSupportedRequestDepOptionRelease)
	}

	for _, filterCriteria := range query.FilterCriteria {
		decodedFilter, err := util3.DecodeFilterCriteriaString(filterCriteria, false)
		if err != nil {
			impl.logger.Errorw("error, DecodeFilterCriteriaString", "err", err)
			return nil, err
		}
		if decodedFilter.Kind == bean.DevtronResourceAppWorkflow {
			filterByWorkflow = decodedFilter
		} else {
			return nil, fmt.Errorf("only appWorkflow filtering supported")
		}
	}
	if filterByWorkflow != nil && filterByWorkflow.Type == bean.Id {
		var err error
		//get environment by cluster
		envIds, err := impl.pipelineRepository.FindCDEnvIdsByAppWorkflowId(filterByWorkflow.ValueInt)
		if err != nil {
			impl.logger.Errorw("error, FindCDEnvIdsByAppWorkflowId", "workflowId", filterByWorkflow.ValueInt, "err", err)
			return nil, err
		}
		envs, err := impl.envService.FindByIdsAndNames(envIds, nil)
		if err != nil {
			impl.logger.Errorw("error, FindByIdsAndNames", "envIds", envIds, "err", err)
			return nil, err
		}
		installationMappingMap, err := impl.dtResObjRelationReadService.GetEnvIdVsInstallationConfigMap()
		if err != nil {
			impl.logger.Errorw("error, GetEnvIdVsInstallationConfigMap", "err", err)
			return nil, err
		}
		for _, env := range envs {
			response = append(response, &bean.DepOptions{
				ResourceKind:        bean.DevtronResourceEnvironment,
				ResourceVersion:     bean.DevtronResourceVersion1,
				Id:                  env.Id,
				Identifier:          env.Environment,
				InstallationMapping: adapter.BuildInstallationMappingConfigFromInternalBean(installationMappingMap[env.Id]),
			})
		}
	}
	return response, nil
}

func (impl *DevtronResourceServiceImpl) getAppIdentifierWithToPropagateOnChildMap(objectData string) map[string]bool {
	applicationFilterCondition := bean.NewDependencyFilterCondition().
		WithFilterByTypes(bean.DevtronResourceDependencyTypeUpstream)
	applicationDependencies := GetDepsBeanFromObjectData(objectData, applicationFilterCondition)
	appIdentifierToPropagateOnChildMap := make(map[string]bool, len(applicationDependencies))
	for _, dep := range applicationDependencies {
		appIdentifierToPropagateOnChildMap[dep.Identifier] = dep.ToPropagateOnChild
	}
	return appIdentifierToPropagateOnChildMap
}

func (impl *DevtronResourceServiceImpl) getParentResourceObjectByIdOrIdentifier(req *bean.DtResObjDescApiBean, query *apiBean.GetDependencyOptionsQueryParams) (*repository.DevtronResourceObject, error) {
	// sending user id as 1 as it is GET req not being used anywhere,
	internalDescriptorBean := adapter.BuildIntDescBeanFromApiBean(req, 1)
	adapter.SetIdAndIdentifierInInternalDescriptorBean(internalDescriptorBean, query.Id, query.Identifier)
	_, existingResourceObject, err := impl.getResourceSchemaAndExistingObject(internalDescriptorBean)
	if err != nil {
		return nil, err
	}
	_, releaseTrackObject, err := impl.getParentConfigVariablesFromDeps(existingResourceObject.ObjectData)
	if err != nil {
		impl.logger.Errorw("error in getParentResourceObjectByIdOrIdentifier", "err", err, "id", internalDescriptorBean.Id)
		return nil, err
	}
	return releaseTrackObject, err

}

func (impl *DevtronResourceServiceImpl) findProjectWiseAppsWithToPropagateOnChild(appNames []string, appNameToPropagateOnChildMap map[string]bool) ([]*bean.TeamAppDepOptionBean, error) {
	var response []*bean.TeamAppDepOptionBean
	if len(appNames) > 0 {
		appNameVsAppMap, err := impl.appCrudService.FindLatestAppWithProjectByNamesIncludingDeleted(appNames)
		if err != nil {
			impl.logger.Errorw("error encountered in findProjectWiseAppsWithToPropagateOnChild", "appIds", appNames, "err", err)
			return nil, err
		}
		response = adapter.GetResponseForProjectWiseAppList(maps.Values(appNameVsAppMap), appNameToPropagateOnChildMap)
	}
	return response, nil
}

// getDepsOffendingStateForRelease get release deps current offending state.
// STEP 1: Get Current Deps for parent (release-track).
// STEP 2: Get Current Deps for release.
// STEP 3: For All Deps(release and release-track) fetch all apps including deleted.
// STEP 4: Calculate Diff in deps for release and release-trac.
// STEP 5: Create DepOffendingStateStatus from devtron_resource_object_audit according to the diff we calculated.
func (impl *DevtronResourceServiceImpl) getDepsOffendingStateForRelease(drResObj *repository.DevtronResourceObject, query *apiBean.GetDepsOffendingQueryParams, userId int32) (response *bean.DepOffendingGetApiBean, err error) {
	status, exist := getReleaseStatusFromObjectData(drResObj.ObjectData)
	if exist && (status == bean.DraftReleaseStatus || status == bean.ReadyForReleaseStatus) {
		var allAppNames []string
		_, releaseTrackObject, err := impl.getParentConfigVariablesFromDeps(drResObj.ObjectData)
		if err != nil {
			impl.logger.Errorw("error in getDepsOffendingStateForRelease", "err", err, "id", drResObj.Id)
			return nil, err
		}
		releaseTrackAppDeps, releaseAppDeps, rTAppIdentifierVsToPropagateOnChild,
			releaseAppIdentifierVsPresentApp, err := impl.getReleaseAndReleaseTrackDepsPopulatingIdentifier(releaseTrackObject.ObjectData, drResObj.ObjectData)
		if err != nil {
			impl.logger.Errorw("error encountered in getDepsOffendingStateForRelease", "id", drResObj.Id, "err", err)
			return nil, err
		}
		allAppNames = append(allAppNames, maps.Keys(rTAppIdentifierVsToPropagateOnChild)...)
		allAppNames = append(allAppNames, maps.Keys(releaseAppIdentifierVsPresentApp)...)

		depStateStatus := make(map[string]*bean.DepOffendingStateStatus)
		if len(allAppNames) > 0 {
			allApps, err := impl.appRepository.FindByNamesIncludingDeleted(allAppNames, helper2.CustomApp)
			if err != nil {
				impl.logger.Errorw("error encountered in getDepsOffendingStateForRelease", "err", err, "allAppNames", allAppNames)
				return nil, err
			}
			appNameVsApp := getAppNameVsAppMap(allApps)
			userIdVsUserEmailMap, err := impl.userService.GetUserIdVsUserEmailMapForAllUsers()
			if err != nil {
				impl.logger.Errorw("error encountered in getDepsOffendingStateForRelease", "err", err)
				return nil, err
			}
			depStateStatus, err = calculateAddedAndRemovedDiffBetweenRtDepsAndReleaseDeps(releaseTrackAppDeps, releaseAppDeps, releaseAppIdentifierVsPresentApp, rTAppIdentifierVsToPropagateOnChild, appNameVsApp, userIdVsUserEmailMap)
			if err != nil {
				impl.logger.Errorw("error encountered in getDepsOffendingStateForRelease", "err", err, "releaseTrackAppDeps", releaseTrackAppDeps, "releaseAppDeps", releaseAppDeps)
				return nil, err
			}
			if len(depStateStatus) > 0 {
				// get audit and update values in depStateStatus
				err = impl.updateAuditInDepStateForAddedAndRemovedDep(releaseTrackObject.Id, depStateStatus, userIdVsUserEmailMap)
				if err != nil {
					impl.logger.Errorw("error encountered in getDepsOffendingStateForRelease", "err", err)
					return nil, err
				}
			}
			response = &bean.DepOffendingGetApiBean{
				DepStatus: maps.Values(depStateStatus),
			}
		}
	}
	if response == nil {
		// sending empty array
		response = &bean.DepOffendingGetApiBean{
			DepStatus: make([]*bean.DepOffendingStateStatus, 0),
		}
	}

	return response, nil

}

func (impl *DevtronResourceServiceImpl) updateAuditInDepStateForAddedAndRemovedDep(releaseTrackId int, depStateStatus map[string]*bean.DepOffendingStateStatus, userIdVsUserEmailMap map[int32]string) error {
	allAudits, err := impl.dtResObjAuditRepository.FindAllByResObjIdOrderById(releaseTrackId, []repository.AuditOperationType{repository.AuditOperationTypeUpdate, repository.AuditOperationTypePatch})
	if err != nil {
		impl.logger.Errorw("error encountered in updateAuditInDepStateForAddedAndRemovedDep", "releaseTrackId", releaseTrackId, "err", err)
		return err
	}
	for _, status := range maps.Values(depStateStatus) {
		identifier := status.Identifier
		if len(allAudits) == 1 && (status.State == bean.AddedDepState || status.State == bean.RemovedDepState) {
			status.PerformedAtTime = allAudits[0].UpdatedOn
			status.UserEmail = userIdVsUserEmailMap[allAudits[0].UpdatedBy]
			continue
		}
		if status.State == bean.AddedDepState {
			// will iterate from back and calculate the whose in previous audit it was not present and present in current dep , that will be latest audit
			for i := len(allAudits) - 1; i >= 1; i-- {
				currentAudit := allAudits[i]
				previousAudit := allAudits[i-1]
				currentAppDeps := getAppDepsBasicDataFromObjectData(currentAudit.ObjectData)
				previousAppDeps := getAppDepsBasicDataFromObjectData(previousAudit.ObjectData)
				presentInPrevious := false
				presentInCurrent := false
				for _, dep := range currentAppDeps {
					if dep.Identifier == identifier {
						presentInCurrent = true
						break
					}
				}
				for _, dep := range previousAppDeps {
					if dep.Identifier == identifier {
						presentInPrevious = true
						break
					}
				}
				if !presentInPrevious && presentInCurrent {
					status.PerformedAtTime = currentAudit.UpdatedOn
					status.UserEmail = userIdVsUserEmailMap[currentAudit.UpdatedBy]
					break
				}
				// corner case handling where it was added first time
				if i == 1 && presentInPrevious {
					status.PerformedAtTime = currentAudit.UpdatedOn
					status.UserEmail = userIdVsUserEmailMap[currentAudit.UpdatedBy]
					break
				}
			}
		} else if status.State == bean.RemovedDepState {
			for i := len(allAudits) - 1; i >= 1; i-- {
				currentAudit := allAudits[i]
				previousAudit := allAudits[i-1]
				currentAppDeps := getAppDepsBasicDataFromObjectData(currentAudit.ObjectData)
				previousAppDeps := getAppDepsBasicDataFromObjectData(previousAudit.ObjectData)
				presentInPrevious := false
				presentInCurrent := false
				for _, dep := range currentAppDeps {
					if dep.Identifier == identifier {
						presentInCurrent = true
					}
				}
				for _, dep := range previousAppDeps {
					if dep.Identifier == identifier {
						presentInPrevious = true
					}
				}
				if presentInPrevious && !presentInCurrent {
					status.PerformedAtTime = currentAudit.UpdatedOn
					status.UserEmail = userIdVsUserEmailMap[currentAudit.UpdatedBy]
					break
				}
			}
		}
	}
	return nil

}

func getAppDepsBasicDataFromObjectData(objectData string) []*bean.DtResDepBean {
	return GetDepsBasicBeanFromObjectData(objectData, []bean.DtResDepType{bean.DevtronResourceDependencyTypeUpstream})
}

func getAppNameVsAppMap(allApps []*appRepository.App) map[string]*appRepository.App {
	appNameVsApp := make(map[string]*appRepository.App)
	for _, app := range allApps {
		appNameVsApp[app.AppName] = app
	}
	return appNameVsApp
}

func calculateAddedAndRemovedDiffBetweenRtDepsAndReleaseDeps(releaseTrackApps, releaseApps []*bean.DtResDepBean,
	releaseAppIdentifierVsPresentApp, rTAppIdentifierVsToPropagateOnChild map[string]bool,
	appNameVsApp map[string]*appRepository.App, userIdVsUserEmailMap map[int32]string) (map[string]*bean.DepOffendingStateStatus, error) {
	depStateStatus := make(map[string]*bean.DepOffendingStateStatus, 0)
	for _, rtApp := range releaseTrackApps {
		appIdentifier := rtApp.Identifier
		presentInRelease := releaseAppIdentifierVsPresentApp[appIdentifier]
		if val, ok := appNameVsApp[appIdentifier]; ok && presentInRelease && !val.Active {
			depStateStatus[appIdentifier] = adapter.BuildDepStateStatus(appIdentifier, val.Id, bean.DeletedDepState, userIdVsUserEmailMap[val.UpdatedBy], val.UpdatedOn)
		} else if ok && rTAppIdentifierVsToPropagateOnChild[appIdentifier] && !presentInRelease && val.Active {
			depStateStatus[appIdentifier] = adapter.BuildDepStateStatus(appIdentifier, val.Id, bean.AddedDepState, "", time.Now())
		} else if !ok {
			return nil, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.AppNotFoundMessage, bean.AppNotFoundMessage)
		}
	}
	for _, rApp := range releaseApps {
		appIdentifier := rApp.Identifier
		_, presentInRT := rTAppIdentifierVsToPropagateOnChild[appIdentifier]
		if val, ok := appNameVsApp[appIdentifier]; ok && !val.Active {
			depStateStatus[appIdentifier] = adapter.BuildDepStateStatus(appIdentifier, val.Id, bean.DeletedDepState, userIdVsUserEmailMap[val.UpdatedBy], val.UpdatedOn)
		} else if ok && !presentInRT {
			depStateStatus[appIdentifier] = adapter.BuildDepStateStatus(appIdentifier, val.Id, bean.RemovedDepState, "", time.Now())
		} else if !ok {
			return nil, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.AppNotFoundMessage, bean.AppNotFoundMessage)
		}
	}
	return depStateStatus, nil
}

func (impl *DevtronResourceServiceImpl) getReleaseAndReleaseTrackDepsPopulatingIdentifier(releaseTrackObjectData, releaseObjectData string) ([]*bean.DtResDepBean, []*bean.DtResDepBean, map[string]bool, map[string]bool, error) {
	releaseTrackApps := getAppDepsBasicDataFromObjectData(releaseTrackObjectData)
	releaseApps := getAppDepsBasicDataFromObjectData(releaseObjectData)
	rTAppIdentifierVsToPropagateOnChild := make(map[string]bool, len(releaseTrackApps))
	releaseAppIdentifierVsPresentApp := make(map[string]bool, len(releaseApps))
	// getting identifiers for releases in  run time as we keep id in in release
	appIdVsAppNameMap, err := impl.getIdentifiersForRelease(releaseApps)
	if err != nil {
		impl.logger.Errorw("error encountered in getReleaseAndReleaseTrackDepsPopulatingIdentifier", "err", err)
		return nil, nil, nil, nil, err
	}
	for _, dep := range releaseTrackApps {
		rTAppIdentifierVsToPropagateOnChild[dep.Identifier] = dep.ToPropagateOnChild
	}
	for _, dep := range releaseApps {
		if val, ok := appIdVsAppNameMap[dep.OldObjectId]; ok {
			dep.Identifier = val
		} else {
			impl.logger.Debugw("error encountered identifier not found", "id", dep.OldObjectId)
		}
		releaseAppIdentifierVsPresentApp[dep.Identifier] = true
	}
	return releaseTrackApps, releaseApps, rTAppIdentifierVsToPropagateOnChild, releaseAppIdentifierVsPresentApp, nil
}

func (impl *DevtronResourceServiceImpl) getIdentifiersForRelease(deps []*bean.DtResDepBean) (map[int]string, error) {
	ids := make([]int, 0, len(deps))
	for _, dep := range deps {
		ids = append(ids, dep.OldObjectId)
	}
	return impl.appCrudService.FindAppIdVsAppNameIncludingDeleted(ids)
}

func (impl *DevtronResourceServiceImpl) getFilteredDepsWithMetaData(dependenciesOfParent []*bean.DtResDepJsonBean, filterTypes []bean.DtResDepType, dependencyFilterKeys []bean.FilterKeyObject, metadataObj *bean.DependencyMetaDataBean, oldObjectIdToActiveBeanMap map[int]*bean.IdNameAndActiveBean) []*bean.DtResDepBean {
	filteredDependenciesOfParent := make([]*bean.DtResDepBean, 0, len(dependenciesOfParent))
	for _, dependencyOfParent := range dependenciesOfParent {
		if slices.Contains(filterTypes, dependencyOfParent.TypeOfDependency) {
			if pass := validateDepFilterCondition(dependencyOfParent, dependencyFilterKeys); !pass {
				continue
			}
			dependencyApiBean := adapter.BuildDepBeanFromInternalBean(dependencyOfParent)
			dependencyApiBean.Metadata = impl.getMetadataForAnyDep(dependencyApiBean.DevtronResourceSchemaId,
				dependencyApiBean.OldObjectId, metadataObj)
			activeBean := oldObjectIdToActiveBeanMap[dependencyApiBean.OldObjectId]
			if activeBean != nil {
				dependencyApiBean.Identifier = activeBean.Name
				dependencyApiBean.Deleted = !activeBean.Active
			}

			filteredDependenciesOfParent = append(filteredDependenciesOfParent, dependencyApiBean)
		}
	}
	return filteredDependenciesOfParent
}

func (impl *DevtronResourceServiceImpl) processDepInfoVsPatchOperationMap(allDependencyInfoForApplication map[*bean.DepInfo][]patchQuery.Operation) ([]*bean.IdentifierAndSchemaIdBean, []*bean.IdentifierAndSchemaIdBean, error) {
	toRemoveDeps := make([]*bean.IdentifierAndSchemaIdBean, 0, len(allDependencyInfoForApplication))
	toAddDeps := make([]*bean.IdentifierAndSchemaIdBean, 0, len(allDependencyInfoForApplication))
	applicationSchema, err := impl.dtResReadService.GetDtApplicationSchema()
	if err != nil {
		impl.logger.Errorw("error encountered in processDepInfoVsPatchOperationMap", "err", err)
		return nil, nil, err
	}
	for depInfo, operations := range allDependencyInfoForApplication {
		for _, operation := range operations {
			if operation == patchQuery.Add {
				toAddDeps = append(toAddDeps, adapter.BuildIdentifierAndSchemaIdBean(depInfo.Identifier, applicationSchema.Id))
			} else if operation == patchQuery.Remove {
				toRemoveDeps = append(toRemoveDeps, adapter.BuildIdentifierAndSchemaIdBean(depInfo.Identifier, applicationSchema.Id))
			}
		}
	}
	return toRemoveDeps, toAddDeps, nil
}

// getMapOfAppIdAndAppNameFromReleaseAndReleaseTrackDeps get active appNameVsAppId as we only check for active apps in release track(source of truth ) from release
// inactive appIdVsAppName as release can have app id deleted(app) to delete
func (impl *DevtronResourceServiceImpl) getMapOfAppIdAndAppNameFromReleaseAndReleaseTrackDeps(releaseDeps, releaseTrackDeps []*bean.DtResDepJsonBean) (map[int]string, map[string]int, error) {
	appIds := make([]int, 0, len(releaseDeps))
	appNames := make([]string, 0, len(releaseTrackDeps))
	for _, dep := range releaseDeps {
		if dep.TypeOfDependency == bean.DevtronResourceDependencyTypeUpstream {
			appIds = append(appIds, dep.Id)
		}
	}
	for _, dep := range releaseTrackDeps {
		if dep.TypeOfDependency == bean.DevtronResourceDependencyTypeUpstream {
			appNames = append(appNames, dep.Identifier)
		}
	}
	appNameVsAppIdMap, _, err := impl.appCrudService.FindActiveAppNameVsAppId(nil, appNames)
	if err != nil {
		impl.logger.Errorw("error encountered in getMapOfAppIdAndAppNameFromReleaseAndReleaseTrackDeps", "appNames", appNames, "err", err)
		return nil, nil, err
	}
	appIdVsAppNameMap, err := impl.appCrudService.FindAppIdVsAppNameIncludingDeleted(appIds)
	if err != nil {
		impl.logger.Errorw("error encountered in getMapOfAppIdAndAppNameFromReleaseAndReleaseTrackDeps", "appIds", appIds, "err", err)
		return nil, nil, err
	}
	return appIdVsAppNameMap, appNameVsAppIdMap, nil

}

func (impl *DevtronResourceServiceImpl) getUpdatedDepsAddingParticularWithId(devtronResourceSchemaId int, objectData string, dependencyInfo *bean.DepInfo, appSchemaId int) ([]*bean.DtResDepJsonBean, error) {
	existingDependencies, err := impl.getDepsInternalBeanInObjectDataFromJsonString(devtronResourceSchemaId, objectData, false)
	if err != nil {
		impl.logger.Errorw("error, getDependenciesInObjectDataFromJsonString", "err", err, "devtronResourceSchemaId", devtronResourceSchemaId)
		return nil, err
	}
	mapOfIdAndSchemaKeyVsIndex := make(map[string]int, len(existingDependencies))
	depsIds := make([]int, 0, len(existingDependencies))
	var parentDepIndex int
	isLevelPropagatedFromParent := true
	finalDependencies := make([]*bean.DtResDepJsonBean, 0)
	for i, dep := range existingDependencies {
		if dep.Id == dependencyInfo.Id && dep.DevtronResourceSchemaId == appSchemaId {
			return nil, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.AppAlreadyPresentMessage, bean.AppAlreadyPresentMessage)
		}
		if dep.TypeOfDependency == bean.DevtronResourceDependencyTypeParent {
			parentDepIndex = i
		}
		if dep.TypeOfDependency == bean.DevtronResourceDependencyTypeLevel {
			// every level will have as true value if level is propagated from release-track
			isLevelPropagatedFromParent = dep.IsLevelPropagatedFromParent
		}
		mapOfIdAndSchemaKeyVsIndex[helper.GetKeyForADependencyMap(dep.Id, dep.DevtronResourceSchemaId)] = i
		depsIds = append(depsIds, dep.Id)
	}
	if isLevelPropagatedFromParent {
		appNameVsAppIdMap, _, err := impl.appCrudService.FindActiveAppNameVsAppId(depsIds, nil)
		if err != nil {
			impl.logger.Errorw("error encountered in getUpdatedDepsAddingParticularWithId", "err", err)
			return nil, err
		}
		mapOfLevelIndexVsAllDependentDeps, mapOfLevelIndexVsLevelDep, err := impl.getReleaseTrackDependenciesWithLevelMapping(objectData)
		if err != nil {
			impl.logger.Errorw("error encountered in getUpdatedDepsAddingParticularWithId", "err", err, "devtronResourceSchemaId", devtronResourceSchemaId)
			return nil, err
		}
		finalDependencies = impl.getDepsForReleaseFromReleaseTrackOrder(mapOfLevelIndexVsAllDependentDeps, appNameVsAppIdMap, mapOfIdAndSchemaKeyVsIndex,
			existingDependencies, mapOfLevelIndexVsLevelDep, dependencyInfo, parentDepIndex)
	} else {
		finalDependencies, err = impl.getUpdatedDepsForReleaseWithoutReleaseOrdering(existingDependencies, dependencyInfo)
		if err != nil {
			impl.logger.Errorw("error encountered in getUpdatedDepsAddingParticularWithId", "err", err)
			return nil, err
		}
	}
	return finalDependencies, nil
}

func validateAppToBeAddedFromReleaseTrackExist(appNameVsAppIdMap map[string]int, toAddApps []*bean.IdentifierAndSchemaIdBean) bool {
	for _, a := range toAddApps {
		appName := a.Identifier
		if _, ok := appNameVsAppIdMap[appName]; !ok {
			return false
		}
	}
	return true
}

func (impl *DevtronResourceServiceImpl) updateReleaseDepsOnPatchingApplication(existingReleaseObject *repository.DevtronResourceObject, existingReleaseDeps, existingRTDeps []*bean.DtResDepJsonBean, toRemoveApps, toAddApps []*bean.IdentifierAndSchemaIdBean) ([]*bean.DtResDepJsonBean, []*repository.DevtronResourceObjectDependencyRelations, error) {
	appIdVsAppName, appNameVsAppId, err := impl.getMapOfAppIdAndAppNameFromReleaseAndReleaseTrackDeps(existingReleaseDeps, existingRTDeps)
	if err != nil {
		impl.logger.Errorw("error encountered in updateReleaseDepsOnPatchingApplication", "existingReleaseDeps", existingReleaseDeps, "existingRTDeps", existingRTDeps, "err", err)
		return nil, nil, err
	}
	// validate for req whether toAddApps exist in release-track.
	validated := validateAppToBeAddedFromReleaseTrackExist(appNameVsAppId, toAddApps)
	if !validated {
		impl.logger.Errorw("error encountered in updateReleaseDepsOnPatchingApplication", "err", err, "toAddApps", toAddApps)
		return nil, nil, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.AppNotFoundMessage, bean.AppNotFoundMessage)
	}

	releaseLevelIndexVsDeps, parentDepOfRelease, isLevelPropagatedFromParent, err := getReleaseIndexDepsWithReleaseData(existingReleaseDeps)
	if err != nil {
		impl.logger.Errorw("error encountered in updateReleaseDepsOnPatchingApplication", "err", err)
		return nil, nil, err
	}
	// if release track is not followed we directly do operations on that level with release existing deps.
	if !isLevelPropagatedFromParent {
		return impl.updateReleaseDepsWithoutRtOrdering(existingReleaseObject, existingReleaseDeps, toRemoveApps, toAddApps, parentDepOfRelease, appIdVsAppName, appNameVsAppId)
	}

	releaseTrackLevelIndexVsDeps, _, err := getLevelIndexVsAllDeps(existingRTDeps, appNameVsAppId)
	if err != nil {
		impl.logger.Errorw("error encountered in updateReleaseDepsOnPatchingApplication", "err", err)
		return nil, nil, err
	}
	allReleaseLevels := maps.Keys(releaseLevelIndexVsDeps)
	allRtLevels := maps.Keys(releaseTrackLevelIndexVsDeps)
	// sorting both release track level and release level
	sort.Slice(allReleaseLevels, func(i, j int) bool {
		return allReleaseLevels[i] < allReleaseLevels[j]
	})
	sort.Slice(allRtLevels, func(i, j int) bool {
		return allRtLevels[i] < allRtLevels[j]
	})
	// ordering changes are not handled here, there can be multiple cases where stage order of apps have been changed,have not handled every case due
	// to order state (in ui) will come in the future, automatically syncing with rt will create confusion to user about what happened.
	// stage order of existing dep in release is maintained
	targetLevel := 0
	releaseIndexPointer := 0
	finalLevelVsDeps := make(map[int][]*bean.DtResDepJsonBean)
	mapOfReleaseLevelArrayIndexVsProcessed := make(map[int]bool, len(allReleaseLevels))
	for _, level := range allRtLevels {
		rtDepsAtCurrentLvl := releaseTrackLevelIndexVsDeps[level]
		var releaseDepsAtCurrentLvl []*bean.DtResDepJsonBean
		if releaseIndexPointer < len(allReleaseLevels) {
			releaseDepsAtCurrentLvl = releaseLevelIndexVsDeps[allReleaseLevels[releaseIndexPointer]]
		}
		appExistInReleaseAtRtLevel := checkIfAnyRTDepExistInReleaseDep(rtDepsAtCurrentLvl, releaseDepsAtCurrentLvl, appIdVsAppName)
		if appExistInReleaseAtRtLevel {
			// check if operation is to be performed
			finalDepsAtCurrentLevel, err := performOperationAtCurrentLevel(rtDepsAtCurrentLvl, releaseDepsAtCurrentLvl, toRemoveApps, toAddApps, appIdVsAppName, appNameVsAppId)
			if err != nil {
				impl.logger.Errorw("error encountered in updateReleaseDepsOnPatchingApplication", "err", err)
				return nil, nil, err
			}
			finalLevelVsDeps[targetLevel] = finalDepsAtCurrentLevel
			targetLevel++
			releaseIndexPointer++
		} else {
			operationPerformed := false
			for k := releaseIndexPointer; k < len(allReleaseLevels); k++ {
				// case when release stage has been removed will iterate and check, set in map
				releaseDepsAtCurrentLvl = releaseLevelIndexVsDeps[allReleaseLevels[k]]
				appExistInReleaseAtRtLevel = checkIfAnyRTDepExistInReleaseDep(rtDepsAtCurrentLvl, releaseDepsAtCurrentLvl, appIdVsAppName)
				if appExistInReleaseAtRtLevel && !mapOfReleaseLevelArrayIndexVsProcessed[k] {
					finalDepsAtCurrentLevel, err := performOperationAtCurrentLevel(rtDepsAtCurrentLvl, releaseDepsAtCurrentLvl, toRemoveApps, toAddApps, appIdVsAppName, appNameVsAppId)
					if err != nil {
						impl.logger.Errorw("error encountered in updateReleaseDepsOnPatchingApplication", "err", err)
						return nil, nil, err
					}
					finalLevelVsDeps[targetLevel] = finalDepsAtCurrentLevel
					targetLevel++
					mapOfReleaseLevelArrayIndexVsProcessed[k] = true
					operationPerformed = true
					break
				}
			}
			//case where no app is found so add this app in new stage, with same release pointer(as for next level check)
			if !operationPerformed {
				finalDepsAtCurrentLevel, err := performOperationAtCurrentLevel(rtDepsAtCurrentLvl, nil, toRemoveApps, toAddApps, appIdVsAppName, appNameVsAppId)
				if err != nil {
					impl.logger.Errorw("error encountered in updateReleaseDepsOnPatchingApplication", "err", err)
					return nil, nil, err
				}
				finalLevelVsDeps[targetLevel] = finalDepsAtCurrentLevel
				targetLevel++
			}
		}

	}
	// handling for cases where release level and release app both does not exist in release track
	for i := releaseIndexPointer; i < len(allReleaseLevels); i++ {
		if !mapOfReleaseLevelArrayIndexVsProcessed[i] {
			releaseDepsAtCurrentLvl := releaseLevelIndexVsDeps[allReleaseLevels[i]]
			// toAddApps will always be nil as there will not app to be added which is not present on any level in release track
			finalDepsAtCurrentLevel, err := performAddOrRemoveOperation(releaseDepsAtCurrentLvl, toRemoveApps, nil, appIdVsAppName, appNameVsAppId)
			if err != nil {
				impl.logger.Errorw("error encountered in updateReleaseDepsOnPatchingApplication", "err", err)
				return nil, nil, err
			}
			finalLevelVsDeps[targetLevel] = finalDepsAtCurrentLevel
			targetLevel++
		}
	}
	finalDeps, allRelationsModels := impl.processLevelVsDepsMap(finalLevelVsDeps, existingReleaseObject.Id, existingReleaseObject.DevtronResourceSchemaId, parentDepOfRelease, isLevelPropagatedFromParent)
	return finalDeps, allRelationsModels, nil

}

func (impl *DevtronResourceServiceImpl) updateReleaseDepsWithoutRtOrdering(existingReleaseObject *repository.DevtronResourceObject, existingReleaseDeps []*bean.DtResDepJsonBean, toRemoveApps, toAddApps []*bean.IdentifierAndSchemaIdBean, parentDepOfRelease *bean.DtResDepJsonBean, appIdVsAppName map[int]string, appNameVsAppId map[string]int) ([]*bean.DtResDepJsonBean, []*repository.DevtronResourceObjectDependencyRelations, error) {
	finalDeps, err := performAddOrRemoveOperation(existingReleaseDeps, toRemoveApps, toAddApps, appIdVsAppName, appNameVsAppId)
	if err != nil {
		impl.logger.Errorw("error encountered in updateReleaseDepsWithoutRtOrdering", "err", err)
		return nil, nil, err
	}
	mapOfLevelVsDeps := make(map[int][]*bean.DtResDepJsonBean)
	mapOfLevelVsDeps[1] = finalDeps
	finalDeps, allRelationsModels := impl.processLevelVsDepsMap(mapOfLevelVsDeps, existingReleaseObject.Id, existingReleaseObject.DevtronResourceSchemaId, parentDepOfRelease, false)
	return finalDeps, allRelationsModels, nil

}
func performOperationAtCurrentLevel(rtDepsAtCurrentLvl, releaseDepsAtCurrentLvl []*bean.DtResDepJsonBean, toRemoveApps, toAddApps []*bean.IdentifierAndSchemaIdBean, appIdVsAppName map[int]string, appNameVsAppId map[string]int) ([]*bean.DtResDepJsonBean, error) {
	appsToRemoveAtCurrentLevel, appToAddAtCurrentLevel := filterAppDepsToPerformAtCurrentLevelDeps(rtDepsAtCurrentLvl, toRemoveApps, toAddApps)
	return performAddOrRemoveOperation(releaseDepsAtCurrentLvl, appsToRemoveAtCurrentLevel, appToAddAtCurrentLevel, appIdVsAppName, appNameVsAppId)
}

// processLevelVsDepsMap: will process level wise deps adn will set Index And DependentOnIndexes
func (impl *DevtronResourceServiceImpl) processLevelVsDepsMap(levelVsDeps map[int][]*bean.DtResDepJsonBean, releaseObjectId, releaseSchemaId int, parentDep *bean.DtResDepJsonBean, isLevelPropagatedFromParent bool) ([]*bean.DtResDepJsonBean, []*repository.DevtronResourceObjectDependencyRelations) {
	finalDependencies := make([]*bean.DtResDepJsonBean, 0)
	indexCounter := 1
	allRelationsModels := make([]*repository.DevtronResourceObjectDependencyRelations, 0)
	for _, deps := range levelVsDeps {
		if len(deps) == 0 {
			continue
		}
		// sending level propagated to true as release track ordering is followed
		finalDependencies = append(finalDependencies, adapter.BuildLevelDepJson(indexCounter, isLevelPropagatedFromParent))
		allRelationsModels = append(allRelationsModels, &repository.DevtronResourceObjectDependencyRelations{
			ComponentObjectId:      releaseObjectId,
			ComponentDtResSchemaId: releaseSchemaId,
			TypeOfDependency:       bean.DevtronResourceDependencyTypeLevel.ToString(),
		})
		levelIndex := indexCounter
		indexCounter++
		for _, dep := range deps {
			dep.Index = indexCounter
			dep.DependentOnIndexes = []int{levelIndex}
			if dep.Config != nil && dep.Config.ArtifactConfig != nil && dep.Config.ArtifactConfig.ArtifactId > 0 {
				dep.ChildInheritance = []*bean.ChildInheritance{{ResourceId: impl.dtResReadService.GetDtResourcesByKindMap()[bean.DevtronResourceCdPipeline.ToString()].Id, Selector: adapter.GetDefaultCdPipelineSelector()}}
			}
			indexCounter++
			finalDependencies = append(finalDependencies, dep)
			allRelationsModels = append(allRelationsModels, &repository.DevtronResourceObjectDependencyRelations{
				ComponentObjectId:       releaseObjectId,
				ComponentDtResSchemaId:  releaseSchemaId,
				DependencyObjectId:      dep.Id,
				DependencyDtResSchemaId: dep.DevtronResourceSchemaId,
				TypeOfDependency:        bean.DevtronResourceDependencyTypeUpstream.ToString(),
			})
		}
	}
	if parentDep != nil {
		parentDep.Index = indexCounter
		finalDependencies = append(finalDependencies, parentDep)
		allRelationsModels = append(allRelationsModels, &repository.DevtronResourceObjectDependencyRelations{
			ComponentObjectId:       releaseObjectId,
			ComponentDtResSchemaId:  releaseSchemaId,
			DependencyObjectId:      parentDep.Id,
			DependencyDtResSchemaId: parentDep.DevtronResourceSchemaId,
			TypeOfDependency:        bean.DevtronResourceDependencyTypeParent.ToString(),
		})
	}
	return finalDependencies, allRelationsModels
}

func performAddOrRemoveOperation(existingDeps []*bean.DtResDepJsonBean, toRemoveApps []*bean.IdentifierAndSchemaIdBean, toAddApps []*bean.IdentifierAndSchemaIdBean, appIdVsAppName map[int]string, appNameVsAppId map[string]int) ([]*bean.DtResDepJsonBean, error) {
	if len(toRemoveApps) == 0 && len(toAddApps) == 0 {
		return existingDeps, nil
	}

	finalDep := make([]*bean.DtResDepJsonBean, 0)
	for _, dep := range existingDeps {
		if dep.TypeOfDependency == bean.DevtronResourceDependencyTypeUpstream {
			appName := appIdVsAppName[dep.Id]
			if util3.ContainsIdentifierBean(toRemoveApps, adapter.BuildIdentifierAndSchemaIdBean(appName, dep.DevtronResourceSchemaId)) {
				continue
			}
			if util3.ContainsIdentifierBean(toAddApps, adapter.BuildIdentifierAndSchemaIdBean(appName, dep.DevtronResourceSchemaId)) {
				return nil, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.AppAlreadyPresentMessage, bean.AppAlreadyPresentMessage)
			}
			finalDep = append(finalDep, dep)
		}
	}
	for _, a := range toAddApps {
		// this stores only active app
		if appId, ok := appNameVsAppId[a.Identifier]; ok {
			// index and dependent index will be set after the processing has been done
			depToBeAdded := adapter.CreateInternalDepData(appId, a.DevtronResourceSchemaId, 0, bean.DevtronResourceDependencyTypeUpstream, bean.OldObjectId)
			finalDep = append(finalDep, depToBeAdded)
		} else {
			return nil, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.AppNotFoundMessage, bean.AppNotFoundMessage)
		}
	}
	return finalDep, nil

}
func checkIfAnyRTDepExistInReleaseDep(rtDeps []*bean.DtResDepJsonBean, releaseDeps []*bean.DtResDepJsonBean, appIdVsAppName map[int]string) bool {
	identifierSchemaIdBean := make([]*bean.IdentifierAndSchemaIdBean, 0, len(rtDeps))
	for _, dep := range rtDeps {
		identifierSchemaIdBean = append(identifierSchemaIdBean, adapter.BuildIdentifierAndSchemaIdBean(dep.Identifier, dep.DevtronResourceSchemaId))
	}
	for _, dep := range releaseDeps {
		appName := appIdVsAppName[dep.Id]
		if util3.ContainsIdentifierBean(identifierSchemaIdBean, adapter.BuildIdentifierAndSchemaIdBean(appName, dep.DevtronResourceSchemaId)) {
			return true
		}
	}
	return false
}

func filterAppDepsToPerformAtCurrentLevelDeps(rtDeps []*bean.DtResDepJsonBean, toRemoveApps []*bean.IdentifierAndSchemaIdBean, toAddApps []*bean.IdentifierAndSchemaIdBean) ([]*bean.IdentifierAndSchemaIdBean, []*bean.IdentifierAndSchemaIdBean) {
	appsToBeAdded := make([]*bean.IdentifierAndSchemaIdBean, 0, len(toAddApps))
	for _, dep := range rtDeps {
		identifierBean := adapter.BuildIdentifierAndSchemaIdBean(dep.Identifier, dep.DevtronResourceSchemaId)
		if util3.ContainsIdentifierBean(toAddApps, identifierBean) {
			appsToBeAdded = append(appsToBeAdded, identifierBean)
		}
	}
	//toRemoveApps is returned as apps have been deleted , we will not be able to figure the level of removed app, sent it for better readability
	return toRemoveApps, appsToBeAdded
}

func getLevelIndexVsAllDeps(existingDependencies []*bean.DtResDepJsonBean, appNameVsAppId map[string]int) (map[int][]*bean.DtResDepJsonBean, map[int]bool, error) {
	mapOfLevelIndexVsAllDependentDeps := make(map[int][]*bean.DtResDepJsonBean, len(existingDependencies))
	mapOfLevelIndexVsIsAnyAppMandatory := make(map[int]bool, len(existingDependencies))
	for _, dep := range existingDependencies {
		if dep.TypeOfDependency == bean.DevtronResourceDependencyTypeUpstream {
			dependentOfIndexes := dep.DependentOnIndexes
			if len(dependentOfIndexes) != 1 {
				return nil, nil, util.GetApiErrorAdapter(http.StatusUnprocessableEntity, "422", bean.InvalidDependencyFound, bean.InvalidDependencyFound)
			}
			levelIndex := dependentOfIndexes[0]
			mapOfLevelIndexVsAllDependentDeps[levelIndex] = append(mapOfLevelIndexVsAllDependentDeps[levelIndex], dep)
			_, appActive := appNameVsAppId[dep.Identifier]
			if val, ok := mapOfLevelIndexVsIsAnyAppMandatory[levelIndex]; !ok {
				mapOfLevelIndexVsIsAnyAppMandatory[levelIndex] = dep.ToPropagateOnChild && appActive
			} else {
				mapOfLevelIndexVsIsAnyAppMandatory[levelIndex] = val || (dep.ToPropagateOnChild && appActive)
			}
		}
	}
	return mapOfLevelIndexVsAllDependentDeps, mapOfLevelIndexVsIsAnyAppMandatory, nil
}

func getReleaseIndexDepsWithReleaseData(existingDependencies []*bean.DtResDepJsonBean) (map[int][]*bean.DtResDepJsonBean, *bean.DtResDepJsonBean, bool, error) {
	mapOfLevelIndexVsAllDependentDeps := make(map[int][]*bean.DtResDepJsonBean, len(existingDependencies))
	var parentDep *bean.DtResDepJsonBean
	isLevelPropagatedFromParent := true
	for _, dep := range existingDependencies {
		if dep.TypeOfDependency == bean.DevtronResourceDependencyTypeUpstream {
			dependentOfIndexes := dep.DependentOnIndexes
			if len(dependentOfIndexes) != 1 {
				return nil, nil, false, util.GetApiErrorAdapter(http.StatusUnprocessableEntity, "422", bean.InvalidDependencyFound, bean.InvalidDependencyFound)
			}
			levelIndex := dependentOfIndexes[0]
			mapOfLevelIndexVsAllDependentDeps[levelIndex] = append(mapOfLevelIndexVsAllDependentDeps[levelIndex], dep)
		} else if dep.TypeOfDependency == bean.DevtronResourceDependencyTypeParent {
			parentDep = dep
		} else if dep.TypeOfDependency == bean.DevtronResourceDependencyTypeLevel {
			isLevelPropagatedFromParent = isLevelPropagatedFromParent && dep.IsLevelPropagatedFromParent
		}
	}
	return mapOfLevelIndexVsAllDependentDeps, parentDep, isLevelPropagatedFromParent, nil
}

func (impl *DevtronResourceServiceImpl) getUpdatedDepsForReleaseWithoutReleaseOrdering(existingDependencies []*bean.DtResDepJsonBean, dependencyInfo *bean.DepInfo) ([]*bean.DtResDepJsonBean, error) {
	var maxIndex float64
	levelIndex := 0
	levelCount := 0
	for _, dep := range existingDependencies {
		maxIndex = math.Max(maxIndex, float64(dep.Index))
		if dep.TypeOfDependency == bean.DevtronResourceDependencyTypeLevel {
			levelIndex = dep.Index
			levelCount++
		}

	}
	// in case release track ordering is not followed we only have one level
	if levelCount != 1 {
		return nil, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.RequestFailedMultipleLevelFoundMessage, bean.RequestFailedMultipleLevelFoundMessage)
	}
	dtApplicationSchema, err := impl.dtResReadService.GetDtApplicationSchema()
	if err != nil {
		impl.logger.Errorw("error encountered in getUpdatedDepsForReleaseWithoutReleaseOrdering", "err", err)
		return nil, err
	}
	depToBeAdded := adapter.CreateInternalDepData(dependencyInfo.Id, dtApplicationSchema.Id, maxIndex, bean.DevtronResourceDependencyTypeUpstream, bean.OldObjectId)
	depToBeAdded.DependentOnIndexes = []int{levelIndex}
	existingDependencies = append(existingDependencies, depToBeAdded)
	return existingDependencies, nil
}

func (impl *DevtronResourceServiceImpl) getReleaseTrackDependenciesWithLevelMapping(releaseObjectData string) (map[int][]*bean.DtResDepJsonBean, map[int]*bean.DtResDepJsonBean, error) {
	_, releaseTrackObject, err := impl.getParentConfigVariablesFromDeps(releaseObjectData)
	if err != nil {
		impl.logger.Errorw("error in getReleaseTrackDependenciesWithLevelMapping", "err", err)
		return nil, nil, err
	}
	existingDependencies, err := impl.getDepsInternalBeanInObjectDataFromJsonString(releaseTrackObject.DevtronResourceSchemaId, releaseTrackObject.ObjectData, false)
	if err != nil {
		impl.logger.Errorw("error, getReleaseTrackDependenciesWithLevelMapping", "err", err, "devtronResourceSchemaId", releaseTrackObject.DevtronResourceSchemaId)
		return nil, nil, err
	}
	mapOfLevelIndexVsAllDependentDeps := make(map[int][]*bean.DtResDepJsonBean, len(existingDependencies))
	mapOfLevelIndexVsLevelDep := make(map[int]*bean.DtResDepJsonBean)
	// release track has only two types of deps (upstream and level)
	for _, dep := range existingDependencies {
		if dep.TypeOfDependency == bean.DevtronResourceDependencyTypeUpstream {
			dependentOfIndexes := dep.DependentOnIndexes
			if len(dependentOfIndexes) != 1 {
				return nil, nil, util.GetApiErrorAdapter(http.StatusUnprocessableEntity, "422", bean.InvalidDependencyFound, bean.InvalidDependencyFound)
			}
			levelIndex := dependentOfIndexes[0]
			mapOfLevelIndexVsAllDependentDeps[levelIndex] = append(mapOfLevelIndexVsAllDependentDeps[levelIndex], dep)
		} else if dep.TypeOfDependency == bean.DevtronResourceDependencyTypeLevel {
			mapOfLevelIndexVsLevelDep[dep.Index] = dep
		}
	}
	return mapOfLevelIndexVsAllDependentDeps, mapOfLevelIndexVsLevelDep, nil
}

func (impl *DevtronResourceServiceImpl) getDepsForReleaseFromReleaseTrackOrder(mapOfLevelIndexVsAllDependentDeps map[int][]*bean.DtResDepJsonBean,
	appNameVsAppIdMap map[string]int, mapOfIdAndSchemaKeyVsIndex map[string]int,
	allReleaseDeps []*bean.DtResDepJsonBean, mapOfLevelIndexVsLevelDep map[int]*bean.DtResDepJsonBean, appToBeAddedInfo *bean.DepInfo, parentDepIndex int) []*bean.DtResDepJsonBean {

	for levelIndex, deps := range mapOfLevelIndexVsAllDependentDeps {
		finalDeps := make([]*bean.DtResDepJsonBean, 0, len(deps))
		for i, dep := range deps {
			appId, ok := appNameVsAppIdMap[dep.Identifier]
			indexDepIsPresentAt, ok2 := mapOfIdAndSchemaKeyVsIndex[helper.GetKeyForADependencyMap(appId, dep.DevtronResourceSchemaId)]
			if ok && ok2 {
				//updating whole dep of release with config
				deps[i] = allReleaseDeps[indexDepIsPresentAt]
				finalDeps = append(finalDeps, deps[i])
			} else if appToBeAddedInfo.Identifier == dep.Identifier {
				// update in release dep as we want to add this dependency in release
				dep.Id = appToBeAddedInfo.Id
				finalDeps = append(finalDeps, deps[i])
			} else {
				//remove this dep by setting this as nil
				deps[i] = nil
			}
		}
		mapOfLevelIndexVsAllDependentDeps[levelIndex] = finalDeps
	}

	maxIndex := 1
	finalDependencies := make([]*bean.DtResDepJsonBean, 0)
	for levelIndex, deps := range mapOfLevelIndexVsAllDependentDeps {
		isDepPresent := len(deps) > 0
		if isDepPresent {
			levelDep := mapOfLevelIndexVsLevelDep[levelIndex]
			currentLevelIndex := maxIndex
			levelDep.Index = currentLevelIndex
			maxIndex++
			finalDependencies = append(finalDependencies, levelDep)
			for _, dep := range deps {
				if dep != nil {
					dep.Index = maxIndex
					maxIndex++
					dep.DependentOnIndexes = []int{currentLevelIndex}
					finalDependencies = append(finalDependencies, dep)
				}
			}
		}
	}
	parentDep := allReleaseDeps[parentDepIndex]
	parentDep.Index = maxIndex
	finalDependencies = append(finalDependencies, parentDep)
	return finalDependencies
}

// getRtAppPropagationOffendingStateForRelease get enforcement status for a release from parent release-track
// Enforcement status means all deps present in release-track should be present in release and only those deps should be present in release which are in release-track. This will be allowed action for release enforcement.
// returns true when allowed else false
// STEP 1: Get release track deps from release object data
// STEP 2: Get release deps from release object data.
// STEP 3: Get All Apps for release apps ids and release track app names.
// STEP 4: Check mandatory apps are present in release
// STEP 5: check all release apps are present in release track
func (impl *DevtronResourceServiceImpl) getRtAppPropagationOffendingStateForRelease(releaseObjectData string) (bool, error) {
	_, releaseTrackObject, err := impl.getParentConfigVariablesFromDeps(releaseObjectData)
	if err != nil {
		impl.logger.Errorw("error in getting release track object", "err", err, "releaseObjectData", releaseObjectData)
		return false, err
	}
	releaseTrackApps := getAppDepsBasicDataFromObjectData(releaseTrackObject.ObjectData)
	releaseApps := getAppDepsBasicDataFromObjectData(releaseObjectData)
	allRtAppIdentifiers := make([]string, 0, len(releaseTrackApps))
	allReleaseAppIds := make([]int, 0, len(releaseApps))
	mapOfIdentifierVsToPropagateOnChild := make(map[string]bool, len(releaseTrackApps))
	for _, dep := range releaseTrackApps {
		allRtAppIdentifiers = append(allRtAppIdentifiers, dep.Identifier)
		mapOfIdentifierVsToPropagateOnChild[dep.Identifier] = dep.ToPropagateOnChild
	}
	for _, dep := range releaseApps {
		allReleaseAppIds = append(allReleaseAppIds, dep.OldObjectId)
	}
	appNameVsAppIdMap := make(map[string]int)
	appIdVsAppNameMap := make(map[int]string)
	if len(allRtAppIdentifiers) > 0 || len(allReleaseAppIds) > 0 {
		appNameVsAppIdMap, appIdVsAppNameMap, err = impl.appCrudService.FindActiveAppNameVsAppId(allReleaseAppIds, allRtAppIdentifiers)
		if err != nil {
			impl.logger.Errorw("error encountered in getRtAppPropagationOffendingStateForRelease", "allReleaseAppIds", allReleaseAppIds, "allRtAppIdentifiers", allRtAppIdentifiers, "err", err)
			return false, err
		}
	}
	for _, app := range releaseTrackApps {
		appName := app.Identifier
		if val, ok := mapOfIdentifierVsToPropagateOnChild[appName]; ok && val {
			appId := appNameVsAppIdMap[appName]
			if !slices.Contains(allReleaseAppIds, appId) {
				return false, nil
			}
		}
	}
	for _, app := range releaseApps {
		appId := app.OldObjectId
		if appName, ok := appIdVsAppNameMap[appId]; ok {
			if !slices.Contains(allRtAppIdentifiers, appName) {
				return false, nil
			}
		} else if !ok {
			return false, nil
		}
	}
	return true, nil
}
func (impl *DevtronResourceServiceImpl) policyGetRtAppPropagationOffendingStateForRelease(releaseObjectData string) (bean4.PolicyReleaseIsRTAppPropagationOffending, error) {
	rtAppPropagationOffendingState, err := impl.getRtAppPropagationOffendingStateForRelease(releaseObjectData)
	if err != nil {
		impl.logger.Errorw("error encountered in getPolicyDefinitionStateFromReleaseObj", "objectData", releaseObjectData, "err", err)
		return "", err
	}
	if rtAppPropagationOffendingState {
		return bean4.ReleasePolicyIsRTAppPropagationOffendingYes, nil
	}
	return bean4.ReleasePolicyIsRTAppPropagationOffendingNo, nil
}

func (impl *DevtronResourceServiceImpl) getPolicyDefinitionStateFromReleaseObj(objectData string, releaseIdAndSchemaId *bean.IdAndSchemaId) (*bean4.ReleaseStatusDefinitionState, error) {
	policyConfigStatus, err := adapter.GetReleasePolicyConfigStatusFromResObjData(objectData)
	if err != nil {
		impl.logger.Errorw("error encountered in getPolicyDefinitionStateFromReleaseObj", "objectData", objectData, "err", err)

		return nil, err
	}
	policyRolloutStatus, err := adapter.GetReleasePolicyRolloutStatusFromResObjData(objectData)
	if err != nil {
		impl.logger.Errorw("error encountered in getPolicyDefinitionStateFromReleaseObj", "objectData", objectData, "err", err)

		return nil, err
	}
	policyDepArtifactStatus := adapter.GetReleasePolicyDepArtifactStatusFromResObjData(objectData)
	policyLockStatus := adapter.GetReleasePolicyLockStatusFromResObjData(objectData)
	rtAppPropagationOffendingState, err := impl.policyGetRtAppPropagationOffendingStateForRelease(objectData)
	if err != nil {
		impl.logger.Errorw("error encountered in getPolicyDefinitionStateFromReleaseObj", "objectData", objectData, "err", err)
		return nil, err
	}
	areAllReleasedTargetPresent, err := impl.policyAreAllReleasedTargetPresent(objectData, releaseIdAndSchemaId)
	if err != nil {
		impl.logger.Errorw("error encountered in getPolicyDefinitionStateFromReleaseObj", "objectData", objectData, "err", err)
		return nil, err
	}
	return adapter.BuildReleaseStatusDefinitionStatus(policyConfigStatus, policyRolloutStatus, policyDepArtifactStatus, policyLockStatus, rtAppPropagationOffendingState, areAllReleasedTargetPresent), nil
}

func (impl *DevtronResourceServiceExtendedImpl) patchReleaseInstructionForApplication(devtronResourceSchemaId int, objectData string, dependencyInfo *bean.DepInfo, value string) (*bean.SuccessResponse, string, []string, error) {
	id, err := impl.findAppIdFromDependencyInfo(dependencyInfo)
	if err != nil {
		impl.logger.Errorw("error encountered in removeApplicationDep", "dependencyInfo", dependencyInfo, "err", err)
		return nil, objectData, nil, err
	}
	updatedDependencies, indexChanged, err := impl.patchReleaseInstructionForADep(devtronResourceSchemaId, objectData, id, value)
	if err != nil {
		impl.logger.Errorw("error in removeApplicationDep", "err", err, "id", id, "value", value)
		return nil, objectData, nil, err
	}
	objectData, err = sjson.Set(objectData, bean.ResourceObjectDependenciesPath, updatedDependencies)
	if err != nil {
		impl.logger.Errorw("error in removeApplicationDep", "err", err, "updatedDependencies", updatedDependencies)
		return nil, objectData, nil, err
	}
	return adapter.GetSuccessPassResponse(), objectData, []string{fmt.Sprintf("%s/%v/%s", "dependencies", indexChanged, "config/releaseInstruction")}, nil
}

func (impl *DevtronResourceServiceExtendedImpl) patchImageForADep(devtronResourceSchemaId int, objectData string, id int, artifactConfig *bean.ArtifactConfigJsonBean) ([]*bean.DtResDepJsonBean, int, error) {
	dependenciesResult := gjson.Get(objectData, bean.ResourceObjectDependenciesPath)
	dependenciesResultArr := dependenciesResult.Array()
	dependencies := make([]*bean.DtResDepJsonBean, 0, len(dependenciesResultArr))
	indexChanged := 0
	kind, subKind, version, err := helper.GetKindSubKindAndVersionOfResourceBySchemaId(devtronResourceSchemaId, impl.dtResReadService.GetDtResourcesSchemaByIdMap(), impl.dtResReadService.GetDtResourcesByIdMap())
	if err != nil {
		return nil, 0, err
	}
	parentResourceType := &bean.DtResTypeReq{
		ResourceKind:    bean.DtResKind(kind),
		ResourceSubKind: bean.DtResKind(subKind),
		ResourceVersion: bean.DtResVersion(version),
	}
	for _, dependencyResult := range dependenciesResultArr {
		dependencyBean, err := impl.getDepInternalBeanWithChildInheritance(parentResourceType, dependencyResult.String(), false)
		if err != nil {
			return nil, 0, err
		}
		if dependencyBean.Id == id && helper.IsApplicationDependency(dependencyBean.DtResTypeInternalReq) {
			dependencyBean.Config.ArtifactConfig = &bean.ArtifactConfigJsonBean{
				ArtifactId:          artifactConfig.ArtifactId,
				RegistryName:        artifactConfig.RegistryName,
				RegistryType:        artifactConfig.RegistryType,
				SourceAppWorkflowId: artifactConfig.SourceAppWorkflowId,
				SourceReleaseConfig: artifactConfig.SourceReleaseConfig,
			}
			dependencyBean.ChildInheritance = []*bean.ChildInheritance{{ResourceId: impl.dtResReadService.GetDtResourcesByKindMap()[bean.DevtronResourceCdPipeline.ToString()].Id, Selector: adapter.GetDefaultCdPipelineSelector()}}
			indexChanged = dependencyBean.Index
		}
		dependencies = append(dependencies, dependencyBean)
	}
	return dependencies, indexChanged, nil
}

// EXTENDED METHODS BELOW

func (impl *DevtronResourceServiceExtendedImpl) fetchReleaseTaskRunInfoForApi(req *bean.TaskInfoPostApiInternalBean, query *apiBean.GetTaskRunInfoQueryParams,
	existingResourceObject *repository.DevtronResourceObject) (*bean.DeploymentTaskInfoResponse, error) {
	response, _, _, err := impl.fetchReleaseTaskRunInfoWithFilters(req, query, existingResourceObject)
	return response, err
}

func (impl *DevtronResourceServiceExtendedImpl) fetchReleaseTaskRunInfoWithFilters(req *bean.TaskInfoPostApiInternalBean, query *apiBean.GetTaskRunInfoQueryParams, existingResourceObject *repository.DevtronResourceObject) (*bean.DeploymentTaskInfoResponse, map[string]bool, map[int]int, error) {
	if existingResourceObject == nil || existingResourceObject.Id == 0 {
		impl.logger.Warnw("invalid get request, object not found", "req", req)
		return nil, nil, nil, util.GetApiErrorAdapter(http.StatusNotFound, "404", bean.ResourceDoesNotExistMessage, bean.ResourceDoesNotExistMessage)
	}
	response := make([]bean.DtReleaseTaskRunInfo, 0)
	var resourceId int
	if req.IdType == bean.ResourceObjectIdType {
		resourceId = existingResourceObject.Id
	} else {
		resourceId = existingResourceObject.OldObjectId
	}
	rsIdentifier := helper.GetTaskRunSourceIdentifier(resourceId, req.IdType, existingResourceObject.DevtronResourceSchemaId)

	// getting all release Info and corresponding map to use in further processing at level/stage or showAll(rollout status)
	pipelineIdAppIdKeyVsReleaseInfo, allCdPipelineReleaseInfo, err := impl.fetchAllReleaseInfoStatusWithMap(existingResourceObject, rsIdentifier, query.FetchWithUnmappedData)
	if err != nil {
		impl.logger.Errorw("error encountered in fetchReleaseTaskRunInfoWithFilters", "rsIdentifier", rsIdentifier, "err", err)
		return nil, nil, nil, err
	}
	// getting internal req for processing by decoding filters from req
	filterConditionReq, err := impl.getDecodingRequestFromDecodeFilters(req.FilterCriteria)
	if err != nil {
		impl.logger.Errorw("error encountered in fetchReleaseTaskRunInfoWithFilters", "FilterCriteria", req.FilterCriteria, "err", err)
		return nil, nil, nil, err
	}
	// calculating count for stage wise for all deployments for deployment status checks
	stageWiseDeploymentStatusCount, totalInstallation := getStageWiseAndTotalTenantsFromPipelineInfo(allCdPipelineReleaseInfo)

	// apply filters and return updated dependencies
	updatedCdPipelineReleaseInfo := impl.applyFiltersToDependencies(filterConditionReq, allCdPipelineReleaseInfo)
	//getting filtered count for deployments
	releaseDeploymentStatusCount, mappingConfigCount, deploymentsCount := getReleaseDeploymentStatusCountFromPipelineInfo(updatedCdPipelineReleaseInfo, len(allCdPipelineReleaseInfo), totalInstallation)
	taskInfoCount := adapter.BuildTaskInfoCount(releaseDeploymentStatusCount, stageWiseDeploymentStatusCount, mappingConfigCount, deploymentsCount)
	levelTenInsKeyMapVsTaskRunAllowedOfCurrentLevel := make(map[string]bool)
	appIdVsLevelIdMap := make(map[int]int)
	if query.IsLite {
		response, err = impl.getOnlyLevelDataForTaskInfo(existingResourceObject.ObjectData, pipelineIdAppIdKeyVsReleaseInfo, query.LevelIndex)
		if err != nil {
			impl.logger.Errorw("error encountered in fetchReleaseTaskRunInfoWithFilters", "rsIdentifier", rsIdentifier, "err", err)
			return nil, nil, nil, err
		}
	} else {
		if query.ShowAll {
			// rollout status page api
			return &bean.DeploymentTaskInfoResponse{TaskInfoCount: taskInfoCount, Data: []bean.DtReleaseTaskRunInfo{{Dependencies: updatedCdPipelineReleaseInfo}}}, nil, nil, nil
		}
		// deploy page api
		response, levelTenInsKeyMapVsTaskRunAllowedOfCurrentLevel, appIdVsLevelIdMap, err = impl.getLevelDataWithDependenciesWithTaskRunAllowedForTaskInfo(filterConditionReq, existingResourceObject.ObjectData, rsIdentifier, query.LevelIndex, pipelineIdAppIdKeyVsReleaseInfo)
		if err != nil {
			impl.logger.Errorw("error encountered in fetchReleaseTaskRunInfoWithFilters", "rsIdentifier", rsIdentifier, "err", err)
			return nil, nil, nil, err
		}
	}
	return &bean.DeploymentTaskInfoResponse{TaskInfoCount: taskInfoCount, Data: response}, levelTenInsKeyMapVsTaskRunAllowedOfCurrentLevel, appIdVsLevelIdMap, nil
}

// performFeasibilityChecks performs feasibility checks for a task for kind release
// STEP 1: get required map using bulk operations and use the required maps appVsArtifactIdMap, artifactIdVsArtifactMap, pipelineIdVsPipelineMap to fetch the data.
// STEP 2: Run feasibility for every task by getting required data from map formed in Step 1 And 2, err is not blocking here
func (impl *DevtronResourceServiceExtendedImpl) performFeasibilityChecks(ctx context.Context, req *bean.DtResTaskExecutionInternalBean,
	envIdVsInstallationMappingConfig map[int]*bean.InstallationMappingConfigInternalBean,
	authorisedTaskTargetIds map[string]bool, userMetadata *userBean.UserMetadata, processedInfo *TaskExecutionProcessInfo) ([]*bean.TaskExecutionResponseBean, error) {
	tasks := req.Tasks
	taskExecutionResponse := make([]*bean.TaskExecutionResponseBean, 0, len(tasks))
	valid := helper.ValidateTasksPayload(tasks)
	if !valid {
		return taskExecutionResponse, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.NoTaskFoundMessage, bean.NoTaskFoundMessage)
	}
	pipelineIdVsPipelineMap, mapOfTenantInstallationIdKeyVsEnvs, appVsArtifactIdMap,
		artifactIdVsArtifactMap, appEnvIdVsPipelineInfo := processedInfo.PipelineIdVsPipelineMap, processedInfo.MapOfTenantInstallationIdKeyVsEnvs, processedInfo.AppVsArtifactIdMap,
		processedInfo.ArtifactIdVsArtifactMap, processedInfo.AppEnvIdVsPipelineInfo
	if req.TaskType == bean.BundledTaskType {
		return impl.executeFeasibilityForBundle(ctx, tasks, pipelineIdVsPipelineMap, authorisedTaskTargetIds, artifactIdVsArtifactMap, envIdVsInstallationMappingConfig, appVsArtifactIdMap, appEnvIdVsPipelineInfo, mapOfTenantInstallationIdKeyVsEnvs, req.UserId, userMetadata)
	} else {
		// performing feasibility for every task.
		return impl.executeFeasibilityForNonBundle(ctx, tasks, pipelineIdVsPipelineMap, authorisedTaskTargetIds, artifactIdVsArtifactMap, envIdVsInstallationMappingConfig, appVsArtifactIdMap, req.UserId, userMetadata)
	}
	return taskExecutionResponse, nil
}

func getAppEnvIdKeyVsPipelineInfo(cdPipelines []*bean.CdPipelineReleaseInfo) map[string]*bean.CdPipelineReleaseInfo {
	mapOfAppEnvIdVsPipelineInfo := make(map[string]*bean.CdPipelineReleaseInfo)
	for _, pipeline := range cdPipelines {
		mapOfAppEnvIdVsPipelineInfo[helper.GetKeyForAppIdAndEnvId(pipeline.AppId, pipeline.EnvId)] = pipeline
	}
	return mapOfAppEnvIdVsPipelineInfo
}

func (impl *DevtronResourceServiceImpl) extractPipelineIdsForTenantAndInstallationForRelease() {

}

func (impl *DevtronResourceServiceExtendedImpl) checkIfReleaseResourceTaskRunValid(req *bean.DtResTaskExecutionInternalBean,
	existingResourceObject *repository.DevtronResourceObject, pipelineIdVsPipelineMap map[int]*pipelineConfig.Pipeline,
	envIdVsInstallationMappingConfig map[int]*bean.InstallationMappingConfigInternalBean,
	mapOfTenantInstallationIdKeyVsEnvs map[string]*bean.EnvIdsAndDeploymentConfig) error {
	isValid, message, err := impl.checkIfReleaseResourceOperationValid(existingResourceObject.ObjectData, "", bean4.PolicyReleaseOperationTypeDeploymentTrigger, nil, adapter.BuildIdAndSchemaId(existingResourceObject.Id, existingResourceObject.DevtronResourceSchemaId))
	if err != nil {
		impl.logger.Errorw("err, checkIfReleaseResourceTaskRunValid", "err", err, "objectData", existingResourceObject.ObjectData)
		return err
	}
	if !isValid {
		return util.GetApiErrorAdapter(http.StatusBadRequest, "400", message, message)
	}

	err = impl.checkIfTenantAndInstallationExists(req, pipelineIdVsPipelineMap, envIdVsInstallationMappingConfig, mapOfTenantInstallationIdKeyVsEnvs)
	if err != nil {
		impl.logger.Errorw("error encountered in checkIfReleaseResourceTaskRunValid", "tasks", req.Tasks, "err", err)
		return err
	}
	if req.TaskType != bean.BundledTaskType {
		_, levelTenInsKeyVsTaskRunAllowedMap, appIdVsLevelIdMap, err := impl.fetchReleaseTaskRunInfoWithFilters(&bean.TaskInfoPostApiInternalBean{DtResObjectInternalDescBean: req.DtResObjectInternalDescBean}, &apiBean.GetTaskRunInfoQueryParams{IsLite: false}, existingResourceObject)
		if err != nil {
			impl.logger.Errorw("error encountered in checkIfReleaseResourceTaskRunValid", "descriptorBean", req.DtResObjectInternalDescBean, "err", err)
			return err
		}
		err = checkIfTaskExecutionAllowed(req.Tasks, levelTenInsKeyVsTaskRunAllowedMap, appIdVsLevelIdMap, pipelineIdVsPipelineMap, envIdVsInstallationMappingConfig)
		if err != nil {
			impl.logger.Errorw("error encountered in checkIfReleaseResourceTaskRunValid", "err", err)
			return err
		}
	}

	return nil
}

func (impl *DevtronResourceServiceExtendedImpl) checkIfReleaseResourceOperationValid(objectData string, newObjectData string, operationType bean4.PolicyReleaseOperationType, operationPaths []bean4.PolicyReleaseOperationPath, releaseIdAndSchemaId *bean.IdAndSchemaId) (bool, string, error) {
	stateFrom, err := impl.getPolicyDefinitionStateFromReleaseObj(objectData, releaseIdAndSchemaId)
	if err != nil {
		impl.logger.Errorw("error, getPolicyDefinitionStateFromReleaseObj", "err", err, "objectData", objectData)
		return false, "", err
	}
	var stateTo *bean4.ReleaseStatusDefinitionState
	// it is possible that some operation do not require desired state to be provided for those cases stateTo is passed as nil
	if len(newObjectData) > 0 {
		stateTo, err = impl.getPolicyDefinitionStateFromReleaseObj(newObjectData, releaseIdAndSchemaId)
		if err != nil {
			impl.logger.Errorw("error, getPolicyDefinitionStateFromReleaseObj", "err", err, "newObjectData", newObjectData)
			return false, "", err
		}
	}
	isValid, err := impl.releasePolicyEvaluationService.EvaluateReleaseActionRequest(operationType, operationPaths, stateFrom, stateTo)
	return isValid, adapter.GetPolicyValidationFailedMessage(stateFrom, stateTo, operationType), err
}

func (impl *DevtronResourceServiceExtendedImpl) checkIfReleaseResourcePatchValid(objectData string, newObjectData string, queries []bean.PatchQuery, releaseIdAndSchemaId *bean.IdAndSchemaId) error {
	operationsPaths := make([]bean4.PolicyReleaseOperationPath, 0, len(queries))
	for _, query := range queries {
		operationsPaths = append(operationsPaths, bean4.PatchQueryPathReleasePolicyOpPathMap[query.Path])
	}
	isValid, message, err := impl.checkIfReleaseResourceOperationValid(objectData, newObjectData, bean4.PolicyReleaseOperationTypePatch, operationsPaths, releaseIdAndSchemaId)
	if err != nil {
		impl.logger.Errorw("err, checkIfReleaseResourcePatchValid", "err", err, "objectData", objectData, "newObjectData", newObjectData)
		return err
	}
	if !isValid {
		return util.GetApiErrorAdapter(http.StatusBadRequest, "400", message, message)
	}
	return nil
}

func (impl *DevtronResourceServiceExtendedImpl) checkIfReleaseResourceDeletionValid(objectData string, releaseIdAndSchemaId *bean.IdAndSchemaId) error {
	isValid, message, err := impl.checkIfReleaseResourceOperationValid(objectData, "", bean4.PolicyReleaseOperationTypeDelete, nil, releaseIdAndSchemaId)
	if err != nil {
		impl.logger.Errorw("err, checkIfReleaseResourceDeletionValid", "err", err, "objectData", objectData)
		return err
	}
	if !isValid {
		return util.GetApiErrorAdapter(http.StatusBadRequest, "400", message, message)
	}
	return nil
}

func (impl *DevtronResourceServiceExtendedImpl) checkReleasePatchPolicyAndAutoAction(oldObjectData, newObjectData string, releaseIdAndSchemaId *bean.IdAndSchemaId) (string, error) {
	stateFrom, err := impl.getPolicyDefinitionStateFromReleaseObj(oldObjectData, releaseIdAndSchemaId)
	if err != nil {
		impl.logger.Errorw("error, getPolicyDefinitionStateFromReleaseObj", "err", err, "oldObjectData", oldObjectData)
		return "", err
	}
	stateTo, err := impl.getPolicyDefinitionStateFromReleaseObj(newObjectData, releaseIdAndSchemaId)
	if err != nil {
		impl.logger.Errorw("error, getPolicyDefinitionStateFromReleaseObj", "err", err, "newObjectData", newObjectData)
		return "", err
	}

	isValid, autoAction, err := impl.releasePolicyEvaluationService.EvaluateReleaseStatusChangeAndGetAutoAction(stateTo, stateFrom)
	if err != nil {
		impl.logger.Errorw("error, EvaluateReleaseStatusChangeAndGetAutoAction", "err", err, "oldObjectData", oldObjectData, "newObjectData", newObjectData)
		return newObjectData, err
	}
	if !isValid {
		impl.logger.Errorw("error in EvaluateReleaseStatusChangeAndGetAutoAction : invalid action", "oldObjectData", oldObjectData, "newObjectData", newObjectData)

		return newObjectData, getReleaseStatusChangePolicyErrResponse(oldObjectData, newObjectData)
	}
	if autoAction != nil {
		patchQueries, err := adapter.GetPatchQueryForPolicyAutoAction(autoAction, stateTo)
		if err != nil {
			impl.logger.Errorw("error, GetPatchQueryForPolicyAutoAction", "autoAction", autoAction, "stateTo", stateTo, "newObjectData", newObjectData)
			return "", err
		}
		for _, patchQuery := range patchQueries {
			newObjectData, err = impl.patchQueryForReleaseObject(newObjectData, patchQuery, releaseIdAndSchemaId)
			if err != nil {
				impl.logger.Errorw("error in auto action patch operation, release track", "err", err, "objectData", "query", patchQuery)
				return newObjectData, err
			}
		}
	}
	return newObjectData, nil
}

func (impl *DevtronResourceServiceExtendedImpl) getUpdatedObjectDataAfterPatchDeps(tx *pg.Tx, existingObject *repository.DevtronResourceObject, updatedObjectData string, toRemoveApps, toAddApps []*bean.IdentifierAndSchemaIdBean) (*bean.SuccessResponse, string, string, []string, error) {
	existingReleaseDependencies, err := impl.getDepsInternalBeanInObjectDataFromJsonString(existingObject.DevtronResourceSchemaId, updatedObjectData, false)
	if err != nil {
		impl.logger.Errorw("error, getUpdatedObjectDataAfterPatchDeps", "err", err, "devtronResourceSchemaId", existingObject.DevtronResourceSchemaId)
		return nil, updatedObjectData, "", nil, err
	}
	_, releaseTrackObject, err := impl.getParentConfigVariablesFromDeps(updatedObjectData)
	if err != nil {
		impl.logger.Errorw("error in getUpdatedObjectDataAfterPatchDeps", "err", err)
		return nil, updatedObjectData, "", nil, err
	}
	existingRTDependencies, err := impl.getDepsInternalBeanInObjectDataFromJsonString(releaseTrackObject.DevtronResourceSchemaId, releaseTrackObject.ObjectData, false)
	if err != nil {
		impl.logger.Errorw("error, getUpdatedObjectDataAfterPatchDeps", "err", err, "devtronResourceSchemaId", releaseTrackObject.DevtronResourceSchemaId)
		return nil, updatedObjectData, "", nil, err
	}

	finalDependencies, allRelationsModels, err := impl.updateReleaseDepsOnPatchingApplication(existingObject, existingReleaseDependencies, existingRTDependencies, toRemoveApps, toAddApps)
	if err != nil {
		impl.logger.Errorw("error encountered in getUpdatedObjectDataAfterPatchDeps", "err", err)
		return nil, updatedObjectData, "", nil, err
	}
	err = impl.dtResObjDepsRelationsRepository.DeleteAllForComponentIdAndComponentSchemaId(tx, existingObject.Id, existingObject.DevtronResourceSchemaId)
	if err != nil {
		impl.logger.Errorw("error encountered in getUpdatedObjectDataAfterPatchDeps", "err", err, "id", existingObject.Id)
		return nil, updatedObjectData, "", nil, err
	}

	if len(allRelationsModels) > 0 {
		err = impl.dtResObjDepsRelationsRepository.SaveInBatch(tx, allRelationsModels)
		if err != nil {
			impl.logger.Errorw("error, SaveInBatch", "err", err)
			return nil, updatedObjectData, "", nil, err
		}
	}
	updatedObjectData, err = sjson.Set(updatedObjectData, bean.ResourceObjectDependenciesPath, finalDependencies)
	if err != nil {
		impl.logger.Errorw("error in addApplicationDep", "err", err, "finalDependencies", finalDependencies)
		return nil, updatedObjectData, "", nil, err
	}

	successResp, newObjectData, auditPaths, err := impl.performReleaseResourcePatchOperation(tx, updatedObjectData, existingObject, []bean.PatchQuery{{Operation: patchQuery.Replace, Path: bean.ReleaseStatusQueryPath, Value: bean.ConfigStatus{Status: bean.ReleaseConfigStatus(bean.DraftReleaseStatus)}}, {Operation: patchQuery.Replace, Path: bean.ReleaseLockQueryPath, Value: false}}, 1)
	if err != nil {
		impl.logger.Errorw("error encountered in removeApplicationDep", "err", err, "updatedObjectData", updatedObjectData)
		return nil, updatedObjectData, "", nil, err
	}
	_, oldObjectDataWithDraftAndUnlockedState, _, err := impl.performReleaseResourcePatchOperation(tx, existingObject.ObjectData, existingObject, []bean.PatchQuery{{Operation: patchQuery.Replace, Path: bean.ReleaseStatusQueryPath, Value: bean.ConfigStatus{Status: bean.ReleaseConfigStatus(bean.DraftReleaseStatus)}}, {Operation: patchQuery.Replace, Path: bean.ReleaseLockQueryPath, Value: false}}, 1)
	if err != nil {
		impl.logger.Errorw("error encountered in removeApplicationDep", "err", err, "updatedObjectData", updatedObjectData)
		return nil, updatedObjectData, "", nil, err
	}
	auditPaths = append(auditPaths, bean.ResourceObjectDependenciesPath)
	return successResp, newObjectData, oldObjectDataWithDraftAndUnlockedState, auditPaths, nil

}

func (impl *DevtronResourceServiceExtendedImpl) performResDepsPatchForRelease(tx *pg.Tx, req *bean.DtResDepPatchAPIBean, existingResourceObject *repository.DevtronResourceObject, internalDescriptorBean *bean.DtResObjectInternalDescBean) (*bean.SuccessResponse, string, []string, error) {
	oldObjectData := existingResourceObject.ObjectData
	newObjectData := oldObjectData
	auditPaths := make([]string, 0)
	var err error
	var resp *bean.SuccessResponse
	toPerformApplicationTypePatch := false
	allDependencyInfoForApplication := make(map[*bean.DepInfo][]patchQuery.Operation)
	allPatchQueries := make([]bean.PatchQuery, 0)
	for _, patchReq := range req.DependencyPatch {
		// performing json patch operations
		patchQuery := patchReq.PatchQuery
		allPatchQueries = append(allPatchQueries, patchQuery...)
		dependencyInfo := patchReq.DependencyInfo
		var jsonPath []string
		isApplicationType := false
		for _, query := range patchQuery {
			resp, newObjectData, jsonPath, isApplicationType, err = impl.performDepPatchOperation(existingResourceObject.DevtronResourceSchemaId, allDependencyInfoForApplication, newObjectData, query, dependencyInfo, tx)
			if err != nil {
				impl.logger.Errorw("error encountered in performResDepsPatchForRelease", "query", query, "err", err)
				return nil, oldObjectData, nil, err
			}
			if len(jsonPath) > 0 {
				auditPaths = append(auditPaths, jsonPath...)
			}
			toPerformApplicationTypePatch = toPerformApplicationTypePatch || isApplicationType
		}
	}

	// performing application operations here(will be judged in bulk and operations will be performed in bulk.
	if toPerformApplicationTypePatch {
		var jsonPath []string
		toRemoveDeps, toAddDeps, err := impl.processDepInfoVsPatchOperationMap(allDependencyInfoForApplication)
		if err != nil {
			impl.logger.Errorw("error encountered in performResDepsPatchForRelease", "err", err)
			return nil, oldObjectData, nil, err
		}
		// overriding old object data to draft and unlocked state as we dont allow action update on release with readyToReleaseState
		resp, newObjectData, oldObjectData, jsonPath, err = impl.getUpdatedObjectDataAfterPatchDeps(tx, existingResourceObject, newObjectData, toRemoveDeps, toAddDeps)
		if err != nil {
			impl.logger.Errorw("error encountered in performResDepsPatchForRelease", "err", err)
			return nil, oldObjectData, nil, err
		}
		if len(jsonPath) > 0 {
			auditPaths = append(auditPaths, jsonPath...)
		}
	}

	err = impl.checkIfResourcePatchOperationValid(internalDescriptorBean, oldObjectData, newObjectData, allPatchQueries, adapter.BuildIdAndSchemaId(existingResourceObject.Id, existingResourceObject.DevtronResourceSchemaId))
	if err != nil {
		impl.logger.Errorw("err, performResDepsPatchForRelease", "err", err, "req", req)
		return nil, oldObjectData, nil, err
	}
	return resp, newObjectData, auditPaths, nil

}

func (impl *DevtronResourceServiceExtendedImpl) getArtifactResponseForDependency(dependency *bean.DtResDepBean, toFetchArtifactData bool,
	mapOfArtifactIdAndReleases map[int][]*bean.DtResObjOverviewDescBean, appWorkflowId int, searchArtifactTag,
	searchImageTag string, artifactIdsForReleaseTrackFilter []int, limit, offset int, userId int32) (bean.DepConfigOptions[*serviceBean.CiArtifactResponse], error) {
	artifactResponse := bean.DepConfigOptions[*serviceBean.CiArtifactResponse]{
		Id:              dependency.OldObjectId,
		Identifier:      dependency.Identifier,
		ResourceKind:    dependency.ResourceKind,
		ResourceVersion: dependency.ResourceVersion,
	}
	if toFetchArtifactData {
		var appWorkflowIds []int
		if appWorkflowId > 0 {
			appWorkflowIds = append(appWorkflowIds, appWorkflowId)
		}
		workflowComponentsMap, err := impl.appWorkflowDataReadService.FindWorkflowComponentsToAppIdMapping(dependency.OldObjectId, appWorkflowIds)
		if err != nil {
			impl.logger.Errorw("error in getting workflowComponentsMap", "err", err,
				"appId", dependency.OldObjectId, "appWorkflowId", appWorkflowId)
			return artifactResponse, err
		}
		if workflowComponentsMap == nil || len(workflowComponentsMap) == 0 {
			//no workflow components, not looking for artifact further
			return artifactResponse, nil
		}
		workflowFilterMap := make(map[string]int) //map of "componentType-componentId" and appWorkflowId
		var ciPipelineIds, externalCiPipelineIds, cdPipelineIds []int
		for appWfId, workflowComponents := range workflowComponentsMap {
			if workflowComponents.CiPipelineId != 0 {
				ciPipelineIds = append(ciPipelineIds, workflowComponents.CiPipelineId)
				workflowFilterMap[fmt.Sprintf("%s-%d", appWorkflow.CIPIPELINE, workflowComponents.CiPipelineId)] = appWfId
			}
			if workflowComponents.ExternalCiPipelineId != 0 {
				externalCiPipelineIds = append(externalCiPipelineIds, workflowComponents.ExternalCiPipelineId)
				workflowFilterMap[fmt.Sprintf("%s-%d", appWorkflow.WEBHOOK, workflowComponents.ExternalCiPipelineId)] = appWfId
			}
			for _, cdPipelineId := range workflowComponents.CdPipelineIds {
				workflowFilterMap[fmt.Sprintf("%s-%d", appWorkflow.CDPIPELINE, cdPipelineId)] = appWfId
				cdPipelineIds = append(cdPipelineIds, cdPipelineId)
			}
		}
		request := &bean3.WorkflowComponentsBean{
			AppId:                 dependency.OldObjectId,
			CiPipelineIds:         ciPipelineIds,
			ExternalCiPipelineIds: externalCiPipelineIds,
			CdPipelineIds:         cdPipelineIds,
			SearchArtifactTag:     searchArtifactTag,
			SearchImageTag:        searchImageTag,
			ArtifactIds:           artifactIdsForReleaseTrackFilter,
			Limit:                 limit,
			Offset:                offset,
			UserId:                userId,
		}
		data, err := impl.appArtifactManager.RetrieveArtifactsForAppWorkflows(request, workflowFilterMap)
		if err != nil && !util.IsErrNoRows(err) {
			impl.logger.Errorw("error in getting artifact list for", "request", request, "err", err)
			return artifactResponse, err
		}
		// Note: Overriding bean.CiArtifactResponse.TagsEditable as we are not supporting Image Tags edit from UI in V1
		// TODO: to be removed when supported in UI
		data.TagsEditable = false
		if len(data.CiArtifacts) > 0 {
			for i := range data.CiArtifacts {
				ciArtifact := data.CiArtifacts[i]
				if releases, ok := mapOfArtifactIdAndReleases[ciArtifact.Id]; ok {
					ciArtifact.ConfiguredInReleases = releases
				}
				data.CiArtifacts[i] = ciArtifact
			}
			artifactResponse.Data = &data
		}
	}
	return artifactResponse, nil
}

func (impl *DevtronResourceServiceExtendedImpl) performDepPatchOperation(devtronResourceSchemaId int, allDependencyInfoForApplicationMap map[*bean.DepInfo][]patchQuery.Operation, objectData string, query bean.PatchQuery, dependencyInfo *bean.DepInfo, tx *pg.Tx) (*bean.SuccessResponse, string, []string, bool, error) {
	// always remember patch child inheritance data when patching application dependencies, can corrupt data if not patched
	toPerformApplicationTypePatch := false
	switch query.Path {
	case bean.ReleaseDepApplicationQueryPath:
		//currently only remove is supported
		if dependencyInfo == nil {
			return nil, "", nil, false, util.GetApiErrorAdapter(http.StatusNotFound, "400", bean.InvalidPatchOperation, bean.InvalidPatchOperation)
		}

		if query.Operation == patchQuery.Remove || query.Operation == patchQuery.Add {
			toPerformApplicationTypePatch = true
			allDependencyInfoForApplicationMap[dependencyInfo] = append(allDependencyInfoForApplicationMap[dependencyInfo], query.Operation)
			// application remove and add are handled separately
			return nil, objectData, nil, toPerformApplicationTypePatch, nil

		}
		return nil, "", nil, false, util.GetApiErrorAdapter(http.StatusNotFound, "400", bean.InvalidPatchOperation, bean.InvalidPatchOperation)

	case bean.ReleaseDepInstructionQueryPath:
		resp, newObjectData, auditPath, err := impl.patchReleaseInstructionForApplication(devtronResourceSchemaId, objectData, dependencyInfo, query.Value.(string))
		return resp, newObjectData, auditPath, false, err
	case bean.ReleaseDepConfigImageQueryPath:
		resp, newObjectData, auditPath, err := impl.patchConfigImageForApplication(devtronResourceSchemaId, objectData, dependencyInfo, query.Value)
		return resp, newObjectData, auditPath, false, err
	default:
		return nil, "", nil, toPerformApplicationTypePatch, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.PatchPathNotSupportedError, bean.PatchPathNotSupportedError)
	}
}

func (impl *DevtronResourceServiceExtendedImpl) patchConfigImageForApplication(devtronResourceSchemaId int, objectData string, dependencyInfo *bean.DepInfo, value interface{}) (*bean.SuccessResponse, string, []string, error) {
	id, err := impl.findAppIdFromDependencyInfo(dependencyInfo)
	if err != nil {
		impl.logger.Errorw("error encountered in removeApplicationDep", "dependencyInfo", dependencyInfo, "err", err)
		return nil, objectData, nil, err
	}
	artifactConfig, err := impl.getArtifactConfigFromQueryValueForPatch(value)
	if err != nil {
		impl.logger.Errorw("error encountered in patchConfigImageForApplication", "value", value, "err", err)
		return nil, objectData, nil, err
	}
	updatedDependencies, indexChanged, err := impl.patchImageForADep(devtronResourceSchemaId, objectData, id, artifactConfig)
	if err != nil {
		impl.logger.Errorw("error in removeApplicationDep", "err", err, "id", id, "value", value)
		return nil, objectData, nil, err
	}
	objectData, err = sjson.Set(objectData, bean.ResourceObjectDependenciesPath, updatedDependencies)
	if err != nil {
		impl.logger.Errorw("error in patchConfigImageForApplication", "err", err, "updatedDependencies", updatedDependencies)
		return nil, objectData, nil, err
	}
	return adapter.GetSuccessPassResponse(), objectData, []string{fmt.Sprintf("%s/%v/%s", "dependencies", indexChanged, "config/image")}, nil
}

func (impl *DevtronResourceServiceExtendedImpl) updateReleaseArtifactListInRespObj(resourceObject *repository.DevtronResourceObject, query *apiBean.GetConfigOptionsQueryParams, userId int32) ([]bean.DepConfigOptions[*serviceBean.CiArtifactResponse], error) {
	response := make([]bean.DepConfigOptions[*serviceBean.CiArtifactResponse], 0)
	filterDependencyTypes := []bean.DtResDepType{
		bean.DevtronResourceDependencyTypeUpstream,
	}
	dependenciesWithMetaData, err := impl.getReleaseDependenciesData(resourceObject, filterDependencyTypes, query.DependenciesInfo, true)
	if err != nil {
		return nil, err
	}
	appWorkflowId, releaseTrackFilter, err := getReleaseConfigOptionsFilterCriteriaData(query)
	if err != nil {
		impl.logger.Errorw("error encountered in decodeFilterCriteriaForConfigOptions", "filterCriteria", query.FilterCriteria, "err", bean.InvalidFilterCriteria)
		return nil, err
	}
	var searchKeyOption bean.SearchKeyOptions
	if len(query.SearchKey) > 0 {
		searchKeyOption, err = getReleaseConfigOptionsSearchKeyData(query)
		if err != nil {
			impl.logger.Errorw("error encountered in decodeSearchKeyForConfigOptions", "searchKey", query.SearchKey, "err", bean.InvalidSearchKey)
			return nil, err
		}
	}

	toFetchArtifactData := true
	mapOfArtifactIdAndReleases := make(map[int][]*bean.DtResObjOverviewDescBean)
	var artifactIdsForReleaseTrackFilter []int
	if releaseTrackFilter != nil {
		//this filter enables user to get configured images in releases of this release track where same workflow is present
		//sending user id as zero as we are just getting data user id is not used
		releaseTrackDescriptorBean := adapter.BuildDtResObjIntDescBean(adapter.BuildDtResObjCommonDescBean(bean.DevtronResourceReleaseTrack.ToString(), "", bean.DevtronResourceVersionAlpha1.ToString(), releaseTrackFilter.ValueInt, "", releaseTrackFilter.Value), 0, nil, 0)
		_, releaseTrack, err := impl.getResourceSchemaAndExistingObject(releaseTrackDescriptorBean)
		if err != nil {
			impl.logger.Errorw("error, getResourceSchemaAndExistingObject", "err", err, "descriptorBean", releaseTrackDescriptorBean)
			return nil, err
		}
		releases, err := impl.dtResObjectRepository.GetChildObjectsByParentArgAndSchemaId(releaseTrack.Id, bean.IdDbColumnKey, releaseTrack.DevtronResourceSchemaId)
		if err != nil {
			impl.logger.Errorw("error, GetChildObjectsByParentArgAndSchemaId", "err", err, "descriptorBean", err)
			return nil, err
		}
		for _, release := range releases {
			rolloutStatus := bean.ReleaseRolloutStatus(gjson.Get(release.ObjectData, bean.ReleaseResourceRolloutStatusPath).String())
			if rolloutStatus == bean.PartiallyDeployedReleaseRolloutStatus || rolloutStatus == bean.CompletelyDeployedReleaseRolloutStatus {
				//getting dependencies
				dependencies, err := impl.getDepsInternalBeanInObjectDataFromJsonString(release.DevtronResourceSchemaId, release.ObjectData, true)
				if err != nil {
					impl.logger.Errorw("error, getDependenciesInObjectDataFromJsonString", "err", err, "objectData", release.ObjectData)
					return nil, err
				}
				overviewDescBean := impl.getReleaseOverviewDescriptorBeanFromObject(release)
				for _, dependency := range dependencies {
					artifactId := 0
					if dependency.Config != nil && dependency.Config.ArtifactConfig != nil {
						artifactId = dependency.Config.ArtifactConfig.ArtifactId
					}
					if artifactId > 0 { //just appending all selected artifacts and app workflow filter will be added later as old artifacts configured do not have appWorkflowId saved with them
						if len(searchKeyOption.ReleaseNameOrVersion) > 0 && !(strings.Contains(overviewDescBean.Name, searchKeyOption.ReleaseNameOrVersion) || strings.Contains(overviewDescBean.ReleaseVersion, searchKeyOption.ReleaseNameOrVersion)) {
							continue
						}
						mapOfArtifactIdAndReleases[artifactId] = append(mapOfArtifactIdAndReleases[artifactId], overviewDescBean)
						artifactIdsForReleaseTrackFilter = append(artifactIdsForReleaseTrackFilter, artifactId)
					}
				}
			}
		}
		toFetchArtifactData = len(artifactIdsForReleaseTrackFilter) != 0
	}

	for _, dependency := range dependenciesWithMetaData {
		artifactResponse, err := impl.getArtifactResponseForDependency(dependency, toFetchArtifactData, mapOfArtifactIdAndReleases, appWorkflowId, searchKeyOption.ArtifactTag,
			searchKeyOption.ImageTag, artifactIdsForReleaseTrackFilter, query.Limit, query.Offset, userId)
		if err != nil {
			return nil, err
		}
		response = append(response, artifactResponse)
	}
	return response, nil
}

func (impl *DevtronResourceServiceExtendedImpl) updateReleaseDataForGetDepsApi(req *bean.DtResObjDescApiBean, query *apiBean.GetDependencyQueryParams,
	resourceSchema *repository.DevtronResourceSchema, resourceObject *repository.DevtronResourceObject, response *bean.DtResObjDepsReqBean) error {
	// getting mapping config map here as to avoid it in loop only if isLite is false
	envIdVsInstallationMappingConfig := make(map[int]*bean.InstallationMappingConfigInternalBean)
	mapOfInstallationIdentifierVsIsReleased := make(map[string]bool)
	if !query.IsLite {
		var err error
		envIdVsInstallationMappingConfig, err = impl.dtResObjRelationReadService.GetEnvIdVsInstallationConfigMap()
		if err != nil {
			impl.logger.Errorw("error encountered in updateReleaseDataForGetDepsApi", "err", err)
			return err
		}
		mapOfInstallationIdentifierVsIsReleased, err = impl.getMapOfInstallationIdentifierVsTriggeredForRelease(adapter.BuildIdAndSchemaId(resourceObject.Id, resourceObject.DevtronResourceSchemaId), envIdVsInstallationMappingConfig)
		if err != nil {
			impl.logger.Errorw("error in updateTargetForGetApiResObj", "err", err)
			return err
		}
	}

	dependenciesOfParent, err := impl.getDepsInternalBeanInObjectDataFromJsonStringForGetApi(resourceObject.DevtronResourceSchemaId, resourceObject.ObjectData, query.IsLite, envIdVsInstallationMappingConfig, mapOfInstallationIdentifierVsIsReleased)
	if err != nil {
		impl.logger.Errorw("error in getting dependencies from json object", "err", err)
		return err
	}
	filterDependencyTypes := []bean.DtResDepType{
		bean.DevtronResourceDependencyTypeLevel,
		bean.DevtronResourceDependencyTypeUpstream,
	}
	appIdsToGetMetadata := helper.GetDependencyOldObjectIdsForSpecificType(dependenciesOfParent, bean.DevtronResourceDependencyTypeUpstream)
	releaseTrackId, RtSchemaId := helper.GetDepOldObjIdAndSchemaIdForParentType(dependenciesOfParent)
	dependencyFilterKeys, err := impl.getFilterKeysFromDependenciesInfo(query.DependenciesInfo)
	if err != nil {
		return err
	}
	appMetadataObj, mapOfAppIdAndActiveBean, inactiveAppNames, err := impl.getMapOfAppIncludingDeletedMetadata(appIdsToGetMetadata)
	if err != nil {
		return err
	}

	// updating old object id(app id) in case where app got deleted and app got created with same name, release has dep id which has to be updated.
	err = impl.handleDeleteAppDepCreationForRelease(resourceObject, dependenciesOfParent, inactiveAppNames, mapOfAppIdAndActiveBean, appMetadataObj)
	if err != nil {
		impl.logger.Errorw("error encountered in updateReleaseDataForGetDepsApi", "err", err)
		return err
	}
	metadataObj := &bean.DependencyMetaDataBean{
		MapOfAppsMetadata: appMetadataObj,
	}
	response.Dependencies = impl.getFilteredDepsWithMetaData(dependenciesOfParent, filterDependencyTypes, dependencyFilterKeys, metadataObj, mapOfAppIdAndActiveBean)
	lenOfUpstream, err := impl.dtResObjRelationReadService.GetUpstreamCount(releaseTrackId, RtSchemaId)
	if err != nil {
		impl.logger.Errorw("error in getting GetUpstreamCount", "err", err)
		return err
	}
	response.IsDepsPresentOnParent = lenOfUpstream > 0
	return nil
}

func (impl *DevtronResourceServiceExtendedImpl) getResObjSummaryForRelease(dbObj *repository.DevtronResourceObject) (*bean.DtResObjSummaryResp, error) {
	//getting all upstream dependencies by object data
	deps, err := impl.getDepsInternalBeanInObjectDataFromJsonStringForGetApi(dbObj.DevtronResourceSchemaId, dbObj.ObjectData, false, nil, nil)
	if err != nil {
		impl.logger.Errorw("error in getting dependencies from json object", "err", err)
		return nil, err
	}
	depDtos := make([]*bean.DtResObjSummaryGroupedDepDto, 0, len(deps))

	appIds := make([]int, 0, len(deps))
	appIdDepDtoIndexMap := make(map[int]int, len(deps))
	for i, dep := range deps {
		if dep.TypeOfDependency == bean.DevtronResourceDependencyTypeUpstream {
			appIds = append(appIds, dep.Id)
			appIdDepDtoIndexMap[dep.Id] = i
		}
	}
	if len(appIds) > 0 {
		apps, err := impl.appRepository.FindAppsByIdsOrNames(appIds, nil)
		if err != nil {
			impl.logger.Errorw("error in getting apps by ids or names", "ids", appIds, "err", err)
			return nil, err
		}
		for _, app := range apps {
			if i, ok := appIdDepDtoIndexMap[app.Id]; ok {
				dep := deps[i]
				depDto := &bean.DtResObjSummaryGroupedDepDto{
					Id:              app.Id,
					Identifier:      app.AppName,
					ResourceKind:    bean.DevtronResourceDevtronApplication,
					ResourceVersion: bean.DevtronResourceVersion1,
				}
				if dep.Config != nil {
					depDto.Data = dep.Config.ArtifactConfig
				}
				depDtos = append(depDtos, depDto)
			} else {
				return nil, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidResourceRequestDescriptorData, bean.InvalidResourceRequestDescriptorData)
			}
		}
	}
	return &bean.DtResObjSummaryResp{
		GroupedDeps: depDtos,
	}, nil
}

func (impl *DevtronResourceServiceExtendedImpl) isAnyNewPipelineTriggered(req *bean.DtResTaskExecutionInternalBean,
	existingObject *repository.DevtronResourceObject, savedTaskRuns []*repository.DevtronResourceTaskRun, currentlyExecutedPipelineIds []int) (bool, error) {
	dRSchemaId := existingObject.DevtronResourceSchemaId
	releaseObjectId := req.Id

	rsIdentifier := helper.GetTaskRunSourceIdentifier(releaseObjectId, req.IdType, dRSchemaId)
	var appDependencyIdentifiers []string
	var excludedTaskRunIds []int
	for _, savedTaskRun := range savedTaskRuns {
		excludedTaskRunIds = append(excludedTaskRunIds, savedTaskRun.Id)
		if !slices.Contains(appDependencyIdentifiers, savedTaskRun.RunSourceDependencyIdentifier) {
			appDependencyIdentifiers = append(appDependencyIdentifiers, savedTaskRun.RunSourceDependencyIdentifier)
		}
	}
	previousCdWfrIds, err := impl.getExecutedCdWfrIdsFromTaskRun(rsIdentifier, appDependencyIdentifiers, excludedTaskRunIds...)
	previouslyExecutedPipelineIds, err := impl.ciCdPipelineOrchestrator.GetCdPipelineIdsForRunnerIds(previousCdWfrIds)
	if err != nil {
		impl.logger.Errorw("error in getting previously executed pipelineIds", "err", err, "previousCdWfrIds", previousCdWfrIds)
		return false, err
	}
	for _, currentlyExecutedPipelineId := range currentlyExecutedPipelineIds {
		if !slices.Contains(previouslyExecutedPipelineIds, currentlyExecutedPipelineId) {
			return true, nil
		}
	}
	return false, nil
}

// getReleaseDeploymentInfoForDependencies gets every data point for dependencies in CdPipelineReleaseInfo object
func (impl *DevtronResourceServiceExtendedImpl) getReleaseDeploymentInfoForDependencies(releaseRunSourceIdentifier string, dependencies []*bean.DtResDepBean, fetchWithUnmappedData bool, objectData string) ([]*bean.CdPipelineReleaseInfo, map[string]*bean.CdPipelineReleaseInfo, error) {
	appIds, cdWfrIds, err := impl.getAppAndCdWfrIdsForDependencies(releaseRunSourceIdentifier, dependencies)
	if err != nil {
		impl.logger.Errorw("error encountered in getReleaseDeploymentInfoForDependencies", "releaseRunSourceIdentifier", releaseRunSourceIdentifier, "err", err)
		return nil, nil, err
	}
	envIds, err := impl.fetchEnvIdsBasedOnReleaseTarget(objectData)
	if err != nil {
		impl.logger.Errorw("error encountered in getReleaseDeploymentInfoForDependencies", "releaseRunSourceIdentifier", releaseRunSourceIdentifier, "err", err)
		return nil, nil, err
	}

	pipelinesInfo, pipelineIdAppIdKeyVsReleaseInfo, err := impl.ciCdPipelineOrchestrator.GetCdPipelinesReleaseInfoForApp(appIds, cdWfrIds, fetchWithUnmappedData, envIds)
	if err != nil {
		impl.logger.Errorw("error encountered in getReleaseDeploymentInfoForDependencies", "appIds", appIds, "cdWfrIds", cdWfrIds, "err", err)
		return nil, nil, err
	}
	return pipelinesInfo, pipelineIdAppIdKeyVsReleaseInfo, nil
}

func (impl *DevtronResourceServiceExtendedImpl) fetchEnvIdsBasedOnReleaseTarget(objectData string) (*bean.EnvIdsAndAllTargeted, error) {
	// fetch target if not found return target as nil
	// if all targeted set to true target as nil
	targetKeyResult := gjson.Get(objectData, bean.ReleaseTargetPathKey)
	// Step2: if target key is not present or target( areAllTargeted true) it will be true as all released will be present, return true
	if targetKeyResult.Exists() {
		targetString := gjson.Get(objectData, bean.ReleaseTargetPathKey).String()
		var targetSchema bean.TargetSchema
		err := json.Unmarshal([]byte(targetString), &targetSchema)
		if err != nil {
			impl.logger.Errorw("error in unmarshalling target schema", "targetString", targetString, "err", err)
			return nil, err
		}
		if targetSchema.AreAllTargeted {
			return adapter.BuildEnvIdsAndAreAllTargeted(true, nil), nil
		} else {
			installationIds, installationSchemaId := helper.ExtractIdsAndSchemaIdFromIdAndSchemaIds(targetSchema.Entities)
			envIds, err := impl.dtResObjRelationReadService.GetEnvIdsForInstallationIds(installationIds, installationSchemaId)
			if err != nil {
				impl.logger.Errorw("error encountered in fetchEnvIdsBasedOnReleaseTarget", "installationIds", installationIds, "installationSchemaId", installationSchemaId, "err", err)
				return nil, err
			}
			return adapter.BuildEnvIdsAndAreAllTargeted(false, envIds), nil
		}
	}
	return adapter.BuildEnvIdsAndAreAllTargeted(true, nil), nil
}

// fetchAllReleaseInfoStatusWithMap will fetch all release info with mappings
func (impl *DevtronResourceServiceExtendedImpl) fetchAllReleaseInfoStatusWithMap(existingResourceObject *repository.DevtronResourceObject, rsIdentifier string, fetchWithUnmappedData bool) (map[string]*bean.CdPipelineReleaseInfo, []*bean.CdPipelineReleaseInfo, error) {
	var dependencyBean []*bean.CdPipelineReleaseInfo
	var pipelineIdAppIdKeyVsReleaseInfo map[string]*bean.CdPipelineReleaseInfo
	var err error
	allApplicationDependencies := getAllApplicationDependenciesFromObjectData(existingResourceObject.ObjectData)
	if len(allApplicationDependencies) != 0 {
		// will get map here for all application dependencies and all data as well
		dependencyBean, pipelineIdAppIdKeyVsReleaseInfo, err = impl.getReleaseDeploymentInfoForDependencies(rsIdentifier, allApplicationDependencies, fetchWithUnmappedData, existingResourceObject.ObjectData)
		if err != nil {
			impl.logger.Errorw("error encountered in fetchReleaseTaskRunInfo", "rsIdentifier", rsIdentifier, "err", err)
			return nil, nil, err
		}
		if !fetchWithUnmappedData {
			// If all the applications in all stages are deployed to their respective environments,
			// Mark the rollout status -> bean.CompletelyDeployedReleaseRolloutStatus
			err = impl.markRolloutStatusBasedOnStatusFromMap(existingResourceObject, pipelineIdAppIdKeyVsReleaseInfo, allApplicationDependencies)
			if err != nil {
				impl.logger.Errorw("error encountered in fetchReleaseTaskRunInfoWithFilters", "rsIdentifier", rsIdentifier, "existingResourceObjectId", existingResourceObject.Id, "err", err)
				return nil, nil, err
			}
		}
	}
	return pipelineIdAppIdKeyVsReleaseInfo, dependencyBean, nil
}

func (impl *DevtronResourceServiceExtendedImpl) markRolloutStatusBasedOnTargetInObjectData(objectData string, releaseIdAndSchemaId *bean.IdAndSchemaId) (string, error) {
	allApplicationDependencies := getAllApplicationDependenciesFromObjectData(objectData)
	if len(allApplicationDependencies) != 0 {
		rsIdentifier := helper.GetTaskRunSourceIdentifier(releaseIdAndSchemaId.Id, bean.ResourceObjectIdType, releaseIdAndSchemaId.DevtronResourceSchemaId)
		// will get map here for all application dependencies and all data as well
		_, pipelineIdAppIdKeyVsReleaseInfo, err := impl.getReleaseDeploymentInfoForDependencies(rsIdentifier, allApplicationDependencies, false, objectData)
		if err != nil {
			impl.logger.Errorw("error encountered in fetchReleaseTaskRunInfo", "rsIdentifier", rsIdentifier, "err", err)
			return "", err
		}
		// If all the applications in all stages are deployed to their respective environments,
		// Mark the rollout status -> bean.CompletelyDeployedReleaseRolloutStatus if all target envs are satisfied
		objectData, err = impl.setRolloutStatusInObjectDataBasedOnStatus(objectData, pipelineIdAppIdKeyVsReleaseInfo, allApplicationDependencies)
		if err != nil {
			impl.logger.Errorw("error encountered in fetchReleaseTaskRunInfoWithFilters", "rsIdentifier", rsIdentifier, "releaseId", releaseIdAndSchemaId, "err", err)
			return "", err
		}
	}
	return objectData, nil
}

// getArtifactAndPipelineMapFromTasks returns three maps appVsArtifactIdMap, artifactIdVsArtifactMap , artifactIdVsArtifactMap
// STEP 1: appVsArtifactIdMap maps app vs artifact id mapped in resource object artifact config
// STEP 2: GetArtifactsIds in bulk and appId Vs Artifact id Map
func (impl *DevtronResourceServiceExtendedImpl) getArtifactMapForRelease(objectData string, dRSchemaId int) (appVsArtifactIdMap map[int]int, artifactIdVsArtifactMap map[int]*repository2.CiArtifact, appIdVsDrSchemaDetail map[int]*bean.DepDetail, appIdVsDepDetail map[int]*bean.DtResDepJsonBean, err error) {
	appVsArtifactIdMap = make(map[int]int)
	// updated appVsArtifactId map
	artifactIds, appIdVsDrSchemaDetail, appIdVsDepDetail, err := impl.updateArtifactIdAndReturnIdsForDependencies(dRSchemaId, objectData, appVsArtifactIdMap)
	if err != nil {
		impl.logger.Errorw("error encountered in performFeasibilityChecks", "objectData", objectData, "appVsArtifactIdMap", appVsArtifactIdMap, "err", err)
		return appVsArtifactIdMap, artifactIdVsArtifactMap, appIdVsDrSchemaDetail, appIdVsDepDetail, err
	}
	// getting all artifacts here with mapping artifact id to artifact
	artifactIdVsArtifactMap, err = impl.getArtifactIdVsArtifactMapForIds(artifactIds)
	if err != nil {
		impl.logger.Errorw("error encountered in performFeasibilityChecks", "objectData", objectData, "artifactIds", artifactIds, "err", err)
		return appVsArtifactIdMap, artifactIdVsArtifactMap, appIdVsDrSchemaDetail, appIdVsDepDetail, err
	}
	return appVsArtifactIdMap, artifactIdVsArtifactMap, appIdVsDrSchemaDetail, appIdVsDepDetail, nil
}

func (impl *DevtronResourceServiceExtendedImpl) getArtifactIdVsArtifactMapForIds(artifactsIds []int) (map[int]*repository2.CiArtifact, error) {
	artifactIdVsArtifactMap := make(map[int]*repository2.CiArtifact, len(artifactsIds))
	artifacts, err := impl.ciArtifactRepository.GetByIds(artifactsIds)
	if err != nil {
		impl.logger.Errorw("error encountered in getArtifactIdVsArtifactMapForIds", "err", err)
		return artifactIdVsArtifactMap, err
	}
	for _, artifact := range artifacts {
		artifactIdVsArtifactMap[artifact.Id] = artifact
	}
	return artifactIdVsArtifactMap, nil
}

func checkIfTaskExecutionAllowed(tasks []*bean.Task, levelTenInsKeyVsTaskRunAllowedMap map[string]bool, appIdVsLevelIdMap map[int]int, pipelineIdVsPipelineMap map[int]*pipelineConfig.Pipeline, envIdVsInstallationMappingConfig map[int]*bean.InstallationMappingConfigInternalBean) error {
	for _, task := range tasks {
		pipeline := pipelineIdVsPipelineMap[task.PipelineId]
		if pipeline == nil {
			return util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.PipelineNotAvailableMessage, bean.PipelineNotAvailableMessage)
		}
		mappingConfig := envIdVsInstallationMappingConfig[pipeline.EnvironmentId]
		if mappingConfig == nil {
			return util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidInstallationEnvMapping, bean.InvalidInstallationEnvMapping)
		}
		levelId, ok := appIdVsLevelIdMap[task.AppId]
		if !ok {
			return util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidStageAppMapping, bean.InvalidStageAppMapping)
		}
		if val, ok := levelTenInsKeyVsTaskRunAllowedMap[helper.GetKeyForLevelAndTenantInstallationId(levelId, mappingConfig.TenantId, mappingConfig.InstallationId)]; !ok || !val {
			return util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.StageTaskExecutionNotAllowedMessage, bean.StageTaskExecutionNotAllowedMessage)
		}
	}
	return nil
}

func (impl *DevtronResourceServiceImpl) handleDefaultTargetForRelease(ctx context.Context, reqBean *bean.DtResObjInternalBean, schemaId int, objectData string) (*bean9.CommonUpdateResDepsOutput, error) {
	// adding default target for release as all
	releaseTarget := adapter.BuildReleaseTargetSchema(true, nil)
	updatedObjectData, err := helper.PatchResourceObjectDataAtAPath(objectData, bean.ReleaseTargetPathKey, releaseTarget)
	if err != nil {
		impl.logger.Errorw("error in handleDefaultTargetForRelease", "releaseTarget", releaseTarget, "err", err)
		return nil, err
	}
	return adapter5.BuildCommonUpdateResDepsOutput(nil, updatedObjectData), nil
}

func (impl *DevtronResourceServiceImpl) policyAreAllReleasedTargetPresent(objectData string, releaseIdAndSchemaId *bean.IdAndSchemaId) (bean4.PolicyReleaseAreAllReleasedTargetPresent, error) {
	allPresent, err := impl.areAllReleasedTargetPresent(objectData, releaseIdAndSchemaId)
	if err != nil {
		impl.logger.Errorw("error encountered in policyAreAllReleasedTargetPresent", "objectData", objectData, "err", err)
		return "", err
	}
	if allPresent {
		return bean4.ReleasePolicyAreAllReleasedTargetPresentYes, nil
	}
	return bean4.ReleasePolicyAreAllReleasedTargetPresentNo, nil
}
func (impl *DevtronResourceServiceImpl) areAllReleasedTargetPresent(objectData string, releaseIdAndSchemaId *bean.IdAndSchemaId) (bool, error) {
	// Step1: get target from object data
	targetKeyResult := gjson.Get(objectData, bean.ReleaseTargetPathKey)
	// Step2: if target key is not present or target( areAllTargeted true) it will be true as all released will be present, return true
	if targetKeyResult.Exists() {
		targetString := gjson.Get(objectData, bean.ReleaseTargetPathKey).String()
		var targetSchema bean.TargetSchema
		err := json.Unmarshal([]byte(targetString), &targetSchema)
		if err != nil {
			impl.logger.Errorw("error in unmarshalling target schema", "targetString", targetString, "err", err)
			return false, err
		}
		if targetSchema.AreAllTargeted {
			return true, nil
		} else {
			installationIds := make([]int, len(targetSchema.Entities))
			for i, installationEntity := range targetSchema.Entities {
				installationIds[i] = installationEntity.Id
			}
			// will fetch installation identifiers from ids and also if any release as been triggered in that installation.
			installationIdentifiers, err := impl.dtResObjectRepository.GetIdentifiersByIds(installationIds)
			if err != nil {
				impl.logger.Errorw("error in updateTargetForGetApiResObj", "installationIds", installationIds, "err", err)
				return false, err
			}
			mapOfInstallationIdentifier := make(map[string]bool, len(installationIdentifiers))
			for _, installationIdentifier := range installationIdentifiers {
				mapOfInstallationIdentifier[installationIdentifier] = true
			}
			// Step3: if specific entities are present, will check all released pipelines(env) - installation live mapping ,
			// installation are present in request, if any fails return false , else true
			mapOfInstallationIdentifierVsIsReleased, err := impl.getMapOfInstallationIdentifierVsTriggeredForRelease(releaseIdAndSchemaId, nil)
			if err != nil {
				impl.logger.Errorw("error in areAllReleasedTargetPresent", "installationIds", installationIds, "err", err)
				return false, err
			}
			for installationIdentifier, _ := range mapOfInstallationIdentifierVsIsReleased {
				if _, ok := mapOfInstallationIdentifier[installationIdentifier]; !ok {
					return false, nil
				}
			}
		}
	}
	// if key does not exist or all installation present return true
	return true, nil
}
