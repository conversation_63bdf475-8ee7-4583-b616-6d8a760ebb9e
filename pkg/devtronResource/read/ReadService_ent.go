/*
 * Copyright (c) 2024. Devtron Inc.
 */

package read

import (
	"encoding/json"
	"fmt"
	"github.com/devtron-labs/devtron/internal/util"
	repository2 "github.com/devtron-labs/devtron/pkg/auth/user/repository"
	"github.com/devtron-labs/devtron/pkg/devtronResource/bean"
	"github.com/devtron-labs/devtron/pkg/devtronResource/repository"
	"github.com/devtron-labs/devtron/util/feHelper"
	"github.com/go-pg/pg"
	"github.com/tidwall/gjson"
	"go.uber.org/zap"
	"net/http"
	"strings"
)

type ReadService interface {
	CheckIfExistingDtObj(id, devtronResourceSchemaId int, idType bean.IdType, identifier string) (exists bool, err error)
	CheckIfDtObjExistsByIdAndIdType(id, devtronResourceSchemaId int, idType bean.IdType) (exists bool, err error)
	GetTaskRunSourceInfoForReleaseIds(releaseIds []int) (map[int]*bean.TaskRunSource, error)
	GetTaskRunSourceInfoForReleases(releases []*repository.DevtronResourceObject) (map[int]*bean.TaskRunSource, error)
	// GetResAndSchemaFromResType gets resource and schema from cached map
	GetResAndSchemaFromResType(resourceType *bean.DtResTypeInternalReq) (dtResource *repository.DevtronResource, dtSchema *repository.DevtronResourceSchema, err error)
	// SetDtResourcesAndSchemaMap is the method for caching all the devtron resources and the resource schemas.
	SetDtResourcesAndSchemaMap() error
	GetDtResourcesByIdMap() map[int]*repository.DevtronResource
	GetDtResourcesByKindMap() map[string]*repository.DevtronResource
	GetDtResourcesSchemaByIdMap() map[int]*repository.DevtronResourceSchema
	GetEnvInstallationReleaseChannelAndTenantSchema() (*repository.DevtronResourceSchema, *repository.DevtronResourceSchema, *repository.DevtronResourceSchema, *repository.DevtronResourceSchema, error)
	GetUpstreamDepIdsByObjIdOrIdentifier(componentId int, componentIdentifier, componentKind, componentSubKind, componentVersion string) ([]int, error)
	GetUpstreamDepIdAndObjIdsMapByObjType(componentKind, componentSubKind bean.DtResKind, componentVersion bean.DtResVersion) (map[int][]int, error)
	GetUpstreamDepIdAndObjIdsMapByObjTypeAndIds(objIds []int, componentKind, componentSubKind bean.DtResKind, componentVersion bean.DtResVersion) (map[int][]int, error)
	GetObjIdByIdentifier(componentIdentifier string, componentKind, componentSubKind bean.DtResKind, componentVersion bean.DtResVersion) (int, error)
	GetAllReleaseAndReleaseTrackIdsMap() (releaseTrackIdsMap map[int]bool, releaseIdsMap map[int]bool, releaseIdReleaseTrackIdMap map[int]int, err error)
	GetAllReleaseIdsMapByReleaseTrackId(releaseTrackId int) ([]int, map[int]bool, error)
	GetDtApplicationAndReleaseTrackSchema() (*repository.DevtronResourceSchema, *repository.DevtronResourceSchema, error)
	GetReleaseAndReleaseTrackSchema() (*repository.DevtronResourceSchema, *repository.DevtronResourceSchema, error)
	GetReleaseTrackSchema() (*repository.DevtronResourceSchema, error)
	GetDtApplicationSchema() (*repository.DevtronResourceSchema, error)
	GetReleaseChannelSchema() (*repository.DevtronResourceSchema, error)
	GetEnvSchema() (*repository.DevtronResourceSchema, error)
	GetInstallationSchema() (*repository.DevtronResourceSchema, error)

	GetSourceDetailForDevtronApp(appId int) (int, string, string, error)
	GetUserSchemaDataById(userId int32) (*bean.UserSchema, error)
}
type ReadServiceImpl struct {
	logger                         *zap.SugaredLogger
	dtResObjRepository             repository.DevtronResourceObjectRepository
	dtResRepository                repository.DevtronResourceRepository
	dtResSchemaRepository          repository.DevtronResourceSchemaRepository
	dtResObjDepRelationsRepository repository.DtResObjDepRelationsRepository
	templateRepository             repository.TemplateRepository
	userRepository                 repository2.UserRepository
	dtResourcesMapById             map[int]*repository.DevtronResource       //map of id and its object
	dtResourcesMapByKind           map[string]*repository.DevtronResource    //map of kind and its object
	dtResourcesSchemaMapById       map[int]*repository.DevtronResourceSchema //map of id and its object
}

func NewReadServiceImpl(logger *zap.SugaredLogger,
	devtronResourceObjectRepository repository.DevtronResourceObjectRepository, devtronResourceRepository repository.DevtronResourceRepository,
	devtronResourceSchemaRepository repository.DevtronResourceSchemaRepository,
	dtResObjDepRelationsRepository repository.DtResObjDepRelationsRepository,
	templateRepository repository.TemplateRepository,
	userRepository repository2.UserRepository) (*ReadServiceImpl, error) {
	readServiceImpl := &ReadServiceImpl{
		logger:                         logger,
		dtResObjRepository:             devtronResourceObjectRepository,
		dtResRepository:                devtronResourceRepository,
		dtResSchemaRepository:          devtronResourceSchemaRepository,
		dtResObjDepRelationsRepository: dtResObjDepRelationsRepository,
		templateRepository:             templateRepository,
		userRepository:                 userRepository,
	}
	err := readServiceImpl.SetDtResourcesAndSchemaMap()
	if err != nil {
		return nil, err
	}
	return readServiceImpl, nil
}

// CheckIfExistingDevtronObject : this method check if it is existing object in the db.
func (impl *ReadServiceImpl) CheckIfExistingDtObj(id, devtronResourceSchemaId int, idType bean.IdType, identifier string) (exists bool, err error) {
	if id > 0 {
		exists, err = impl.CheckIfDtObjExistsByIdAndIdType(id, devtronResourceSchemaId, idType)
		if err != nil {
			impl.logger.Errorw("error in checking object exists by id", "err", err, "id", id, "idType", idType, "devtronResourceSchemaId", devtronResourceSchemaId)
			return exists, err
		}
	} else if len(identifier) > 0 {
		exists, err = impl.dtResObjRepository.CheckIfExistByIdentifier(identifier, devtronResourceSchemaId)
		if err != nil {
			impl.logger.Errorw("error in checking object exists by identifier", "err", err, "identifier", identifier, "devtronResourceSchemaId", devtronResourceSchemaId)
			return exists, err
		}
	}
	return exists, err
}

// CheckIfDevtronObjectExistsByIdAndIdType : this method check if it is existing object in the db by resource id or oldObjectId.
func (impl *ReadServiceImpl) CheckIfDtObjExistsByIdAndIdType(id, devtronResourceSchemaId int, idType bean.IdType) (exists bool, err error) {
	if idType == bean.ResourceObjectIdType {
		exists, err = impl.dtResObjRepository.CheckIfExistById(id, devtronResourceSchemaId)
		if err != nil {
			return exists, err
		}
	} else if idType == bean.OldObjectId {
		exists, err = impl.dtResObjRepository.CheckIfExistByOldObjectId(id, devtronResourceSchemaId)
		if err != nil {
			return exists, err
		}
	} else {
		return exists, fmt.Errorf(bean.IdTypeNotSupportedError)
	}
	return exists, err
}

func (impl *ReadServiceImpl) GetTaskRunSourceInfoForReleaseIds(releaseIds []int) (map[int]*bean.TaskRunSource, error) {
	//getting release objects
	releases, err := impl.dtResObjRepository.FindByIds(releaseIds)
	if err != nil {
		impl.logger.Errorw("error in getting release objects", "err", err, "releaseIds", releaseIds)
		return nil, err
	}
	return impl.GetTaskRunSourceInfoForReleases(releases)
}

func (impl *ReadServiceImpl) GetTaskRunSourceInfoForReleases(releases []*repository.DevtronResourceObject) (map[int]*bean.TaskRunSource, error) {
	releaseTrackIdReleaseIdMap := make(map[int][]int)
	releaseTrackIds := make([]int, 0)
	resp := make(map[int]*bean.TaskRunSource, len(releases))
	for _, release := range releases {
		resp[release.Id] = &bean.TaskRunSource{
			Kind:           bean.DevtronResourceRelease,
			Version:        bean.DevtronResourceVersionAlpha1,
			Id:             release.Id,
			Identifier:     release.Identifier,
			ReleaseVersion: gjson.Get(release.ObjectData, bean.ReleaseResourceObjectReleaseVersionPath).String(),
			Name:           gjson.Get(release.ObjectData, bean.ResourceObjectNamePath).String(),
		}
		//getting parent dependency
		parentDep := gjson.Get(release.ObjectData, `dependencies.#(typeOfDependency=="parent")#`)
		releaseTrackDepStr := parentDep.Array()[0].String()
		releaseTrackId := int(gjson.Get(releaseTrackDepStr, bean.IdKey).Int())
		releaseTrackIds = append(releaseTrackIds, releaseTrackId)
		releaseTrackIdReleaseIdMap[releaseTrackId] = append(releaseTrackIdReleaseIdMap[releaseTrackId], release.Id)
	}
	//getting release tracks
	releaseTracks, err := impl.dtResObjRepository.FindByIds(releaseTrackIds)
	if err != nil {
		impl.logger.Errorw("error in getting releaseTrack objects", "err", err, "releaseTrackIds", releaseTrackIds)
		return nil, err
	}
	for _, releaseTrack := range releaseTracks {
		releaseTrackName := gjson.Get(releaseTrack.ObjectData, bean.ResourceObjectNamePath).String()
		for _, releaseId := range releaseTrackIdReleaseIdMap[releaseTrack.Id] {
			resp[releaseId].ReleaseTrackName = releaseTrackName
		}
	}
	return resp, nil
}

func (impl *ReadServiceImpl) SetDtResourcesAndSchemaMap() error {
	devtronResources, err := impl.dtResRepository.GetAll()
	if err != nil {
		impl.logger.Errorw("error in getting devtron resources, SetDtResourcesAndSchemaMap", "err", err)
		return err
	}
	devtronResourcesMap := make(map[int]*repository.DevtronResource)
	devtronResourcesMapByKind := make(map[string]*repository.DevtronResource)
	for _, devtronResource := range devtronResources {
		devtronResourcesMap[devtronResource.Id] = devtronResource
		devtronResourcesMapByKind[devtronResource.Kind] = devtronResource
	}
	devtronResourceSchemas, err := impl.dtResSchemaRepository.GetAll()
	if err != nil {
		impl.logger.Errorw("error in getting devtron resource schemas, SetDtResourcesAndSchemaMap", "err", err)
		return err
	}
	devtronResourceSchemasMap := make(map[int]*repository.DevtronResourceSchema)
	for _, devtronResourceSchema := range devtronResourceSchemas {
		devtronResourceSchemasMap[devtronResourceSchema.Id] = devtronResourceSchema
	}
	impl.dtResourcesMapById = devtronResourcesMap
	impl.dtResourcesMapByKind = devtronResourcesMapByKind
	impl.dtResourcesSchemaMapById = devtronResourceSchemasMap
	return nil
}

func (impl *ReadServiceImpl) GetDtResourcesByIdMap() map[int]*repository.DevtronResource {
	return impl.dtResourcesMapById
}

func (impl *ReadServiceImpl) GetDtResourcesByKindMap() map[string]*repository.DevtronResource {
	return impl.dtResourcesMapByKind
}

func (impl *ReadServiceImpl) GetDtResourcesSchemaByIdMap() map[int]*repository.DevtronResourceSchema {
	return impl.dtResourcesSchemaMapById
}

func (impl *ReadServiceImpl) GetResAndSchemaFromResType(resourceType *bean.DtResTypeInternalReq) (dtResource *repository.DevtronResource, dtSchema *repository.DevtronResourceSchema, err error) {
	kindSplits := strings.Split(resourceType.ResourceKind.ToString(), "/")
	var ok bool
	if len(kindSplits) == 1 && len(resourceType.ResourceSubKind) == 0 {
		//no subKind
		kind := kindSplits[0]
		dtResource, ok = impl.GetDtResourcesByKindMap()[kind]
		if !ok {
			impl.logger.Errorw("invalid resourceType", "resourceType", resourceType)
			return dtResource, dtSchema, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidResourceKind, bean.InvalidResourceKind)
		}
		resourceType.ResourceKind = bean.DtResKind(kindSplits[0])
	} else if len(resourceType.ResourceSubKind) != 0 {
		_, ok = impl.GetDtResourcesByKindMap()[kindSplits[0]]
		if !ok {
			impl.logger.Errorw("invalid resourceType", "resourceType", resourceType)
			return dtResource, dtSchema, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidResourceKind, bean.InvalidResourceKind)
		}
		dtResource, ok = impl.GetDtResourcesByKindMap()[resourceType.ResourceSubKind.ToString()]
		if !ok {
			impl.logger.Errorw("invalid resourceType", "resourceType", resourceType)
			return dtResource, dtSchema, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidResourceKind, bean.InvalidResourceKind)
		}
	} else if len(kindSplits) == 2 {
		kind := kindSplits[0]
		subKind := kindSplits[1]
		_, ok = impl.GetDtResourcesByKindMap()[kind]
		if !ok {
			impl.logger.Errorw("invalid resourceType", "resourceType", resourceType)
			return dtResource, dtSchema, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidResourceKind, bean.InvalidResourceKind)
		}
		dtResource, ok = impl.GetDtResourcesByKindMap()[subKind]
		if !ok {
			impl.logger.Errorw("invalid resourceType", "resourceType", resourceType)
			return dtResource, dtSchema, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidResourceKind, bean.InvalidResourceKind)
		}
	}
	//check version
	if supportedVersionsMap, ok :=
		bean.DevtronResourceSupportedVersionMap[bean.DtResKind(dtResource.Kind)]; !ok || !supportedVersionsMap[resourceType.ResourceVersion] {
		impl.logger.Errorw("invalid resourceType", "resourceType", resourceType)
		return dtResource, dtSchema, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidResourceVersion, bean.InvalidResourceVersion)
	}
	dtSchema, err = impl.getSchemaByResourceIdAndVersion(dtResource.Id, resourceType.ResourceVersion)
	if err != nil {
		impl.logger.Errorw("invalid resourceType", "resourceType", resourceType)
		return dtResource, dtSchema, err
	}
	return dtResource, dtSchema, nil
}

func (impl *ReadServiceImpl) getSchemaByResourceIdAndVersion(resourceId int,
	version bean.DtResVersion) (*repository.DevtronResourceSchema, error) {
	for _, schema := range impl.GetDtResourcesSchemaByIdMap() {
		if schema.DevtronResourceId == resourceId && schema.Version == version.ToString() {
			return schema, nil
		}
	}
	return nil, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidResourceVersion, bean.InvalidResourceVersion)
}

func (impl *ReadServiceImpl) GetEnvInstallationReleaseChannelAndTenantSchema() (*repository.DevtronResourceSchema, *repository.DevtronResourceSchema, *repository.DevtronResourceSchema, *repository.DevtronResourceSchema, error) {
	_, envSchema, err := impl.GetResAndSchemaFromResType(bean.EnvironmentResTypeIntReq)
	if err != nil {
		impl.logger.Errorw("error in getEnvInstallationAndTenantSchema for environment", "err", err)
		return nil, nil, nil, nil, err
	}
	_, installationSchema, err := impl.GetResAndSchemaFromResType(bean.InstallationResTypeIntReq)
	if err != nil {
		impl.logger.Errorw("error in getEnvInstallationAndTenantSchema for installation", "err", err)
		return nil, nil, nil, nil, err
	}
	_, tenantSchema, err := impl.GetResAndSchemaFromResType(bean.TenantResTypeIntReq)
	if err != nil {
		impl.logger.Errorw("error in getEnvInstallationAndTenantSchema for tenant", "err", err)
		return nil, nil, nil, nil, err
	}
	releaseChannelSchema, err := impl.GetReleaseChannelSchema()
	if err != nil {
		impl.logger.Errorw("error in getEnvInstallationAndTenantSchema for tenant", "err", err)
		return nil, nil, nil, nil, err
	}
	return envSchema, installationSchema, tenantSchema, releaseChannelSchema, nil
}

func (impl *ReadServiceImpl) GetUpstreamDepIdsByObjIdOrIdentifier(componentId int, componentIdentifier, componentKind, componentSubKind, componentVersion string) ([]int, error) {
	_, dtResSchema, err := impl.GetResAndSchemaFromResType(&bean.DtResTypeInternalReq{ResourceKind: bean.DtResKind(componentKind), ResourceSubKind: bean.DtResKind(componentSubKind), ResourceVersion: bean.DtResVersion(componentVersion)})
	if err != nil {
		impl.logger.Errorw("error, GetResAndSchemaFromResType", "kind", componentKind, "version", componentVersion, "err", err)
		return nil, err
	}
	schemaId := dtResSchema.Id
	if componentId == 0 {
		ids, err := impl.dtResObjRepository.GetIdsByIdentifiers([]string{componentIdentifier}, schemaId)
		if err != nil {
			impl.logger.Errorw("error, GetIdsByIdentifiers", "componentIdentifier", componentIdentifier, "schemaId", schemaId, "err", err)
			return nil, err
		}
		if len(ids) == 1 {
			componentId = ids[0]
		} else {
			return nil, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.ResourceDoesNotExistMessage, bean.ResourceDoesNotExistMessage)
		}
	}
	upstreamMappings, err := impl.dtResObjDepRelationsRepository.GetAllForAComponentAndDepType(componentId, schemaId, bean.DevtronResourceDependencyTypeUpstream.ToString())
	if err != nil && err != pg.ErrNoRows {
		impl.logger.Errorw("error encountered in GetAllMappingsForObjForADepType", "componentObjectId", componentId, "err", err)
		return nil, err
	}
	upstreamIds := make([]int, 0, len(upstreamMappings))
	for _, upstreamMapping := range upstreamMappings {
		upstreamIds = append(upstreamIds, upstreamMapping.DependencyObjectId)
	}
	return upstreamIds, nil
}

func (impl *ReadServiceImpl) GetObjIdByIdentifier(componentIdentifier string, componentKind, componentSubKind bean.DtResKind, componentVersion bean.DtResVersion) (int, error) {
	_, dtResSchema, err := impl.GetResAndSchemaFromResType(&bean.DtResTypeInternalReq{ResourceKind: componentKind, ResourceSubKind: componentSubKind, ResourceVersion: componentVersion})
	if err != nil {
		impl.logger.Errorw("error, GetResAndSchemaFromResType", "kind", componentKind, "version", componentVersion, "err", err)
		return 0, err
	}
	schemaId := dtResSchema.Id
	ids, err := impl.dtResObjRepository.GetIdsByIdentifiers([]string{componentIdentifier}, schemaId)
	if err != nil {
		impl.logger.Errorw("error, GetIdsByIdentifiers", "componentIdentifier", componentIdentifier, "schemaId", schemaId, "err", err)
		return 0, err
	}
	if len(ids) == 1 {
		return ids[0], nil
	} else {
		return 0, util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.ResourceDoesNotExistMessage, bean.ResourceDoesNotExistMessage)
	}
}

func (impl *ReadServiceImpl) GetUpstreamDepIdAndObjIdsMapByObjType(componentKind, componentSubKind bean.DtResKind, componentVersion bean.DtResVersion) (map[int][]int, error) {
	_, dtResSchema, err := impl.GetResAndSchemaFromResType(&bean.DtResTypeInternalReq{ResourceKind: componentKind, ResourceSubKind: componentSubKind, ResourceVersion: componentVersion})
	if err != nil {
		impl.logger.Errorw("error, GetResAndSchemaFromResType", "kind", componentKind, "version", componentVersion, "err", err)
		return nil, err
	}
	schemaId := dtResSchema.Id
	upstreamMappings, err := impl.dtResObjDepRelationsRepository.GetAllForAComponentTypeAndDepType(schemaId, bean.DevtronResourceDependencyTypeUpstream.ToString())
	if err != nil && err != pg.ErrNoRows {
		impl.logger.Errorw("error encountered in GetAllForAComponentTypeAndDepType", "schemaId", schemaId, "err", err)
		return nil, err
	}
	depIdObjIdsMap := make(map[int][]int, len(upstreamMappings))
	for _, upstreamMapping := range upstreamMappings {
		depIdObjIdsMap[upstreamMapping.DependencyObjectId] = append(depIdObjIdsMap[upstreamMapping.DependencyObjectId], upstreamMapping.ComponentObjectId)
	}
	return depIdObjIdsMap, nil
}

func (impl *ReadServiceImpl) GetUpstreamDepIdAndObjIdsMapByObjTypeAndIds(objIds []int, componentKind, componentSubKind bean.DtResKind, componentVersion bean.DtResVersion) (map[int][]int, error) {
	_, dtResSchema, err := impl.GetResAndSchemaFromResType(&bean.DtResTypeInternalReq{ResourceKind: componentKind, ResourceSubKind: componentSubKind, ResourceVersion: componentVersion})
	if err != nil {
		impl.logger.Errorw("error, GetResAndSchemaFromResType", "kind", componentKind, "version", componentVersion, "err", err)
		return nil, err
	}
	schemaId := dtResSchema.Id
	upstreamMappings, err := impl.dtResObjDepRelationsRepository.GetAllForMultipleComponentsAndDepType(objIds, schemaId, bean.DevtronResourceDependencyTypeUpstream.ToString())
	if err != nil && err != pg.ErrNoRows {
		impl.logger.Errorw("error encountered in GetAllForAComponentTypeAndDepType", "objIds", objIds, "schemaId", schemaId, "err", err)
		return nil, err
	}
	depIdObjIdsMap := make(map[int][]int, len(upstreamMappings))
	for _, upstreamMapping := range upstreamMappings {
		depIdObjIdsMap[upstreamMapping.DependencyObjectId] = append(depIdObjIdsMap[upstreamMapping.DependencyObjectId], upstreamMapping.ComponentObjectId)
	}
	return depIdObjIdsMap, nil
}

func (impl *ReadServiceImpl) GetAllReleaseAndReleaseTrackIdsMap() (map[int]bool, map[int]bool, map[int]int, error) {
	_, releaseTrackSchema, err := impl.GetResAndSchemaFromResType(bean.ReleaseTrackResTypeIntReq)
	if err != nil {
		impl.logger.Errorw("error, GetResAndSchemaFromResType for releaseTrack", "err", err)
		return nil, nil, nil, err
	}
	_, releaseSchema, err := impl.GetResAndSchemaFromResType(bean.ReleaseResTypeIntReq)
	if err != nil {
		impl.logger.Errorw("error, GetResAndSchemaFromResType for release", "err", err)
		return nil, nil, nil, err
	}

	releaseTrackObjs, err := impl.dtResObjRepository.GetAllWithSchemaId(releaseTrackSchema.Id)
	if err != nil && !util.IsErrNoRows(err) {
		impl.logger.Errorw("error in getting all objects for releaseTrack", "err", err)
		return nil, nil, nil, err
	}
	releaseTrackIdsMap := make(map[int]bool, len(releaseTrackObjs))
	releaseIdsMap := make(map[int]bool, len(releaseTrackObjs))
	releaseIdReleaseTrackIdMap := make(map[int]int, len(releaseTrackObjs))
	if len(releaseTrackObjs) > 0 {
		for _, releaseTrackObj := range releaseTrackObjs {
			releaseTrackIdsMap[releaseTrackObj.Id] = true
		}
		//not getting release from objRepo but rather than mappings, because if release exists then parent mapping has to exist
		releaseParentMappings, err := impl.dtResObjDepRelationsRepository.GetAllForAComponentTypeAndDepType(releaseSchema.Id, bean.DevtronResourceDependencyTypeParent.ToString())
		if err != nil && err != pg.ErrNoRows {
			impl.logger.Errorw("error encountered in GetAllForAComponentTypeAndDepType, parent of releases", "err", err)
			return nil, nil, nil, err
		}
		for _, releaseParentMapping := range releaseParentMappings {
			releaseIdsMap[releaseParentMapping.ComponentObjectId] = true
			releaseIdReleaseTrackIdMap[releaseParentMapping.ComponentObjectId] = releaseParentMapping.DependencyObjectId
		}
	}
	return releaseTrackIdsMap, releaseIdsMap, releaseIdReleaseTrackIdMap, nil
}

func (impl *ReadServiceImpl) GetAllReleaseIdsMapByReleaseTrackId(releaseTrackId int) ([]int, map[int]bool, error) {
	_, releaseSchema, err := impl.GetResAndSchemaFromResType(bean.ReleaseResTypeIntReq)
	if err != nil {
		impl.logger.Errorw("error, GetResAndSchemaFromResType for release", "err", err)
		return nil, nil, err
	}
	//if releaseTrackId present then gets releases list by it, else all releases
	if releaseTrackId > 0 {
		_, releaseTrackSchema, err := impl.GetResAndSchemaFromResType(bean.ReleaseTrackResTypeIntReq)
		if err != nil {
			impl.logger.Errorw("error, GetResAndSchemaFromResType for releaseTrack", "err", err)
			return nil, nil, err
		}
		childMappings, err := impl.dtResObjDepRelationsRepository.GetAllForADependencyType(releaseTrackId, releaseTrackSchema.Id, bean.DevtronResourceDependencyTypeParent.ToString())
		if err != nil {
			impl.logger.Errorw("error in GetAllForADependencyType", "err", err)
			return nil, nil, err
		}
		releaseIdsMap := make(map[int]bool, len(childMappings))
		releaseIds := make([]int, 0, len(childMappings))
		for _, childMapping := range childMappings {
			releaseIdsMap[childMapping.ComponentObjectId] = true
			releaseIds = append(releaseIds, childMapping.ComponentObjectId)
		}
		return releaseIds, releaseIdsMap, nil
	} else {
		releaseObjs, err := impl.dtResObjRepository.GetAllWithSchemaId(releaseSchema.Id)
		if err != nil && !util.IsErrNoRows(err) {
			impl.logger.Errorw("error in getting all objects for release", "err", err)
			return nil, nil, err
		}
		releaseIdsMap := make(map[int]bool, len(releaseObjs))
		releaseIds := make([]int, 0, len(releaseObjs))
		for _, releaseObj := range releaseObjs {
			releaseIdsMap[releaseObj.Id] = true
			releaseIds = append(releaseIds, releaseObj.Id)
		}
		return releaseIds, releaseIdsMap, nil
	}
}

func (impl *ReadServiceImpl) GetDtApplicationAndReleaseTrackSchema() (*repository.DevtronResourceSchema, *repository.DevtronResourceSchema, error) {
	dtInternalReq := &bean.DtResTypeInternalReq{ResourceKind: bean.DevtronResourceApplication, ResourceSubKind: bean.DevtronResourceDevtronApplication, ResourceVersion: bean.DevtronResourceVersion1}
	_, dtApplicationSchema, err := impl.GetResAndSchemaFromResType(dtInternalReq)
	if err != nil {
		impl.logger.Errorw("error in GetDtApplicationAndReleaseTrackSchema", "dtInternalReq", dtInternalReq, "err", err)
		return nil, nil, err
	}
	releaseTrackInternalReq := &bean.DtResTypeInternalReq{ResourceKind: bean.DevtronResourceReleaseTrack, ResourceSubKind: "", ResourceVersion: bean.DevtronResourceVersionAlpha1}
	_, releaseTrackSchema, err := impl.GetResAndSchemaFromResType(releaseTrackInternalReq)
	if err != nil {
		impl.logger.Errorw("error in GetDtApplicationAndReleaseTrackSchema", "releaseTrackInternalReq", releaseTrackInternalReq, "err", err)
		return nil, nil, err
	}
	return dtApplicationSchema, releaseTrackSchema, nil

}

func (impl *ReadServiceImpl) GetReleaseAndReleaseTrackSchema() (*repository.DevtronResourceSchema, *repository.DevtronResourceSchema, error) {
	releaseInternalReq := &bean.DtResTypeInternalReq{ResourceKind: bean.DevtronResourceRelease, ResourceSubKind: "", ResourceVersion: bean.DevtronResourceVersionAlpha1}
	_, releaseSchema, err := impl.GetResAndSchemaFromResType(releaseInternalReq)
	if err != nil {
		impl.logger.Errorw("error in GetReleaseAndReleaseTrackSchema", "releaseInternalReq", releaseInternalReq, "err", err)
		return nil, nil, err
	}
	releaseTrackInternalReq := &bean.DtResTypeInternalReq{ResourceKind: bean.DevtronResourceReleaseTrack, ResourceSubKind: "", ResourceVersion: bean.DevtronResourceVersionAlpha1}
	_, releaseTrackSchema, err := impl.GetResAndSchemaFromResType(releaseTrackInternalReq)
	if err != nil {
		impl.logger.Errorw("error in GetReleaseAndReleaseTrackSchema", "releaseTrackInternalReq", releaseTrackInternalReq, "err", err)
		return nil, nil, err
	}
	return releaseSchema, releaseTrackSchema, nil

}

func (impl *ReadServiceImpl) GetReleaseTrackSchema() (*repository.DevtronResourceSchema, error) {
	releaseTrackInternalReq := &bean.DtResTypeInternalReq{ResourceKind: bean.DevtronResourceReleaseTrack, ResourceSubKind: "", ResourceVersion: bean.DevtronResourceVersionAlpha1}
	_, releaseTrackSchema, err := impl.GetResAndSchemaFromResType(releaseTrackInternalReq)
	if err != nil {
		impl.logger.Errorw("error in GetDtApplicationAndReleaseTrackSchema", "releaseTrackInternalReq", releaseTrackInternalReq, "err", err)
		return nil, err
	}
	return releaseTrackSchema, nil

}

func (impl *ReadServiceImpl) GetDtApplicationSchema() (*repository.DevtronResourceSchema, error) {
	dtInternalReq := &bean.DtResTypeInternalReq{ResourceKind: bean.DevtronResourceApplication, ResourceSubKind: bean.DevtronResourceDevtronApplication, ResourceVersion: bean.DevtronResourceVersion1}
	_, dtApplicationSchema, err := impl.GetResAndSchemaFromResType(dtInternalReq)
	if err != nil {
		impl.logger.Errorw("error in GetDtApplicationSchema", "dtInternalReq", dtInternalReq, "err", err)
		return nil, err
	}
	return dtApplicationSchema, nil

}

func (impl *ReadServiceImpl) GetReleaseChannelSchema() (*repository.DevtronResourceSchema, error) {
	_, releaseChannelSchema, err := impl.GetResAndSchemaFromResType(bean.ReleaseChannelResTypeIntReq)
	if err != nil {
		impl.logger.Errorw("error in GetReleaseChannelSchema", "err", err)
		return nil, err
	}
	return releaseChannelSchema, nil

}
func (impl *ReadServiceImpl) GetEnvSchema() (*repository.DevtronResourceSchema, error) {
	_, envSchema, err := impl.GetResAndSchemaFromResType(bean.EnvironmentResTypeIntReq)
	if err != nil {
		impl.logger.Errorw("error in GetEnvSchema for environment", "err", err)
		return nil, err
	}
	return envSchema, nil

}

func (impl *ReadServiceImpl) GetInstallationSchema() (*repository.DevtronResourceSchema, error) {
	_, installationSchema, err := impl.GetResAndSchemaFromResType(bean.InstallationResTypeIntReq)
	if err != nil {
		impl.logger.Errorw("error in GetInstallationSchema for installation", "err", err)
		return nil, err
	}
	return installationSchema, nil

}

func (impl *ReadServiceImpl) GetSourceDetailForDevtronApp(appId int) (templateId int, templateIdentifier, templateName string, err error) {

	dtInternalReq := &bean.DtResTypeInternalReq{ResourceKind: bean.DevtronResourceApplication, ResourceSubKind: bean.DevtronResourceDevtronApplication, ResourceVersion: bean.DevtronResourceVersion1}
	_, dtApplicationSchema, err := impl.GetResAndSchemaFromResType(dtInternalReq)
	if err != nil {
		impl.logger.Errorw("error in GetDtApplicationAndReleaseTrackSchema", "dtInternalReq", dtInternalReq, "err", err)
		return templateId, templateIdentifier, templateName, err
	}
	obj, err := impl.dtResObjRepository.FindByOldObjectId(appId, dtApplicationSchema.Id)
	if err != nil && !util.IsErrNoRows(err) {
		impl.logger.Errorw("error in GetDevtronApp", "err", err)
		return
	}
	if obj != nil && len(obj.SourceDetail) > 0 {
		templateSource := bean.ResourceObjectSource{}
		err = json.Unmarshal([]byte(obj.SourceDetail), &templateSource)
		if err != nil {
			return templateId, templateIdentifier, templateName, err
		}
		if templateSource.SourceTemplateId > 0 || len(templateSource.SourceTemplateIdentifier) > 0 {
			templateDb, err := impl.templateRepository.GetByIdOrIdentifierForSourceDetail(templateSource.SourceTemplateId, templateSource.SourceTemplateIdentifier)
			if err != nil && !util.IsErrNoRows(err) {
				return templateId, templateIdentifier, templateName, err
			}
			templateDb.Name = feHelper.GetModifiedDataStrIfDeleted(templateDb.Name, templateDb.Deleted)
			return templateDb.Id, templateDb.Identifier, templateDb.Name, nil
		}
	}
	return templateId, templateIdentifier, templateName, nil
}

func (impl *ReadServiceImpl) GetUserSchemaDataById(userId int32) (*bean.UserSchema, error) {
	userDetails, err := impl.userRepository.GetById(userId)
	if err != nil {
		impl.logger.Errorw("found error on getting user data ", "userId", userId)
		return nil, err
	}
	return &bean.UserSchema{
		Icon: true,
		Id:   userDetails.Id,
		Name: userDetails.EmailId}, nil
}
