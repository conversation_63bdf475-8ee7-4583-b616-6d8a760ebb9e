package template

import (
	"github.com/devtron-labs/devtron/pkg/bean"
)

type CommonTemplateObj struct {
	AppId                              int
	ChartRefIds                        []int //chartRefIds of the base deployment template
	CIPipelineIds                      []int
	ExternalCIPipelineIds              []int
	CDPipelineIds                      []int
	CDPipelineEnvIds                   []int //envIds of cdPipelines in the app. envId related to a pipeline can be found at same index of any pipelineId in CDPipelineIds
	CDPipelineEnvIdLatestChartRefIdMap map[int]int
}

type CommonTemplateCUDObj struct {
	Action         ActionType
	GitMaterial    GitMaterialCUDObj
	CiWorkflowData CiWorkflowCUDObj
}

type GitMaterialCUDObj struct {
	Req               *bean.CreateMaterialDTO
	FinalGitMaterials []*bean.GitMaterial
}
type CiWorkflowCUDObj struct {
	Req             *bean.CiPatchRequest
	FinalCiWorkflow *bean.CiConfigRequest
}

type TemplateJsonObjMultipleRespOfAPI struct {
	EnvIdDataMap map[int]interface{} `json:"envIdDataMap,omitempty"`
}

// TemplateDataJson Map that'll be stored as json string in db
// The key will be "entityType-MethodUsage-MethodType" and the value will be the json of response of the api
// example, key: "GitMaterial-listAllGitMaterials-GET" and value: json of response of the api mentioned
type TemplateDataJson = map[string]interface{}
