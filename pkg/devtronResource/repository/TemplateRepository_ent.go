package repository

import (
	"fmt"
	"github.com/devtron-labs/devtron/pkg/sql"
	"github.com/devtron-labs/devtron/util/rand"
	"github.com/go-pg/pg"
	"go.uber.org/zap"
	"time"
)

type TemplateRepository interface {
	Save(model *DevtronResourceTemplate) error
	UpdateJsonDataForATemplate(id int, identifier, data string, userId int32) error
	UpdateDescriptionForATemplate(id int, identifier, description string, userId int32) error
	MarkTemplateAsDeleted(id int, identifier string, userId int32) error
	GetByIdOrIdentifier(id int, identifier string) (*DevtronResourceTemplate, error)
	GetByIdOrIdentifierForSourceDetail(id int, identifier string) (*DevtronResourceTemplate, error)
	GetMinDataForListing(schemaId int) ([]*DevtronResourceTemplate, error)
	GetMinDataForOverview(id int, identifier string) (*DevtronResourceTemplate, error)
}

type TemplateRepositoryImpl struct {
	logger       *zap.SugaredLogger
	dbConnection *pg.DB
	*sql.TransactionUtilImpl
}

func NewTemplateRepositoryImpl(logger *zap.SugaredLogger,
	dbConnection *pg.DB) *TemplateRepositoryImpl {
	return &TemplateRepositoryImpl{
		logger:              logger,
		dbConnection:        dbConnection,
		TransactionUtilImpl: sql.NewTransactionUtilImpl(dbConnection),
	}
}

type DevtronResourceTemplate struct {
	tableName               struct{} `sql:"devtron_resource_template" pg:",discard_unknown_columns"`
	Id                      int      `sql:"id,pk"`
	DevtronResourceSchemaId int      `sql:"devtron_resource_schema_id"`
	DataVersion             string   `sql:"data_version"`
	SourceDetails           string   `sql:"source_details"`
	Name                    string   `sql:"name"`
	Identifier              string   `sql:"identifier"`
	Description             string   `sql:"description"`
	Data                    string   `sql:"data"` //json string
	IsPreset                bool     `sql:"is_preset,notnull"`
	Deleted                 bool     `sql:"deleted,notnull"`
	EmailId                 string   `sql:"-"` //createdByEmail
	UserActive              bool     `sql:"-"` //createdByUser active or not
	sql.AuditLog
}

func (repo *TemplateRepositoryImpl) Save(model *DevtronResourceTemplate) error {
	model.DataVersion = rand.GetNewUUIDString()
	err := repo.dbConnection.Insert(model)
	if err != nil {
		repo.logger.Errorw("error in saving resource template", "model", model, "err", err)
		return err
	}
	return nil
}

func (repo *TemplateRepositoryImpl) UpdateJsonDataForATemplate(id int, identifier, data string, userId int32) error {
	query := repo.dbConnection.Model((*DevtronResourceTemplate)(nil)).
		Set("data = ?", data).Set("data_version = ?", rand.GetNewUUIDString()).
		Set("updated_by = ?", userId).Set("updated_on = ?", time.Now())
	if id > 0 {
		query = query.Where("id = ?", id)
	} else if len(identifier) > 0 {
		query = query.Where("identifier = ?", identifier)
	} else {
		return fmt.Errorf("invalid input param, id or identifier is required")
	}
	_, err := query.Update()
	return err
}

func (repo *TemplateRepositoryImpl) UpdateDescriptionForATemplate(id int, identifier, description string, userId int32) error {
	query := repo.dbConnection.Model((*DevtronResourceTemplate)(nil)).
		Set("description = ?", description).Set("data_version = ?", rand.GetNewUUIDString()).
		Set("updated_by = ?", userId).Set("updated_on = ?", time.Now())
	if id > 0 {
		query = query.Where("id = ?", id)
	} else if len(identifier) > 0 {
		query = query.Where("identifier = ?", identifier)
	} else {
		return fmt.Errorf("invalid input param, id or identifier is required")
	}
	_, err := query.Update()
	return err
}

func (repo *TemplateRepositoryImpl) MarkTemplateAsDeleted(id int, identifier string, userId int32) error {
	query := repo.dbConnection.Model((*DevtronResourceTemplate)(nil)).Set("deleted = ?", true).
		Set("updated_by = ?", userId).Set("updated_on = ?", time.Now())
	if id > 0 {
		query = query.Where("id = ?", id)
	} else if len(identifier) > 0 {
		query = query.Where("identifier = ?", identifier)
	} else {
		return fmt.Errorf("invalid input param, id or identifier is required")
	}
	_, err := query.Update()
	return err
}

func (repo *TemplateRepositoryImpl) GetByIdOrIdentifier(id int, identifier string) (*DevtronResourceTemplate, error) {
	model := &DevtronResourceTemplate{}
	query := repo.dbConnection.Model(model).
		Where("deleted = ?", false)
	if id > 0 {
		query = query.Where("id = ?", id)
	} else if len(identifier) > 0 {
		query = query.Where("identifier = ?", identifier)
	} else {
		return nil, fmt.Errorf("invalid input param, id or identifier is required")
	}

	err := query.Select()
	if err != nil {
		return nil, err
	}
	return model, nil
}

func (repo *TemplateRepositoryImpl) GetByIdOrIdentifierForSourceDetail(id int, identifier string) (*DevtronResourceTemplate, error) {
	model := &DevtronResourceTemplate{}
	query := repo.dbConnection.Model(model) //no deleted = false clause since need for sourrce detail
	if id > 0 {
		query = query.Where("id = ?", id)
	} else if len(identifier) > 0 {
		query = query.Where("identifier = ?", identifier)
	} else {
		return nil, fmt.Errorf("invalid input param, id or identifier is required")
	}

	err := query.Select()
	if err != nil {
		return nil, err
	}
	return model, nil
}

func (repo *TemplateRepositoryImpl) GetMinDataForListing(schemaId int) ([]*DevtronResourceTemplate, error) {
	models := make([]*DevtronResourceTemplate, 0)
	err := repo.dbConnection.Model(&models).
		Column("devtron_resource_template.id", "devtron_resource_template.name",
			"devtron_resource_template.identifier", "devtron_resource_template.description",
			"devtron_resource_template.is_preset", "users.email_id").
		Join("INNER JOIN users ON devtron_resource_template.created_by = users.id").
		Where("devtron_resource_template.deleted = ?", false).
		Where("devtron_resource_schema_id = ?", schemaId).Select()
	if err != nil {
		return nil, err
	}
	return models, nil
}

func (repo *TemplateRepositoryImpl) GetMinDataForOverview(id int, identifier string) (*DevtronResourceTemplate, error) {
	model := &DevtronResourceTemplate{}
	query := repo.dbConnection.Model(model).
		Column("devtron_resource_template.id", "devtron_resource_template.name",
			"devtron_resource_template.identifier", "devtron_resource_template.description",
			"users.email_id",
			"devtron_resource_template.created_on").
		ColumnExpr("users.active as user_active").
		Join("INNER JOIN users ON devtron_resource_template.created_by = users.id").
		Where("devtron_resource_template.deleted = ?", false)
	if id > 0 {
		query = query.Where("devtron_resource_template.id = ?", id)
	} else if len(identifier) > 0 {
		query = query.Where("devtron_resource_template.identifier = ?", identifier)
	} else {
		return nil, fmt.Errorf("invalid input param, id or identifier is required")
	}
	err := query.Select()
	if err != nil {
		return nil, err
	}
	return model, nil
}
