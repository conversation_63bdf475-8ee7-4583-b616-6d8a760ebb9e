package devtronResource

import (
	"encoding/json"
	"github.com/devtron-labs/devtron/internal/util"
	"github.com/devtron-labs/devtron/pkg/app"
	bean2 "github.com/devtron-labs/devtron/pkg/bean"
	"github.com/devtron-labs/devtron/pkg/bean/configMapBean"
	"github.com/devtron-labs/devtron/pkg/devtronResource/adapter"
	template2 "github.com/devtron-labs/devtron/pkg/devtronResource/adapter/template"
	"github.com/devtron-labs/devtron/pkg/devtronResource/bean"
	"github.com/devtron-labs/devtron/pkg/devtronResource/bean/template"
	devtronResourceHelper "github.com/devtron-labs/devtron/pkg/devtronResource/helper"
	bean3 "github.com/devtron-labs/devtron/pkg/pipeline/bean"
	"github.com/juju/errors"
	"github.com/tidwall/sjson"
	"net/http"
	"time"
)

func (impl *TemplateServiceImpl) CreateTemplate(reqBean *template.CreateTemplateReq) (*template.TemplateDto, error) {
	sourceDetails := reqBean.SourceDetail
	var err error
	sourceDetails.Kind, sourceDetails.SubKind, err = devtronResourceHelper.GetKindAndSubKindFrom(sourceDetails.Kind)
	if err != nil {
		return nil, util.GetApiErrorAdapter(400, "400", "template source not supported", "template source not supported")
	}
	if !bean.DtResKind(sourceDetails.Kind).IsApplication() || !bean.DtResKind(sourceDetails.SubKind).IsDevtronApplication() || sourceDetails.Version != bean.DevtronResourceVersionAlpha1 {
		return nil, util.GetApiErrorAdapter(400, "400", "template source not supported", "template source not supported")
	}

	t, err := impl.templateRepository.GetByIdOrIdentifier(0, reqBean.Identifier)
	if err != nil && !util.IsErrNoRows(err) {
		impl.logger.Error("error, GetByIdOrIdentifier", "identifier", reqBean.Identifier, "err", err)
		return nil, err
	} else if t != nil && t.Id >= 0 {
		impl.logger.Debugw("template already exists", "identifier", reqBean.Identifier)
		return nil, util.GetApiErrorAdapter(http.StatusBadRequest, "400", "template already exists", "template already exists")
	}

	dataMap := make(template.TemplateDataJson)
	commonInternalObj, err := impl.getCommonInternalObjForAllAPIsParamsData(sourceDetails)
	if err != nil {
		impl.logger.Errorw("error in getting common object", "sourceDetails", sourceDetails, "err", err)
		return nil, err
	}
	for key, dataGetFunc := range createTemplateDataMapKeyVsFunc {
		resp, err := dataGetFunc(impl, commonInternalObj)
		if err != nil {
			impl.logger.Errorw("error in executing dataFunc for createTemplateDataMap creation", "key", key, "err", err)
			//TODO: confirm if need to break whole flow or can have logic to skip parts if they do not hinder other data
		}

		dataMap[key] = resp
	}

	_, sourceResourceSchema, err := impl.readService.GetResAndSchemaFromResType(adapter.BuildDtResTypeInternalReq(sourceDetails.Kind, sourceDetails.SubKind, sourceDetails.Version.ToString()))
	if err != nil {
		return nil, err
	}

	createDbObj, err := template2.GetDbObjForCreateRequest(reqBean, sourceResourceSchema.Id, dataMap)
	if err != nil {
		impl.logger.Errorw("error in getting createDbObj", "err", err)
		return nil, err
	}
	err = impl.templateRepository.Save(createDbObj)
	if err != nil {
		impl.logger.Errorw("error in saving createDbObj", "createDbObj", createDbObj, "err", err)
		return nil, err
	}
	responseDto, _ := template2.GetDtoObjForCreateRequest(createDbObj)
	return responseDto, nil
}

var createTemplateDataMapKeyVsFunc = map[string]func(impl *TemplateServiceImpl, commonInternalObj template.CommonTemplateObj) (resp interface{}, err error){
	template.GitMaterialEntity:            (*TemplateServiceImpl).createTemplateGitMaterial,
	template.BuildConfigEntity:            (*TemplateServiceImpl).createTemplateBuildConfig,
	template.DeploymentTemplateBaseEntity: (*TemplateServiceImpl).createTemplateDeploymentTemplateBaseLevel,
	template.DeploymentTemplateEnvEntity:  (*TemplateServiceImpl).createTemplateDeploymentTemplateEnvLevel,
	template.CIPipelineEntity:             (*TemplateServiceImpl).createTemplateCIPipelines,
	template.CDPipelineEntity:             (*TemplateServiceImpl).createTemplateCDPipelines,
	template.StrategyEntity:               (*TemplateServiceImpl).createTemplateStrategyForApp,
	template.ExternalCIEntity:             (*TemplateServiceImpl).createTemplateExternalCIPipelines,
	template.ConfigMapBaseEntity:          (*TemplateServiceImpl).createTemplateGlobalCMData,
	template.SecretBaseEntity:             (*TemplateServiceImpl).createTemplateGlobalSecretData,
	template.ConfigMapEnvEntity:           (*TemplateServiceImpl).createTemplateEnvCMData,
	template.SecretEnvEntity:              (*TemplateServiceImpl).createTemplateEnvSecretData,
	template.WorkflowEntity:               (*TemplateServiceImpl).createTemplateWorkflowData,
	template.MetaDataEntity:               (*TemplateServiceImpl).createTemplateMetaData,
}

func (impl *TemplateServiceImpl) getCommonInternalObjForAllAPIsParamsData(sourceDetails *template.TemplateSourceDetail) (commonInternalObj template.CommonTemplateObj, err error) {
	appId := sourceDetails.Id
	exists, err := impl.appRepository.IsCustomDevtronApp(appId)
	if err != nil {
		impl.logger.Errorw("error, IsCustomDevtronApp check", "appId", appId, "err", err)
		return commonInternalObj, err
	}
	if !exists {
		return commonInternalObj, util.GetApiErrorAdapter(400, "400", "source app does not exists", "source app does not exist")
	}
	charts, err := impl.chartsRepository.FindActiveChartsByAppId(appId)
	if err != nil {
		impl.logger.Errorw("error, FindActiveChartsByAppId", "appId", appId, "err", err)
		return commonInternalObj, err
	}
	chartRefIds := make([]int, 0, len(charts))
	latestChartChartRefId := 0
	for _, chart := range charts {
		chartRefIds = append(chartRefIds, chart.ChartRefId)
		if chart.Latest {
			latestChartChartRefId = chart.ChartRefId
		}
	}
	externalCiPipelines, err := impl.ciPipelineRepository.FindExternalCiByAppId(appId)
	if err != nil && !util.IsErrNoRows(err) {
		impl.logger.Errorw("error in fetching external ci", "appId", appId, "err", err)
		return commonInternalObj, err
	}
	externalCiPipelineIds := make([]int, len(externalCiPipelines))
	for i := range externalCiPipelines {
		externalCiPipelineIds[i] = externalCiPipelines[i].Id
	}
	ciPipelines, err := impl.ciPipelineRepository.FindByAppId(appId)
	if err != nil {
		impl.logger.Errorw("error, FindByAppId ciPipeline", "appId", appId, "err", err)
		return commonInternalObj, err
	}
	ciPipelineIds := make([]int, len(ciPipelines))
	for i := range ciPipelines {
		ciPipelineIds[i] = ciPipelines[i].Id
	}
	cdPipelines, err := impl.pipelineRepository.FindEnvNameAndIdByAppId(appId)
	if err != nil {
		impl.logger.Errorw("error in FindEnvNameAndIdByAppId cdPipeline", "appId", appId, "err", err)
		return commonInternalObj, err
	}
	cdPipelineIds := make([]int, len(cdPipelines))
	cdPipelineEnvIds := make([]int, len(cdPipelines))

	cdPipelineEnvIdLatestChartRefIdMap := make(map[int]int)
	for i := range cdPipelines {
		cdPipelineIds[i] = cdPipelines[i].Id
		envId := cdPipelines[i].EnvironmentId
		cdPipelineEnvIds[i] = envId
		if envId > 0 {
			envOverride, err := impl.envConfigOverrideReadService.FindLatestChartForAppByAppIdAndEnvId(nil, appId, envId)
			if err != nil && !errors.IsNotFound(err) {
				impl.logger.Errorw("error in fetching latest chart", "err", err)
				return commonInternalObj, err
			}
			if envOverride != nil && envOverride.Chart != nil {
				cdPipelineEnvIdLatestChartRefIdMap[envId] = envOverride.Chart.ChartRefId
			} else {
				cdPipelineEnvIdLatestChartRefIdMap[envId] = latestChartChartRefId
			}
		}
	}

	commonInternalObj = template.CommonTemplateObj{
		AppId:                              appId,
		ChartRefIds:                        chartRefIds,
		CIPipelineIds:                      ciPipelineIds,
		ExternalCIPipelineIds:              externalCiPipelineIds,
		CDPipelineIds:                      cdPipelineIds,
		CDPipelineEnvIds:                   cdPipelineEnvIds,
		CDPipelineEnvIdLatestChartRefIdMap: cdPipelineEnvIdLatestChartRefIdMap,
	}
	return commonInternalObj, nil
}

func (impl *TemplateServiceImpl) createTemplateGitMaterial(commonInternalObj template.CommonTemplateObj) (resp interface{}, err error) {
	appId := commonInternalObj.AppId
	ciConf, err := impl.pipelineBuilder.GetAppWithWorkflowCacheConfig(appId, "")
	if err != nil {
		impl.logger.Errorw("service err, GetAppWithWorkflowCacheConfig", "appId", appId, "err", err)
		return resp, err
	}
	return ciConf.Material, nil
}

func (impl *TemplateServiceImpl) createTemplateBuildConfig(commonInternalObj template.CommonTemplateObj) (resp interface{}, err error) {
	appId := commonInternalObj.AppId
	ciConf, err := impl.pipelineBuilder.GetCiPipelineRespResolved(appId)
	if err != nil {
		impl.logger.Errorw("service err, GetCiPipelineRespResolved", "appId", appId, "err", err)
		return resp, err
	}
	if ciConf != nil {
		ciConf.CiPipelines = nil
		ciConf.Materials = nil
	}
	return ciConf, nil
}

func (impl *TemplateServiceImpl) createTemplateDeploymentTemplateBaseLevel(commonInternalObj template.CommonTemplateObj) (resp interface{}, err error) {
	appId := commonInternalObj.AppId
	respCharts := make([]map[string]interface{}, 0, len(commonInternalObj.ChartRefIds))
	for _, chartRefId := range commonInternalObj.ChartRefIds {
		chartBaseLevel, err := impl.chartService.GetDeploymentTemplateDataByAppIdAndCharRefId(appId, chartRefId)
		if err != nil {
			impl.logger.Errorw("service err, CMEnvironmentFetch", "appId", appId, "chartRefId", chartRefId, "err", err)
			return nil, err
		}
		globalConfigStr := string(chartBaseLevel["globalConfig"].(json.RawMessage))
		globalConfigStr, err = sjson.Delete(globalConfigStr, "readme")
		globalConfigStr, err = sjson.Delete(globalConfigStr, "schema")
		chartBaseLevel["globalConfig"] = json.RawMessage(globalConfigStr)
		respCharts = append(respCharts, chartBaseLevel)
	}
	return respCharts, nil
}

func (impl *TemplateServiceImpl) createTemplateDeploymentTemplateEnvLevel(commonInternalObj template.CommonTemplateObj) (resp interface{}, err error) {
	appId := commonInternalObj.AppId
	respMap := make(map[int]interface{}, len(commonInternalObj.CDPipelineEnvIds))
	for _, envId := range commonInternalObj.CDPipelineEnvIds {
		latestEnvChartRefId := commonInternalObj.CDPipelineEnvIdLatestChartRefIdMap[envId]
		envProperties, err := impl.propertiesConfigService.GetEnvironmentProperties(appId, envId, latestEnvChartRefId)
		if err != nil {
			impl.logger.Errorw("service err, GetEnvConfigOverride", "appId", appId, "envId", envId, "latestChartRefId", latestEnvChartRefId, "err", err)
			return nil, err
		}
		envProperties.GlobalConfig = nil
		envProperties.GlobalChartRefId = 0
		envProperties.Readme = ""
		envProperties.Schema = nil
		envProperties.GUISchema = "" //TODO: confirm
		respMap[envId] = []*bean3.EnvironmentPropertiesResponse{envProperties}
	}
	return &template.TemplateJsonObjMultipleRespOfAPI{
		EnvIdDataMap: respMap,
	}, nil
}

func (impl *TemplateServiceImpl) createTemplateGlobalCMData(commonInternalObj template.CommonTemplateObj) (resp interface{}, err error) {
	appId := commonInternalObj.AppId
	//TODO: confirm draft state data removal
	resGlobal, err := impl.configMapService.CMGlobalFetch(appId)
	if err != nil {
		impl.logger.Errorw("service err, CMGlobalFetch", "appId", appId, "err", err)
		return nil, err
	}
	return resGlobal, nil
}

func (impl *TemplateServiceImpl) createTemplateGlobalSecretData(commonInternalObj template.CommonTemplateObj) (resp interface{}, err error) {
	appId := commonInternalObj.AppId
	resGlobal, err := impl.configMapService.CSGlobalFetch(appId)
	if err != nil {
		impl.logger.Errorw("service err, CSGlobalFetch", "appId", appId, "err", err)
		return nil, err
	}
	return resGlobal, nil
}

func (impl *TemplateServiceImpl) createTemplateEnvCMData(commonInternalObj template.CommonTemplateObj) (resp interface{}, err error) {
	appId := commonInternalObj.AppId
	respCM := make([]*configMapBean.ConfigDataRequest, 0, len(commonInternalObj.CDPipelineEnvIds))
	for _, envId := range commonInternalObj.CDPipelineEnvIds {
		resCMEnv, err := impl.configMapService.CMEnvironmentFetch(appId, envId)
		if err != nil {
			impl.logger.Errorw("service err, CMEnvironmentFetch", "appId", appId, "envId", envId, "err", err)
			return nil, err
		}
		if resCMEnv != nil {
			configData := make([]*configMapBean.ConfigData, 0, len(resCMEnv.ConfigData))
			for _, config := range resCMEnv.ConfigData {
				if config.Overridden || !config.Global { //only overridden or env ones
					configData = append(configData, config)
				}
			}
			resCMEnv.ConfigData = configData
		}
		respCM = append(respCM, resCMEnv)
	}
	return respCM, nil
}

func (impl *TemplateServiceImpl) createTemplateEnvSecretData(commonInternalObj template.CommonTemplateObj) (resp interface{}, err error) {
	appId := commonInternalObj.AppId
	respCS := make([]*configMapBean.ConfigDataRequest, 0, len(commonInternalObj.CDPipelineEnvIds))
	for _, envId := range commonInternalObj.CDPipelineEnvIds {
		resCSEnv, err := impl.configMapService.CSEnvironmentFetch(appId, envId)
		if err != nil {
			impl.logger.Errorw("service err, CSEnvironmentFetch", "appId", appId, "envId", envId, "err", err)
			return nil, err
		}
		if resCSEnv != nil {
			configData := make([]*configMapBean.ConfigData, 0, len(resCSEnv.ConfigData))
			for _, config := range resCSEnv.ConfigData {
				if config.Overridden || !config.Global { //only overridden or env ones
					configData = append(configData, config)
				}
			}
			resCSEnv.ConfigData = configData
		}
		respCS = append(respCS, resCSEnv)
	}
	return respCS, nil
}

func (impl *TemplateServiceImpl) createTemplateCIPipelines(commonInternalObj template.CommonTemplateObj) (resp interface{}, err error) {
	respCIPipelines := make([]*bean2.CiPipeline, 0, len(commonInternalObj.CIPipelineIds))
	for _, pipelineId := range commonInternalObj.CIPipelineIds {
		resCIPipeline, err := impl.pipelineBuilder.GetCiPipelineByIdWithDefaultTag(pipelineId)
		if err != nil {
			impl.logger.Errorw("service err, createTemplateCIPipelines", "pipelineId", pipelineId, "err", err)
			return nil, err
		}
		resCIPipeline.LinkedCount = 0
		if len(resCIPipeline.DockerArgs) == 0 {
			resCIPipeline.DockerArgs = make(map[string]string)
		}
		respCIPipelines = append(respCIPipelines, resCIPipeline)
	}
	return respCIPipelines, nil
}

func (impl *TemplateServiceImpl) createTemplateCDPipelines(commonInternalObj template.CommonTemplateObj) (resp interface{}, err error) {
	respCDPipelines := make([]*bean2.CDPipelineConfigObject, 0, len(commonInternalObj.CIPipelineIds))
	for _, pipelineId := range commonInternalObj.CDPipelineIds {
		resCDPipeline, err := impl.pipelineBuilder.GetCdPipelineByIdResolved(pipelineId, "v2")
		if err != nil {
			impl.logger.Errorw("service err, createTemplateCDPipelines", "pipelineId", pipelineId, "err", err)
			return nil, err
		}
		if resCDPipeline.UserApprovalConfigV2 != nil {
			resCDPipeline.UserApprovalConfigV2 = nil
		}
		respCDPipelines = append(respCDPipelines, resCDPipeline)
	}
	return respCDPipelines, nil
}

func (impl *TemplateServiceImpl) createTemplateExternalCIPipelines(commonInternalObj template.CommonTemplateObj) (resp interface{}, err error) {
	appId := commonInternalObj.AppId
	respExternalCIs := make([]*bean2.ExternalCiConfig, 0, len(commonInternalObj.ExternalCIPipelineIds))
	for _, externalCIPipelineId := range commonInternalObj.ExternalCIPipelineIds {
		resEnv, err := impl.pipelineBuilder.GetExternalCiById(appId, externalCIPipelineId)
		if err != nil {
			impl.logger.Errorw("service err, createTemplateExternalCIPipelines", "externalCIPipelineId", externalCIPipelineId, "err", err)
			return nil, err
		}
		respExternalCIs = append(respExternalCIs, resEnv)
	}
	return respExternalCIs, nil
}

func (impl *TemplateServiceImpl) createTemplateStrategyForApp(commonInternalObj template.CommonTemplateObj) (resp interface{}, err error) {
	strategyApp, err := impl.devtronAppStrategyService.FetchCDPipelineStrategy(commonInternalObj.AppId)
	if err != nil {
		impl.logger.Errorw("service err, FetchCDPipelineStrategy", "appId", commonInternalObj.AppId, "err", err)
		return nil, err
	}
	return strategyApp, nil
}

func (impl *TemplateServiceImpl) createTemplateWorkflowData(commonInternalObj template.CommonTemplateObj) (resp interface{}, err error) {
	appId := commonInternalObj.AppId
	workflowResponse, _, err := impl.appWorkflowService.FindAppWorkflowsListResolvedResp(appId) //get all wf for the app
	if err != nil {
		impl.logger.Errorw("service err, FindAppWorkflows", "appId", appId)
		return workflowResponse, err
	}
	return workflowResponse, nil
}

func (impl *TemplateServiceImpl) createTemplateMetaData(commonInternalObj template.CommonTemplateObj) (resp interface{}, err error) {
	appId := commonInternalObj.AppId
	metadata, err := impl.appService.GetAppMetaInfo(appId, app.ZERO_INSTALLED_APP_ID, app.ZERO_ENVIRONMENT_ID)
	if err != nil {
		impl.logger.Errorw("service err, GetAppMetaInfo", "appId", appId, "err", err)
		return resp, err
	}
	if metadata.Note != nil {
		metadata.Note.Description = ""
		metadata.Note.Id = 0
		metadata.Note.UpdatedOn = time.Time{}
		metadata.Note.UpdatedBy = ""
	}
	return metadata, nil
}
