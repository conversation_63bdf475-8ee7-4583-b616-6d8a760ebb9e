package common

import (
	"encoding/json"
	"errors"
	"fmt"
	commonBean "github.com/devtron-labs/common-lib/workflow"
	"github.com/devtron-labs/devtron/pkg/valueConstraint/bean"
)

// RuntimeParameters holds values that needed to be injected/used in ci build process. Currently, only present in enteprise version.
type RuntimeParameters struct {
	EnvVariables           map[string]string           `json:"envVariables"` // Deprecated; use RuntimePluginVariables instead
	RuntimePluginVariables []*RuntimePluginVariableDto `json:"runtimePluginVariables,omitempty" validate:"dive"`
}

func (r *RuntimeParameters) MigrateEnvVariables() *RuntimeParameters {
	if r == nil {
		return r
	}
	runtimeParamsMap := r.GetGlobalRuntimeVariables()
	for k, v := range r.EnvVariables {
		// runtimeParamsMap has higher priority
		// If the key is already present in runtimeParamsMap, don't override it
		if _, ok := runtimeParamsMap[k]; !ok {
			r.RuntimePluginVariables = append(r.RuntimePluginVariables, NewRuntimeGlobalVariableDto(k, v))
		}
	}
	// reset EnvVariables as it is deprecated
	r.EnvVariables = nil
	return r
}

func (r *RuntimeParameters) AddSystemVariable(name, value string) *RuntimeParameters {
	r.RuntimePluginVariables = append(r.RuntimePluginVariables, NewRuntimeSystemVariableDto(name, value))
	return r
}

func (r *RuntimeParameters) AddRuntimeGlobalVariable(name, value string) *RuntimeParameters {
	r.RuntimePluginVariables = append(r.RuntimePluginVariables, NewRuntimeGlobalVariableDto(name, value))
	return r
}

func (r *RuntimeParameters) GetSystemVariables() map[string]string {
	response := make(map[string]string)
	for _, variable := range r.RuntimePluginVariables {
		if variable.IsSystemVariableScope() {
			response[variable.Name] = variable.Value
		}
	}
	return response
}

func (r *RuntimeParameters) GetGlobalRuntimeVariables() map[string]string {
	response := make(map[string]string)
	for _, variable := range r.RuntimePluginVariables {
		if variable.IsGlobalVariableScope() {
			response[variable.Name] = variable.Value
		}
	}
	return response
}

// RuntimePluginVariableDto is used to define the runtime plugin variables.
type RuntimePluginVariableDto struct {
	Name              string            `json:"name" validate:"required"`
	Value             string            `json:"value"`
	Format            commonBean.Format `json:"format" validate:"required"`
	VariableStepScope VariableStepScope `json:"variableStepScope" validate:"oneof=GLOBAL PIPELINE_STAGE"`
	FileReferenceId   int               `json:"fileReferenceId,omitempty"`
	FileMountDir      string            `json:"fileMountDir,omitempty"`
	RuntimePluginVariableApiDto
}

// RuntimePluginVariableApiDto is used to define the additional response fields for the RuntimePluginVariableDto.
type RuntimePluginVariableApiDto struct {
	ValueConstraint *bean.ValueConstraintDto `json:"valueConstraint,omitempty"`
	StepVariableId  int                      `json:"stepVariableId,omitempty"`
	ValueType       RuntimeVariableValueType `json:"valueType,omitempty" validate:"oneof=NEW GLOBAL"`
	RefPluginId     int                      `json:"refPluginId,omitempty"`
	PluginIcon      string                   `json:"pluginIcon,omitempty"`
	Description     string                   `json:"description,omitempty"`
	StepName        string                   `json:"stepName,omitempty"`
	StepType        PipelineStageStepType    `json:"stepType,omitempty"`
	IsRequired      bool                     `json:"isRequired,omitempty"`
}

// PipelineStageStepType is used to define the type of the pipeline stage step.
type PipelineStageStepType string

const (
	// PipelineStepTypeInline refers that the pipeline stage step is of an inline type (script).
	PipelineStepTypeInline PipelineStageStepType = "INLINE"
	// PipelineStepTypeRefPlugin refers that the pipeline stage step is of a reference plugin type (plugin).
	PipelineStepTypeRefPlugin PipelineStageStepType = "REF_PLUGIN"
)

func (p PipelineStageStepType) String() string {
	return string(p)
}

type WorkflowCacheConfigType string

const (
	WorkflowCacheConfigIgnore   WorkflowCacheConfigType = "IGNORE"
	WorkflowCacheConfigUse      WorkflowCacheConfigType = "USE"
	WorkflowCacheConfigInherit  WorkflowCacheConfigType = "INHERIT"
	WorkflowCacheConfigOverride WorkflowCacheConfigType = "OVERRIDE" //this is not saved in DB, only used for UI-purpose
)

func (n WorkflowCacheConfigType) ToString() string {
	return string(n)
}

func (t *WorkflowCacheConfigType) UnmarshalJSON(b []byte) error {
	var s string
	err := json.Unmarshal(b, &s)
	if err != nil {
		return err
	}
	switch s {
	case WorkflowCacheConfigIgnore.ToString():
		fallthrough
	case WorkflowCacheConfigUse.ToString():
		fallthrough
	case WorkflowCacheConfigOverride.ToString():
		fallthrough
	case WorkflowCacheConfigInherit.ToString():
		*t = WorkflowCacheConfigType(s)
		return nil
	case "": //backward compatibility
		*t = WorkflowCacheConfigInherit
		return nil
	default:
		return errors.New("Invalid cache config type in payload")
	}
}

func NewPipelineStageStepType(value string) PipelineStageStepType {
	if value != PipelineStepTypeInline.String() &&
		value != PipelineStepTypeRefPlugin.String() {
		// Default value is INLINE
		return PipelineStepTypeInline
	}
	return PipelineStageStepType(value)
}

// RuntimeVariableValueType is used to define the type of the runtime plugin variable value.
type RuntimeVariableValueType string

const (
	// RuntimeVariableValueTypeNew refers that the runtime plugin variable has a user-defined value.
	RuntimeVariableValueTypeNew RuntimeVariableValueType = "NEW"
	// RuntimeVariableValueTypeGlobal refers that the runtime plugin variable has a global variable.
	RuntimeVariableValueTypeGlobal RuntimeVariableValueType = "GLOBAL"
)

func (r RuntimeVariableValueType) String() string {
	return string(r)
}

func (r RuntimeVariableValueType) IsValid() bool {
	return r == RuntimeVariableValueTypeNew || r == RuntimeVariableValueTypeGlobal
}

func (r RuntimeVariableValueType) IsValueTypeGlobal() bool {
	return r == RuntimeVariableValueTypeGlobal
}

func (r RuntimeVariableValueType) IsValueTypeNew() bool {
	return r == RuntimeVariableValueTypeNew
}

func NewRuntimePluginVariableValueType(value string) RuntimeVariableValueType {
	if value != RuntimeVariableValueTypeNew.String() &&
		value != RuntimeVariableValueTypeGlobal.String() {
		// Default value is NEW
		return RuntimeVariableValueTypeNew
	}
	return RuntimeVariableValueType(value)
}

// NewRuntimeParameters returns a new instance of RuntimeParameters.
func NewRuntimeParameters() *RuntimeParameters {
	return &RuntimeParameters{
		EnvVariables: make(map[string]string),
	}
}

// NewRuntimeGlobalVariableDto returns a new instance of RuntimePluginVariableDto with global variable scope.
func NewRuntimeGlobalVariableDto(name, value string) *RuntimePluginVariableDto {
	return NewRuntimePluginVariableDto(name, value, commonBean.FormatTypeString, GlobalVariableScope)
}

// NewRuntimeSystemVariableDto returns a new instance of RuntimePluginVariableDto with system variable.
func NewRuntimeSystemVariableDto(name, value string) *RuntimePluginVariableDto {
	return NewRuntimePluginVariableDto(name, value, commonBean.FormatTypeString, SystemVariableScope)
}

// NewRuntimePluginVariableDto returns a new instance of RuntimePluginVariableDto.
func NewRuntimePluginVariableDto(name, value string, format commonBean.Format, variableStepScope VariableStepScope) *RuntimePluginVariableDto {
	return &RuntimePluginVariableDto{
		Name:              name,
		Value:             value,
		Format:            format,
		VariableStepScope: variableStepScope,
	}
}

// WithFileReferenceId sets the file reference id for the runtime plugin variable.
// If the variable is nil, it returns nil.
func (r *RuntimePluginVariableDto) WithFileReferenceId(id int) *RuntimePluginVariableDto {
	if r == nil {
		return r
	}
	r.FileReferenceId = id
	return r
}

// WithFileMountDir sets the file mount directory for the runtime plugin variable.
// If the variable is nil, it returns nil.
func (r *RuntimePluginVariableDto) WithFileMountDir(dir string) *RuntimePluginVariableDto {
	if r == nil {
		return r
	}
	r.FileMountDir = dir
	return r
}

// GetUniqueIdentifier returns a unique identifier for the runtime plugin variable.
//   - format: {VariableStepScope}-{StepVariableId}-{Name}
//
// examples:
//   - GLOBAL-0-VARIABLE_1_NAME
//   - GLOBAL-0-VARIABLE_2_NAME
//   - GLOBAL-0-VARIABLE_3_NAME
//   - PIPELINE_STAGE-2-VARIABLE_NAME
//   - PIPELINE_STAGE-3-VARIABLE_NAME
func (r *RuntimePluginVariableDto) GetUniqueIdentifier() string {
	return fmt.Sprintf("%s-%d-%s", r.VariableStepScope, r.StepVariableId, r.Name)
}

// IsSystemVariableScope returns true if the variable is of SYSTEM variable type.
// If the variable is nil, it returns false.
func (r *RuntimePluginVariableDto) IsSystemVariableScope() bool {
	if r == nil {
		return false
	}
	return r.VariableStepScope == SystemVariableScope
}

// IsGlobalVariableScope returns true if the runtime plugin variable is of global variable scope.
// If the variable is nil, it returns false.
func (r *RuntimePluginVariableDto) IsGlobalVariableScope() bool {
	if r == nil {
		return false
	}
	return r.VariableStepScope == GlobalVariableScope
}

// IsPipelineStageVariableScope returns true if the runtime plugin variable is of pipeline stage variable scope.
// If the variable is nil, it returns false.
func (r *RuntimePluginVariableDto) IsPipelineStageVariableScope() bool {
	if r == nil {
		return false
	}
	return r.VariableStepScope == PipelineStageVariableScope
}

// IsEmptyValue returns true if the value of the runtime plugin variable is empty.
// If the variable is nil, it returns true.
func (r *RuntimePluginVariableDto) IsEmptyValue() bool {
	if r == nil {
		return true
	}
	if r.Format == commonBean.FormatTypeFile {
		return len(r.Value) == 0 || r.FileReferenceId == 0
	} else {
		return len(r.Value) == 0
	}
}

// VariableStepScope is used to define the scope of the runtime plugin variable.
type VariableStepScope string

const (
	// GlobalVariableScope is used to define the global variable scope.
	GlobalVariableScope VariableStepScope = "GLOBAL"
	// PipelineStageVariableScope is used to define the pipeline stage variable scope.
	PipelineStageVariableScope VariableStepScope = "PIPELINE_STAGE"
	// SystemVariableScope is used to define the global variable scope.
	SystemVariableScope VariableStepScope = "SYSTEM"
)
