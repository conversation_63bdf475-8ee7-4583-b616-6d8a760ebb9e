/*
 * Copyright (c) 2024. Devtron Inc.
 */

package bean

type EnvironmentBean struct {
	Id                       int                `json:"id,omitempty" validate:"number"`
	Environment              string             `json:"environment_name,omitempty" validate:"required,max=50"`
	ClusterId                int                `json:"cluster_id,omitempty" validate:"number,required"`
	ClusterName              string             `json:"cluster_name,omitempty"`
	Active                   bool               `json:"active"`
	Default                  bool               `json:"default"`
	PrometheusEndpoint       string             `json:"prometheus_endpoint,omitempty"`
	Namespace                string             `json:"namespace,omitempty" validate:"name-space-component,max=50"`
	CdArgoSetup              bool               `json:"isClusterCdActive"`
	EnvironmentIdentifier    string             `json:"environmentIdentifier"`
	Description              string             `json:"description" validate:"max=40"`
	AppCount                 int                `json:"appCount"`
	IsVirtualEnvironment     bool               `json:"isVirtualEnvironment"`
	AllowedDeploymentTypes   []string           `json:"allowedDeploymentTypes"`
	IsDigestEnforcedForEnv   bool               `json:"isDigestEnforcedForEnv"`
	UpdateLabels             bool               `json:"updateLabels"`
	NamespaceResourceVersion string             `json:"namespaceResourceVersion"`
	NamespaceLabels          []*NamespaceLabels `json:"namespaceLabels" validate:"dive"`
	ClusterServerUrl         string             `json:"-"`
	ErrorInConnecting        string             `json:"-"`
	ClusterToken             string             `json:"-"`
	InsecureSkipTlsVerify    bool               `json:"-"`
	ClusterConfig            map[string]string  `json:"-"`
	ClusterCAData            string             `json:"-"`
	ClusterKeyData           string             `json:"-"`
	ClusterCertData          string             `json:"-"`
	DataSourceId             int                `json:"-"`
	Category                 *CategoryDto       `json:"category"`
}

type NamespaceLabels struct {
	Key   string `json:"key" validate:"required"`
	Value string `json:"value" validate:"required"`
}

type VirtualEnvironmentBean struct {
	Id                   int          `json:"id,omitempty" validate:"number"`
	Environment          string       `json:"environment_name,omitempty" validate:"required,max=50"`
	ClusterId            int          `json:"cluster_id,omitempty" validate:"number,required"`
	ClusterName          string       `json:"cluster_name,omitempty"`
	Active               bool         `json:"active"`
	Namespace            string       `json:"namespace,omitempty"`
	Description          string       `json:"description" validate:"max=40"`
	IsVirtualEnvironment bool         `json:"isVirtualEnvironment"`
	IsProd               bool         `json:"isProd"`
	Category             *CategoryDto `json:"category"`
}

type EnvDto struct {
	EnvironmentId         int    `json:"environmentId" validate:"number"`
	EnvironmentName       string `json:"environmentName,omitempty" validate:"max=50"`
	Namespace             string `json:"namespace,omitempty" validate:"name-space-component,max=50"`
	EnvironmentIdentifier string `json:"environmentIdentifier,omitempty"`
	Description           string `json:"description" validate:"max=40"`
	IsVirtualEnvironment  bool   `json:"isVirtualEnvironment"`
	Default               bool   `json:"default"`
}

type ClusterEnvDto struct {
	ClusterId        int       `json:"clusterId"`
	ClusterName      string    `json:"clusterName,omitempty"`
	Environments     []*EnvDto `json:"environments,omitempty"`
	IsVirtualCluster bool      `json:"isVirtualCluster"`
}

type ResourceGroupingResponse struct {
	EnvList  []EnvironmentBean `json:"envList"`
	EnvCount int               `json:"envCount"`
}

const (
	PIPELINE_DEPLOYMENT_TYPE_HELM = "helm"
	PIPELINE_DEPLOYMENT_TYPE_ACD  = "argo_cd"
	PIPELINE_DEPLOYMENT_TYPE_FLUX = "flux_cd"
)

type DataSourceMetaData struct {
	Id   int    `json:"id"`
	Name string `json:"name"`
}

type CategoriesDto struct {
	Categories []*CategoryDto `json:"categories,omitempty" validate:"dive"`
	UserId     int32          `json:"-"`
}
type CategoryDto struct {
	Id          int    `json:"id,omitempty"`
	Name        string `json:"name,omitempty" validate:"max=50"`
	Description string `json:"description,omitempty" validate:"max=350"`
	UserId      int32  `json:"-"`
}
