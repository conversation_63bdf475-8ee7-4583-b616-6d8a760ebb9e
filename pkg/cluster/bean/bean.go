/*
 * Copyright (c) 2024. Devtron Inc.
 */

package bean

import (
	"github.com/devtron-labs/common-lib/utils/k8s"
	"github.com/devtron-labs/common-lib/utils/k8s/commonBean"
	remoteConnectionBean "github.com/devtron-labs/common-lib/utils/remoteConnection/bean"
	bean2 "github.com/devtron-labs/devtron/pkg/cluster/environment/bean"
	"github.com/devtron-labs/devtron/pkg/remoteConnection/bean"
)

const (
	DefaultClusterId = 1
	DefaultCluster   = "default_cluster"
)

type ClusterBean struct {
	Id                      int                              `json:"id" validate:"number"`
	ClusterName             string                           `json:"cluster_name,omitempty" validate:"required"`
	Description             string                           `json:"description"`
	ServerUrl               string                           `json:"server_url,omitempty" validate:"url,required"`
	PrometheusUrl           string                           `json:"prometheus_url,omitempty" validate:"validate-non-empty-url"`
	Active                  bool                             `json:"active"`
	ProxyUrl                string                           `json:"proxyUrl,omitempty"`
	Config                  map[string]string                `json:"config,omitempty"`
	PrometheusAuth          *PrometheusAuth                  `json:"prometheusAuth,omitempty"`
	DefaultClusterComponent []*DefaultClusterComponent       `json:"defaultClusterComponent"`
	AgentInstallationStage  int                              `json:"agentInstallationStage,notnull"` // -1=external, 0=not triggered, 1=progressing, 2=success, 3=fails
	K8sVersion              string                           `json:"k8sVersion"`
	HasConfigOrUrlChanged   bool                             `json:"-"`
	UserName                string                           `json:"userName,omitempty"`
	InsecureSkipTLSVerify   bool                             `json:"insecureSkipTlsVerify"`
	ErrorInConnecting       string                           `json:"errorInConnecting"`
	IsCdArgoSetup           bool                             `json:"isCdArgoSetup"`
	IsVirtualCluster        bool                             `json:"isVirtualCluster"`
	isClusterNameEmpty      bool                             `json:"-"`
	ClusterUpdated          bool                             `json:"clusterUpdated"`
	ToConnectWithSSHTunnel  bool                             `json:"toConnectWithSSHTunnel,omitempty"`
	IsProd                  bool                             `json:"isProd"`
	SSHTunnelConfig         *SSHTunnelConfig                 `json:"sshTunnelConfig,omitempty"`
	RemoteConnectionConfig  *bean.RemoteConnectionConfigBean `json:"remoteConnectionConfig"`
	InstallationId          int                              `json:"installationId"`
	Category                *bean2.CategoryDto               `json:"category"`
}

type DeleteClusterBean struct {
	Id int `json:"id" validate:"number,required"`
}

func (bean *ClusterBean) IsVirtual() bool {
	return bean.IsVirtualCluster
}

type VirtualClusterBean struct {
	Id               int                `json:"id,omitempty" validate:"number"`
	ClusterName      string             `json:"clusterName,omitempty" validate:"required"`
	Active           bool               `json:"active"`
	IsVirtualCluster bool               `json:"isVirtualCluster" default:"true"`
	IsProd           bool               `json:"isProd"`
	Category         *bean2.CategoryDto `json:"category"`
}

type PrometheusAuth struct {
	UserName      string `json:"userName,omitempty"`
	Password      string `json:"password,omitempty"`
	TlsClientCert string `json:"tlsClientCert,omitempty"`
	TlsClientKey  string `json:"tlsClientKey,omitempty"`
	IsAnonymous   bool   `json:"isAnonymous"`
}

type SSHTunnelConfig struct {
	User             string `json:"user"`
	Password         string `json:"password"`
	AuthKey          string `json:"authKey"`
	SSHServerAddress string `json:"sshServerAddress"`
}

func (bean ClusterBean) GetClusterConfig() *k8s.ClusterConfig {
	//bean = *adapter.ConvertClusterBeanToNewClusterBean(&bean)
	configMap := bean.Config
	bearerToken := configMap[commonBean.BearerToken]
	clusterCfg := &k8s.ClusterConfig{
		ClusterId:             bean.Id,
		ClusterName:           bean.ClusterName,
		Host:                  bean.ServerUrl,
		BearerToken:           bearerToken,
		InsecureSkipTLSVerify: bean.InsecureSkipTLSVerify,
	}
	if bean.InsecureSkipTLSVerify == false {
		clusterCfg.KeyData = configMap[commonBean.TlsKey]
		clusterCfg.CertData = configMap[commonBean.CertData]
		clusterCfg.CAData = configMap[commonBean.CertificateAuthorityData]
	}
	if bean.RemoteConnectionConfig != nil {
		clusterCfg.RemoteConnectionConfig = &remoteConnectionBean.RemoteConnectionConfigBean{
			RemoteConnectionConfigId: bean.RemoteConnectionConfig.RemoteConnectionConfigId,
			ConnectionMethod:         remoteConnectionBean.RemoteConnectionMethod(bean.RemoteConnectionConfig.ConnectionMethod),
			ProxyConfig:              (*remoteConnectionBean.ProxyConfig)(bean.RemoteConnectionConfig.ProxyConfig),
			SSHTunnelConfig:          (*remoteConnectionBean.SSHTunnelConfig)(bean.RemoteConnectionConfig.SSHTunnelConfig),
		}
	}
	return clusterCfg
}

type UserInfo struct {
	UserName          string            `json:"userName,omitempty"`
	Config            map[string]string `json:"config,omitempty"`
	ErrorInConnecting string            `json:"errorInConnecting"`
}

type ValidateClusterBean struct {
	UserInfos map[string]*UserInfo `json:"userInfos,omitempty""`
	*ClusterBean
}

type UserClusterBeanMapping struct {
	Mapping map[string]*ClusterBean `json:"mapping"`
}

type Kubeconfig struct {
	Config string `json:"config"`
}

type DefaultClusterComponent struct {
	ComponentName  string `json:"name"`
	AppId          int    `json:"appId"`
	InstalledAppId int    `json:"installedAppId,omitempty"`
	EnvId          int    `json:"envId"`
	EnvName        string `json:"envName"`
	Status         string `json:"status"`
}

type ClusterNamespaceMetadata struct {
	Name            string                  `json:"name"`
	ResourceVersion string                  `json:"resourceVersion"`
	Labels          []bean2.NamespaceLabels `json:"labels"`
}

const (
	DefaultNamespace = "default"
	CmFieldUpdatedOn = "updated_on"
)

type ClusterType string

const (
	ClusterTypeImported        ClusterType = "Imported"
	ClusterTypeVirtual         ClusterType = "Virtual"
	ClusterTypeAutoProvisioned ClusterType = "AutoProvisioned"
)

func (s ClusterType) String() string {
	return string(s)
}

type ClusterCategoryMappingDto struct {
	Id                int   `json:"id,omitempty"`
	ClusterCategoryId int   `json:"cluster_category_id,omitempty"`
	ClusterId         int   `json:"cluster_id,omitempty"`
	UserId            int32 `json:"-"`
}
