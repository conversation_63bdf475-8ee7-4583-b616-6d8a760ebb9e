// Code generated by mockery v2.18.0. DO NOT EDIT.

package mocks

import (
	pg "github.com/go-pg/pg"
	mock "github.com/stretchr/testify/mock"

	repository "github.com/devtron-labs/devtron/pkg/cluster/repository"
)

// EphemeralContainersRepository is an autogenerated mock type for the EphemeralContainersRepository type
type EphemeralContainersRepository struct {
	mock.Mock
}

// CommitTx provides a mock function with given fields: tx
func (_m *EphemeralContainersRepository) CommitTx(tx *pg.Tx) error {
	ret := _m.Called(tx)

	var r0 error
	if rf, ok := ret.Get(0).(func(*pg.Tx) error); ok {
		r0 = rf(tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// FindContainerByName provides a mock function with given fields: clusterID, namespace, podName, name
func (_m *EphemeralContainersRepository) FindContainerByName(clusterID int, namespace string, podName string, name string) (*repository.EphemeralContainerBean, error) {
	ret := _m.Called(clusterID, namespace, podName, name)

	var r0 *repository.EphemeralContainerBean
	if rf, ok := ret.Get(0).(func(int, string, string, string) *repository.EphemeralContainerBean); ok {
		r0 = rf(clusterID, namespace, podName, name)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.EphemeralContainerBean)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, string, string, string) error); ok {
		r1 = rf(clusterID, namespace, podName, name)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RollbackTx provides a mock function with given fields: tx
func (_m *EphemeralContainersRepository) RollbackTx(tx *pg.Tx) error {
	ret := _m.Called(tx)

	var r0 error
	if rf, ok := ret.Get(0).(func(*pg.Tx) error); ok {
		r0 = rf(tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveEphemeralContainerActionAudit provides a mock function with given fields: tx, model
func (_m *EphemeralContainersRepository) SaveEphemeralContainerActionAudit(tx *pg.Tx, model *repository.EphemeralContainerAction) error {
	ret := _m.Called(tx, model)

	var r0 error
	if rf, ok := ret.Get(0).(func(*pg.Tx, *repository.EphemeralContainerAction) error); ok {
		r0 = rf(tx, model)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveEphemeralContainerData provides a mock function with given fields: tx, model
func (_m *EphemeralContainersRepository) SaveEphemeralContainerData(tx *pg.Tx, model *repository.EphemeralContainerBean) error {
	ret := _m.Called(tx, model)

	var r0 error
	if rf, ok := ret.Get(0).(func(*pg.Tx, *repository.EphemeralContainerBean) error); ok {
		r0 = rf(tx, model)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// StartTx provides a mock function with given fields:
func (_m *EphemeralContainersRepository) StartTx() (*pg.Tx, error) {
	ret := _m.Called()

	var r0 *pg.Tx
	if rf, ok := ret.Get(0).(func() *pg.Tx); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pg.Tx)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewEphemeralContainersRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewEphemeralContainersRepository creates a new instance of EphemeralContainersRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewEphemeralContainersRepository(t mockConstructorTestingTNewEphemeralContainersRepository) *EphemeralContainersRepository {
	mock := &EphemeralContainersRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
