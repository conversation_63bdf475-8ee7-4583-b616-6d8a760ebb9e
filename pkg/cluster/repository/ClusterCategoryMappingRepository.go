/*
 * Copyright (c) 2020-2024. Devtron Inc.
 */

package repository

import (
	"github.com/devtron-labs/devtron/pkg/sql"
	"github.com/go-pg/pg"
	"go.uber.org/zap"
)

type ClusterCategoryMapping struct {
	TableName         struct{} `sql:"cluster_category_mapping" pg:",discard_unknown_columns"`
	Id                int      `sql:"id,pk"`
	ClusterId         int      `sql:"cluster_id"`
	ClusterCategoryId int      `sql:"cluster_category_id"`
	sql.AuditLog
	Cluster         *Cluster
	ClusterCategory *ClusterCategory
}

type ClusterCategoryMappingRepository interface {
	GetConnection() *pg.DB
	FindByClusterId(clusterId int) ([]*ClusterCategoryMapping, error)
	FindByClusterIds(clusterIds []int) ([]*ClusterCategoryMapping, error)
	FindByCategoryId(categoryId int, tx *pg.Tx) ([]*ClusterCategoryMapping, error)
	Save(mapping *ClusterCategoryMapping, tx *pg.Tx) error
	Delete(mapping *ClusterCategoryMapping, tx *pg.Tx) error
}

type ClusterCategoryMappingRepositoryImpl struct {
	dbConnection *pg.DB
	Logger       *zap.SugaredLogger
}

func NewClusterCategoryMappingRepositoryImpl(dbConnection *pg.DB, Logger *zap.SugaredLogger) *ClusterCategoryMappingRepositoryImpl {
	return &ClusterCategoryMappingRepositoryImpl{
		dbConnection: dbConnection,
		Logger:       Logger,
	}
}

func (repo *ClusterCategoryMappingRepositoryImpl) GetConnection() *pg.DB {
	return repo.dbConnection
}

func (impl *ClusterCategoryMappingRepositoryImpl) FindByClusterId(clusterId int) ([]*ClusterCategoryMapping, error) {
	var clusterCategoryMapping []*ClusterCategoryMapping
	err := impl.dbConnection.Model(&clusterCategoryMapping).
		Column("cluster_category_mapping.*", "Cluster", "ClusterCategory").
		Where("cluster_category_mapping.cluster_id = ?", clusterId).
		Select()
	return clusterCategoryMapping, err
}

func (impl *ClusterCategoryMappingRepositoryImpl) FindByClusterIds(clusterIds []int) ([]*ClusterCategoryMapping, error) {
	var clusterCategoryMapping []*ClusterCategoryMapping
	err := impl.dbConnection.Model(&clusterCategoryMapping).
		Column("cluster_category_mapping.*", "Cluster", "ClusterCategory").
		Where("cluster_category_mapping.cluster_id in (?)", pg.In(clusterIds)).
		Select()
	return clusterCategoryMapping, err
}

func (impl *ClusterCategoryMappingRepositoryImpl) FindByCategoryId(categoryId int, tx *pg.Tx) ([]*ClusterCategoryMapping, error) {
	var mapping []*ClusterCategoryMapping
	err := tx.Model(&mapping).
		Column("cluster_category_mapping.*", "Cluster", "ClusterCategory").
		Where("cluster_category_mapping.cluster_category_id = ?", categoryId).
		Where("cluster.active = ?", true).Select()
	return mapping, err
}

func (impl *ClusterCategoryMappingRepositoryImpl) Save(mapping *ClusterCategoryMapping, tx *pg.Tx) error {
	err := tx.Insert(mapping)
	return err
}

func (impl *ClusterCategoryMappingRepositoryImpl) Delete(mapping *ClusterCategoryMapping, tx *pg.Tx) error {
	err := tx.Delete(mapping)
	return err
}
