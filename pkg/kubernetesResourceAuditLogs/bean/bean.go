package bean

type K8sResourceAuditLogRequest struct {
	AppId          int    `json:"app_id"`
	EnvId          int    `json:"env_id"`
	Namespace      string `json:"namespace,omitempty"`
	ResourceName   string `json:"resource_name,notnull"`
	Group          string `json:"group"`
	Version        string `json:"version"`
	Kind           string `json:"kind,notnull"`
	Resource       string `json:"resource,notnull"`
	ForceDelete    bool   `json:"force_delete"`
	ActionType     string `json:"action_type"`
	ActionMetadata string `json:"action_metadata"`
	UserId         int32  `json:"user_id"`
}

type K8sResourceAction string

const (
	K8sResourceActionAdd    K8sResourceAction = "add"
	K8sResourceActionDelete K8sResourceAction = "delete"
	K8sResourceActionUpdate K8sResourceAction = "update"
	K8sResourceActionPatch  K8sResourceAction = "patch"
	K8sResourceActionGet    K8sResourceAction = "get"
)
