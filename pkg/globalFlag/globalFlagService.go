package globalFlag

import (
	"encoding/json"
	"github.com/devtron-labs/devtron/pkg/attributes"
	"github.com/devtron-labs/devtron/pkg/auth/user"
	"github.com/devtron-labs/devtron/util"
)

type GlobalFlagService interface {
	CanOnlyViewPermittedData(userId int32) (bool, error)
}

type GlobalFlagServiceImpl struct {
	UserAttributesService attributes.UserAttributesService
	attributesService     attributes.AttributesService
	userService           user.UserService
	globalEnvVariables    *util.GlobalEnvVariables
}

func NewGlobalFlagServiceImpl(userAttributesService attributes.UserAttributesService,
	attributesService attributes.AttributesService,
	userService user.UserService, envVariables *util.EnvironmentVariables) *GlobalFlagServiceImpl {
	return &GlobalFlagServiceImpl{
		UserAttributesService: userAttributesService,
		attributesService:     attributesService,
		userService:           userService,
		globalEnvVariables:    envVariables.GlobalEnvVariables,
	}
}

type UserPreferences struct {
	ViewPermittedEnvOnly bool   `json:"viewPermittedEnvOnly"`
	Theme                string `json:"theme"`
}

const userPreferencesKey = "userPreferences"

func (impl GlobalFlagServiceImpl) CanOnlyViewPermittedData(userId int32) (bool, error) {
	email, err := impl.userService.GetEmailById(userId)
	if err != nil {
		return false, err
	}
	// check if the flag is set at org level
	if impl.globalEnvVariables.CanOnlyViewPermittedEnvOrgLevel {
		return true, nil
	} else {
		// logic to get value from user attributes which is set at user level
		userPreferences, error := impl.UserAttributesService.GetUserAttribute(&attributes.UserAttributesDto{
			EmailId: email,
			Key:     userPreferencesKey,
		})
		if error != nil {
			return false, error
		}
		// default value is false if flag is not set
		if userPreferences == nil || userPreferences.Value == "" {
			return false, nil
		} else {
			// unmarshal userPreferences
			userPreferencesValue := UserPreferences{}
			err := json.Unmarshal([]byte(userPreferences.Value), &userPreferencesValue)
			if err != nil {
				return false, err
			}
			return userPreferencesValue.ViewPermittedEnvOnly, nil
		}
	}
}
