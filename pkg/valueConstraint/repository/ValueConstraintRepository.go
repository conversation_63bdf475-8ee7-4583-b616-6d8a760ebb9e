package repository

import (
	"github.com/devtron-labs/devtron/pkg/sql"
	"github.com/go-pg/pg"
	"github.com/go-pg/pg/orm"
	"time"
)

type ValueConstraintRepository interface {
	// sql.TransactionWrapper is used to start, rollback and commit the transaction
	sql.TransactionWrapper
	// GetValueConstraints returns the multiple value constraints by ids
	//	- returns []*ValueConstraint
	GetValueConstraints(ids []int) ([]*ValueConstraint, error)
	// SaveValueConstraints saves the multiple value constraints in a single transaction
	//	- returns the saved []*ValueConstraint
	SaveValueConstraints(valueConstraints []*ValueConstraint, tx *pg.Tx) ([]*ValueConstraint, error)
	// UpdateValueConstraints updates the multiple value constraints in a single transaction
	//	- returns the updated []*ValueConstraint
	UpdateValueConstraints(valueConstraints []*ValueConstraint, tx *pg.Tx) ([]*ValueConstraint, error)
	// DeleteValueConstraintByIds deletes the multiple value constraints by ids in a single transaction
	//	- returns error
	DeleteValueConstraintByIds(tx *pg.Tx, ids []int, valueType ValueType, userId int32) error
}

type ValueConstraintRepositoryImpl struct {
	dbConnection *pg.DB
	*sql.TransactionUtilImpl
}

func NewValueConstraintRepositoryImpl(dbConnection *pg.DB, transactionUtilImpl *sql.TransactionUtilImpl) *ValueConstraintRepositoryImpl {
	return &ValueConstraintRepositoryImpl{
		dbConnection:        dbConnection,
		TransactionUtilImpl: transactionUtilImpl,
	}
}

// ValueConstraint table is used to store constraints for variable values
//   - Choices: list of choices for variable value
//   - BlockCustomValue: if true, then custom value is blocked
//   - Deleted: if true, then this record is deleted
//
// Note: This table is used for PipelineStageVariable, in future it can be used for PluginPipelineScript as well
type ValueConstraint struct {
	tableName        struct{}  `sql:"value_constraint" pg:",discard_unknown_columns"`
	Id               int       `sql:"id,pk"`
	ValueOf          ValueType `sql:"value_of,notnull"`
	Choices          []string  `sql:"choices,type:text[]" pg:",array"`
	Constraint       string    `sql:"constraint,type:jsonb"`
	BlockCustomValue bool      `sql:"block_custom_value,notnull"`
	Deleted          bool      `sql:"deleted,notnull"`
	// UniqueKey is to identify the record in bulk created operation.
	// It is not stored in DB.
	// For pipeline-stage-step-variable, it is the variable name.
	UniqueKey string `sql:"-"`
	sql.AuditLog
}

func (impl *ValueConstraintRepositoryImpl) GetValueConstraints(ids []int) ([]*ValueConstraint, error) {
	if len(ids) == 0 {
		return make([]*ValueConstraint, 0), nil
	}
	var valueConstraints []*ValueConstraint
	err := impl.dbConnection.Model(&valueConstraints).
		Where("id IN (?)", pg.In(ids)).
		Where("deleted = ?", false).
		Select()
	return valueConstraints, err
}

func (impl *ValueConstraintRepositoryImpl) SaveValueConstraints(valueConstraints []*ValueConstraint, tx *pg.Tx) ([]*ValueConstraint, error) {
	if len(valueConstraints) == 0 {
		return valueConstraints, nil
	}
	var err error
	if tx == nil {
		err = impl.dbConnection.RunInTransaction(func(tx *pg.Tx) error {
			err = tx.Insert(&valueConstraints)
			return err
		})
		return valueConstraints, err
	}
	err = tx.Insert(&valueConstraints)
	return valueConstraints, err
}

func (impl *ValueConstraintRepositoryImpl) UpdateValueConstraints(valueConstraints []*ValueConstraint, tx *pg.Tx) ([]*ValueConstraint, error) {
	if len(valueConstraints) == 0 {
		return valueConstraints, nil
	}
	_, err := tx.Model(&valueConstraints).
		// Exclude the fields which are not allowed to update
		// Intentionally not updating the fields: created_by, created_on, deleted
		// As these fields are not null and doesn't require to update
		Column("choices", "block_custom_value", "constraint", "updated_by", "updated_on").
		UpdateNotNull()
	return valueConstraints, err
}

func (impl *ValueConstraintRepositoryImpl) DeleteValueConstraintByIds(tx *pg.Tx, ids []int, valueType ValueType, userId int32) error {
	if len(ids) == 0 {
		return nil
	}
	var model ValueConstraint
	var queryModel *orm.Query
	if tx == nil {
		queryModel = impl.dbConnection.Model(&model)
	} else {
		queryModel = tx.Model(&model)
	}
	_, err := queryModel.
		Set("deleted = ?", true).
		Set("updated_by = ?", userId).
		Set("updated_on = ?", time.Now()).
		Where("value_of = ?", valueType).
		Where("id IN (?)", pg.In(ids)).
		Update()
	return err
}
