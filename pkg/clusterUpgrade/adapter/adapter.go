package adapter

import (
	"fmt"
	"github.com/devtron-labs/common-lib/utils/k8s"
	bean3 "github.com/devtron-labs/common-lib/utils/remoteConnection/bean"
	"github.com/devtron-labs/devtron/client/silverSurfer/grpc"
	"github.com/devtron-labs/devtron/pkg/clusterUpgrade/bean"
)

func GrpcStructToSingleK8sObjectSummaryReportAdapter(item *grpc.SummaryValidationResult) *bean.SingleK8sObjectSummaryReport {
	// except MigrationStatusMessage the adaptor adapts everything, because MigrationStatusMessage dynamically changes acc. to logic
	if item != nil {
		return &bean.SingleK8sObjectSummaryReport{
			Kind:              item.Kind,
			APIVersion:        item.APIVersion,
			ResourceName:      item.ResourceName,
			ResourceNamespace: item.ResourceNamespace,
			Deleted:           item.Deleted,
			Deprecated:        item.Deprecated,
			LatestAPIVersion:  item.LatestAPIVersion,
			//not adding validation or deprecation error here because they will be added as per the summary bucket they belong to
		}
	}
	return &bean.SingleK8sObjectSummaryReport{}

}

func SummarySchemaErrorAdaptor(req []*grpc.SummarySchemaError) []*bean.SummarySchemaError {
	sses := make([]*bean.SummarySchemaError, 0, len(req))
	for _, item := range req {
		if item != nil {
			sse := &bean.SummarySchemaError{
				Path:        item.Path,
				ErrorReason: item.Reason,
				SchemaField: item.SchemaField,
			}
			sses = append(sses, sse)
		}
	}
	return sses
}

func ConvertClusterConfigToGrpcObj(req *k8s.ClusterConfig) *grpc.ClusterConfig {
	if req != nil {
		return &grpc.ClusterConfig{
			ApiServerUrl:           req.Host,
			Token:                  req.BearerToken,
			ClusterId:              int32(req.ClusterId),
			ClusterName:            req.ClusterName,
			InsecureSkipTLSVerify:  req.InsecureSkipTLSVerify,
			KeyData:                req.KeyData,
			CertData:               req.CertData,
			CaData:                 req.CAData,
			RemoteConnectionConfig: ConvertRemoteConnectionConfigToGrpcObj(req.RemoteConnectionConfig),
		}
	}
	return &grpc.ClusterConfig{}
}

func ConvertRemoteConnectionConfigToGrpcObj(req *bean3.RemoteConnectionConfigBean) *grpc.RemoteConnectionConfig {
	if req != nil {
		var remoteConnectionMethod grpc.RemoteConnectionMethod
		switch req.ConnectionMethod {
		case bean3.RemoteConnectionMethodProxy:
			remoteConnectionMethod = grpc.RemoteConnectionMethod_PROXY
		case bean3.RemoteConnectionMethodSSH:
			remoteConnectionMethod = grpc.RemoteConnectionMethod_SSH
		case bean3.RemoteConnectionMethodDirect:
			remoteConnectionMethod = grpc.RemoteConnectionMethod_DIRECT
		}
		return &grpc.RemoteConnectionConfig{
			RemoteConnectionMethod: remoteConnectionMethod,
			ProxyConfig:            ConvertProxyConfigToGrpcObj(req.ProxyConfig),
			SSHTunnelConfig:        ConvertSSHTunnelConfigToGrpcObj(req.SSHTunnelConfig),
		}
	}
	return &grpc.RemoteConnectionConfig{}
}

func ConvertProxyConfigToGrpcObj(req *bean3.ProxyConfig) *grpc.ProxyConfig {
	if req != nil {
		return &grpc.ProxyConfig{ProxyUrl: req.ProxyUrl}
	}
	return &grpc.ProxyConfig{}
}

func ConvertSSHTunnelConfigToGrpcObj(req *bean3.SSHTunnelConfig) *grpc.SSHTunnelConfig {
	if req != nil {
		return &grpc.SSHTunnelConfig{
			SSHServerAddress: req.SSHServerAddress,
			SSHUsername:      req.SSHUsername,
			SSHPassword:      req.SSHPassword,
			SSHAuthKey:       req.SSHAuthKey,
		}
	}
	return &grpc.SSHTunnelConfig{}
}

func ConvertToClusterUpgradeSummaryReport(summary []*grpc.SummaryValidationResult, reportType bean.ReportType, title string) *bean.ClusterUpgradeSummaryReport {
	clusterUpgradeSummaryReport := bean.NewClusterUpgradeSummaryReport(reportType, title, nil)
	summaryAndValidationDataList := make([]*bean.SummaryAndValidationData, 0, 5)

	k8sResourcesForSummary := make([]*bean.SingleK8sObjectSummaryReport, 0, len(summary))
	k8sResourcesForValidationLatest := make([]*bean.SingleK8sObjectSummaryReport, 0, len(summary))
	k8sResourcesForValidationCurrent := make([]*bean.SingleK8sObjectSummaryReport, 0, len(summary))
	k8sResourcesForDeprecationLatest := make([]*bean.SingleK8sObjectSummaryReport, 0, len(summary))
	k8sResourcesForDeprecationCurrent := make([]*bean.SingleK8sObjectSummaryReport, 0, len(summary))

	for _, item := range summary {
		migrationMessage := bean.CanBeMigratedStatus
		singleK8sSummaryObj := GrpcStructToSingleK8sObjectSummaryReportAdapter(item)
		if len(item.ErrorsForLatest) > 0 {
			migrationMessage = fmt.Sprintf(bean.FixIssuesMigrationStatus, len(item.ErrorsForLatest))
		}
		if item.Deleted {
			migrationMessage = bean.CannotMigrateMigrationStatus
		}
		singleK8sSummaryObj.MigrationStatusMessage = migrationMessage
		k8sResourcesForSummary = append(k8sResourcesForSummary, singleK8sSummaryObj)
		if len(item.ErrorsForLatest) > 0 {
			k8sResourcesForValidationLatest = append(k8sResourcesForValidationLatest,
				getK8sSummaryObjWithUpdatedSummarySchemaErrorsData(singleK8sSummaryObj, item.ErrorsForLatest)...)
		}
		if len(item.ErrorsForOriginal) > 0 && reportType != bean.Removed {
			k8sResourcesForValidationCurrent = append(k8sResourcesForValidationCurrent,
				getK8sSummaryObjWithUpdatedSummarySchemaErrorsData(singleK8sSummaryObj, item.ErrorsForOriginal)...)
		}
		if len(item.DeprecationForOriginal) > 0 && reportType != bean.Removed {
			k8sResourcesForDeprecationCurrent = append(k8sResourcesForDeprecationCurrent,
				getK8sSummaryObjWithUpdatedSummarySchemaErrorsData(singleK8sSummaryObj, item.DeprecationForOriginal)...)
		}
		if len(item.DeprecationForLatest) > 0 {
			k8sResourcesForDeprecationLatest = append(k8sResourcesForDeprecationLatest,
				getK8sSummaryObjWithUpdatedSummarySchemaErrorsData(singleK8sSummaryObj, item.DeprecationForLatest)...)
		}
	}

	svdForSummarySubType := bean.NewSummaryAndValidationData(bean.Summary, bean.SummaryTitle, "")
	svdForSummarySubType.WithK8sObjectsSummaryReport(&bean.K8sObjectsSummaryReport{
		Headers: bean.SummaryHeaders,
		Data:    k8sResourcesForSummary,
	})
	summaryAndValidationDataList = append(summaryAndValidationDataList, svdForSummarySubType)

	if len(k8sResourcesForValidationLatest) > 0 {
		svdForValidationLatestSubType := bean.NewSummaryAndValidationData(bean.ValidationErrorAgainstLatestApiVersion, bean.ValidationErrorTitle, bean.LatestSubTitle)
		svdForValidationLatestSubType.WithK8sObjectsSummaryReport(&bean.K8sObjectsSummaryReport{
			Headers: bean.ValidationAndDeprecationWithLatestApiVersionHeaders,
			Data:    k8sResourcesForValidationLatest,
		})
		summaryAndValidationDataList = append(summaryAndValidationDataList, svdForValidationLatestSubType)
	}
	if len(k8sResourcesForValidationCurrent) > 0 {
		svdForValidationCurrentSubType := bean.NewSummaryAndValidationData(bean.ValidationErrorAgainstCurrentApiVersion, bean.ValidationErrorTitle, bean.CurrentSubTitle)
		svdForValidationCurrentSubType.WithK8sObjectsSummaryReport(&bean.K8sObjectsSummaryReport{
			Headers: bean.ValidationAndDeprecationWithCurrentApiVersionHeaders,
			Data:    k8sResourcesForValidationCurrent,
		})
		summaryAndValidationDataList = append(summaryAndValidationDataList, svdForValidationCurrentSubType)
	}
	if len(k8sResourcesForDeprecationLatest) > 0 {
		svdForDeprecationLatestSubType := bean.NewSummaryAndValidationData(bean.DeprecationAgainstLatestApiVersion, bean.DeprecatedFieldsTitle, bean.LatestSubTitle)
		svdForDeprecationLatestSubType.WithK8sObjectsSummaryReport(&bean.K8sObjectsSummaryReport{
			Headers: bean.ValidationAndDeprecationWithLatestApiVersionHeaders,
			Data:    k8sResourcesForDeprecationLatest,
		})
		summaryAndValidationDataList = append(summaryAndValidationDataList, svdForDeprecationLatestSubType)
	}

	if len(k8sResourcesForDeprecationCurrent) > 0 {
		svdForDeprecationCurrentSubType := bean.NewSummaryAndValidationData(bean.DeprecationAgainstCurrentApiVersion, bean.DeprecatedFieldsTitle, bean.CurrentSubTitle)
		svdForDeprecationCurrentSubType.WithK8sObjectsSummaryReport(&bean.K8sObjectsSummaryReport{
			Headers: bean.ValidationAndDeprecationWithCurrentApiVersionHeaders,
			Data:    k8sResourcesForDeprecationCurrent,
		})
		summaryAndValidationDataList = append(summaryAndValidationDataList, svdForDeprecationCurrentSubType)
	}

	clusterUpgradeSummaryReport.WithSummaryAndValidationData(summaryAndValidationDataList)

	return clusterUpgradeSummaryReport
}

func getK8sSummaryObjWithUpdatedSummarySchemaErrorsData(singleK8sSummaryObj *bean.SingleK8sObjectSummaryReport, summarySchemaError []*grpc.SummarySchemaError) []*bean.SingleK8sObjectSummaryReport {
	resp := make([]*bean.SingleK8sObjectSummaryReport, 0, len(summarySchemaError))
	if singleK8sSummaryObj == nil {
		return resp
	}
	for _, err := range summarySchemaError {
		cpSingleK8sSummaryObj := *singleK8sSummaryObj
		cpSingleK8sSummaryObj.Path = err.Path
		cpSingleK8sSummaryObj.ErrorReason = err.Reason
		cpSingleK8sSummaryObj.SchemaField = err.SchemaField
		resp = append(resp, &cpSingleK8sSummaryObj)
	}
	return resp
}
