/*
 * Copyright (c) 2024. Devtron Inc.
 */

package resourceQualifiers

import (
	"fmt"
	"github.com/devtron-labs/devtron/pkg/globalPolicy/repository"
	"github.com/devtron-labs/devtron/pkg/sql"
	"strconv"
)

type ResourceType int

func (n ResourceType) ToInt() int {
	return int(n)
}

const (
	Variable              ResourceType = 0
	Filter                ResourceType = 1
	ImageDigest           ResourceType = 2
	ImageDigestResourceId              = -1 // for ImageDigest resource id will is constant unlike filter and variables
	InfraProfile          ResourceType = 3
	DeploymentWindow      ResourceType = 4
	ImagePromotionPolicy  ResourceType = 5
	ChartRefSchema        ResourceType = 6
	FeatureFlag           ResourceType = 7
	Criteria              ResourceType = 9
	HibernationPatch      ResourceType = 10
	ApprovalConfiguration ResourceType = 11
	ApprovalDeployment    ResourceType = 12
)

const (
	AllProjectsValue                     = "-1"
	AllProjectsInt                       = -1
	AllExistingAndFutureProdEnvsValue    = "-2"
	AllExistingAndFutureProdEnvsInt      = -2
	AllExistingAndFutureNonProdEnvsValue = "-1"
	AllExistingAndFutureNonProdEnvsInt   = -1
	AllExistingAndFutureEnvsString       = "-3"
	AllExistingAndFutureEnvsInt          = -3
	AllExistingAndFutureClustersString   = "-4"
	AllExistingAndFutureClustersInt      = -4
	AllExistingAndFutureAppsString       = "-5"
	AllExistingAndFutureAppsInt          = -5
	BaseDeploymentTemplateString         = "-6"
	BaseDeploymentTemplateInt            = -6
	BaseConfigurationString              = "-7"
	BaseConfigurationInt                 = -7
)

func IsVirtualResource(resourceName string) (bool, int, error) {
	i, err := strconv.Atoi(resourceName)
	if err != nil {
		return false, 0, err
	} else {
		return true, i, nil
	}
}

func GetEnvIdentifierValue(scope Scope) int {
	if scope.IsProdEnv {
		return AllExistingAndFutureProdEnvsInt
	}
	return AllExistingAndFutureNonProdEnvsInt
}

type ResourceQualifierMappings struct {
	ResourceId          int
	ResourceType        ResourceType
	SelectionIdentifier *SelectionIdentifier
}

type QualifierMapping struct {
	tableName             struct{}     `sql:"resource_qualifier_mapping" pg:",discard_unknown_columns"`
	Id                    int          `sql:"id,pk"`
	ResourceId            int          `sql:"resource_id"`
	ResourceType          ResourceType `sql:"resource_type"`
	QualifierId           int          `sql:"qualifier_id"`
	IdentifierKey         int          `sql:"identifier_key"`
	IdentifierValueInt    int          `sql:"identifier_value_int"`
	Active                bool         `sql:"active"`
	IdentifierValueString string       `sql:"identifier_value_string"`
	ParentIdentifier      int          `sql:"parent_identifier"`
	CompositeKey          string       `sql:"-"`
	GlobalPolicyId        int          `sql:"global_policy_id"`
	//PolicyScopeConfig     string       `sql:"policy_scope_config"` // to be implemented in future
	sql.AuditLog
	GlobalPolicy *repository.GlobalPolicy
}

type QualifierMappingWithExtraColumns struct {
	QualifierMapping
	TotalCount int
}

type ResourceMappingSelection struct {
	ResourceType        ResourceType
	ResourceId          int
	QualifierSelector   QualifierSelector
	SelectionIdentifier *SelectionIdentifier
	Id                  int
}

type SelectionIdentifier struct {
	AppId                   int                      `json:"appId"`
	EnvId                   int                      `json:"envId"`
	ClusterId               int                      `json:"clusterId"`
	SelectionIdentifierName *SelectionIdentifierName `json:"-"`
}

type SelectionIdentifierName struct {
	AppName         string
	EnvironmentName string
	ClusterName     string
}

func (mapping *QualifierMapping) GetIdValueAndName() (int, string) {
	return mapping.IdentifierValueInt, mapping.IdentifierValueString
}

func (mapping *QualifierMapping) GenerateKeyForDeDuplication() string {
	return fmt.Sprintf("%d-%d-%d-%d-%d-%s", mapping.ResourceId, mapping.ResourceType, mapping.QualifierId, mapping.IdentifierKey, mapping.IdentifierValueInt, mapping.IdentifierValueString)
}

func (mapping *QualifierMapping) GenerateKeyForDeDuplicationScopedVariableByName(newIdentifierKeyForName int) string {
	//not sending identifierValueInt as in new data we don't have id so it will not match with old data
	return fmt.Sprintf("%d-%d-%d-%d-%s", mapping.ResourceId, mapping.ResourceType, mapping.QualifierId, newIdentifierKeyForName, mapping.IdentifierValueString)
}
