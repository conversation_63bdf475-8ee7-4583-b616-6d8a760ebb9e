/*
 * Copyright (c) 2024. Devtron Inc.
 */

package resourceQualifiers

import (
	"encoding/json"
	"errors"
)

func BuildScope(appId, envId, clusterId, projectId int, isProdEnv bool) Scope {
	return Scope{
		AppId:     appId,
		EnvId:     envId,
		ClusterId: clusterId,
		ProjectId: projectId,
		IsProdEnv: isProdEnv}

}

func ConvertGroupJsonDataToModel(jsonData string) (*CriteriaQualifierMappingJson, error) {
	if jsonData == "" {
		return nil, errors.New("invalid json data")
	}

	mappingGroupModel := &CriteriaQualifierMappingJson{}
	err := json.Unmarshal([]byte(jsonData), mappingGroupModel)
	if err != nil {
		return nil, err
	}

	return mappingGroupModel, nil

}
