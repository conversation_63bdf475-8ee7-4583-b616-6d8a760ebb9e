package resourceQualifiers

import (
	"github.com/devtron-labs/devtron/pkg/devtronResource/bean"
	"testing"
)

func TestGetParentAndChildrenMapping_CompoundQualifier_ApplicationEnvironmentSelector(t *testing.T) {
	// Arrange
	resourceMappingSelections := []*ResourceMappingSelection{
		{
			ResourceType:      0,
			ResourceId:        3,
			QualifierSelector: 3, // ApplicationEnvironmentSelector
			SelectionIdentifier: &SelectionIdentifier{
				AppId:     1,
				EnvId:     2,
				ClusterId: 0,
				SelectionIdentifierName: &SelectionIdentifierName{
					AppName:         "app1",
					EnvironmentName: "testenv",
					ClusterName:     "",
				},
			},
			Id: 0,
		},
	}

	resourceKeyMap := map[bean.DtResSearchableKeyName]int{}
	userId := int32(1)

	// Act
	parentMappings, childrenMappings, mappingsToSelection, parentMappingsMap := getParentAndChildrenMappingForCreation(resourceMappingSelections, resourceKeyMap, userId)

	// Assert
	if len(parentMappings) != 1 {
		t.Errorf("Expected 1 parent mapping, got %d", len(parentMappings))
	}

	if len(childrenMappings) != 1 {
		t.Errorf("Expected 1 child mapping, got %d", len(childrenMappings))
	}

	if len(mappingsToSelection) != 1 {
		t.Errorf("Expected 1 mapping to selection, got %d", len(mappingsToSelection))
	}

	if len(parentMappingsMap) != 1 {
		t.Errorf("Expected 1 parent mapping in the map, got %d", len(parentMappingsMap))
	}
}

func TestGetParentAndChildrenMapping_NonCompoundQualifier_ApplicationSelector(t *testing.T) {
	// Arrange
	resourceMappingSelections := []*ResourceMappingSelection{
		{
			ResourceType:      0,
			ResourceId:        3,
			QualifierSelector: 0, // ApplicationSelector
			SelectionIdentifier: &SelectionIdentifier{
				AppId:     1,
				EnvId:     0,
				ClusterId: 0,
				SelectionIdentifierName: &SelectionIdentifierName{
					AppName:         "app1",
					EnvironmentName: "",
					ClusterName:     "",
				},
			},
			Id: 0,
		},
	}

	resourceKeyMap := map[bean.DtResSearchableKeyName]int{}
	userId := int32(1)

	// Act
	parentMappings, childrenMappings, mappingsToSelection, parentMappingsMap := getParentAndChildrenMappingForCreation(resourceMappingSelections, resourceKeyMap, userId)

	// Assert
	if len(parentMappings) != 1 {
		t.Errorf("Expected 1 parent mapping, got %d", len(parentMappings))
	}

	if len(childrenMappings) != 0 {
		t.Errorf("Expected 0 children mappings, got %d", len(childrenMappings))
	}

	if len(mappingsToSelection) != 1 {
		t.Errorf("Expected 1 mapping to selection, got %d", len(mappingsToSelection))
	}

	if len(parentMappingsMap) != 0 {
		t.Errorf("Expected 1 parent mapping in the map, got %d", len(parentMappingsMap))
	}
}

func TestGetParentAndChildrenMapping_MultipleSelections(t *testing.T) {
	// Arrange
	resourceMappingSelections := []*ResourceMappingSelection{
		{
			ResourceType:      0,
			ResourceId:        3,
			QualifierSelector: 0, // ApplicationSelector (non-compound)
			SelectionIdentifier: &SelectionIdentifier{
				AppId:     1,
				EnvId:     0,
				ClusterId: 0,
				SelectionIdentifierName: &SelectionIdentifierName{
					AppName:         "app1",
					EnvironmentName: "",
					ClusterName:     "",
				},
			},
			Id: 0,
		},
		{
			ResourceType:      0,
			ResourceId:        3,
			QualifierSelector: 3, // ApplicationEnvironmentSelector (compound)
			SelectionIdentifier: &SelectionIdentifier{
				AppId:     1,
				EnvId:     2,
				ClusterId: 0,
				SelectionIdentifierName: &SelectionIdentifierName{
					AppName:         "app2",
					EnvironmentName: "prod",
					ClusterName:     "",
				},
			},
			Id: 1,
		},
	}

	resourceKeyMap := map[bean.DtResSearchableKeyName]int{}
	userId := int32(1)

	// Act
	parentMappings, childrenMappings, mappingsToSelection, parentMappingsMap := getParentAndChildrenMappingForCreation(resourceMappingSelections, resourceKeyMap, userId)

	// Assert
	if len(parentMappings) != 2 {
		t.Errorf("Expected 2 parent mappings, got %d", len(parentMappings))
	}

	if len(childrenMappings) != 1 {
		t.Errorf("Expected 1 child mapping, got %d", len(childrenMappings))
	}

	if len(mappingsToSelection) != 2 {
		t.Errorf("Expected 2 mappings to selection, got %d", len(mappingsToSelection))
	}

	if len(parentMappingsMap) != 1 {
		t.Errorf("Expected 2 parent mappings in the map, got %d", len(parentMappingsMap))
	}
}
