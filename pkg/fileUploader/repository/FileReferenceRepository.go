package repository

import (
	"github.com/devtron-labs/devtron/pkg/sql"
	"github.com/go-pg/pg"
)

type FileReferenceRepository interface {
	// SaveFileReference saves the file reference.
	// If tx is not nil, it saves the file reference in the transaction.
	// If tx is nil, it saves the file reference in the database.
	SaveFileReference(tx *pg.Tx, fileReference *FileReference) (*FileReference, error)
	// GetFileMetadataByIds fetches the file reference by ids.
	// It fetches only the id, name, size, mime_type, extension, and file_type columns.
	GetFileMetadataByIds(ids []int) ([]*FileReference, error)
	// GetFileReferenceByIds fetches the file reference by ids.
	// It fetches all the columns of the file reference.
	GetFileReferenceByIds(ids []int) ([]*FileReference, error)
}

type FileReferenceRepositoryImpl struct {
	dbConnection *pg.DB
}

func NewFileReferenceRepositoryImpl(dbConnection *pg.DB) *FileReferenceRepositoryImpl {
	return &FileReferenceRepositoryImpl{
		dbConnection: dbConnection,
	}
}

type FileReference struct {
	tableName struct{} `sql:"file_reference" pg:",discard_unknown_columns"`
	Id        int      `sql:"id,pk"`
	Data      []byte   `sql:"data"`
	Name      string   `sql:"name,notnull"`
	Size      int64    `sql:"size,notnull"`
	MimeType  string   `sql:"mime_type,notnull"`
	Extension string   `sql:"extension,notnull"`
	FileType  FileType `sql:"file_type,notnull"`
	sql.AuditLog
}

func (impl *FileReferenceRepositoryImpl) SaveFileReference(tx *pg.Tx, fileReference *FileReference) (*FileReference, error) {
	var err error
	if tx != nil {
		_, err = tx.Model(fileReference).Insert()
		return fileReference, err
	}
	_, err = impl.dbConnection.Model(fileReference).Insert()
	return fileReference, err
}

func (impl *FileReferenceRepositoryImpl) GetFileMetadataByIds(ids []int) ([]*FileReference, error) {
	var fileReferences []*FileReference
	err := impl.dbConnection.Model(&fileReferences).
		Column("id", "name", "size", "mime_type", "extension", "file_type").
		Where("id in (?)", pg.In(ids)).
		Select()
	return fileReferences, err
}

func (impl *FileReferenceRepositoryImpl) GetFileReferenceByIds(ids []int) ([]*FileReference, error) {
	var fileReferences []*FileReference
	err := impl.dbConnection.Model(&fileReferences).
		Column("file_reference.*").
		Where("id in (?)", pg.In(ids)).
		Select()
	return fileReferences, err
}
