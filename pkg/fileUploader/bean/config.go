/*
 * Copyright (c) 2024. Devtron Inc.
 */

package bean

import (
	"github.com/caarlos0/env"
)

// CATEGORY=FILE_UPLOAD
type fileUploadEnvConfig struct {
	// MaxUploadSize: Maximum upload size in bytes
	MaxUploadSize int64 `env:"-"` // Unit: bytes
	// MaxUploadSizeInKB is the maximum upload size in KB
	//	- Default: 1MB
	// Note: Use MaxUploadSize for the byte converted value. MaxUploadSizeInKB is for user convenience.
	// The max file upload size allowed for any pipeline stage variables is 1MB as it is being mounted as ConfigMap
	MaxUploadSizeInKB int64 `env:"MAX_UPLOAD_SIZE_IN_KB" envDefault:"1024" description:"Maximum upload size in KB" example:"1024" deprecated:"false"` // Unit: KB, default: 1MB
}

// FileUploadInternalConfig is the internal configuration for file upload
// Deduced from fileUploadEnvConfig
type FileUploadInternalConfig struct {
	MaxUploadSize UploadSize
}

const (
	Bytes = 1024
)

type UploadSize int64

func NewUploadSize(size int64) UploadSize {
	return UploadSize(size)
}

func (u UploadSize) Int64() int64 {
	return int64(u)
}

func (u UploadSize) KB() UploadSize {
	return u / Bytes
}

// GetFileUploadConfig returns the file upload configuration i.e., fileUploadEnvConfig
func GetFileUploadConfig() (*FileUploadInternalConfig, error) {
	envConfig := &fileUploadEnvConfig{}
	internalConfig := &FileUploadInternalConfig{}
	err := env.Parse(envConfig)
	if err != nil {
		return internalConfig, err
	}
	// Convert KB to bytes
	internalConfig.MaxUploadSize = NewUploadSize(envConfig.MaxUploadSizeInKB * Bytes)
	return internalConfig, err
}
