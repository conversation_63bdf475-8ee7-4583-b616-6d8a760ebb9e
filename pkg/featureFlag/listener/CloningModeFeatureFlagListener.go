package listener

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/devtron-labs/devtron/client/gitSensor"
	"github.com/devtron-labs/devtron/internal/sql/repository/app"
	repository3 "github.com/devtron-labs/devtron/pkg/autoRemediation/repository"
	repository4 "github.com/devtron-labs/devtron/pkg/build/git/gitMaterial/repository"
	repository5 "github.com/devtron-labs/devtron/pkg/cluster/environment/repository"
	"github.com/devtron-labs/devtron/pkg/cluster/repository"
	"github.com/devtron-labs/devtron/pkg/devtronResource/read"
	"github.com/devtron-labs/devtron/pkg/featureFlag/helper"
	"github.com/devtron-labs/devtron/pkg/featureFlag/model"
	"github.com/devtron-labs/devtron/pkg/resourceQualifiers"
	"github.com/devtron-labs/devtron/pkg/variables"
	repository2 "github.com/devtron-labs/devtron/pkg/variables/repository"
	"go.uber.org/zap"
)

type CloningModeFeatureFlagListener interface {
	ListenerInterface
}

type CloningModeFeatureFlagListenerImpl struct {
	logger                              *zap.SugaredLogger
	scopedVariableRepository            repository2.ScopedVariableRepository
	devtronResourceSearchableKeyService read.DevtronResourceSearchableKeyService
	watcherRepository                   repository3.K8sEventWatcherRepository

	// Enterprise only
	appRepository         app.AppRepository
	environmentRepository repository5.EnvironmentRepository
	clusterRepository     repository.ClusterRepository
	scopedVariableService variables.ScopedVariableService
	featureFlagHelper     helper.FeatureFlagHelper
	gitSensorClient       gitSensor.Client
}

func NewCloningModeFeatureFlagListenerImpl(logger *zap.SugaredLogger, scopedVariableRepository repository2.ScopedVariableRepository, appRepository app.AppRepository, environmentRepository repository5.EnvironmentRepository, devtronResourceSearchableKeyService read.DevtronResourceSearchableKeyService, clusterRepository repository.ClusterRepository,
	watcherRepository repository3.K8sEventWatcherRepository, featureFlagUtil helper.FeatureFlagHelper,
	gitSensorClient gitSensor.Client, scopedVariableService variables.ScopedVariableService) *CloningModeFeatureFlagListenerImpl {
	return &CloningModeFeatureFlagListenerImpl{
		logger:                   logger,
		scopedVariableRepository: scopedVariableRepository,
		scopedVariableService:    scopedVariableService,
		watcherRepository:        watcherRepository,

		// Enterprise only
		appRepository:                       appRepository,
		environmentRepository:               environmentRepository,
		devtronResourceSearchableKeyService: devtronResourceSearchableKeyService,
		clusterRepository:                   clusterRepository,
		featureFlagHelper:                   featureFlagUtil,
		gitSensorClient:                     gitSensorClient,
	}
}
func (impl *CloningModeFeatureFlagListenerImpl) OnFeatureFlagChanged(payload *model.ListenerPayload) error {
	impl.logger.Debugw("handling default feature flag changed", "payload", payload)
	if payload == nil || payload.Name != model.CloningMode {
		impl.logger.Debugw("feature isn't cloning mode hence skipping", "payload", payload)
		return nil
	}

	data := model.CloningModeListenerData{}
	err := json.Unmarshal(payload.Data, &data)
	if err != nil {
		impl.logger.Errorw("some error occured in decoding payload", "err", err, "payload", payload)
		return err
	}

	if data.OldMap == nil {
		impl.logger.Debugw("no oldMap found in Listener payload, skipping further processing", "payload", data)
		// this is first time data is being created, so no oldMap found in Listener payload, skipping further processing
		return nil
	}
	data.UserRequest.Name = payload.Name
	// again get All active resource qualifier for particular feature flag
	qualifierMappings, qualifierMappingsErr := impl.scopedVariableService.GetQualifierMappingsByResourceIdForTx(model.FeatureFlagToIdMap[data.UserRequest.Name], resourceQualifiers.FeatureFlag, payload.Tx)
	if qualifierMappingsErr != nil {
		impl.logger.Errorw("error in getting qualifierMappings", zap.Error(qualifierMappingsErr))
		return qualifierMappingsErr
	}
	if len(qualifierMappings) == 0 {
		impl.logger.Debugw("no active qualifier mappings found after creating new data for featureFlag=", data.UserRequest.Name)
		return errors.New("no active qualifier mappings found after creating new data")
	}

	// for each scope need to evaluate map[appId]featureFlagValue - newMap
	newDataMap, newMapErr := impl.featureFlagHelper.GetMapAppIdToFeatureFlagData(qualifierMappings, data.UserRequest, data.ActiveGitMaterialData, data.Scopes)
	if newMapErr != nil {
		impl.logger.Errorw("error in getting old map", zap.Error(newMapErr))
		return newMapErr
	}

	// Compare oldMap and newMap and prepare AppIds which needs to be reloaded
	diffAppIds := make([]int, 0)
	for appId, _ := range newDataMap {
		if data.OldMap[appId] != newDataMap[appId] {
			diffAppIds = append(diffAppIds, appId)
		}
	}
	impl.logger.Debugw("diffAppIds=", diffAppIds)

	// call git-sensor API to reload specific git matpapperial Ids using AppIds
	if len(diffAppIds) > 0 {
		reloadMaterials := gitSensor.ReloadMaterialsDto{}
		for _, appId := range diffAppIds {
			gitMaterialIdList := impl.featureFlagHelper.GetGitMaterialListForAppId(data.ActiveGitMaterialData, appId)
			for _, gitMaterialId := range gitMaterialIdList {
				reloadMaterials.ReloadMaterial = append(reloadMaterials.ReloadMaterial, gitSensor.ReloadMaterialDto{GitmaterialId: int64(gitMaterialId), AppId: appId, CloningMode: newDataMap[appId]})

			}
		}
		_, err = impl.gitSensorClient.ReloadAndSyncGitMaterials(context.Background(), &reloadMaterials)
		if err != nil {
			impl.logger.Errorw("error in reloading materials", err)
			return err
		}
	} else {
		impl.logger.Infow("no diffAppIds detected for featureFlag=", data.UserRequest.Name)
	}
	return nil
}

func (impl *CloningModeFeatureFlagListenerImpl) getMapAppIdToFeatureFlagData(qualifierMappings []*resourceQualifiers.QualifierMapping,
	request model.FeatureFlagModel, activeGitMaterialData []*repository4.GitMaterial,
	scopes []resourceQualifiers.Scope) (map[int]string, error) {
	// for each scope need to evaluate map[appId]featureFlagValue - oldMap
	searchableIdMap := impl.devtronResourceSearchableKeyService.GetAllSearchableKeyNameIdMap()
	scopeIds := make([]int, 0)
	mapAppIdToScopeId := make(map[int]int, 0)
	for _, scope := range scopes {
		qualifiedMappings := impl.featureFlagHelper.GetMatchedQualifierMappings(scope, qualifierMappings, searchableIdMap)
		matchedScopes := impl.scopedVariableService.GetMatchedScopedVariables(qualifiedMappings)
		variableIdToSelectedScopeId := impl.scopedVariableService.GetScopeWithPriority(matchedScopes)
		scopeId := variableIdToSelectedScopeId[model.FeatureFlagToIdMap[request.Name]]
		mapAppIdToScopeId[scope.AppId] = scopeId
		scopeIds = append(scopeIds, scopeId)

	}

	//get data for scopeIds
	dataForAllScopeIds, dataForAllScopeIdsErr := impl.scopedVariableRepository.GetDataForScopeIds(scopeIds)
	fmt.Println("getFeatureFlagValue -------- ", dataForAllScopeIds)
	if dataForAllScopeIdsErr != nil {
		impl.logger.Errorw("error in getting data for scopeIds", zap.Error(dataForAllScopeIdsErr))
		return nil, dataForAllScopeIdsErr
	}

	//prepare a map of Map<ScopeId> = VariableData
	mapScopeIdToVariableDataId := make(map[int]*repository2.VariableData, 0)
	for _, data := range dataForAllScopeIds {
		mapScopeIdToVariableDataId[data.VariableScopeId] = data
	}

	//prepare Map<AppId> = <Value of feature flag>
	mapAppIdToVariableDataOld := make(map[int]string, 0)
	for _, gitMaterialData := range activeGitMaterialData {
		appId := gitMaterialData.App.Id
		mapAppIdToVariableDataOld[appId] = mapScopeIdToVariableDataId[mapAppIdToScopeId[appId]].Data
	}

	return mapAppIdToVariableDataOld, nil
}
