package featureFlag

import "github.com/devtron-labs/devtron/pkg/featureFlag/model"

type FeatureFlagValueRequest struct {
	model.ResourceScope
	FeatureFlag model.FeatureFlagEnum `json:"featureFlag"`
}

type FeatureFlagValueResponse struct {
	model.ResourceScope
	FeatureFlag      model.FeatureFlagEnum `json:"featureFlag"`
	FeatureFlagValue string                `json:"featureFlagValue"`
}

type FeatureFlagValueForMultipleScopesRequest struct {
	Scopes []FeatureFlagValueRequest `json:"scopes"`
}

type FeatureFlagValueForMultipleScopesResponse struct {
	ScopesData []*FeatureFlagValueResponse `json:"scopesData"`
}

type FeatureFlagHandlerRequest struct {
	model.FeatureFlagModel
}

// NewFeatureFlagHandlerRequest is the Constructor for FeatureFlagHandlerRequest struct
func NewFeatureFlagHandlerRequest(featureFlagVal model.FeatureFlagEnum) FeatureFlagValueRequest {
	return FeatureFlagValueRequest{
		FeatureFlag: featureFlagVal,
	}
}

func (f FeatureFlagValueRequest) WithEnvId(envId int) FeatureFlagValueRequest {
	f.EnvId = envId
	return f
}

func (f FeatureFlagValueRequest) WithAppId(appId int) FeatureFlagValueRequest {
	f.AppId = appId
	return f
}
