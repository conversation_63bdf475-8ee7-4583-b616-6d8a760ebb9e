/*
 * Copyright (c) 2020-2024. Devtron Inc.
 */

package apiToken

import (
	"errors"
	"fmt"
	"github.com/caarlos0/env"
	"github.com/devtron-labs/devtron/pkg/apiToken/adapter"
	bean2 "github.com/devtron-labs/devtron/pkg/apiToken/bean"
	"github.com/devtron-labs/devtron/pkg/apiToken/repository"
	userBean "github.com/devtron-labs/devtron/pkg/auth/user/bean"
	"strconv"
	"strings"
	"time"

	"github.com/devtron-labs/authenticator/middleware"
	openapi "github.com/devtron-labs/devtron/api/openapi/openapiClient"
	"github.com/devtron-labs/devtron/pkg/auth/user"
	"github.com/devtron-labs/devtron/pkg/sql"
	"github.com/go-pg/pg"
	"github.com/golang-jwt/jwt/v4"
	"go.uber.org/zap"
)

type ApiTokenService interface {
	GetAllActiveApiTokens() ([]*openapi.ApiToken, error)
	CreateApiToken(request *openapi.CreateApiTokenRequest, createdBy int32, managerAuth func(resource, token, object string) bool) (*openapi.CreateApiTokenResponse, error)
	UpdateApiToken(apiTokenId int, request *openapi.UpdateApiTokenRequest, updatedBy int32) (*openapi.UpdateApiTokenResponse, error)
	DeleteApiToken(apiTokenId int, deletedBy int32) (*openapi.ActionResponse, error)
	GetAllApiTokensForWebhook(projectName string, environmentName string, appName string, auth func(token string, projectObject string, envObject string) bool) ([]*openapi.ApiToken, error)
	CreateApiJwtTokenForNotification(claims *bean2.TokenCustomClaimsForNotification, expireAtInMs int64) (string, error)
	CreateApiJwtToken(email string, tokenVersion int, expireAtInMs int64) (string, error)
	GetApiTokenById(id int) (*bean2.ApiTokenBean, error)
}

type ApiTokenServiceImpl struct {
	logger                *zap.SugaredLogger
	apiTokenSecretService ApiTokenSecretService
	userService           user.UserService
	userAuditService      user.UserAuditService
	apiTokenRepository    repository.ApiTokenRepository
	TokenVariableConfig   *TokenVariableConfig
}

func NewApiTokenServiceImpl(logger *zap.SugaredLogger, apiTokenSecretService ApiTokenSecretService, userService user.UserService, userAuditService user.UserAuditService,
	apiTokenRepository repository.ApiTokenRepository,
) (*ApiTokenServiceImpl, error) {
	apiTokenService := &ApiTokenServiceImpl{
		logger:                logger,
		apiTokenSecretService: apiTokenSecretService,
		userService:           userService,
		userAuditService:      userAuditService,
		apiTokenRepository:    apiTokenRepository,
	}

	cfg, err := GetTokenConfig()
	if err != nil {
		return nil, err
	}
	apiTokenService.TokenVariableConfig = cfg

	return apiTokenService, err
}
func GetTokenConfig() (*TokenVariableConfig, error) {
	cfg := &TokenVariableConfig{}
	err := env.Parse(cfg)
	return cfg, err
}

type TokenVariableConfig struct {
	ExpireAtInHours int64 `env:"NOTIFICATION_TOKEN_EXPIRY_TIME_HOURS" envDefault:"720"` // 30*24
}

func (config TokenVariableConfig) GetExpiryTimeInMs() int64 {
	return time.Now().Add(time.Duration(config.ExpireAtInHours) * time.Hour).UnixMilli()
}

func (impl ApiTokenServiceImpl) GetAllApiTokensForWebhook(projectName string, environmentName string, appName string, auth func(token string, projectObject string, envObject string) bool) ([]*openapi.ApiToken, error) {
	impl.logger.Info("Getting active api tokens")
	apiTokensFromDb, err := impl.apiTokenRepository.FindAllActive()
	if err != nil {
		impl.logger.Errorw("error while getting all active api tokens from DB", "error", err)
		return nil, err
	}

	apiTokens := make([]*openapi.ApiToken, 0)
	for _, apiTokenFromDb := range apiTokensFromDb {
		authPassed := true
		userId := apiTokenFromDb.User.Id
		// checking permission on each of the roles associated with this API Token
		environmentNames := strings.Split(environmentName, ",")
		for _, environment := range environmentNames {
			projectObject := fmt.Sprintf("%s/%s", projectName, appName)
			envObject := fmt.Sprintf("%s/%s", environment, appName)
			isValidAuth := auth(apiTokenFromDb.Token, projectObject, envObject)
			if !isValidAuth {
				impl.logger.Debugw("authentication for token failed", "apiTokenFromDb", apiTokenFromDb)
				authPassed = false
				continue
			}
		}

		if authPassed {
			apiTokenIdI32 := int32(apiTokenFromDb.Id)
			updatedAtStr := apiTokenFromDb.UpdatedOn.String()
			apiToken := &openapi.ApiToken{
				Id:             &apiTokenIdI32,
				UserId:         &userId,
				UserIdentifier: &apiTokenFromDb.User.EmailId,
				Name:           &apiTokenFromDb.Name,
				Description:    &apiTokenFromDb.Description,
				ExpireAtInMs:   &apiTokenFromDb.ExpireAtInMs,
				Token:          &apiTokenFromDb.Token,
				UpdatedAt:      &updatedAtStr,
			}
			apiTokens = append(apiTokens, apiToken)
		}
	}

	return apiTokens, nil
}

func (impl ApiTokenServiceImpl) GetAllActiveApiTokens() ([]*openapi.ApiToken, error) {
	impl.logger.Info("Getting all active api tokens")
	apiTokensFromDb, err := impl.apiTokenRepository.FindAllActive()
	if err != nil {
		impl.logger.Errorw("error while getting all active api tokens from DB", "error", err)
		return nil, err
	}

	var apiTokens []*openapi.ApiToken
	for _, apiTokenFromDb := range apiTokensFromDb {
		userId := apiTokenFromDb.User.Id
		latestAuditLog, err := impl.userAuditService.GetLatestByUserId(userId)
		if err != nil {
			impl.logger.Errorw("error while getting latest audit log", "error", err)
			return nil, err
		}

		apiTokenIdI32 := int32(apiTokenFromDb.Id)
		updatedAtStr := apiTokenFromDb.UpdatedOn.String()
		apiToken := &openapi.ApiToken{
			Id:             &apiTokenIdI32,
			UserId:         &userId,
			UserIdentifier: &apiTokenFromDb.User.EmailId,
			Name:           &apiTokenFromDb.Name,
			Description:    &apiTokenFromDb.Description,
			ExpireAtInMs:   &apiTokenFromDb.ExpireAtInMs,
			Token:          &apiTokenFromDb.Token,
			UpdatedAt:      &updatedAtStr,
		}
		if latestAuditLog != nil {
			lastUsedAtStr := latestAuditLog.CreatedOn.String()
			apiToken.LastUsedAt = &lastUsedAtStr
			apiToken.LastUsedByIp = &latestAuditLog.ClientIp
		}
		apiTokens = append(apiTokens, apiToken)
	}

	return apiTokens, nil
}

func (impl ApiTokenServiceImpl) CreateApiToken(request *openapi.CreateApiTokenRequest, createdBy int32, managerAuth func(resource, token string, object string) bool) (*openapi.CreateApiTokenResponse, error) {
	impl.logger.Infow("Creating API token", "request", request, "createdBy", createdBy)

	name := request.GetName()
	// check if name contains some characters which are not allowed
	if bean2.InvalidCharsInApiTokenName.MatchString(name) {
		return nil, errors.New(fmt.Sprintf("name '%s' contains either white-space or comma, which is not allowed", name))
	}

	// step-1 - check if the name exists, if exists with active user - throw error
	apiToken, err := impl.apiTokenRepository.FindByName(name)
	if err != nil && err != pg.ErrNoRows {
		impl.logger.Errorw("error while getting api token by name", "name", name, "error", err)
		return nil, err
	}
	var apiTokenExists bool
	if apiToken != nil && apiToken.Id > 0 {
		apiTokenExists = true
		if apiToken.User.Active {
			return nil, errors.New(fmt.Sprintf("name '%s' is already used. please use another name", name))
		}
	}

	impl.logger.Info(fmt.Sprintf("apiTokenExists : %s", strconv.FormatBool(apiTokenExists)))

	// step-2 - Build email and version
	email := fmt.Sprintf("%s%s", userBean.API_TOKEN_USER_EMAIL_PREFIX, name)
	var (
		tokenVersion         int
		previousTokenVersion int
	)
	if apiTokenExists {
		tokenVersion = apiToken.Version + 1
		previousTokenVersion = apiToken.Version
	} else {
		tokenVersion = 1
	}

	// step-3 - Build token
	token, err := impl.CreateApiJwtToken(email, tokenVersion, *request.ExpireAtInMs)
	if err != nil {
		return nil, err
	}

	// step-4 - Create user using email
	createUserRequest := userBean.UserInfo{
		UserId:   createdBy,
		EmailId:  email,
		UserType: userBean.USER_TYPE_API_TOKEN,
	}
	createUserResponse, err := impl.userService.CreateUserV2(&createUserRequest, token, managerAuth)
	if err != nil {
		impl.logger.Errorw("error while creating user for api-token", "email", email, "error", err)
		return nil, err
	}
	createUserResponseLength := len(createUserResponse)
	if createUserResponseLength != 1 {
		return nil, errors.New(fmt.Sprintf("some error while creating user. length of createUserResponse expected 1. found %d", createUserResponseLength))
	}
	userId := createUserResponse[0].Id

	// step-5 - Save API token (update or save)
	apiTokenSaveRequest := &repository.ApiToken{
		UserId:       userId,
		Name:         name,
		Description:  *request.Description,
		ExpireAtInMs: *request.ExpireAtInMs,
		Token:        token,
		Version:      tokenVersion,
		AuditLog:     sql.AuditLog{UpdatedOn: time.Now()},
	}
	if apiTokenExists {
		apiTokenSaveRequest.Id = apiToken.Id
		apiTokenSaveRequest.CreatedBy = apiToken.CreatedBy
		apiTokenSaveRequest.CreatedOn = apiToken.CreatedOn
		apiTokenSaveRequest.UpdatedBy = createdBy
		// update api-token only if `previousTokenVersion` is same as version stored in DB
		// we are checking this to ensure that two users are not updating the same token at the same time
		err = impl.apiTokenRepository.UpdateIf(apiTokenSaveRequest, previousTokenVersion)
	} else {
		apiTokenSaveRequest.CreatedBy = createdBy
		apiTokenSaveRequest.CreatedOn = time.Now()
		err = impl.apiTokenRepository.Save(apiTokenSaveRequest)
	}
	if err != nil {
		impl.logger.Errorw("error while saving api-token into DB", "error", err)
		// fetching error code from pg error for Unique key violation constraint
		// in case of save
		pgErr, ok := err.(pg.Error)
		if ok {
			errCode, conversionErr := strconv.Atoi(pgErr.Field('C'))
			if conversionErr == nil && errCode == bean2.UniqueKeyViolationPgErrorCode {
				return nil, fmt.Errorf(bean2.ConcurrentTokenUpdateRequest)
			}
		}
		// in case of update
		if errors.Is(err, fmt.Errorf(bean2.TokenVersionMismatch)) {
			return nil, fmt.Errorf(bean2.ConcurrentTokenUpdateRequest)
		}
		return nil, err
	}

	success := true
	return &openapi.CreateApiTokenResponse{
		Success:        &success,
		Token:          &token,
		UserId:         &userId,
		UserIdentifier: &email,
		Id:             &apiTokenSaveRequest.Id,
	}, nil
}

func (impl ApiTokenServiceImpl) UpdateApiToken(apiTokenId int, request *openapi.UpdateApiTokenRequest, updatedBy int32) (*openapi.UpdateApiTokenResponse, error) {
	impl.logger.Infow("Updating API token", "request", request, "updatedBy", updatedBy, "apiTokenId", apiTokenId)

	// step-1 - check if the api-token exists, if not exists - throw error
	apiToken, err := impl.apiTokenRepository.FindActiveById(apiTokenId)
	if err != nil && err != pg.ErrNoRows {
		impl.logger.Errorw("error while getting api token by id", "apiTokenId", apiTokenId, "error", err)
		return nil, err
	}
	if apiToken == nil || apiToken.Id == 0 {
		return nil, errors.New(fmt.Sprintf("api-token corresponds to apiTokenId '%d' is not found", apiTokenId))
	}

	previousTokenVersion := apiToken.Version
	tokenVersion := apiToken.Version + 1

	// step-2 - If expires_at is not same, then token needs to be generated again
	if *request.ExpireAtInMs != apiToken.ExpireAtInMs {
		// regenerate token
		token, err := impl.CreateApiJwtToken(apiToken.User.EmailId, tokenVersion, *request.ExpireAtInMs)
		if err != nil {
			return nil, err
		}
		apiToken.Token = token
		apiToken.Version = tokenVersion
	}

	// step-3 - update in DB
	apiToken.Description = *request.Description
	apiToken.ExpireAtInMs = *request.ExpireAtInMs
	apiToken.UpdatedBy = updatedBy
	apiToken.UpdatedOn = time.Now()
	// update api-token only if `previousTokenVersion` is same as version stored in DB
	// we are checking this to ensure that two users are not updating the same token at the same time
	err = impl.apiTokenRepository.UpdateIf(apiToken, previousTokenVersion)
	if err != nil {
		impl.logger.Errorw("error while updating api-token", "apiTokenId", apiTokenId, "error", err)
		return nil, err
	}

	success := true
	return &openapi.UpdateApiTokenResponse{
		Success: &success,
		Token:   &apiToken.Token,
	}, nil
}

func (impl ApiTokenServiceImpl) DeleteApiToken(apiTokenId int, deletedBy int32) (*openapi.ActionResponse, error) {
	impl.logger.Infow("Deleting API token", "deletedBy", deletedBy, "apiTokenId", apiTokenId)

	// step-1 - check if the api-token exists, if not exists - throw error
	apiToken, err := impl.apiTokenRepository.FindActiveById(apiTokenId)
	if err != nil && err != pg.ErrNoRows {
		impl.logger.Errorw("error while getting api token by id", "apiTokenId", apiTokenId, "error", err)
		return nil, err
	}
	if apiToken == nil || apiToken.Id == 0 {
		return nil, errors.New(fmt.Sprintf("api-token corresponds to apiTokenId '%d' is not found", apiTokenId))
	}

	apiToken.ExpireAtInMs = time.Now().UnixMilli()
	err = impl.apiTokenRepository.Update(apiToken)
	if err != nil && err != pg.ErrNoRows {
		impl.logger.Errorw("error while getting api token by id", "apiTokenId", apiTokenId, "error", err)
		return nil, err
	}

	// step-2 inactivate user corresponds to this api-token
	deleteUserRequest := userBean.UserInfo{
		Id:     apiToken.UserId,
		UserId: deletedBy,
	}
	success, err := impl.userService.DeleteUser(&deleteUserRequest)
	if err != nil {
		impl.logger.Errorw("error while inactivating user for", "apiTokenId", apiTokenId, "userId", apiToken.UserId, "error", err)
		return nil, err
	}
	if !success {
		return nil, errors.New(fmt.Sprintf("Couldn't in-activate user corresponds to apiTokenId '%d'", apiTokenId))
	}

	return &openapi.ActionResponse{
		Success: &success,
	}, nil

}

func (impl ApiTokenServiceImpl) GetApiTokenById(id int) (*bean2.ApiTokenBean, error) {
	apiToken, err := impl.apiTokenRepository.FindActiveById(id)
	if err != nil {
		impl.logger.Errorw("error encountered in GetApiTokenById", "id", id, "err", err)
		return nil, err
	}
	return adapter.BuildApiTokenBean(apiToken), nil

}
func (impl ApiTokenServiceImpl) CreateApiJwtTokenForNotification(claims *bean2.TokenCustomClaimsForNotification, expireAtInMs int64) (string, error) {
	registeredClaims, secretByteArr, err := impl.setRegisteredClaims(expireAtInMs)
	if err != nil {
		impl.logger.Errorw("error in setting registered claims", "err", err)
		return "", err
	}
	claims.SetRegisteredClaims(registeredClaims)
	token, err := claims.GenerateToken(secretByteArr)
	if err != nil {
		impl.logger.Errorw("error in generating token", "err", err)
		return token, err
	}

	return token, nil

}
func (impl ApiTokenServiceImpl) CreateApiJwtToken(email string, tokenVersion int, expireAtInMs int64) (string, error) {
	registeredClaims, secretByteArr, err := impl.setRegisteredClaims(expireAtInMs)
	if err != nil {
		return "", err
	}
	claims := &bean2.ApiTokenCustomClaims{
		email,
		strconv.Itoa(tokenVersion),
		registeredClaims,
	}
	token, err := impl.generateToken(claims, secretByteArr)
	if err != nil {
		return "", err
	}
	return token, nil
}

func (impl ApiTokenServiceImpl) CreateApiJwtTokenForArtifactPromotion(claims *bean2.ArtifactPromotionApprovalNotificationClaims, expireAtInMs int64) (string, error) {
	registeredClaims, secretByteArr, err := impl.setRegisteredClaims(expireAtInMs)
	if err != nil {
		impl.logger.Errorw("error in setting registered claims", "err", err)
		return "", err
	}
	claims.RegisteredClaims = registeredClaims
	unsignedToken := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	token, err := unsignedToken.SignedString(secretByteArr)
	if err != nil {
		return "", err
	}
	return token, nil
}

func (impl ApiTokenServiceImpl) setRegisteredClaims(expireAtInMs int64) (jwt.RegisteredClaims, []byte, error) {
	secretByteArr, err := impl.apiTokenSecretService.GetApiTokenSecretByteArr()
	if err != nil {
		impl.logger.Errorw("error while getting api token secret", "error", err)
		return jwt.RegisteredClaims{}, secretByteArr, err
	}

	registeredClaims := jwt.RegisteredClaims{
		Issuer: middleware.ApiTokenClaimIssuer,
	}

	if expireAtInMs > 0 {
		registeredClaims.ExpiresAt = jwt.NewNumericDate(time.Unix(expireAtInMs/1000, 0))
	}
	return registeredClaims, secretByteArr, nil
}

func (impl ApiTokenServiceImpl) generateToken(claims *bean2.ApiTokenCustomClaims, secretByteArr []byte) (string, error) {
	unsignedToken := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	token, err := unsignedToken.SignedString(secretByteArr)
	if err != nil {
		impl.logger.Errorw("error while signing api-token", "error", err)
		return "", err
	}
	return token, nil
}
