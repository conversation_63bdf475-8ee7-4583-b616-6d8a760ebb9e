/*
 * Copyright (c) 2020-2024. Devtron Inc.
 */

package deployEntityMigration

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/devtron-labs/devtron/api/bean/gitOps"
	"github.com/devtron-labs/devtron/client/argocdServer"
	bean7 "github.com/devtron-labs/devtron/client/argocdServer/bean"
	"github.com/devtron-labs/devtron/enterprise/pkg/deploymentWindow"
	"github.com/devtron-labs/devtron/internal/sql/repository/app"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/internal/util"
	app2 "github.com/devtron-labs/devtron/pkg/app"
	chartRepoRepository "github.com/devtron-labs/devtron/pkg/chartRepo/repository"
	"github.com/devtron-labs/devtron/pkg/cluster/environment"
	"github.com/devtron-labs/devtron/pkg/deployment/common"
	commonBean "github.com/devtron-labs/devtron/pkg/deployment/common/bean"
	"github.com/devtron-labs/devtron/pkg/deployment/deployEntityMigration/bean"
	"github.com/devtron-labs/devtron/pkg/deployment/deployEntityMigration/repository"
	bean5 "github.com/devtron-labs/devtron/pkg/deployment/gitOps/common/bean"
	"github.com/devtron-labs/devtron/pkg/deployment/gitOps/config"
	"github.com/devtron-labs/devtron/pkg/deployment/gitOps/git"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/chartRef"
	read2 "github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/read"
	bean3 "github.com/devtron-labs/devtron/pkg/deployment/trigger/devtronApps/bean"
	bean4 "github.com/devtron-labs/devtron/pkg/globalPolicy/bean"
	repository2 "github.com/devtron-labs/devtron/pkg/pipeline/history/repository"
	v0 "github.com/devtron-labs/devtron/pkg/policyGovernance/common/v0"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/model"
	"github.com/devtron-labs/devtron/pkg/sql"
	"github.com/devtron-labs/devtron/pkg/timeoutWindow"
	bean8 "github.com/devtron-labs/devtron/pkg/workflow/cd/bean"
	"github.com/devtron-labs/devtron/pkg/workflow/cd/configHistory"
	"github.com/devtron-labs/devtron/pkg/workflow/cd/read"
	util2 "github.com/devtron-labs/devtron/util"
	"github.com/go-pg/pg"
	"github.com/google/go-github/github"
	"go.opentelemetry.io/otel"
	"go.uber.org/zap"
	"math/rand"
	"net/http"
	"strings"
	"time"
)

type MigrationService interface {
	PerformMigration(ctx *util2.RequestCtx, req *bean.MigrationRequestDto) (*bean.MigrateResponseDto, error)
	PerformMigrationAppLevel(ctx *util2.RequestCtx, req *bean.MigrationRequestDto) (*bean.MigrateResponseDto, error)
}

type MigrationServiceImpl struct {
	logger                              *zap.SugaredLogger
	migrationHistoryRepository          repository.MigrationHistoryRepository
	deploymentConfigService             common.DeploymentConfigService
	gitOperationService                 git.GitOperationService
	chartTemplateService                util.ChartTemplateService
	gitOpsConfigReadService             config.GitOpsConfigReadService
	argoClientWrapperService            argocdServer.ArgoClientWrapperService
	appCrudOperationService             app2.AppCrudOperationService
	deploymentWindowService             deploymentWindow.DeploymentWindowService
	commonPolicyActionService           v0.CommonPolicyActionsServiceV0
	appRepository                       app.AppRepository
	pipelineRepository                  pipelineConfig.PipelineRepository
	chartRepository                     chartRepoRepository.ChartRepository
	environmentService                  environment.EnvironmentService
	pcoReadService                      configHistory.PipelineConfigOverrideReadService
	cdWfrReadService                    read.CdWorkflowRunnerReadService
	deploymentTemplateService           deploymentTemplate.DeploymentTemplateService
	deploymentTemplateHistoryRepository repository2.DeploymentTemplateHistoryRepository
	ChartRefService                     chartRef.ChartRefService
	migrationConfigEnvVars              *util2.DeploymentMigrationConfig
	EnvConfigOverrideService            read2.EnvConfigOverrideService
}

func NewMigrationServiceImpl(logger *zap.SugaredLogger,
	migrationHistoryRepository repository.MigrationHistoryRepository,
	deploymentConfigService common.DeploymentConfigService,
	gitOperationService git.GitOperationService,
	chartTemplateService util.ChartTemplateService,
	gitOpsConfigReadService config.GitOpsConfigReadService,
	argoClientWrapperService argocdServer.ArgoClientWrapperService,
	appCrudOperationService app2.AppCrudOperationService,
	deploymentWindowService deploymentWindow.DeploymentWindowService,
	commonPolicyActionService v0.CommonPolicyActionsServiceV0,
	appRepository app.AppRepository,
	pipelineRepository pipelineConfig.PipelineRepository,
	chartRepository chartRepoRepository.ChartRepository,
	environmentService environment.EnvironmentService,
	pcoReadService configHistory.PipelineConfigOverrideReadService,
	cdWfrReadService read.CdWorkflowRunnerReadService,
	deploymentTemplateService deploymentTemplate.DeploymentTemplateService,
	deploymentTemplateHistoryRepository repository2.DeploymentTemplateHistoryRepository,
	ChartRefService chartRef.ChartRefService,
	envVariables *util2.EnvironmentVariables,
	EnvConfigOverrideService read2.EnvConfigOverrideService) *MigrationServiceImpl {
	//marking all active migrations false
	impl := &MigrationServiceImpl{
		logger:                              logger,
		migrationHistoryRepository:          migrationHistoryRepository,
		deploymentConfigService:             deploymentConfigService,
		gitOperationService:                 gitOperationService,
		chartTemplateService:                chartTemplateService,
		gitOpsConfigReadService:             gitOpsConfigReadService,
		argoClientWrapperService:            argoClientWrapperService,
		appCrudOperationService:             appCrudOperationService,
		deploymentWindowService:             deploymentWindowService,
		commonPolicyActionService:           commonPolicyActionService,
		appRepository:                       appRepository,
		pipelineRepository:                  pipelineRepository,
		chartRepository:                     chartRepository,
		environmentService:                  environmentService,
		pcoReadService:                      pcoReadService,
		cdWfrReadService:                    cdWfrReadService,
		deploymentTemplateService:           deploymentTemplateService,
		deploymentTemplateHistoryRepository: deploymentTemplateHistoryRepository,
		ChartRefService:                     ChartRefService,
		migrationConfigEnvVars:              envVariables.DeploymentMigrationConfig,
		EnvConfigOverrideService:            EnvConfigOverrideService,
	}
	impl.MarkAllActiveFalseAtStartup()
	return impl
}

func (impl *MigrationServiceImpl) MarkAllActiveFalseAtStartup() {
	err := impl.migrationHistoryRepository.UpdateAllActiveFalseAtStartup(bean.MigrationMarkFailedOrchestratorStartup)
	if err != nil {
		impl.logger.Errorw("error in MarkAllActiveFalseAtStartup", "err", err)
	}
}

func (impl *MigrationServiceImpl) PerformMigration(ctx *util2.RequestCtx, req *bean.MigrationRequestDto) (*bean.MigrateResponseDto, error) {
	migrateToGitopsConfig, err := impl.gitOpsConfigReadService.GetGitOpsById(req.MigrateTo.GitOpsProviderId)
	if err != nil || migrateToGitopsConfig == nil || migrateToGitopsConfig.Id < 1 {
		impl.logger.Errorw("error in getting gitOps config", "err", err)
		return nil, &util.ApiError{
			UserMessage:    "Migration cannot be done, migrate to gitOps config not valid",
			Code:           "400",
			HttpStatusCode: http.StatusBadRequest,
		}
	}
	migrateToJson, err := json.Marshal(req.MigrateTo)
	if err != nil {
		impl.logger.Errorw("error in marshalling migrateTo", "err", err)
		return nil, err
	}

	deploymentConfigMap, devtronAppIdMap, err := impl.getDeploymentConfigMap(req)
	if err != nil {
		impl.logger.Errorw("error in getting deployment config", "req", req, "err", err)
		return nil, err
	}

	appIds := make([]int, 0, len(deploymentConfigMap))
	envIds := make([]int, 0, len(deploymentConfigMap))
	for _, dc := range deploymentConfigMap {
		appIds = append(appIds, dc.AppId)
		envIds = append(envIds, dc.EnvironmentId)
	}

	responseDetails := make([]*bean.MigrateResponseDetailDto, len(appIds))
	performMigrationOnIndexes := make([]int, 0, len(appIds))
	createBlackoutWindowForIndexes := make([]int, 0, len(appIds))
	activeMigrationMap := make(map[string]bool) //map of "appId-envId" and true
	activeMigrations, err := impl.migrationHistoryRepository.CheckActiveMigrationForAppIdsAndEnvIds(appIds, envIds)
	if err != nil && err != pg.ErrNoRows {
		impl.logger.Errorw("error checking active migrations", "err", err)
		return nil, err
	}
	for _, activeMigration := range activeMigrations {
		activeMigrationMap[fmt.Sprintf("%d-%d", activeMigration.AppId, activeMigration.EnvId)] = true
	}
	migrationHistoryReqs := make([]*repository.DeploymentAppMigrationHistory, 0, len(appIds))
	for i, appId := range appIds {
		envId := envIds[i]
		//to update metadata
		responseDetails[i] = &bean.MigrateResponseDetailDto{
			AppId: appId,
			EnvId: envId,
		}
		key := fmt.Sprintf("%d-%d", appId, envId)
		if _, ok := activeMigrationMap[key]; ok {
			responseDetails[i].Status = "Existing migration going on"
			continue
		}
		deploymentConfig := deploymentConfigMap[commonBean.UniqueDeploymentConfigIdentifier(key)]
		if deploymentConfig == nil {
			responseDetails[i].Status = "Migration source configuration not found"
			continue
		} else {
			if deploymentConfig.DeploymentAppType != bean3.ArgoCd {
				responseDetails[i].Status = "Migration source not supported"
				continue
			} else {
				if req.DryRun {
					responseDetails[i].Status = "Migration can be done"
					continue
				} else {
					//perform migration
					responseDetails[i].Status = "Migration initiated successfully"
					performMigrationOnIndexes = append(performMigrationOnIndexes, i)
					migrateFromJson, err := json.Marshal(deploymentConfig)
					if err != nil {
						impl.logger.Errorw("error in marshalling migrateFrom", "err", err)
						return nil, err
					}
					migrationHistoryReqs = append(migrationHistoryReqs, &repository.DeploymentAppMigrationHistory{
						AppId:             appId,
						EnvId:             envId,
						IsMigrationActive: true,
						MigrateTo:         string(migrateToJson),
						MigrateFrom:       string(migrateFromJson),
						CurrentStatus:     bean.MigrationStateInitiated,
						AuditLog: sql.AuditLog{
							CreatedOn: time.Now(),
							CreatedBy: req.UserId,
							UpdatedOn: time.Now(),
							UpdatedBy: req.UserId,
						},
					})
					//if app is of type devtron app, then add index for blackout window creation
					if _, ok := devtronAppIdMap[appId]; ok {
						createBlackoutWindowForIndexes = append(createBlackoutWindowForIndexes, i)
					}
				}
			}
		}
	}

	if !req.DryRun && len(performMigrationOnIndexes) > 0 {
		profileName, appIdNameMap, err := impl.createDeploymentWindowProfileAndApply(ctx, createBlackoutWindowForIndexes, appIds, envIds)
		if err != nil {
			impl.logger.Errorw("skipping migration, error in configuring deployment window before migration", "req", req, "err", err)
			return nil, err
		}

		err = impl.migrationHistoryRepository.CreateInBatch(migrationHistoryReqs)
		if err != nil {
			impl.logger.Errorw("error in creating migration history", "error", err)
			return nil, err
		}
		migrationEntryMap := make(map[string]int, len(migrationHistoryReqs)) //map of "appId-envId" and migrationHistory
		for _, entry := range migrationHistoryReqs {
			migrationEntryMap[fmt.Sprintf("%d-%d", entry.AppId, entry.EnvId)] = entry.Id
		}
		migrationInternalReq := &bean.MigrationInternalDto{
			PerformMigrationOnIndexes: performMigrationOnIndexes,
			AppIds:                    appIds,
			EnvIds:                    envIds,
			MigrationEntryMap:         migrationEntryMap,
			DeploymentConfigMap:       deploymentConfigMap,
			MigrateToGitOpsConfig:     migrateToGitopsConfig,
			ProfileName:               profileName,
			AppIdNameMap:              appIdNameMap,
			DevtronAppIdMap:           devtronAppIdMap,
			UserId:                    req.UserId,
		}
		go impl.performMigrationActivity(ctx, migrationInternalReq)
	}
	return &bean.MigrateResponseDto{
		Details: responseDetails,
	}, nil
}

func (impl *MigrationServiceImpl) getDeploymentConfigMap(req *bean.MigrationRequestDto) (map[commonBean.UniqueDeploymentConfigIdentifier]*commonBean.DeploymentConfig, map[int]bool, error) {
	deploymentConfigMap := make(map[commonBean.UniqueDeploymentConfigIdentifier]*commonBean.DeploymentConfig)
	devtronAppIdMap := make(map[int]bool)
	for _, s := range req.Selectors {
		appList, err := impl.appCrudOperationService.GetAppListByIdsOrTeamIds(s.AppIds, s.ProjectIds, s.AppType)
		if err != nil {
			impl.logger.Errorw("error in getting app list by project ids", "appIds", s.AppIds, "projectIds", s.ProjectIds, "appType", s.AppType, "err", err)
			return nil, nil, err
		}
		for _, _app := range appList {
			for _, l := range _app.AppList {
				appId := l.Id
				if s.AppType == app.DevtronApp {
					devtronAppIdMap[appId] = true
				}
				for _, envId := range s.EnvIds {
					var deploymentConfig *commonBean.DeploymentConfig
					if s.AppType == app.DevtronApp {
						deploymentConfig, err = impl.deploymentConfigService.GetAndMigrateConfigIfAbsentForDevtronApps(appId, envId)
					} else if s.AppType == app.DevtronChart {
						deploymentConfig, err = impl.deploymentConfigService.GetAndMigrateConfigIfAbsentForHelmApp(appId, envId)
					}
					if err != nil && err != pg.ErrNoRows {
						impl.logger.Errorw("error in getting deployment config ", "appId", appId, "envId", envId, "err", err)
						return nil, nil, err
					}
					if deploymentConfig != nil {
						deploymentConfigMap[commonBean.GetConfigUniqueIdentifier(deploymentConfig.AppId, deploymentConfig.EnvironmentId)] = deploymentConfig
					}
				}
			}
		}

	}
	return deploymentConfigMap, devtronAppIdMap, nil
}

func (impl *MigrationServiceImpl) createDeploymentWindowProfileAndApply(ctx *util2.RequestCtx, createBlackoutWindowForIndexes, appIds, envIds []int) (string, map[int]string, error) {
	var profileName string
	appIdNameMap := make(map[int]string)
	apps, err := impl.appRepository.FindAppsByIdsOrNames(appIds, nil)
	if err != nil {
		impl.logger.Errorw("error in finding the apps with ids", "appIds", appIds, "err", err)
		return profileName, appIdNameMap, err
	}
	for _, _app := range apps {
		appIdNameMap[_app.Id] = _app.AppName
	}
	if len(createBlackoutWindowForIndexes) == 0 { //no eligible apps, skipping creation
		return profileName, appIdNameMap, nil
	}
	profileName = fmt.Sprintf("%s-%d", "devtron-gitOps-migration", rand.Intn(1000))
	applicationEnvironments := make([]model.AppEnvPolicyContainer, 0, len(createBlackoutWindowForIndexes))

	deploymentWindowPerPipeline := 300
	if impl.migrationConfigEnvVars != nil {
		deploymentWindowPerPipeline = impl.migrationConfigEnvVars.BlackoutWindowTimePerPipeline
	}

	profile := &deploymentWindow.DeploymentWindowProfile{
		DeploymentWindowList: []*timeoutWindow.TimeWindow{{
			Frequency: timeoutWindow.Fixed,
			TimeFrom:  time.Now(),
			TimeTo:    time.Now().Add(time.Duration(len(createBlackoutWindowForIndexes)) * time.Duration(deploymentWindowPerPipeline) * time.Second),
		}},
		Enabled:        true,
		TimeZone:       time.Local.String(),
		DisplayMessage: "GitOps migration in progress, Deployment cannot be triggered",
		DeploymentWindowProfileMetadata: deploymentWindow.DeploymentWindowProfileMetadata{
			Description: "GitOps migration is in progress for this pipeline",
			Name:        profileName,
			Type:        deploymentWindow.Blackout,
			WindowCount: 0,
		},
	}

	profile, err = impl.deploymentWindowService.CreateDeploymentWindowProfile(profile, ctx.GetUserId())
	if err != nil {
		impl.logger.Errorw("error in creating deployment window profile", "err", err)
		return profileName, nil, err
	}

	envs, err := impl.environmentService.FindByIdsAndNames(envIds, nil)
	if err != nil {
		impl.logger.Errorw("error in finding the environments with ids", "envIds", envIds, "err", err)
		return profileName, nil, err
	}
	envIdNameMap := make(map[int]string)

	for _, env := range envs {
		envIdNameMap[env.Id] = env.Environment
	}
	for _, i := range createBlackoutWindowForIndexes {
		appId := appIds[i]
		envId := envIds[i]
		applicationEnvironments = append(applicationEnvironments, model.AppEnvPolicyContainer{
			AppName:  appIdNameMap[appId],
			EnvName:  envIdNameMap[envId],
			PolicyId: profile.Id,
		})
	}
	err = impl.configureDeploymentWindow(ctx, profileName, applicationEnvironments)
	if err != nil {
		impl.logger.Errorw("skipping migration, error in applying deployment window before migration", "profileName", profileName, "appEnvs", applicationEnvironments, "err", err)
		return profileName, nil, err
	}
	return profileName, appIdNameMap, nil
}

func (impl *MigrationServiceImpl) configureDeploymentWindow(ctx *util2.RequestCtx, profileName string, profileAppEnvs []model.AppEnvPolicyContainer) error {
	policyApplyRequest := &model.BulkPromotionPolicyApplyRequest{
		PolicyType:              bean4.GLOBAL_POLICY_TYPE_DEPLOYMENT_WINDOW,
		PolicyVersion:           bean4.GLOBAL_POLICY_VERSION_V1,
		ApplicationEnvironments: profileAppEnvs,
		ApplyToPolicyNames:      []string{profileName},
	}
	err := impl.commonPolicyActionService.ApplyPolicyToIdentifiers(ctx, policyApplyRequest)
	if err != nil {
		impl.logger.Errorw("error in applying deployment window profile to given app and env", "err", err)
		return err
	}
	return err
}

func (impl *MigrationServiceImpl) performMigrationActivity(ctx *util2.RequestCtx, payload *bean.MigrationInternalDto) {
	for _, i := range payload.PerformMigrationOnIndexes {
		appId := payload.AppIds[i]
		envId := payload.EnvIds[i]
		key := commonBean.GetConfigUniqueIdentifier(appId, envId)
		migrationId := payload.MigrationEntryMap[string(key)]
		deploymentConfig := payload.DeploymentConfigMap[key]

		if len(deploymentConfig.RepoName) == 0 {
			//if the repoUrl is not found in the deploymentConfig
			if len(deploymentConfig.GetRepoURL()) == 0 {
				env, err := impl.environmentService.FindById(envId)
				if err != nil {
					impl.logger.Errorw("error in finding environment", "envId", envId, "err", err)
					return
				}
				acdAppName := util2.BuildDeployedAppName(payload.AppIdNameMap[appId], env.Environment)
				repoUrl, err := impl.argoClientWrapperService.GetGitOpsRepoURLForApplication(ctx, acdAppName)
				if err != nil {
					impl.logger.Errorw("error in getting repo url", "err", err)
					return
				}
				deploymentConfig.SetRepoURL(repoUrl)
			}
			deploymentConfig.RepoName = impl.gitOpsConfigReadService.GetGitOpsRepoNameFromUrl(deploymentConfig.GetRepoURL())
		}
		chartDir := fmt.Sprintf("%s-%s", deploymentConfig.RepoName, impl.chartTemplateService.GetDir())
		//get new repo url
		chartGitAttr, err := impl.createRepoAndGetURL(ctx, migrationId, payload.MigrateToGitOpsConfig, deploymentConfig.RepoName, deploymentConfig.GetTargetRevision(), payload.UserId)
		if err != nil {
			impl.logger.Errorw("getNewRepoUrl", "gitopsConfigId", payload.MigrateToGitOpsConfig.Id, "deploymentConfig", deploymentConfig, "err", err)
			errStatusUpdate := impl.updateMigrationHistoryStatus(migrationId, bean.MigrationFailed, false, err)
			if errStatusUpdate != nil {
				impl.logger.Errorw("error in updating migration history", "err", errStatusUpdate)
			}
			continue
		}

		newRepoUrl := chartGitAttr.RepoUrl
		isInstalledApp := !payload.DevtronAppIdMap[appId]
		if chartGitAttr.IsNewRepo || chartGitAttr.IsRepoEmpty || isInstalledApp { //repo not existed before, to clone old repo and push to new
			// repo not existed before, to clone old repo and push to new
			// clone existing repo
			cloneDir, errCloneExistingRepo := impl.cloneExistingRepo(ctx, migrationId, chartDir, deploymentConfig)
			if errCloneExistingRepo != nil {
				impl.logger.Errorw("error in cloning existing repo", "migrationId", migrationId, "chartDir", chartDir, "deploymentConfig", deploymentConfig, "err", errCloneExistingRepo)
				continue
			}
			//push to new remote
			errPushToNewRemote := impl.pushToNewRemote(ctx, migrationId, newRepoUrl, cloneDir)
			if errPushToNewRemote != nil {
				impl.logger.Errorw("error in pushing to new remote", "migrationId", migrationId, "newRepoUrl", newRepoUrl, "chartDir", chartDir, "err", errPushToNewRemote)
				continue
			}
		} else {
			latestCdWfr, err := impl.cdWfrReadService.FindLatestWorkflowRunnerByAppAndEnvId(appId, envId)
			if err != nil {
				impl.logger.Errorw("error, FindLatestWorkflowRunnerByAppAndEnvId", "appId", appId, "envId", envId, "err", err)
				errStatusUpdate := impl.updateMigrationHistoryStatus(migrationId, bean.MigrationFailed, false, err)
				if errStatusUpdate != nil {
					impl.logger.Errorw("error in updating migration history", "err", errStatusUpdate)
				}
				continue
			}
			//since repo already exists, only commit environment values to new remote
			if latestCdWfr != nil && latestCdWfr.Id > 0 {
				// commit values only if pipeline is triggered
				errCommitToNewRemote := impl.commitToNewRemote(ctx, deploymentConfig, migrationId, newRepoUrl, appId, envId, payload.AppIdNameMap[appId], latestCdWfr, ctx.GetUserId())
				if errCommitToNewRemote != nil {
					impl.logger.Errorw("error in committing to new remote", "migrationId", migrationId, "newRepoUrl", newRepoUrl, "appId", appId, "envId", envId, "err", errCommitToNewRemote)
					continue
				}
			}
		}

		//argocd patch
		errArgoPatch := impl.patchArgoCDApp(ctx, migrationId, newRepoUrl, deploymentConfig)
		if errArgoPatch != nil {
			impl.logger.Errorw("error in patching ArgoCD app", "migrationId", migrationId, "newRepoUrl", newRepoUrl, "deploymentConfig", deploymentConfig, "err", errArgoPatch)
			continue
		}

		//update configuration
		errDBConfigUpdate := impl.updateDBConfiguration(ctx, migrationId, newRepoUrl, deploymentConfig, payload.UserId)
		if errDBConfigUpdate != nil {
			impl.logger.Errorw("error in updating DB configuration", "migrationId", migrationId, "newRepoUrl", newRepoUrl, "deploymentConfig", deploymentConfig, "err", errDBConfigUpdate)
			continue
		}

		//mark finished
		errStatusUpdate := impl.updateMigrationHistoryStatus(migrationId, bean.MigrationFinished, false, nil)
		if errStatusUpdate != nil {
			impl.logger.Errorw("error in updating migration history", "err", errStatusUpdate)
			continue
		}

		sleepTime := time.Duration(impl.migrationConfigEnvVars.SleepTimeBetweenMigration)
		time.Sleep(sleepTime * time.Second)

	}
	if len(payload.ProfileName) > 0 {
		//when all work is finished, delete deployment window
		err := impl.deploymentWindowService.DeleteDeploymentWindowProfileForName(payload.ProfileName, ctx.GetUserId())
		if err != nil {
			impl.logger.Errorw("error in deleting deployment window profile", "profileName", payload.ProfileName, "err", err)
		}
	}
}

func (impl *MigrationServiceImpl) cloneExistingRepo(ctx context.Context, migrationId int, chartDir string, deploymentConfig *commonBean.DeploymentConfig) (string, error) {
	errStatusUpdate := impl.updateMigrationHistoryStatus(migrationId, bean.MigrationStateOldRepoCloneStarted, true, nil)
	if errStatusUpdate != nil {
		impl.logger.Errorw("error in updating migration history", "err", errStatusUpdate)
		return "", errStatusUpdate
	}
	clonedDir, err := impl.gitOperationService.CloneAndPull(chartDir, deploymentConfig.GetRepoURL(), deploymentConfig.GetTargetRevision())
	if err != nil {
		errStatusUpdate = impl.updateMigrationHistoryStatus(migrationId, bean.MigrationStateOldRepoCloneFailed, false, err)
		if errStatusUpdate != nil {
			impl.logger.Errorw("error in updating migration history", "err", errStatusUpdate)
		}
		return "", err
	}
	errStatusUpdate = impl.updateMigrationHistoryStatus(migrationId, bean.MigrationStateOldRepoCloneFinished, true, nil)
	if errStatusUpdate != nil {
		impl.logger.Errorw("error in updating migration history", "err", errStatusUpdate)
		return "", errStatusUpdate
	}
	return clonedDir, nil
}

func (impl *MigrationServiceImpl) createRepoAndGetURL(ctx context.Context, migrationId int, gitopsConfig *gitOps.GitOpsConfigDto, repoName string,
	targetRevision string, userId int32) (*bean5.ChartGitAttribute, error) {
	chartGitAttribute, err := impl.gitOperationService.CreateGitRepositoryAtHost(context.Background(), gitopsConfig.Host, repoName, targetRevision, userId)
	if err != nil {

		impl.SleepIfRateLimitExceededErrForGithub(err)

		impl.logger.Errorw("error in getting repo url", "err", err)
		errStatusUpdate := impl.updateMigrationHistoryStatus(migrationId, bean.MigrationFailed, false, err)
		if errStatusUpdate != nil {
			impl.logger.Errorw("error in updating migration history", "err", errStatusUpdate)
		}
		return nil, err
	}

	return chartGitAttribute, nil
}

func (impl *MigrationServiceImpl) SleepIfRateLimitExceededErrForGithub(err error) {
	var rateLimitError *github.RateLimitError
	var abuseRateLimitError *github.AbuseRateLimitError
	var sleepTime time.Duration
	if errors.As(err, &rateLimitError) {
		resetTime := rateLimitError.Rate.Reset.Time
		sleepTime = time.Until(resetTime)
		if sleepTime < 0 {
			sleepTime = 0
		}
	} else if errors.As(err, &abuseRateLimitError) {
		sleepTime = abuseRateLimitError.GetRetryAfter()
	} else if strings.Contains(err.Error(), "secondary rate limit") {
		sleepTime = time.Minute
	}
	if sleepTime > 0 {
		impl.logger.Infow("sleeping as github rate time exceeded", "sleepTime", sleepTime)
		time.Sleep(sleepTime)
	}
}

func (impl *MigrationServiceImpl) commitToNewRemote(ctx context.Context, envDeploymentConfig *commonBean.DeploymentConfig, migrationId int, newRepoUrl string, appId, envId int, appName string,
	latestCdWfr *bean8.CdWorkflowRunnerDto, userId int32) error {
	repoName := envDeploymentConfig.RepoName
	errStatusUpdate := impl.updateMigrationHistoryStatus(migrationId, bean.MigrationStateNewRepoCommitStarted, true, nil)
	if errStatusUpdate != nil {
		impl.logger.Errorw("error in updating migration history", "err", errStatusUpdate)
		return errStatusUpdate
	}
	pco, err := impl.pcoReadService.FindLatestPCO(appId, envId, bean3.ArgoCd)
	if err != nil {
		impl.logger.Errorw("error, FindLatestPCO", "appId", appId, "envId", envId, "err", err)
		errStatusUpdate = impl.updateMigrationHistoryStatus(migrationId, bean.MigrationStateNewRepoCommitFailed, false, err)
		if errStatusUpdate != nil {
			impl.logger.Errorw("error in updating migration history", "err", errStatusUpdate)
		}
		return err
	}

	deploymentTemplateHistory, err := impl.deploymentTemplateHistoryRepository.GetHistoryByPipelineIdAndWfrId(latestCdWfr.PipelineId, latestCdWfr.Id)
	//VARIABLE_SNAPSHOT_GET and resolve
	if err != nil {
		impl.logger.Errorw("error in getting deployed deployment template history by pipelineId and wfrId", "err", err, "pipelineId", &latestCdWfr.PipelineId, "wfrId", latestCdWfr.Id)
		errStatusUpdate := impl.updateMigrationHistoryStatus(migrationId, bean.MigrationStateNewRepoCommitFailed, false, err)
		if errStatusUpdate != nil {
			impl.logger.Errorw("error in updating migration history", "err", errStatusUpdate)
		}
		return err
	}
	templateName := deploymentTemplateHistory.TemplateName
	templateVersion := deploymentTemplateHistory.TemplateVersion
	//getting chart_ref by id
	chartRefDto, err := impl.ChartRefService.FindByVersionAndName(templateVersion, templateName)
	if err != nil {
		impl.logger.Errorw("error in getting chartRef by version and name", "err", err, "version", templateVersion, "name", templateName)
		errStatusUpdate := impl.updateMigrationHistoryStatus(migrationId, bean.MigrationStateNewRepoCommitFailed, false, err)
		if errStatusUpdate != nil {
			impl.logger.Errorw("error in updating migration history", "err", errStatusUpdate)
		}
		return err
	}
	//assuming that if a chartVersion is deployed then it's envConfigOverride will be available
	envOverride, err := impl.EnvConfigOverrideService.GetByAppIdEnvIdAndChartRefId(appId, envId, chartRefDto.Id)
	if err != nil {
		errStatusUpdate := impl.updateMigrationHistoryStatus(migrationId, bean.MigrationStateNewRepoCommitFailed, false, err)
		if errStatusUpdate != nil {
			impl.logger.Errorw("error in updating migration history", "err", errStatusUpdate)
		}
		impl.logger.Errorw("error in getting envConfigOverride for pipeline for specific chartVersion", "err", err, "appId", appId, "envId", envId, "chartRefId", chartRefDto.Id)
		return err
	}

	builtChartPath, err := impl.deploymentTemplateService.BuildChartAndGetPath(appName, envOverride, envDeploymentConfig, ctx)
	if err != nil {
		impl.logger.Errorw("error in parsing reference chart", "err", err)
		errStatusUpdate = impl.updateMigrationHistoryStatus(migrationId, bean.MigrationStateNewRepoCommitFailed, false, err)
		if errStatusUpdate != nil {
			impl.logger.Errorw("error in updating migration history", "err", errStatusUpdate)
		}
		return err
	}
	err = impl.gitOperationService.PushChartToGitRepo(ctx, repoName, envDeploymentConfig.GetChartLocation(),
		builtChartPath, newRepoUrl, envDeploymentConfig.GetTargetRevision(), userId)
	if err != nil {
		impl.logger.Errorw("error in pushing chart to git", "err", err)
		errStatusUpdate = impl.updateMigrationHistoryStatus(migrationId, bean.MigrationStateNewRepoCommitFailed, false, err)
		if errStatusUpdate != nil {
			impl.logger.Errorw("error in updating migration history", "err", errStatusUpdate)
		}
		return err
	}

	chartRepoName := impl.gitOpsConfigReadService.GetGitOpsRepoNameFromUrl(newRepoUrl)
	_, span := otel.Tracer("orchestrator").Start(ctx, "chartTemplateService.GetUserEmailIdAndNameForGitOpsCommit")
	//getting username & emailId for commit author data
	userEmailId, userName := impl.gitOpsConfigReadService.GetUserEmailIdAndNameForGitOpsCommit(userId)
	span.End()
	chartGitAttr := &git.ChartConfig{
		FileName:       fmt.Sprintf("_%d-values.yaml", envId),
		FileContent:    pco.PipelineMergedValues,
		ChartName:      envOverride.Chart.ChartName,
		ChartLocation:  envOverride.Chart.ChartLocation,
		ChartRepoName:  chartRepoName,
		ReleaseMessage: fmt.Sprintf("release-%d-env-%d ", pco.Id, envId),
		UserName:       userName,
		UserEmailId:    userEmailId,
		ChartRepoURL:   newRepoUrl,
		TargetRevision: envDeploymentConfig.GetTargetRevision(),
	}

	_, span = otel.Tracer("orchestrator").Start(ctx, "gitOperationService.CommitValues")
	_, _, err = impl.gitOperationService.CommitValues(ctx, chartGitAttr)
	span.End()
	if err != nil {
		impl.logger.Errorw("error in git commit", "err", err)
		errStatusUpdate = impl.updateMigrationHistoryStatus(migrationId, bean.MigrationStateNewRepoCommitFailed, false, err)
		if errStatusUpdate != nil {
			impl.logger.Errorw("error in updating migration history", "err", errStatusUpdate)
		}
		return err
	}
	errStatusUpdate = impl.updateMigrationHistoryStatus(migrationId, bean.MigrationStateNewRepoCommitFinished, true, nil)
	if errStatusUpdate != nil {
		impl.logger.Errorw("error in updating migration history", "err", errStatusUpdate)
		return errStatusUpdate
	}
	return nil
}

func (impl *MigrationServiceImpl) pushToNewRemote(ctx context.Context, migrationId int, newRepoUrl, chartDir string) error {

	defer impl.chartTemplateService.CleanDir(chartDir)

	errStatusUpdate := impl.updateMigrationHistoryStatus(migrationId, bean.MigrationStateNewRepoPushStarted, true, nil)
	if errStatusUpdate != nil {
		impl.logger.Errorw("error in updating migration history", "err", errStatusUpdate)
		return errStatusUpdate
	}
	err := impl.gitOperationService.PushToNewRemote(ctx, chartDir, "new-origin", newRepoUrl)
	if err != nil && err.Error() != "already up-to-date" {
		impl.logger.Errorw("error in pushing to remote", "err", err)
		errStatusUpdate = impl.updateMigrationHistoryStatus(migrationId, bean.MigrationStateNewRepoPushFailed, false, err)
		if errStatusUpdate != nil {
			impl.logger.Errorw("error in updating migration history", "err", errStatusUpdate)
		}
		return err
	}
	errStatusUpdate = impl.updateMigrationHistoryStatus(migrationId, bean.MigrationStateNewRepoPushFinished, true, nil)
	if errStatusUpdate != nil {
		impl.logger.Errorw("error in updating migration history", "err", errStatusUpdate)
		return errStatusUpdate
	}
	return nil
}

func (impl *MigrationServiceImpl) updateDBConfiguration(ctx context.Context, migrationId int, newRepoUrl string, deploymentConfig *commonBean.DeploymentConfig, userId int32) error {
	errStatusUpdate := impl.updateMigrationHistoryStatus(migrationId, bean.MigrationDBConfigProcessStarted, true, nil)
	if errStatusUpdate != nil {
		impl.logger.Errorw("error in updating migration history", "err", errStatusUpdate)
		return errStatusUpdate
	}

	deploymentConfig.SetRepoURL(newRepoUrl)
	deploymentConfig, err := impl.deploymentConfigService.CreateOrUpdateConfig(nil, deploymentConfig, userId)
	if err != nil {
		impl.logger.Errorw("error in updating newRepoURl deployment config", "newRepoUrl", newRepoUrl, "appId", deploymentConfig.AppId, "envId", deploymentConfig.EnvironmentId, "err", err)
		errStatusUpdate = impl.updateMigrationHistoryStatus(migrationId, bean.MigrationDBConfigProcessFailed, false, err)
		if errStatusUpdate != nil {
			impl.logger.Errorw("error in updating migration history", "err", errStatusUpdate)
		}
		return err
	}

	errStatusUpdate = impl.updateMigrationHistoryStatus(migrationId, bean.MigrationDBConfigProcessFinished, true, nil)
	if errStatusUpdate != nil {
		impl.logger.Errorw("error in updating migration history", "err", errStatusUpdate)
		return errStatusUpdate
	}
	return nil
}

func (impl *MigrationServiceImpl) patchArgoCDApp(ctx context.Context, migrationId int, newRepoUrl string, deploymentConfig *commonBean.DeploymentConfig) error {
	errStatusUpdate := impl.updateMigrationHistoryStatus(migrationId, bean.MigrationArgoPatchStarted, true, nil)
	if errStatusUpdate != nil {
		impl.logger.Errorw("error in updating migration history", "err", errStatusUpdate)
		return errStatusUpdate
	}

	argoAppName, err := impl.getDefaultArgoAppName(deploymentConfig.AppId, deploymentConfig.EnvironmentId)
	if err != nil {
		impl.logger.Errorw("error in getting argo app name", "appId", deploymentConfig.AppId, "envId", deploymentConfig.EnvironmentId, "err", err)
		return err
	}

	patchRequestDto := &bean7.ArgoCdAppPatchReqDto{
		ArgoAppName:    argoAppName,
		GitRepoUrl:     newRepoUrl,
		TargetRevision: bean7.TargetRevisionMaster,
		PatchType:      bean7.PatchTypeMerge,
	}
	urlWithUserName, err := impl.gitOperationService.GetRepoUrlWithUserName(newRepoUrl)
	if err != nil {
		errStatusUpdate = impl.updateMigrationHistoryStatus(migrationId, bean.MigrationFailed, false, err)
		if errStatusUpdate != nil {
			impl.logger.Errorw("error in updating migration history", "err", errStatusUpdate)
		}
		return err
	}
	patchRequestDto.GitRepoUrl = urlWithUserName
	err = impl.argoClientWrapperService.PatchArgoCdApp(ctx, patchRequestDto)
	if err != nil {
		impl.logger.Errorw("error in patching argo pipeline", "err", err, "req", patchRequestDto)
		errStatusUpdate = impl.updateMigrationHistoryStatus(migrationId, bean.MigrationArgoPatchFailed, false, err)
		if errStatusUpdate != nil {
			impl.logger.Errorw("error in updating migration history", "err", errStatusUpdate)
		}
		return err
	}
	errStatusUpdate = impl.updateMigrationHistoryStatus(migrationId, bean.MigrationArgoPatchFinished, true, nil)
	if errStatusUpdate != nil {
		impl.logger.Errorw("error in updating migration history", "err", errStatusUpdate)
		return errStatusUpdate
	}
	return nil
}

func (impl *MigrationServiceImpl) updateMigrationHistoryStatus(migrationId int, status bean.GitOpsToGitOpsMigrationState, isMigrationActive bool, errEncountered error) error {
	if status == bean.MigrationFinished {
		err := impl.migrationHistoryRepository.UpdateMigrationStatus(migrationId, status, false)
		if err != nil {
			impl.logger.Errorw("error, updateMigrationHistoryStatus", "migrationId", migrationId, "status", status, "err", err)
			errNew := impl.updateMigrationHistoryErrStatus(migrationId, bean.MigrationFailed, err)
			if errNew != nil {
				return errNew
			}
			return err
		}
	} else {
		if isMigrationActive {
			err := impl.migrationHistoryRepository.UpdateMigrationStatus(migrationId, status, isMigrationActive)
			if err != nil {
				impl.logger.Errorw("error, updateMigrationHistoryStatus", "migrationId", migrationId, "status", status, "isActive", isMigrationActive, "err", err)
				errNew := impl.updateMigrationHistoryErrStatus(migrationId, bean.MigrationFailed, err)
				if errNew != nil {
					return errNew
				}
				return err
			}
		} else {
			return impl.updateMigrationHistoryErrStatus(migrationId, status, errEncountered)
		}
	}
	return nil
}

func (impl *MigrationServiceImpl) updateMigrationHistoryErrStatus(migrationId int, errStatus bean.GitOpsToGitOpsMigrationState, errEncountered error) error {
	err := impl.migrationHistoryRepository.UpdateMigrationErrorStatus(migrationId, errStatus, errEncountered)
	if err != nil {
		impl.logger.Errorw("error, updateMigrationHistoryErrStatus", "migrationId", migrationId, "errStatus", errStatus, "err", err)
		return err
	}
	return nil
}

func (impl *MigrationServiceImpl) getDefaultArgoAppName(appId, envId int) (string, error) {
	app, err := impl.appRepository.FindById(appId)
	if err != nil {
		impl.logger.Errorw("error in getting app by id", "appId", appId, "err", err)
		return "", err
	}
	env, err := impl.environmentService.FindById(envId)
	if err != nil {
		impl.logger.Errorw("error in fetching env by id", "envId", envId, "err", err)
		return "", err
	}
	return util2.BuildDeployedAppName(app.AppName, env.Environment), nil
}

/*

//steps to migration
0. check the migrateTo config if its valid or not
1. get all pipelines in the request
2. get their deployment config (if possible get in 1)
3. compare the migrateTo and migrateFrom
4. create payloads
5. if dryRun return
6. create the migration entry
7. create the blackout window
8. iterate over valid requests
9. do migration activity

argo-argo migration -
1. clone repo source code
2. push source code to new url (if clone source was not found then create new)
3. update config at devtron side
4. update config at argo-cd side


at startup if there any incomplete migration, then mark them fail. Not changing blackout window here as will require manual intervention here
to rollback or complete migration

*/
