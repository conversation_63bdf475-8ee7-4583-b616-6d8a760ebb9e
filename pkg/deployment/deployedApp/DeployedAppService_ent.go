package deployedApp

import (
	"context"
	"fmt"
	"github.com/devtron-labs/devtron/enterprise/pkg/deploymentWindow"
	"github.com/devtron-labs/devtron/internal/constants"
	"github.com/devtron-labs/devtron/internal/util"
	bean6 "github.com/devtron-labs/devtron/pkg/auth/user/bean"
	bean2 "github.com/devtron-labs/devtron/pkg/deployment/common/bean"
	"github.com/devtron-labs/devtron/pkg/deployment/deployedApp/bean"
	bean5 "github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/chartResourceConfig/bean"
	bean7 "github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/bean"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/common/alpha1/adaptor"
	model2 "github.com/devtron-labs/devtron/pkg/policyGovernance/model"
	"github.com/devtron-labs/devtron/pkg/resourceQualifiers"
	"net/http"
	"time"
)

func (impl *DeployedAppServiceImpl) HibernationPatch(ctx context.Context, appId, envId int) (*bean.HibernationPatchResponse, error) {
	stopTemplateResp, err := impl.getStopTemplateByAppIdEnvId(appId, envId)
	if err != nil {
		impl.logger.Errorw("error in getting the HibernationPatch", "appId", appId, "envId", envId, "err", err)
		return nil, err
	}

	// Determine whether a hibernation patch is configured.
	resp := &bean.HibernationPatchResponse{
		IsHibernationPatchConfigured: !stopTemplateResp.IsUserUploaded || stopTemplateResp.HibernationPatch != "",
	}

	// Set ChartResourceDto: if selectors are missing, provide a fallback to its chart applied
	if stopTemplateResp.ChartStopResource == nil || len(stopTemplateResp.ChartStopResource.Selectors) == 0 || (stopTemplateResp.ChartStopResource != nil && stopTemplateResp.ChartStopResource.ResourceValue == bean5.DefaultEmptyValue) {
		fallbackChartResource := &bean5.ChartResourceDto{
			Selectors: []bean5.Selector{
				{
					AttributeSelector: bean5.AttributeSelector{
						Category:      bean5.CHART,
						ChartVersions: []bean5.ChartVersion{{Type: stopTemplateResp.ChartName}},
					},
				},
			},
		}

		var resourceVal string
		if stopTemplateResp.ChartStopResource != nil && stopTemplateResp.ChartStopResource.ResourceValue != "" {
			if stopTemplateResp.ChartStopResource.ResourceValue == bean5.DefaultEmptyValue {
				resp.IsHibernationPatchConfigured = false
				resourceVal = ""
			} else {
				resourceVal = stopTemplateResp.ChartStopResource.ResourceValue
			}
		}
		fallbackChartResource.ResourceValue = resourceVal
		resp.ChartResourceDto = fallbackChartResource
	} else {
		resp.ChartResourceDto = &bean5.ChartResourceDto{
			Selectors:     stopTemplateResp.ChartStopResource.Selectors,
			ResourceValue: stopTemplateResp.ChartStopResource.ResourceValue,
		}
	}
	return resp, nil
}
func (impl *DeployedAppServiceImpl) StopStartAppV1(ctx context.Context, stopRequest *bean.StopAppRequest, userMetadata *bean6.UserMetadata) (int, error) {
	if stopRequest.RequestType == bean.STOP {
		hibernateResourceConfigured, err := impl.HibernationPatch(ctx, stopRequest.AppId, stopRequest.EnvironmentId)
		if err != nil {
			impl.logger.Errorw("error in checking hibernation configured or not StopStartApp", "stopRequest", stopRequest, "err", err)
			return 0, err
		} else if hibernateResourceConfigured.IsHibernationPatchConfigured != true {
			impl.logger.Errorw("hibernation is not configured", "stopRequest", stopRequest, "err", bean.HibernationNotConfiguredErr.Error())
			return 0, bean.HibernationNotConfiguredErr
		}
		stopRequest.IsHibernationPatchConfigured = hibernateResourceConfigured.IsHibernationPatchConfigured
	}
	return impl.stopStartApp(ctx, stopRequest, userMetadata)
}

func (impl *DeployedAppServiceImpl) getTemplate(stopRequest *bean.StopAppRequest) (string, error) {
	resp, err := impl.getStopTemplateByAppIdEnvId(stopRequest.AppId, stopRequest.EnvironmentId)
	if err != nil {
		impl.logger.Errorw("error in getting hibernating patch configurations", "err", err)
		return "", err
	}

	var template string
	if resp.ChartStopResource != nil {
		template = resp.ChartStopResource.ResourceValue
	}

	// For non-user-uploaded templates, if no value is provided, use the default.
	if !resp.IsUserUploaded && template == "" {
		template = bean2.DefaultStopTemplate
	}

	if template == "" {
		return "", bean.HibernationNotConfiguredErr
	}
	return template, nil
}
func (impl *DeployedAppServiceImpl) getStopTemplateByAppIdEnvId(appId, envId int) (*bean5.StopTemplateResponse, error) {
	chartRefId, err := impl.envConfigOverrideService.FindChartRefForAppByAppIdAndEnvId(appId, envId)
	if err != nil || chartRefId == 0 {
		impl.logger.Errorw("error in fetching the latestChart for app", "appId", appId, "envId", envId, "err", err)
		return nil, err
	}
	scope := &resourceQualifiers.Scope{
		AppId:      appId,
		EnvId:      envId,
		ChartRefId: chartRefId,
	}
	return impl.chartConfigResourceService.GetStopTemplate(scope)
}
func (impl *DeployedAppServiceImpl) checkForDeploymentWindow(appId, envId int, userId int32) error {
	actionState, envState, err := impl.deploymentWindowService.GetStateForAppEnv(time.Now(), appId, envId, userId)
	if err != nil {
		return fmt.Errorf("error in getting deployment window state %v", err)
	}
	if !actionState.IsActionAllowedWithBypass() {
		return deploymentWindow.GetActionBlockedError(actionState.GetErrorMessageForProfileAndState(envState), constants.HttpStatusUnprocessableEntity)
	}
	return nil
}

func (impl *DeployedAppServiceImpl) checkForDeploymentApprovalConfiguration(ctx context.Context, appId, envId int, userMetadata *bean6.UserMetadata) error {
	approvalConfigurationMap, err := impl.approvalConfigurationEnforcementService.GetConfigurationsByAppAndEnvId(model2.APPLY_POLICY_APPROVAL_DEPLOYMENT, appId, envId)
	if err != nil {
		impl.logger.Error("error in getting the approval config info by app and env ids", "appId", appId, "envId", envId, "err", err)
		return err
	}
	approvalConfiguration := approvalConfigurationMap[bean7.APPROVAL_FOR_DEPLOYMENT]
	if approvalConfiguration == nil {
		// deployment approval not configured hence return from here
		return nil
	}
	isUserException, err := impl.globalPolicyDataManager.IsUserAnExceptionByRqmResourceType(ctx, adaptor.ApplyPolicyTypeToRqmResourceType(model2.APPLY_POLICY_APPROVAL_DEPLOYMENT).ToInt(), userMetadata)
	if err != nil {
		impl.logger.Errorw("error in checking if a user is exception user or not by policy type ", "policyType", model2.APPLY_POLICY_APPROVAL_DEPLOYMENT, "err", err)
		return err
	} else if isUserException {
		// deployment approval configured and user is exception then return from here
		return nil
	}
	return util.DefaultApiError().WithHttpStatusCode(http.StatusConflict).
		WithUserMessage(bean.AppDeploymentRequiredApprovalErr.Error())
}

func (impl *DeployedAppServiceImpl) checkForFeasibilityBeforeStartStop(ctx context.Context, appId, envId int, userMetadata *bean6.UserMetadata) error {
	err := impl.checkForDeploymentApprovalConfiguration(ctx, appId, envId, userMetadata)
	if err != nil {
		return err
	}
	err = impl.checkForDeploymentWindow(appId, envId, userMetadata.UserId)
	if err != nil {
		impl.logger.Errorw("error in passing deploymentWindow feasibility", "appId", appId, "envId", envId, "err", err)
		return err
	}
	return nil
}
