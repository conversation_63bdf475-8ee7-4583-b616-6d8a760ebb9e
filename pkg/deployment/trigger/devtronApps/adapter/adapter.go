/*
 * Copyright (c) 2024. Devtron Inc.
 */

package adapter

import (
	"context"
	apiBean "github.com/devtron-labs/devtron/api/bean"
	helmBean "github.com/devtron-labs/devtron/api/helm-app/service/bean"
	"github.com/devtron-labs/devtron/enterprise/pkg/resourceFilter"
	"github.com/devtron-labs/devtron/internal/sql/models"
	"github.com/devtron-labs/devtron/internal/sql/repository"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig/bean/workflow/cdWorkflow"
	bean2 "github.com/devtron-labs/devtron/pkg/deployment/common/bean"
	"github.com/devtron-labs/devtron/pkg/deployment/trigger/devtronApps/bean"
	eventProcessorBean "github.com/devtron-labs/devtron/pkg/eventProcessor/bean"
	bean3 "github.com/devtron-labs/devtron/pkg/resourceQualifiers"
	"github.com/devtron-labs/devtron/util/expressionEvaluator"
	"time"
)

func SetPipelineFieldsInOverrideRequest(overrideRequest *apiBean.ValuesOverrideRequest, pipeline *pipelineConfig.Pipeline, deploymentConfig *bean2.DeploymentConfig) {
	overrideRequest.PipelineId = pipeline.Id
	overrideRequest.PipelineName = pipeline.Name
	overrideRequest.EnvId = pipeline.EnvironmentId
	environment := pipeline.Environment
	overrideRequest.EnvName = environment.Name
	overrideRequest.ClusterId = environment.ClusterId
	overrideRequest.IsProdEnv = environment.Default
	overrideRequest.AppId = pipeline.AppId
	overrideRequest.ProjectId = pipeline.App.TeamId
	overrideRequest.AppName = pipeline.App.AppName
	overrideRequest.DeploymentAppType = deploymentConfig.DeploymentAppType
	overrideRequest.Namespace = pipeline.Environment.Namespace
	overrideRequest.ReleaseName = pipeline.DeploymentAppName
}

func GetTriggerRequirementRequest(scope bean3.Scope, triggerRequest bean.CdTriggerRequest, stage resourceFilter.ReferenceType, deploymentType models.DeploymentType) *bean.TriggerRequirementRequestDto {
	return &bean.TriggerRequirementRequestDto{
		TriggerRequest: triggerRequest,
		Scope:          scope,
		Stage:          stage,
		DeploymentType: deploymentType,
	}
}

func GetTriggerFeasibilityResponse(isApprovalPolicyConfiguredForPipeline bool, approvalRequestData *bean.ImageApprovedActionData, triggerRequest bean.CdTriggerRequest, filterIdVsState map[int]expressionEvaluator.FilterState, filters []*resourceFilter.FilterMetaDataBean) *bean.TriggerFeasibilityResponse {
	return &bean.TriggerFeasibilityResponse{
		ImageApprovedActionData:               approvalRequestData,
		TriggerRequest:                        triggerRequest,
		FilterIdVsState:                       filterIdVsState,
		Filters:                               filters,
		IsApprovalPolicyConfiguredForPipeline: isApprovalPolicyConfiguredForPipeline,
	}
}

func GetVulnerabilityCheckRequest(cdPipeline *pipelineConfig.Pipeline, imageDigest string) *bean.VulnerabilityCheckRequest {
	return &bean.VulnerabilityCheckRequest{
		CdPipeline:  cdPipeline,
		ImageDigest: imageDigest,
	}
}

func GetTriggerOperationDto(triggerRequest bean.CdTriggerRequest, executorType cdWorkflow.WorkflowExecutorType, pipelineId int, scope bean3.Scope, triggeredAt time.Time, overrideCdWrfId int, byPassFeasibility bool, appId int, envId int) *bean.TriggerOperationDto {
	return &bean.TriggerOperationDto{
		TriggerRequest:    triggerRequest,
		ExecutorType:      executorType,
		PipelineId:        pipelineId,
		Scope:             scope,
		OverrideCdWrfId:   overrideCdWrfId,
		TriggeredAt:       triggeredAt,
		ByPassFeasibility: byPassFeasibility,
		AppId:             appId,
		EnvId:             envId,
	}
}

func GetTriggerRequest(pipeline *pipelineConfig.Pipeline,
	artifact *repository.CiArtifact,
	triggeredBy int32,
	triggerContext bean.TriggerContext,
	cdWorkflowType apiBean.WorkflowType,
	CdWorkflowRunnerId int,
	ByPassFeasibility bool,
	TriggeredFromRelease bool,
	isRollbackDeployment bool) bean.CdTriggerRequest {
	return bean.CdTriggerRequest{
		Pipeline:             pipeline,
		Artifact:             artifact,
		TriggeredBy:          triggeredBy,
		TriggerContext:       triggerContext,
		WorkflowType:         cdWorkflowType,
		CdWorkflowRunnerId:   CdWorkflowRunnerId,
		ByPassFeasibility:    ByPassFeasibility,
		TriggeredFromRelease: TriggeredFromRelease,
		IsRollbackDeployment: isRollbackDeployment,
	}
}

func GetTriggerContext(ctx context.Context) bean.TriggerContext {
	return bean.TriggerContext{
		Context: ctx,
	}
}

func NewUserDeploymentRequest(overrideRequest *apiBean.ValuesOverrideRequest, triggeredAt time.Time, triggeredBy int32) *eventProcessorBean.UserDeploymentRequest {
	return &eventProcessorBean.UserDeploymentRequest{
		ValuesOverrideRequest: overrideRequest,
		TriggeredAt:           triggeredAt,
		TriggeredBy:           triggeredBy,
	}
}

func NewAppIdentifierFromOverrideRequest(overrideRequest *apiBean.ValuesOverrideRequest) *helmBean.AppIdentifier {
	return &helmBean.AppIdentifier{
		ClusterId:   overrideRequest.ClusterId,
		Namespace:   overrideRequest.Namespace,
		ReleaseName: overrideRequest.ReleaseName,
	}
}

func NewValidateDeploymentTriggerObj(runner *pipelineConfig.CdWorkflowRunner, cdPipeline *pipelineConfig.Pipeline, imageDigest string,
	deploymentConfig *bean2.DeploymentConfig, userId int32, isRollbackDeployment bool) *bean.ValidateDeploymentTriggerObj {
	return &bean.ValidateDeploymentTriggerObj{
		Runner:               runner,
		CdPipeline:           cdPipeline,
		ImageDigest:          imageDigest,
		DeploymentConfig:     deploymentConfig,
		TriggeredBy:          userId,
		IsRollbackDeployment: isRollbackDeployment,
	}
}

func BuildFeasibilityCheckerResponse(feasibilityCheckerDto *bean.FeasibilityCheckerRequestDto, envName string, isVirtualEnv bool, feasibility error) *bean.FeasibilityCheckerResponse {
	resp := &bean.FeasibilityCheckerResponse{
		EnvName:              envName,
		Feasibility:          feasibility,
		IsVirtualEnvironment: isVirtualEnv,
	}
	if feasibilityCheckerDto != nil {
		resp.PipelineId = feasibilityCheckerDto.PipelineId
		resp.ArtifactId = feasibilityCheckerDto.ArtifactId
		resp.AppId = feasibilityCheckerDto.AppId
		resp.CdWorkflowType = feasibilityCheckerDto.CdWorkflowType
	}
	return resp
}

func BuildNewParentTriggerDetail(cdWorkflowRunnerId int, cdWorkflowId int, workflowType apiBean.WorkflowType) *bean.ParentTriggerDetail {
	return &bean.ParentTriggerDetail{
		CdWorkflowRunnerId: cdWorkflowRunnerId,
		CdWorkflowId:       cdWorkflowId,
		CdWorkflowType:     workflowType,
	}
}
