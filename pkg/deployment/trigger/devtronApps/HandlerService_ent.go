/*
 * Copyright (c) 2024. Devtron Inc.
 */

package devtronApps

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	bean3 "github.com/devtron-labs/devtron/api/bean"
	bean7 "github.com/devtron-labs/devtron/client/argocdServer/bean"
	bean8 "github.com/devtron-labs/devtron/enterprise/pkg/deploymentWindow"
	"github.com/devtron-labs/devtron/enterprise/pkg/resourceFilter"
	"github.com/devtron-labs/devtron/internal/constants"
	"github.com/devtron-labs/devtron/internal/sql/models"
	repository3 "github.com/devtron-labs/devtron/internal/sql/repository"
	appRepository "github.com/devtron-labs/devtron/internal/sql/repository/app"
	repository4 "github.com/devtron-labs/devtron/internal/sql/repository/dockerRegistry"
	repository6 "github.com/devtron-labs/devtron/internal/sql/repository/dockerRegistry"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig/bean/timelineStatus"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig/bean/workflow/cdWorkflow"
	"github.com/devtron-labs/devtron/internal/util"
	"github.com/devtron-labs/devtron/internal/util/helm"
	"github.com/devtron-labs/devtron/pkg/app"
	bean4 "github.com/devtron-labs/devtron/pkg/app/bean"
	userBean "github.com/devtron-labs/devtron/pkg/auth/user/bean"
	"github.com/devtron-labs/devtron/pkg/auth/userGroup/beans"
	bean2 "github.com/devtron-labs/devtron/pkg/bean"
	repository2 "github.com/devtron-labs/devtron/pkg/cluster/environment/repository"
	bean5 "github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/chartRef/bean"
	"github.com/devtron-labs/devtron/pkg/deployment/trigger/devtronApps/adapter"
	"github.com/devtron-labs/devtron/pkg/deployment/trigger/devtronApps/bean"
	constants2 "github.com/devtron-labs/devtron/pkg/deployment/trigger/devtronApps/constants"
	"github.com/devtron-labs/devtron/pkg/deployment/trigger/devtronApps/helper"
	adapter4 "github.com/devtron-labs/devtron/pkg/devtronResource/adapter"
	helper3 "github.com/devtron-labs/devtron/pkg/devtronResource/helper"
	bean11 "github.com/devtron-labs/devtron/pkg/eventProcessor/bean"
	bean6 "github.com/devtron-labs/devtron/pkg/pipeline/bean"
	"github.com/devtron-labs/devtron/pkg/pipeline/repository"
	approvalConfig2 "github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/bean"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/common/alpha1/adaptor"
	model2 "github.com/devtron-labs/devtron/pkg/policyGovernance/model"
	adapter2 "github.com/devtron-labs/devtron/pkg/remoteConnection/adapter"
	"github.com/devtron-labs/devtron/pkg/resourceQualifiers"
	"github.com/devtron-labs/devtron/pkg/sql"
	util4 "github.com/devtron-labs/devtron/pkg/util"
	adapter3 "github.com/devtron-labs/devtron/pkg/workflow/cd/adapter"
	bean10 "github.com/devtron-labs/devtron/pkg/workflow/cd/bean"
	util3 "github.com/devtron-labs/devtron/util"
	util2 "github.com/devtron-labs/devtron/util/event"
	"github.com/devtron-labs/devtron/util/expressionEvaluator"
	"github.com/ghodss/yaml"
	"github.com/go-pg/pg"
	"go.opentelemetry.io/otel"
	"helm.sh/helm/v3/pkg/chart"
	"io/ioutil"
	"net/http"
	"net/url"
	"path"
	"strconv"
	"strings"
	"time"
)

type HandlerService_ent interface {
	HandlerService
	CheckFeasibility(triggerRequirementRequest *bean.TriggerRequirementRequestDto, userMetadata *userBean.UserMetadata) (*bean.TriggerFeasibilityResponse, bool, bool, error)
	GetCdWorkflowRunnerWithEnvConfig(cdWorkflowType bean3.WorkflowType, pipeline *pipelineConfig.Pipeline, envNameSpace string, cdWorkflowId int, triggeredBy int32, triggeredTime time.Time) *bean10.CdWorkflowRunnerDto
	PushCRDChartForRelease(ctx context.Context, dockerRegistryId, repoName, chartVersion string, chartBytes []byte, triggeredBy int32) error
	CheckCdTriggerFeasibility(ctx context.Context, feasibilityCheckerDto []*bean.FeasibilityCheckerRequestDto, authorisedPipelineIdsMap map[int]bool,
		reDeployActiveImages bool, userMetadata *userBean.UserMetadata) ([]*bean.FeasibilityCheckerResponse, error)
}

func (impl *HandlerServiceImpl) handleBlockedTrigger(request bean.CdTriggerRequest, stage resourceFilter.ReferenceType) error {
	if request.TriggerContext.IsAutoTrigger() {
		go impl.writeBlockedTriggerEvent(request)
		err := impl.createAuditDataForDeploymentWindowBlock(request, stage)
		if err != nil {
			return fmt.Errorf("audit data entry for blocked trigger failed %v %v", request, err)
		}
	}
	return nil
}

func isDeploymentAllowed(triggerRequest bean.CdTriggerRequest, actionState bean8.UserActionState) bool {

	if triggerRequest.TriggerContext.IsAutoTrigger() {
		return actionState.IsActionAllowed()
	}
	return actionState.IsActionAllowedWithBypass()
}

// createAuditForFeasibility creates audit currrently in case of filters fail and error when nil
func (impl *HandlerServiceImpl) createAuditForFeasibility(createAudit bool, subjectId int, refType resourceFilter.ReferenceType, refId int, filters []*resourceFilter.FilterMetaDataBean, filterIdVsState map[int]expressionEvaluator.FilterState) *resourceFilter.ResourceFilterEvaluationAudit {
	// creating audit only when error occurred due to filters fail or err is nil because in other err cases filter audit is not required.
	if createAudit {
		// store evaluated result
		filterEvaluationAudit, err := impl.resourceFilterService.CreateFilterEvaluationAudit(resourceFilter.Artifact, subjectId, refType, refId, filters, filterIdVsState)
		if err != nil {
			impl.logger.Errorw("error in creating filter evaluation audit data cd post stage trigger", "err", err, "subjectId", subjectId, "refId", refId)
		}
		return filterEvaluationAudit
	}
	return nil
}

func (impl *HandlerServiceImpl) isArtifactDeploymentAllowed(pipeline *pipelineConfig.Pipeline, ciArtifact *repository3.CiArtifact, deployStage bean3.WorkflowType) (bool, error) {

	// fetch latest workflow to find current running artifact
	//TODO: optimize this query
	latestWf, err := impl.cdWorkflowRepository.FindArtifactByPipelineIdAndRunnerType(
		pipeline.Id,
		deployStage,
		"",
		1,
		[]string{bean7.Healthy, bean7.SUCCEEDED, bean7.Progressing})
	if err != nil && !errors.Is(err, pg.ErrNoRows) {
		impl.logger.Errorw("error in getting latest workflow by pipelineId", "pipelineId", pipeline.Id, "currentStageType", deployStage, "err", err)
		return false, err
	}
	if len(latestWf) > 0 {
		currentRunningArtifact := latestWf[0].CdWorkflow.CiArtifact
		if currentRunningArtifact.Id == ciArtifact.Id {
			return true, nil
		}
	}

	parentId, parentType, _, err := impl.cdPipelineConfigService.ExtractParentMetaDataByPipeline(pipeline, deployStage)
	if err != nil {
		impl.logger.Errorw("error in getting parent details for cd pipeline id", "cdPipelineId", pipeline.Id, "err", err)
		return false, err
	}
	if parentType == bean3.CI_WORKFLOW_TYPE {
		// artifact can be created at post-ci as well
		if parentId == ciArtifact.PipelineId || (ciArtifact.DataSource == repository3.POST_CI && ciArtifact.ComponentId == parentId) {
			return true, nil
		}
	} else if parentType == bean3.WEBHOOK_WORKFLOW_TYPE {
		if parentId == ciArtifact.ExternalCiPipelineId {
			return true, nil
		}
	} else {
		var pluginStage string
		if parentType == bean6.WorkflowTypePre {
			pluginStage = repository3.PRE_CD
		} else if parentType == bean6.WorkflowTypePost {
			pluginStage = repository3.POST_CD
		}
		artifactAvailable, err := impl.ciArtifactRepository.IsArtifactAvailableForDeployment(pipeline.Id, parentId, ciArtifact, string(parentType), pluginStage, string(deployStage))
		if err != nil {
			impl.logger.Errorw("error in getting if artifact is available for deployment or not ", "pipelineId", pipeline.Id, "parentId", parentId, "parentType", string(parentType), "ciArtifactId", ciArtifact.Id, "err", err)
			return false, err
		}
		return artifactAvailable, nil
	}
	return false, nil

}

// checkFeasibilityAndCreateAudit first checks feasibility and creates audit if createAudit flag comes to true
func (impl *HandlerServiceImpl) checkFeasibilityAndCreateAudit(triggerRequirementRequest *bean.TriggerRequirementRequestDto, subjectId int, refType resourceFilter.ReferenceType, refId int, userMetadata *userBean.UserMetadata) (*bean.TriggerFeasibilityResponse, *resourceFilter.ResourceFilterEvaluationAudit, error) {
	var filters []*resourceFilter.FilterMetaDataBean
	var filterIdVsState map[int]expressionEvaluator.FilterState
	feasibilityResponse, createAudit, _, err := impl.CheckFeasibility(triggerRequirementRequest, userMetadata)
	if feasibilityResponse != nil {
		filterIdVsState, filters = feasibilityResponse.FilterIdVsState, feasibilityResponse.Filters
	}
	// this operation is independent of the error occured
	filterEvaluationAudit := impl.createAuditForFeasibility(createAudit, subjectId, refType, refId, filters, filterIdVsState)
	// checking from parentTriggerDetails if this has been triggered from release if yes, will consider auto case as well triggered from release,
	// task run will be created with new cd workflow runner.
	// currently it is being put here due to imports but in future there should be a taskTriggerService which should own this logic and
	// other trigger operations and talk to handler service, and handler service should not be exposed to anyone.
	if helper.ShouldCheckParentTriggeredFromRelease(triggerRequirementRequest.TriggerRequest.ParentTriggerDetail) {
		// check if triggered from release
		createdCdWfrId, err := impl.handleAutoReleaseTriggeredOperations(triggerRequirementRequest)
		if err != nil {
			impl.logger.Errorw("error in handling auto release triggered operations", "triggerRequirementRequest", triggerRequirementRequest, "err", err)
			return nil, nil, err
		}
		triggerRequirementRequest.TriggerRequest.SetCdWorkflowRunnerId(createdCdWfrId)
		if feasibilityResponse != nil {
			feasibilityResponse.TriggerRequest.SetCdWorkflowRunnerId(createdCdWfrId)
		}
	}
	// handling error from CheckFeasibility
	if err != nil {
		impl.logger.Errorw("error encountered in checkFeasibilityAndCreateAudit", "err", err, "triggerRequirementRequest", triggerRequirementRequest)
		return nil, nil, err
	}
	return feasibilityResponse, filterEvaluationAudit, nil
}

func (impl *HandlerServiceImpl) handleAutoReleaseTriggeredOperations(triggerRequirementRequest *bean.TriggerRequirementRequestDto) (int, error) {
	triggered, parentTaskRun, err := impl.taskRunTriggerService.CheckIfTriggeredFromRelease(triggerRequirementRequest.TriggerRequest.ParentTriggerDetail.CdWorkflowRunnerId, triggerRequirementRequest.TriggerRequest.ParentTriggerDetail.CdWorkflowType)
	if err != nil {
		impl.logger.Errorw("error in getting triggered from release", "parentTriggerDetail", triggerRequirementRequest.TriggerRequest.ParentTriggerDetail, "err", err)
		return 0, err
	}
	// if no do nothing
	if triggered {
		tx, err := impl.cdWorkflowRepository.StartTx()
		if err != nil {
			impl.logger.Errorw("error in starting transaction", "err", err)
			return 0, err
		}
		defer impl.cdWorkflowRepository.RollbackTx(tx)
		// if yes , create wf runner and create taskRun for release
		cdWf := triggerRequirementRequest.TriggerRequest.CdWf
		if cdWf == nil {
			cdWf = &pipelineConfig.CdWorkflow{
				CiArtifactId: triggerRequirementRequest.TriggerRequest.Artifact.Id,
				PipelineId:   triggerRequirementRequest.TriggerRequest.Pipeline.Id,
				AuditLog:     sql.AuditLog{CreatedOn: triggerRequirementRequest.TriggerRequest.TriggeredTime, CreatedBy: triggerRequirementRequest.TriggerRequest.TriggeredBy, UpdatedOn: triggerRequirementRequest.TriggerRequest.TriggeredTime, UpdatedBy: triggerRequirementRequest.TriggerRequest.TriggeredBy},
			}
			err := impl.cdWorkflowRepository.SaveWorkFlow(context.Background(), cdWf)
			if err != nil {
				impl.logger.Errorw("error in saving cd workflow", "err", err)
				return 0, err
			}
		}
		cdWorkflowRunnerDto := impl.GetCdWorkflowRunnerWithEnvConfig(triggerRequirementRequest.TriggerRequest.WorkflowType, triggerRequirementRequest.TriggerRequest.Pipeline,
			triggerRequirementRequest.TriggerRequest.RunStageInEnvNamespace, cdWf.Id,
			triggerRequirementRequest.TriggerRequest.TriggeredBy, triggerRequirementRequest.TriggerRequest.TriggeredTime)
		mapOfCdWfIdVsCdWfrId, err := impl.cdWorkflowRunnerService.CreateBulkCdWorkflowRunners(tx, []*bean10.CdWorkflowRunnerDto{cdWorkflowRunnerDto})
		if err != nil {
			impl.logger.Errorw("error in saving cd workflow runner", "cdWorkflowRunnerDto", cdWorkflowRunnerDto, "err", err)
			return 0, err
		}
		cdWfrId := mapOfCdWfIdVsCdWfrId[cdWf.Id]
		// set created cdWfrIf any in feasibilityResponse
		taskRunCreationReq := adapter4.BuildAutoTaskRunCreationReqBean(cdWfrId, triggerRequirementRequest.TriggerRequest.Pipeline.Id, triggerRequirementRequest.TriggerRequest.Pipeline.EnvironmentId, triggerRequirementRequest.TriggerRequest.WorkflowType, triggerRequirementRequest.TriggerRequest.TriggeredBy, triggerRequirementRequest.TriggerRequest.TriggeredTime)
		err = impl.taskRunTriggerService.CreateTaskRunObjForAutoDeployment(tx, parentTaskRun, taskRunCreationReq)
		if err != nil {
			impl.logger.Errorw("error in creating task run obj for auto deployment", "parentTaskRun", parentTaskRun, "err", err)
			return 0, err
		}
		err = impl.cdWorkflowRepository.CommitTx(tx)
		if err != nil {
			impl.logger.Errorw("error in commiting transaction in handleAutoReleaseTriggeredOperations", "err", err)
			return 0, err
		}
		return cdWfrId, nil
	}
	return 0, nil
}

// checkFeasibilityAndAuditStateChanges performs the feasibility and required operation before actual deployment for both auto and manual deploy
// step1:checks feasibility if any policy is blocking the deployment including exception users on approval policies
// step2: Creates filter evaluation audit irrespective of err in filter and deployment window block
// step3: gets or create cd workflow basis of auto or manual deployment
// step4: creates cdWorkflowRunner
// step5: createAuditDataForDeploymentWindowBypass
// step6: UpdateFilterEvaluationAuditRef if applicable
// step7: ConsumeApprovalRequest if applicable
func (impl *HandlerServiceImpl) checkFeasibilityAndAuditStateChanges(triggerOperationReq *bean.TriggerOperationDto, deploymentType models.DeploymentType, userMetadata *userBean.UserMetadata) (runner *pipelineConfig.CdWorkflowRunner, cdWfId int, triggerMessage string, err error) {
	cdPipeline := triggerOperationReq.TriggerRequest.Pipeline
	ciArtifactId := triggerOperationReq.TriggerRequest.Artifact.Id
	triggeredBy := triggerOperationReq.TriggerRequest.TriggeredBy
	triggerRequirementRequest := adapter.GetTriggerRequirementRequest(triggerOperationReq.Scope, triggerOperationReq.TriggerRequest, resourceFilter.Deploy, deploymentType)
	feasibilityResponse, filterEvaluationAudit, err := impl.checkFeasibilityAndCreateAudit(triggerRequirementRequest, ciArtifactId, resourceFilter.Pipeline, cdPipeline.Id, userMetadata)
	if err != nil {
		impl.logger.Errorw("error encountered in performOperationsForAutoOrManualTrigger", "err", err, "triggerRequirementRequest", triggerRequirementRequest)
		return nil, 0, "", err
	}
	//overriding the request from feasibility response
	triggerOperationReq.TriggerRequest = feasibilityResponse.TriggerRequest

	var cdWorkflowId int
	runnerIdFromRequest := triggerOperationReq.TriggerRequest.CdWorkflowRunnerId
	imageApprovalRequestData := feasibilityResponse.ImageApprovedActionData
	if runnerIdFromRequest == 0 {
		switch triggerOperationReq.TriggerRequest.TriggerContext.TriggerType {
		case bean.Manual:
			{
				cdWf, err := impl.cdWorkflowRepository.FindByWorkflowIdAndRunnerType(triggerOperationReq.TriggerRequest.TriggerContext.Context, triggerOperationReq.OverrideCdWrfId, bean3.CD_WORKFLOW_TYPE_PRE)
				if err != nil && !util.IsErrNoRows(err) {
					impl.logger.Errorw("error in getting cdWorkflow, ManualCdTrigger", "CdWorkflowId", triggerOperationReq.OverrideCdWrfId, "err", err)
					return nil, 0, "", err
				}

				cdWorkflowId = cdWf.CdWorkflowId
				if cdWorkflowId == 0 {
					cdWf := &pipelineConfig.CdWorkflow{
						CiArtifactId: ciArtifactId,
						PipelineId:   triggerOperationReq.PipelineId,
						AuditLog:     sql.AuditLog{CreatedOn: triggerOperationReq.TriggeredAt, CreatedBy: triggeredBy, UpdatedOn: triggerOperationReq.TriggeredAt, UpdatedBy: triggeredBy},
					}
					err := impl.cdWorkflowRepository.SaveWorkFlow(triggerOperationReq.TriggerRequest.TriggerContext.Context, cdWf)
					if err != nil {
						impl.logger.Errorw("error in creating cdWorkflow, ManualCdTrigger", "PipelineId", triggerOperationReq.PipelineId, "err", err)
						return nil, 0, "", err
					}
					cdWorkflowId = cdWf.Id
				}
			}
		case bean.Automatic:
			{
				cdWf := triggerOperationReq.TriggerRequest.CdWf

				if cdWf == nil || (cdWf != nil && cdWf.CiArtifactId != ciArtifactId) {
					// cdWf != nil && cdWf.CiArtifactId != artifact.Id for auto trigger case when deployment is triggered with image generated by plugin
					cdWf = &pipelineConfig.CdWorkflow{
						CiArtifactId: ciArtifactId,
						PipelineId:   triggerOperationReq.PipelineId,
						AuditLog:     sql.AuditLog{CreatedOn: triggerOperationReq.TriggeredAt, CreatedBy: 1, UpdatedOn: triggerOperationReq.TriggeredAt, UpdatedBy: 1},
					}
					err := impl.cdWorkflowRepository.SaveWorkFlow(context.Background(), cdWf)
					if err != nil {
						impl.logger.Errorw("error encountered in  performOperationsForAutoOrManualTrigger", "ciArtifactId", ciArtifactId, "err", err)
						return nil, 0, "", err
					}
				}
				cdWorkflowId = cdWf.Id
			}

		}
		runner = &pipelineConfig.CdWorkflowRunner{
			Name:            cdPipeline.Name,
			WorkflowType:    bean3.CD_WORKFLOW_TYPE_DEPLOY,
			ExecutorType:    triggerOperationReq.ExecutorType,
			Status:          cdWorkflow.WorkflowInitiated,
			TriggeredBy:     triggeredBy,
			StartedOn:       triggerOperationReq.TriggeredAt,
			Namespace:       impl.config.GetDefaultNamespace(),
			CdWorkflowId:    cdWorkflowId,
			AuditLog:        sql.AuditLog{CreatedOn: triggerOperationReq.TriggeredAt, CreatedBy: triggeredBy, UpdatedOn: triggerOperationReq.TriggeredAt, UpdatedBy: triggeredBy},
			ReferenceId:     triggerOperationReq.TriggerRequest.TriggerContext.ReferenceId,
			TriggerMetadata: triggerOperationReq.TriggerRequest.TriggerMessage,
		}
		if imageApprovalRequestData != nil && imageApprovalRequestData.DeploymentApprovalRequestId > 0 {
			runner.DeploymentApprovalRequestId = imageApprovalRequestData.DeploymentApprovalRequestId
		}
		imageState := impl.determineImageState(feasibilityResponse)
		if imageState.IsValid() {
			runner.ImageState = imageState
		}

		err = impl.cdWorkflowRunnerService.SaveWfr(nil, runner)
		if err != nil {
			impl.logger.Errorw("err in creating cdWorkflowRunner, performOperationsForAutoOrManualTrigger", "cdWorkflowId", cdWorkflowId, "err", err)
			return nil, 0, "", err
		}

	} else {
		runner, err = impl.cdWorkflowRepository.FindBasicWorkflowRunnerById(runnerIdFromRequest)
		if err != nil {
			impl.logger.Errorw("err in FindWorkflowRunnerById, performOperationsForAutoOrManualTrigger", "runnerIdFromRequest", runnerIdFromRequest, "err", err)
			return nil, 0, "", err
		}
		imageState := impl.determineImageState(feasibilityResponse)
		if imageState.IsValid() {
			runner.ImageState = imageState
		}
		if len(imageState) > 0 {
			err = impl.cdWorkflowRepository.UpdateWorkFlowRunnerWithImageState(runnerIdFromRequest, imageState)
			if err != nil {
				impl.logger.Errorw("err in  UpdateWorkFlowRunnerWithDeploymentApprovalReqId", "runnerIdFromRequest", runnerIdFromRequest, "err", err)
				return nil, 0, "", err
			}
		}
		if imageApprovalRequestData != nil && imageApprovalRequestData.DeploymentApprovalRequestId > 0 {
			runner.DeploymentApprovalRequestId = imageApprovalRequestData.DeploymentApprovalRequestId
			err = impl.cdWorkflowRepository.UpdateWorkFlowRunnerWithDeploymentApprovalReqId(runnerIdFromRequest, imageApprovalRequestData.DeploymentApprovalRequestId)
			if err != nil {
				impl.logger.Errorw("err in  UpdateWorkFlowRunnerWithDeploymentApprovalReqId", "runnerIdFromRequest", runnerIdFromRequest, "err", err)
				return nil, 0, "", err
			}
		}
		cdWorkflowId = runner.CdWorkflowId
	}

	impl.createAuditDataForDeploymentWindowBypass(triggerOperationReq.TriggerRequest, runner.Id)
	if filterEvaluationAudit != nil {
		// update resource_filter_evaluation entry with wfrId and type
		err = impl.resourceFilterService.UpdateFilterEvaluationAuditRef(filterEvaluationAudit.Id, resourceFilter.CdWorkflowRunner, runner.Id)
		if err != nil {
			impl.logger.Errorw("error in updating filter evaluation audit reference", "filterEvaluationAuditId", filterEvaluationAudit.Id, "err", err)
			return nil, 0, "", err
		}
	}
	if imageApprovalRequestData != nil && imageApprovalRequestData.IsArtifactApproved {
		err = impl.deploymentApprovalRepository.ConsumeApprovalRequest(imageApprovalRequestData.DeploymentApprovalRequestId)
		if err != nil {
			return nil, 0, "", err
		}
	}
	runner.CdWorkflow = &pipelineConfig.CdWorkflow{
		Pipeline: cdPipeline,
	}

	// save filterEvaluation audit here for image approvalConfig
	if imageApprovalRequestData != nil {
		approvalRequestAuditDataBytes, err := json.Marshal(imageApprovalRequestData)
		if err != nil {
			impl.logger.Errorw("error in marshaling the approvalRequestAuditData", "approvalRequestData", imageApprovalRequestData, "err", err)
		}
		_, err = impl.resourceFilterAuditService.CreateFilterEvaluationAuditCustom(resourceFilter.DeploymentApprovalRequest, imageApprovalRequestData.DeploymentApprovalRequestId, resourceFilter.CdWorkflowRunner, runner.Id, string(approvalRequestAuditDataBytes), resourceFilter.IMAGE_APPROVAL_POLICY)
		if err != nil {
			impl.logger.Errorw("error in creating the approvalRequestAuditData", "approvalRequestData", imageApprovalRequestData, "err", err)
		}
	}
	return runner, cdWorkflowId, triggerOperationReq.TriggerRequest.TriggerMessage, nil
}

func (impl *HandlerServiceImpl) createAuditDataForDeploymentWindowBlock(request bean.CdTriggerRequest, stage resourceFilter.ReferenceType) error {
	_, err := impl.resourceFilterAuditService.CreateFilterEvaluationAuditCustom(resourceFilter.Artifact, request.Artifact.Id, stage, request.Pipeline.Id, request.DeploymentWindowState.GetSerializedAuditData(request.TriggerContext.ToTriggerTypeString(), request.TriggerMessage), resourceFilter.DEPLOYMENT_WINDOW)
	if err != nil {
		return err
	}
	return nil
}

// determineImageState determines image state based on the approval policy and current image approval status
func (impl *HandlerServiceImpl) determineImageState(feasibilityResponse *bean.TriggerFeasibilityResponse) constants2.ImageStateWhileDeployment {
	imageApprovalRequestData := feasibilityResponse.ImageApprovedActionData
	if feasibilityResponse.IsApprovalPolicyConfiguredForPipeline {
		if imageApprovalRequestData != nil && imageApprovalRequestData.DeploymentApprovalRequestId > 0 {
			// sets DeploymentApprovalRequestId when an approval context exists in the deployment_approval_request table.
			// this occurs in two scenarios:
			// 1. the image has received required approvals and is ready for deployment
			// 2. the image approval request is pending and awaiting approver action

			// if artifact is not approved yet and deployment is done by exception user then mark artifact state as bypassed
			if !imageApprovalRequestData.IsArtifactApproved {
				return constants2.ImageStateBypassed
			}
		} else {
			//this means that approval policy is configured and the artifact is not yet sent for approval
			return constants2.ImageStateBypassed
		}
	}
	// in case approved image is triggered or image without approval policy is triggered then imageState is empty for backward compatibility
	return ""
}

func (impl *HandlerServiceImpl) createAuditDataForDeploymentWindowBypass(request bean.CdTriggerRequest, wfrId int) error {
	if request.TriggerMessage == "" || request.TriggerContext.IsAutoTrigger() {
		return nil
	}
	_, err := impl.resourceFilterAuditService.CreateFilterEvaluationAuditCustom(resourceFilter.Artifact, request.Artifact.Id, resourceFilter.CdWorkflowRunner, wfrId, request.DeploymentWindowState.GetSerializedAuditData(request.TriggerContext.ToTriggerTypeString(), request.TriggerMessage), resourceFilter.DEPLOYMENT_WINDOW)
	if err != nil {
		return err
	}
	return nil
}

func (impl *HandlerServiceImpl) getHelmGeneratedManifest(triggerEvent bean.TriggerEvent, overrideRequest *bean3.ValuesOverrideRequest,
	valuesOverrideResponse *app.ValuesOverrideResponse, builtChartPath string, ctx context.Context) (helmManifest []byte, err error) {
	newCtx, span := otel.Tracer("orchestrator").Start(ctx, "HandlerServiceImpl.getHelmGeneratedManifest")
	defer span.End()
	timeline := &pipelineConfig.PipelineStatusTimeline{
		CdWorkflowRunnerId: overrideRequest.WfrId,
		Status:             timelineStatus.TIMELINE_STATUS_MANIFEST_GENERATED,
		StatusDetail:       "Helm package generated successfully.",
		StatusTime:         time.Now(),
	}
	timeline.CreateAuditLog(overrideRequest.UserId)
	// TODO: refactor tracer
	_, span = otel.Tracer("orchestrator").Start(newCtx, "PipelineStatusTimelineServiceImpl.SaveTimeline")
	_, err = impl.pipelineStatusTimelineService.SaveTimelineIfNotAlreadyPresent(timeline, nil)
	if err != nil {
		impl.logger.Errorw("error in saving timeline for manifest_download type")
	}
	span.End()
	err = impl.mergeDefaultValuesWithOverrideValues(newCtx, valuesOverrideResponse.MergedValues, builtChartPath)
	if err != nil {
		impl.logger.Errorw("error in merging default values with override values ", "err", err)
		return helmManifest, err
	}
	// for downloaded manifest name is equal to <app-name>-<env-name>-<image-tag>
	image := valuesOverrideResponse.Artifact.Image
	var imageTag string
	if len(image) > 0 {
		imageTag = util4.GetImageTagFromImage(image)
	}
	chartName := helm.BuildHelmChartNameForDevtronApps(overrideRequest.AppName, overrideRequest.EnvName, imageTag, bean3.CD_WORKFLOW_TYPE_DEPLOY)
	// As this chart will be pushed, don't delete it now
	deleteChart := !triggerEvent.PerformChartPush
	helmManifest, err = impl.chartTemplateService.LoadChartInBytes(builtChartPath, deleteChart, chartName, valuesOverrideResponse.EnvOverride.Chart.ChartVersion)
	if err != nil {
		impl.logger.Errorw("error in converting chart to bytes", "err", err)
		return helmManifest, err
	}
	return helmManifest, err
}

// getAcdAppGitOpsRepoName returns the GitOps repository name, configured for the argoCd app
func (impl *HandlerServiceImpl) getAcdAppGitOpsRepoName(appName string, environmentName string) (string, error) {
	// this method should only call in case of argo-integration and gitops configured
	ctx := context.Background()
	acdAppName := util3.BuildDeployedAppName(appName, environmentName)
	return impl.argoClientWrapperService.GetGitOpsRepoNameForApplication(ctx, acdAppName)
}

func (impl *HandlerServiceImpl) sendResourceScanEvent(overrideRequest *bean3.ValuesOverrideRequest, valuesOverrideResponse *app.ValuesOverrideResponse, referenceChartByte []byte, err error) {
	pipeline := valuesOverrideResponse.Pipeline
	envOverride := valuesOverrideResponse.EnvOverride
	if envOverride.Chart != nil && len(pipeline.App.AppName) > 0 {
		if len(referenceChartByte) == 0 {
			chartMetaData := &chart.Metadata{
				Name:    pipeline.App.AppName,
				Version: envOverride.Chart.ChartVersion,
			}
			referenceTemplatePath := path.Join(bean5.RefChartDirPath, envOverride.Chart.ReferenceTemplate)
			refChartByte, err := impl.chartTemplateService.GetByteArrayRefChart(chartMetaData, referenceTemplatePath)
			if err != nil {
				impl.logger.Errorw("sendResourceScanEvent ref chart commit error on cd trigger", "err", err, "req", overrideRequest)
				return
			}
			referenceChartByte = refChartByte
		}
		chartScanEventBean := bean11.ChartScanEventBean{
			DevtronAppDto: &bean11.DevtronAppDto{
				ChartContent: referenceChartByte,
				ValuesYaml:   valuesOverrideResponse.MergedValues,
				ChartName:    envOverride.Chart.ChartName,
				ChartVersion: envOverride.Chart.ChartVersion,
				CdWorkflowId: overrideRequest.CdWorkflowId,
			},
		}
		err = impl.chartScanPublishService.PublishChartScanEventForDevtronApps(chartScanEventBean)
		if err != nil {
			impl.logger.Errorw("error occurred while publishing scan event", "err", err)
		}
	}
	return
}

func (impl *HandlerServiceImpl) writeBlockedTriggerEvent(request bean.CdTriggerRequest) {
	event, err := impl.eventFactory.Build(util2.Blocked, &request.Pipeline.Id, request.Pipeline.AppId, &request.Pipeline.EnvironmentId, util2.CD)
	if err != nil {
		impl.logger.Errorw("error in building blocked trigger event", "pipelineId", request.Pipeline.Id, "err", err)
	}
	impl.logger.Debugw("event writeBlockedTriggerEvent", "event", event)
	event.UserId = int(request.TriggeredBy)
	event = impl.eventFactory.BuildExtraBlockedTriggerData(event, bean3.CD_WORKFLOW_TYPE_DEPLOY, request.TriggerMessage, request.Artifact)
	_, evtErr := impl.eventClient.WriteNotificationEvent(event)
	if evtErr != nil {
		impl.logger.Errorw("CD trigger event not sent", "error", evtErr)
	}
}

// TO check where to put, got from oss enterprise diff

func (impl *HandlerServiceImpl) PushPrePostCDManifest(cdWorklowRunnerId int, triggeredBy int32, jobHelmPackagePath string, deployType string, pipeline *pipelineConfig.Pipeline, imageTag string, ctx context.Context) (*bean4.ManifestPushTemplate, error) {

	manifestPushTemplate, err := impl.BuildManifestPushTemplateForPrePostCd(pipeline, cdWorklowRunnerId, triggeredBy, jobHelmPackagePath, deployType, imageTag)
	if err != nil {
		impl.logger.Errorw("error in building manifest push template for pre post cd")
		return nil, err
	}
	manifestPushService := impl.getManifestPushService(manifestPushTemplate.StorageType)

	manifestPushResponse := manifestPushService.PushChart(ctx, manifestPushTemplate)
	if manifestPushResponse.Error != nil {
		impl.logger.Errorw("error in pushing chart to helm repo", "err", err)
		return nil, manifestPushResponse.Error
	}
	return nil, nil
}

func (impl *HandlerServiceImpl) mergeDefaultValuesWithOverrideValues(ctx context.Context, overrideValues string, builtChartPath string) error {
	_, span := otel.Tracer("orchestrator").Start(ctx, "HandlerServiceImpl.mergeDefaultValuesWithOverrideValues")
	defer span.End()
	valuesFilePath := path.Join(builtChartPath, "values.yaml") // default values of helm chart
	defaultValues, err := ioutil.ReadFile(valuesFilePath)
	if err != nil {
		return err
	}
	defaultValuesJson, err := yaml.YAMLToJSON(defaultValues)
	if err != nil {
		return err
	}
	mergedValues, err := impl.mergeUtil.JsonPatch(defaultValuesJson, []byte(overrideValues))
	if err != nil {
		return err
	}
	mergedValuesYaml, err := yaml.JSONToYAML(mergedValues)
	if err != nil {
		return err
	}
	err = ioutil.WriteFile(valuesFilePath, mergedValuesYaml, 0600)
	if err != nil {
		return err
	}
	return err
}

func (impl *HandlerServiceImpl) BuildManifestPushTemplateForPrePostCd(pipeline *pipelineConfig.Pipeline, cdWorklowRunnerId int, triggeredBy int32, jobHelmPackagePath string, deployType string, imageTag string) (*bean4.ManifestPushTemplate, error) {

	manifestPushTemplate := &bean4.ManifestPushTemplate{
		WorkflowRunnerId:    cdWorklowRunnerId,
		AppId:               pipeline.AppId,
		EnvironmentId:       pipeline.EnvironmentId,
		UserId:              triggeredBy,
		AppName:             pipeline.App.AppName,
		TargetEnvironmentId: pipeline.Environment.Id,
		ChartVersion:        fmt.Sprintf("%d.%d.%d-%s-%s", 1, 1, cdWorklowRunnerId, deployType, imageTag),
	}

	manifestPushConfig, err := impl.manifestPushConfigRepository.GetManifestPushConfigByAppIdAndEnvId(pipeline.AppId, pipeline.EnvironmentId)
	if err != nil && err != pg.ErrNoRows {
		impl.logger.Errorw("error in fetching manifest push config for pre/post cd", "err", err)
		return manifestPushTemplate, err
	}
	manifestPushTemplate.StorageType = manifestPushConfig.StorageType

	if manifestPushConfig != nil && manifestPushConfig.StorageType == bean2.ManifestStorageOCIHelmRepo {

		var credentialsConfig bean4.HelmRepositoryConfig
		err = json.Unmarshal([]byte(manifestPushConfig.CredentialsConfig), &credentialsConfig)
		if err != nil {
			impl.logger.Errorw("error in json unmarshal", "err", err)
			return manifestPushTemplate, err
		}
		dockerArtifactStore, err := impl.dockerArtifactStoreRepository.FindOne(credentialsConfig.ContainerRegistryName)
		if err != nil {
			impl.logger.Errorw("error in fetching artifact info", "err", err)
			return manifestPushTemplate, err
		}
		repoPath, chartName := app.GetRepoPathAndChartNameFromRepoName(credentialsConfig.RepositoryName)
		manifestPushTemplate.RepoUrl = path.Join(dockerArtifactStore.RegistryURL, repoPath)
		manifestPushTemplate.ChartName = chartName
		chartBytes, err := impl.chartTemplateService.LoadChartInBytes(jobHelmPackagePath, true, chartName, manifestPushTemplate.ChartVersion)
		if err != nil {
			return manifestPushTemplate, err
		}
		manifestPushTemplate.BuiltChartBytes = &chartBytes
		containerRegistryConfig := &bean4.ContainerRegistryConfig{
			CredentialsType:            dockerArtifactStore.CredentialsType,
			RegistryUrl:                dockerArtifactStore.RegistryURL,
			Username:                   dockerArtifactStore.Username,
			Password:                   dockerArtifactStore.Password,
			Insecure:                   true,
			AwsRegion:                  dockerArtifactStore.AWSRegion,
			AccessKey:                  dockerArtifactStore.AWSAccessKeyId,
			SecretKey:                  dockerArtifactStore.AWSSecretAccessKey,
			RegistryType:               string(dockerArtifactStore.RegistryType),
			IsPublic:                   false,
			RepoName:                   repoPath,
			Connection:                 dockerArtifactStore.Connection,
			Certificate:                dockerArtifactStore.Cert,
			RegistryName:               dockerArtifactStore.Id,
			RemoteConnectionConfigBean: adapter2.GetRemoteConnectionConfigBean(dockerArtifactStore.RemoteConnectionConfig),
		}
		for _, ociRegistryConfig := range dockerArtifactStore.OCIRegistryConfig {
			if ociRegistryConfig.RepositoryType == repository6.OCI_REGISRTY_REPO_TYPE_CHART {
				containerRegistryConfig.IsPublic = ociRegistryConfig.IsPublic
			}
		}
		manifestPushTemplate.ContainerRegistryConfig = containerRegistryConfig
	} else if manifestPushConfig.StorageType == bean2.ManifestStorageGit {
		// need to implement for git repo push
	}

	return manifestPushTemplate, nil
}

func (impl *HandlerServiceImpl) checkApprovalNodeForDeployment(requestedUserId int32, pipeline *pipelineConfig.Pipeline, artifactId int,
	userMetadata *userBean.UserMetadata, isManualTrigger bool) (*bean.ImageApprovedActionData, bool, error) {
	var imageApprovedActionData *bean.ImageApprovedActionData
	var isApprovalPolicyConfiguredForPipeline bool
	approvalConfigurationMap, err := impl.approvalConfigurationEnforcementService.GetConfigurationsByAppAndEnvId(model2.APPLY_POLICY_APPROVAL_DEPLOYMENT, pipeline.AppId, pipeline.EnvironmentId)
	if err != nil {
		impl.logger.Error("error in getting the approval config info by app and env ids", "appId", pipeline.AppId, "envId", pipeline.EnvironmentId, "err", err)
		return nil, false, err
	}
	approvalConfiguration := approvalConfigurationMap[approvalConfig2.APPROVAL_FOR_DEPLOYMENT]
	if approvalConfiguration != nil {
		isApprovalPolicyConfiguredForPipeline = true
		var toBypassApprovalCheckForExceptionUser bool
		isUserException, err := impl.globalPolicyDataManager.IsUserAnExceptionByRqmResourceType(nil, adaptor.ApplyPolicyTypeToRqmResourceType(model2.APPLY_POLICY_APPROVAL_DEPLOYMENT).ToInt(), userMetadata)
		if err != nil {
			impl.logger.Errorw("error in checking if a user is exception user or not by policy type ", "policyType", model2.APPLY_POLICY_APPROVAL_DEPLOYMENT, "err", err)
			return nil, isApprovalPolicyConfiguredForPipeline, err
		}
		// exception users are currently permitted to manually trigger blocked deployments, automatic triggers will continue to follow the existing behavior.
		toBypassApprovalCheckForExceptionUser = isUserException && isManualTrigger
		pipelineId := pipeline.Id
		userApprovalMetadataRequest, err := impl.artifactApprovalDataReadService.PrepareApprovalMetadataRequest(util3.NewRequestCtx(context.Background()), pipeline.Id)
		if err != nil {
			impl.logger.Errorw("error in getting user approval metadata", "pipelineId", pipeline.Id, "err", err)
			return nil, isApprovalPolicyConfiguredForPipeline, err
		}
		userApprovalMetadata, err := impl.artifactApprovalDataReadService.FetchApprovalDataForArtifacts(util3.NewRequestCtx(context.Background()), userApprovalMetadataRequest, []int{artifactId}, pipelineId, approvalConfiguration)
		if err != nil {
			return nil, isApprovalPolicyConfiguredForPipeline, err
		}
		approvalMetadata, ok := userApprovalMetadata[artifactId]
		if !toBypassApprovalCheckForExceptionUser {
			if !ok {
				return nil, isApprovalPolicyConfiguredForPipeline, &util.ApiError{
					HttpStatusCode:  http.StatusBadRequest,
					Code:            constants.ApprovalNodeFail,
					InternalMessage: "request not raised for artifact",
					UserMessage:     "request not raised for artifact",
				}
			}
			// if the artifact is not approved, return an error for non-exception users.
			if approvalMetadata != nil && approvalMetadata.ApprovalRuntimeState != approvalConfig2.ApprovedApprovalState {
				impl.logger.Errorw("not triggering deployment since artifact is not approved", "pipelineId", pipelineId, "artifactId", artifactId)
				return nil, isApprovalPolicyConfiguredForPipeline, &util.ApiError{
					HttpStatusCode:  http.StatusBadRequest,
					Code:            constants.ApprovalNodeFail,
					InternalMessage: "not triggering deployment since artifact is not approved",
					UserMessage:     "not triggering deployment since artifact is not approved",
				}
			}
			// if the user is marked as an exception, approval restrictions (such as whether the approver can deploy) do not apply.
			if !impl.config.CanApproverDeploy {
				for _, approvalData := range approvalMetadata.ApprovalUsersData {
					if approvalData.UserId == requestedUserId {
						return nil, isApprovalPolicyConfiguredForPipeline, &util.ApiError{
							HttpStatusCode:  http.StatusBadRequest,
							Code:            constants.ApprovalNodeFail,
							InternalMessage: "image cannot be deployed by its approver",
							UserMessage:     "image cannot be deployed by its approver",
						}
					}
				}
			}
		}

		imageApprovedActionData = &bean.ImageApprovedActionData{
			ApprovalConfig: approvalConfiguration,
		}
		// Build userId -> userGroup mapping
		userIdVsUserGroupMappings := make(map[int32][]*beans.UserGroupDTO)
		if approvalMetadata != nil {
			for _, approvedUser := range approvalMetadata.ApprovalUsersData {
				userIdVsUserGroupMappings[approvedUser.UserId] = approvedUser.UserGroups
			}
			imageApprovedActionData.UserGroups = userIdVsUserGroupMappings
			imageApprovedActionData.DeploymentApprovalRequestId = approvalMetadata.ApprovalRequestId
			imageApprovedActionData.IsArtifactApproved = approvalMetadata.ApprovalRuntimeState == approvalConfig2.ApprovedApprovalState
		}

	}
	return imageApprovedActionData, isApprovalPolicyConfiguredForPipeline, nil

}

func (impl *HandlerServiceImpl) getWorkflowExecutorTypeForPrePostOrDeploy(workflowType bean3.WorkflowType) cdWorkflow.WorkflowExecutorType {
	switch workflowType {
	case cdWorkflow.WorkflowTypePre:
		return impl.config.GetWorkflowExecutorType()
	case cdWorkflow.WorkflowTypePost:
		return impl.config.GetWorkflowExecutorType()
	case cdWorkflow.WorkflowTypeDeploy:
		return cdWorkflow.WORKFLOW_EXECUTOR_TYPE_AWF
	}
	// default assuming to be AWF
	return cdWorkflow.WORKFLOW_EXECUTOR_TYPE_AWF
}

func (impl *HandlerServiceImpl) getNamespaceForWorkflowType(workflowType bean3.WorkflowType, nameSpaceFromEnvironment string, runPreStageInEnv, runPostStageInEnv bool) string {
	namespace := impl.config.GetDefaultNamespace()
	switch workflowType {
	case cdWorkflow.WorkflowTypePre:
		if runPreStageInEnv {
			return nameSpaceFromEnvironment
		}
	case cdWorkflow.WorkflowTypePost:
		if runPostStageInEnv {
			return nameSpaceFromEnvironment
		}
	case cdWorkflow.WorkflowTypeDeploy:
		return namespace
	}
	// default assuming to be Deploy
	return namespace
}

func (impl *HandlerServiceImpl) GetCdWorkflowRunnerWithEnvConfig(cdWorkflowType bean3.WorkflowType, pipeline *pipelineConfig.Pipeline, envNameSpace string, cdWorkflowId int, triggeredBy int32, triggeredTime time.Time) *bean10.CdWorkflowRunnerDto {
	return adapter3.BuildCdWorkflowRunnerDto(pipeline.Name, cdWorkflowType,
		impl.getWorkflowExecutorTypeForPrePostOrDeploy(cdWorkflowType),
		impl.cdWorkflowCommonService.GetRunnerStatusBasedInWorkflowType(cdWorkflowType),
		triggeredBy, triggeredTime, impl.getNamespaceForWorkflowType(cdWorkflowType, envNameSpace,
			pipeline.RunPreStageInEnv, pipeline.RunPostStageInEnv),
		cdWorkflowId, impl.getIfBlobStorageIsEnabled(),
		impl.getLogLocationBasedOnWorkflowType(cdWorkflowType, pipeline.Name, cdWorkflowId))
}

func (impl *HandlerServiceImpl) ociHelmPush(overrideRequest *bean3.ValuesOverrideRequest, valuesOverrideResponse *app.ValuesOverrideResponse, builtChartPath string, err error, manifestPushConfig *repository.ManifestPushConfig, manifestPushTemplate *bean4.ManifestPushTemplate) error {
	var credentialsConfig bean4.HelmRepositoryConfig
	err = json.Unmarshal([]byte(manifestPushConfig.CredentialsConfig), &credentialsConfig)
	if err != nil {
		impl.logger.Errorw("error in json unmarshal", "err", err)
		return err
	}
	dockerArtifactStore, err := impl.dockerArtifactStoreRepository.FindOne(credentialsConfig.ContainerRegistryName)
	if err != nil {
		impl.logger.Errorw("error in fetching artifact info", "err", err)
		return err
	}
	image := valuesOverrideResponse.Artifact.Image
	imageTag := util4.GetImageTagFromImage(image)
	repoPath, chartName := app.GetRepoPathAndChartNameFromRepoName(credentialsConfig.RepositoryName)
	manifestPushTemplate.RepoUrl = path.Join(dockerArtifactStore.RegistryURL, repoPath)
	// pushed chart name should be same as repo name configured by user (if repo name is a/b/c chart name will be c)
	manifestPushTemplate.ChartName = chartName
	manifestPushTemplate.ChartVersion = fmt.Sprintf("%d.%d.%d-%s-%s", 1, 0, overrideRequest.WfrId, "DEPLOY", imageTag)
	manifestBytes, err := impl.chartTemplateService.LoadChartInBytes(builtChartPath, true, chartName, manifestPushTemplate.ChartVersion)
	if err != nil {
		impl.logger.Errorw("error in converting chart to bytes", "err", err)
		return err
	}
	manifestPushTemplate.BuiltChartBytes = &manifestBytes
	containerRegistryConfig := &bean4.ContainerRegistryConfig{
		RegistryUrl:                dockerArtifactStore.RegistryURL,
		CredentialsType:            dockerArtifactStore.CredentialsType,
		Username:                   dockerArtifactStore.Username,
		Password:                   dockerArtifactStore.Password,
		Insecure:                   true,
		AwsRegion:                  dockerArtifactStore.AWSRegion,
		AccessKey:                  dockerArtifactStore.AWSAccessKeyId,
		SecretKey:                  dockerArtifactStore.AWSSecretAccessKey,
		RegistryType:               string(dockerArtifactStore.RegistryType),
		IsPublic:                   false,
		RepoName:                   repoPath,
		Connection:                 dockerArtifactStore.Connection,
		Certificate:                dockerArtifactStore.Cert,
		RegistryName:               dockerArtifactStore.Id,
		RemoteConnectionConfigBean: nil,
	}
	for _, ociRegistryConfig := range dockerArtifactStore.OCIRegistryConfig {
		if ociRegistryConfig.RepositoryType == repository4.OCI_REGISRTY_REPO_TYPE_CHART {
			containerRegistryConfig.IsPublic = ociRegistryConfig.IsPublic
		}
	}
	containerRegistryConfig.RemoteConnectionConfigBean = adapter2.GetRemoteConnectionConfigBean(dockerArtifactStore.RemoteConnectionConfig)
	manifestPushTemplate.ContainerRegistryConfig = containerRegistryConfig
	return nil
}

func (impl *HandlerServiceImpl) PushCRDChartForRelease(ctx context.Context, dockerRegistryId, repoName, chartVersion string, chartBytes []byte, triggeredBy int32) error {
	manifestPushTemplate, err := impl.buildManifestPushTemplateFromDockerRegistryId(bean4.HelmRepositoryConfig{ContainerRegistryName: dockerRegistryId, RepositoryName: repoName}, chartVersion, chartBytes, triggeredBy)
	if err != nil {
		impl.logger.Errorw("error in PushChartForRelease", "dockerRegistryId", dockerRegistryId, "chartVersion", chartVersion, "err", err)
		return err
	}
	manifestPushService := impl.getManifestPushService(manifestPushTemplate.StorageType)
	manifestPushResponse := manifestPushService.PushChart(ctx, manifestPushTemplate)
	if manifestPushResponse.Error != nil {
		impl.logger.Errorw("error in pushing chart to helm repo", "err", err)
		return manifestPushResponse.Error
	}
	return nil
}

func (impl *HandlerServiceImpl) buildManifestPushTemplateFromDockerRegistryId(credentialsConfig bean4.HelmRepositoryConfig, chartVersion string, chartBytes []byte, triggeredBy int32) (*bean4.ManifestPushTemplate, error) {
	manifestPushTemplate := &bean4.ManifestPushTemplate{
		UserId:       triggeredBy,
		ChartVersion: chartVersion,
		StorageType:  bean2.ManifestStorageOras,
	}
	dockerArtifactStore, err := impl.dockerArtifactStoreRepository.FindOne(credentialsConfig.ContainerRegistryName)
	if err != nil {
		if errors.Is(err, pg.ErrNoRows) {
			impl.logger.Errorw("docker artifact store not found", "containerRegistryName", credentialsConfig.ContainerRegistryName)
			return manifestPushTemplate, util.GetApiErrorAdapter(http.StatusNotFound, "404", "registry not found", "registry not found")
		}
		impl.logger.Errorw("error in fetching artifact info", "err", err)
		return manifestPushTemplate, err
	}
	repoPath, chartName := app.GetRepoPathAndChartNameFromRepoName(credentialsConfig.RepositoryName)
	finalRepoUrl, err := url.JoinPath(dockerArtifactStore.RegistryURL, repoPath)
	if err != nil {
		impl.logger.Errorw("error encountered in joining path", "err", err, "registryURL", dockerArtifactStore.RegistryURL, "repoPath", repoPath)
		return nil, err
	}
	manifestPushTemplate.RepoUrl = finalRepoUrl
	manifestPushTemplate.ChartName = chartName
	manifestPushTemplate.BuiltChartBytes = &chartBytes
	containerRegistryConfig := &bean4.ContainerRegistryConfig{
		RegistryUrl:                strings.Trim(dockerArtifactStore.RegistryURL, "/"),
		CredentialsType:            dockerArtifactStore.CredentialsType,
		Username:                   dockerArtifactStore.Username,
		Password:                   dockerArtifactStore.Password,
		Insecure:                   true,
		AwsRegion:                  dockerArtifactStore.AWSRegion,
		AccessKey:                  dockerArtifactStore.AWSAccessKeyId,
		SecretKey:                  dockerArtifactStore.AWSSecretAccessKey,
		RegistryType:               string(dockerArtifactStore.RegistryType),
		IsPublic:                   false,
		RepoName:                   repoPath,
		Connection:                 dockerArtifactStore.Connection,
		Certificate:                dockerArtifactStore.Cert,
		RegistryName:               dockerArtifactStore.Id,
		RemoteConnectionConfigBean: adapter2.GetRemoteConnectionConfigBean(dockerArtifactStore.RemoteConnectionConfig),
	}
	for _, ociRegistryConfig := range dockerArtifactStore.OCIRegistryConfig {
		if ociRegistryConfig.RepositoryType == repository6.OCI_REGISRTY_REPO_TYPE_CHART {
			containerRegistryConfig.IsPublic = ociRegistryConfig.IsPublic
		}
	}
	manifestPushTemplate.ContainerRegistryConfig = containerRegistryConfig
	return manifestPushTemplate, err
}

func (impl *HandlerServiceImpl) enrichFeasibilityDtoWithLatestArtifactsIfExists(feasibilityCheckerDto []*bean.FeasibilityCheckerRequestDto) ([]*bean.FeasibilityCheckerRequestDto, error) {
	pipelineIds := make([]int, 0, len(feasibilityCheckerDto))
	var err error
	for _, item := range feasibilityCheckerDto {
		pipelineIds = append(pipelineIds, item.PipelineId)
	}
	cdWorkflowsMetadata, err := impl.cdWorkflowRepository.FindLatestSucceededWfsByCDPipelineIds(pipelineIds)
	if err != nil {
		impl.logger.Errorw("error in getting latest succeeded workflow by pipelineIds", "pipelineIds", pipelineIds, "err", err)
		return feasibilityCheckerDto, err
	}
	pipelineIdVsDeployedArtifactIdMap := make(map[int]int, len(cdWorkflowsMetadata))
	for _, cdWorkflowMetadata := range cdWorkflowsMetadata {
		pipelineIdVsDeployedArtifactIdMap[cdWorkflowMetadata.PipelineId] = cdWorkflowMetadata.CiArtifactId
	}

	feasibilityCheckerDto = helper.PopulateFeasibilityCheckerRequestDtoWithArtifactId(feasibilityCheckerDto, pipelineIdVsDeployedArtifactIdMap)
	return feasibilityCheckerDto, nil
}

func (impl *HandlerServiceImpl) CheckCdTriggerFeasibility(ctx context.Context, feasibilityCheckerDto []*bean.FeasibilityCheckerRequestDto, authorisedPipelineIdsMap map[int]bool,
	reDeployActiveImages bool, userMetadata *userBean.UserMetadata) ([]*bean.FeasibilityCheckerResponse, error) {
	resp := make([]*bean.FeasibilityCheckerResponse, 0, len(feasibilityCheckerDto))
	var err error
	if reDeployActiveImages {
		feasibilityCheckerDto, err = impl.enrichFeasibilityDtoWithLatestArtifactsIfExists(feasibilityCheckerDto)
		if err != nil {
			impl.logger.Errorw("error in enriching feasibility dto with latest artifacts if exists", "feasibilityCheckerDto", feasibilityCheckerDto, "err", err)
			return nil, err
		}

	}
	pipelineIdToPipelineMap, err := impl.getPipelineIdVsPipelineMap(feasibilityCheckerDto)
	if err != nil {
		impl.logger.Errorw("error in getting pipelineIds vs pipeline map", "feasibilityCheckerDto", feasibilityCheckerDto, "err", err)
		return nil, err
	}
	artifactIdToArtifactMap, err := impl.getArtifactIdVsArtifactMap(feasibilityCheckerDto)
	if err != nil {
		impl.logger.Errorw("error in getting pipelineIds vs pipeline map", "feasibilityCheckerDto", feasibilityCheckerDto, "err", err)
		return nil, err
	}
	for _, item := range feasibilityCheckerDto {
		feasibilityRespForPipeline, err := impl.feasibilityDryRunBeforeCdTrigger(ctx, item, pipelineIdToPipelineMap, artifactIdToArtifactMap, authorisedPipelineIdsMap, userMetadata)
		if err != nil {
			impl.logger.Errorw("error in getting feasibility response for pipeline", "pipelineId", item.PipelineId, "artifactId", item.ArtifactId, "err", err)
			if pipelineIdToPipelineMap != nil && pipelineIdToPipelineMap[item.PipelineId] != nil {
				env := pipelineIdToPipelineMap[item.PipelineId].Environment
				feasibilityErr := helper3.ConvertErrorAccordingToFeasibility(err, false, false)
				resp = append(resp, adapter.BuildFeasibilityCheckerResponse(item, env.Name, env.IsVirtualEnvironment, feasibilityErr))
				continue
			} else {
				return nil, err
			}

		}
		resp = append(resp, feasibilityRespForPipeline)
	}
	return resp, nil
}

func (impl *HandlerServiceImpl) feasibilityDryRunBeforeCdTrigger(ctx context.Context, feasibilityCheckerDto *bean.FeasibilityCheckerRequestDto,
	pipelineIdToPipelineMap map[int]*pipelineConfig.Pipeline, artifactIdToArtifactMap map[int]*repository3.CiArtifact, authorisedPipelineIdsMap map[int]bool, userMetadata *userBean.UserMetadata) (*bean.FeasibilityCheckerResponse, error) {
	env := repository2.Environment{}
	app := appRepository.App{}
	if pipelineIdToPipelineMap != nil && pipelineIdToPipelineMap[feasibilityCheckerDto.PipelineId] != nil {
		env = pipelineIdToPipelineMap[feasibilityCheckerDto.PipelineId].Environment
		app = pipelineIdToPipelineMap[feasibilityCheckerDto.PipelineId].App
	}
	var feasibilityError error
	if val, ok := authorisedPipelineIdsMap[feasibilityCheckerDto.PipelineId]; !ok || !val {
		feasibilityError = util.GetApiErrorAdapter(http.StatusForbidden, strconv.Itoa(http.StatusForbidden), "Not authorised", "unauthorised user")
	} else {
		artifact := artifactIdToArtifactMap[feasibilityCheckerDto.ArtifactId]
		cdPipeline := pipelineIdToPipelineMap[feasibilityCheckerDto.PipelineId]
		cdWorkflowType := feasibilityCheckerDto.CdWorkflowType
		if len(feasibilityCheckerDto.CdWorkflowType) == 0 {
			cdWorkflowType = bean3.CD_WORKFLOW_TYPE_DEPLOY
		}
		if app.Id > 0 && env.Id > 0 {
			scope := resourceQualifiers.BuildScope(app.Id, env.Id, env.ClusterId, app.TeamId, env.Default)
			triggerRequest := adapter.GetTriggerRequest(cdPipeline, artifact, userMetadata.UserId, adapter.GetTriggerContext(ctx), cdWorkflowType, 0, false, false, false)
			triggerRequirementRequest := adapter.GetTriggerRequirementRequest(scope, triggerRequest, resourceFilter.WorkflowTypeToReferenceType(cdWorkflowType), cdWorkflowType.GetDeploymentStageType())
			// checking feasibility
			feasibilityResp, _, deploymentWindowByPassed, err := impl.CheckFeasibility(triggerRequirementRequest, userMetadata)
			// err is not blocking here as feasibility breaks returns error with message and error code, just if err is nil we will convert that to success status
			if err != nil {
				// just logging error here for debugging purposes and not returning from here
				impl.logger.Debugw("error encountered in CheckFeasibility", "triggerRequirementRequest", triggerRequirementRequest, "err", err)
			}
			isUserException, err1 := impl.globalPolicyDataManager.IsUserAnExceptionByRqmResourceType(ctx, adaptor.ApplyPolicyTypeToRqmResourceType(model2.APPLY_POLICY_APPROVAL_DEPLOYMENT).ToInt(), userMetadata)
			if err1 != nil {
				impl.logger.Errorw("error in checking if a user is exception user or not by policy type ", "policyType", model2.APPLY_POLICY_APPROVAL_DEPLOYMENT, "err", err)
				return nil, err1
			}
			var canUserBypassApprovalPolicy bool
			if feasibilityResp != nil && feasibilityResp.ImageApprovedActionData != nil {
				// user can bypass approval policy if user is exception user and approval policy is configured and artifact is not approved
				canUserBypassApprovalPolicy = feasibilityResp.IsApprovalPolicyConfiguredForPipeline && !feasibilityResp.ImageApprovedActionData.IsArtifactApproved && isUserException
			}
			feasibilityError = helper3.ConvertErrorAccordingToFeasibility(err, deploymentWindowByPassed, canUserBypassApprovalPolicy)
		}
	}
	return adapter.BuildFeasibilityCheckerResponse(feasibilityCheckerDto, env.Name, env.IsVirtualEnvironment, feasibilityError), nil
}

func (impl *HandlerServiceImpl) getPipelineIdVsPipelineMap(feasibilityCheckerDto []*bean.FeasibilityCheckerRequestDto) (map[int]*pipelineConfig.Pipeline, error) {
	pipelineIds := make([]int, 0, len(feasibilityCheckerDto))
	for _, item := range feasibilityCheckerDto {
		pipelineIds = append(pipelineIds, item.PipelineId)
	}
	return impl.cdPipelineConfigService.GetPipelineIdVsPipelineMap(pipelineIds)
}

func (impl *HandlerServiceImpl) getArtifactIdVsArtifactMap(feasibilityCheckerDto []*bean.FeasibilityCheckerRequestDto) (map[int]*repository3.CiArtifact, error) {
	artifactIds := make([]int, 0, len(feasibilityCheckerDto))
	artifactIdVsArtifactMap := make(map[int]*repository3.CiArtifact, len(feasibilityCheckerDto))
	for _, item := range feasibilityCheckerDto {
		artifactIds = append(artifactIds, item.ArtifactId)
	}
	ciArtifacts, err := impl.ciArtifactRepository.GetByIds(artifactIds)
	if err != nil {
		impl.logger.Errorw("error in getting artifacts by artifact ids", "artifactIds", artifactIds, "err", err)
		return nil, err
	}
	for _, artifact := range ciArtifacts {
		if _, ok := artifactIdVsArtifactMap[artifact.Id]; !ok {
			artifactIdVsArtifactMap[artifact.Id] = artifact
		}
	}
	return artifactIdVsArtifactMap, nil
}
