/*
 * Copyright (c) 2024. Devtron Inc.
 */

package bean

import (
	"context"
	"github.com/devtron-labs/devtron/api/bean"
	"github.com/devtron-labs/devtron/enterprise/pkg/deploymentWindow"
	"github.com/devtron-labs/devtron/enterprise/pkg/resourceFilter"
	"github.com/devtron-labs/devtron/internal/sql/models"
	"github.com/devtron-labs/devtron/internal/sql/repository"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig/bean/workflow/cdWorkflow"
	"github.com/devtron-labs/devtron/pkg/auth/userGroup/beans"
	"github.com/devtron-labs/devtron/pkg/bean/common"
	bean3 "github.com/devtron-labs/devtron/pkg/deployment/common/bean"
	beans2 "github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/bean"
	bean2 "github.com/devtron-labs/devtron/pkg/resourceQualifiers"
	"github.com/devtron-labs/devtron/util/expressionEvaluator"
	"time"
)

const (
	ARGOCD_SYNC_ERROR             = "error in syncing argoCD app"
	HibernatingAppDeploymentError = "deployment trigger restricted due to hibernating app status"
)

type TriggerEvent struct {
	SaveTriggerHistory         bool
	PerformChartPush           bool
	PerformDeploymentOnCluster bool
	GetManifestInResponse      bool
	DeployArgoCdApp            bool
	DeploymentAppType          string
	ManifestStorageType        string
	UpdateRunnerOnSuccess      bool
	TriggeredBy                int32
	TriggeredAt                time.Time
}

type TriggerRequestExt struct {
}

type CdTriggerRequest struct {
	CdWf                   *pipelineConfig.CdWorkflow
	Pipeline               *pipelineConfig.Pipeline
	Artifact               *repository.CiArtifact
	ApplyAuth              bool
	TriggeredBy            int32
	RefCdWorkflowRunnerId  int
	RunStageInEnvNamespace string
	WorkflowType           bean.WorkflowType
	TriggerMessage         string
	RuntimeParameters      *common.RuntimeParameters
	DeploymentWindowState  *deploymentWindow.EnvironmentState
	CdWorkflowRunnerId     int // current used for release if runner id comes we don't create runner
	ScanExecutionHistoryId int // currently used to fetch scan scan_tool_execution_history_mapping for final state scanning
	ByPassFeasibility      bool
	TriggeredFromRelease   bool
	IsRollbackDeployment   bool
	TriggeredTime          time.Time
	ParentTriggerDetail    *ParentTriggerDetail
	TriggerContext
	// below fields used for retrigger flow
	IsRetrigger bool
}

func (r *CdTriggerRequest) SetCdWorkflowRunnerId(cdWfrId int) {
	if cdWfrId > 0 {
		r.CdWorkflowRunnerId = cdWfrId
	}
}

type ParentTriggerDetail struct {
	CdWorkflowRunnerId int
	CdWorkflowId       int
	CdWorkflowType     bean.WorkflowType
}

func (r *CdTriggerRequest) IsDeploymentTypeRollback() bool {
	return r.IsRollbackDeployment
}

type ImageApprovedActionData struct {
	ApprovalConfig              *beans2.UserApprovalConfig      `json:"approvalConfig"`
	DeploymentApprovalRequestId int                             `json:"-"`
	UserGroups                  map[int32][]*beans.UserGroupDTO `json:"userGroups"`
	IsArtifactApproved          bool                            `json:"-"`
}

type TriggerContext struct {
	// Context is a context object to be passed to the pipeline trigger
	// +optional
	Context context.Context
	// ReferenceId is a unique identifier for the workflow runner
	// refer pipelineConfig.CdWorkflowRunner
	ReferenceId *string

	// manual or automatic
	TriggerType TriggerType
}

type TriggerType int

const (
	Automatic TriggerType = 1
	Manual    TriggerType = 2
)

func (context TriggerContext) IsAutoTrigger() bool {
	return context.TriggerType == Automatic
}

func (context TriggerContext) ToTriggerTypeString() string {
	if context.IsAutoTrigger() {
		return "AUTO"
	}
	return "MANUAL"
}

type DeploymentType = string

const (
	Helm                    DeploymentType = "helm"
	ArgoCd                  DeploymentType = "argo_cd"
	FluxCd                  DeploymentType = "flux_cd"
	ManifestDownload        DeploymentType = "manifest_download"
	GitOpsWithoutDeployment DeploymentType = "git_ops_without_deployment"
	ManifestPush            DeploymentType = "manifest_push"
)

const ImagePromotionPolicyValidationErr = "error in cd trigger, user who has approved the image for promotion cannot deploy"

type FeasibilityCheckerRequestDto struct {
	ArtifactId     int               `json:"artifactId" validate:"number"`
	PipelineId     int               `json:"pipelineId" validate:"number,required"`
	AppId          int               `json:"appId" validate:"number"`
	CdWorkflowType bean.WorkflowType `json:"cdWorkflowType"`
}

// FeasibilityCheckerResponse being consumed in feasibility checker API
type FeasibilityCheckerResponse struct {
	EnvName              string            `json:"envName"`
	Feasibility          error             `json:"feasibility"`
	IsVirtualEnvironment bool              `json:"isVirtualEnvironment"`
	PipelineId           int               `json:"pipelineId"`
	ArtifactId           int               `json:"artifactId,omitempty"`
	AppId                int               `json:"appId,omitempty"`
	CdWorkflowType       bean.WorkflowType `json:"cdWorkflowType"`
}
type TriggerRequirementRequestDto struct {
	Scope          bean2.Scope
	TriggerRequest CdTriggerRequest
	Stage          resourceFilter.ReferenceType
	DeploymentType models.DeploymentType
}

type TriggerFeasibilityResponse struct {
	IsApprovalPolicyConfiguredForPipeline bool
	ImageApprovedActionData               *ImageApprovedActionData
	// todo: add config protection approval action data
	TriggerRequest  CdTriggerRequest
	FilterIdVsState map[int]expressionEvaluator.FilterState
	Filters         []*resourceFilter.FilterMetaDataBean
}

type VulnerabilityCheckRequest struct {
	ImageDigest string
	CdPipeline  *pipelineConfig.Pipeline
}

type TriggerOperationDto struct {
	TriggerRequest    CdTriggerRequest
	ExecutorType      cdWorkflow.WorkflowExecutorType
	PipelineId        int
	Scope             bean2.Scope
	TriggeredAt       time.Time
	OverrideCdWrfId   int
	ByPassFeasibility bool
}

const (
	CronJobChartRegexExpression = "cronjob-chart_1-(2|3|4|5|6)-0"
)

const (
	APP_LABEL_KEY_PREFIX         = "APP_LABEL_KEY"
	APP_LABEL_VALUE_PREFIX       = "APP_LABEL_VALUE"
	APP_LABEL_COUNT              = "APP_LABEL_COUNT"
	CHILD_CD_ENV_NAME_PREFIX     = "CHILD_CD_ENV_NAME"
	CHILD_CD_CLUSTER_NAME_PREFIX = "CHILD_CD_CLUSTER_NAME"
	CHILD_CD_COUNT               = "CHILD_CD_COUNT"
	APP_NAME                     = "APP_NAME"
)

type ValidateDeploymentTriggerObj struct {
	Runner               *pipelineConfig.CdWorkflowRunner
	CdPipeline           *pipelineConfig.Pipeline
	ImageDigest          string
	DeploymentConfig     *bean3.DeploymentConfig
	TriggeredBy          int32
	IsRollbackDeployment bool
}

func (r *ValidateDeploymentTriggerObj) IsDeploymentTypeRollback() bool {
	return r.IsRollbackDeployment
}
