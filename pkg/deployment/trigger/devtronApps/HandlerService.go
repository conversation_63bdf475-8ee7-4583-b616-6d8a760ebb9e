/*
 * Copyright (c) 2024. Devtron Inc.
 */

package devtronApps

import (
	"bufio"
	"context"
	util5 "github.com/devtron-labs/common-lib-private/utils/k8s"
	"github.com/devtron-labs/common-lib/async"
	service3 "github.com/devtron-labs/devtron/pkg/workflow/trigger/audit/service"
	"os"
	"time"

	"github.com/devtron-labs/devtron/client/fluxcd"
	bean3 "github.com/devtron-labs/devtron/api/bean"
	"github.com/devtron-labs/devtron/api/helm-app/gRPC"
	client2 "github.com/devtron-labs/devtron/api/helm-app/service"
	"github.com/devtron-labs/devtron/client/argocdServer"
	client "github.com/devtron-labs/devtron/client/events"
	"github.com/devtron-labs/devtron/enterprise/pkg/deploymentWindow"
	"github.com/devtron-labs/devtron/enterprise/pkg/globalTag"
	"github.com/devtron-labs/devtron/enterprise/pkg/resourceFilter"
	repository3 "github.com/devtron-labs/devtron/internal/sql/repository"
	appRepository "github.com/devtron-labs/devtron/internal/sql/repository/app"
	"github.com/devtron-labs/devtron/internal/sql/repository/appWorkflow"
	"github.com/devtron-labs/devtron/internal/sql/repository/chartConfig"
	repository4 "github.com/devtron-labs/devtron/internal/sql/repository/dockerRegistry"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/internal/util"
	"github.com/devtron-labs/devtron/internal/util/configUtil"
	"github.com/devtron-labs/devtron/pkg/app"
	bean4 "github.com/devtron-labs/devtron/pkg/app/bean"
	"github.com/devtron-labs/devtron/pkg/app/status"
	"github.com/devtron-labs/devtron/pkg/attributes"
	"github.com/devtron-labs/devtron/pkg/auth/user"
	userBean "github.com/devtron-labs/devtron/pkg/auth/user/bean"
	"github.com/devtron-labs/devtron/pkg/build/git/gitMaterial/read"
	pipeline2 "github.com/devtron-labs/devtron/pkg/build/pipeline"
	chartRepoRepository "github.com/devtron-labs/devtron/pkg/chartRepo/repository"
	"github.com/devtron-labs/devtron/pkg/cluster"
	repository2 "github.com/devtron-labs/devtron/pkg/cluster/environment/repository"
	repository5 "github.com/devtron-labs/devtron/pkg/cluster/repository"
	"github.com/devtron-labs/devtron/pkg/deployment/common"
	bean9 "github.com/devtron-labs/devtron/pkg/deployment/common/bean"
	"github.com/devtron-labs/devtron/pkg/deployment/gitOps/config"
	"github.com/devtron-labs/devtron/pkg/deployment/gitOps/git"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/publish"
	"github.com/devtron-labs/devtron/pkg/deployment/trigger/devtronApps/bean"
	"github.com/devtron-labs/devtron/pkg/deployment/trigger/devtronApps/userDeploymentRequest/service"
	"github.com/devtron-labs/devtron/pkg/devtronResource/taskRun"
	"github.com/devtron-labs/devtron/pkg/eventProcessor/out"
	"github.com/devtron-labs/devtron/pkg/executor"
	"github.com/devtron-labs/devtron/pkg/globalPolicy"
	"github.com/devtron-labs/devtron/pkg/imageDigestPolicy"
	"github.com/devtron-labs/devtron/pkg/pipeline"
	"github.com/devtron-labs/devtron/pkg/pipeline/history"
	"github.com/devtron-labs/devtron/pkg/pipeline/repository"
	"github.com/devtron-labs/devtron/pkg/pipeline/types"
	"github.com/devtron-labs/devtron/pkg/plugin"
	read4 "github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/read"
	security2 "github.com/devtron-labs/devtron/pkg/policyGovernance/security/imageScanning"
	read2 "github.com/devtron-labs/devtron/pkg/policyGovernance/security/imageScanning/read"
	"github.com/devtron-labs/devtron/pkg/sql"
	"github.com/devtron-labs/devtron/pkg/variables"
	"github.com/devtron-labs/devtron/pkg/workflow/cd"
	globalUtil "github.com/devtron-labs/devtron/util"
	"github.com/devtron-labs/devtron/util/rbac"
	"go.uber.org/zap"

	imageTagRead "github.com/devtron-labs/devtron/pkg/build/artifacts/imageTagging/read"
	service2 "github.com/devtron-labs/devtron/pkg/featureFlag/service"
	helper2 "github.com/devtron-labs/devtron/pkg/pipeline/runtimeParam"
	read3 "github.com/devtron-labs/devtron/pkg/policyGovernance/artifactApproval/read"
	read5 "github.com/devtron-labs/devtron/pkg/policyGovernance/artifactPromotion/read"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/plugin/alpha1"
)

/*
files in this package are -
HandlerService.go - containing If and impl with common used code
HandlerService_ent.go - containing ent If and impl with common used code
deployStageHandlerCode.go - code related to deploy stage trigger
deployStageHandlerCode_ent.go - ent code related to deploy stage trigger
preStageHandlerCode.go - code related to pre stage trigger
preStageHandlerCode_ent.go - ent code related to pre stage trigger
postStageHandlerCode.go - code related to post stage trigger
postStageHandlerCode_ent.go - ent code related to post stage trigger
prePostWfAndLogsCode.go - code containing pre/post wf handling(abort) and logs related code
*/

type HandlerService interface {
	TriggerPostStage(request bean.CdTriggerRequest, userMetadata *userBean.UserMetadata) (*bean4.ManifestPushTemplate, error)
	TriggerPreStage(request bean.CdTriggerRequest, userMetadata *userBean.UserMetadata) (*bean4.ManifestPushTemplate, error)

	TriggerAutoCDOnPreStageSuccess(triggerContext bean.TriggerContext, cdPipelineId, ciArtifactId, workflowId int, parentTriggerDetail *bean.ParentTriggerDetail) error

	TriggerStageForBulk(triggerRequest bean.CdTriggerRequest, userMetadata *userBean.UserMetadata) error

	ManualCdTrigger(triggerContext bean.TriggerContext, overrideRequest *bean3.ValuesOverrideRequest, userMetadata *userBean.UserMetadata) (int, string, *bean4.ManifestPushTemplate, error)
	TriggerAutomaticDeployment(request bean.CdTriggerRequest) error

	TriggerRelease(ctx context.Context, overrideRequest *bean3.ValuesOverrideRequest, envDeploymentConfig *bean9.DeploymentConfig, triggeredAt time.Time, triggeredBy int32) (releaseNo int, manifestPushTemplate *bean4.ManifestPushTemplate, err error)

	CancelStage(workflowRunnerId int, forceAbort bool, userId int32) (int, error)
	DownloadCdWorkflowArtifacts(buildId int) (*os.File, error)
	GetRunningWorkflowLogs(environmentId int, pipelineId int, workflowId int, followLogs bool) (*bufio.Reader, func() error, error)
}

type HandlerServiceImpl struct {
	logger                              *zap.SugaredLogger
	cdWorkflowCommonService             cd.CdWorkflowCommonService
	gitOpsManifestPushService           publish.GitOpsPushService
	gitOpsConfigReadService             config.GitOpsConfigReadService
	argoK8sClient                       argocdServer.ArgoK8sClient
	ACDConfig                           *argocdServer.ACDConfig
	argoClientWrapperService            argocdServer.ArgoClientWrapperService
	pipelineStatusTimelineService       status.PipelineStatusTimelineService
	chartTemplateService                util.ChartTemplateService
	eventFactory                        client.EventFactory
	eventClient                         client.EventClient
	globalEnvVariables                  *globalUtil.GlobalEnvVariables
	workflowEventPublishService         out.WorkflowEventPublishService
	manifestCreationService             manifest.ManifestCreationService
	deployedConfigurationHistoryService history.DeployedConfigurationHistoryService
	pipelineStageService                pipeline.PipelineStageService
	globalPluginService                 plugin.GlobalPluginService
	customTagService                    pipeline.CustomTagService
	pluginInputVariableParser           pipeline.PluginInputVariableParser
	prePostCdScriptHistoryService       history.PrePostCdScriptHistoryService
	scopedVariableManager               variables.ScopedVariableCMCSManager
	imageDigestPolicyService            imageDigestPolicy.ImageDigestPolicyService
	userService                         user.UserService
	config                              *types.CdConfig
	helmAppService                      client2.HelmAppService
	imageScanService                    security2.ImageScanService
	enforcerUtil                        rbac.EnforcerUtil
	userDeploymentRequestService        service.UserDeploymentRequestService
	helmAppClient                       gRPC.HelmAppClient //TODO refactoring: use helm app service instead
	appRepository                       appRepository.AppRepository
	ciPipelineMaterialRepository        pipelineConfig.CiPipelineMaterialRepository
	imageScanHistoryReadService         read2.ImageScanHistoryReadService
	imageScanDeployInfoService          security2.ImageScanDeployInfoService
	imageScanDeployInfoReadService      read2.ImageScanDeployInfoReadService
	pipelineRepository                  pipelineConfig.PipelineRepository
	pipelineOverrideRepository          chartConfig.PipelineOverrideRepository
	manifestPushConfigRepository        repository.ManifestPushConfigRepository
	chartRepository                     chartRepoRepository.ChartRepository
	envRepository                       repository2.EnvironmentRepository
	cdWorkflowRepository                pipelineConfig.CdWorkflowRepository
	ciWorkflowRepository                pipelineConfig.CiWorkflowRepository
	ciArtifactRepository                repository3.CiArtifactRepository
	ciTemplateService                   pipeline2.CiTemplateReadService
	gitMaterialReadService              read.GitMaterialReadService
	appLabelRepository                  pipelineConfig.AppLabelRepository
	ciPipelineRepository                pipelineConfig.CiPipelineRepository
	appWorkflowRepository               appWorkflow.AppWorkflowRepository
	dockerArtifactStoreRepository       repository4.DockerArtifactStoreRepository
	K8sUtil                             *util5.K8sUtilExtended
	transactionUtilImpl                 *sql.TransactionUtilImpl
	deploymentConfigService             common.DeploymentConfigService
	ciCdPipelineOrchestrator            pipeline.CiCdPipelineOrchestrator
	gitOperationService                 git.GitOperationService
	attributeService                    attributes.AttributesService
	clusterRepository                   repository5.ClusterRepository
	cdWorkflowRunnerService             cd.CdWorkflowRunnerService
	clusterService                      cluster.ClusterService
	ciLogService                        pipeline.CiLogService
	workflowService                     executor.WorkflowService
	blobConfigStorageService            pipeline.BlobStorageConfigService
	deploymentEventHandler              app.DeploymentEventHandler
	asyncRunnable                       *async.Runnable
	workflowTriggerAuditService         service3.WorkflowTriggerAuditService
	fluxCdDeploymentService             fluxcd.DeploymentService

	//ent only
	resourceFilterService           resourceFilter.ResourceFilterService
	resourceFilterAuditService      resourceFilter.FilterEvaluationAuditService
	deploymentApprovalRepository    pipelineConfig.DeploymentApprovalRepository
	helmRepoPushService             publish.HelmRepoPushService
	imageTaggingReadService         imageTagRead.ImageTaggingReadService
	artifactApprovalDataReadService read3.ArtifactApprovalDataReadService
	mergeUtil                       configUtil.MergeUtil

	artifactPromotionDataReadService        read5.ArtifactPromotionDataReadService
	cdPipelineConfigService                 pipeline.CdPipelineConfigService
	featureFlagService                      service2.FeatureFlagService
	pipelineHelper                          helper2.HelperService
	chartScanPublishService                 out.ChartScanPublishService
	deploymentWindowService                 deploymentWindow.DeploymentWindowService
	orasPushService                         publish.OrasPushService
	pluginPolicyV2                          alpha1.MandatoryPluginEnforcementService
	globalTagService                        globalTag.GlobalTagService
	approvalConfigurationEnforcementService read4.ApprovalPolicyReadService
	appListingService                       app.AppListingService
	globalPolicyDataManager                 globalPolicy.GlobalPolicyDataManager

	//task trigger
	taskRunTriggerService taskRun.TaskRunTriggerOperationService
}

func NewHandlerServiceImpl(logger *zap.SugaredLogger,
	cdWorkflowCommonService cd.CdWorkflowCommonService,
	gitOpsManifestPushService publish.GitOpsPushService,
	gitOpsConfigReadService config.GitOpsConfigReadService,
	argoK8sClient argocdServer.ArgoK8sClient,
	ACDConfig *argocdServer.ACDConfig,
	argoClientWrapperService argocdServer.ArgoClientWrapperService,
	pipelineStatusTimelineService status.PipelineStatusTimelineService,
	chartTemplateService util.ChartTemplateService,
	workflowEventPublishService out.WorkflowEventPublishService,
	manifestCreationService manifest.ManifestCreationService,
	deployedConfigurationHistoryService history.DeployedConfigurationHistoryService,
	pipelineStageService pipeline.PipelineStageService,
	globalPluginService plugin.GlobalPluginService,
	customTagService pipeline.CustomTagService,
	pluginInputVariableParser pipeline.PluginInputVariableParser,
	prePostCdScriptHistoryService history.PrePostCdScriptHistoryService,
	scopedVariableManager variables.ScopedVariableCMCSManager,
	imageDigestPolicyService imageDigestPolicy.ImageDigestPolicyService,
	userService user.UserService,
	helmAppService client2.HelmAppService,
	enforcerUtil rbac.EnforcerUtil,
	userDeploymentRequestService service.UserDeploymentRequestService,
	helmAppClient gRPC.HelmAppClient,
	eventFactory client.EventFactory,
	eventClient client.EventClient,
	envVariables *globalUtil.EnvironmentVariables,
	appRepository appRepository.AppRepository,
	ciPipelineMaterialRepository pipelineConfig.CiPipelineMaterialRepository,
	imageScanHistoryReadService read2.ImageScanHistoryReadService,
	imageScanDeployInfoReadService read2.ImageScanDeployInfoReadService,
	imageScanDeployInfoService security2.ImageScanDeployInfoService,
	pipelineRepository pipelineConfig.PipelineRepository,
	pipelineOverrideRepository chartConfig.PipelineOverrideRepository,
	manifestPushConfigRepository repository.ManifestPushConfigRepository,
	chartRepository chartRepoRepository.ChartRepository,
	envRepository repository2.EnvironmentRepository,
	cdWorkflowRepository pipelineConfig.CdWorkflowRepository,
	ciWorkflowRepository pipelineConfig.CiWorkflowRepository,
	ciArtifactRepository repository3.CiArtifactRepository,
	ciTemplateService pipeline2.CiTemplateReadService,
	gitMaterialReadService read.GitMaterialReadService,
	appLabelRepository pipelineConfig.AppLabelRepository,
	ciPipelineRepository pipelineConfig.CiPipelineRepository,
	appWorkflowRepository appWorkflow.AppWorkflowRepository,
	dockerArtifactStoreRepository repository4.DockerArtifactStoreRepository,
	imageScanService security2.ImageScanService,
	K8sUtil *util5.K8sUtilExtended,
	transactionUtilImpl *sql.TransactionUtilImpl,
	deploymentConfigService common.DeploymentConfigService,
	ciCdPipelineOrchestrator pipeline.CiCdPipelineOrchestrator,
	gitOperationService git.GitOperationService,
	attributeService attributes.AttributesService,
	clusterRepository repository5.ClusterRepository,
	cdWorkflowRunnerService cd.CdWorkflowRunnerService,
	clusterService cluster.ClusterService,
	ciLogService pipeline.CiLogService,
	workflowService executor.WorkflowService,
	blobConfigStorageService pipeline.BlobStorageConfigService,
	deploymentEventHandler app.DeploymentEventHandler,
	asyncRunnable *async.Runnable,
	workflowTriggerAuditService service3.WorkflowTriggerAuditService,
	fluxCdDeploymentService fluxcd.DeploymentService,

	//ent only
	pipelineHelper helper2.HelperService,
	artifactApprovalDataReadService read3.ArtifactApprovalDataReadService,
	mergeUtil configUtil.MergeUtil,
	imageTaggingReadService imageTagRead.ImageTaggingReadService,
	cdPipelineConfigService pipeline.CdPipelineConfigService,
	deploymentApprovalRepository pipelineConfig.DeploymentApprovalRepository,
	helmRepoPushService publish.HelmRepoPushService,
	resourceFilterService resourceFilter.ResourceFilterService,
	resourceFilterAuditService resourceFilter.FilterEvaluationAuditService,
	deploymentWindowService deploymentWindow.DeploymentWindowService,
	artifactPromotionDataReadService read5.ArtifactPromotionDataReadService,
	featureFlagService service2.FeatureFlagService,
	chartScanPublishService out.ChartScanPublishService,
	orasPushService publish.OrasPushService,
	pluginPolicyV2 alpha1.MandatoryPluginEnforcementService,
	globalTagService globalTag.GlobalTagService,
	approvalConfigurationEnforcementService read4.ApprovalPolicyReadService,
	appListingService app.AppListingService,
	globalPolicyDataManager globalPolicy.GlobalPolicyDataManager,
	taskRunTriggerService taskRun.TaskRunTriggerOperationService,
) (*HandlerServiceImpl, error) {
	impl := &HandlerServiceImpl{
		logger:                              logger,
		cdWorkflowCommonService:             cdWorkflowCommonService,
		gitOpsManifestPushService:           gitOpsManifestPushService,
		gitOpsConfigReadService:             gitOpsConfigReadService,
		argoK8sClient:                       argoK8sClient,
		ACDConfig:                           ACDConfig,
		argoClientWrapperService:            argoClientWrapperService,
		pipelineStatusTimelineService:       pipelineStatusTimelineService,
		chartTemplateService:                chartTemplateService,
		workflowEventPublishService:         workflowEventPublishService,
		manifestCreationService:             manifestCreationService,
		deployedConfigurationHistoryService: deployedConfigurationHistoryService,
		pipelineStageService:                pipelineStageService,
		globalPluginService:                 globalPluginService,
		customTagService:                    customTagService,
		pluginInputVariableParser:           pluginInputVariableParser,
		prePostCdScriptHistoryService:       prePostCdScriptHistoryService,
		scopedVariableManager:               scopedVariableManager,
		imageDigestPolicyService:            imageDigestPolicyService,
		userService:                         userService,
		helmAppService:                      helmAppService,
		enforcerUtil:                        enforcerUtil,
		eventFactory:                        eventFactory,
		eventClient:                         eventClient,

		globalEnvVariables:             envVariables.GlobalEnvVariables,
		userDeploymentRequestService:   userDeploymentRequestService,
		helmAppClient:                  helmAppClient,
		appRepository:                  appRepository,
		ciPipelineMaterialRepository:   ciPipelineMaterialRepository,
		imageScanHistoryReadService:    imageScanHistoryReadService,
		imageScanDeployInfoReadService: imageScanDeployInfoReadService,
		imageScanDeployInfoService:     imageScanDeployInfoService,
		pipelineRepository:             pipelineRepository,
		pipelineOverrideRepository:     pipelineOverrideRepository,
		manifestPushConfigRepository:   manifestPushConfigRepository,
		chartRepository:                chartRepository,
		envRepository:                  envRepository,
		cdWorkflowRepository:           cdWorkflowRepository,
		ciWorkflowRepository:           ciWorkflowRepository,
		ciArtifactRepository:           ciArtifactRepository,
		ciTemplateService:              ciTemplateService,
		gitMaterialReadService:         gitMaterialReadService,
		appLabelRepository:             appLabelRepository,
		ciPipelineRepository:           ciPipelineRepository,
		appWorkflowRepository:          appWorkflowRepository,
		dockerArtifactStoreRepository:  dockerArtifactStoreRepository,

		imageScanService: imageScanService,
		K8sUtil:          K8sUtil,

		transactionUtilImpl: transactionUtilImpl,

		deploymentConfigService:  deploymentConfigService,
		ciCdPipelineOrchestrator: ciCdPipelineOrchestrator,
		gitOperationService:      gitOperationService,
		attributeService:         attributeService,
		cdWorkflowRunnerService:  cdWorkflowRunnerService,

		clusterRepository:           clusterRepository,
		clusterService:              clusterService,
		ciLogService:                ciLogService,
		workflowService:             workflowService,
		blobConfigStorageService:    blobConfigStorageService,
		deploymentEventHandler:      deploymentEventHandler,
		asyncRunnable:               asyncRunnable,
		workflowTriggerAuditService: workflowTriggerAuditService,
		fluxCdDeploymentService:  fluxCdDeploymentService,

		//ent only
		pipelineHelper:                          pipelineHelper,
		deploymentWindowService:                 deploymentWindowService,
		artifactApprovalDataReadService:         artifactApprovalDataReadService,
		cdPipelineConfigService:                 cdPipelineConfigService,
		artifactPromotionDataReadService:        artifactPromotionDataReadService,
		mergeUtil:                               mergeUtil,
		imageTaggingReadService:                 imageTaggingReadService,
		deploymentApprovalRepository:            deploymentApprovalRepository,
		helmRepoPushService:                     helmRepoPushService,
		resourceFilterService:                   resourceFilterService,
		resourceFilterAuditService:              resourceFilterAuditService,
		chartScanPublishService:                 chartScanPublishService,
		featureFlagService:                      featureFlagService,
		orasPushService:                         orasPushService,
		pluginPolicyV2:                          pluginPolicyV2,
		globalTagService:                        globalTagService,
		approvalConfigurationEnforcementService: approvalConfigurationEnforcementService,
		appListingService:                       appListingService,
		globalPolicyDataManager:                 globalPolicyDataManager,
		taskRunTriggerService:                   taskRunTriggerService,
	}
	cdConfig, err := types.GetCdConfig()
	if err != nil {
		return nil, err
	}
	impl.config = cdConfig
	return impl, nil
}
