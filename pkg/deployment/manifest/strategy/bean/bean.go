package bean

import (
	"fmt"
	"github.com/devtron-labs/common-lib/utils/k8s"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig/bean/workflow/cdWorkflow"
	chartRepoRepository "github.com/devtron-labs/devtron/pkg/chartRepo/repository"
)

type DeploymentStrategyWithStatusResponse struct {
	*DeploymentStrategyStatus
	Strategy chartRepoRepository.DeploymentStrategy `json:"strategy"`
	AppId    int                                    `json:"appId"`
	EnvId    int                                    `json:"envId"`
}
type DeploymentStrategyStatus struct {
	CurrentStepIndex     int64                   `json:"currentStepIndex"`
	Steps                interface{}             `json:"steps"`
	Phase                string                  `json:"phase"`
	CurrentStepHash      string                  `json:"currentStepHash"`
	CurrentPodHash       string                  `json:"currentPodHash"`
	StableRS             string                  `json:"stableRS"`
	RolloutIdentifier    *k8s.ResourceIdentifier `json:"rolloutIdentifier"`
	Error                error                   `json:"error,omitempty"`
	AutoPromotionEnabled bool                    `json:"autoPromotionEnabled"`
	PromoteFull          bool                    `json:"promoteFull"`
}

func (d *DeploymentStrategyStatus) WithError(err error) *DeploymentStrategyStatus {
	d.Error = err
	return d
}

func (r *DeploymentStrategyWithStatusResponse) SetDeploymentStrategyStatus(deploymentStatus *DeploymentStrategyStatus) {
	r.DeploymentStrategyStatus = deploymentStatus

}
func GetAppIdEnvIdKey(appId int, envId int) string {
	return fmt.Sprintf("%d-%d", appId, envId)
}

const (
	K8sManifestKeyStrategy             string = "strategy"
	K8sManifestKeyStatus               string = "status"
	K8sManifestKeySpec                 string = "spec"
	K8sManifestKeyPhase                string = "phase"
	K8sManifestKeySteps                string = "steps"
	K8sManifestKeyCurrentStepIndex     string = "currentStepIndex"
	K8sManifestKeyCurrentStepHash      string = "currentStepHash"
	K8sManifestKeyCurrentPodHash       string = "currentPodHash"
	K8sManifestKeyStableRS             string = "stableRS"
	K8sManifestKeyCanaryStrategy       string = "canary"
	K8sManifestKeyBlueGreenStrategy    string = "blueGreen"
	K8sManifestKeyAutoPromotionEnabled string = "autoPromotionEnabled"
	K8sManifestKeyPromoteFull          string = "promoteFull"
)

type StrategyStatusConfig struct {
	WorkerPoolSize   int `env:"FEATURE_STRATEGY_STATUS_WORKER_POOL_SIZE" envDefault:"5" description:"Strategy Status retrieval pool size"`
	RequestBatchSize int `env:"FEATURE_STRATEGY_STATUS_BATCH_SIZE" envDefault:"1" description:"Strategy Status retrieval batch size "`
}

const (
	HealthyPhase     = "Healthy"
	ProgressingPhase = "Progressing"
)

func RunnerStatusToPhase(wfrStatus string) string {
	switch wfrStatus {
	case cdWorkflow.WorkflowStarting, cdWorkflow.WorkflowInitiated, cdWorkflow.WorkflowInProgress, cdWorkflow.WorkflowInQueue, cdWorkflow.WorkflowTimedOut:
		return ProgressingPhase
	case cdWorkflow.WorkflowSucceeded:
		return HealthyPhase
	default:
		return wfrStatus
	}
}
