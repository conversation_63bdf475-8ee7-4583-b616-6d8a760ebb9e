package strategy

import (
	"context"
	"encoding/json"
	"github.com/argoproj/argo-rollouts/pkg/apis/rollouts/v1alpha1"
	rolloututil "github.com/argoproj/argo-rollouts/utils/rollout"
	"github.com/caarlos0/env"
	"github.com/devtron-labs/common-lib/utils/k8s"
	apiBean "github.com/devtron-labs/devtron/api/restHandler/app/pipeline/configure/bean"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/internal/util"
	"github.com/devtron-labs/devtron/pkg/chart/read"
	chartRepoRepository "github.com/devtron-labs/devtron/pkg/chartRepo/repository"
	bean5 "github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/chartRef/bean"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/strategy/adapter"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/strategy/bean"
	"github.com/devtron-labs/devtron/pkg/generateManifest"
	bean2 "github.com/devtron-labs/devtron/pkg/generateManifest/bean"
	k8s2 "github.com/devtron-labs/devtron/pkg/k8s"
	adapter2 "github.com/devtron-labs/devtron/pkg/k8s/application/adapter"
	bean4 "github.com/devtron-labs/devtron/pkg/k8s/application/bean"
	bean3 "github.com/devtron-labs/devtron/pkg/k8s/bean"
	"github.com/devtron-labs/devtron/pkg/pipeline/history/repository"
	read2 "github.com/devtron-labs/devtron/pkg/workflow/cd/read"
	"github.com/gammazero/workerpool"
	"go.uber.org/zap"
	"net/http"
	"sync"
)

type DeploymentPipelineStrategyService interface {
	GetStrategyForAppIdsAndEnvId(request *apiBean.DeploymentStrategyRequest) ([]*bean.DeploymentStrategyWithStatusResponse, error)
}

type DeploymentPipelineStrategyServiceImpl struct {
	logger                            *zap.SugaredLogger
	pipelineRepository                pipelineConfig.PipelineRepository
	pipelineStrategyHistoryRepository repository.PipelineStrategyHistoryRepository
	deploymentTemplateService         generateManifest.DeploymentTemplateService
	k8sCommonService                  k8s2.K8sCommonService
	strategyPromotionConfig           *bean.StrategyStatusConfig
	chartReadService                  read.ChartReadService
	cdWorkflowRunnerReadService       read2.CdWorkflowRunnerReadService
}

func NewDeploymentPipelineStrategyServiceImpl(logger *zap.SugaredLogger,
	pipelineRepository pipelineConfig.PipelineRepository,
	pipelineStrategyHistoryRepository repository.PipelineStrategyHistoryRepository,
	deploymentTemplateService generateManifest.DeploymentTemplateService,
	k8sCommonService k8s2.K8sCommonService,
	chartReadService read.ChartReadService,
	cdWorkflowRunnerReadService read2.CdWorkflowRunnerReadService) (*DeploymentPipelineStrategyServiceImpl, error) {
	cfg, err := GetStrategyStatusConfig()
	if err != nil {
		logger.Errorw("error in getting strategy status config", "err", err)
		return nil, err
	}
	return &DeploymentPipelineStrategyServiceImpl{
		logger:                            logger,
		pipelineRepository:                pipelineRepository,
		pipelineStrategyHistoryRepository: pipelineStrategyHistoryRepository,
		deploymentTemplateService:         deploymentTemplateService,
		k8sCommonService:                  k8sCommonService,
		strategyPromotionConfig:           cfg,
		chartReadService:                  chartReadService,
		cdWorkflowRunnerReadService:       cdWorkflowRunnerReadService,
	}, nil

}
func GetStrategyStatusConfig() (*bean.StrategyStatusConfig, error) {
	cfg := &bean.StrategyStatusConfig{}
	err := env.Parse(cfg)
	return cfg, err
}

// GetStrategyForAppIdsAndEnvId retrieves deployment strategies and statuses for given app IDs and environment ID. It fetches pipeline
// data, determines the latest deployment strategy, and optionally includes rollout deployment status based on the request.
func (impl *DeploymentPipelineStrategyServiceImpl) GetStrategyForAppIdsAndEnvId(request *apiBean.DeploymentStrategyRequest) ([]*bean.DeploymentStrategyWithStatusResponse, error) {
	appIds := request.AppIds
	envId := request.EnvId
	response := make([]*bean.DeploymentStrategyWithStatusResponse, 0, len(appIds))
	// Step 1: Fetch All pipelines for the given appIds and envId
	pipelines, err := impl.pipelineRepository.FindActiveByInFilter(envId, appIds)
	if err != nil {
		impl.logger.Errorw("error fetching active pipelines", "appIds", request.AppIds, "envId", request.EnvId, "err", err)
		return response, err
	}
	if len(pipelines) == 0 {
		impl.logger.Warnw("no pipeline found", "appIds", request.AppIds, "envId", request.EnvId)
		return response, nil
	}

	mapOfAppIdEnvIdVsPipeline := make(map[string]int, len(pipelines))
	pipelineIds := make([]int, 0, len(pipelines))
	eligibleAppIdsForStatus := make([]int, 0, len(appIds))
	for _, pipeline := range pipelines {
		mapOfAppIdEnvIdVsPipeline[bean.GetAppIdEnvIdKey(pipeline.AppId, pipeline.EnvironmentId)] = pipeline.Id
		pipelineIds = append(pipelineIds, pipeline.Id)
		// namespace,cluster id  will be same as only one env
		request.SetClusterId(pipeline.Environment.ClusterId)
		request.SetNamespace(pipeline.Environment.Namespace)
	}

	// Step 2: Get the strategy from pipeline_strategy table for latest deployment trigger
	pipelineStrategyHistory, err := impl.pipelineStrategyHistoryRepository.GetLatestDeployedHistoryForPipelineIds(pipelineIds)
	if err != nil {
		impl.logger.Errorw("error in GetStrategyForAppIdsAndEnvId", "appIds", appIds, "envId", envId, "err", err)
		return response, err
	}
	mapOfPipelineIdVsStrategy := make(map[int]chartRepoRepository.DeploymentStrategy, len(pipelineStrategyHistory))
	for _, strategyHistory := range pipelineStrategyHistory {
		mapOfPipelineIdVsStrategy[strategyHistory.PipelineId] = strategyHistory.Strategy
	}
	// Step 3: Get the type of chart used for app and env for optimisation
	appIdVsChartNameMap, err := impl.chartReadService.FindAppIdVsLatestChartNameForAppIds(appIds)
	if err != nil {
		impl.logger.Errorw("error in FindAppIdVsLatestChartIdForAppIds", "appIds", appIds, "err", err)
		return nil, err
	}
	pipelineIdVsStatus, err := impl.cdWorkflowRunnerReadService.FetchLatestRunnerStatusMapForPipelineIds(pipelineIds, envId)
	if err != nil {
		impl.logger.Errorw("error in FetchLatestRunnerStatusMapForPipelineIds", "pipelineIds", pipelineIds, "envId", envId, "err", err)
		return nil, err
	}
	// Step 4: Set the strategy in response and find eligible appIds for status
	for _, appId := range appIds {
		key := bean.GetAppIdEnvIdKey(appId, envId)
		pipelineId, exists := mapOfAppIdEnvIdVsPipeline[key]
		if !exists {
			impl.logger.Warnw("no pipeline found for app and env", "appId", appId, "envId", envId)
			continue
		}

		strategy, ok := mapOfPipelineIdVsStrategy[pipelineId]
		var errToSend error
		if !ok {
			errToSend = util.DefaultApiError().WithUserMessage("No deployed strategy found").WithHttpStatusCode(http.StatusNotFound).WithCode("404")
		}
		status := ""
		if statusVal, ok := pipelineIdVsStatus[pipelineId]; ok {
			status = bean.RunnerStatusToPhase(statusVal)
		}

		response = append(response, adapter.BuildDeploymentStrategyResponse(appId, envId, strategy, status, errToSend))

		if chartName, ok := appIdVsChartNameMap[appId]; ok && chartName == bean5.RolloutChartType {
			eligibleAppIdsForStatus = append(eligibleAppIdsForStatus, appId)
		}
	}
	// Step 5 : Set Rollout status and corresponding fields in response
	if request.FetchStatus && len(eligibleAppIdsForStatus) > 0 {
		err = impl.setDeploymentStatusFromRolloutInResp(request, response)
		if err != nil {
			impl.logger.Errorw("error in setting deployment status from rollout", "appIds", appIds, "envId", envId, "err", err)
			return nil, err
		}
	}
	return response, nil
}

// setDeploymentStatusFromRolloutInResp updates deployment status in the response based on rollout status fetched in bulk.
func (impl *DeploymentPipelineStrategyServiceImpl) setDeploymentStatusFromRolloutInResp(request *apiBean.DeploymentStrategyRequest, response []*bean.DeploymentStrategyWithStatusResponse) error {
	mapOfAppIdEnvIdKeyVsRolloutStatus, err := impl.getRolloutManifestInBulk(request)
	if err != nil {
		impl.logger.Errorw("error in getRolloutManifestInBulk", "appIds", request.AppIds, "envId", request.EnvId, "err", err)
		return err
	}
	for _, resp := range response {
		if status, ok := mapOfAppIdEnvIdKeyVsRolloutStatus[bean.GetAppIdEnvIdKey(resp.AppId, resp.EnvId)]; ok {
			resp.SetDeploymentStrategyStatus(status)
		}
	}
	return nil
}

// getRolloutManifestInBulk retrieves the rollout deployment manifest in bulk for provided app IDs and environment ID.
// It fetches rollout resource identifiers and uses a worker pool to concurrently get the deployment strategy statuses.
// Returns a map of app-environment keys to deployment strategy statuses, or an error if any occurs during processing.
func (impl *DeploymentPipelineStrategyServiceImpl) getRolloutManifestInBulk(request *apiBean.DeploymentStrategyRequest) (map[string]*bean.DeploymentStrategyStatus, error) {
	appIds := request.AppIds
	envId := request.EnvId
	namespace := request.Namespace
	clusterId := request.ClusterId
	mapOfAppIdEnvIdVsRolloutIdentifier, err := impl.getRolloutResourceIdentifierMap(request, namespace)
	if err != nil {
		impl.logger.Errorw("error in getRolloutResourceIdentifierMap", "err", err)
		return nil, err
	}
	mapOfAppIdEnvVsDeploymentStrategyStatus := make(map[string]*bean.DeploymentStrategyStatus, len(appIds))
	manifestResponseLock := &sync.Mutex{}
	wp := workerpool.New(impl.strategyPromotionConfig.WorkerPoolSize)

	for _, appId := range appIds {
		localCopyOfAppId := appId // Create local copy for goroutine
		appIdEnvIdKey := bean.GetAppIdEnvIdKey(localCopyOfAppId, envId)
		if identifier, ok := mapOfAppIdEnvIdVsRolloutIdentifier[appIdEnvIdKey]; ok {
			identifierCopy := identifier
			wp.Submit(func() {
				strategyStatus := impl.setSpecAndStatusFromManifestInMap(request.Ctx, localCopyOfAppId, envId, clusterId, identifierCopy)
				manifestResponseLock.Lock()
				mapOfAppIdEnvVsDeploymentStrategyStatus[appIdEnvIdKey] = strategyStatus
				manifestResponseLock.Unlock()
			})
		}
	}
	// Wait for all workers to complete
	wp.StopWait()
	return mapOfAppIdEnvVsDeploymentStrategyStatus, nil
}

// getRolloutResourceIdentifierMap retrieves a map of resource identifiers for rollout objects based on app IDs and environment ID.
// It utilizes fetched workload metadata to construct resource identifiers and filters out invalid or incomplete data.
func (impl *DeploymentPipelineStrategyServiceImpl) getRolloutResourceIdentifierMap(request *apiBean.DeploymentStrategyRequest, namespace string) (map[string]*k8s.ResourceIdentifier, error) {
	mapOfAppIdEnvIdVsRolloutIdentifier := make(map[string]*k8s.ResourceIdentifier)
	appIds := request.AppIds
	envId := request.EnvId
	workloadsResp, err := impl.deploymentTemplateService.GetRestartWorkloadData(request.Ctx, appIds, envId, request.UserMetadata, []string{bean2.Rollout.ToString()})
	if err != nil {
		impl.logger.Errorw("error in GetRestartWorkloadData", "appIds", appIds, "envId", envId, "err", err)
		return nil, err
	}

	if workloadsResp == nil || workloadsResp.RestartPodMap == nil {
		impl.logger.Warnw("empty response from GetRestartWorkloadData", "appIds", appIds, "envId", envId)
		return mapOfAppIdEnvIdVsRolloutIdentifier, nil
	}

	for _, appId := range appIds {
		if res, ok := workloadsResp.RestartPodMap[appId]; ok && len(res.ResourceMetaData) > 0 && len(res.ErrorResponse) == 0 {
			identifier := res.ResourceMetaData[0]
			if identifier.Name == "" || identifier.GroupVersionKind.Kind == "" {
				impl.logger.Warnw("invalid resource metadata", "appId", appId, "metadata", identifier)
				continue
			}

			mapOfAppIdEnvIdVsRolloutIdentifier[bean.GetAppIdEnvIdKey(appId, envId)] = k8s.NewResourceIdentifier().
				WithName(identifier.Name).
				WithNameSpace(namespace).
				WithGroup(identifier.GroupVersionKind.Group).
				WithVersion(identifier.GroupVersionKind.Version).
				WithKind(identifier.GroupVersionKind.Kind)
		}
	}

	return mapOfAppIdEnvIdVsRolloutIdentifier, nil
}

// setSpecAndStatusFromManifestInMap retrieves and parses Kubernetes manifest details for a given app, environment, and cluster.
// It extracts `spec` and `status` fields to build a DeploymentStrategyStatus object with relevant strategy and rollout info.
func (impl *DeploymentPipelineStrategyServiceImpl) setSpecAndStatusFromManifestInMap(ctx context.Context, appId, envId, clusterId int, resourceIdentifier *k8s.ResourceIdentifier) *bean.DeploymentStrategyStatus {
	k8Req := &bean3.ResourceRequestBean{
		AppType:              bean4.DevtronAppType,
		ClusterId:            clusterId,
		DevtronAppIdentifier: adapter2.BuildDevtronAppIdentifier(appId, envId, clusterId),
		K8sRequest:           &k8s.K8sRequestBean{ResourceIdentifier: *resourceIdentifier},
	}
	k8sManifestResp, err := impl.k8sCommonService.GetResource(ctx, k8Req)
	if err != nil {
		impl.logger.Errorw("error in GetResource", "appId", appId, "envId", envId, "err", err)
		return adapter.NewDeploymentStrategyStatus().WithError(util.DefaultApiError().WithUserMessage(err.Error()).WithHttpStatusCode(500))
	}
	if k8sManifestResp != nil && k8sManifestResp.ManifestResponse != nil {
		manifestObject := k8sManifestResp.ManifestResponse.Manifest.Object
		var specMap map[string]interface{}
		if specVal, ok := manifestObject[bean.K8sManifestKeySpec]; ok {
			if specMapVal, ok := specVal.(map[string]interface{}); ok {
				specMap = specMapVal
			}
		}

		var statusMap map[string]interface{}
		if statusVal, ok := manifestObject[bean.K8sManifestKeyStatus]; ok {
			if statusMapVal, ok := statusVal.(map[string]interface{}); ok {
				statusMap = statusMapVal
			}
		}
		var phase string
		var currentStepIndex int64
		var steps interface{}
		var currentStepHash string
		var currentPodHash string
		var stableRS string
		var autoPromotionEnabled bool
		var promoteFull bool
		var compatibilityError error

		if statusMap != nil {

			if hashVal, ok := statusMap[bean.K8sManifestKeyCurrentStepHash]; ok {
				currentStepHash = hashVal.(string)
			}
			if podHashVal, ok := statusMap[bean.K8sManifestKeyCurrentPodHash]; ok {
				currentPodHash = podHashVal.(string)
			}
			if stableRSVal, ok := statusMap[bean.K8sManifestKeyStableRS]; ok {
				// new schema
				stableRS = stableRSVal.(string)
			} else if canaryMapVal, ok := statusMap[bean.K8sManifestKeyCanaryStrategy].(map[string]interface{}); ok {
				if stableRSVal, ok = canaryMapVal[bean.K8sManifestKeyStableRS]; ok {
					stableRS = stableRSVal.(string)
				}
				compatibilityError = util.DefaultApiError().WithUserMessage("Rollout chart is not compatible, please upgrade to latest version").WithHttpStatusCode(http.StatusFailedDependency).WithCode("424")
			}

			if phaseVal, ok := statusMap[bean.K8sManifestKeyPhase]; ok {
				phase = phaseVal.(string)
			} else {
				// for older rollout controller, phase is calculated at client side
				phase, err = impl.getRolloutPhase(manifestObject, stableRS, appId, envId)
				if err != nil {
					impl.logger.Errorw("error in getRolloutPhase", "appId", appId, "envId", envId, "err", err)
				}
				compatibilityError = util.DefaultApiError().WithUserMessage("Rollout chart is not compatible, please upgrade to latest version").WithHttpStatusCode(http.StatusFailedDependency).WithCode("424")
			}

			if currentStepIndexVal, ok := statusMap[bean.K8sManifestKeyCurrentStepIndex]; ok {
				currentStepIndex = currentStepIndexVal.(int64)
			}
			if promoteFullVal, ok := statusMap[bean.K8sManifestKeyPromoteFull]; ok {
				promoteFull = promoteFullVal.(bool)
			}
		}
		if specMap != nil {
			if strategyMap, ok := specMap[bean.K8sManifestKeyStrategy].(map[string]interface{}); ok {
				if canaryStrategyMap, ok := strategyMap[bean.K8sManifestKeyCanaryStrategy].(map[string]interface{}); ok {
					steps = canaryStrategyMap[bean.K8sManifestKeySteps]
				}
				if blueGreenStrategyMap, ok := strategyMap[bean.K8sManifestKeyBlueGreenStrategy].(map[string]interface{}); ok {
					if enabled, ok2 := blueGreenStrategyMap[bean.K8sManifestKeyAutoPromotionEnabled]; ok2 {
						if boolVal, ok3 := enabled.(bool); ok3 {
							autoPromotionEnabled = boolVal
						} else {
							autoPromotionEnabled = true
						}
					} else {
						// considering default value as true as officially rollout controller consider it true if not found in manifest
						autoPromotionEnabled = true
					}
				}
			}
		}
		return adapter.BuildDeploymentStrategyStatus(currentStepIndex, steps, phase, currentStepHash, currentPodHash, stableRS, resourceIdentifier, autoPromotionEnabled, promoteFull, compatibilityError)
	}
	return nil
}

func (impl *DeploymentPipelineStrategyServiceImpl) getRolloutPhase(manifestObject map[string]interface{}, stableRS string, appId int, envId int) (string, error) {
	var rollout *v1alpha1.Rollout
	rolloutJSON, err := json.Marshal(manifestObject)
	if err != nil {
		impl.logger.Errorw("error in marshal rollout object", "appId", appId, "envId", envId, "err", err)
		return "", err
	}
	err = json.Unmarshal(rolloutJSON, &rollout)
	if err != nil {
		impl.logger.Errorw("error in unmarshal rollout object", "appId", appId, "envId", envId, "err", err)
		return "", err
	}
	if rollout != nil {
		// hack - setting stable rs found from old path in new path
		rollout.Status.StableRS = stableRS
	}
	rolloutPhase, _ := rolloututil.GetRolloutPhase(rollout)
	phase := string(rolloutPhase)
	return phase, nil
}
