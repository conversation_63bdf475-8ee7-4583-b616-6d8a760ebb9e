package adapter

import (
	"github.com/devtron-labs/common-lib/utils/k8s"
	chartRepoRepository "github.com/devtron-labs/devtron/pkg/chartRepo/repository"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/strategy/bean"
)

func BuildDeploymentStrategyResponse(appId, EnvId int, strategy chartRepoRepository.DeploymentStrategy, phase string, err error) *bean.DeploymentStrategyWithStatusResponse {
	return &bean.DeploymentStrategyWithStatusResponse{
		AppId:    appId,
		EnvId:    EnvId,
		Strategy: strategy,
		DeploymentStrategyStatus: &bean.DeploymentStrategyStatus{
			Phase: phase,
			Error: err,
		},
	}
}
func NewDeploymentStrategyStatus() *bean.DeploymentStrategyStatus {
	return &bean.DeploymentStrategyStatus{}

}
func BuildDeploymentStrategyStatus(currentStepIndex int64, steps interface{}, phase string, currentStepHash string, currentPodHash string, stableRS string, rolloutIdentifier *k8s.ResourceIdentifier, autoPromotionEnabled bool, promoteFull bool, error error) *bean.DeploymentStrategyStatus {
	return &bean.DeploymentStrategyStatus{
		CurrentStepIndex:     currentStepIndex,
		Steps:                steps,
		Phase:                phase,
		CurrentPodHash:       currentPodHash,
		CurrentStepHash:      currentStepHash,
		StableRS:             stableRS,
		RolloutIdentifier:    rolloutIdentifier,
		AutoPromotionEnabled: autoPromotionEnabled,
		PromoteFull:          promoteFull,
		Error:                error,
	}
}

func NewDeploymentStrategyWithStatusResponse() *bean.DeploymentStrategyWithStatusResponse {
	return &bean.DeploymentStrategyWithStatusResponse{}
}
