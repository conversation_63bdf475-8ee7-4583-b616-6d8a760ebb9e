package repository

import (
	"errors"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/chartResourceConfig/bean"
	"github.com/devtron-labs/devtron/pkg/sql"
	"github.com/go-pg/pg"
)

type ChartRefSchema struct {
	tableName     struct{} `sql:"chart_ref_schema" pg:",discard_unknown_columns"`
	Id            int      `sql:"id,pk"`
	Name          string   `sql:"name"`
	Type          int      `sql:"type"`
	Schema        string   `sql:"schema"` //deprecated
	ResourceValue string   `sql:"resource_value"`
	Active        bool     `sql:"active,notnull"`
	ResourceType  int      `sql:"resource_type,notnull"`
	sql.AuditLog
}

type ChartRefSchemaRepository interface {
	sql.TransactionWrapper
	GetSchemas(tx *pg.Tx) ([]ChartRefSchema, error)
	GetSchemaById(tx *pg.Tx, Id int) (*ChartRefSchema, error)
	GetSchemaByName(name string, tx *pg.Tx) (*ChartRefSchema, error)
	CreateSchema(userId int32, schema *ChartRefSchema, tx *pg.Tx) (*ChartRefSchema, error)
	UpdateSchema(userId int32, schema *ChartRefSchema, tx *pg.Tx) error
	DeleteSchema(userId int32, name string, tx *pg.Tx) error
	DeleteSchemaByNameAndType(userId int32, name string, resourceType int, tx *pg.Tx) error

	GetSchemaByNameAndType(name string, resourceType int, tx *pg.Tx) (*ChartRefSchema, error)
}

type ChartRefSchemaRepositoryImpl struct {
	dbConnection *pg.DB
	*sql.TransactionUtilImpl
}

func NewChartRefSchemaRepositoryImpl(dbConnection *pg.DB, TransactionUtilImpl *sql.TransactionUtilImpl) *ChartRefSchemaRepositoryImpl {
	return &ChartRefSchemaRepositoryImpl{
		dbConnection:        dbConnection,
		TransactionUtilImpl: TransactionUtilImpl,
	}
}

func (impl *ChartRefSchemaRepositoryImpl) GetSchemaByName(name string, tx *pg.Tx) (*ChartRefSchema, error) {
	schema := ChartRefSchema{Name: name}
	err := tx.Model(&schema).Where("name = ?", name).Where("active=?", true).Select()
	if err != nil {
		if errors.Is(err, pg.ErrNoRows) {
			return nil, bean.NoSchemaExistWithThisName
		}
		return nil, err
	}
	return &schema, err
}
func (impl *ChartRefSchemaRepositoryImpl) GetSchemaByNameAndType(name string, resourceType int, tx *pg.Tx) (*ChartRefSchema, error) {
	schema := ChartRefSchema{Name: name, ResourceType: resourceType}
	err := tx.Model(&schema).Where("name = ?", name).
		Where("resource_type = ?", resourceType).
		Where("active=?", true).Select()
	if err != nil {
		if errors.Is(err, pg.ErrNoRows) {
			return nil, bean.NoSchemaExistWithThisNameAndType
		}
	}
	return &schema, err
}

func (impl *ChartRefSchemaRepositoryImpl) GetSchemaById(tx *pg.Tx, Id int) (*ChartRefSchema, error) {
	chartRefSchema := &ChartRefSchema{}
	err := tx.Model(chartRefSchema).Where("id = ?", Id).Select()
	return chartRefSchema, err
}

func (impl *ChartRefSchemaRepositoryImpl) GetSchemas(tx *pg.Tx) ([]ChartRefSchema, error) {
	var schemas []ChartRefSchema
	err := tx.Model(&schemas).Where("active=?", true).Select()
	return schemas, err
}

func (impl *ChartRefSchemaRepositoryImpl) CreateSchema(userId int32, schema *ChartRefSchema, tx *pg.Tx) (*ChartRefSchema, error) {
	_, err := impl.GetSchemaByNameAndType(schema.Name, schema.ResourceType, tx)
	if err != nil && errors.Is(err, bean.NoSchemaExistWithThisNameAndType) {
		schema.AuditLog.CreateAuditLog(userId)
		err := tx.Insert(schema)
		if err != nil {
			return nil, err
		}
		return schema, nil
	} else if err != nil {
		return nil, err
	}
	return nil, bean.NameAlreadyTaken
}

func (impl *ChartRefSchemaRepositoryImpl) UpdateSchema(userId int32, schema *ChartRefSchema, tx *pg.Tx) error {
	schema.AuditLog.UpdateAuditLog(userId)
	_, err := tx.Model(schema).
		WherePK().
		Update()
	return err
}

func (impl *ChartRefSchemaRepositoryImpl) DeleteSchema(userId int32, name string, tx *pg.Tx) error {
	schema := ChartRefSchema{Name: name}
	schema.AuditLog.UpdateAuditLog(userId)
	_, err := tx.Model(&schema).
		Where("name = ?", name).
		Set("updated_by =?", schema.AuditLog.UpdatedBy).
		Set("updated_on = ?", schema.AuditLog.UpdatedOn).
		Set("active = ?", false).Update()
	return err
}
func (impl *ChartRefSchemaRepositoryImpl) DeleteSchemaByNameAndType(userId int32, name string, resourceType int, tx *pg.Tx) error {
	schema := ChartRefSchema{Name: name, ResourceType: resourceType}
	schema.AuditLog.UpdateAuditLog(userId)
	_, err := tx.Model(&schema).Where("name = ?", name).Where("resource_type = ? ", resourceType).
		Set("updated_by =?", schema.AuditLog.UpdatedBy).
		Set("updated_on = ?", schema.AuditLog.UpdatedOn).
		Set("active = ?", false).Update()
	return err
}
