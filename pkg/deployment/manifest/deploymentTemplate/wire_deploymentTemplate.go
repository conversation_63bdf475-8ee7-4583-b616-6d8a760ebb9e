/*
 * Copyright (c) 2024. Devtron Inc.
 */

package deploymentTemplate

import (
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/chartRef"
	chartRefRead "github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/chartRef/read"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/chartResourceConfig"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/chartResourceConfig/repository"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/read"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/validator"
	"github.com/google/wire"
)

var DeploymentTemplateWireSet = wire.NewSet(
	NewDeploymentTemplateServiceImpl,
	wire.Bind(new(DeploymentTemplateService), new(*DeploymentTemplateServiceImpl)),

	read.NewEnvConfigOverrideReadServiceExtendedImpl,
	wire.Bind(new(read.EnvConfigOverrideService), new(*read.EnvConfigOverrideReadServiceExtendedImpl)),

	validator.NewDeploymentTemplateValidationServiceEntImpl,
	validator.NewDeploymentTemplateValidationServiceImpl,
	wire.Bind(new(validator.DeploymentTemplateValidationService), new(*validator.DeploymentTemplateValidationServiceImpl)),

	chartRefRead.NewChartRefReadServiceImpl,
	wire.Bind(new(chartRefRead.ChartRefReadService), new(*chartRefRead.ChartRefReadServiceImpl)),

	chartRef.NewChartRefServiceImpl,
	wire.Bind(new(chartRef.ChartRefService), new(*chartRef.ChartRefServiceImpl)),
	chartResourceConfig.NewChartRefSchemaServiceImpl, wire.Bind(new(chartResourceConfig.ChartRefSchemaService), new(*chartResourceConfig.ChartRefSchemaServiceImpl)),
	repository.NewChartRefSchemaRepositoryImpl, wire.Bind(new(repository.ChartRefSchemaRepository), new(*repository.ChartRefSchemaRepositoryImpl)),
	read.NewDeploymentTemplateHistoryReadServiceImpl,
	wire.Bind(new(read.DeploymentTemplateHistoryReadService), new(*read.DeploymentTemplateHistoryReadServiceImpl)),
)
