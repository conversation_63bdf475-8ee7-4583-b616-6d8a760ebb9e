/*
 * Copyright (c) 2020-2024. Devtron Inc.
 */

package read

import (
	"fmt"
	"github.com/devtron-labs/devtron/internal/sql/models"
	"github.com/devtron-labs/devtron/internal/sql/repository/chartConfig"
	"github.com/devtron-labs/devtron/internal/util/configUtil"
	chartRepoRepository "github.com/devtron-labs/devtron/pkg/chartRepo/repository"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/adapter"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/bean"
	"github.com/devtron-labs/devtron/pkg/resourceQualifiers"
	"github.com/devtron-labs/devtron/pkg/variables"
	"github.com/devtron-labs/devtron/pkg/variables/parsers"
	repository5 "github.com/devtron-labs/devtron/pkg/variables/repository"
	globalUtil "github.com/devtron-labs/devtron/util"
	"github.com/juju/errors"
	"go.uber.org/zap"
)

type EnvConfigOverrideServiceEnt interface {
	FindResolvedChartByAppIdAndEnvIdAndChartRefId(appId, targetEnvironmentId, chartRefId int, scope resourceQualifiers.Scope, unMaskSensitiveData bool) (*bean.EnvConfigOverride, error)
	ActiveEnvConfigOverrideResolved(appId, environmentId int, scope resourceQualifiers.Scope, unMaskSensitiveData bool) (*bean.EnvConfigOverride, error)
	GetByIdIncludingInactiveResolved(id int, scope resourceQualifiers.Scope, unMaskSensitiveData bool) (*bean.EnvConfigOverride, error)

	FindChartRefForAppByAppIdAndEnvId(appId, targetEnvironmentId int) (int, error)
	GetOverrideValuesForPatchStrategy(appId int, envOverridePatchValues string) (string, error)

	GetRuntimeValueForEnvOverrideByAppIdUnResolved(mergeStrategy models.MergeStrategy, appId int, requestOverrideValues string) (string, string, error)

	GetRuntimeValueForUnsavedEnvOverrideResolved(mergeStrategy models.MergeStrategy, chart *chartRepoRepository.Chart,
		appId int, requestOverrideValues string, scope resourceQualifiers.Scope) (string, string, error)
	GetRuntimeValueForSavedEnvOverrideResolved(mergeStrategy models.MergeStrategy, chart *chartRepoRepository.Chart, appId, envOverrideId int, requestOverrideValues string, scope resourceQualifiers.Scope, unMaskSensitiveData bool) (string, string, map[string]string, error)
}

type EnvConfigOverrideReadServiceExtendedImpl struct {
	scopedVariableManager variables.ScopedVariableManager
	*EnvConfigOverrideReadServiceImpl
}

func NewEnvConfigOverrideReadServiceExtendedImpl(repository chartConfig.EnvConfigOverrideRepository, logger *zap.SugaredLogger,
	chartRepository chartRepoRepository.ChartRepository, mergeUtil configUtil.MergeUtil,
	scopedVariableManager variables.ScopedVariableManager,
) *EnvConfigOverrideReadServiceExtendedImpl {
	return &EnvConfigOverrideReadServiceExtendedImpl{
		EnvConfigOverrideReadServiceImpl: &EnvConfigOverrideReadServiceImpl{envConfigOverrideRepository: repository,
			logger:          logger,
			chartRepository: chartRepository,
			mergeUtil:       mergeUtil,
		},
		scopedVariableManager: scopedVariableManager,
	}
}

//dummy impl start

func (impl EnvConfigOverrideReadServiceImpl) FindResolvedChartByAppIdAndEnvIdAndChartRefId(appId, targetEnvironmentId, chartRefId int, scope resourceQualifiers.Scope, unMaskSensitiveData bool) (*bean.EnvConfigOverride, error) {
	return nil, fmt.Errorf("not implemented")
}

func (impl EnvConfigOverrideReadServiceImpl) ActiveEnvConfigOverrideResolved(appId, environmentId int, scope resourceQualifiers.Scope, unMaskSensitiveData bool) (*bean.EnvConfigOverride, error) {
	return nil, fmt.Errorf("not implemented")
}

func (impl EnvConfigOverrideReadServiceImpl) GetByIdIncludingInactiveResolved(id int, scope resourceQualifiers.Scope, unMaskSensitiveData bool) (*bean.EnvConfigOverride, error) {
	return nil, fmt.Errorf("not implemented")
}

func (impl EnvConfigOverrideReadServiceImpl) FindChartRefForAppByAppIdAndEnvId(appId, targetEnvironmentId int) (int, error) {
	return 0, fmt.Errorf("not implemented")
}

// GetOverrideValuesForPatchStrategy patches passed values with latest base deployment template values of app
func (impl EnvConfigOverrideReadServiceImpl) GetOverrideValuesForPatchStrategy(appId int, envOverridePatchValues string) (string, error) {
	return "", fmt.Errorf("not implemented")

}

func (impl EnvConfigOverrideReadServiceImpl) GetRuntimeValueForUnsavedEnvOverrideResolved(mergeStrategy models.MergeStrategy, chart *chartRepoRepository.Chart,
	appId int, requestOverrideValues string, scope resourceQualifiers.Scope) (string, string, error) {
	return "", "", fmt.Errorf("not implemented")
}

func (impl EnvConfigOverrideReadServiceImpl) GetRuntimeValueForSavedEnvOverrideResolved(mergeStrategy models.MergeStrategy, chart *chartRepoRepository.Chart,
	appId, envOverrideId int, requestOverrideValues string, scope resourceQualifiers.Scope, unMaskSensitiveData bool) (string, string, map[string]string, error) {
	return "", "", nil, fmt.Errorf("not implemented")
}

//dummy impl ended

func (impl EnvConfigOverrideReadServiceImpl) getOverrideDataWithUpdatedPatchDataUnResolved(overrideDTO *bean.EnvConfigOverride, appId int) (*bean.EnvConfigOverride, error) {
	var err error
	if overrideDTO.IsOverride {
		overrideDTO.EnvOverrideValues, overrideDTO.EnvOverridePatchValues, err = impl.GetRuntimeValueForEnvOverrideByAppIdUnResolved(overrideDTO.MergeStrategy, appId, overrideDTO.GetDBOverrideValuesByMergeStrategy())
		if err != nil {
			impl.logger.Errorw("error in patching values to base template values", "envId", overrideDTO.TargetEnvironment, "appId", appId, "err", err)
			return nil, err
		}
	}
	return overrideDTO, nil
}

func (impl EnvConfigOverrideReadServiceExtendedImpl) FindResolvedChartByAppIdAndEnvIdAndChartRefId(appId, targetEnvironmentId, chartRefId int, scope resourceQualifiers.Scope, unMaskSensitiveData bool) (*bean.EnvConfigOverride, error) {
	overrideDBObj, err := impl.envConfigOverrideRepository.FindChartByAppIdAndEnvIdAndChartRefId(appId, targetEnvironmentId, chartRefId)
	if err != nil {
		impl.logger.Errorw("error in getting chart env config override", "appId", appId, "targetEnvironmentIds", targetEnvironmentId, "chartRefId", chartRefId, "err", err)
		return nil, err
	}
	overrideDTO := adapter.EnvOverrideDBToDTO(overrideDBObj)
	if overrideDTO.IsOverride {
		dbOverrideValues := overrideDTO.GetDBOverrideValuesByMergeStrategy()
		overrideDTO.ResolvedEnvOverrideValues, overrideDTO.EnvOverridePatchValues, overrideDTO.VariableSnapshot, err =
			impl.GetRuntimeValueForSavedEnvOverrideResolved(overrideDTO.MergeStrategy, overrideDTO.Chart, overrideDTO.Chart.AppId, overrideDTO.Id,
				dbOverrideValues, scope, false)
		if err != nil {
			impl.logger.Errorw("error in patching values to base template values", "envId", overrideDTO.TargetEnvironment, "appId", overrideDTO.Chart.AppId, "err", err)
			return nil, err
		}
		//updating unresolved data in envOverrideValues, being used in history
		overrideDTO.EnvOverrideValues, _, err = impl.GetRuntimeValueForEnvOverrideByAppIdUnResolved(overrideDTO.MergeStrategy, appId, dbOverrideValues)
		if err != nil {
			impl.logger.Errorw("error in patching values to base template values", "envId", overrideDTO.TargetEnvironment, "appId", appId, "err", err)
			return nil, err
		}
	}
	return overrideDTO, nil
}

func (impl EnvConfigOverrideReadServiceExtendedImpl) ActiveEnvConfigOverrideResolved(appId, environmentId int, scope resourceQualifiers.Scope, unMaskSensitiveData bool) (*bean.EnvConfigOverride, error) {
	overrideDBObj, err := impl.envConfigOverrideRepository.ActiveEnvConfigOverride(appId, environmentId)
	if err != nil {
		impl.logger.Errorw("error in getting chart env config override", "appId", appId, "environmentId", environmentId, "err", err)
		return nil, err
	}
	overrideDTO := adapter.EnvOverrideDBToDTO(overrideDBObj)
	if overrideDTO.IsOverride {
		dbOverrideValues := overrideDTO.GetDBOverrideValuesByMergeStrategy()
		overrideDTO.ResolvedEnvOverrideValues, overrideDTO.EnvOverridePatchValues, overrideDTO.VariableSnapshot, err =
			impl.GetRuntimeValueForSavedEnvOverrideResolved(overrideDTO.MergeStrategy, nil, appId, overrideDTO.Id, dbOverrideValues, scope, unMaskSensitiveData)
		if err != nil {
			impl.logger.Errorw("error in patching values to base template values", "envId", environmentId, "appId", appId, "err", err)
			return nil, err
		}
		//updating unresolved data in envOverrideValues, being used in history
		overrideDTO.EnvOverrideValues, _, err = impl.GetRuntimeValueForEnvOverrideByAppIdUnResolved(overrideDTO.MergeStrategy, appId, dbOverrideValues)
		if err != nil {
			impl.logger.Errorw("error in patching values to base template values", "envId", overrideDTO.TargetEnvironment, "appId", appId, "err", err)
			return nil, err
		}
	}
	return overrideDTO, nil
}

func (impl EnvConfigOverrideReadServiceExtendedImpl) GetByIdIncludingInactiveResolved(id int, scope resourceQualifiers.Scope, unMaskSensitiveData bool) (*bean.EnvConfigOverride, error) {
	overrideDBObj, err := impl.envConfigOverrideRepository.GetByIdIncludingInactive(id)
	if err != nil {
		impl.logger.Errorw("error in getting chart env config override", "id", id, "err", err)
		return nil, err
	}
	overrideDTO := adapter.EnvOverrideDBToDTO(overrideDBObj)
	if overrideDTO.IsOverride {
		dbOverrideValues := overrideDTO.GetDBOverrideValuesByMergeStrategy()
		overrideDTO.ResolvedEnvOverrideValues, overrideDTO.EnvOverridePatchValues, overrideDTO.VariableSnapshot, err =
			impl.GetRuntimeValueForSavedEnvOverrideResolved(overrideDTO.MergeStrategy, nil, overrideDTO.Chart.AppId, overrideDTO.Id, dbOverrideValues, scope, unMaskSensitiveData)
		if err != nil {
			impl.logger.Errorw("error in patching values to base template values", "envId", overrideDTO.TargetEnvironment, "appId", overrideDTO.Chart.AppId, "err", err)
			return nil, err
		}
		//updating unresolved data in envOverrideValues, being used in history
		overrideDTO.EnvOverrideValues, _, err = impl.GetRuntimeValueForEnvOverrideByAppIdUnResolved(overrideDTO.MergeStrategy, overrideDTO.Chart.AppId, dbOverrideValues)
		if err != nil {
			impl.logger.Errorw("error in patching values to base template values", "envId", overrideDTO.TargetEnvironment, "appId", overrideDTO.Chart.AppId, "err", err)
			return nil, err
		}
	}
	return overrideDTO, nil
}

func (impl EnvConfigOverrideReadServiceExtendedImpl) FindChartRefForAppByAppIdAndEnvId(appId, targetEnvironmentId int) (int, error) {
	if targetEnvironmentId > 0 {
		overrideDBObj, err := impl.envConfigOverrideRepository.FindLatestChartForAppByAppIdAndEnvId(appId, targetEnvironmentId)
		if err != nil && !errors.IsNotFound(err) {
			impl.logger.Errorw("error in getting chart env config override", "appId", appId, "targetEnvironmentId", targetEnvironmentId, "err", err)
			return 0, err
		}
		if overrideDBObj != nil && overrideDBObj.Chart != nil {
			return overrideDBObj.Chart.ChartRefId, nil
		}
	}
	chart, err := impl.chartRepository.FindLatestChartForAppByAppId(appId)
	if err != nil {
		impl.logger.Errorw("error i n getting latest chart", "appId", appId, "err", err)
		return 0, err
	}
	return chart.ChartRefId, nil
}

// GetOverrideValuesForPatchStrategy patches passed values with latest base deployment template values of app
func (impl EnvConfigOverrideReadServiceExtendedImpl) GetOverrideValuesForPatchStrategy(appId int, envOverridePatchValues string) (string, error) {
	chart, err := impl.chartRepository.FindLatestChartForAppByAppId(appId)
	if err != nil {
		impl.logger.Errorw("error in finding latest chart by appId", "appId", appId, "err", err)
		return "", err
	}
	overrideValuesAfterPatch, err := impl.mergeUtil.ApplyPatch(
		[]byte(chart.GlobalOverride), []byte(envOverridePatchValues))
	if err != nil {
		impl.logger.Errorw("error in patching values in global override", "err", err)
		return "", err
	}
	return string(overrideValuesAfterPatch), nil
}

func (impl EnvConfigOverrideReadServiceImpl) GetRuntimeValueForEnvOverrideByAppIdUnResolved(mergeStrategy models.MergeStrategy, appId int, requestOverrideValues string) (string, string, error) {

	chart, err := impl.chartRepository.FindLatestChartForAppByAppId(appId)
	if err != nil {
		impl.logger.Errorw("error in finding latest chart by appId", "appId", appId, "err", err)
		return "", "", err
	}
	envOverrideValues, envOverridePatchValues, err := impl.GetRuntimeValueForEnvOverrideByBaseTemplateValues(mergeStrategy, chart.GlobalOverride, requestOverrideValues)
	if err != nil {
		impl.logger.Errorw("error in getting runtime override values", "appId", appId, "err", err)
		return "", "", err
	}

	return envOverrideValues, envOverridePatchValues, nil
}

func (impl EnvConfigOverrideReadServiceImpl) GetRuntimeValueForEnvOverrideByBaseTemplateValues(mergeStrategy models.MergeStrategy, baseDeploymentTemplateValues, requestOverrideValues string) (string, string, error) {
	var (
		envOverrideValues      = globalUtil.GetEmptyJSON()
		envOverridePatchValues = "{}"
		err                    error
	)
	if requestOverrideValues == "" {
		requestOverrideValues = "{}"
	}
	switch mergeStrategy {
	case models.MERGE_STRATEGY_PATCH:
		envOverrideValues, err = impl.mergeUtil.ApplyPatch([]byte(baseDeploymentTemplateValues), []byte(requestOverrideValues))
		if err != nil {
			return "", "", err
		}
		envOverridePatchValues = requestOverrideValues
	case models.MERGE_STRATEGY_REPLACE:
		// handling of old dashboard and new orchestrator ?
		envOverrideValues = []byte(requestOverrideValues)
	default:
		envOverrideValues = []byte(requestOverrideValues)
	}
	return string(envOverrideValues), envOverridePatchValues, nil
}

func (impl EnvConfigOverrideReadServiceExtendedImpl) GetRuntimeValueForUnsavedEnvOverrideResolved(mergeStrategy models.MergeStrategy, chart *chartRepoRepository.Chart,
	appId int, requestOverrideValues string, scope resourceQualifiers.Scope) (string, string, error) {
	var err error
	if chart == nil || chart.Id == 0 {
		if appId == 0 { //normal err message because handling just for dev mistake, since too many possible callee
			return "", "", fmt.Errorf("invalid request argument : no chart or appId")
		}
		chart, err = impl.chartRepository.FindLatestChartForAppByAppId(appId)
		if err != nil {
			impl.logger.Errorw("error in finding latest chart by appId", "appId", appId, "err", err)
			return "", "", err
		}
	}

	baseDeploymentTemplateValues := chart.GlobalOverride
	var resolvedBaseTemplate, resolvedEnvTemplate string

	//resolve baseDeploymentTemplate
	resolvedBaseTemplate, _, err = impl.scopedVariableManager.ExtractVariablesAndResolveTemplate(scope, baseDeploymentTemplateValues, parsers.JsonVariableTemplate, true, false, false)
	if err != nil {
		impl.logger.Errorw("error, ExtractVariablesAndResolveTemplate base override level template", "err", err)
		return "", "", err
	}

	//resolve envDeploymentTemplate
	resolvedEnvTemplate, _, err = impl.scopedVariableManager.ExtractVariablesAndResolveTemplate(scope, requestOverrideValues, parsers.JsonVariableTemplate, true, false, false)
	if err != nil {
		impl.logger.Errorw("error, ExtractVariablesAndResolveTemplate base override level template", "err", err)
		return "", "", err
	}

	envOverrideValuesFinal, envOverridePatchValuesFinal, err := impl.GetRuntimeValueForEnvOverrideByBaseTemplateValues(mergeStrategy, resolvedBaseTemplate, resolvedEnvTemplate)
	if err != nil {
		impl.logger.Errorw("error in getting runtime override values", "appId", appId, "err", err)
		return "", "", err
	}

	return envOverrideValuesFinal, envOverridePatchValuesFinal, nil
}

func (impl EnvConfigOverrideReadServiceExtendedImpl) GetRuntimeValueForSavedEnvOverrideResolved(mergeStrategy models.MergeStrategy, chart *chartRepoRepository.Chart,
	appId, envOverrideId int, requestOverrideValues string, scope resourceQualifiers.Scope, unMaskSensitiveData bool) (string, string, map[string]string, error) {
	var err error
	if chart == nil || chart.Id == 0 {
		if appId == 0 { //normal err message because handling just for dev mistake, since too many possible callee
			return "", "", nil, fmt.Errorf("invalid request argument : no chart or appId")
		}
		chart, err = impl.chartRepository.FindLatestChartForAppByAppId(appId)
		if err != nil {
			impl.logger.Errorw("error in finding latest chart by appId", "appId", appId, "err", err)
			return "", "", nil, err
		}
	}

	baseDeploymentTemplateValues := chart.GlobalOverride
	var resolvedBaseTemplate, resolvedEnvTemplate string

	//resolve baseDeploymentTemplate
	entity := repository5.GetEntity(chart.Id, repository5.EntityTypeDeploymentTemplateAppLevel)
	resolvedBaseTemplate, _, err = impl.scopedVariableManager.GetMappedVariablesAndResolveTemplate(baseDeploymentTemplateValues, scope, entity, unMaskSensitiveData, true)
	if err != nil {
		impl.logger.Errorw("error,  GetMappedVariablesAndResolveTemplate base override level template", "err", err)
		return "", "", nil, err
	}

	//resolve envDeploymentTemplate
	entityEnv := repository5.GetEntity(envOverrideId, repository5.EntityTypeDeploymentTemplateEnvLevel)
	var variableMap map[string]string
	resolvedEnvTemplate, variableMap, err = impl.scopedVariableManager.GetMappedVariablesAndResolveTemplate(requestOverrideValues, scope, entityEnv, unMaskSensitiveData, true)
	if err != nil {
		impl.logger.Errorw("error,  GetMappedVariablesAndResolveTemplate env override level template", "err", err)
		return "", "", nil, err
	}

	envOverrideValuesFinal, envOverridePatchValuesFinal, err := impl.GetRuntimeValueForEnvOverrideByBaseTemplateValues(mergeStrategy, resolvedBaseTemplate, resolvedEnvTemplate)
	if err != nil {
		impl.logger.Errorw("error in getting runtime override values", "appId", appId, "err", err)
		return "", "", nil, err
	}

	return envOverrideValuesFinal, envOverridePatchValuesFinal, variableMap, nil
}
