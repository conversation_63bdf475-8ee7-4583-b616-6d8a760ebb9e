/*
 * Copyright (c) 2024. Devtron Inc.
 */

package validator

import (
	"errors"
	"fmt"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/internal/util"
	bean3 "github.com/devtron-labs/devtron/pkg/chart/bean"
	"github.com/devtron-labs/devtron/pkg/chart/read"
	chartRefRead "github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/chartRef/read"
	"github.com/go-pg/pg"
	"gopkg.in/go-playground/validator.v9"
	"net/http"
)

type DeploymentTemplateValidationServiceEnt interface {
	ValidateAndGetTemplateChangeRefRequest(request *bean3.TemplateRefChangeRequest) (*bean3.TemplateRefChangeRequest, error)
}

type DeploymentTemplateValidationServiceEntImpl struct {
	validator           *validator.Validate
	chartReadService    read.ChartReadService
	pipelineRepository  pipelineConfig.PipelineRepository
	chartRefReadService chartRefRead.ChartRefReadService
}

func NewDeploymentTemplateValidationServiceEntImpl(
	validator *validator.Validate,
	chartReadService read.ChartReadService,
	pipelineRepository pipelineConfig.PipelineRepository,
	chartRefReadService chartRefRead.ChartRefReadService,
) *DeploymentTemplateValidationServiceEntImpl {
	return &DeploymentTemplateValidationServiceEntImpl{
		validator:           validator,
		chartReadService:    chartReadService,
		pipelineRepository:  pipelineRepository,
		chartRefReadService: chartRefReadService,
	}
}

func (impl *DeploymentTemplateValidationServiceImpl) ValidateAndGetTemplateChangeRefRequest(request *bean3.TemplateRefChangeRequest) (*bean3.TemplateRefChangeRequest, error) {
	err := impl.validator.Struct(request)
	if err != nil {
		impl.logger.Errorw("validation err, ChangeChartRef", "err", err, "payload", request)
		return request, util.NewApiError(http.StatusBadRequest, err.Error(), err.Error())
	}
	targetRefChart, err := impl.chartRefReadService.FindById(request.TargetChartRefId)
	if errors.Is(err, pg.ErrNoRows) {
		impl.logger.Errorw("no target chart ref found", "err", err)
		errMsg := fmt.Sprintf("no target chart ref found")
		return request, util.NewApiError(http.StatusNotFound, errMsg, errMsg)
	} else if err != nil {
		impl.logger.Errorw("error in finding chart ref by id", "appId", request.AppId, "err", err)
		return request, err
	}
	request.IsTargetChartCustom = targetRefChart.UserUploaded
	template, err := impl.chartReadService.FindLatestChartForAppByAppId(request.AppId)
	if err != nil && !errors.Is(err, pg.ErrNoRows) {
		impl.logger.Errorw("error in finding latest chart for app by app id", "appId", request.AppId, "err", err)
		return request, err
	} else if errors.Is(err, pg.ErrNoRows) {
		impl.logger.Errorw("no chart found for app", "appId", request.AppId)
		errMsg := fmt.Sprintf("no chart found for the application")
		return request, util.NewApiError(http.StatusNotFound, errMsg, errMsg)
	}
	request.AppTemplate = template
	compatible, chartChangeType := impl.chartRefService.ChartRefIdsCompatible(template.ChartRefId, request.TargetChartRefId)
	if !compatible {
		errMsg := fmt.Sprintf("charts not compatible")
		return request, util.NewApiError(http.StatusUnprocessableEntity, errMsg, errMsg)
	}
	// Check if this is just a version change of the same chart type
	isSameChartTypeVersionChange := chartChangeType.OldChartType == chartChangeType.NewChartType
	if !chartChangeType.IsFlaggerCanarySupported() && !isSameChartTypeVersionChange {
		enabled, err := impl.FlaggerCanaryEnabled(template.DefaultAppOverride)
		if err != nil {
			impl.logger.Errorw("error in checking flaggerCanary, ChangeChartRef", "err", err, "payload", template.DefaultAppOverride)
			return request, err
		} else if enabled {
			errMsg := fmt.Sprintf("%q charts do not support flaggerCanary", chartChangeType.NewChartType)
			impl.logger.Errorw(errMsg, "templateValues", template.DefaultAppOverride)
			return request, util.NewApiError(http.StatusUnprocessableEntity, errMsg, errMsg)
		}
	}
	template.DefaultAppOverride, err = impl.chartRefService.PerformChartSpecificPatchForSwitch(template.DefaultAppOverride, chartChangeType)
	if err != nil {
		impl.logger.Errorw("error in chart specific patch operations, ChangeChartRef", "err", err, "payload", request)
		return request, err
	}
	if !chartChangeType.IsFlaggerCanarySupported() && !isSameChartTypeVersionChange {
		enabled, err := impl.FlaggerCanaryEnabled(template.DefaultAppOverride)
		if err != nil || enabled {
			impl.logger.Errorw("rollout charts do not support flaggerCanary, ChangeChartRef", "err", err, "payload", request)
			errMsg := fmt.Sprintf("%q charts do not support flaggerCanary", chartChangeType.NewChartType)
			return request, util.NewApiError(http.StatusUnprocessableEntity, errMsg, errMsg)
		}
	}

	pipelineModels, err := impl.pipelineRepository.FindActiveByAppId(request.AppId)
	if err != nil {
		impl.logger.Errorw("error in fetching pipelines by appId", "appId", request.AppId, "err", err)
		return request, err
	}
	envIds := make(map[int]int)
	for _, pipelineModel := range pipelineModels {
		envIds[pipelineModel.Id] = pipelineModel.EnvironmentId
	}
	request.PipelineIdToEnvIdMap = envIds
	return request, nil
}
