/*
 * Copyright (c) 2020-2024. Devtron Inc.
 */

package git

import (
	"context"
	"fmt"
	"github.com/devtron-labs/devtron/api/bean"
	apiGitOpsBean "github.com/devtron-labs/devtron/api/bean/gitOps"
	"github.com/devtron-labs/devtron/internal/sql/constants"
	bean2 "github.com/devtron-labs/devtron/pkg/deployment/gitOps/git/bean"
	git "github.com/devtron-labs/devtron/pkg/deployment/gitOps/git/commandManager"
	"github.com/devtron-labs/devtron/util"
	"github.com/devtron-labs/devtron/util/retryFunc"
	"github.com/devtron-labs/devtron/util/sliceUtil"
	"go.opentelemetry.io/otel"
	"io/ioutil"
	"log"
	"os"
	"path"
	"path/filepath"
	"strings"
	"time"

	"go.uber.org/zap"
)

const SSH_PRIVATE_KEY_FILE_NAME = "ssh_pvt_key"
const SSH_KEY_FILE_PATH = "/ssh_key"

// GitOpsHelper GitOps Helper maintains the auth creds in state and is used by implementation of
// git client implementations and GitFactory
type GitOpsHelper struct {
	Auth              *git.BasicAuth
	SshHost           string
	logger            *zap.SugaredLogger
	gitCommandManager git.GitCommandManager
	tlsConfig         *bean.TLSConfig
	isTlsEnabled      bool
}

func NewGitOpsHelperImpl(auth *git.BasicAuth, sshHost string, logger *zap.SugaredLogger, tlsConfig *bean.TLSConfig, isTlsEnabled bool) *GitOpsHelper {
	return &GitOpsHelper{
		Auth:              auth,
		SshHost:           strings.TrimRight(sshHost, "/"),
		logger:            logger,
		gitCommandManager: git.NewGitCommandManager(logger),
		tlsConfig:         tlsConfig,
		isTlsEnabled:      isTlsEnabled,
	}
}

func (impl *GitOpsHelper) SetAuth(auth *git.BasicAuth) {
	impl.Auth = auth
}

func (impl *GitOpsHelper) SetSShHost(sshHost string) {
	impl.SshHost = strings.TrimRight(sshHost, "/")
}

func (impl *GitOpsHelper) IsApiDisabled() bool {
	return impl.Auth.Password == "" && impl.Auth.AuthMode == constants.AUTH_MODE_SSH
}

func (impl *GitOpsHelper) GetCloneDirectory(targetDir string) (clonedDir string) {
	start := time.Now()
	defer func() {
		util.TriggerGitOpsMetrics("GetCloneDirectory", "GitService", start, nil)
	}()
	clonedDir = filepath.Join(bean2.GIT_WORKING_DIR, targetDir)
	return clonedDir
}

func (impl *GitOpsHelper) CloneAndGetDefaultBranch(url, targetDir string) (clonedDir, defaultBranch string, err error) {
	start := time.Now()
	clonedDir, defaultBranch, err = impl.cloneAndGetDefaultBranch(url, targetDir)
	util.TriggerGitOpsMetrics("CloneAndGetDefaultBranch", "GitService", start, err)
	return clonedDir, defaultBranch, err
}

func (impl *GitOpsHelper) cloneAndGetDefaultBranch(url, targetDir string) (clonedDir, defaultBranch string, err error) {
	var gitCtx git.GitContext
	gitCtx, clonedDir, err = impl.cloneAndFetch(url, targetDir)
	if err != nil {
		impl.logger.Errorw("error in git clone and fetch", "err", err, "url", url, "targetDir", targetDir)
		return clonedDir, defaultBranch, err
	}
	var isEmptyRepo bool
	defaultBranch, isEmptyRepo, err = impl.getDefaultTargetRevision(gitCtx, clonedDir, url)
	if err != nil {
		impl.logger.Errorw("no branch found in git repo", "err", err, "clonedDir", clonedDir)
		return clonedDir, defaultBranch, err
	}
	if !isEmptyRepo {
		_, errMsg, err := impl.pullFromBranch(gitCtx, clonedDir, defaultBranch)
		if err != nil {
			impl.logger.Errorw("error on git pull", "err", err, "errMsg", errMsg, "clonedDir", clonedDir, "branch", defaultBranch)
			return clonedDir, defaultBranch, err
		}
	} else {
		impl.logger.Infow("empty repository, no branch found", "clonedDir", clonedDir, "defaultBranch", defaultBranch)
	}
	return clonedDir, defaultBranch, nil
}

func (impl *GitOpsHelper) Clone(url, targetDir, targetRevision string) (clonedDir string, err error) {
	start := time.Now()
	defer func() {
		util.TriggerGitOpsMetrics("Clone", "GitService", start, err)
	}()
	var gitCtx git.GitContext
	gitCtx, clonedDir, err = impl.cloneAndFetch(url, targetDir)
	if err != nil {
		impl.logger.Errorw("error in git clone and fetch", "url", url, "targetDir", targetDir, "err", err)
		return clonedDir, err
	}
	err = impl.pullFromTargetRevision(gitCtx, clonedDir, targetRevision)
	if err != nil {
		impl.logger.Errorw("error in git pull from target revision", "clonedDir", clonedDir, "targetRevision", targetRevision, "err", err)
		return clonedDir, err
	}
	return clonedDir, nil
}

func (impl *GitOpsHelper) cloneAndFetch(url, targetDir string) (ctx git.GitContext, clonedDir string, err error) {
	if impl.SshHost != "" {
		url, err = bean2.AddUserNameToUrl(url, impl.Auth.Username)
		if err != nil {
			return ctx, clonedDir, err
		}
	}
	impl.logger.Debugw("git checkout ", "url", url, "dir", targetDir)
	clonedDir = filepath.Join(bean2.GIT_WORKING_DIR, targetDir)
	ctx = git.BuildGitContext(context.Background()).WithCredentials(impl.Auth).
		WithTLSData(impl.tlsConfig.CaData, impl.tlsConfig.TLSKeyData, impl.tlsConfig.TLSCertData, impl.isTlsEnabled)
	err = impl.init(ctx, clonedDir, url, false)
	if err != nil {
		return ctx, clonedDir, err
	}
	_, err = impl.CreateSshFileIfNotExistsAndConfigureSshCommand(ctx, clonedDir, impl.Auth.SshKey)
	if err != nil {
		return ctx, clonedDir, err
	}
	_, errMsg, err := impl.gitCommandManager.Fetch(ctx, clonedDir)
	if errMsg != "" {
		impl.logger.Errorw("error in git fetch command", "errMsg", errMsg, "err", err)
		return ctx, clonedDir, fmt.Errorf(errMsg)
	} else if err != nil {
		impl.logger.Errorw("error in git fetch command", "clonedDir", clonedDir, "url", url, "err", err)
		return ctx, clonedDir, fmt.Errorf("error in git fetch command: %v", err)
	}
	impl.logger.Debugw("git fetch completed, pulling master branch data from remote origin")
	return ctx, clonedDir, nil
}

func (impl *GitOpsHelper) pullFromTargetRevision(ctx git.GitContext, clonedDir, targetRevision string) (err error) {
	branch, isEmptyRepo, err := impl.getSanitisedTargetRevision(ctx, clonedDir, targetRevision)
	if err != nil {
		impl.logger.Errorw("no branch found in git repo", "clonedDir", clonedDir, "targetRevision", targetRevision, "err", err)
		return err
	}
	if !isEmptyRepo {
		_, errMsg, err := impl.pullFromBranch(ctx, clonedDir, branch)
		if err != nil {
			impl.logger.Errorw("error on git pull", "clonedDir", clonedDir, "branch", branch, "errMsg", errMsg, "err", err)
			return err
		}
	}
	return nil
}

func (impl *GitOpsHelper) Pull(repoRoot, targetRevision string) (err error) {
	start := time.Now()
	ctx := git.BuildGitContext(context.Background()).WithCredentials(impl.Auth).
		WithTLSData(impl.tlsConfig.CaData, impl.tlsConfig.TLSKeyData, impl.tlsConfig.TLSCertData, impl.isTlsEnabled)
	_, err = impl.CreateSshFileIfNotExistsAndConfigureSshCommand(ctx, repoRoot, impl.Auth.SshKey)
	if err != nil {
		return err
	}
	err = impl.gitCommandManager.Pull(ctx, targetRevision, repoRoot)
	if err != nil {
		util.TriggerGitOpsMetrics("Pull", "GitService", start, err)
		return err
	}
	return nil
}

const PushErrorMessage = "failed to push some refs"

func (impl *GitOpsHelper) CommitAndPushAllChanges(ctx context.Context, repoRoot, targetRevision, commitMsg, name, emailId string) (commitHash string, err error) {
	start := time.Now()
	newCtx, span := otel.Tracer("orchestrator").Start(ctx, "GitOpsHelper.CommitAndPushAllChanges")
	defer func() {
		util.TriggerGitOpsMetrics("CommitAndPushAllChanges", "GitService", start, err)
		span.End()
	}()
	gitCtx := git.BuildGitContext(newCtx).
		WithCredentials(impl.Auth).
		WithTLSData(impl.tlsConfig.CaData, impl.tlsConfig.TLSKeyData, impl.tlsConfig.TLSCertData, impl.isTlsEnabled)

	_, err = impl.CreateSshFileIfNotExistsAndConfigureSshCommand(gitCtx, repoRoot, impl.Auth.SshKey)
	if err != nil {
		return "", err
	}
	commitHash, err = impl.gitCommandManager.CommitAndPush(gitCtx, repoRoot, targetRevision, commitMsg, name, emailId)
	if err != nil && strings.Contains(err.Error(), PushErrorMessage) {
		impl.logger.Errorw("error in commit and push", repoRoot)
		return commitHash, retryFunc.NewRetryableError(err)
	}
	return commitHash, nil
}

func (impl *GitOpsHelper) AddNewRemoteAndPush(ctx context.Context, repoRoot, newRemoteName, newRemoteURL string) (err error) {
	start := time.Now()
	defer func() {
		util.TriggerGitOpsMetrics("AddNewRemoteAndPush", "GitService", start, err)
	}()
	gitCtx := git.BuildGitContext(ctx).WithCredentials(impl.Auth)

	_, err = impl.CreateSshFileIfNotExistsAndConfigureSshCommand(gitCtx, repoRoot, impl.Auth.SshKey)
	if err != nil {
		return err
	}
	err = impl.gitCommandManager.GitAddNewRemoteAndForcePush(gitCtx, repoRoot, newRemoteName, newRemoteURL)
	if err != nil {
		impl.logger.Errorw("error, GitAddNewRemoteAndForcePush", "repoRoot", repoRoot, "newRemoteName", newRemoteName, "newRemoteURL", newRemoteURL, "err", err)
		return retryFunc.NewRetryableError(err)
	}
	return nil
}

func (impl *GitOpsHelper) pullFromBranch(ctx git.GitContext, rootDir, branch string) (string, string, error) {
	start := time.Now()
	response, errMsg, err := impl.gitCommandManager.PullCli(ctx, rootDir, branch)
	if err != nil {
		util.TriggerGitOpsMetrics("Pull", "GitCli", start, err)
		impl.logger.Errorw("error on git pull", "branch", branch, "err", err)
		return response, errMsg, err
	}
	return response, errMsg, err
}

func (impl *GitOpsHelper) init(ctx git.GitContext, rootDir string, remoteUrl string, isBare bool) error {
	//-----------------
	start := time.Now()
	var err error
	defer func() {
		util.TriggerGitOpsMetrics("Init", "GitCli", start, err)
	}()
	err = os.RemoveAll(rootDir)
	if err != nil {
		impl.logger.Errorw("error in cleaning rootDir", "err", err)
		return err
	}
	err = os.MkdirAll(rootDir, 0755)
	if err != nil {
		return err
	}

	return impl.gitCommandManager.AddRepo(ctx, rootDir, remoteUrl, isBare)
}

func (impl *GitOpsHelper) getRemoteBranchList(ctx git.GitContext, rootDir string) ([]string, error) {
	validBranches := make([]string, 0)
	response, errMsg, err := impl.gitCommandManager.ListBranch(ctx, rootDir)
	if err != nil {
		impl.logger.Errorw("error on git pull", "response", response, "errMsg", errMsg, "err", err)
		return validBranches, err
	}
	branches := strings.Split(response, "\n")
	validBranches = sliceUtil.Filter(validBranches, branches, func(item string) bool {
		return len(item) != 0
	})
	impl.logger.Infow("total branch available in git repo", "branch length", len(validBranches), "branches", validBranches)
	return validBranches, nil
}

func (impl *GitOpsHelper) getDefaultTargetRevision(ctx git.GitContext, rootDir, url string) (defaultBranch string, isEmptyRepo bool, err error) {
	validBranches, err := impl.getRemoteBranchList(ctx, rootDir)
	if err != nil {
		impl.logger.Errorw("error in getting remote branch list", "err", err, "rootDir", rootDir)
		return defaultBranch, isEmptyRepo, err
	}
	defaultBranch, isEmptyRepo, err = impl.getDefaultBranch(ctx, rootDir, validBranches)
	if err != nil {
		impl.logger.Errorw("error in getting default branch", "err", err, "branches", validBranches)
		return defaultBranch, isEmptyRepo, err
	}
	if isEmptyRepo {
		// if no branch found then it's a new git repo,
		// return default branch.
		// default branch is always the current branch after git clone.
		defaultBranch, err = impl.getDefaultBranchForEmptyRepo(ctx, rootDir, url)
		if err != nil {
			impl.logger.Errorw("error in getting default branch for empty repo", "err", err, "url", url)
			return defaultBranch, isEmptyRepo, err
		}
	}
	if strings.HasPrefix(defaultBranch, "origin/") {
		defaultBranch = strings.TrimPrefix(defaultBranch, "origin/")
	}
	return defaultBranch, isEmptyRepo, err
}

func (impl *GitOpsHelper) getSanitisedTargetRevision(ctx git.GitContext, rootDir, targetRevision string) (string, bool, error) {
	validBranches, err := impl.getRemoteBranchList(ctx, rootDir)
	if err != nil {
		impl.logger.Errorw("error in getting remote branch list", "rootDir", rootDir, "targetRevision", targetRevision, "err", err)
		return "", false, err
	}
	branch, isEmptyRepo, err := impl.getTargetRevision(ctx, rootDir, validBranches, targetRevision)
	if err != nil {
		impl.logger.Errorw("error in getting default branch", "branches", validBranches, "targetRevision", targetRevision, "err", err)
		return "", isEmptyRepo, err
	}
	if strings.HasPrefix(branch, "origin/") {
		branch = strings.TrimPrefix(branch, "origin/")
	}
	return branch, isEmptyRepo, nil
}

func (impl *GitOpsHelper) getDefaultBranchForEmptyRepo(ctx git.GitContext, cloneDir, repoUrl string) (branch string, err error) {
	err = os.RemoveAll(cloneDir)
	if err != nil {
		impl.logger.Errorw("error in cleaning cloneDir", "err", err, "cloneDir", cloneDir)
		return "", err
	}
	// create clone directory
	err = os.MkdirAll(cloneDir, os.ModePerm)
	if err != nil {
		impl.logger.Errorw("error in creating cloneDir", "err", err, "cloneDir", cloneDir)
		return "", err
	}
	// create the ssh file if not exists and get ssh command
	sshCmdEnv, err := impl.CreateSshFileIfNotExistsAndGetSshCommand(impl.Auth.SshKey)
	if err != nil {
		impl.logger.Errorw("error in getting ssh command args", "err", err)
		return "", err
	}
	cloneResponse, errMsg, err := impl.gitCommandManager.Clone(ctx, cloneDir, repoUrl, sshCmdEnv)
	if err != nil {
		impl.logger.Errorw("error on git clone", "url", repoUrl, "cloneDir", cloneDir, "response", cloneResponse, "errMsg", errMsg, "err", err)
		return "", err
	} else if errMsg != "" {
		impl.logger.Errorw("error on git clone", "url", repoUrl, "cloneDir", cloneDir, "response", cloneResponse, "errMsg", errMsg)
		return "", fmt.Errorf(errMsg)
	}
	impl.logger.Debugw("git clone completed, trying to get current branch", "url", repoUrl, "cloneDir", cloneDir, "response", cloneResponse)
	response, errMsg, err := impl.gitCommandManager.GetCurrentBranch(ctx, cloneDir)
	if err != nil {
		impl.logger.Errorw("error on git pull", "url", repoUrl, "cloneDir", cloneDir, "response", response, "errMsg", errMsg, "err", err)
		return response, err
	}
	if strings.TrimSpace(response) != "" {
		branch = strings.TrimSpace(response)
		impl.logger.Debugw("default branch found in git repo", "branch", branch, "url", repoUrl, "cloneDir", cloneDir)
		return branch, nil
	}
	// if no branch found then return default branch
	impl.logger.Warnw("no branch found in git repo, returning default branch", "branch", util.GetDefaultTargetRevision(), "url", repoUrl, "cloneDir", cloneDir)
	return util.GetDefaultTargetRevision(), nil
}

// getDefaultBranch returns the default branch of the git repository.
// - CASE 1: If the git repository has some branches, it will return the default branch.
// - CASE 2: If the git repository has no branches, it will return the current branch.
func (impl *GitOpsHelper) getDefaultBranch(ctx git.GitContext, rootDir string, branches []string) (branch string, isEmptyRepo bool, err error) {
	// if git repo has some branch, take pull of the default branch.
	// but eventually commit will push into TargetRevision branch.
	if len(branches) != 0 {
		// if no branch found then try to get the default branch from git
		impl.logger.Debugw("no branch found in git repo, trying to get default branch", "branches", branches)
		response, errMsg, err := impl.gitCommandManager.GetDefaultBranch(ctx, rootDir)
		if err == nil && strings.TrimSpace(response) != "" {
			branch = strings.TrimSpace(response)
			impl.logger.Debugw("default branch found in git repo", "branch", branch)
			return branch, isEmptyRepo, nil
		}
		impl.logger.Errorw("error on git pull", "response", response, "errMsg", errMsg, "err", err)
		// if error is not nil, then it means no default branch found in git repo.
		// so we will return the first branch as default branch.

		// As the origin/HEAD is found in the branches list,
		// we will consider it as an empty repository.
		isEmptyRepo = true
		impl.logger.Warnw("no default branch found in git repo, returning first branch", "branches", branches)
		branch = strings.TrimSpace(branches[0])
		return branch, isEmptyRepo, nil
	}
	isEmptyRepo = true
	impl.logger.Warnw("no branch found in git repo, returning default branch", "branch", util.GetDefaultTargetRevision())
	branch = util.GetDefaultTargetRevision()
	return branch, isEmptyRepo, err
}

func (impl *GitOpsHelper) getTargetRevision(ctx git.GitContext, rootDir string, branches []string, targetRevision string) (branch string, isEmptyRepo bool, err error) {
	// the preferred branch is bean.TargetRevisionMaster
	for _, item := range branches {
		if len(targetRevision) != 0 && item == targetRevision {
			return targetRevision, isEmptyRepo, nil
		} else if util.IsDefaultTargetRevision(item) {
			return util.GetDefaultTargetRevision(), isEmptyRepo, nil
		}
	}
	branch, isEmptyRepo, err = impl.getDefaultBranch(ctx, rootDir, branches)
	if err != nil {
		impl.logger.Errorw("error in getting default branch", "branches", branches, "targetRevision", targetRevision, "err", err)
		return "", isEmptyRepo, err
	}
	return branch, isEmptyRepo, err
}

/*
SanitiseCustomGitRepoURL
- It will sanitise the user given repository url based on GitOps provider

Case BITBUCKET_PROVIDER:
  - The clone URL format https://<user-name>@bitbucket.org/<workspace-name>/<repo-name>.git
  - Here the <user-name> can differ from user to user. SanitiseCustomGitRepoURL will return the repo url in format : https://bitbucket.org/<workspace-name>/<repo-name>.git

Case AZURE_DEVOPS_PROVIDER:
  - The clone URL format https://<organisation-name>@dev.azure.com/<organisation-name>/<project-name>/_git/<repo-name>
  - Here the <user-name> can differ from user to user. SanitiseCustomGitRepoURL will return the repo url in format : https://dev.azure.com/<organisation-name>/<project-name>/_git/<repo-name>
*/
func SanitiseCustomGitRepoURL(activeGitOpsConfig *apiGitOpsBean.GitOpsConfigDto, gitRepoURL string) (sanitisedGitRepoURL string) {
	sanitisedGitRepoURL = gitRepoURL
	if activeGitOpsConfig.Provider == bean2.BITBUCKET_PROVIDER && strings.Contains(gitRepoURL, fmt.Sprintf("://%s@%s", activeGitOpsConfig.Username, "bitbucket.org/")) {
		sanitisedGitRepoURL = strings.ReplaceAll(gitRepoURL, fmt.Sprintf("://%s@%s", activeGitOpsConfig.Username, "bitbucket.org/"), "://bitbucket.org/")
	}
	if activeGitOpsConfig.Provider == bean2.AZURE_DEVOPS_PROVIDER {
		azureDevopsOrgName := activeGitOpsConfig.Host[strings.LastIndex(activeGitOpsConfig.Host, "/")+1:]
		invalidBaseUrlFormat := fmt.Sprintf("://%s@%s", azureDevopsOrgName, "dev.azure.com/")
		if invalidBaseUrlFormat != "" && strings.Contains(gitRepoURL, invalidBaseUrlFormat) {
			sanitisedGitRepoURL = strings.ReplaceAll(gitRepoURL, invalidBaseUrlFormat, "://dev.azure.com/")
		}
	}
	return sanitisedGitRepoURL
}

func (impl *GitOpsHelper) CreateSshFileIfNotExistsAndConfigureSshCommand(gitCtx git.GitContext, location string, sshPrivateKeyContent string) (string, error) {
	// add private key
	if len(sshPrivateKeyContent) == 0 {
		return "", nil
	}

	sshPrivateKeyPath, err := GetOrCreateSshPrivateKeyOnDisk(sshPrivateKeyContent)
	if err != nil {
		impl.logger.Errorw("error in creating ssh private key", "err", err)
		return sshPrivateKeyPath, err
	}

	//git config core.sshCommand
	_, errorMsg, err := impl.gitCommandManager.ConfigureSshCommand(gitCtx, location, sshPrivateKeyPath)
	if err != nil {
		impl.logger.Errorw("error in configuring ssh command while adding repo", "errorMsg", errorMsg, "err", err)
		return sshPrivateKeyPath, err
	}

	return sshPrivateKeyPath, nil
}

func (impl *GitOpsHelper) CreateSshFileIfNotExistsAndGetSshCommand(sshPrivateKeyContent string) (string, error) {
	// add private key
	if len(sshPrivateKeyContent) == 0 {
		return "", nil
	}

	sshPrivateKeyPath, err := GetOrCreateSshPrivateKeyOnDisk(sshPrivateKeyContent)
	if err != nil {
		impl.logger.Errorw("error in creating ssh private key", "err", err)
		return "", err
	}

	// git config core.sshCommand
	return impl.gitCommandManager.GetSshCommand(sshPrivateKeyPath), nil
}

func GetOrCreateSshPrivateKeyOnDisk(sshPrivateKeyContent string) (privateKeyPath string, err error) {
	sshPrivateKeyFolderPath := path.Join(bean2.GIT_WORKING_DIR + SSH_KEY_FILE_PATH)
	sshPrivateKeyFilePath := path.Join(sshPrivateKeyFolderPath, SSH_PRIVATE_KEY_FILE_NAME)

	// if file exists then return
	if _, err := os.Stat(sshPrivateKeyFilePath); os.IsExist(err) {
		return sshPrivateKeyFilePath, nil
	}

	// create dirs
	err = os.MkdirAll(sshPrivateKeyFolderPath, 0755)
	if err != nil {
		return "", err
	}

	log.Println("writing sshPrivateKeyContent", len(sshPrivateKeyContent))
	// create file with content
	err = ioutil.WriteFile(sshPrivateKeyFilePath, []byte(sshPrivateKeyContent), 0600)
	if err != nil {
		return "", err
	}

	return sshPrivateKeyFilePath, nil
}
