/*
 * Copyright (c) 2024. Devtron Inc.
 */

package git

import (
	"context"
	"crypto/tls"
	"encoding/base64"
	"errors"
	"fmt"
	"github.com/devtron-labs/bitbucketdc-gosdk/swagger"
	bean2 "github.com/devtron-labs/devtron/api/bean/gitOps"
	"github.com/devtron-labs/devtron/pkg/deployment/gitOps/git/bean"
	git "github.com/devtron-labs/devtron/pkg/deployment/gitOps/git/commandManager"
	"github.com/devtron-labs/devtron/util"
	globalUtil "github.com/devtron-labs/devtron/util"
	"github.com/devtron-labs/devtron/util/retryFunc"
	"github.com/devtron-labs/go-bitbucket"
	"go.uber.org/zap"
	"math/rand"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"
)

const (
	HTTP_URL_PROTOCOL            = "http://"
	HTTPS_URL_PROTOCOL           = "https://"
	BITBUCKET_CLONE_BASE_URL     = "https://bitbucket.org/"
	BITBUCKET_GITOPS_DIR         = "bitbucketGitOps"
	BITBUCKET_COMMIT_TIME_LAYOUT = "2001-01-01T10:00:00+00:00"
	BEARER                       = "Bearer "
	BASIC                        = "Basic "
)

type GitBitbucketClient struct {
	apiClient          BitBucketApiClient
	logger             *zap.SugaredLogger
	gitOpsHelper       *GitOpsHelper
	globalEnvVariables *globalUtil.GlobalEnvVariables
}

type BitBucketApiClient interface {
	CommitAndPush(ctx git.GitContext, config *ChartConfig, gitOpsConfig *bean2.GitOpsConfigDto, cloneDir string, authorBitbucket string) (string, time.Time, error)
	CleanUp(filePath string)
	// GetDirPathForCommit is used to get the directory path for committing files in bitbucket,
	// which is then used by CommitAndPush method.
	//	params:
	//		- config: containing details of GitOps repository and commit file.
	//	returns:
	//		- config: Updated ChartConfig with target revision (default branch).
	//			This is used to commit the file to the default branch.
	//		- cloneDir: Directory where the repository is cloned
	//		- bitbucketCommitFilePath: Full path where the commit file will be written
	GetDirPathForCommit(config *ChartConfig) (*ChartConfig, string, string, error)
	GetRepo(repoOptions *bitbucket.RepositoryOptions) (string, error)
	CreateRepo(repoOptions *bitbucket.RepositoryOptions) (string, error)
	DeleteRepo(config *bean2.GitOpsConfigDto) error
}

func NewGitBitbucketDcClient(username, token, host string, project string, logger *zap.SugaredLogger, globalEnvVariables *globalUtil.GlobalEnvVariables, gitOpsHelper *GitOpsHelper, usePat bool) GitBitbucketClient {

	apiPath, _ := url.JoinPath(host, "rest")
	headers := make(map[string]string)
	headers["referer"] = host

	addAuthHeader(username, token, usePat, headers)

	dcClient := swagger.NewAPIClient(&swagger.Configuration{
		BasePath:      apiPath,
		Host:          "",
		Scheme:        "https",
		DefaultHeader: headers,
		UserAgent:     "",
		HTTPClient:    nil,
	})
	logger.Infow("bitbucket client created", "clientDetails", dcClient)
	return GitBitbucketClient{
		apiClient: &BitBucketV1Client{
			dcClient:     dcClient,
			DcHost:       strings.TrimRight(host, "/"),
			ProjectId:    project,
			logger:       logger,
			gitOpsHelper: gitOpsHelper,
		},
		logger:             logger,
		globalEnvVariables: globalEnvVariables,
		gitOpsHelper:       gitOpsHelper,
	}
}

func addAuthHeader(username string, token string, usePat bool, headers map[string]string) {
	if usePat {
		a := BuildBearerAuth(token)
		headers["Authorization"] = a
	} else {
		headers["Authorization"] = BuildBasicAuth(username, token)
	}
}

func BuildBearerAuth(token string) string {
	return BEARER + token
}

func BuildBasicAuth(username string, token string) string {
	basicToken := username + ":" + token
	enc := base64.StdEncoding.EncodeToString([]byte(basicToken))
	return BASIC + enc
}

func NewGitBitbucketClient(username, token, host string, logger *zap.SugaredLogger, globalEnvVariables *globalUtil.GlobalEnvVariables, gitOpsHelper *GitOpsHelper, tlsConfig *tls.Config) GitBitbucketClient {
	coreClient := bitbucket.NewBasicAuth(username, token)
	httpClient := util.GetHTTPClientWithTLSConfig(tlsConfig)
	coreClient.HttpClient = httpClient
	logger.Infow("bitbucket client created", "clientDetails", coreClient)
	return GitBitbucketClient{
		apiClient: &BitBucketV2Client{
			client:       coreClient,
			logger:       logger,
			gitOpsHelper: gitOpsHelper,
		},
		logger:             logger,
		globalEnvVariables: globalEnvVariables,
		gitOpsHelper:       gitOpsHelper,
	}
}

func (impl GitBitbucketClient) getClient() BitBucketApiClient {
	return impl.apiClient
}

func (impl GitBitbucketClient) DeleteRepository(config *bean2.GitOpsConfigDto) (err error) {
	start := time.Now()
	defer func() {
		globalUtil.TriggerGitOpsMetrics("DeleteRepository", "GitBitbucketClient", start, err)
	}()
	err = impl.apiClient.DeleteRepo(config)
	if err != nil {
		impl.logger.Errorw("error in deleting repo gitlab", "repoName", config.GitRepoName, "err", err)
	}
	return err
}

func (impl GitBitbucketClient) GetRepoUrl(config *bean2.GitOpsConfigDto) (repoUrl string, isRepoEmpty bool, err error) {
	start := time.Now()
	defer func() {
		globalUtil.TriggerGitOpsMetrics("GetRepoUrl", "GitBitbucketClient", start, err)
	}()

	repoOptions := &bitbucket.RepositoryOptions{
		Owner:    config.BitBucketWorkspaceId,
		Project:  config.BitBucketProjectKey,
		RepoSlug: config.GitRepoName,
	}
	repoUrl, exists, err := impl.repoExists(repoOptions)
	if err != nil {
		return "", isRepoEmpty, err
	} else if !exists {
		return "", isRepoEmpty, fmt.Errorf("%s :repo not found", repoOptions.RepoSlug)
	}
	return repoUrl, isRepoEmpty, nil

}

func (impl GitBitbucketClient) CreateRepository(ctx context.Context, config *bean2.GitOpsConfigDto) (url string, isNew bool, isEmpty bool, detailedErrorGitOpsConfigActions DetailedErrorGitOpsConfigActions) {
	var err error
	start := time.Now()

	detailedErrorGitOpsConfigActions.StageErrorMap = make(map[string]error)

	workSpaceId := config.BitBucketWorkspaceId
	projectKey := config.BitBucketProjectKey
	repoOptions := &bitbucket.RepositoryOptions{
		Owner:       workSpaceId,
		RepoSlug:    config.GitRepoName,
		Scm:         "git",
		IsPrivate:   "true",
		Description: config.Description,
		Project:     projectKey,
	}

	repoUrl, repoExists, err := impl.repoExists(repoOptions)
	if err != nil {
		impl.logger.Errorw("error in communication with bitbucket", "repoOptions", repoOptions, "err", err)
		detailedErrorGitOpsConfigActions.StageErrorMap[bean.GetRepoUrlStage] = err
		util.TriggerGitOpsMetrics("CreateRepository", "GitBitbucketClient", start, err)
		return "", false, isEmpty, detailedErrorGitOpsConfigActions
	}
	if repoExists {
		detailedErrorGitOpsConfigActions.SuccessfulStages = append(detailedErrorGitOpsConfigActions.SuccessfulStages, bean.GetRepoUrlStage)
		util.TriggerGitOpsMetrics("CreateRepository", "GitBitbucketClient", start, nil)
		return repoUrl, false, isEmpty, detailedErrorGitOpsConfigActions
	}

	repoUrl, err = impl.apiClient.CreateRepo(repoOptions)
	if err != nil {
		impl.logger.Errorw("error in creating repo bitbucket", "repoOptions", repoOptions, "err", err)
		detailedErrorGitOpsConfigActions.StageErrorMap[bean.CreateRepoStage] = err
		repoUrl, repoExists, err = impl.repoExists(repoOptions)
		if err != nil {
			impl.logger.Errorw("error in creating repo bitbucket", "repoOptions", repoOptions, "err", err)
		}
		if err != nil || !repoExists {
			util.TriggerGitOpsMetrics("CreateRepository", "GitBitbucketClient", start, err)
			return "", true, isEmpty, detailedErrorGitOpsConfigActions
		}
	}

	impl.logger.Infow("repo created ", "repoUrl", repoUrl)
	detailedErrorGitOpsConfigActions.SuccessfulStages = append(detailedErrorGitOpsConfigActions.SuccessfulStages, bean.CreateRepoStage)

	validated, repoUrl, err := impl.ensureProjectAvailabilityOnHttp(repoOptions, config.Host)
	if err != nil {
		impl.logger.Errorw("error in ensuring project availability bitbucket", "repoName", repoOptions.RepoSlug, "err", err)
		detailedErrorGitOpsConfigActions.StageErrorMap[bean.CloneHttpStage] = err
		util.TriggerGitOpsMetrics("CreateRepository", "GitBitbucketClient", start, err)
		return "", true, isEmpty, detailedErrorGitOpsConfigActions
	}
	if !validated {
		err = fmt.Errorf("unable to validate project:%s in given time", config.GitRepoName)
		detailedErrorGitOpsConfigActions.StageErrorMap[bean.CloneHttpStage] = err
		util.TriggerGitOpsMetrics("CreateRepository", "GitBitbucketClient", start, err)
		return "", true, isEmpty, detailedErrorGitOpsConfigActions
	}
	detailedErrorGitOpsConfigActions.SuccessfulStages = append(detailedErrorGitOpsConfigActions.SuccessfulStages, bean.CloneHttpStage)
	callback := func(retriesLeft int) error {
		_, err = impl.CreateReadme(ctx, config, "")
		return err
	}
	err = retryFunc.Retry(callback,
		impl.isRetryableCreateReadMeError,
		impl.globalEnvVariables.ArgoGitCommitRetryCountOnConflict,
		time.Duration(impl.globalEnvVariables.ArgoGitCommitRetryDelayOnConflict)*time.Second,
		impl.logger)
	if err != nil {
		impl.logger.Errorw("error in creating readme bitbucket", "repoName", repoOptions.RepoSlug, "err", err)
		detailedErrorGitOpsConfigActions.StageErrorMap[bean.CreateReadmeStage] = err
		util.TriggerGitOpsMetrics("CreateRepository", "GitBitbucketClient", start, err)
		return "", true, isEmpty, detailedErrorGitOpsConfigActions
	}
	detailedErrorGitOpsConfigActions.SuccessfulStages = append(detailedErrorGitOpsConfigActions.SuccessfulStages, bean.CreateReadmeStage)

	validated, err = impl.ensureProjectAvailabilityOnSsh(repoOptions, repoUrl, config.TargetRevision)
	if err != nil {
		impl.logger.Errorw("error in ensuring project availability bitbucket", "project", config.GitRepoName, "err", err)
		detailedErrorGitOpsConfigActions.StageErrorMap[bean.CloneSshStage] = err
		util.TriggerGitOpsMetrics("CreateRepository", "GitBitbucketClient", start, err)
		return "", true, isEmpty, detailedErrorGitOpsConfigActions
	}
	if !validated {
		err = fmt.Errorf("unable to validate project:%s in given time", config.GitRepoName)
		detailedErrorGitOpsConfigActions.StageErrorMap[bean.CloneSshStage] = err
		util.TriggerGitOpsMetrics("CreateRepository", "GitBitbucketClient", start, err)
		return "", true, isEmpty, detailedErrorGitOpsConfigActions
	}
	detailedErrorGitOpsConfigActions.SuccessfulStages = append(detailedErrorGitOpsConfigActions.SuccessfulStages, bean.CloneSshStage)
	util.TriggerGitOpsMetrics("CreateRepository", "GitBitbucketClient", start, nil)
	return repoUrl, true, isEmpty, detailedErrorGitOpsConfigActions
}

func (impl GitBitbucketClient) isRetryableCreateReadMeError(err error) bool {
	if retryErr := (&retryFunc.RetryableError{}); errors.As(err, &retryErr) {
		return true
	}
	return false
}

func (impl GitBitbucketClient) repoExists(repoOptions *bitbucket.RepositoryOptions) (repoUrl string, exists bool, err error) {

	start := time.Now()
	defer func() {
		globalUtil.TriggerGitOpsMetrics("repoExists", "GitBitbucketClient", start, err)
	}()

	repoUrl, err = impl.apiClient.GetRepo(repoOptions)
	if errors.Is(err, BitbucketRepoNotFoundError) {
		return "", false, nil
	} else if err != nil {
		return "", false, err
	}
	return repoUrl, true, nil
}

func (impl GitBitbucketClient) ensureProjectAvailabilityOnHttp(repoOptions *bitbucket.RepositoryOptions, dcHostURL string) (bool, string, error) {
	for count := 0; count < 5; count++ {
		gitRepoUrl, exists, err := impl.repoExists(repoOptions)
		if err == nil && exists {
			impl.logger.Infow("repo validated successfully on https")
			return true, gitRepoUrl, nil
		} else if err != nil {
			impl.logger.Errorw("error in validating repo bitbucket", "repoDetails", repoOptions, "err", err)
			return false, gitRepoUrl, err
		} else {
			impl.logger.Errorw("repo not available on http", "repoDetails", repoOptions)
		}
		time.Sleep(10 * time.Second)
	}
	return false, "", nil
}

func getDir() string {
	/* #nosec */
	r1 := rand.New(rand.NewSource(time.Now().UnixNano())).Int63()
	return strconv.FormatInt(r1, 10)
}

func (impl GitBitbucketClient) CreateFirstCommitOnHead(ctx context.Context, config *bean2.GitOpsConfigDto, customGitRepo string) (string, error) {
	return impl.createReadme(ctx, config, customGitRepo, true)
}

func (impl GitBitbucketClient) CreateReadme(ctx context.Context, config *bean2.GitOpsConfigDto, customGitRepo string) (string, error) {
	return impl.createReadme(ctx, config, customGitRepo, false)
}

func (impl GitBitbucketClient) createReadme(ctx context.Context, config *bean2.GitOpsConfigDto, customGitRepo string, useDefaultBranch bool) (string, error) {
	var err error
	start := time.Now()
	defer func() {
		globalUtil.TriggerGitOpsMetrics("CreateReadme", "GitBitbucketClient", start, err)
	}()

	cfg := &ChartConfig{
		ChartName:      config.GitRepoName,
		ChartLocation:  "",
		FileName:       "README.md",
		FileContent:    "@devtron",
		ReleaseMessage: "pushing readme",
		ChartRepoName:  config.GitRepoName,
		TargetRevision: config.TargetRevision,
		UserName:       config.Username,
		UserEmailId:    config.UserEmailId,
	}
	cfg.SetBitBucketBaseDir(getDir())
	// UseDefaultBranch will override the TargetRevision and use the default branch of the repo
	cfg.UseDefaultBranch = useDefaultBranch
	if customGitRepo != "" {
		cfg.ChartRepoURL = customGitRepo
	}
	hash, _, err := impl.CommitValues(ctx, cfg, config, true)
	if err != nil {
		impl.logger.Errorw("error in creating readme bitbucket", "repo", config.GitRepoName, "err", err)
	}
	return hash, err
}

func (impl GitBitbucketClient) ensureProjectAvailabilityOnSsh(repoOptions *bitbucket.RepositoryOptions, repoUrl, targetRevision string) (bool, error) {
	for count := 0; count < 5; count++ {
		_, err := impl.gitOpsHelper.Clone(repoUrl, fmt.Sprintf("/ensure-clone/%s", repoOptions.RepoSlug), targetRevision)
		if err == nil {
			impl.logger.Infow("ensureProjectAvailability clone passed Bitbucket", "try count", count, "repoUrl", repoUrl)
			return true, nil
		}
		impl.logger.Errorw("ensureProjectAvailability clone failed ssh Bitbucket", "try count", count, "err", err)
		time.Sleep(10 * time.Second)
	}
	return false, nil
}

func (impl GitBitbucketClient) CommitValues(ctx context.Context, config *ChartConfig, gitOpsConfig *bean2.GitOpsConfigDto, publishStatusConflictError bool) (commitHash string, commitTime time.Time, err error) {
	return impl.commitValues(ctx, config, gitOpsConfig, publishStatusConflictError)
}

func (impl GitBitbucketClient) commitValues(ctx context.Context, config *ChartConfig, gitOpsConfig *bean2.GitOpsConfigDto, publishStatusConflictError bool) (commitHash string, commitTime time.Time, err error) {
	start := time.Now()
	config, cloneDir, bitbucketCommitFilePath, err := impl.apiClient.GetDirPathForCommit(config)
	defer impl.apiClient.CleanUp(cloneDir)
	if err != nil {
		impl.logger.Errorw("error in getting home dir", "err", err)
		util.TriggerGitOpsMetrics("CommitValues", "GitBitbucketClient", start, err)
		return "", time.Time{}, err
	}
	impl.logger.Debugw("cloned directory success", "config", config)

	err = os.WriteFile(bitbucketCommitFilePath, []byte(config.FileContent), 0666)
	if err != nil {
		impl.logger.Errorw("error in writing bitbucket commit file", "bitbucketCommitFilePath", bitbucketCommitFilePath, "err", err)
		util.TriggerGitOpsMetrics("CommitValues", "GitBitbucketClient", start, err)
		return "", time.Time{}, err
	}

	if len(config.TargetRevision) == 0 {
		config.TargetRevision = util.GetDefaultTargetRevision()
	}
	//bitbucket needs author as - "Name <email-Id>"
	authorBitbucket := fmt.Sprintf("%s <%s>", config.UserName, config.UserEmailId)
	gitCtx := git.BuildGitContext(ctx).WithCredentials(impl.gitOpsHelper.Auth)
	// commit values file
	commitHash, commitTime, err = impl.apiClient.CommitAndPush(gitCtx, config, gitOpsConfig, cloneDir, authorBitbucket)
	if err != nil {
		impl.logger.Errorw("error in committing and pushing file to bitbucket", "err", err)
		if publishStatusConflictError {
			util.TriggerGitOpsMetrics("CommitValues", "GitBitbucketClient", start, err)
		}
		return "", time.Time{}, err
	}
	util.TriggerGitOpsMetrics("CommitValues", "GitBitbucketClient", start, nil)
	return commitHash, commitTime, nil
}
