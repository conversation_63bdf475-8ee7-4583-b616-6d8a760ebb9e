package git

import (
	"errors"
	"fmt"
	bean2 "github.com/devtron-labs/devtron/api/bean/gitOps"
	git "github.com/devtron-labs/devtron/pkg/deployment/gitOps/git/commandManager"
	globalUtil "github.com/devtron-labs/devtron/util"
	"github.com/devtron-labs/devtron/util/retryFunc"
	"github.com/devtron-labs/go-bitbucket"
	"go.uber.org/zap"
	"os"
	"path"
	"path/filepath"
	"strings"
	"time"
)

// used for bitbucket cloud
type BitBucketV2Client struct {
	client       *bitbucket.Client
	logger       *zap.SugaredLogger
	gitOpsHelper *GitOpsHelper
}

func (impl *BitBucketV2Client) CreateRepo(repoOptions *bitbucket.RepositoryOptions) (string, error) {
	var err error
	start := time.Now()
	defer func() {
		globalUtil.TriggerGitOpsMetrics("CreateRepo", "BitBucketV2Client", start, err)
	}()
	_, err = impl.client.Repositories.Repository.Create(repoOptions)
	repoUrl := impl.getRepoUrl(repoOptions)
	return repoUrl, err
}

func (impl *BitBucketV2Client) GetRepo(repoOptions *bitbucket.RepositoryOptions) (string, error) {
	var err error
	start := time.Now()

	_, err = impl.client.Repositories.Repository.Get(repoOptions)
	if err != nil {
		if !impl.isBitbucketRepoNotFoundError(err) {
			globalUtil.TriggerGitOpsMetrics("GetRepo", "BitBucketV2Client", start, err)
		}
		return "", BitbucketRepoNotFoundError
	}

	repoUrl := impl.getRepoUrl(repoOptions)
	globalUtil.TriggerGitOpsMetrics("GetRepo", "BitBucketV2Client", start, nil)
	return repoUrl, nil
}

func (impl *BitBucketV2Client) isBitbucketRepoNotFoundError(err error) bool {
	if err != nil && strings.Contains(err.Error(), BitbucketRepoNotFoundError.Error()) {
		return true
	}
	return false
}

func (impl *BitBucketV2Client) getRepoUrl(repoOptions *bitbucket.RepositoryOptions) string {
	repoUrl := fmt.Sprintf(BITBUCKET_CLONE_BASE_URL+"%s/%s.git", repoOptions.Owner, repoOptions.RepoSlug)
	return repoUrl
}

func (impl *BitBucketV2Client) CommitAndPush(ctx git.GitContext, config *ChartConfig, gitOpsConfig *bean2.GitOpsConfigDto, dir string, authorBitbucket string) (string, time.Time, error) {

	var err error
	start := time.Now()
	defer func() {
		globalUtil.TriggerGitOpsMetrics("CommitAndPush", "BitBucketV2Client", start, err)
	}()

	bitbucketCommitFilePath := path.Join(dir, config.FileName)
	fileName := filepath.Join(config.ChartLocation, config.FileName)
	repoWriteOptions := &bitbucket.RepositoryBlobWriteOptions{
		Owner:    gitOpsConfig.BitBucketWorkspaceId,
		RepoSlug: config.ChartRepoName,
		FilePath: bitbucketCommitFilePath,
		FileName: fileName,
		Message:  config.ReleaseMessage,
		Branch:   "master",
		Author:   authorBitbucket,
	}
	repoWriteOptions.WithContext(ctx)
	err = impl.client.Repositories.Repository.WriteFileBlob(repoWriteOptions)
	if err != nil {
		impl.logger.Errorw("error in committing file to bitbucket", "repoWriteOptions", repoWriteOptions, "err", err)
		if e := (&bitbucket.UnexpectedResponseStatusError{}); errors.As(err, &e) && strings.Contains(e.Error(), "500 Internal Server Error") {
			return "", time.Time{}, retryFunc.NewRetryableError(err)
		}
		return "", time.Time{}, err
	}

	commitOptions := &bitbucket.CommitsOptions{
		RepoSlug:    config.ChartRepoName,
		Owner:       gitOpsConfig.BitBucketWorkspaceId,
		Branchortag: "master",
	}
	commits, err := impl.client.Repositories.Commits.GetCommits(commitOptions)
	if err != nil {
		impl.logger.Errorw("error in getting commits from bitbucket", "commitOptions", commitOptions, "err", err)
		return "", time.Time{}, err
	}

	//extracting the latest commit hash from the paginated api response of above method, reference of api & response - https://developer.atlassian.com/bitbucket/api/2/reference/resource/repositories/%7Bworkspace%7D/%7Brepo_slug%7D/commits
	commitsMap, ok := commits.(map[string]interface{})
	if !ok {
		impl.logger.Errorw("unexpected response format from bitbucket", "commits", commits)
		return "", time.Time{}, fmt.Errorf("unexpected response format from bitbucket")
	}

	values, ok := commitsMap["values"]
	if !ok || values == nil {
		impl.logger.Errorw("no values found in bitbucket response", "commits", commits, "commitsMap", commitsMap)
		return "", time.Time{}, fmt.Errorf("no commits found in bitbucket response")
	}

	valuesArray, ok := values.([]interface{})
	if !ok || len(valuesArray) == 0 {
		impl.logger.Errorw("empty values array in bitbucket response", "commits", commits, "values", values)
		return "", time.Time{}, fmt.Errorf("empty commits array in bitbucket response")
	}

	firstCommit, ok := valuesArray[0].(map[string]interface{})
	if !ok {
		impl.logger.Errorw("invalid commit format in bitbucket response", "commits", commits, "firstCommit", valuesArray[0])
		return "", time.Time{}, fmt.Errorf("invalid commit format in bitbucket response")
	}

	commitHash, ok := firstCommit["hash"].(string)
	if !ok || commitHash == "" {
		impl.logger.Errorw("no hash found in commit", "commits", commits, "firstCommit", firstCommit)
		return "", time.Time{}, fmt.Errorf("no hash found in commit")
	}

	dateStr, ok := firstCommit["date"].(string)
	if !ok || dateStr == "" {
		impl.logger.Errorw("no date found in commit", "firstCommit", firstCommit)
		return "", time.Time{}, fmt.Errorf("no date found in commit response")
	}

	commitTime, err := time.Parse(time.RFC3339, dateStr)
	if err != nil {
		impl.logger.Errorw("error in getting commitTime", "dateStr", dateStr, "err", err)
		return "", time.Time{}, err
	}

	return commitHash, commitTime, nil
}

func (impl *BitBucketV2Client) CleanUp(cloneDir string) {
	err := os.RemoveAll(cloneDir)
	if err != nil {
		impl.logger.Errorw("error cleaning work path for git-ops", "err", err, "cloneDir", cloneDir)
	}
}

func (impl *BitBucketV2Client) GetDirPathForCommit(config *ChartConfig) (*ChartConfig, string, string, error) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		impl.logger.Errorw("error in getting home dir", "err", err)
		return config, "", "", err
	}
	bitbucketGitOpsDirPath := path.Join(homeDir, BITBUCKET_GITOPS_DIR, config.GetBitBucketBaseDir())
	osErr := os.MkdirAll(bitbucketGitOpsDirPath, os.ModePerm)
	if osErr != nil {
		impl.logger.Errorw("error in creating bitbucket commit base dir", "bitbucketGitOpsDirPath", bitbucketGitOpsDirPath, "err", osErr)
	}
	bitbucketCommitFilePath := path.Join(bitbucketGitOpsDirPath, config.FileName)
	impl.logger.Debugw("bitbucket commit FilePath", "bitbucketCommitFilePath", bitbucketCommitFilePath)
	return config, bitbucketGitOpsDirPath, bitbucketCommitFilePath, nil
}

func (impl *BitBucketV2Client) DeleteRepo(config *bean2.GitOpsConfigDto) error {
	var err error
	start := time.Now()
	defer func() {
		globalUtil.TriggerGitOpsMetrics("Delete", "BitBucketV2Client", start, err)
	}()

	repoOptions := &bitbucket.RepositoryOptions{
		Owner:     config.BitBucketWorkspaceId,
		RepoSlug:  config.GitRepoName,
		IsPrivate: "true",
		Project:   config.BitBucketProjectKey,
	}
	_, err = impl.client.Repositories.Repository.Delete(repoOptions)
	return err
}
