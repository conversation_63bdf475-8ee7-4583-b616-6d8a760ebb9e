/*
 * Copyright (c) 2024. Devtron Inc.
 */

package adapter

import (
	bean2 "github.com/devtron-labs/devtron/api/bean/gitOps"
	"github.com/devtron-labs/devtron/pkg/deployment/gitOps/git/bean"
)

func ConvertGitOpsConfigToGitConfig(dto *bean2.GitOpsConfigDto) *bean.GitConfig {
	config := &bean.GitConfig{
		GitlabGroupId:         dto.GitLabGroupId,
		GitToken:              dto.Token,
		GitUserName:           dto.Username,
		GithubOrganization:    dto.GitHubOrgId,
		GitProvider:           dto.Provider,
		GitHost:               dto.Host,
		AzureToken:            dto.Token,
		AzureProject:          dto.AzureProjectName,
		BitbucketWorkspaceId:  dto.BitBucketWorkspaceId,
		BitbucketProjectKey:   dto.BitBucketProjectKey,
		IsActiveConfig:        dto.Active,
		SShKey:                dto.SshKey,
		SshHost:               dto.GetSshHost(),
		AuthMode:              dto.AuthMode.ToInternalAuthMode(),
		EnableTLSVerification: dto.EnableTLSVerification,
	}
	if dto.TLSConfig != nil {
		config.CaCert = dto.TLSConfig.CaData
		config.TLSCert = dto.TLSConfig.TLSCertData
		config.TLSKey = dto.TLSConfig.TLSKeyData
	}
	return config
}
