package git

import (
	"context"
	"fmt"
	"github.com/antihax/optional"
	"github.com/devtron-labs/bitbucketdc-gosdk/swagger"
	bean2 "github.com/devtron-labs/devtron/api/bean/gitOps"
	bean3 "github.com/devtron-labs/devtron/client/argocdServer/bean"
	"github.com/devtron-labs/devtron/internal/sql/constants"
	"github.com/devtron-labs/devtron/pkg/deployment/gitOps/git/bean"
	git "github.com/devtron-labs/devtron/pkg/deployment/gitOps/git/commandManager"
	globalUtil "github.com/devtron-labs/devtron/util"
	"github.com/devtron-labs/devtron/util/retryFunc"
	"github.com/devtron-labs/go-bitbucket"
	"go.uber.org/zap"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"time"
)

// BitBucketV1Client is a client for interacting with Bitbucket Data Center API v1.
type BitBucketV1Client struct {
	dcClient     *swagger.APIClient
	DcHost       string
	ProjectId    string
	logger       *zap.SugaredLogger
	gitOpsHelper *GitOpsHelper
}

func (impl *BitBucketV1Client) CommitAndPush(ctx git.GitContext, config *ChartConfig, gitOpsConfig *bean2.GitOpsConfigDto, cloneDir string, authorBitbucket string) (string, time.Time, error) {
	var err error
	start := time.Now()
	defer func() {
		globalUtil.TriggerGitOpsMetrics("CommitAndPush", "BitBucketV1Client", start, err)
	}()

	commitHash, err := impl.gitOpsHelper.CommitAndPushAllChanges(ctx, cloneDir, config.TargetRevision, config.ReleaseMessage, authorBitbucket, config.UserEmailId)
	if err != nil {
		err = retryFunc.NewRetryableError(err)
	}
	return commitHash, time.Now(), err
}

func (impl *BitBucketV1Client) CleanUp(cloneDir string) {
	err := os.RemoveAll(cloneDir)
	if err != nil {
		impl.logger.Errorw("error cleaning work path for git-ops", "err", err, "cloneDir", cloneDir)
	}
}

func (impl *BitBucketV1Client) GetDirPathForCommit(config *ChartConfig) (*ChartConfig, string, string, error) {
	var err error
	start := time.Now()
	defer func() {
		globalUtil.TriggerGitOpsMetrics("GetDirPathForCommit", "BitBucketV1Client", start, err)
	}()
	config, cloneDir, err := impl.getCloneDirForCommit(config)
	if err != nil {
		impl.logger.Errorw("error getting clone dir for commit", "err", err, "repoUrl", config.ChartRepoURL, "targetRevision", config.TargetRevision)
		return nil, "", "", err
	}
	fileName := filepath.Join(config.ChartLocation, config.FileName)
	bitbucketCommitFilePath := path.Join(cloneDir, fileName)
	impl.logger.Debugw("bitbucket commit FilePath", "bitbucketCommitFilePath", bitbucketCommitFilePath)
	return config, cloneDir, bitbucketCommitFilePath, err
}

func (impl *BitBucketV1Client) DeleteRepo(config *bean2.GitOpsConfigDto) error {
	var err error
	start := time.Now()
	defer func() {
		globalUtil.TriggerGitOpsMetrics("DeleteRepo", "BitBucketV1Client", start, err)
	}()

	if impl.gitOpsHelper.IsApiDisabled() {
		return nil
	}
	_, err = impl.dcClient.ProjectApi.DeleteRepository(context.Background(), config.BitBucketProjectKey, config.GitRepoName)
	return err
}

func (impl *BitBucketV1Client) CreateRepo(repoOptions *bitbucket.RepositoryOptions) (string, error) {
	var err error
	start := time.Now()
	defer func() {
		globalUtil.TriggerGitOpsMetrics("CreateRepo", "BitBucketV1Client", start, err)
	}()

	if impl.gitOpsHelper.IsApiDisabled() {

		return "", fmt.Errorf(bean.API_DISABLED_ERROR)
	}
	_, _, err = impl.dcClient.ProjectApi.CreateRepository(context.Background(),
		repoOptions.Project, &swagger.ProjectApiCreateRepositoryOpts{Body: optional.NewInterface(
			swagger.RestRepository{
				Name:          repoOptions.RepoSlug,
				Public:        false,
				Project:       &swagger.RestPullRequestFromRefRepositoryProject{Key: repoOptions.Project},
				Slug:          repoOptions.RepoSlug,
				ScmId:         "git",
				DefaultBranch: bean3.TargetRevisionMaster,
			})})
	repoUrl := impl.getRepoUrlDc(repoOptions.RepoSlug)
	return repoUrl, err
}

func (impl *BitBucketV1Client) GetRepo(repoOptions *bitbucket.RepositoryOptions) (string, error) {
	var err error
	start := time.Now()
	repoUrl := impl.getRepoUrlDc(repoOptions.RepoSlug)
	if impl.gitOpsHelper.IsApiDisabled() {
		return repoUrl, nil
	}
	_, resp, err := impl.dcClient.ProjectApi.GetRepository(context.Background(), repoOptions.Project, repoOptions.RepoSlug)
	if err != nil {
		impl.logger.Errorw("error getting repo", "err", err, "repoUrl", repoUrl, "resp", resp)
		if !impl.isBitbucketRepoNotFoundError(resp) {
			globalUtil.TriggerGitOpsMetrics("GetRepo", "BitBucketV1Client", start, err)
			return "", fmt.Errorf("error: %w, while fetching repo %q", err, repoUrl)
		}
		return "", BitbucketRepoNotFoundError
	}
	globalUtil.TriggerGitOpsMetrics("GetRepo", "BitBucketV1Client", start, nil)
	return repoUrl, nil

}

func (impl *BitBucketV1Client) isBitbucketRepoNotFoundError(resp *http.Response) bool {
	if resp != nil && resp.StatusCode == 404 {
		return true
	}
	return false
}

func (impl *BitBucketV1Client) getCloneDirForCommit(config *ChartConfig) (*ChartConfig, string, error) {
	repoUrl := ""
	if config.ChartRepoURL != "" {
		repoUrl = config.ChartRepoURL
	} else {
		repoUrl = impl.getRepoUrlDc(config.ChartRepoName)
	}
	directory := path.Join(BITBUCKET_GITOPS_DIR, config.GetBitBucketBaseDir())
	if config.UseDefaultBranch {
		cloneDir, defaultBranch, err := impl.gitOpsHelper.CloneAndGetDefaultBranch(repoUrl, directory)
		if err != nil {
			impl.logger.Errorw("error cloning repo", "err", err, "repoUrl", repoUrl)
		}
		config.TargetRevision = defaultBranch
		impl.logger.Debugw("cloned repo", "repoUrl", repoUrl, "cloneDir", cloneDir, "defaultBranch", defaultBranch)
		return config, cloneDir, err
	}
	cloneDir, err := impl.gitOpsHelper.Clone(repoUrl, directory, config.TargetRevision)
	if err != nil {
		impl.logger.Errorw("error cloning repo", "err", err, "repoUrl", repoUrl, "targetRevision", config.TargetRevision)
		return nil, "", err
	}
	impl.logger.Debugw("cloned repo", "repoUrl", repoUrl, "cloneDir", cloneDir, "targetRevision", config.TargetRevision)
	return config, cloneDir, err
}

func (impl *BitBucketV1Client) getRepoUrlDc(repoSlug string) string {
	url := impl.DcHost
	authMode := constants.AUTH_MODE_USERNAME_PASSWORD
	if impl.gitOpsHelper.Auth.AuthMode == constants.AUTH_MODE_SSH || impl.gitOpsHelper.Auth.AuthMode == constants.PAT_AND_SSH {
		url = impl.gitOpsHelper.SshHost
		authMode = constants.AUTH_MODE_SSH

	}
	return getBitBucketDcRepoUrl(impl.ProjectId, repoSlug, url, authMode)

}

func getBitBucketDcRepoUrl(project string, repoSlug string, dcURL string, mode constants.AuthMode) string {

	if mode == constants.AUTH_MODE_SSH || mode == constants.PAT_AND_SSH {
		return fmt.Sprintf(dcURL+"/%s/%s.git", project, repoSlug)
	}
	return fmt.Sprintf(dcURL+"/scm/%s/%s.git", project, repoSlug)
}
