/*
 * Copyright (c) 2024. Devtron Inc.
 */

package git

import (
	"context"
	"errors"
	"fmt"
	bean2 "github.com/devtron-labs/devtron/api/bean"
	apiBean "github.com/devtron-labs/devtron/api/bean/gitOps"
	"github.com/devtron-labs/devtron/internal/util"
	util2 "github.com/devtron-labs/devtron/pkg/appStore/util"
	commonBean "github.com/devtron-labs/devtron/pkg/deployment/gitOps/common/bean"
	"github.com/devtron-labs/devtron/pkg/deployment/gitOps/config"
	"github.com/devtron-labs/devtron/pkg/deployment/gitOps/git/bean"
	globalUtil "github.com/devtron-labs/devtron/util"
	"github.com/devtron-labs/devtron/util/retryFunc"
	dirCopy "github.com/otiai10/copy"
	"go.opentelemetry.io/otel"
	"go.uber.org/zap"
	"net/url"
	"os"
	"path"
	"path/filepath"
	"regexp"
	"strings"
	"time"
)

type GitOperationService interface {
	CreateGitRepositoryForDevtronApp(ctx context.Context, gitOpsRepoName string, targetRevision string, userId int32) (chartGitAttribute *commonBean.ChartGitAttribute, err error)
	CreateGitRepositoryAtHost(ctx context.Context, host, gitOpsRepoName string, targetRevision string, userId int32) (chartGitAttribute *commonBean.ChartGitAttribute, err error)
	// CreateFirstCommitOnHead - creates the first commit on the head of the git repository (mostly empty).
	// To register the git repository in ArgoCD, there should be a commit HEAD on the default branch.
	CreateFirstCommitOnHead(ctx context.Context, gitOpsRepoName string, gitOpsRepoURL string, userId int32) error
	GitPull(clonedDir string, repoUrl string, targetRevision string) error

	CommitValues(ctx context.Context, chartGitAttr *ChartConfig) (commitHash string, commitTime time.Time, err error)
	PushChartToGitRepo(ctx context.Context, gitOpsRepoName, chartLocation, tempReferenceTemplateDir, repoUrl, targetRevision string, userId int32) (err error)
	PushChartToGitOpsRepoForHelmApp(ctx context.Context, pushChartToGitRequest *bean.PushChartToGitRequestDTO, requirementsConfig, valuesConfig *ChartConfig) (*commonBean.ChartGitAttribute, string, error)

	CreateRepository(ctx context.Context, dto *apiBean.GitOpsConfigDto, userId int32) (string, bool, bool, error)

	GetClonedDir(ctx context.Context, chartDir, repoUrl, targetRevision string) (string, error)
	ReloadGitOpsProvider() error
	UpdateGitHostUrlByProvider(request *apiBean.GitOpsConfigDto) error

	GetRepoUrlWithUserName(url string) (string, error)

	CloneAndPull(chartDir, oldRepoURL, targetRevision string) (string, error)
	PushToNewRemote(ctx context.Context, chartDir, newRemoteName, newRepoURL string) error
}

type GitOperationServiceImpl struct {
	logger                  *zap.SugaredLogger
	gitFactory              *GitFactory
	gitOpsConfigReadService config.GitOpsConfigReadService
	chartTemplateService    util.ChartTemplateService
	globalEnvVariables      *globalUtil.GlobalEnvVariables
}

func NewGitOperationServiceImpl(logger *zap.SugaredLogger, gitFactory *GitFactory,
	gitOpsConfigReadService config.GitOpsConfigReadService,
	chartTemplateService util.ChartTemplateService,
	envVariables *globalUtil.EnvironmentVariables) *GitOperationServiceImpl {
	return &GitOperationServiceImpl{
		logger:                  logger,
		gitFactory:              gitFactory,
		gitOpsConfigReadService: gitOpsConfigReadService,
		chartTemplateService:    chartTemplateService,
		globalEnvVariables:      envVariables.GlobalEnvVariables,
	}

}

func (impl *GitOperationServiceImpl) GetRepoUrlWithUserName(url string) (string, error) {
	_, gitOpsHelper, err := impl.getGitOpsClientAndHelperForUrl(url)
	if err != nil {
		impl.logger.Errorw("error in getting gitOps client", "err", err, "url", url)
		return url, err
	}
	if gitOpsHelper.SshHost != "" {
		return bean.AddUserNameToUrl(url, gitOpsHelper.Auth.Username)
	}
	return url, nil
}

func (impl *GitOperationServiceImpl) CreateGitRepositoryForDevtronApp(ctx context.Context, gitOpsRepoName string, targetRevision string, userId int32) (chartGitAttribute *commonBean.ChartGitAttribute, err error) {
	// baseTemplateName replace whitespace
	space := regexp.MustCompile(`\s+`)
	gitOpsRepoName = space.ReplaceAllString(gitOpsRepoName, "-")

	bitbucketMetadata, err := impl.gitOpsConfigReadService.GetBitbucketMetadataForRepoCreate()
	if err != nil {
		impl.logger.Errorw("error in getting bitbucket metadata", "err", err)
		return nil, err
	}
	//getting username & emailId for commit author data
	gitRepoRequest := &apiBean.GitOpsConfigDto{
		GitRepoName:          gitOpsRepoName,
		TargetRevision:       targetRevision,
		Description:          fmt.Sprintf("helm chart for " + gitOpsRepoName),
		BitBucketWorkspaceId: bitbucketMetadata.BitBucketWorkspaceId,
		BitBucketProjectKey:  bitbucketMetadata.BitBucketProjectKey,
	}
	repoUrl, isNew, isEmpty, err := impl.CreateRepository(ctx, gitRepoRequest, userId)
	if err != nil {
		impl.logger.Errorw("error in creating git project", "name", gitOpsRepoName, "err", err)
		return nil, err
	}
	return &commonBean.ChartGitAttribute{
		RepoUrl:        repoUrl,
		IsNewRepo:      isNew,
		TargetRevision: targetRevision,
		IsRepoEmpty:    isEmpty}, nil
}

// CreateGitRepositoryAtHost assumes there is only one gitOps provider configured for given host, if there are multiple provider for a host this function should be modified accordingly
func (impl *GitOperationServiceImpl) CreateGitRepositoryAtHost(ctx context.Context, gitHost, gitOpsRepoName string, targetRevision string, userId int32) (chartGitAttribute *commonBean.ChartGitAttribute, err error) {
	//baseTemplateName  replace whitespace
	space := regexp.MustCompile(`\s+`)
	gitOpsRepoName = space.ReplaceAllString(gitOpsRepoName, "-")

	bitbucketMetadata, err := impl.gitOpsConfigReadService.GetBitbucketMetadataByHost(gitHost)
	if err != nil {
		impl.logger.Errorw("error in getting bitbucket metadata", "err", err)
		return nil, err
	}
	//getting username & emailId for commit author data
	gitRepoRequest := &apiBean.GitOpsConfigDto{
		Host:                 gitHost,
		GitRepoName:          gitOpsRepoName,
		TargetRevision:       targetRevision,
		Description:          fmt.Sprintf("helm chart for " + gitOpsRepoName),
		BitBucketWorkspaceId: bitbucketMetadata.BitBucketWorkspaceId,
		BitBucketProjectKey:  bitbucketMetadata.BitBucketProjectKey,
	}
	repoUrl, isNew, isEmpty, err := impl.CreateRepository(ctx, gitRepoRequest, userId)
	if err != nil {
		impl.logger.Errorw("error in creating git project", "name", gitOpsRepoName, "err", err)
		return nil, err
	}
	return &commonBean.ChartGitAttribute{
		RepoUrl:        repoUrl,
		IsNewRepo:      isNew,
		TargetRevision: targetRevision,
		IsRepoEmpty:    isEmpty}, nil
}

func getChartDirPathFromCloneDir(cloneDirPath string) (string, error) {
	return filepath.Rel(bean.GIT_WORKING_DIR, cloneDirPath)
}

func (impl *GitOperationServiceImpl) PushChartToGitRepo(ctx context.Context, gitOpsRepoName, chartLocation, tempReferenceTemplateDir, repoUrl, targetRevision string, userId int32) (err error) {
	newCtx, span := otel.Tracer("orchestrator").Start(ctx, "GitOperationServiceImpl.PushChartToGitRepo")
	defer span.End()
	chartDir := fmt.Sprintf("%s-%s", gitOpsRepoName, impl.chartTemplateService.GetDir())
	clonedDir, err := impl.GetClonedDir(newCtx, chartDir, repoUrl, targetRevision)
	defer impl.chartTemplateService.CleanDir(clonedDir)
	if err != nil {
		impl.logger.Errorw("error in cloning repo", "url", repoUrl, "err", err)
		return err
	}
	// TODO: verify if GitPull is required or not; remove if not at all required.
	err = impl.GitPull(clonedDir, repoUrl, targetRevision)
	if err != nil {
		impl.logger.Errorw("error in pulling git repo", "url", repoUrl, "err", err)
		return err
	}
	dir := filepath.Join(clonedDir, chartLocation)
	performFirstCommitPush := true

	//if chart already exists don't overrides it by reference template
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		err = os.MkdirAll(dir, os.ModePerm)
		if err != nil {
			impl.logger.Errorw("error in making dir", "err", err)
			return err
		}
		err = dirCopy.Copy(tempReferenceTemplateDir, dir)
		if err != nil {
			impl.logger.Errorw("error copying dir", "err", err)
			return err
		}
	} else {
		// auto-healing : data corruption fix - sometimes reference chart contents are not pushed in git-ops repo.
		// copying content from reference template dir to cloned dir (if Chart.yaml file is not found)
		// if Chart.yaml file is not found, we are assuming here that reference chart contents are not pushed in git-ops repo
		if _, err := os.Stat(filepath.Join(dir, "Chart.yaml")); os.IsNotExist(err) {
			impl.logger.Infow("auto-healing: Chart.yaml not found in cloned repo from git-ops. copying content", "from", tempReferenceTemplateDir, "to", dir)
			err = dirCopy.Copy(tempReferenceTemplateDir, dir)
			if err != nil {
				impl.logger.Errorw("error copying content in auto-healing", "err", err)
				return err
			}
		} else {
			// chart exists on git, hence not performing first commit
			performFirstCommitPush = false
		}
	}

	// if push needed, then only push
	if performFirstCommitPush {

		_, gitOpsHelper, err := impl.getGitOpsClientAndHelperForUrl(repoUrl)
		if err != nil {
			impl.logger.Errorw("error in getting gitOps client", "err", err, "repoUrl", repoUrl)
			return err
		}
		userEmailId, userName := impl.gitOpsConfigReadService.GetUserEmailIdAndNameForGitOpsCommit(userId)
		commit, err := gitOpsHelper.CommitAndPushAllChanges(newCtx, clonedDir, targetRevision, "first commit", userName, userEmailId)
		if err != nil {
			impl.logger.Errorw("error in pushing git", "err", err)
			callback := func(int) error {
				commit, err = impl.updateRepoAndPushAllChanges(newCtx, clonedDir, repoUrl, targetRevision,
					tempReferenceTemplateDir, dir, userName, userEmailId, gitOpsHelper)
				return err
			}
			err = retryFunc.Retry(callback,
				impl.isRetryableGitCommitError,
				impl.globalEnvVariables.ArgoGitCommitRetryCountOnConflict,
				time.Duration(impl.globalEnvVariables.ArgoGitCommitRetryDelayOnConflict)*time.Second,
				impl.logger)
			if err != nil {
				impl.logger.Errorw("error in pushing git", "err", err)
				return err
			}
		}
		impl.logger.Debugw("template committed", "url", repoUrl, "commit", commit)
	}

	return nil
}

func (impl *GitOperationServiceImpl) updateRepoAndPushAllChanges(ctx context.Context, clonedDir, repoUrl, targetRevision,
	tempReferenceTemplateDir, dir, userName, userEmailId string, gitOpsHelper *GitOpsHelper) (commit string, err error) {
	impl.logger.Warn("re-trying, taking pull and then push again")
	err = impl.GitPull(clonedDir, repoUrl, targetRevision)
	if err != nil {
		return commit, err
	}
	err = dirCopy.Copy(tempReferenceTemplateDir, dir)
	if err != nil {
		impl.logger.Errorw("error copying dir", "err", err)
		return commit, err
	}
	commit, err = gitOpsHelper.CommitAndPushAllChanges(ctx, clonedDir, targetRevision, "first commit", userName, userEmailId)
	if err != nil {
		impl.logger.Errorw("error in pushing git", "err", err)
		return commit, retryFunc.NewRetryableError(err)
	}
	return commit, nil
}

func (impl *GitOperationServiceImpl) CreateFirstCommitOnHead(ctx context.Context, gitOpsRepoName string, gitOpsRepoURL string, userId int32) error {
	userEmailId, userName := impl.gitOpsConfigReadService.GetUserEmailIdAndNameForGitOpsCommit(userId)
	gitOpsConfig, err := impl.gitOpsConfigReadService.GetGitOpsConfigActive()
	if err != nil {
		impl.logger.Errorw("error in getting active gitOps config", "err", err)
		return err
	}
	//updating user email and name in request
	if gitOpsConfig != nil {
		gitOpsConfig.UserEmailId = userEmailId
		gitOpsConfig.Username = userName
		gitOpsConfig.GitRepoName = gitOpsRepoName
	}
	gitOpsClient, _, err := impl.getGitOpsClientAndHelperForUrl(gitOpsRepoURL)
	if err != nil {
		impl.logger.Errorw("error in getting gitOps client", "err", err, "gitOpsRepoURL", gitOpsRepoURL)
		return err
	}
	_, err = gitOpsClient.CreateFirstCommitOnHead(ctx, gitOpsConfig, gitOpsRepoURL)
	if err != nil {
		impl.logger.Errorw("error in creating readme", "gitOpsRepoName", gitOpsRepoName, "userId", userId, "err", err)
		return err
	}
	return nil
}

func (impl *GitOperationServiceImpl) GitPull(clonedDir string, repoUrl string, targetRevision string) error {
	_, gitOpsHelper, err := impl.getGitOpsClientAndHelperForUrl(repoUrl)
	if err != nil {
		impl.logger.Errorw("error in getting gitOps client", "err", err, "repoUrl", repoUrl)
		return err
	}
	err = gitOpsHelper.Pull(clonedDir, targetRevision)
	if err != nil {
		impl.logger.Errorw("error in pulling git", "clonedDir", clonedDir, "err", err)
		impl.chartTemplateService.CleanDir(clonedDir)
		chartDir, err := getChartDirPathFromCloneDir(clonedDir)
		if err != nil {
			impl.logger.Errorw("error in getting chart dir from cloned dir", "clonedDir", clonedDir, "err", err)
			return err
		}
		_, err = gitOpsHelper.Clone(repoUrl, chartDir, targetRevision)
		if err != nil {
			impl.logger.Errorw("error in cloning repo", "url", repoUrl, "err", err)
			return err
		}
		return nil
	}
	return nil
}

func (impl *GitOperationServiceImpl) CommitValues(ctx context.Context, chartGitAttr *ChartConfig) (commitHash string, commitTime time.Time, err error) {
	newCtx, span := otel.Tracer("orchestrator").Start(ctx, "gitOperationService.CommitValues")
	defer span.End()

	impl.logger.Debugw("committing values to git", "chartGitAttr", chartGitAttr)
	bitbucketMetadata, err := impl.gitOpsConfigReadService.GetBitbucketMetadataByHost(chartGitAttr.ChartRepoURL)
	if err != nil {
		impl.logger.Errorw("error in getting bitbucket metadata", "err", err)
		return commitHash, commitTime, err
	}
	gitOpsConfig := &apiBean.GitOpsConfigDto{BitBucketWorkspaceId: bitbucketMetadata.BitBucketWorkspaceId}
	callback := func(retriesLeft int) error {
		publishStatusConflictError := false
		if retriesLeft <= 0 {
			publishStatusConflictError = true
		}
		gitOpsClient, _, err := impl.getGitOpsClientAndHelperForUrl(chartGitAttr.ChartRepoURL)
		if err != nil {
			impl.logger.Errorw("error in getting git ops client", "err", err)
			return err
		}
		commitHash, commitTime, err = gitOpsClient.CommitValues(newCtx, chartGitAttr, gitOpsConfig, publishStatusConflictError)
		return err
	}
	err = retryFunc.Retry(callback, impl.isRetryableGitCommitError,
		impl.globalEnvVariables.ArgoGitCommitRetryCountOnConflict,
		time.Duration(impl.globalEnvVariables.ArgoGitCommitRetryDelayOnConflict)*time.Second,
		impl.logger)
	if err != nil {
		impl.logger.Errorw("error in git commit", "err", err)
		return commitHash, commitTime, err
	}
	return commitHash, commitTime, nil
}

func (impl *GitOperationServiceImpl) isRetryableGitCommitError(err error) bool {
	if retryErr := (&retryFunc.RetryableError{}); errors.As(err, &retryErr) {
		return true
	}
	return false
}

func (impl *GitOperationServiceImpl) CreateRepository(ctx context.Context, dto *apiBean.GitOpsConfigDto, userId int32) (string, bool, bool, error) {
	//getting username & emailId for commit author data
	userEmailId, userName := impl.gitOpsConfigReadService.GetUserEmailIdAndNameForGitOpsCommit(userId)
	if dto != nil {
		dto.UserEmailId = userEmailId
		dto.Username = userName
	}
	gitOpsClient, _, err := impl.getGitOpsClientAndHelperForUrl(dto.Host)
	if err != nil {
		impl.logger.Errorw("error in getting git ops client", "err", err)
		return "", false, false, err
	}

	repoUrl, isNew, isEmpty, detailedError := gitOpsClient.CreateRepository(ctx, dto)
	for _, err := range detailedError.StageErrorMap {
		if err != nil {
			impl.logger.Errorw("error in creating git project", "req", dto, "err", err)
			return "", false, false, err
		}
	}
	return repoUrl, isNew, isEmpty, nil
}

func (impl *GitOperationServiceImpl) GetRepoUrlByHostAndRepoName(host, repoName string) (string, error) {
	bitbucketMetadata, err := impl.gitOpsConfigReadService.GetBitbucketMetadataByHost(host)
	if err != nil {
		impl.logger.Errorw("error in getting bitbucket metadata", "err", err)
		return "", err
	}
	dto := &apiBean.GitOpsConfigDto{
		GitRepoName:          repoName,
		BitBucketWorkspaceId: bitbucketMetadata.BitBucketWorkspaceId,
		BitBucketProjectKey:  bitbucketMetadata.BitBucketProjectKey,
	}
	gitOpsClient, _, err := impl.getGitOpsClientAndHelperForUrl(host)
	if err != nil {
		impl.logger.Errorw("error in getting git ops client", "err", err)
		return "", err
	}
	repoUrl, _, err := gitOpsClient.GetRepoUrl(dto)
	if err != nil {
		//will allow to continue to persist status on next operation
		impl.logger.Errorw("error in getting repo url", "err", err, "repoName", repoName)
		return "", nil
	}
	return repoUrl, nil
}

// PushChartToGitOpsRepoForHelmApp pushes built chart to GitOps repo (Specific implementation for Helm Apps)
// TODO refactoring: Make a common method for both PushChartToGitRepo and PushChartToGitOpsRepoForHelmApp
func (impl *GitOperationServiceImpl) PushChartToGitOpsRepoForHelmApp(ctx context.Context, pushChartToGitRequest *bean.PushChartToGitRequestDTO, requirementsConfig, valuesConfig *ChartConfig) (*commonBean.ChartGitAttribute, string, error) {
	chartDir := fmt.Sprintf("%s-%s", pushChartToGitRequest.AppName, impl.chartTemplateService.GetDir())
	_, gitOpsHelper, err := impl.getGitOpsClientAndHelperForUrl(pushChartToGitRequest.RepoURL)
	if err != nil {
		impl.logger.Errorw("error in getting gitOps client", "err", err, "repoUrl", pushChartToGitRequest.RepoURL)
		return nil, "", err
	}
	clonedDir := gitOpsHelper.GetCloneDirectory(chartDir)
	if _, err := os.Stat(clonedDir); os.IsNotExist(err) {
		clonedDir, err = gitOpsHelper.Clone(pushChartToGitRequest.RepoURL, chartDir, pushChartToGitRequest.TargetRevision)
		if err != nil {
			impl.logger.Errorw("error in cloning repo", "url", pushChartToGitRequest.RepoURL, "err", err)
			return nil, "", err
		}
	} else {
		err = impl.GitPull(clonedDir, pushChartToGitRequest.RepoURL, pushChartToGitRequest.TargetRevision)
		if err != nil {
			return nil, "", err
		}
	}
	gitOpsChartLocation := fmt.Sprintf("%s-%s", pushChartToGitRequest.AppName, pushChartToGitRequest.EnvName)
	dir := filepath.Join(clonedDir, gitOpsChartLocation)
	err = os.MkdirAll(dir, os.ModePerm)
	if err != nil {
		impl.logger.Errorw("error in making dir", "err", err)
		return nil, "", err
	}
	err = dirCopy.Copy(pushChartToGitRequest.TempChartRefDir, dir)
	if err != nil {
		impl.logger.Errorw("error copying dir", "err", err)
		return nil, "", err
	}
	err = impl.addConfigFileToChart(requirementsConfig, dir, clonedDir)
	if err != nil {
		impl.logger.Errorw("error in adding requirements.yaml to chart", "appName", pushChartToGitRequest.AppName, "err", err)
		return nil, "", err
	}
	err = impl.addConfigFileToChart(valuesConfig, dir, clonedDir)
	if err != nil {
		impl.logger.Errorw("error in adding values.yaml to chart", "appName", pushChartToGitRequest.AppName, "err", err)
		return nil, "", err
	}
	userEmailId, userName := impl.gitOpsConfigReadService.GetUserEmailIdAndNameForGitOpsCommit(pushChartToGitRequest.UserId)
	commit, err := gitOpsHelper.CommitAndPushAllChanges(ctx, clonedDir, pushChartToGitRequest.TargetRevision, "first commit", userName, userEmailId)
	if err != nil {
		impl.logger.Errorw("error in pushing git", "err", err)
		impl.logger.Warn("re-trying, taking pull and then push again")
		err = impl.GitPull(clonedDir, pushChartToGitRequest.RepoURL, pushChartToGitRequest.TargetRevision)
		if err != nil {
			impl.logger.Errorw("error in git pull", "appName", gitOpsChartLocation, "err", err)
			return nil, "", err
		}
		err = dirCopy.Copy(pushChartToGitRequest.TempChartRefDir, dir)
		if err != nil {
			impl.logger.Errorw("error copying dir", "err", err)
			return nil, "", err
		}
		commit, err = gitOpsHelper.CommitAndPushAllChanges(ctx, clonedDir, pushChartToGitRequest.TargetRevision, "first commit", userName, userEmailId)
		if err != nil {
			impl.logger.Errorw("error in pushing git", "err", err)
			return nil, "", err
		}
	}
	impl.logger.Debugw("template committed", "url", pushChartToGitRequest.RepoURL, "commit", commit)
	defer impl.chartTemplateService.CleanDir(clonedDir)
	return &commonBean.ChartGitAttribute{
		RepoUrl:        pushChartToGitRequest.RepoURL,
		ChartLocation:  gitOpsChartLocation,
		TargetRevision: pushChartToGitRequest.TargetRevision,
	}, commit, err
}

func (impl *GitOperationServiceImpl) GetClonedDir(ctx context.Context, chartDir, repoUrl, targetRevision string) (string, error) {
	_, span := otel.Tracer("orchestrator").Start(ctx, "GitOperationServiceImpl.GetClonedDir")
	defer span.End()
	_, gitOpsHelper, err := impl.getGitOpsClientAndHelperForUrl(repoUrl)
	if err != nil {
		impl.logger.Errorw("error in getting gitOps client", "err", err, "repoUrl", repoUrl)
		return "", err
	}
	clonedDir := gitOpsHelper.GetCloneDirectory(chartDir)
	if _, err := os.Stat(clonedDir); os.IsNotExist(err) {
		return impl.cloneInDir(repoUrl, chartDir, targetRevision)
	} else if err != nil {
		impl.logger.Errorw("error in cloning repo", "url", repoUrl, "err", err)
		return "", err
	}
	return clonedDir, nil
}

func (impl *GitOperationServiceImpl) cloneInDir(repoUrl, chartDir, targetRevision string) (string, error) {
	_, gitOpsHelper, err := impl.getGitOpsClientAndHelperForUrl(repoUrl)
	if err != nil {
		impl.logger.Errorw("error in getting gitOps client", "err", err, "repoUrl", repoUrl)
		return "", err
	}
	clonedDir, err := gitOpsHelper.Clone(repoUrl, chartDir, targetRevision)
	if err != nil {
		impl.logger.Errorw("error in cloning repo", "url", repoUrl, "err", err)
		return "", err
	}
	return clonedDir, nil
}
func (impl *GitOperationServiceImpl) ReloadGitOpsProvider() error {
	return impl.gitFactory.Reload(impl.gitOpsConfigReadService)
}

func (impl *GitOperationServiceImpl) UpdateGitHostUrlByProvider(request *apiBean.GitOpsConfigDto) error {
	switch strings.ToUpper(request.Provider) {
	case bean.GITHUB_PROVIDER:
		orgUrl, err := buildGithubOrgUrl(request.Host, request.GitHubOrgId)
		if err != nil {
			return err
		}
		request.Host = orgUrl

	case bean.GITLAB_PROVIDER:

		if request.EnableTLSVerification &&
			(request.TLSConfig == nil ||
				(request.TLSConfig != nil && (len(request.TLSConfig.TLSCertData) == 0 && len(request.TLSConfig.TLSKeyData) == 0 && len(request.TLSConfig.CaData) == 0))) {
			model, err := impl.gitOpsConfigReadService.GetGitOpsById(request.Id)
			if err != nil {
				impl.logger.Errorw("gitops provider not found", "id", model.Id, "err", err)
				return err
			}
			request.TLSConfig = &bean2.TLSConfig{
				CaData:      model.TLSConfig.CaData,
				TLSCertData: model.TLSConfig.TLSCertData,
				TLSKeyData:  model.TLSConfig.TLSKeyData,
			}
		}

		groupName, err := impl.gitFactory.GetGitLabGroupPath(request)
		if err != nil {
			return err
		}
		slashSuffixPresent := strings.HasSuffix(request.Host, "/")
		if slashSuffixPresent {
			request.Host += groupName
		} else {
			request.Host = fmt.Sprintf(request.Host+"/%s", groupName)
		}
	case bean.BITBUCKET_PROVIDER:
		request.Host = BITBUCKET_CLONE_BASE_URL + request.BitBucketWorkspaceId
	}
	return nil
}

func (impl *GitOperationServiceImpl) CloneAndPull(chartDir, oldRepoURL, targetRevision string) (string, error) {
	clonedDir, err := impl.GetClonedDir(context.Background(), chartDir, oldRepoURL, targetRevision)
	if err != nil {
		impl.logger.Errorw("error in cloning repo", "url", oldRepoURL, "err", err)
		return clonedDir, err
	}
	err = impl.GitPull(clonedDir, oldRepoURL, targetRevision)
	if err != nil {
		impl.logger.Errorw("error in pulling git", "err", err)
		return clonedDir, err
	}
	return clonedDir, nil
}

func (impl *GitOperationServiceImpl) PushToNewRemote(ctx context.Context, chartDir, newRemoteName, newRepoURL string) error {
	_, gitOpsHelper, err := impl.getGitOpsClientAndHelperForUrl(newRepoURL)
	if err != nil {
		impl.logger.Errorw("error in getting gitOps client", "err", err, "newRepoURL", newRepoURL)
		return err
	}
	err = gitOpsHelper.AddNewRemoteAndPush(ctx, chartDir, newRemoteName, newRepoURL)
	if err != nil {
		impl.logger.Errorw("error in pushing git", "err", err)
		return err
	}
	return nil
}

func buildGithubOrgUrl(host, orgId string) (orgUrl string, err error) {
	if !strings.HasPrefix(host, HTTP_URL_PROTOCOL) && !strings.HasPrefix(host, HTTPS_URL_PROTOCOL) {
		return orgUrl, fmt.Errorf("invalid host url '%s'", host)
	}
	hostUrl, err := url.Parse(host)
	if err != nil {
		return "", err
	}
	hostUrl.Path = path.Join(hostUrl.Path, orgId)
	return hostUrl.String(), nil
}

// addConfigFileToChart will override requirements.yaml or values.yaml file in chart
func (impl *GitOperationServiceImpl) addConfigFileToChart(config *ChartConfig, destinationDir string, clonedDir string) error {
	filePath := filepath.Join(clonedDir, config.FileName)
	filePath, err := util2.CreateFileAtFilePathAndWrite(filePath, config.FileContent)
	if err != nil {
		impl.logger.Errorw("error in creating yaml file", "err", err)
		return err
	}
	destinationFilePath := filepath.Join(destinationDir, config.FileName)
	err = util2.MoveFileToDestination(filePath, destinationFilePath)
	if err != nil {
		impl.logger.Errorw("error in moving file from source to destination", "err", err)
		return err
	}
	return nil
}

func (impl *GitOperationServiceImpl) getGitOpsClientAndHelperForUrl(inputRepoUrl string) (GitOpsClient, *GitOpsHelper, error) {
	clientResp, helperResp := impl.gitFactory.Client, impl.gitFactory.GitOpsHelper //default is active. TODO : confirm check
	for repoUrl, clientHelperObject := range impl.gitFactory.ClientHelperMap {
		if clientHelperObject == nil {
			continue
		}
		// Extract the scheme from the URL to distinguish between SSH (Other_Git_Ops)
		// and HTTPS supported providers. This ensures inputRepoUrl and repoUrl are matched correctly.
		// There are two cases:
		// 1. SSH scheme -> Other_Git_Ops provider
		// 2. HTTPS scheme -> All other supported providers

		hostURL, scheme, err := globalUtil.GetHost(repoUrl)
		if err != nil {
			impl.logger.Debugw("error in parsing repoUrl, getGitOpsClientAndHelperForUrl", "repoUrl", repoUrl, "err", err)
		}
		inputHostURL, inputScheme, err := globalUtil.GetHost(inputRepoUrl)
		if err != nil {
			impl.logger.Debugw("error in parsing inputRepoUrl, getGitOpsClientAndHelperForUrl", "inputRepoUrl", inputRepoUrl, "err", err)
		}
		if len(hostURL) > 0 && len(inputHostURL) > 0 && scheme == inputScheme {
			if strings.HasPrefix(inputHostURL, hostURL) {
				clientResp = clientHelperObject.Client
				helperResp = clientHelperObject.GitOpsHelper
				if clientHelperObject.ClientCreationError != nil {
					impl.logger.Errorw("error in creating client for repoUrl", "repoUrl", repoUrl, "err", clientHelperObject.ClientCreationError)
					return clientResp, helperResp, clientHelperObject.ClientCreationError
				}
			}
		}
	}
	return clientResp, helperResp, nil
}
