/*
 * Copyright (c) 2024. Devtron Inc.
 */

package git

import (
	"crypto/tls"
	"errors"
	"fmt"
	"github.com/devtron-labs/devtron/api/bean/gitOps"
	"github.com/devtron-labs/devtron/pkg/deployment/gitOps/config"
	"github.com/devtron-labs/devtron/pkg/deployment/gitOps/git/adapter"
	"github.com/devtron-labs/devtron/pkg/deployment/gitOps/git/bean"
	"github.com/devtron-labs/devtron/util"
	"github.com/go-pg/pg"
	"github.com/xanzy/go-gitlab"
	"go.uber.org/zap"
	"time"
)

type GitFactory struct {
	Client             GitOpsClient
	GitOpsHelper       *GitOpsHelper
	logger             *zap.SugaredLogger
	globalEnvVariables *util.GlobalEnvVariables
	ClientHelperMap    map[string]*ClientHelperObject //map of gitOps url and client
}

type ClientHelperObject struct {
	Client              GitOpsClient
	GitOpsHelper        *GitOpsHelper
	ClientCreationError error
	Config              *bean.GitConfig
}

func (factory *GitFactory) Reload(gitOpsConfigReadService config.GitOpsConfigReadService) (err error) {
	start := time.Now()
	defer func() {
		util.TriggerGitOpsMetrics("Reload", "GitService", start, err)
	}()
	factory.logger.Infow("reloading gitops details")
	gitConfigs, err := gitOpsConfigReadService.GetGitConfigAll()
	if err != nil {
		factory.logger.Errorw("error in getting gitops details", "err", err)
		return err
	} else if errors.Is(err, pg.ErrNoRows) || len(gitConfigs) == 0 {
		factory.logger.Warn("no gitops config found, gitops will not work")
		return nil
	}
	clientHelperMap := make(map[string]*ClientHelperObject, len(gitConfigs))
	// activeConfigError is used to capture the error for the active config,
	// if any error occurs while creating the client or helper for the active config.
	var activeConfigError error
	for _, cfg := range gitConfigs {
		gitOpsHelper := NewGitOpsHelperImpl(cfg.GetAuth(), cfg.SshHost, factory.logger, cfg.GetTLSConfig(), cfg.EnableTLSVerification)
		host := cfg.GitHost
		if len(cfg.SshHost) > 0 {
			host = cfg.SshHost
		}
		clientHelperMap[host] = &ClientHelperObject{
			Config: cfg,
		}
		clientHelperMap[host].GitOpsHelper = gitOpsHelper
		client, clientError := NewGitOpsClient(cfg, factory.logger, gitOpsHelper, factory.globalEnvVariables)
		var clientCreationError error
		if clientError != nil {
			clientCreationError = fmt.Errorf("error in creating gitOps client: %v", clientError)
			factory.logger.Errorw("error in creating gitOps client", "err", clientError, "gitProvider", cfg.GitProvider)
			if cfg.IsActiveConfig && activeConfigError == nil {
				// only passing error in case of active config
				activeConfigError = clientError
			}
		}
		if cfg.IsActiveConfig {
			// also setting in old gitOps client with active config
			factory.Client = client
			// also setting in old gitOps helper with active config
			factory.GitOpsHelper = gitOpsHelper
		}
		clientHelperMap[host].Client = client
		clientHelperMap[host].ClientCreationError = clientCreationError
	}
	factory.ClientHelperMap = clientHelperMap
	if activeConfigError != nil {
		factory.logger.Errorw("error in creating gitOps client or helper for active config", "err", activeConfigError)
		return activeConfigError
	}
	factory.logger.Infow(" gitops details reload success")
	return nil
}

func (factory *GitFactory) GetGitLabGroupPath(gitOpsConfig *gitOps.GitOpsConfigDto) (string, error) {
	start := time.Now()
	var err error
	defer func() {
		util.TriggerGitOpsMetrics("GetGitLabGroupPath", "GitOpsHelper", start, err)
	}()

	var tlsConfig *tls.Config
	if gitOpsConfig.TLSConfig != nil {
		tlsConfig, err = util.GetTlsConfig(gitOpsConfig.TLSConfig.TLSKeyData, gitOpsConfig.TLSConfig.TLSCertData, gitOpsConfig.TLSConfig.CaData, bean.GIT_TLS_DIR)
		if err != nil {
			factory.logger.Errorw("error in getting tls config", "err", err)
			return "", err
		}
	}

	gitLabClient, err := CreateGitlabClient(gitOpsConfig.Host, gitOpsConfig.Token, tlsConfig)
	if err != nil {
		factory.logger.Errorw("error in creating gitlab client", "err", err)
		return "", err
	}
	group, _, err := gitLabClient.Groups.GetGroup(gitOpsConfig.GitLabGroupId, &gitlab.GetGroupOptions{})
	if err != nil {
		factory.logger.Errorw("error in fetching gitlab group name", "gitLab groupID", gitOpsConfig.GitLabGroupId, "err", err)
		return "", err
	}
	if group == nil {
		factory.logger.Errorw("no matching groups found for gitlab", "gitLab groupID", gitOpsConfig.GitLabGroupId, "err", err)
		return "", fmt.Errorf("no matching groups found for gitlab group ID : %s", gitOpsConfig.GitLabGroupId)
	}
	return group.FullPath, nil
}

func (factory *GitFactory) NewClientForValidation(gitOpsConfig *gitOps.GitOpsConfigDto) (GitOpsClient, *GitOpsHelper, error) {
	start := time.Now()
	var err error
	defer func() {
		util.TriggerGitOpsMetrics("NewClientForValidation", "GitOpsHelper", start, err)
	}()
	cfg := adapter.ConvertGitOpsConfigToGitConfig(gitOpsConfig)
	//factory.GitOpsHelper.SetAuth(cfg.GetAuth())
	gitOpsHelper := NewGitOpsHelperImpl(cfg.GetAuth(), gitOpsConfig.GetSshHost(), factory.logger, cfg.GetTLSConfig(), cfg.EnableTLSVerification)

	client, err := NewGitOpsClient(cfg, factory.logger, gitOpsHelper, factory.globalEnvVariables)
	if err != nil {
		factory.logger.Errorw("error in creating gitOps client", "gitProvider", cfg.GitProvider, "err", err)
		return client, gitOpsHelper, err
	}

	//factory.Client = client
	factory.logger.Infow("client changed successfully", "cfg", cfg)
	return client, gitOpsHelper, nil
}

func NewGitFactory(logger *zap.SugaredLogger, variable *util.EnvironmentVariables, gitOpsConfigReadService config.GitOpsConfigReadService) (*GitFactory, error) {
	factory := &GitFactory{
		logger:             logger,
		globalEnvVariables: variable.GlobalEnvVariables,
	}
	err := factory.Reload(gitOpsConfigReadService)
	if err != nil {
		logger.Errorw("error in reloading gitops details", "err", err)
		// error handling is skipped intentionally here, otherwise orchestration will not work
	}
	if err != nil || factory.Client == nil {
		factory.Client = &UnimplementedGitOpsClient{}
	}
	return factory, nil
}
