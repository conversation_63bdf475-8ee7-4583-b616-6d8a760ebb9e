/*
 * Copyright (c) 2024. Devtron Inc.
 */

package commandManager

import (
	"context"
	"fmt"
	"github.com/devtron-labs/common-lib/git-manager"
	"github.com/devtron-labs/devtron/util"
	"go.uber.org/zap"
	"os"
	"os/exec"
	"strings"
	"time"
)

type GitCommandManagerBase interface {
	Clone(ctx GitContext, rootDir, repoUrl, sshCmd string) (response, errMsg string, err error)
	Fetch(ctx GitContext, rootDir string) (response, errMsg string, err error)
	ListBranch(ctx GitContext, rootDir string) (response, errMsg string, err error)
	// GetCurrentBranch returns the current branch of the git repository directory.
	//	command: git -C <rootDir> branch --show-current
	GetCurrentBranch(ctx GitContext, rootDir string) (response, errMsg string, err error)
	// GetDefaultBranch returns the default branch of the git repository directory.
	//	command: git -C <rootDir> rev-parse --abbrev-ref origin/HEAD
	// If the repository is empty, it returns an error.
	// In that case, use GetCurrentBranch to get the default branch.
	GetDefaultBranch(ctx GitContext, rootDir string) (response, errMsg string, err error)
	PullCli(ctx GitContext, rootDir string, branch string) (response, errMsg string, err error)
	GetSshCommand(sshPrivateKeyPath string) string
	ConfigureSshCommand(gitCtx GitContext, rootDir string, sshPrivateKeyPath string) (response, errMsg string, err error)
}

type GitManagerBaseImpl struct {
	logger *zap.SugaredLogger
	cfg    *configuration
}

func (impl *GitManagerBaseImpl) Clone(ctx GitContext, rootDir, repoUrl, sshCmd string) (response, errMsg string, err error) {
	start := time.Now()
	defer func() {
		util.TriggerGitOpsMetrics("Clone", "GitCli", start, err)
	}()
	impl.logger.Debugw("git clone --depth=1 --single-branch --no-tags", "location", rootDir, "repoUrl", repoUrl)
	args := []string{"clone", "--depth=1", "--single-branch", "--no-tags", repoUrl, rootDir}
	args = impl.appendBearerAuth(ctx, args)
	cmd, cancel := impl.createCmdWithContext(ctx, "git", args...)
	defer cancel()
	tlsPathInfo, err := git_manager.CreateFilesForTlsData(git_manager.BuildTlsData(ctx.TLSKey, ctx.TLSCertificate, ctx.CACert, ctx.TLSVerificationEnabled), TLS_FOLDER)
	if err != nil {
		//making it non-blocking
		impl.logger.Errorw("error encountered in createFilesForTlsData", "err", err)
	}
	defer git_manager.DeleteTlsFiles(tlsPathInfo)
	additionalEnv := make([]string, 0)
	if sshCmd != "" {
		additionalEnv = append(additionalEnv, fmt.Sprintf("GIT_SSH_COMMAND=%s", sshCmd))
	}
	output, errMsg, err := impl.runCommandWithCred(cmd, ctx.auth, tlsPathInfo, additionalEnv...)
	impl.logger.Debugw("git clone --depth=1 --single-branch --no-tags output", "root", rootDir, "repoUrl", repoUrl, "opt", output, "errMsg", errMsg, "error", err)
	return output, errMsg, err
}

func (impl *GitManagerBaseImpl) Fetch(ctx GitContext, rootDir string) (response, errMsg string, err error) {
	start := time.Now()
	defer func() {
		util.TriggerGitOpsMetrics("Fetch", "GitCli", start, err)
	}()
	impl.logger.Debugw("git fetch ", "location", rootDir)
	args := []string{"-C", rootDir, "fetch", "origin", "--tags", "--force"}
	args = impl.appendBearerAuth(ctx, args)
	cmd, cancel := impl.createCmdWithContext(ctx, "git", args...)
	defer cancel()
	tlsPathInfo, err := git_manager.CreateFilesForTlsData(git_manager.BuildTlsData(ctx.TLSKey, ctx.TLSCertificate, ctx.CACert, ctx.TLSVerificationEnabled), TLS_FOLDER)
	if err != nil {
		//making it non-blocking
		impl.logger.Errorw("error encountered in createFilesForTlsData", "err", err)
	}
	defer git_manager.DeleteTlsFiles(tlsPathInfo)
	output, errMsg, err := impl.runCommandWithCred(cmd, ctx.auth, tlsPathInfo)
	impl.logger.Debugw("fetch output", "root", rootDir, "opt", output, "errMsg", errMsg, "error", err)
	return output, errMsg, err
}

func (impl *GitManagerBaseImpl) appendBearerAuth(ctx GitContext, args []string) []string {
	if ctx.auth.BearerAuth != "" {
		// http.extraHeader='Authorization: Bearer BBDC-MDgwNjQyOTY5MTM4OiPgTrwii04uCofF0fa3j42FyaLX'
		args = append([]string{"-c", "http.extraHeader=Authorization:" + ctx.auth.BearerAuth}, args...)
	}
	return args
}

func (impl *GitManagerBaseImpl) ListBranch(ctx GitContext, rootDir string) (response, errMsg string, err error) {
	start := time.Now()
	defer func() {
		util.TriggerGitOpsMetrics("ListBranch", "GitCli", start, err)
	}()
	impl.logger.Debugw("git branch -r", "location", rootDir)
	cmd, cancel := impl.createCmdWithContext(ctx, "git", "-C", rootDir, "branch", "-r")
	defer cancel()
	tlsPathInfo, err := git_manager.CreateFilesForTlsData(git_manager.BuildTlsData(ctx.TLSKey, ctx.TLSCertificate, ctx.CACert, ctx.TLSVerificationEnabled), TLS_FOLDER)
	if err != nil {
		//making it non-blocking
		impl.logger.Errorw("error encountered in createFilesForTlsData", "err", err)
	}
	defer git_manager.DeleteTlsFiles(tlsPathInfo)
	output, errMsg, err := impl.runCommandWithCred(cmd, ctx.auth, tlsPathInfo)
	impl.logger.Debugw("git branch -r output", "root", rootDir, "opt", output, "errMsg", errMsg, "error", err)
	return output, errMsg, err
}

func (impl *GitManagerBaseImpl) GetCurrentBranch(ctx GitContext, rootDir string) (response, errMsg string, err error) {
	start := time.Now()
	defer func() {
		util.TriggerGitOpsMetrics("GetCurrentBranch", "GitCli", start, err)
	}()
	impl.logger.Debugw("git branch --show-current", "location", rootDir)
	cmd, cancel := impl.createCmdWithContext(ctx, "git", "-C", rootDir, "branch", "--show-current")
	defer cancel()
	tlsPathInfo, err := git_manager.CreateFilesForTlsData(git_manager.BuildTlsData(ctx.TLSKey, ctx.TLSCertificate, ctx.CACert, ctx.TLSVerificationEnabled), TLS_FOLDER)
	if err != nil {
		//making it non-blocking
		impl.logger.Errorw("error encountered in createFilesForTlsData", "err", err)
	}
	defer git_manager.DeleteTlsFiles(tlsPathInfo)
	output, errMsg, err := impl.runCommandWithCred(cmd, ctx.auth, tlsPathInfo)
	impl.logger.Debugw("git branch --show-current output", "root", rootDir, "opt", output, "errMsg", errMsg, "error", err)
	return output, errMsg, err
}

func (impl *GitManagerBaseImpl) GetDefaultBranch(ctx GitContext, rootDir string) (response, errMsg string, err error) {
	start := time.Now()
	defer func() {
		util.TriggerGitOpsMetrics("GetDefaultBranch", "GitCli", start, err)
	}()
	impl.logger.Debugw("git rev-parse --abbrev-ref origin/HEAD", "location", rootDir)
	cmd, cancel := impl.createCmdWithContext(ctx, "git", "-C", rootDir, "rev-parse", "--abbrev-ref", "origin/HEAD")
	defer cancel()
	tlsPathInfo, err := git_manager.CreateFilesForTlsData(git_manager.BuildTlsData(ctx.TLSKey, ctx.TLSCertificate, ctx.CACert, ctx.TLSVerificationEnabled), TLS_FOLDER)
	if err != nil {
		//making it non-blocking
		impl.logger.Errorw("error encountered in createFilesForTlsData", "err", err)
	}
	defer git_manager.DeleteTlsFiles(tlsPathInfo)
	output, errMsg, err := impl.runCommandWithCred(cmd, ctx.auth, tlsPathInfo)
	impl.logger.Debugw("git rev-parse --abbrev-ref origin/HEAD output", "root", rootDir, "opt", output, "errMsg", errMsg, "error", err)
	return output, errMsg, err
}

func (impl *GitManagerBaseImpl) PullCli(ctx GitContext, rootDir string, branch string) (response, errMsg string, err error) {
	start := time.Now()
	impl.logger.Debugw("git pull ", "location", rootDir)
	args := []string{"-C", rootDir, "pull", "origin", branch, "--force"}
	args = impl.appendBearerAuth(ctx, args)
	cmd, cancel := impl.createCmdWithContext(ctx, "git", args...)
	defer cancel()
	tlsPathInfo, err := git_manager.CreateFilesForTlsData(git_manager.BuildTlsData(ctx.TLSKey, ctx.TLSCertificate, ctx.CACert, ctx.TLSVerificationEnabled), TLS_FOLDER)
	if err != nil {
		//making it non-blocking
		impl.logger.Errorw("error encountered in createFilesForTlsData", "err", err)
	}
	defer git_manager.DeleteTlsFiles(tlsPathInfo)
	output, errMsg, err := impl.runCommandWithCred(cmd, ctx.auth, tlsPathInfo)
	if err != nil {
		if !IsAlreadyUpToDateError(response, errMsg) {
			util.TriggerGitOpsMetrics("Pull", "GitCli", start, err)
		}
	}
	impl.logger.Debugw("pull output", "root", rootDir, "opt", output, "errMsg", errMsg, "error", err)
	return output, errMsg, err
}

func (impl *GitManagerBaseImpl) runCommandWithCred(cmd *exec.Cmd, auth *BasicAuth, tlsPathInfo *git_manager.TlsPathInfo, additionalEnv ...string) (response, errMsg string, err error) {
	cmd.Env = append(os.Environ(),
		fmt.Sprintf("GIT_ASKPASS=%s", GIT_ASK_PASS),
		fmt.Sprintf("GIT_USERNAME=%s", auth.Username),
		fmt.Sprintf("GIT_PASSWORD=%s", auth.Password),
	)
	if tlsPathInfo != nil {
		if tlsPathInfo.TlsKeyPath != "" && tlsPathInfo.TlsCertPath != "" {
			cmd.Env = append(cmd.Env,
				fmt.Sprintf("GIT_SSL_KEY=%s", tlsPathInfo.TlsKeyPath),
				fmt.Sprintf("GIT_SSL_CERT=%s", tlsPathInfo.TlsCertPath))
		}
		if tlsPathInfo.CaCertPath != "" {
			cmd.Env = append(cmd.Env, fmt.Sprintf("GIT_SSL_CAINFO=%s", tlsPathInfo.CaCertPath))
		}
	}
	if len(additionalEnv) != 0 {
		cmd.Env = append(cmd.Env, additionalEnv...)
	}
	return impl.runCommand(cmd)
}

func (impl *GitManagerBaseImpl) runCommand(cmd *exec.Cmd) (response, errMsg string, err error) {
	cmd.Env = append(cmd.Env, "HOME=/dev/null")
	outBytes, err := cmd.CombinedOutput()
	if err != nil {
		exErr, ok := err.(*exec.ExitError)
		if !ok {
			return "", "", fmt.Errorf("%s %v", outBytes, err)
		}
		errOutput := fmt.Sprintf("%s %s", string(exErr.Stderr), string(outBytes))
		return "", errOutput, fmt.Errorf("%s %v", outBytes, err)
	}
	output := string(outBytes)
	output = strings.TrimSpace(output)
	return output, "", nil
}

func (impl *GitManagerBaseImpl) createCmdWithContext(ctx GitContext, name string, arg ...string) (*exec.Cmd, context.CancelFunc) {
	newCtx := ctx
	cancel := func() {}

	timeout := impl.cfg.CliCmdTimeoutGlobal
	if _, ok := ctx.Deadline(); !ok && timeout > 0 {
		newCtx, cancel = ctx.WithTimeout(timeout)
	}
	cmd := exec.CommandContext(newCtx, name, arg...)
	return cmd, cancel
}

func (impl *GitManagerBaseImpl) GetSshCommand(sshPrivateKeyPath string) string {
	coreSshCommand := fmt.Sprintf("ssh -i %s -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no", sshPrivateKeyPath)
	return coreSshCommand
}

func (impl *GitManagerBaseImpl) ConfigureSshCommand(gitCtx GitContext, rootDir string, sshPrivateKeyPath string) (response, errMsg string, err error) {
	impl.logger.Debugw("configuring ssh command on ", "location", rootDir)
	coreSshCommand := fmt.Sprintf("ssh -i %s -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no", sshPrivateKeyPath)
	cmd, cancel := impl.createCmdWithContext(gitCtx, "git", "-C", rootDir, "config", "core.sshCommand", coreSshCommand)
	defer cancel()
	output, errMsg, err := impl.runCommand(cmd)
	impl.logger.Debugw("configure ssh command output ", "root", rootDir, "opt", output, "errMsg", errMsg, "error", err)
	return output, errMsg, err
}
