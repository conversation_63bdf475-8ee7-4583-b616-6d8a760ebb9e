package bean

import (
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig/bean/workflow/cdWorkflow"
	"github.com/devtron-labs/devtron/pkg/devtronResource/bean"
)

// apiResponse keys from json event used from external helm app response

const (
	ResourceTreeResponse = "resourceTreeResponse"
	ChartMetaDataKey     = "chartMetadata"
	ReleaseStatus        = "releaseStatus"
	ApplicationStatus    = "applicationStatus"
	LastDeployed         = "lastDeployed"
	EnvironmentDetails   = "environmentDetails"
	ReleaseExist         = "ReleaseExist"
	InstalledAppInfo     = "installedAppInfo"
	ReportedTime         = "reportedTime"
)

// output response key
const (
	StatusKey          = "status"
	ServerVersionKey   = "serverVersion"
	LastSnapShotTime   = "lastSnapshotTime"
	CdWorkflowRunnerId = "wfrId"
)

// MapOfResourceTreeStatusVsCdWorkflowRunnerStatus if not found in thsi will update unknown by default
var MapOfResourceTreeStatusVsCdWorkflowRunnerStatus = map[string]string{
	StatusUnknown.ToString():         bean.Unknown,
	StatusDeployed.ToString():        cdWorkflow.WorkflowSucceeded,
	StatusUninstalled.ToString():     bean.Unknown,
	StatusSuperseded.ToString():      cdWorkflow.WorkflowFailed,
	StatusFailed.ToString():          cdWorkflow.WorkflowFailed,
	StatusUninstalling.ToString():    cdWorkflow.WorkflowInProgress,
	StatusPendingInstall.ToString():  cdWorkflow.WorkflowInProgress,
	StatusPendingUpgrade.ToString():  cdWorkflow.WorkflowInProgress,
	StatusPendingRollback.ToString(): cdWorkflow.WorkflowInProgress,
}

type HelmReleaseStatus string

func (h HelmReleaseStatus) ToString() string {
	return string(h)
}

const (
	// StatusUnknown indicates that a release is in an uncertain state.
	StatusUnknown HelmReleaseStatus = "unknown"
	// StatusDeployed indicates that the release has been pushed to Kubernetes.
	StatusDeployed HelmReleaseStatus = "deployed"
	// StatusUninstalled indicates that a release has been uninstalled from Kubernetes.
	StatusUninstalled HelmReleaseStatus = "uninstalled"
	// StatusSuperseded indicates that this release object is outdated and a newer one exists.
	StatusSuperseded HelmReleaseStatus = "superseded"
	// StatusFailed indicates that the release was not successfully deployed.
	StatusFailed HelmReleaseStatus = "failed"
	// StatusUninstalling indicates that a uninstall operation is underway.
	StatusUninstalling HelmReleaseStatus = "uninstalling"
	// StatusPendingInstall indicates that an install operation is underway.
	StatusPendingInstall HelmReleaseStatus = "pending-install"
	// StatusPendingUpgrade indicates that an upgrade operation is underway.
	StatusPendingUpgrade HelmReleaseStatus = "pending-upgrade"
	// StatusPendingRollback indicates that an rollback operation is underway.
	StatusPendingRollback HelmReleaseStatus = "pending-rollback"
)
