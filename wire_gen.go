// Code generated by Wire. DO NOT EDIT.

//go:generate go run github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/devtron-labs/authenticator/apiToken"
	"github.com/devtron-labs/authenticator/client"
	"github.com/devtron-labs/authenticator/middleware"
	"github.com/devtron-labs/common-lib-private/recommended/resources"
	k8s2 "github.com/devtron-labs/common-lib-private/utils/k8s"
	"github.com/devtron-labs/common-lib-private/utils/k8s/proxy"
	"github.com/devtron-labs/common-lib-private/utils/ssh"
	"github.com/devtron-labs/common-lib/cloud-provider-identifier"
	"github.com/devtron-labs/common-lib/env"
	"github.com/devtron-labs/common-lib/pubsub-lib"
	"github.com/devtron-labs/common-lib/telemetry"
	"github.com/devtron-labs/common-lib/utils/grpc"
	"github.com/devtron-labs/common-lib/utils/k8s"
	apiToken2 "github.com/devtron-labs/devtron/api/apiToken"
	"github.com/devtron-labs/devtron/api/appStore"
	"github.com/devtron-labs/devtron/api/appStore/chartCategory"
	chartGroup2 "github.com/devtron-labs/devtron/api/appStore/chartGroup"
	chartProvider2 "github.com/devtron-labs/devtron/api/appStore/chartProvider"
	"github.com/devtron-labs/devtron/api/appStore/deployment"
	"github.com/devtron-labs/devtron/api/appStore/discover"
	"github.com/devtron-labs/devtron/api/appStore/values"
	argoApplication2 "github.com/devtron-labs/devtron/api/argoApplication"
	"github.com/devtron-labs/devtron/api/auth/authorisation/globalConfig"
	sso2 "github.com/devtron-labs/devtron/api/auth/sso"
	user2 "github.com/devtron-labs/devtron/api/auth/user"
	"github.com/devtron-labs/devtron/api/auth/userGroup"
	chartRepo2 "github.com/devtron-labs/devtron/api/chartRepo"
	chat2 "github.com/devtron-labs/devtron/api/chat"
	bean4 "github.com/devtron-labs/devtron/api/chat/bean"
	cluster3 "github.com/devtron-labs/devtron/api/cluster"
	clusterUpgrade2 "github.com/devtron-labs/devtron/api/clusterUpgrade"
	"github.com/devtron-labs/devtron/api/connector"
	"github.com/devtron-labs/devtron/api/dashboardEvent"
	deployment3 "github.com/devtron-labs/devtron/api/deployment"
	deployEntityMigration2 "github.com/devtron-labs/devtron/api/deployment/deployEntityMigration"
	"github.com/devtron-labs/devtron/api/deployment/event"
	devtronResource2 "github.com/devtron-labs/devtron/api/devtronResource"
	"github.com/devtron-labs/devtron/api/drift"
	externalLink2 "github.com/devtron-labs/devtron/api/externalLink"
	"github.com/devtron-labs/devtron/api/featureFlag"
	fileUploader2 "github.com/devtron-labs/devtron/api/fileUploader"
	fluxApplication2 "github.com/devtron-labs/devtron/api/fluxApplication"
	globalPolicy2 "github.com/devtron-labs/devtron/api/globalPolicy"
	client4 "github.com/devtron-labs/devtron/api/helm-app"
	"github.com/devtron-labs/devtron/api/helm-app/gRPC"
	"github.com/devtron-labs/devtron/api/helm-app/service"
	read8 "github.com/devtron-labs/devtron/api/helm-app/service/read"
	"github.com/devtron-labs/devtron/api/infraConfig"
	"github.com/devtron-labs/devtron/api/infrastructureDeployment"
	application3 "github.com/devtron-labs/devtron/api/k8s/application"
	capacity2 "github.com/devtron-labs/devtron/api/k8s/capacity"
	module2 "github.com/devtron-labs/devtron/api/module"
	"github.com/devtron-labs/devtron/api/restHandler"
	"github.com/devtron-labs/devtron/api/restHandler/app/appInfo"
	"github.com/devtron-labs/devtron/api/restHandler/app/appList"
	configDiff2 "github.com/devtron-labs/devtron/api/restHandler/app/configDiff"
	pipeline4 "github.com/devtron-labs/devtron/api/restHandler/app/pipeline"
	"github.com/devtron-labs/devtron/api/restHandler/app/pipeline/configure"
	history3 "github.com/devtron-labs/devtron/api/restHandler/app/pipeline/history"
	status3 "github.com/devtron-labs/devtron/api/restHandler/app/pipeline/status"
	trigger2 "github.com/devtron-labs/devtron/api/restHandler/app/pipeline/trigger"
	"github.com/devtron-labs/devtron/api/restHandler/app/pipeline/webhook"
	"github.com/devtron-labs/devtron/api/restHandler/app/workflow"
	autoRemediation2 "github.com/devtron-labs/devtron/api/restHandler/autoRemediation"
	imageDigestPolicy2 "github.com/devtron-labs/devtron/api/restHandler/imageDigestPolicy"
	resourceFilter2 "github.com/devtron-labs/devtron/api/restHandler/resourceFilter"
	"github.com/devtron-labs/devtron/api/restHandler/scopedVariable"
	"github.com/devtron-labs/devtron/api/router"
	app4 "github.com/devtron-labs/devtron/api/router/app"
	appInfo2 "github.com/devtron-labs/devtron/api/router/app/appInfo"
	appList2 "github.com/devtron-labs/devtron/api/router/app/appList"
	configDiff3 "github.com/devtron-labs/devtron/api/router/app/configDiff"
	pipeline5 "github.com/devtron-labs/devtron/api/router/app/pipeline"
	configure2 "github.com/devtron-labs/devtron/api/router/app/pipeline/configure"
	history4 "github.com/devtron-labs/devtron/api/router/app/pipeline/history"
	status4 "github.com/devtron-labs/devtron/api/router/app/pipeline/status"
	trigger3 "github.com/devtron-labs/devtron/api/router/app/pipeline/trigger"
	workflow2 "github.com/devtron-labs/devtron/api/router/app/workflow"
	scanTool2 "github.com/devtron-labs/devtron/api/scanTool"
	scoop2 "github.com/devtron-labs/devtron/api/scoop"
	server2 "github.com/devtron-labs/devtron/api/server"
	"github.com/devtron-labs/devtron/api/sse"
	systemNetworkController2 "github.com/devtron-labs/devtron/api/systemNetworkController"
	team2 "github.com/devtron-labs/devtron/api/team"
	terminal2 "github.com/devtron-labs/devtron/api/terminal"
	userResource2 "github.com/devtron-labs/devtron/api/userResource"
	util4 "github.com/devtron-labs/devtron/api/util"
	webhookHelm2 "github.com/devtron-labs/devtron/api/webhook/helm"
	"github.com/devtron-labs/devtron/cel"
	"github.com/devtron-labs/devtron/client/argocdServer"
	"github.com/devtron-labs/devtron/client/argocdServer/application"
	"github.com/devtron-labs/devtron/client/argocdServer/bean"
	"github.com/devtron-labs/devtron/client/argocdServer/certificate"
	"github.com/devtron-labs/devtron/client/argocdServer/cluster"
	config3 "github.com/devtron-labs/devtron/client/argocdServer/config"
	"github.com/devtron-labs/devtron/client/argocdServer/connection"
	"github.com/devtron-labs/devtron/client/argocdServer/repoCredsK8sClient"
	repository13 "github.com/devtron-labs/devtron/client/argocdServer/repocreds"
	repository12 "github.com/devtron-labs/devtron/client/argocdServer/repository"
	"github.com/devtron-labs/devtron/client/argocdServer/version"
	"github.com/devtron-labs/devtron/client/chat"
	cron2 "github.com/devtron-labs/devtron/client/cron"
	"github.com/devtron-labs/devtron/client/dashboard"
	client3 "github.com/devtron-labs/devtron/client/events"
	"github.com/devtron-labs/devtron/client/fluxcd"
	"github.com/devtron-labs/devtron/client/gitSensor"
	"github.com/devtron-labs/devtron/client/grafana"
	"github.com/devtron-labs/devtron/client/lens"
	proxy2 "github.com/devtron-labs/devtron/client/proxy"
	"github.com/devtron-labs/devtron/client/scoop"
	grpc2 "github.com/devtron-labs/devtron/client/silverSurfer/grpc"
	telemetry2 "github.com/devtron-labs/devtron/client/telemetry"
	"github.com/devtron-labs/devtron/enterprise/api/artifactPromotionApprovalRequest"
	"github.com/devtron-labs/devtron/enterprise/api/artifactPromotionPolicy"
	"github.com/devtron-labs/devtron/enterprise/api/commonPolicyActions"
	deploymentWindow2 "github.com/devtron-labs/devtron/enterprise/api/deploymentWindow"
	drafts2 "github.com/devtron-labs/devtron/enterprise/api/drafts"
	globalTag2 "github.com/devtron-labs/devtron/enterprise/api/globalTag"
	"github.com/devtron-labs/devtron/enterprise/api/lockConfiguation"
	protect2 "github.com/devtron-labs/devtron/enterprise/api/protect"
	"github.com/devtron-labs/devtron/enterprise/api/resourceScan"
	app3 "github.com/devtron-labs/devtron/enterprise/pkg/app"
	"github.com/devtron-labs/devtron/enterprise/pkg/deploymentWindow"
	"github.com/devtron-labs/devtron/enterprise/pkg/drafts"
	rbac3 "github.com/devtron-labs/devtron/enterprise/pkg/drafts/rbac"
	repository39 "github.com/devtron-labs/devtron/enterprise/pkg/drafts/repository"
	"github.com/devtron-labs/devtron/enterprise/pkg/globalTag"
	"github.com/devtron-labs/devtron/enterprise/pkg/lockConfiguration"
	pipeline3 "github.com/devtron-labs/devtron/enterprise/pkg/pipeline"
	"github.com/devtron-labs/devtron/enterprise/pkg/protect"
	repository38 "github.com/devtron-labs/devtron/enterprise/pkg/protect/repository"
	"github.com/devtron-labs/devtron/enterprise/pkg/resourceFilter"
	repository2 "github.com/devtron-labs/devtron/internal/sql/repository"
	"github.com/devtron-labs/devtron/internal/sql/repository/app"
	"github.com/devtron-labs/devtron/internal/sql/repository/appStatus"
	"github.com/devtron-labs/devtron/internal/sql/repository/appWorkflow"
	"github.com/devtron-labs/devtron/internal/sql/repository/bulkUpdate"
	"github.com/devtron-labs/devtron/internal/sql/repository/chartConfig"
	"github.com/devtron-labs/devtron/internal/sql/repository/chartConfig/configMapRepository"
	"github.com/devtron-labs/devtron/internal/sql/repository/deploymentConfig"
	repository15 "github.com/devtron-labs/devtron/internal/sql/repository/dockerRegistry"
	"github.com/devtron-labs/devtron/internal/sql/repository/helper"
	repository44 "github.com/devtron-labs/devtron/internal/sql/repository/imageTagging"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/internal/sql/repository/resourceGroup"
	"github.com/devtron-labs/devtron/internal/util"
	"github.com/devtron-labs/devtron/internal/util/configUtil"
	"github.com/devtron-labs/devtron/licensing/handler"
	"github.com/devtron-labs/devtron/licensing/licenseClient"
	"github.com/devtron-labs/devtron/pkg/InfrastructureInstallationService"
	"github.com/devtron-labs/devtron/pkg/InfrastructureInstallationService/eventHandler"
	publish2 "github.com/devtron-labs/devtron/pkg/InfrastructureInstallationService/events/publish"
	"github.com/devtron-labs/devtron/pkg/InfrastructureInstallationService/events/subscribe"
	"github.com/devtron-labs/devtron/pkg/InfrastructureInstallationService/installer"
	read4 "github.com/devtron-labs/devtron/pkg/InfrastructureInstallationService/read"
	repository16 "github.com/devtron-labs/devtron/pkg/InfrastructureInstallationService/repository"
	"github.com/devtron-labs/devtron/pkg/apiToken"
	repository27 "github.com/devtron-labs/devtron/pkg/apiToken/repository"
	app2 "github.com/devtron-labs/devtron/pkg/app"
	"github.com/devtron-labs/devtron/pkg/app/appDetails"
	read14 "github.com/devtron-labs/devtron/pkg/app/appDetails/read"
	"github.com/devtron-labs/devtron/pkg/app/dbMigration"
	"github.com/devtron-labs/devtron/pkg/app/status"
	"github.com/devtron-labs/devtron/pkg/appClone"
	"github.com/devtron-labs/devtron/pkg/appClone/batch"
	appStatus2 "github.com/devtron-labs/devtron/pkg/appStatus"
	"github.com/devtron-labs/devtron/pkg/appStore/chartGroup"
	repository48 "github.com/devtron-labs/devtron/pkg/appStore/chartGroup/repository"
	"github.com/devtron-labs/devtron/pkg/appStore/chartProvider"
	"github.com/devtron-labs/devtron/pkg/appStore/discover/repository"
	service10 "github.com/devtron-labs/devtron/pkg/appStore/discover/service"
	read7 "github.com/devtron-labs/devtron/pkg/appStore/installedApp/read"
	repository3 "github.com/devtron-labs/devtron/pkg/appStore/installedApp/repository"
	service9 "github.com/devtron-labs/devtron/pkg/appStore/installedApp/service"
	"github.com/devtron-labs/devtron/pkg/appStore/installedApp/service/EAMode"
	deployment2 "github.com/devtron-labs/devtron/pkg/appStore/installedApp/service/EAMode/deployment"
	"github.com/devtron-labs/devtron/pkg/appStore/installedApp/service/FullMode"
	"github.com/devtron-labs/devtron/pkg/appStore/installedApp/service/FullMode/deployment"
	"github.com/devtron-labs/devtron/pkg/appStore/installedApp/service/FullMode/deploymentTypeChange"
	"github.com/devtron-labs/devtron/pkg/appStore/installedApp/service/FullMode/resource"
	"github.com/devtron-labs/devtron/pkg/appStore/installedApp/service/common"
	"github.com/devtron-labs/devtron/pkg/appStore/values/repository"
	service8 "github.com/devtron-labs/devtron/pkg/appStore/values/service"
	appWorkflow2 "github.com/devtron-labs/devtron/pkg/appWorkflow"
	read27 "github.com/devtron-labs/devtron/pkg/appWorkflow/read"
	"github.com/devtron-labs/devtron/pkg/argoApplication"
	read24 "github.com/devtron-labs/devtron/pkg/argoApplication/read"
	config2 "github.com/devtron-labs/devtron/pkg/argoApplication/read/config"
	"github.com/devtron-labs/devtron/pkg/asyncProvider"
	"github.com/devtron-labs/devtron/pkg/attributes"
	repository49 "github.com/devtron-labs/devtron/pkg/attributes/repository"
	"github.com/devtron-labs/devtron/pkg/auth/authentication"
	"github.com/devtron-labs/devtron/pkg/auth/authorisation/casbin"
	client2 "github.com/devtron-labs/devtron/pkg/auth/authorisation/casbin/client"
	"github.com/devtron-labs/devtron/pkg/auth/authorisation/globalConfig"
	repository5 "github.com/devtron-labs/devtron/pkg/auth/authorisation/globalConfig/repository"
	"github.com/devtron-labs/devtron/pkg/auth/sso"
	"github.com/devtron-labs/devtron/pkg/auth/user"
	repository4 "github.com/devtron-labs/devtron/pkg/auth/user/repository"
	repository8 "github.com/devtron-labs/devtron/pkg/auth/userGroup/repository"
	"github.com/devtron-labs/devtron/pkg/autoRemediation"
	repository41 "github.com/devtron-labs/devtron/pkg/autoRemediation/repository"
	"github.com/devtron-labs/devtron/pkg/build/artifacts"
	"github.com/devtron-labs/devtron/pkg/build/artifacts/imageTagging"
	read25 "github.com/devtron-labs/devtron/pkg/build/artifacts/imageTagging/read"
	"github.com/devtron-labs/devtron/pkg/build/git/gitHost"
	read30 "github.com/devtron-labs/devtron/pkg/build/git/gitHost/read"
	repository47 "github.com/devtron-labs/devtron/pkg/build/git/gitHost/repository"
	read18 "github.com/devtron-labs/devtron/pkg/build/git/gitMaterial/read"
	repository32 "github.com/devtron-labs/devtron/pkg/build/git/gitMaterial/repository"
	"github.com/devtron-labs/devtron/pkg/build/git/gitProvider"
	read11 "github.com/devtron-labs/devtron/pkg/build/git/gitProvider/read"
	repository21 "github.com/devtron-labs/devtron/pkg/build/git/gitProvider/repository"
	"github.com/devtron-labs/devtron/pkg/build/git/gitWebhook"
	repository20 "github.com/devtron-labs/devtron/pkg/build/git/gitWebhook/repository"
	pipeline2 "github.com/devtron-labs/devtron/pkg/build/pipeline"
	read15 "github.com/devtron-labs/devtron/pkg/build/pipeline/read"
	"github.com/devtron-labs/devtron/pkg/build/trigger"
	service11 "github.com/devtron-labs/devtron/pkg/bulkAction/service"
	"github.com/devtron-labs/devtron/pkg/chart"
	"github.com/devtron-labs/devtron/pkg/chart/gitOpsConfig"
	read17 "github.com/devtron-labs/devtron/pkg/chart/read"
	repository52 "github.com/devtron-labs/devtron/pkg/chartCategory/repository"
	service13 "github.com/devtron-labs/devtron/pkg/chartCategory/service"
	"github.com/devtron-labs/devtron/pkg/chartRepo"
	"github.com/devtron-labs/devtron/pkg/chartRepo/repository"
	cluster2 "github.com/devtron-labs/devtron/pkg/cluster"
	"github.com/devtron-labs/devtron/pkg/cluster/environment"
	read5 "github.com/devtron-labs/devtron/pkg/cluster/environment/read"
	"github.com/devtron-labs/devtron/pkg/cluster/environment/repository"
	rbac2 "github.com/devtron-labs/devtron/pkg/cluster/rbac"
	read3 "github.com/devtron-labs/devtron/pkg/cluster/read"
	repository10 "github.com/devtron-labs/devtron/pkg/cluster/repository"
	"github.com/devtron-labs/devtron/pkg/clusterTerminalAccess"
	"github.com/devtron-labs/devtron/pkg/clusterUpgrade"
	"github.com/devtron-labs/devtron/pkg/commonService"
	"github.com/devtron-labs/devtron/pkg/config/configDiff"
	read12 "github.com/devtron-labs/devtron/pkg/config/read"
	delete2 "github.com/devtron-labs/devtron/pkg/delete"
	"github.com/devtron-labs/devtron/pkg/deployment/common"
	read10 "github.com/devtron-labs/devtron/pkg/deployment/common/read"
	"github.com/devtron-labs/devtron/pkg/deployment/deployEntityMigration"
	repository50 "github.com/devtron-labs/devtron/pkg/deployment/deployEntityMigration/repository"
	"github.com/devtron-labs/devtron/pkg/deployment/deployedApp"
	"github.com/devtron-labs/devtron/pkg/deployment/deployedApp/status/resourceTree"
	"github.com/devtron-labs/devtron/pkg/deployment/gitOps/config"
	"github.com/devtron-labs/devtron/pkg/deployment/gitOps/git"
	"github.com/devtron-labs/devtron/pkg/deployment/gitOps/validation"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/configMapAndSecret"
	read29 "github.com/devtron-labs/devtron/pkg/deployment/manifest/configMapAndSecret/read"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deployedAppMetrics"
	repository30 "github.com/devtron-labs/devtron/pkg/deployment/manifest/deployedAppMetrics/repository"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/chartRef"
	read16 "github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/chartRef/read"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/chartResourceConfig"
	repository40 "github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/chartResourceConfig/repository"
	read9 "github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/read"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/validator"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/publish"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/strategy"
	"github.com/devtron-labs/devtron/pkg/deployment/providerConfig"
	"github.com/devtron-labs/devtron/pkg/deployment/resourceTree/devtronApp"
	repository28 "github.com/devtron-labs/devtron/pkg/deployment/resourceTree/devtronApp/repository"
	"github.com/devtron-labs/devtron/pkg/deployment/trigger/devtronApps"
	repository46 "github.com/devtron-labs/devtron/pkg/deployment/trigger/devtronApps/userDeploymentRequest/repository"
	service7 "github.com/devtron-labs/devtron/pkg/deployment/trigger/devtronApps/userDeploymentRequest/service"
	"github.com/devtron-labs/devtron/pkg/deploymentGroup"
	"github.com/devtron-labs/devtron/pkg/devtronResource"
	"github.com/devtron-labs/devtron/pkg/devtronResource/audit"
	"github.com/devtron-labs/devtron/pkg/devtronResource/history/deployment/cdPipeline"
	"github.com/devtron-labs/devtron/pkg/devtronResource/in"
	read2 "github.com/devtron-labs/devtron/pkg/devtronResource/read"
	repository9 "github.com/devtron-labs/devtron/pkg/devtronResource/repository"
	"github.com/devtron-labs/devtron/pkg/devtronResource/taskRun"
	"github.com/devtron-labs/devtron/pkg/dockerRegistry"
	service12 "github.com/devtron-labs/devtron/pkg/drift/managedResourcesService"
	"github.com/devtron-labs/devtron/pkg/eventProcessor"
	"github.com/devtron-labs/devtron/pkg/eventProcessor/celEvaluator"
	in2 "github.com/devtron-labs/devtron/pkg/eventProcessor/in"
	"github.com/devtron-labs/devtron/pkg/eventProcessor/out"
	"github.com/devtron-labs/devtron/pkg/executor"
	"github.com/devtron-labs/devtron/pkg/externalLink"
	helper2 "github.com/devtron-labs/devtron/pkg/featureFlag/helper"
	"github.com/devtron-labs/devtron/pkg/featureFlag/listener"
	service6 "github.com/devtron-labs/devtron/pkg/featureFlag/service"
	"github.com/devtron-labs/devtron/pkg/fileUploader"
	bean3 "github.com/devtron-labs/devtron/pkg/fileUploader/bean"
	read13 "github.com/devtron-labs/devtron/pkg/fileUploader/read"
	repository26 "github.com/devtron-labs/devtron/pkg/fileUploader/repository"
	"github.com/devtron-labs/devtron/pkg/fluxApplication"
	"github.com/devtron-labs/devtron/pkg/generateManifest"
	"github.com/devtron-labs/devtron/pkg/genericNotes"
	repository19 "github.com/devtron-labs/devtron/pkg/genericNotes/repository"
	"github.com/devtron-labs/devtron/pkg/gitops"
	"github.com/devtron-labs/devtron/pkg/globalFlag"
	"github.com/devtron-labs/devtron/pkg/globalPolicy"
	"github.com/devtron-labs/devtron/pkg/globalPolicy/history"
	repository35 "github.com/devtron-labs/devtron/pkg/globalPolicy/history/repository"
	read31 "github.com/devtron-labs/devtron/pkg/globalPolicy/read"
	repository34 "github.com/devtron-labs/devtron/pkg/globalPolicy/repository"
	"github.com/devtron-labs/devtron/pkg/imageDigestPolicy"
	config4 "github.com/devtron-labs/devtron/pkg/infraConfig/config"
	repository22 "github.com/devtron-labs/devtron/pkg/infraConfig/repository"
	audit2 "github.com/devtron-labs/devtron/pkg/infraConfig/repository/audit"
	service2 "github.com/devtron-labs/devtron/pkg/infraConfig/service"
	audit3 "github.com/devtron-labs/devtron/pkg/infraConfig/service/audit"
	k8s3 "github.com/devtron-labs/devtron/pkg/k8s"
	application2 "github.com/devtron-labs/devtron/pkg/k8s/application"
	"github.com/devtron-labs/devtron/pkg/k8s/capacity"
	"github.com/devtron-labs/devtron/pkg/k8s/informer"
	"github.com/devtron-labs/devtron/pkg/k8s/krr"
	job2 "github.com/devtron-labs/devtron/pkg/k8s/krr/job"
	read23 "github.com/devtron-labs/devtron/pkg/k8s/krr/read"
	repository43 "github.com/devtron-labs/devtron/pkg/k8s/krr/repository"
	"github.com/devtron-labs/devtron/pkg/kubernetesResourceAuditLogs"
	repository11 "github.com/devtron-labs/devtron/pkg/kubernetesResourceAuditLogs/repository"
	"github.com/devtron-labs/devtron/pkg/module"
	bean2 "github.com/devtron-labs/devtron/pkg/module/bean"
	"github.com/devtron-labs/devtron/pkg/module/read"
	"github.com/devtron-labs/devtron/pkg/module/repo"
	"github.com/devtron-labs/devtron/pkg/module/store"
	"github.com/devtron-labs/devtron/pkg/notifier"
	"github.com/devtron-labs/devtron/pkg/operationAudit"
	repository6 "github.com/devtron-labs/devtron/pkg/operationAudit/repository"
	"github.com/devtron-labs/devtron/pkg/panel"
	"github.com/devtron-labs/devtron/pkg/panel/repo"
	"github.com/devtron-labs/devtron/pkg/pipeline"
	"github.com/devtron-labs/devtron/pkg/pipeline/bulkSwitchCi"
	"github.com/devtron-labs/devtron/pkg/pipeline/bulkSwitchCi/pipelineConverters"
	"github.com/devtron-labs/devtron/pkg/pipeline/cacheResourceSelector"
	"github.com/devtron-labs/devtron/pkg/pipeline/draftAwareConfigService"
	"github.com/devtron-labs/devtron/pkg/pipeline/executors"
	history2 "github.com/devtron-labs/devtron/pkg/pipeline/history"
	repository36 "github.com/devtron-labs/devtron/pkg/pipeline/history/repository"
	"github.com/devtron-labs/devtron/pkg/pipeline/infraProviders"
	"github.com/devtron-labs/devtron/pkg/pipeline/infraProviders/infraGetters/ci"
	"github.com/devtron-labs/devtron/pkg/pipeline/infraProviders/infraGetters/job"
	repository25 "github.com/devtron-labs/devtron/pkg/pipeline/repository"
	"github.com/devtron-labs/devtron/pkg/pipeline/runtimeParam"
	"github.com/devtron-labs/devtron/pkg/pipeline/types"
	"github.com/devtron-labs/devtron/pkg/pipeline/workflowStatus"
	repository31 "github.com/devtron-labs/devtron/pkg/pipeline/workflowStatus/repository"
	"github.com/devtron-labs/devtron/pkg/plugin"
	repository24 "github.com/devtron-labs/devtron/pkg/plugin/repository"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig"
	read20 "github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/read"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/artifactApproval/action"
	read26 "github.com/devtron-labs/devtron/pkg/policyGovernance/artifactApproval/read"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/artifactPromotion"
	read28 "github.com/devtron-labs/devtron/pkg/policyGovernance/artifactPromotion/read"
	repository45 "github.com/devtron-labs/devtron/pkg/policyGovernance/artifactPromotion/repository"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/common/alpha1"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/common/v0"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/devtronResource/release"
	service5 "github.com/devtron-labs/devtron/pkg/policyGovernance/lockConfiguration/adapter/service"
	repository37 "github.com/devtron-labs/devtron/pkg/policyGovernance/lockConfiguration/repository"
	alpha1_2 "github.com/devtron-labs/devtron/pkg/policyGovernance/plugin/alpha1"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/plugin/alpha1/DAG"
	service4 "github.com/devtron-labs/devtron/pkg/policyGovernance/plugin/alpha1/adapter/service"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/plugin/alpha1/enforcer"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/security/imageScanning"
	read21 "github.com/devtron-labs/devtron/pkg/policyGovernance/security/imageScanning/read"
	repository42 "github.com/devtron-labs/devtron/pkg/policyGovernance/security/imageScanning/repository"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/security/scanTool"
	repository23 "github.com/devtron-labs/devtron/pkg/policyGovernance/security/scanTool/repository"
	"github.com/devtron-labs/devtron/pkg/remoteConnection"
	repository14 "github.com/devtron-labs/devtron/pkg/remoteConnection/repository"
	resourceGroup2 "github.com/devtron-labs/devtron/pkg/resourceGroup"
	"github.com/devtron-labs/devtron/pkg/resourceQualifiers"
	"github.com/devtron-labs/devtron/pkg/server"
	"github.com/devtron-labs/devtron/pkg/server/config"
	"github.com/devtron-labs/devtron/pkg/server/store"
	"github.com/devtron-labs/devtron/pkg/sql"
	"github.com/devtron-labs/devtron/pkg/syncDevImage"
	"github.com/devtron-labs/devtron/pkg/systemNetworkController"
	repository51 "github.com/devtron-labs/devtron/pkg/systemNetworkController/repository"
	"github.com/devtron-labs/devtron/pkg/team"
	read6 "github.com/devtron-labs/devtron/pkg/team/read"
	repository17 "github.com/devtron-labs/devtron/pkg/team/repository"
	"github.com/devtron-labs/devtron/pkg/terminal"
	"github.com/devtron-labs/devtron/pkg/timeoutWindow"
	repository7 "github.com/devtron-labs/devtron/pkg/timeoutWindow/repository"
	"github.com/devtron-labs/devtron/pkg/ucid"
	"github.com/devtron-labs/devtron/pkg/userResource"
	util3 "github.com/devtron-labs/devtron/pkg/util"
	"github.com/devtron-labs/devtron/pkg/valueConstraint"
	read19 "github.com/devtron-labs/devtron/pkg/valueConstraint/read"
	repository29 "github.com/devtron-labs/devtron/pkg/valueConstraint/repository"
	"github.com/devtron-labs/devtron/pkg/variables"
	"github.com/devtron-labs/devtron/pkg/variables/parsers"
	repository18 "github.com/devtron-labs/devtron/pkg/variables/repository"
	"github.com/devtron-labs/devtron/pkg/webhook/helm"
	"github.com/devtron-labs/devtron/pkg/workflow/cd"
	"github.com/devtron-labs/devtron/pkg/workflow/cd/configHistory"
	read22 "github.com/devtron-labs/devtron/pkg/workflow/cd/read"
	"github.com/devtron-labs/devtron/pkg/workflow/dag"
	status2 "github.com/devtron-labs/devtron/pkg/workflow/status"
	"github.com/devtron-labs/devtron/pkg/workflow/trigger/audit/hook"
	repository33 "github.com/devtron-labs/devtron/pkg/workflow/trigger/audit/repository"
	service3 "github.com/devtron-labs/devtron/pkg/workflow/trigger/audit/service"
	util2 "github.com/devtron-labs/devtron/util"
	"github.com/devtron-labs/devtron/util/commonEnforcementFunctionsUtil"
	"github.com/devtron-labs/devtron/util/cron"
	"github.com/devtron-labs/devtron/util/rbac"
	"github.com/devtron-labs/devtron/util/rbac/filter"
)

import (
	_ "github.com/argoproj/argo-cd/v2/pkg/apis/application/v1alpha1"
	_ "github.com/argoproj/argo-workflows/v3/pkg/apis/workflow/v1alpha1"
	_ "github.com/lib/pq"
)

// Injectors from Wire.go:

func InitializeApp() (*App, error) {
	sugaredLogger, err := util.NewSugardLogger()
	if err != nil {
		return nil, err
	}
	sqlConfig, err := sql.GetConfig()
	if err != nil {
		return nil, err
	}
	db, err := sql.NewDbConnection(sqlConfig, sugaredLogger)
	if err != nil {
		return nil, err
	}
	appStatusRepositoryImpl := appStatus.NewAppStatusRepositoryImpl(db, sugaredLogger)
	environmentRepositoryImpl := repository.NewEnvironmentRepositoryImpl(db, sugaredLogger, appStatusRepositoryImpl)
	httpClient := util.NewHttpClient()
	grafanaClientConfig, err := grafana.GetGrafanaClientConfig()
	if err != nil {
		return nil, err
	}
	attributesRepositoryImpl := repository2.NewAttributesRepositoryImpl(db)
	attributesServiceImpl := attributes.NewAttributesServiceImpl(sugaredLogger, attributesRepositoryImpl)
	grafanaClientImpl := grafana.NewGrafanaClientImpl(sugaredLogger, httpClient, grafanaClientConfig, attributesServiceImpl)
	installedAppRepositoryImpl := repository3.NewInstalledAppRepositoryImpl(sugaredLogger, db)
	sshTunnelWrapperServiceImpl, err := ssh.NewSSHTunnelWrapperServiceImpl(sugaredLogger)
	if err != nil {
		return nil, err
	}
	gitOpsConfigRepositoryImpl := repository2.NewGitOpsConfigRepositoryImpl(sugaredLogger, db)
	defaultAuthPolicyRepositoryImpl := repository4.NewDefaultAuthPolicyRepositoryImpl(db, sugaredLogger)
	defaultAuthRoleRepositoryImpl := repository4.NewDefaultAuthRoleRepositoryImpl(db, sugaredLogger)
	userAuthRepositoryImpl := repository4.NewUserAuthRepositoryImpl(db, sugaredLogger, defaultAuthPolicyRepositoryImpl, defaultAuthRoleRepositoryImpl)
	userRepositoryImpl := repository4.NewUserRepositoryImpl(db, sugaredLogger)
	roleGroupRepositoryImpl := repository4.NewRoleGroupRepositoryImpl(db, sugaredLogger)
	runtimeConfig, err := client.GetRuntimeConfig()
	if err != nil {
		return nil, err
	}
	k8sClient, err := client.NewK8sClient(runtimeConfig)
	if err != nil {
		return nil, err
	}
	dexConfig, err := client.BuildDexConfig(k8sClient)
	if err != nil {
		return nil, err
	}
	settings, err := client.GetSettings(dexConfig)
	if err != nil {
		return nil, err
	}
	apiTokenSecretStore := apiTokenAuth.InitApiTokenSecretStore()
	sessionManager := middleware.NewSessionManager(settings, dexConfig, apiTokenSecretStore)
	rbacPolicyDataRepositoryImpl := repository4.NewRbacPolicyDataRepositoryImpl(sugaredLogger, db)
	rbacRoleDataRepositoryImpl := repository4.NewRbacRoleDataRepositoryImpl(sugaredLogger, db)
	rbacDataCacheFactoryImpl := repository4.NewRbacDataCacheFactoryImpl(sugaredLogger, rbacPolicyDataRepositoryImpl, rbacRoleDataRepositoryImpl)
	userCommonServiceImpl, err := user.NewUserCommonServiceImpl(userAuthRepositoryImpl, sugaredLogger, userRepositoryImpl, roleGroupRepositoryImpl, sessionManager, rbacDataCacheFactoryImpl)
	if err != nil {
		return nil, err
	}
	userAuditRepositoryImpl := repository4.NewUserAuditRepositoryImpl(db)
	userAuditServiceImpl := user.NewUserAuditServiceImpl(sugaredLogger, userAuditRepositoryImpl)
	globalAuthorisationConfigRepositoryImpl := repository5.NewGlobalAuthorisationConfigRepositoryImpl(sugaredLogger, db)
	globalAuthorisationConfigServiceImpl := auth.NewGlobalAuthorisationConfigServiceImpl(sugaredLogger, globalAuthorisationConfigRepositoryImpl)
	operationAuditRepositoryImpl := repository6.NewOperationAuditRepositoryImpl(db, sugaredLogger)
	operationAuditServiceImpl := operationAudit.NewOperationAuditServiceImpl(sugaredLogger, operationAuditRepositoryImpl)
	userOperationAuditServiceImpl := operationAudit.NewUserOperationAuditServiceImpl(sugaredLogger, operationAuditServiceImpl)
	roleGroupServiceImpl := user.NewRoleGroupServiceImpl(userAuthRepositoryImpl, sugaredLogger, userRepositoryImpl, roleGroupRepositoryImpl, userCommonServiceImpl, userOperationAuditServiceImpl)
	userAutoAssignGroupMapRepositoryImpl := repository4.NewUserAutoAssignGroupMapRepositoryImpl(db, sugaredLogger)
	syncedEnforcer, err := casbin.Create()
	if err != nil {
		return nil, err
	}
	casbinSyncedEnforcer := casbin.CreateV2()
	casbinClientConfig, err := client2.GetConfig()
	if err != nil {
		return nil, err
	}
	casbinClientImpl := client2.NewCasbinClientImpl(sugaredLogger, casbinClientConfig)
	casbinServiceImpl := casbin.NewCasbinServiceImpl(sugaredLogger, casbinClientImpl)
	enterpriseEnforcerImpl, err := casbin.NewEnterpriseEnforcerImpl(syncedEnforcer, casbinSyncedEnforcer, sessionManager, sugaredLogger, casbinServiceImpl, globalAuthorisationConfigServiceImpl)
	if err != nil {
		return nil, err
	}
	timeWindowRepositoryImpl := repository7.NewTimeWindowRepositoryImpl(db, sugaredLogger)
	timeoutWindowResourceMappingRepositoryImpl := repository7.NewTimeoutWindowResourceMappingRepositoryImpl(db, sugaredLogger)
	timeWindowServiceImpl := timeoutWindow.NewTimeWindowServiceImpl(sugaredLogger, timeWindowRepositoryImpl, timeoutWindowResourceMappingRepositoryImpl)
	usergroupRepositoryImpl := repository8.NewUserGroupRepositoryImpl(db, sugaredLogger)
	userGroupMappingRepositoryImpl := repository8.NewUserGroupMappingRepositoryImpl(db, sugaredLogger)
	userGroupServiceImpl := user.NewUserGroupServiceImpl(sugaredLogger, usergroupRepositoryImpl, userRepositoryImpl, userGroupMappingRepositoryImpl)
	userServiceImpl := user.NewUserServiceImpl(userAuthRepositoryImpl, sugaredLogger, userRepositoryImpl, roleGroupRepositoryImpl, sessionManager, userCommonServiceImpl, userAuditServiceImpl, globalAuthorisationConfigServiceImpl, roleGroupServiceImpl, userAutoAssignGroupMapRepositoryImpl, enterpriseEnforcerImpl, timeWindowServiceImpl, userGroupServiceImpl, userOperationAuditServiceImpl)
	environmentVariables, err := util2.GetEnvironmentVariables()
	if err != nil {
		return nil, err
	}
	moduleRepositoryImpl := moduleRepo.NewModuleRepositoryImpl(db)
	moduleReadServiceImpl := read.NewModuleReadServiceImpl(sugaredLogger, moduleRepositoryImpl)
	gitOpsConfigReadServiceImpl := config.NewGitOpsConfigReadServiceImpl(sugaredLogger, gitOpsConfigRepositoryImpl, userServiceImpl, environmentVariables, moduleReadServiceImpl)
	transactionUtilImpl := sql.NewTransactionUtilImpl(db)
	devtronResourceSearchableKeyRepositoryImpl := repository9.NewDevtronResourceSearchableKeyRepositoryImpl(sugaredLogger, db)
	devtronResourceSearchableKeyServiceImpl, err := read2.NewDevtronResourceSearchableKeyServiceImpl(sugaredLogger, devtronResourceSearchableKeyRepositoryImpl)
	if err != nil {
		return nil, err
	}
	qualifiersMappingRepositoryImpl, err := resourceQualifiers.NewQualifiersMappingRepositoryImpl(db, sugaredLogger, transactionUtilImpl, devtronResourceSearchableKeyServiceImpl)
	if err != nil {
		return nil, err
	}
	qualifierMappingServiceImpl, err := resourceQualifiers.NewQualifierMappingServiceImpl(sugaredLogger, qualifiersMappingRepositoryImpl, devtronResourceSearchableKeyServiceImpl)
	if err != nil {
		return nil, err
	}
	clusterRepositoryImpl := repository10.NewClusterRepositoryImpl(db, sugaredLogger)
	imageDigestPolicyServiceImpl := imageDigestPolicy.NewImageDigestPolicyServiceImpl(sugaredLogger, qualifierMappingServiceImpl, devtronResourceSearchableKeyServiceImpl, environmentRepositoryImpl, clusterRepositoryImpl, db)
	beanConfig, err := bean.GetConfig()
	if err != nil {
		return nil, err
	}
	settingsManager, err := connection.SettingsManager(beanConfig)
	if err != nil {
		return nil, err
	}
	k8sRuntimeConfig, err := k8s.GetRuntimeConfig()
	if err != nil {
		return nil, err
	}
	k8sUtilExtended, err := k8s2.NewK8sUtilExtended(sugaredLogger, k8sRuntimeConfig, sshTunnelWrapperServiceImpl)
	if err != nil {
		return nil, err
	}
	k8sResourceHistoryRepositoryImpl := repository11.NewK8sResourceHistoryRepositoryImpl(db, sugaredLogger)
	appRepositoryImpl := app.NewAppRepositoryImpl(db, sugaredLogger)
	k8sResourceHistoryServiceImpl := kubernetesResourceAuditLogs.Newk8sResourceHistoryServiceImpl(k8sResourceHistoryRepositoryImpl, sugaredLogger, appRepositoryImpl, environmentRepositoryImpl)
	argoApplicationConfigServiceImpl := config2.NewArgoApplicationConfigServiceImpl(sugaredLogger, k8sUtilExtended, clusterRepositoryImpl)
	clusterReadServiceImpl := read3.NewClusterReadServiceImpl(sugaredLogger, clusterRepositoryImpl)
	k8sCommonServiceImpl := k8s3.NewK8sCommonServiceImpl(sugaredLogger, k8sUtilExtended, k8sResourceHistoryServiceImpl, argoApplicationConfigServiceImpl, clusterReadServiceImpl)
	versionServiceImpl := version.NewVersionServiceImpl(sugaredLogger)
	acdAuthConfig, err := util3.GetACDAuthConfig()
	if err != nil {
		return nil, err
	}
	argoCDConfigGetterImpl := config3.NewArgoCDConfigGetter(beanConfig, environmentVariables, acdAuthConfig, clusterReadServiceImpl, sugaredLogger, k8sUtilExtended)
	argoCDConnectionManagerImpl, err := connection.NewArgoCDConnectionManagerImpl(sugaredLogger, settingsManager, moduleRepositoryImpl, environmentVariables, k8sUtilExtended, k8sCommonServiceImpl, versionServiceImpl, gitOpsConfigReadServiceImpl, k8sRuntimeConfig, argoCDConfigGetterImpl)
	if err != nil {
		return nil, err
	}
	serviceClientImpl, err := application.NewApplicationClientImpl(sugaredLogger, argoCDConnectionManagerImpl)
	if err != nil {
		return nil, err
	}
	repositoryServiceClientImpl := repository12.NewServiceClientImpl(sugaredLogger, argoCDConnectionManagerImpl)
	clusterServiceClientImpl := cluster.NewServiceClientImpl(sugaredLogger, argoCDConnectionManagerImpl)
	serviceClientImpl2 := repository13.NewServiceClientImpl(sugaredLogger, argoCDConnectionManagerImpl)
	certificateServiceClientImpl := certificate.NewServiceClientImpl(sugaredLogger, argoCDConnectionManagerImpl)
	acdConfig, err := argocdServer.GetACDDeploymentConfig()
	if err != nil {
		return nil, err
	}
	gitFactory, err := git.NewGitFactory(sugaredLogger, environmentVariables, gitOpsConfigReadServiceImpl)
	if err != nil {
		return nil, err
	}
	chartTemplateServiceImpl := util.NewChartTemplateServiceImpl(sugaredLogger)
	gitOperationServiceImpl := git.NewGitOperationServiceImpl(sugaredLogger, gitFactory, gitOpsConfigReadServiceImpl, chartTemplateServiceImpl, environmentVariables)
	runnable := asyncProvider.NewAsyncRunnable(sugaredLogger)
	repositoryCredsK8sClientImpl := repoCredsK8sClient.NewRepositoryCredsK8sClientImpl(sugaredLogger, k8sUtilExtended)
	argoClientWrapperServiceEAImpl := argocdServer.NewArgoClientWrapperServiceEAImpl(sugaredLogger, repositoryCredsK8sClientImpl, argoCDConfigGetterImpl)
	argoK8sClientImpl := argocdServer.NewArgoK8sClientImpl(sugaredLogger, k8sUtilExtended)
	argoClientWrapperServiceImpl := argocdServer.NewArgoClientWrapperServiceImpl(serviceClientImpl, repositoryServiceClientImpl, clusterServiceClientImpl, serviceClientImpl2, certificateServiceClientImpl, sugaredLogger, acdConfig, gitOpsConfigReadServiceImpl, gitOperationServiceImpl, runnable, argoCDConfigGetterImpl, argoClientWrapperServiceEAImpl, argoK8sClientImpl)
	syncMap := informer.NewGlobalMapClusterNamespace()
	k8sInformerFactoryImpl := informer.NewK8sInformerFactoryImpl(sugaredLogger, syncMap, k8sUtilExtended)
	remoteConnectionRepositoryImpl := repository14.NewRemoteConnectionRepositoryImpl(db, sugaredLogger)
	dockerArtifactStoreRepositoryImpl := repository15.NewDockerArtifactStoreRepositoryImpl(db)
	remoteConnectionServiceImpl := remoteConnection.NewRemoteConnectionServiceImpl(sugaredLogger, remoteConnectionRepositoryImpl, dockerArtifactStoreRepositoryImpl)
	cronLoggerImpl := cron.NewCronLoggerImpl(sugaredLogger)
	infrastructureInstallationRepositoryImpl := repository16.NewInfrastructureInstallationRepositoryImpl(db, sugaredLogger)
	infrastructureInstallationVersionsRepositoryImpl := repository16.NewInfrastructureInstallationVersionsRepositoryImpl(db, sugaredLogger)
	installationReadServiceImpl := read4.NewInstallationReadServiceImpl(sugaredLogger, infrastructureInstallationRepositoryImpl, infrastructureInstallationVersionsRepositoryImpl)
	clusterCategoryRepositoryImpl := repository10.NewClusterCategoryRepositoryImpl(db, sugaredLogger)
	clusterCategoryMappingRepositoryImpl := repository10.NewClusterCategoryMappingRepositoryImpl(db, sugaredLogger)
	loginService := middleware.NewUserLogin(sessionManager, k8sClient)
	userAuthServiceImpl := user.NewUserAuthServiceImpl(userAuthRepositoryImpl, sessionManager, loginService, sugaredLogger, userRepositoryImpl, roleGroupRepositoryImpl, userServiceImpl)
	environmentCategoryMappingRepositoryImpl := repository.NewEnvironmentCategoryMappingRepositoryImpl(db, sugaredLogger)
	clusterCategoryServiceImpl, err := cluster2.NewClusterCategoryServiceImpl(sugaredLogger, clusterCategoryRepositoryImpl, clusterCategoryMappingRepositoryImpl, clusterRepositoryImpl, userAuthServiceImpl, environmentCategoryMappingRepositoryImpl)
	if err != nil {
		return nil, err
	}
	clusterServiceImpl, err := cluster2.NewClusterServiceImpl(clusterRepositoryImpl, sugaredLogger, k8sUtilExtended, k8sInformerFactoryImpl, userAuthRepositoryImpl, userRepositoryImpl, roleGroupRepositoryImpl, globalAuthorisationConfigServiceImpl, userServiceImpl, remoteConnectionServiceImpl, environmentVariables, cronLoggerImpl, clusterReadServiceImpl, installationReadServiceImpl, clusterCategoryServiceImpl)
	if err != nil {
		return nil, err
	}
	clusterServiceImplExtended := cluster2.NewClusterServiceImplExtended(environmentRepositoryImpl, grafanaClientImpl, installedAppRepositoryImpl, sshTunnelWrapperServiceImpl, gitOpsConfigReadServiceImpl, imageDigestPolicyServiceImpl, argoClientWrapperServiceImpl, clusterServiceImpl)
	devtronResourceSchemaRepositoryImpl := repository9.NewDevtronResourceSchemaRepositoryImpl(db, sugaredLogger)
	devtronResourceObjectRepositoryImpl := repository9.NewDevtronResourceObjectRepositoryImpl(sugaredLogger, db)
	devtronResourceRepositoryImpl := repository9.NewDevtronResourceRepositoryImpl(db, sugaredLogger)
	dtResObjDepRelationsRepositoryImpl := repository9.NewDtResObjDepRelationsRepositoryImpl(sugaredLogger, db)
	templateRepositoryImpl := repository9.NewTemplateRepositoryImpl(sugaredLogger, db)
	readServiceImpl, err := read2.NewReadServiceImpl(sugaredLogger, devtronResourceObjectRepositoryImpl, devtronResourceRepositoryImpl, devtronResourceSchemaRepositoryImpl, dtResObjDepRelationsRepositoryImpl, templateRepositoryImpl, userRepositoryImpl)
	if err != nil {
		return nil, err
	}
	devtronResourceObjectAuditRepositoryImpl := repository9.NewDevtronResourceObjectAuditRepositoryImpl(sugaredLogger, db)
	objectAuditServiceImpl := audit.NewObjectAuditServiceImpl(sugaredLogger, devtronResourceObjectAuditRepositoryImpl)
	internalProcessingServiceImpl := in.NewInternalProcessingServiceImpl(sugaredLogger, devtronResourceSchemaRepositoryImpl, devtronResourceObjectRepositoryImpl, readServiceImpl, objectAuditServiceImpl, dtResObjDepRelationsRepositoryImpl)
	environmentCategoryServiceImpl, err := environment.NewEnvironmentCategoryServiceImpl(sugaredLogger, environmentCategoryMappingRepositoryImpl, environmentRepositoryImpl, userAuthServiceImpl)
	if err != nil {
		return nil, err
	}
	environmentServiceImpl := environment.NewEnvironmentServiceImpl(environmentRepositoryImpl, clusterServiceImplExtended, sugaredLogger, k8sUtilExtended, k8sInformerFactoryImpl, userAuthServiceImpl, attributesRepositoryImpl, internalProcessingServiceImpl, clusterReadServiceImpl, grafanaClientImpl, environmentCategoryServiceImpl)
	environmentReadServiceImpl := read5.NewEnvironmentReadServiceImpl(sugaredLogger, environmentRepositoryImpl, environmentCategoryServiceImpl)
	validate, err := util.IntValidator()
	if err != nil {
		return nil, err
	}
	teamRepositoryImpl := repository17.NewTeamRepositoryImpl(db)
	teamReadServiceImpl := read6.NewTeamReadService(sugaredLogger, teamRepositoryImpl)
	teamServiceImpl := team.NewTeamServiceImpl(sugaredLogger, teamRepositoryImpl, userAuthServiceImpl, teamReadServiceImpl)
	pipelineRepositoryImpl := pipelineConfig.NewPipelineRepositoryImpl(db, sugaredLogger)
	chartRepoRepositoryImpl := chartRepoRepository.NewChartRepoRepositoryImpl(db)
	serverEnvConfigServerEnvConfig, err := serverEnvConfig.ParseServerEnvConfig()
	if err != nil {
		return nil, err
	}
	chartRepositoryServiceImpl := chartRepo.NewChartRepositoryServiceImpl(sugaredLogger, chartRepoRepositoryImpl, k8sUtilExtended, acdAuthConfig, httpClient, serverEnvConfigServerEnvConfig, argoClientWrapperServiceImpl, clusterReadServiceImpl)
	helmClientConfig, err := gRPC.GetConfig()
	if err != nil {
		return nil, err
	}
	configuration, err := grpc.GetConfiguration()
	if err != nil {
		return nil, err
	}
	helmAppClientImpl := gRPC.NewHelmAppClientImpl(sugaredLogger, helmClientConfig, configuration)
	pumpImpl := connector.NewPumpImpl(sugaredLogger)
	installedAppReadServiceEAImpl := read7.NewInstalledAppReadServiceEAImpl(sugaredLogger, installedAppRepositoryImpl)
	dbMigrationServiceImpl := dbMigration.NewDbMigrationServiceImpl(sugaredLogger, appRepositoryImpl, installedAppReadServiceEAImpl)
	enforcerUtilHelmImpl := rbac.NewEnforcerUtilHelmImpl(sugaredLogger, clusterRepositoryImpl, appRepositoryImpl, installedAppRepositoryImpl, dbMigrationServiceImpl, teamReadServiceImpl)
	serverDataStoreServerDataStore := serverDataStore.InitServerDataStore()
	appStoreApplicationVersionRepositoryImpl := appStoreDiscoverRepository.NewAppStoreApplicationVersionRepositoryImpl(sugaredLogger, db)
	helmReleaseConfig, err := service.GetHelmReleaseConfig()
	if err != nil {
		return nil, err
	}
	portForwardManagerImpl, err := proxy.NewPortForwardManagerImpl(sugaredLogger, k8sUtilExtended)
	if err != nil {
		return nil, err
	}
	interClusterServiceCommunicationHandlerImpl, err := proxy.NewInterClusterServiceCommunicationHandlerImpl(sugaredLogger, portForwardManagerImpl)
	if err != nil {
		return nil, err
	}
	scoopClientGetterImpl, err := scoop.NewScoopClientGetter(environmentServiceImpl, sugaredLogger, clusterReadServiceImpl, k8sUtilExtended, interClusterServiceCommunicationHandlerImpl)
	if err != nil {
		return nil, err
	}
	helmAppReadServiceImpl := read8.NewHelmAppReadServiceImpl(sugaredLogger, clusterReadServiceImpl)
	installedAppVersionHistoryRepositoryImpl := repository3.NewInstalledAppVersionHistoryRepositoryImpl(sugaredLogger, db)
	repositoryImpl := deploymentConfig.NewRepositoryImpl(db)
	chartRepositoryImpl := chartRepoRepository.NewChartRepository(db, transactionUtilImpl)
	envConfigOverrideRepositoryImpl := chartConfig.NewEnvConfigOverrideRepository(db)
	mergeUtil := configUtil.MergeUtil{
		Logger: sugaredLogger,
	}
	scopedVariableRepositoryImpl := repository18.NewScopedVariableRepository(db, sugaredLogger, transactionUtilImpl)
	scopedVariableServiceImpl, err := variables.NewScopedVariableServiceImpl(sugaredLogger, scopedVariableRepositoryImpl, appRepositoryImpl, environmentRepositoryImpl, devtronResourceSearchableKeyServiceImpl, clusterRepositoryImpl, qualifierMappingServiceImpl, attributesServiceImpl)
	if err != nil {
		return nil, err
	}
	variableEntityMappingRepositoryImpl := repository18.NewVariableEntityMappingRepository(sugaredLogger, db, transactionUtilImpl)
	variableEntityMappingServiceImpl := variables.NewVariableEntityMappingServiceImpl(variableEntityMappingRepositoryImpl, sugaredLogger)
	variableSnapshotHistoryRepositoryImpl := repository18.NewVariableSnapshotHistoryRepository(sugaredLogger, db)
	variableSnapshotHistoryServiceImpl := variables.NewVariableSnapshotHistoryServiceImpl(variableSnapshotHistoryRepositoryImpl, sugaredLogger)
	variableTemplateParserImpl, err := parsers.NewVariableTemplateParserImpl(sugaredLogger)
	if err != nil {
		return nil, err
	}
	scopeManagerConfig, err := variables.GetScopeManagerConfig()
	if err != nil {
		return nil, err
	}
	scopedVariableManagerImpl, err := variables.NewScopedVariableManagerImpl(sugaredLogger, scopedVariableServiceImpl, variableEntityMappingServiceImpl, variableSnapshotHistoryServiceImpl, variableTemplateParserImpl, scopeManagerConfig)
	if err != nil {
		return nil, err
	}
	envConfigOverrideReadServiceExtendedImpl := read9.NewEnvConfigOverrideReadServiceExtendedImpl(envConfigOverrideRepositoryImpl, sugaredLogger, chartRepositoryImpl, mergeUtil, scopedVariableManagerImpl)
	chartRefRepositoryImpl := chartRepoRepository.NewChartRefRepositoryImpl(db)
	deploymentConfigReadServiceImpl := read10.NewDeploymentConfigReadServiceImpl(sugaredLogger, repositoryImpl, environmentVariables, chartRepositoryImpl, pipelineRepositoryImpl, appRepositoryImpl, environmentRepositoryImpl, envConfigOverrideReadServiceExtendedImpl)
	deploymentConfigServiceImpl := common.NewDeploymentConfigServiceImpl(repositoryImpl, sugaredLogger, chartRepositoryImpl, pipelineRepositoryImpl, appRepositoryImpl, installedAppReadServiceEAImpl, environmentVariables, envConfigOverrideReadServiceExtendedImpl, environmentRepositoryImpl, chartRefRepositoryImpl, deploymentConfigReadServiceImpl, acdAuthConfig)
	installedAppDBServiceImpl := EAMode.NewInstalledAppDBServiceImpl(sugaredLogger, installedAppRepositoryImpl, appRepositoryImpl, userServiceImpl, environmentServiceImpl, installedAppVersionHistoryRepositoryImpl, deploymentConfigServiceImpl)
	helmAppServiceImpl := service.NewHelmAppServiceImpl(sugaredLogger, clusterServiceImplExtended, helmAppClientImpl, pumpImpl, enforcerUtilHelmImpl, serverDataStoreServerDataStore, serverEnvConfigServerEnvConfig, appStoreApplicationVersionRepositoryImpl, environmentServiceImpl, pipelineRepositoryImpl, installedAppRepositoryImpl, appRepositoryImpl, clusterRepositoryImpl, k8sUtilExtended, helmReleaseConfig, remoteConnectionServiceImpl, acdAuthConfig, scoopClientGetterImpl, helmAppReadServiceImpl, installedAppDBServiceImpl, clusterReadServiceImpl)
	dockerRegistryIpsConfigRepositoryImpl := repository15.NewDockerRegistryIpsConfigRepositoryImpl(db)
	ociRegistryConfigRepositoryImpl := repository15.NewOCIRegistryConfigRepositoryImpl(db)
	dockerRegistryConfigImpl := pipeline.NewDockerRegistryConfigImpl(sugaredLogger, helmAppServiceImpl, dockerArtifactStoreRepositoryImpl, dockerRegistryIpsConfigRepositoryImpl, ociRegistryConfigRepositoryImpl, remoteConnectionServiceImpl, remoteConnectionRepositoryImpl, argoClientWrapperServiceImpl)
	deleteServiceExtendedImpl := delete2.NewDeleteServiceExtendedImpl(sugaredLogger, teamServiceImpl, clusterServiceImplExtended, environmentServiceImpl, appRepositoryImpl, environmentRepositoryImpl, pipelineRepositoryImpl, chartRepositoryServiceImpl, installedAppRepositoryImpl, dockerRegistryConfigImpl, dockerArtifactStoreRepositoryImpl, internalProcessingServiceImpl, k8sUtilExtended, k8sInformerFactoryImpl, infrastructureInstallationRepositoryImpl, clusterCategoryServiceImpl, environmentCategoryServiceImpl)
	ciPipelineRepositoryImpl := pipelineConfig.NewCiPipelineRepositoryImpl(db, sugaredLogger, transactionUtilImpl)
	enforcerUtilImpl := rbac.NewEnforcerUtilImpl(sugaredLogger, appRepositoryImpl, environmentRepositoryImpl, pipelineRepositoryImpl, ciPipelineRepositoryImpl, clusterRepositoryImpl, enterpriseEnforcerImpl, devtronResourceObjectRepositoryImpl, dtResObjDepRelationsRepositoryImpl, dbMigrationServiceImpl, teamReadServiceImpl)
	commonEnforcementUtilImpl := commonEnforcementFunctionsUtil.NewCommonEnforcementUtilImpl(enterpriseEnforcerImpl, enforcerUtilImpl, sugaredLogger, userServiceImpl, userCommonServiceImpl)
	environmentRestHandlerImpl := cluster3.NewEnvironmentRestHandlerImpl(environmentServiceImpl, environmentReadServiceImpl, sugaredLogger, userServiceImpl, validate, enterpriseEnforcerImpl, deleteServiceExtendedImpl, k8sUtilExtended, k8sCommonServiceImpl, commonEnforcementUtilImpl, environmentCategoryServiceImpl)
	environmentRouterImpl := cluster3.NewEnvironmentRouterImpl(environmentRestHandlerImpl)
	genericNoteRepositoryImpl := repository19.NewGenericNoteRepositoryImpl(db, transactionUtilImpl)
	genericNoteHistoryRepositoryImpl := repository19.NewGenericNoteHistoryRepositoryImpl(db, transactionUtilImpl)
	genericNoteHistoryServiceImpl := genericNotes.NewGenericNoteHistoryServiceImpl(genericNoteHistoryRepositoryImpl, sugaredLogger)
	genericNoteServiceImpl := genericNotes.NewGenericNoteServiceImpl(genericNoteRepositoryImpl, genericNoteHistoryServiceImpl, userRepositoryImpl, sugaredLogger)
	clusterDescriptionRepositoryImpl := repository10.NewClusterDescriptionRepositoryImpl(db, sugaredLogger)
	clusterDescriptionServiceImpl := cluster2.NewClusterDescriptionServiceImpl(clusterDescriptionRepositoryImpl, userRepositoryImpl, sugaredLogger)
	clusterRbacServiceImpl := rbac2.NewClusterRbacServiceImpl(environmentServiceImpl, enterpriseEnforcerImpl, enforcerUtilImpl, clusterServiceImplExtended, sugaredLogger, userServiceImpl, clusterReadServiceImpl)
	panelRepositoryImpl := repo.NewPanelRepositoryImpl(db)
	panelServiceImpl := panel.NewPanelServiceImpl(panelRepositoryImpl, clusterServiceImplExtended)
	clusterRestHandlerImpl := cluster3.NewClusterRestHandlerImpl(clusterServiceImplExtended, genericNoteServiceImpl, clusterDescriptionServiceImpl, sugaredLogger, userServiceImpl, validate, enterpriseEnforcerImpl, deleteServiceExtendedImpl, environmentServiceImpl, clusterRbacServiceImpl, panelServiceImpl, attributesServiceImpl, clusterReadServiceImpl, clusterCategoryServiceImpl)
	clusterRouterImpl := cluster3.NewClusterRouterImpl(clusterRestHandlerImpl)
	gitWebhookRepositoryImpl := repository20.NewGitWebhookRepositoryImpl(db)
	ciCdConfig, err := types.GetCiCdConfig()
	if err != nil {
		return nil, err
	}
	gitProviderRepositoryImpl := repository21.NewGitProviderRepositoryImpl(db)
	gitProviderReadServiceImpl := read11.NewGitProviderReadService(sugaredLogger, gitProviderRepositoryImpl)
	commonBaseServiceImpl := commonService.NewCommonBaseServiceImpl(sugaredLogger, environmentVariables, moduleReadServiceImpl)
	commonServiceImpl := commonService.NewCommonServiceImpl(sugaredLogger, chartRepositoryImpl, envConfigOverrideRepositoryImpl, dockerArtifactStoreRepositoryImpl, attributesRepositoryImpl, environmentRepositoryImpl, appRepositoryImpl, gitOpsConfigReadServiceImpl, gitProviderReadServiceImpl, envConfigOverrideReadServiceExtendedImpl, commonBaseServiceImpl, teamReadServiceImpl)
	configMapRepositoryImpl := configMapRepository.NewConfigMapRepositoryImpl(sugaredLogger, db)
	scopedVariableCMCSManagerImpl, err := variables.NewScopedVariableCMCSManagerImpl(sugaredLogger, scopedVariableServiceImpl, variableEntityMappingServiceImpl, variableSnapshotHistoryServiceImpl, variableTemplateParserImpl, scopeManagerConfig)
	if err != nil {
		return nil, err
	}
	configReadServiceImpl := read12.NewConfigReadServiceImpl(sugaredLogger, commonServiceImpl, configMapRepositoryImpl, mergeUtil, scopedVariableCMCSManagerImpl)
	globalCMCSRepositoryImpl := repository2.NewGlobalCMCSRepositoryImpl(sugaredLogger, db)
	globalCMCSServiceImpl := pipeline.NewGlobalCMCSServiceImpl(sugaredLogger, globalCMCSRepositoryImpl)
	argoWorkflowExecutorImpl := executors.NewArgoWorkflowExecutorImpl(sugaredLogger)
	systemWorkflowExecutorImpl := executors.NewSystemWorkflowExecutorImpl(sugaredLogger, k8sUtilExtended)
	infraConfigAuditRepositoryImpl := audit2.NewInfraConfigAuditRepositoryImpl(db)
	infraConfigAuditServiceImpl := audit3.NewInfraConfigAuditServiceImpl(sugaredLogger, infraConfigAuditRepositoryImpl, transactionUtilImpl)
	infraGetter, err := job.NewJobInfraGetter(sugaredLogger, configReadServiceImpl, infraConfigAuditServiceImpl)
	if err != nil {
		return nil, err
	}
	infraConfigRepositoryImpl := repository22.NewInfraProfileRepositoryImpl(db, transactionUtilImpl)
	pipelineOverrideRepositoryImpl := chartConfig.NewPipelineOverrideRepository(db)
	configUtilMergeUtil := &configUtil.MergeUtil{
		Logger: sugaredLogger,
	}
	eventClientConfig, err := client3.GetEventClientConfig()
	if err != nil {
		return nil, err
	}
	pubSubClientServiceImpl, err := pubsub_lib.NewPubSubClientServiceImpl(sugaredLogger)
	if err != nil {
		return nil, err
	}
	moduleActionAuditLogRepositoryImpl := module.NewModuleActionAuditLogRepositoryImpl(db)
	serverCacheServiceImpl, err := server.NewServerCacheServiceImpl(sugaredLogger, serverEnvConfigServerEnvConfig, serverDataStoreServerDataStore, helmAppServiceImpl)
	if err != nil {
		return nil, err
	}
	moduleEnvConfig, err := bean2.ParseModuleEnvConfig()
	if err != nil {
		return nil, err
	}
	moduleCacheServiceImpl, err := module.NewModuleCacheServiceImpl(sugaredLogger, k8sUtilExtended, moduleEnvConfig, serverEnvConfigServerEnvConfig, serverDataStoreServerDataStore, moduleRepositoryImpl, teamReadServiceImpl)
	if err != nil {
		return nil, err
	}
	moduleServiceHelperImpl := module.NewModuleServiceHelperImpl(serverEnvConfigServerEnvConfig)
	moduleResourceStatusRepositoryImpl := moduleRepo.NewModuleResourceStatusRepositoryImpl(db)
	moduleDataStoreModuleDataStore := moduleDataStore.InitModuleDataStore()
	moduleCronServiceImpl, err := module.NewModuleCronServiceImpl(sugaredLogger, moduleEnvConfig, moduleRepositoryImpl, serverEnvConfigServerEnvConfig, helmAppServiceImpl, moduleServiceHelperImpl, moduleResourceStatusRepositoryImpl, moduleDataStoreModuleDataStore, cronLoggerImpl)
	if err != nil {
		return nil, err
	}
	scanToolMetadataRepositoryImpl := repository23.NewScanToolMetadataRepositoryImpl(db, sugaredLogger)
	globalPluginRepositoryImpl := repository24.NewGlobalPluginRepository(sugaredLogger, db)
	pipelineStageRepositoryImpl := repository25.NewPipelineStageRepository(sugaredLogger, db)
	fileReferenceRepositoryImpl := repository26.NewFileReferenceRepositoryImpl(db)
	fileReferenceReaderImpl := read13.NewFileReferenceReaderImpl(sugaredLogger, fileReferenceRepositoryImpl)
	globalPluginServiceImpl := plugin.NewGlobalPluginService(sugaredLogger, globalPluginRepositoryImpl, pipelineStageRepositoryImpl, userServiceImpl, fileReferenceReaderImpl)
	scanToolMetadataServiceImpl := scanTool.NewScanToolMetadataServiceImpl(sugaredLogger, scanToolMetadataRepositoryImpl, globalPluginServiceImpl, transactionUtilImpl)
	moduleServiceImpl := module.NewModuleServiceImpl(sugaredLogger, serverEnvConfigServerEnvConfig, moduleRepositoryImpl, moduleActionAuditLogRepositoryImpl, helmAppServiceImpl, serverDataStoreServerDataStore, serverCacheServiceImpl, moduleCacheServiceImpl, moduleCronServiceImpl, moduleServiceHelperImpl, moduleResourceStatusRepositoryImpl, scanToolMetadataServiceImpl, environmentVariables, moduleEnvConfig)
	notificationSettingsRepositoryImpl := repository2.NewNotificationSettingsRepositoryImpl(db)
	eventRESTClientImpl := client3.NewEventRESTClientImpl(sugaredLogger, httpClient, eventClientConfig, pubSubClientServiceImpl, ciPipelineRepositoryImpl, pipelineRepositoryImpl, attributesRepositoryImpl, moduleServiceImpl, notificationSettingsRepositoryImpl)
	cdWorkflowRepositoryImpl := pipelineConfig.NewCdWorkflowRepositoryImpl(db, sugaredLogger)
	ciWorkflowRepositoryImpl := pipelineConfig.NewCiWorkflowRepositoryImpl(db, sugaredLogger)
	ciPipelineMaterialRepositoryImpl := pipelineConfig.NewCiPipelineMaterialRepositoryImpl(db, sugaredLogger)
	ciArtifactRepositoryImpl := repository2.NewCiArtifactRepositoryImpl(db, sugaredLogger)
	requestApprovalUserDataRepositoryImpl := pipelineConfig.NewRequestApprovalUserDataRepositoryImpl(db, sugaredLogger)
	sesNotificationRepositoryImpl := repository2.NewSESNotificationRepositoryImpl(db)
	smtpNotificationRepositoryImpl := repository2.NewSMTPNotificationRepositoryImpl(db)
	apiTokenSecretServiceImpl, err := apiToken.NewApiTokenSecretServiceImpl(sugaredLogger, attributesServiceImpl, apiTokenSecretStore)
	if err != nil {
		return nil, err
	}
	apiTokenRepositoryImpl := repository27.NewApiTokenRepositoryImpl(db)
	apiTokenServiceImpl, err := apiToken.NewApiTokenServiceImpl(sugaredLogger, apiTokenSecretServiceImpl, userServiceImpl, userAuditServiceImpl, apiTokenRepositoryImpl)
	if err != nil {
		return nil, err
	}
	filterEvaluationAuditRepositoryImpl := resourceFilter.NewFilterEvaluationAuditRepositoryImpl(sugaredLogger, db)
	filterAuditRepositoryImpl := resourceFilter.NewFilterAuditRepositoryImpl(sugaredLogger, db)
	filterEvaluationAuditServiceImpl := resourceFilter.NewFilterEvaluationAuditServiceImpl(sugaredLogger, filterEvaluationAuditRepositoryImpl, filterAuditRepositoryImpl)
	webhookNotificationRepositoryImpl := repository2.NewWebhookNotificationRepositoryImpl(db)
	slackNotificationRepositoryImpl := repository2.NewSlackNotificationRepositoryImpl(db)
	eventSimpleFactoryImpl := client3.NewEventSimpleFactoryImpl(sugaredLogger, cdWorkflowRepositoryImpl, pipelineOverrideRepositoryImpl, ciWorkflowRepositoryImpl, ciPipelineMaterialRepositoryImpl, ciPipelineRepositoryImpl, pipelineRepositoryImpl, userRepositoryImpl, ciArtifactRepositoryImpl, requestApprovalUserDataRepositoryImpl, sesNotificationRepositoryImpl, smtpNotificationRepositoryImpl, appRepositoryImpl, environmentRepositoryImpl, apiTokenServiceImpl, filterEvaluationAuditServiceImpl, webhookNotificationRepositoryImpl, slackNotificationRepositoryImpl)
	pipelineStatusTimelineRepositoryImpl := pipelineConfig.NewPipelineStatusTimelineRepositoryImpl(db, sugaredLogger)
	pipelineStatusTimelineResourcesRepositoryImpl := pipelineConfig.NewPipelineStatusTimelineResourcesRepositoryImpl(db, sugaredLogger)
	pipelineStatusTimelineResourcesServiceImpl := status.NewPipelineStatusTimelineResourcesServiceImpl(db, sugaredLogger, pipelineStatusTimelineResourcesRepositoryImpl)
	pipelineStatusSyncDetailRepositoryImpl := pipelineConfig.NewPipelineStatusSyncDetailRepositoryImpl(db, sugaredLogger)
	pipelineStatusSyncDetailServiceImpl := status.NewPipelineStatusSyncDetailServiceImpl(sugaredLogger, pipelineStatusSyncDetailRepositoryImpl)
	pipelineStatusTimelineServiceImpl := status.NewPipelineStatusTimelineServiceImpl(sugaredLogger, pipelineStatusTimelineRepositoryImpl, cdWorkflowRepositoryImpl, userServiceImpl, pipelineStatusTimelineResourcesServiceImpl, pipelineStatusSyncDetailServiceImpl, installedAppRepositoryImpl, installedAppVersionHistoryRepositoryImpl, deploymentConfigServiceImpl)
	appServiceConfig, err := app2.GetAppServiceConfig()
	if err != nil {
		return nil, err
	}
	appStatusServiceImpl := appStatus2.NewAppStatusServiceImpl(appStatusRepositoryImpl, sugaredLogger, enterpriseEnforcerImpl, enforcerUtilImpl)
	installedAppReadServiceImpl := read7.NewInstalledAppReadServiceImpl(installedAppReadServiceEAImpl)
	appListingRepositoryQueryBuilder := helper.NewAppListingRepositoryQueryBuilder(sugaredLogger)
	appListingRepositoryImpl := repository2.NewAppListingRepositoryImpl(sugaredLogger, db, appListingRepositoryQueryBuilder, environmentRepositoryImpl)
	appWorkflowRepositoryImpl := appWorkflow.NewAppWorkflowRepositoryImpl(sugaredLogger, db)
	deploymentEventRepositoryImpl := repository28.NewDeploymentEventRepositoryImpl(sugaredLogger, db)
	appDetailsReadServiceImpl := read14.NewAppDetailsReadServiceImpl(db, sugaredLogger, gitOpsConfigReadServiceImpl, deploymentConfigReadServiceImpl, appWorkflowRepositoryImpl, deploymentEventRepositoryImpl, appListingRepositoryImpl)
	appListingViewBuilderImpl := app2.NewAppListingViewBuilderImpl(sugaredLogger)
	linkoutsRepositoryImpl := repository2.NewLinkoutsRepositoryImpl(sugaredLogger, db)
	ciTemplateOverrideRepositoryImpl := pipelineConfig.NewCiTemplateOverrideRepositoryImpl(db, sugaredLogger)
	ciPipelineConfigReadServiceImpl := read15.NewCiPipelineConfigReadServiceImpl(sugaredLogger, ciPipelineRepositoryImpl, ciTemplateOverrideRepositoryImpl)
	dockerRegistryIpsConfigServiceImpl := dockerRegistry.NewDockerRegistryIpsConfigServiceImpl(sugaredLogger, dockerRegistryIpsConfigRepositoryImpl, k8sUtilExtended, dockerArtifactStoreRepositoryImpl, clusterReadServiceImpl, ciPipelineConfigReadServiceImpl)
	appLabelRepositoryImpl := pipelineConfig.NewAppLabelRepositoryImpl(db)
	globalTagRepositoryImpl := globalTag.NewGlobalTagRepositoryImpl(db)
	valueConstraintRepositoryImpl := repository29.NewValueConstraintRepositoryImpl(db, transactionUtilImpl)
	constraintServiceImpl := valueConstraint.NewConstraintServiceImpl(sugaredLogger, valueConstraintRepositoryImpl)
	globalTagServiceImpl := globalTag.NewGlobalTagServiceImpl(sugaredLogger, globalTagRepositoryImpl, constraintServiceImpl)
	appLevelMetricsRepositoryImpl := repository30.NewAppLevelMetricsRepositoryImpl(db, sugaredLogger)
	envLevelAppMetricsRepositoryImpl := repository30.NewEnvLevelAppMetricsRepositoryImpl(db, sugaredLogger)
	chartRefReadServiceImpl := read16.NewChartRefReadServiceImpl(sugaredLogger, chartRefRepositoryImpl)
	globalStrategyMetadataChartRefMappingRepositoryImpl := chartRepoRepository.NewGlobalStrategyMetadataChartRefMappingRepositoryImpl(db, sugaredLogger)
	chartRefServiceImpl := chartRef.NewChartRefServiceImpl(sugaredLogger, chartRefRepositoryImpl, chartRefReadServiceImpl, chartTemplateServiceImpl, chartRepositoryImpl, mergeUtil, globalStrategyMetadataChartRefMappingRepositoryImpl)
	deployedAppMetricsServiceImpl := deployedAppMetrics.NewDeployedAppMetricsServiceImpl(sugaredLogger, appLevelMetricsRepositoryImpl, envLevelAppMetricsRepositoryImpl, chartRefServiceImpl)
	chartReadServiceImpl := read17.NewChartReadServiceImpl(sugaredLogger, chartRepositoryImpl, deploymentConfigServiceImpl, deployedAppMetricsServiceImpl, gitOpsConfigReadServiceImpl, chartRefReadServiceImpl)
	appListingServiceImpl := app2.NewAppListingServiceImpl(sugaredLogger, appListingRepositoryImpl, appDetailsReadServiceImpl, appRepositoryImpl, appListingViewBuilderImpl, pipelineRepositoryImpl, linkoutsRepositoryImpl, cdWorkflowRepositoryImpl, pipelineOverrideRepositoryImpl, environmentRepositoryImpl, chartRepositoryImpl, ciPipelineRepositoryImpl, dockerRegistryIpsConfigServiceImpl, userRepositoryImpl, appLabelRepositoryImpl, globalTagServiceImpl, deployedAppMetricsServiceImpl, ciArtifactRepositoryImpl, envConfigOverrideReadServiceExtendedImpl, ciPipelineConfigReadServiceImpl, deploymentConfigReadServiceImpl, helmAppReadServiceImpl, helmAppServiceImpl, environmentVariables, chartReadServiceImpl)
	workflowStageRepositoryImpl := repository31.NewWorkflowStageRepositoryImpl(sugaredLogger, db)
	workFlowStageStatusServiceImpl := workflowStatus.NewWorkflowStageFlowStatusServiceImpl(sugaredLogger, workflowStageRepositoryImpl, ciWorkflowRepositoryImpl, cdWorkflowRepositoryImpl, transactionUtilImpl)
	cdWorkflowRunnerServiceImpl := cd.NewCdWorkflowRunnerServiceImpl(sugaredLogger, cdWorkflowRepositoryImpl, workFlowStageStatusServiceImpl, transactionUtilImpl)
	deploymentEventHandlerImpl := app2.NewDeploymentEventHandlerImpl(sugaredLogger, appListingServiceImpl, eventRESTClientImpl, eventSimpleFactoryImpl, runnable)
	appServiceImpl := app2.NewAppService(pipelineOverrideRepositoryImpl, configUtilMergeUtil, sugaredLogger, pipelineRepositoryImpl, eventRESTClientImpl, eventSimpleFactoryImpl, appRepositoryImpl, configMapRepositoryImpl, chartRepositoryImpl, cdWorkflowRepositoryImpl, commonServiceImpl, chartTemplateServiceImpl, pipelineStatusTimelineRepositoryImpl, pipelineStatusTimelineResourcesServiceImpl, pipelineStatusSyncDetailServiceImpl, pipelineStatusTimelineServiceImpl, appServiceConfig, appStatusServiceImpl, installedAppReadServiceImpl, installedAppVersionHistoryRepositoryImpl, dockerArtifactStoreRepositoryImpl, scopedVariableCMCSManagerImpl, acdConfig, gitOpsConfigReadServiceImpl, gitOperationServiceImpl, appListingServiceImpl, deploymentConfigServiceImpl, envConfigOverrideReadServiceExtendedImpl, cdWorkflowRunnerServiceImpl, deploymentEventHandlerImpl)
	infraConfigClientImpl := config4.NewInfraConfigClient(sugaredLogger, scopedVariableManagerImpl, configReadServiceImpl)
	infraConfigServiceImpl, err := service2.NewInfraConfigServiceImpl(sugaredLogger, infraConfigRepositoryImpl, appServiceImpl, devtronResourceSearchableKeyServiceImpl, qualifierMappingServiceImpl, attributesServiceImpl, infraConfigClientImpl, environmentVariables)
	if err != nil {
		return nil, err
	}
	ciInfraGetter := ci.NewCiInfraGetter(sugaredLogger, infraConfigServiceImpl, infraConfigAuditServiceImpl)
	infraProviderImpl := infraProviders.NewInfraProviderImpl(sugaredLogger, infraGetter, ciInfraGetter)
	crudOperationServiceConfig, err := app2.GetCrudOperationServiceConfig()
	if err != nil {
		return nil, err
	}
	materialRepositoryImpl := repository32.NewMaterialRepositoryImpl(db)
	gitMaterialReadServiceImpl := read18.NewGitMaterialReadServiceImpl(sugaredLogger, materialRepositoryImpl)
	appCrudOperationServiceImpl := app2.NewAppCrudOperationServiceImpl(appLabelRepositoryImpl, sugaredLogger, appRepositoryImpl, userRepositoryImpl, installedAppRepositoryImpl, teamRepositoryImpl, genericNoteServiceImpl, installedAppDBServiceImpl, crudOperationServiceConfig, dbMigrationServiceImpl, gitMaterialReadServiceImpl, readServiceImpl)
	appCrudOperationServiceEnterpriseImpl := app3.NewAppCrudOperationServiceEnterpriseImpl(appCrudOperationServiceImpl, sugaredLogger, appRepositoryImpl, globalTagServiceImpl)
	serviceImpl := ucid.NewServiceImpl(sugaredLogger, k8sUtilExtended, acdAuthConfig)
	workflowConfigSnapshotRepositoryImpl := repository33.NewWorkflowConfigSnapshotRepositoryImpl(db, sugaredLogger, transactionUtilImpl)
	workflowTriggerAuditServiceImpl := service3.NewWorkflowTriggerAuditServiceImpl(sugaredLogger, workflowConfigSnapshotRepositoryImpl, ciCdConfig, dockerRegistryConfigImpl, transactionUtilImpl)
	triggerAuditHookImpl := hook.NewTriggerAuditHookImpl(sugaredLogger, workflowTriggerAuditServiceImpl)
	workflowServiceImpl, err := executor.NewWorkflowServiceImpl(sugaredLogger, environmentRepositoryImpl, ciCdConfig, configReadServiceImpl, globalCMCSServiceImpl, argoWorkflowExecutorImpl, systemWorkflowExecutorImpl, k8sCommonServiceImpl, infraProviderImpl, k8sUtilExtended, chartTemplateServiceImpl, configUtilMergeUtil, appCrudOperationServiceEnterpriseImpl, serviceImpl, triggerAuditHookImpl, infraConfigAuditServiceImpl)
	if err != nil {
		return nil, err
	}
	pipelineStageConfig, err := pipeline.GetPipelineStageConfig()
	if err != nil {
		return nil, err
	}
	globalPolicyRepositoryImpl := repository34.NewGlobalPolicyRepositoryImpl(sugaredLogger, db, devtronResourceSearchableKeyServiceImpl)
	globalPolicySearchableFieldRepositoryImpl := repository34.NewGlobalPolicySearchableFieldRepositoryImpl(sugaredLogger, db)
	globalPolicyHistoryRepositoryImpl := repository35.NewGlobalPolicyHistoryRepositoryImpl(sugaredLogger, db)
	globalPolicyHistoryServiceImpl := history.NewGlobalPolicyHistoryServiceImpl(sugaredLogger, globalPolicyHistoryRepositoryImpl)
	globalPolicyDataManagerImpl := globalPolicy.NewGlobalPolicyDataManagerImpl(sugaredLogger, globalPolicyRepositoryImpl, globalPolicySearchableFieldRepositoryImpl, globalPolicyHistoryServiceImpl, userGroupServiceImpl, userServiceImpl)
	pluginPolicyDataAdaptorImpl := service4.NewPluginPolicyDataAdaptorImpl(sugaredLogger, validate, globalPluginServiceImpl, globalPolicyDataManagerImpl)
	criteriaQualifiersMappingRepositoryImpl := resourceQualifiers.NewCriteriaQualifiersMappingRepositoryImpl(sugaredLogger, db, transactionUtilImpl)
	criteriaQualifierMappingServiceImpl := resourceQualifiers.NewCriteriaQualifierMappingServiceImpl(sugaredLogger, criteriaQualifiersMappingRepositoryImpl, qualifierMappingServiceImpl, devtronResourceSearchableKeyServiceImpl, globalPolicyRepositoryImpl)
	commonPolicyActionsServiceImpl := alpha1.NewCommonPolicyActionsServiceImpl(globalPolicyDataManagerImpl, qualifierMappingServiceImpl, appServiceImpl, environmentServiceImpl, clusterServiceImplExtended, globalPluginServiceImpl, criteriaQualifierMappingServiceImpl, userRepositoryImpl, sugaredLogger, transactionUtilImpl, devtronResourceSearchableKeyServiceImpl, teamReadServiceImpl, userServiceImpl)
	policyRegisterServiceImpl := alpha1_2.NewPolicyRegisterServiceImpl(sugaredLogger, pluginPolicyDataAdaptorImpl, commonPolicyActionsServiceImpl)
	pluginDAGUtilImpl := DAG.NewPluginDAGUtilImpl(ciPipelineRepositoryImpl, pipelineRepositoryImpl, pipelineStageRepositoryImpl, globalPluginRepositoryImpl, ciPipelineMaterialRepositoryImpl, sugaredLogger)
	ciEnforcerImpl := enforcer.NewCiEnforcerImpl(pluginPolicyDataAdaptorImpl, devtronResourceSearchableKeyServiceImpl, pluginDAGUtilImpl, sugaredLogger)
	cdEnforcerImpl := enforcer.NewCdEnforcerImpl(sugaredLogger, pipelineRepositoryImpl, devtronResourceSearchableKeyServiceImpl, pluginPolicyDataAdaptorImpl, pluginDAGUtilImpl)
	mandatoryPluginEnforcementServiceImpl := alpha1_2.NewMandatoryPluginEnforcementServiceImpl(sugaredLogger, appServiceImpl, environmentRepositoryImpl, pipelineRepositoryImpl, ciPipelineRepositoryImpl, pluginPolicyDataAdaptorImpl, globalPolicyDataManagerImpl, commonPolicyActionsServiceImpl, policyRegisterServiceImpl, ciEnforcerImpl, cdEnforcerImpl, pluginDAGUtilImpl)
	valueConstraintReadServiceImpl := read19.NewValueConstraintReadService(sugaredLogger, valueConstraintRepositoryImpl)
	helperServiceImpl := runtimeParam.NewHelperServiceImpl(sugaredLogger, attributesServiceImpl, pipelineStageRepositoryImpl, valueConstraintReadServiceImpl)
	pipelineStageServiceImpl := pipeline.NewPipelineStageService(sugaredLogger, pipelineStageRepositoryImpl, globalPluginRepositoryImpl, pipelineRepositoryImpl, scopedVariableManagerImpl, pipelineStageConfig, globalPluginServiceImpl, constraintServiceImpl, mandatoryPluginEnforcementServiceImpl, helperServiceImpl, fileReferenceReaderImpl, valueConstraintReadServiceImpl)
	ciTemplateRepositoryImpl := pipelineConfig.NewCiTemplateRepositoryImpl(db, sugaredLogger)
	ciTemplateReadServiceImpl := pipeline2.NewCiTemplateReadServiceImpl(sugaredLogger, ciTemplateRepositoryImpl, ciTemplateOverrideRepositoryImpl)
	imageTagRepositoryImpl := repository2.NewImageTagRepository(db, sugaredLogger)
	customTagServiceImpl := pipeline.NewCustomTagService(sugaredLogger, imageTagRepositoryImpl)
	clientConfig, err := gitSensor.GetConfig()
	if err != nil {
		return nil, err
	}
	clientImpl, err := gitSensor.NewGitSensorClient(sugaredLogger, clientConfig)
	if err != nil {
		return nil, err
	}
	prePostCdScriptHistoryRepositoryImpl := repository36.NewPrePostCdScriptHistoryRepositoryImpl(sugaredLogger, db)
	configMapHistoryRepositoryImpl := repository36.NewConfigMapHistoryRepositoryImpl(sugaredLogger, db, transactionUtilImpl)
	configMapHistoryServiceImpl := configMapAndSecret.NewConfigMapHistoryServiceImpl(sugaredLogger, configMapHistoryRepositoryImpl, pipelineRepositoryImpl, configMapRepositoryImpl, userServiceImpl, scopedVariableCMCSManagerImpl, mergeUtil)
	prePostCdScriptHistoryServiceImpl := history2.NewPrePostCdScriptHistoryServiceImpl(sugaredLogger, prePostCdScriptHistoryRepositoryImpl, configMapRepositoryImpl, configMapHistoryServiceImpl)
	gitMaterialHistoryRepositoryImpl := repository36.NewGitMaterialHistoryRepositoyImpl(db)
	gitMaterialHistoryServiceImpl := history2.NewGitMaterialHistoryServiceImpl(gitMaterialHistoryRepositoryImpl, sugaredLogger)
	ciPipelineHistoryRepositoryImpl := repository36.NewCiPipelineHistoryRepositoryImpl(db, sugaredLogger)
	ciPipelineHistoryServiceImpl := history2.NewCiPipelineHistoryServiceImpl(ciPipelineHistoryRepositoryImpl, sugaredLogger, ciPipelineRepositoryImpl)
	ciBuildConfigRepositoryImpl := pipelineConfig.NewCiBuildConfigRepositoryImpl(db, sugaredLogger)
	ciBuildConfigServiceImpl := pipeline.NewCiBuildConfigServiceImpl(sugaredLogger, ciBuildConfigRepositoryImpl)
	ciTemplateServiceImpl := pipeline.NewCiTemplateServiceImpl(sugaredLogger, ciBuildConfigServiceImpl, ciTemplateRepositoryImpl, ciTemplateOverrideRepositoryImpl)
	manifestPushConfigRepositoryImpl := repository25.NewManifestPushConfigRepository(sugaredLogger, db)
	pipelineConfigRepositoryImpl := chartConfig.NewPipelineConfigRepository(db)
	configMapServiceImpl := pipeline.NewConfigMapServiceImpl(chartRepositoryImpl, sugaredLogger, chartRepoRepositoryImpl, mergeUtil, pipelineConfigRepositoryImpl, configMapRepositoryImpl, commonServiceImpl, appRepositoryImpl, configMapHistoryServiceImpl, environmentRepositoryImpl, scopedVariableCMCSManagerImpl, configReadServiceImpl)
	pipelineStrategyHistoryRepositoryImpl := repository36.NewPipelineStrategyHistoryRepositoryImpl(sugaredLogger, db)
	pipelineStrategyHistoryServiceImpl := history2.NewPipelineStrategyHistoryServiceImpl(sugaredLogger, pipelineStrategyHistoryRepositoryImpl, userServiceImpl)
	deploymentTemplateHistoryRepositoryImpl := repository36.NewDeploymentTemplateHistoryRepositoryImpl(sugaredLogger, db)
	deploymentTemplateHistoryServiceImpl := deploymentTemplate.NewDeploymentTemplateHistoryServiceImpl(sugaredLogger, deploymentTemplateHistoryRepositoryImpl, pipelineRepositoryImpl, chartRepositoryImpl, userServiceImpl, cdWorkflowRepositoryImpl, scopedVariableManagerImpl, deployedAppMetricsServiceImpl, chartRefServiceImpl, envConfigOverrideReadServiceExtendedImpl)
	repositoryRepositoryImpl := repository37.NewRepositoryImpl(db)
	lockConfigurationDataAdaptorImpl := service5.NewLockConfigurationDataAdaptorImpl(sugaredLogger, validate, qualifierMappingServiceImpl, repositoryRepositoryImpl)
	lockConfigurationServiceImpl := lockConfiguration.NewLockConfigurationServiceImpl(sugaredLogger, repositoryRepositoryImpl, userServiceImpl, commonPolicyActionsServiceImpl, mergeUtil, criteriaQualifierMappingServiceImpl, qualifierMappingServiceImpl, lockConfigurationDataAdaptorImpl, commonPolicyActionsServiceImpl)
	deploymentTemplateValidationServiceEntImpl := validator.NewDeploymentTemplateValidationServiceEntImpl(validate, chartReadServiceImpl, pipelineRepositoryImpl, chartRefReadServiceImpl)
	deploymentTemplateValidationServiceImpl := validator.NewDeploymentTemplateValidationServiceImpl(sugaredLogger, chartRefServiceImpl, scopedVariableManagerImpl, deployedAppMetricsServiceImpl, deploymentTemplateValidationServiceEntImpl)
	approvalCrudServiceImpl := approvalConfig.NewApprovalCrudServiceImpl(sugaredLogger, validate, commonPolicyActionsServiceImpl, globalPolicyDataManagerImpl, userServiceImpl)
	resourceProtectionRepositoryImpl := repository38.NewResourceProtectionRepositoryImpl(sugaredLogger, db)
	approvalConfigMigratorImpl := read20.NewApprovalConfigMigratorImpl(sugaredLogger, resourceProtectionRepositoryImpl, pipelineRepositoryImpl, transactionUtilImpl, commonPolicyActionsServiceImpl, attributesServiceImpl)
	approvalPolicyReadServiceImpl, err := read20.NewApprovalPolicyReadServiceImpl(sugaredLogger, commonPolicyActionsServiceImpl, appRepositoryImpl, environmentRepositoryImpl, devtronResourceSearchableKeyServiceImpl, criteriaQualifierMappingServiceImpl, approvalCrudServiceImpl, approvalConfigMigratorImpl)
	if err != nil {
		return nil, err
	}
	configDraftRepositoryImpl := repository39.NewConfigDraftRepositoryImpl(sugaredLogger, db)
	resourceProtectionServiceImpl := protect.NewResourceProtectionServiceImpl(sugaredLogger, approvalPolicyReadServiceImpl, pipelineRepositoryImpl, configDraftRepositoryImpl, chartRepositoryImpl, appRepositoryImpl, userGroupServiceImpl, commonPolicyActionsServiceImpl)
	chartRefSchemaRepositoryImpl := repository40.NewChartRefSchemaRepositoryImpl(db, transactionUtilImpl)
	chartRefSchemaServiceImpl := chartResourceConfig.NewChartRefSchemaServiceImpl(sugaredLogger, chartRefSchemaRepositoryImpl, qualifierMappingServiceImpl, devtronResourceSearchableKeyServiceImpl, appServiceImpl, environmentServiceImpl, clusterServiceImplExtended, chartRefServiceImpl)
	chartServiceImpl := chart.NewChartServiceImpl(chartRepositoryImpl, sugaredLogger, chartTemplateServiceImpl, chartRepoRepositoryImpl, appRepositoryImpl, mergeUtil, envConfigOverrideRepositoryImpl, pipelineConfigRepositoryImpl, pipelineRepositoryImpl, pipelineStrategyHistoryServiceImpl, environmentRepositoryImpl, deploymentTemplateHistoryServiceImpl, scopedVariableManagerImpl, deployedAppMetricsServiceImpl, chartRefServiceImpl, lockConfigurationServiceImpl, deploymentTemplateValidationServiceImpl, gitOpsConfigReadServiceImpl, deploymentConfigServiceImpl, envConfigOverrideReadServiceExtendedImpl, resourceProtectionServiceImpl, chartReadServiceImpl, chartRefSchemaServiceImpl)
	k8sEventWatcherRepositoryImpl := repository41.NewWatcherRepositoryImpl(db, sugaredLogger)
	featureFlagHelperImpl := helper2.NewFeatureFlagHelperImpl(sugaredLogger, scopedVariableServiceImpl, environmentRepositoryImpl, devtronResourceSearchableKeyServiceImpl, scopedVariableRepositoryImpl)
	cloningModeFeatureFlagListenerImpl := listener.NewCloningModeFeatureFlagListenerImpl(sugaredLogger, scopedVariableRepositoryImpl, appRepositoryImpl, environmentRepositoryImpl, devtronResourceSearchableKeyServiceImpl, clusterRepositoryImpl, k8sEventWatcherRepositoryImpl, featureFlagHelperImpl, clientImpl, scopedVariableServiceImpl)
	defaultFeatureFlagListenerImpl := listener.NewDefaultFeatureFlagListenerImpl(sugaredLogger, scopedVariableRepositoryImpl, appRepositoryImpl, environmentRepositoryImpl, devtronResourceSearchableKeyServiceImpl, clusterRepositoryImpl, qualifierMappingServiceImpl, k8sEventWatcherRepositoryImpl)
	featureFlagServiceImpl := service6.NewFeatureFlagServiceImpl(sugaredLogger, scopedVariableRepositoryImpl, appRepositoryImpl, environmentRepositoryImpl, devtronResourceSearchableKeyServiceImpl, clusterRepositoryImpl, cloningModeFeatureFlagListenerImpl, featureFlagHelperImpl, defaultFeatureFlagListenerImpl, scopedVariableServiceImpl)
	dtResRelationReadServiceImpl := read2.NewDtResRelationReadServiceImpl(sugaredLogger, dtResObjDepRelationsRepositoryImpl, devtronResourceObjectRepositoryImpl, readServiceImpl)
	ciCdPipelineOrchestratorImpl := pipeline.NewCiCdPipelineOrchestrator(appRepositoryImpl, sugaredLogger, materialRepositoryImpl, pipelineRepositoryImpl, ciPipelineRepositoryImpl, ciPipelineMaterialRepositoryImpl, cdWorkflowRepositoryImpl, clientImpl, ciCdConfig, appWorkflowRepositoryImpl, environmentRepositoryImpl, attributesServiceImpl, appCrudOperationServiceEnterpriseImpl, userAuthServiceImpl, prePostCdScriptHistoryServiceImpl, pipelineStageServiceImpl, gitMaterialHistoryServiceImpl, ciPipelineHistoryServiceImpl, ciTemplateReadServiceImpl, ciTemplateServiceImpl, dockerArtifactStoreRepositoryImpl, pipelineOverrideRepositoryImpl, ciArtifactRepositoryImpl, manifestPushConfigRepositoryImpl, configMapServiceImpl, customTagServiceImpl, genericNoteServiceImpl, internalProcessingServiceImpl, chartServiceImpl, featureFlagServiceImpl, dtResRelationReadServiceImpl, transactionUtilImpl, gitOpsConfigReadServiceImpl, deploymentConfigServiceImpl, deploymentConfigReadServiceImpl, approvalPolicyReadServiceImpl, globalPluginServiceImpl, userGroupServiceImpl, chartReadServiceImpl)
	ciCdPipelineOrchestratorEnterpriseImpl := pipeline3.NewCiCdPipelineOrchestratorEnterpriseImpl(ciCdPipelineOrchestratorImpl, globalTagServiceImpl)
	pluginInputVariableParserImpl := pipeline.NewPluginInputVariableParserImpl(sugaredLogger, dockerRegistryConfigImpl, customTagServiceImpl)
	ciServiceImpl, err := pipeline.NewCiServiceImpl(sugaredLogger, workFlowStageStatusServiceImpl, eventRESTClientImpl, eventSimpleFactoryImpl, ciWorkflowRepositoryImpl, transactionUtilImpl)
	if err != nil {
		return nil, err
	}
	ciLogServiceImpl, err := pipeline.NewCiLogServiceImpl(sugaredLogger, ciServiceImpl, k8sUtilExtended)
	if err != nil {
		return nil, err
	}
	blobStorageConfigServiceImpl := pipeline.NewBlobStorageConfigServiceImpl(sugaredLogger, k8sUtilExtended, ciCdConfig)
	evaluatorServiceImpl := cel.NewCELServiceImpl(sugaredLogger)
	ephemeralContainersRepositoryImpl := repository10.NewEphemeralContainersRepositoryImpl(db, transactionUtilImpl)
	ephemeralContainerServiceImpl := cluster2.NewEphemeralContainerServiceImpl(ephemeralContainersRepositoryImpl, sugaredLogger)
	terminalSessionHandlerImpl := terminal.NewTerminalSessionHandlerImpl(environmentServiceImpl, sugaredLogger, k8sUtilExtended, ephemeralContainerServiceImpl, argoApplicationConfigServiceImpl, clusterReadServiceImpl)
	deploymentWindowServiceImpl, err := deploymentWindow.NewDeploymentWindowServiceImpl(sugaredLogger, qualifierMappingServiceImpl, timeWindowServiceImpl, globalPolicyDataManagerImpl, userServiceImpl, transactionUtilImpl, pipelineRepositoryImpl)
	if err != nil {
		return nil, err
	}
	imageScanHistoryRepositoryImpl := repository42.NewImageScanHistoryRepositoryImpl(db, sugaredLogger)
	imageScanResultRepositoryImpl := repository42.NewImageScanResultRepositoryImpl(db, sugaredLogger)
	imageScanObjectMetaRepositoryImpl := repository42.NewImageScanObjectMetaRepositoryImpl(db, sugaredLogger)
	cveStoreRepositoryImpl := repository42.NewCveStoreRepositoryImpl(db, sugaredLogger)
	imageScanDeployInfoRepositoryImpl := repository42.NewImageScanDeployInfoRepositoryImpl(db, sugaredLogger)
	cvePolicyRepositoryImpl := repository42.NewPolicyRepositoryImpl(db, sugaredLogger)
	imageScanHistoryReadServiceImpl := read21.NewImageScanHistoryReadService(sugaredLogger, imageScanHistoryRepositoryImpl)
	policyServiceImpl := imageScanning.NewPolicyServiceImpl(environmentServiceImpl, sugaredLogger, appRepositoryImpl, pipelineOverrideRepositoryImpl, cvePolicyRepositoryImpl, clusterServiceImplExtended, pipelineRepositoryImpl, imageScanResultRepositoryImpl, imageScanDeployInfoRepositoryImpl, imageScanObjectMetaRepositoryImpl, httpClient, ciArtifactRepositoryImpl, ciCdConfig, cveStoreRepositoryImpl, ciTemplateRepositoryImpl, pubSubClientServiceImpl, imageScanHistoryReadServiceImpl, clusterReadServiceImpl, transactionUtilImpl)
	scanToolExecutionHistoryMappingRepositoryImpl := repository42.NewScanToolExecutionHistoryMappingRepositoryImpl(db, sugaredLogger)
	cdWorkflowReadServiceImpl := read22.NewCdWorkflowReadServiceImpl(sugaredLogger, cdWorkflowRepositoryImpl)
	imageScanDeployInfoReadServiceImpl := read21.NewImageScanDeployInfoReadService(sugaredLogger, imageScanDeployInfoRepositoryImpl)
	resourceScanResultRepositoryImpl := repository42.NewResourceScanResultRepositoryImpl(db, sugaredLogger)
	imageScanServiceImpl, err := imageScanning.NewImageScanServiceImpl(sugaredLogger, imageScanHistoryRepositoryImpl, imageScanResultRepositoryImpl, imageScanObjectMetaRepositoryImpl, cveStoreRepositoryImpl, imageScanDeployInfoRepositoryImpl, userServiceImpl, appRepositoryImpl, environmentServiceImpl, ciArtifactRepositoryImpl, policyServiceImpl, pipelineRepositoryImpl, ciPipelineRepositoryImpl, scanToolMetadataRepositoryImpl, scanToolExecutionHistoryMappingRepositoryImpl, cvePolicyRepositoryImpl, imageScanHistoryReadServiceImpl, cdWorkflowReadServiceImpl, installedAppVersionHistoryRepositoryImpl, imageScanDeployInfoReadServiceImpl, resourceScanResultRepositoryImpl)
	if err != nil {
		return nil, err
	}
	resourceTreeServiceV2Impl := appDetails.NewResourceTreeServiceV2Impl(sugaredLogger, scoopClientGetterImpl, helmAppClientImpl, clusterReadServiceImpl)
	fluxApplicationServiceImpl := fluxApplication.NewFluxApplicationServiceImpl(sugaredLogger, helmAppReadServiceImpl, clusterServiceImplExtended, helmAppClientImpl, pumpImpl, resourceTreeServiceV2Impl, acdAuthConfig, scoopClientGetterImpl, pipelineRepositoryImpl, installedAppRepositoryImpl)
	workloadListConfig, err := resources.NewWorkloadListConfig()
	if err != nil {
		return nil, err
	}
	resourcesServiceImpl := resources.NewServiceImpl(sugaredLogger, k8sUtilExtended, workloadListConfig)
	triggerKrrJobImpl, err := job2.NewTriggerKrrJobImpl(sugaredLogger, k8sUtilExtended, clusterReadServiceImpl, sqlConfig, cronLoggerImpl, acdAuthConfig, environmentVariables)
	if err != nil {
		return nil, err
	}
	krrServiceImpl := krr.NewServiceImpl(sugaredLogger, triggerKrrJobImpl)
	krrScanRequestRepositoryImpl := repository43.NewKrrScanRequestRepositoryImpl(db)
	krrScanHistoryRepositoryImpl := repository43.NewKrrScanHistoryRepositoryImpl(db)
	krrScanServiceImpl, err := read23.NewKRRScanReadServiceImpl(sugaredLogger, userServiceImpl, krrScanRequestRepositoryImpl, krrScanHistoryRepositoryImpl)
	if err != nil {
		return nil, err
	}
	argoApplicationReadServiceImpl := read24.NewArgoApplicationReadServiceImpl(sugaredLogger, clusterRepositoryImpl, k8sUtilExtended, helmAppClientImpl, helmAppServiceImpl)
	k8sApplicationServiceImpl, err := application2.NewK8sApplicationServiceImpl(sugaredLogger, clusterServiceImplExtended, pumpImpl, helmAppServiceImpl, k8sUtilExtended, acdAuthConfig, k8sResourceHistoryServiceImpl, k8sCommonServiceImpl, terminalSessionHandlerImpl, ephemeralContainerServiceImpl, ephemeralContainersRepositoryImpl, environmentRepositoryImpl, clusterRepositoryImpl, evaluatorServiceImpl, interClusterServiceCommunicationHandlerImpl, deploymentWindowServiceImpl, scoopClientGetterImpl, imageScanServiceImpl, fluxApplicationServiceImpl, enterpriseEnforcerImpl, enforcerUtilImpl, enforcerUtilHelmImpl, resourcesServiceImpl, krrServiceImpl, krrScanServiceImpl, triggerKrrJobImpl, argoApplicationReadServiceImpl, clusterReadServiceImpl)
	if err != nil {
		return nil, err
	}
	ciCacheResourceSelectorImpl := cacheResourceSelector.NewCiCacheResourceSelectorImpl(sugaredLogger, evaluatorServiceImpl, k8sApplicationServiceImpl, k8sCommonServiceImpl)
	handlerServiceImpl, err := trigger.NewHandlerServiceImpl(sugaredLogger, workflowServiceImpl, ciPipelineMaterialRepositoryImpl, ciPipelineRepositoryImpl, ciArtifactRepositoryImpl, pipelineStageServiceImpl, userServiceImpl, ciTemplateReadServiceImpl, appCrudOperationServiceEnterpriseImpl, environmentRepositoryImpl, appRepositoryImpl, scopedVariableManagerImpl, customTagServiceImpl, ciCdPipelineOrchestratorEnterpriseImpl, attributesServiceImpl, pluginInputVariableParserImpl, globalPluginServiceImpl, ciServiceImpl, ciWorkflowRepositoryImpl, clientImpl, ciLogServiceImpl, blobStorageConfigServiceImpl, clusterServiceImplExtended, environmentServiceImpl, k8sUtilExtended, runnable, workflowTriggerAuditServiceImpl, workFlowStageStatusServiceImpl, mandatoryPluginEnforcementServiceImpl, infraProviderImpl, infraConfigServiceImpl, remoteConnectionServiceImpl, dockerRegistryConfigImpl, ciCacheResourceSelectorImpl, featureFlagServiceImpl, scanToolMetadataServiceImpl, transactionUtilImpl, helperServiceImpl)
	if err != nil {
		return nil, err
	}
	gitWebhookServiceImpl := gitWebhook.NewGitWebhookServiceImpl(sugaredLogger, gitWebhookRepositoryImpl, handlerServiceImpl)
	gitWebhookRestHandlerImpl := restHandler.NewGitWebhookRestHandlerImpl(sugaredLogger, gitWebhookServiceImpl)
	ecrConfig, err := pipeline.GetEcrConfig()
	if err != nil {
		return nil, err
	}
	ciTemplateHistoryRepositoryImpl := repository36.NewCiTemplateHistoryRepositoryImpl(db, sugaredLogger)
	ciTemplateHistoryServiceImpl := history2.NewCiTemplateHistoryServiceImpl(ciTemplateHistoryRepositoryImpl, sugaredLogger)
	resourceGroupRepositoryImpl := resourceGroup.NewResourceGroupRepositoryImpl(db)
	resourceGroupMappingRepositoryImpl := resourceGroup.NewResourceGroupMappingRepositoryImpl(db)
	resourceGroupServiceImpl := resourceGroup2.NewResourceGroupServiceImpl(sugaredLogger, resourceGroupRepositoryImpl, resourceGroupMappingRepositoryImpl, enforcerUtilImpl, devtronResourceSearchableKeyServiceImpl, appStatusRepositoryImpl)
	buildPipelineSwitchServiceImpl := pipeline.NewBuildPipelineSwitchServiceImpl(sugaredLogger, ciPipelineConfigReadServiceImpl, ciPipelineRepositoryImpl, ciCdPipelineOrchestratorEnterpriseImpl, pipelineRepositoryImpl, ciWorkflowRepositoryImpl, appWorkflowRepositoryImpl, ciPipelineHistoryServiceImpl, ciTemplateOverrideRepositoryImpl, ciPipelineMaterialRepositoryImpl)
	ciPipelineConfigServiceImpl := pipeline.NewCiPipelineConfigServiceImpl(sugaredLogger, ciCdPipelineOrchestratorEnterpriseImpl, dockerArtifactStoreRepositoryImpl, gitMaterialReadServiceImpl, appRepositoryImpl, pipelineRepositoryImpl, ciPipelineConfigReadServiceImpl, ciPipelineRepositoryImpl, ecrConfig, appWorkflowRepositoryImpl, ciCdConfig, attributesServiceImpl, pipelineStageServiceImpl, ciPipelineMaterialRepositoryImpl, ciTemplateServiceImpl, ciTemplateReadServiceImpl, ciTemplateOverrideRepositoryImpl, ciTemplateHistoryServiceImpl, enforcerUtilImpl, ciWorkflowRepositoryImpl, resourceGroupServiceImpl, customTagServiceImpl, cdWorkflowRepositoryImpl, buildPipelineSwitchServiceImpl, helperServiceImpl, appServiceImpl, mandatoryPluginEnforcementServiceImpl, environmentServiceImpl, appListingServiceImpl)
	ciMaterialConfigServiceImpl := pipeline.NewCiMaterialConfigServiceImpl(sugaredLogger, materialRepositoryImpl, ciTemplateReadServiceImpl, ciCdPipelineOrchestratorEnterpriseImpl, ciPipelineRepositoryImpl, gitMaterialHistoryServiceImpl, pipelineRepositoryImpl, ciPipelineMaterialRepositoryImpl, transactionUtilImpl, gitMaterialReadServiceImpl)
	imageTaggingRepositoryImpl := repository44.NewImageTaggingRepositoryImpl(db, transactionUtilImpl)
	imageTaggingReadServiceImpl, err := read25.NewImageTaggingReadServiceImpl(imageTaggingRepositoryImpl, sugaredLogger)
	if err != nil {
		return nil, err
	}
	imageTaggingServiceImpl := imageTagging.NewImageTaggingServiceImpl(imageTaggingRepositoryImpl, imageTaggingReadServiceImpl, ciPipelineRepositoryImpl, pipelineRepositoryImpl, environmentRepositoryImpl, sugaredLogger)
	resourceFilterRepositoryImpl := resourceFilter.NewResourceFilterRepositoryImpl(sugaredLogger, db, filterAuditRepositoryImpl)
	resourceFilterEvaluatorImpl, err := resourceFilter.NewResourceFilterEvaluatorImpl(sugaredLogger, evaluatorServiceImpl)
	if err != nil {
		return nil, err
	}
	resourceFilterServiceImpl := resourceFilter.NewResourceFilterServiceImpl(sugaredLogger, qualifierMappingServiceImpl, resourceFilterRepositoryImpl, resourceFilterEvaluatorImpl, appRepositoryImpl, teamRepositoryImpl, clusterRepositoryImpl, environmentRepositoryImpl, devtronResourceSearchableKeyServiceImpl, filterEvaluationAuditServiceImpl, filterAuditRepositoryImpl)
	propertiesConfigServiceImpl := pipeline.NewPropertiesConfigServiceImpl(sugaredLogger, envConfigOverrideRepositoryImpl, chartRepositoryImpl, mergeUtil, environmentRepositoryImpl, deploymentTemplateHistoryServiceImpl, scopedVariableManagerImpl, lockConfigurationServiceImpl, deployedAppMetricsServiceImpl, deploymentTemplateValidationServiceImpl, envConfigOverrideReadServiceExtendedImpl, deploymentConfigServiceImpl, chartServiceImpl)
	installedAppDBExtendedServiceImpl := FullMode.NewInstalledAppDBExtendedServiceImpl(installedAppDBServiceImpl, appStatusServiceImpl, gitOpsConfigReadServiceImpl)
	gitOpsValidationServiceImpl := validation.NewGitOpsValidationServiceImpl(sugaredLogger, gitFactory, gitOperationServiceImpl, gitOpsConfigReadServiceImpl, chartTemplateServiceImpl, chartServiceImpl, installedAppDBExtendedServiceImpl)
	pipelineConfigEventPublishServiceImpl := out.NewPipelineConfigEventPublishServiceImpl(sugaredLogger, pubSubClientServiceImpl)
	deploymentTypeOverrideServiceImpl := providerConfig.NewDeploymentTypeOverrideServiceImpl(sugaredLogger, environmentVariables, attributesServiceImpl, environmentServiceImpl)
	deploymentServiceImpl := fluxcd.NewDeploymentService(sugaredLogger, k8sUtilExtended, gitOpsConfigReadServiceImpl)
	cdPipelineConfigServiceImpl := pipeline.NewCdPipelineConfigServiceImpl(sugaredLogger, pipelineRepositoryImpl, environmentRepositoryImpl, pipelineConfigRepositoryImpl, appWorkflowRepositoryImpl, pipelineStageServiceImpl, appRepositoryImpl, appServiceImpl, ciCdPipelineOrchestratorEnterpriseImpl, appStatusRepositoryImpl, ciPipelineRepositoryImpl, prePostCdScriptHistoryServiceImpl, clusterRepositoryImpl, helmAppServiceImpl, enforcerUtilImpl, pipelineStrategyHistoryServiceImpl, chartRepositoryImpl, resourceGroupServiceImpl, chartTemplateServiceImpl, propertiesConfigServiceImpl, deploymentTemplateHistoryServiceImpl, scopedVariableManagerImpl, environmentVariables, manifestPushConfigRepositoryImpl, customTagServiceImpl, ciPipelineConfigServiceImpl, buildPipelineSwitchServiceImpl, internalProcessingServiceImpl, argoClientWrapperServiceImpl, deployedAppMetricsServiceImpl, gitOpsConfigReadServiceImpl, gitOpsValidationServiceImpl, gitOperationServiceImpl, chartServiceImpl, imageDigestPolicyServiceImpl, pipelineConfigEventPublishServiceImpl, deploymentWindowServiceImpl, deploymentTypeOverrideServiceImpl, mandatoryPluginEnforcementServiceImpl, deploymentConfigServiceImpl, envConfigOverrideReadServiceExtendedImpl, chartRefReadServiceImpl, approvalPolicyReadServiceImpl, appLabelRepositoryImpl, globalTagServiceImpl, clusterReadServiceImpl, installedAppReadServiceImpl, chartReadServiceImpl, k8sUtilExtended, deploymentServiceImpl)
	deploymentApprovalRepositoryImpl := pipelineConfig.NewDeploymentApprovalRepositoryImpl(db, sugaredLogger, requestApprovalUserDataRepositoryImpl, userGroupServiceImpl)
	artifactApprovalDataReadServiceImpl := read26.NewArtifactApprovalDataReadServiceImpl(sugaredLogger, userServiceImpl, userGroupServiceImpl, pipelineRepositoryImpl, approvalPolicyReadServiceImpl, deploymentApprovalRepositoryImpl)
	requestRepositoryImpl := repository45.NewRequestRepositoryImpl(db)
	appWorkflowDataReadServiceImpl := read27.NewAppWorkflowDataReadServiceImpl(appWorkflowRepositoryImpl, sugaredLogger)
	artifactPromotionDataReadServiceImpl := read28.NewArtifactPromotionDataReadServiceImpl(requestRepositoryImpl, sugaredLogger, requestApprovalUserDataRepositoryImpl, userServiceImpl, pipelineRepositoryImpl, qualifierMappingServiceImpl, globalPolicyDataManagerImpl, appWorkflowDataReadServiceImpl, filterEvaluationAuditServiceImpl)
	appArtifactManagerImpl := pipeline.NewAppArtifactManagerImpl(sugaredLogger, cdWorkflowRepositoryImpl, userServiceImpl, imageTaggingServiceImpl, imageTaggingReadServiceImpl, ciArtifactRepositoryImpl, ciWorkflowRepositoryImpl, resourceFilterServiceImpl, filterEvaluationAuditServiceImpl, pipelineStageServiceImpl, cdPipelineConfigServiceImpl, dockerArtifactStoreRepositoryImpl, ciPipelineRepositoryImpl, ciTemplateReadServiceImpl, artifactApprovalDataReadServiceImpl, environmentRepositoryImpl, appWorkflowRepositoryImpl, artifactPromotionDataReadServiceImpl, appWorkflowDataReadServiceImpl, ciPipelineConfigServiceImpl, enforcerUtilImpl, ciCdPipelineOrchestratorEnterpriseImpl, teamReadServiceImpl, approvalPolicyReadServiceImpl, userGroupServiceImpl, commonPolicyActionsServiceImpl, globalPolicyDataManagerImpl)
	devtronAppCMCSServiceImpl := pipeline.NewDevtronAppCMCSServiceImpl(sugaredLogger, appServiceImpl, attributesRepositoryImpl)
	devtronAppStrategyServiceImpl := pipeline.NewDevtronAppStrategyServiceImpl(sugaredLogger, chartRepositoryImpl, globalStrategyMetadataChartRefMappingRepositoryImpl, ciCdPipelineOrchestratorEnterpriseImpl, cdPipelineConfigServiceImpl, chartRefServiceImpl)
	cdWorkflowCommonServiceImpl, err := cd.NewCdWorkflowCommonServiceImpl(sugaredLogger, cdWorkflowRepositoryImpl, pipelineStatusTimelineServiceImpl, pipelineRepositoryImpl, pipelineStatusTimelineRepositoryImpl, deploymentConfigServiceImpl, cdWorkflowRunnerServiceImpl)
	if err != nil {
		return nil, err
	}
	triggerEventEvaluatorImpl, err := celEvaluator.NewTriggerEventEvaluatorImpl(sugaredLogger, imageTaggingReadServiceImpl, teamServiceImpl, attributesServiceImpl, evaluatorServiceImpl, teamReadServiceImpl)
	if err != nil {
		return nil, err
	}
	deploymentGroupRepositoryImpl := repository2.NewDeploymentGroupRepositoryImpl(sugaredLogger, db)
	workflowEventPublishServiceImpl, err := out.NewWorkflowEventPublishServiceImpl(sugaredLogger, pubSubClientServiceImpl, cdWorkflowCommonServiceImpl, pipelineStatusTimelineServiceImpl, triggerEventEvaluatorImpl, cdWorkflowRepositoryImpl, pipelineRepositoryImpl, deploymentGroupRepositoryImpl)
	if err != nil {
		return nil, err
	}
	appDeploymentTypeChangeManagerImpl := pipeline.NewAppDeploymentTypeChangeManagerImpl(sugaredLogger, pipelineRepositoryImpl, appServiceImpl, appStatusRepositoryImpl, helmAppServiceImpl, appArtifactManagerImpl, cdPipelineConfigServiceImpl, gitOpsConfigReadServiceImpl, chartServiceImpl, workflowEventPublishServiceImpl, deploymentConfigServiceImpl, argoClientWrapperServiceImpl, chartReadServiceImpl, deploymentConfigReadServiceImpl)
	devtronAppConfigServiceImpl := pipeline.NewDevtronAppConfigServiceImpl(sugaredLogger, ciCdPipelineOrchestratorEnterpriseImpl, appRepositoryImpl, pipelineRepositoryImpl, resourceGroupServiceImpl, enforcerUtilImpl, ciMaterialConfigServiceImpl, userRepositoryImpl)
	pipelineBuilderImpl := pipeline.NewPipelineBuilderImpl(sugaredLogger, gitMaterialReadServiceImpl, chartRepositoryImpl, ciPipelineConfigServiceImpl, ciMaterialConfigServiceImpl, appArtifactManagerImpl, devtronAppCMCSServiceImpl, devtronAppStrategyServiceImpl, appDeploymentTypeChangeManagerImpl, cdPipelineConfigServiceImpl, devtronAppConfigServiceImpl)
	devtronAppGitOpConfigServiceImpl := gitOpsConfig.NewDevtronAppGitOpConfigServiceImpl(sugaredLogger, chartRepositoryImpl, chartServiceImpl, gitOpsConfigReadServiceImpl, gitOpsValidationServiceImpl, argoClientWrapperServiceImpl, deploymentConfigServiceImpl, chartReadServiceImpl)
	ciHandlerImpl := pipeline.NewCiHandlerImpl(sugaredLogger, ciServiceImpl, ciPipelineMaterialRepositoryImpl, clientImpl, ciWorkflowRepositoryImpl, ciArtifactRepositoryImpl, userServiceImpl, eventRESTClientImpl, eventSimpleFactoryImpl, ciPipelineRepositoryImpl, appListingRepositoryImpl, pipelineRepositoryImpl, enforcerUtilImpl, resourceGroupServiceImpl, environmentRepositoryImpl, imageTaggingServiceImpl, k8sCommonServiceImpl, appWorkflowRepositoryImpl, customTagServiceImpl, workFlowStageStatusServiceImpl, ciPipelineConfigServiceImpl, ciCacheResourceSelectorImpl, helperServiceImpl)
	rbacFilterUtilImpl := filter.NewRbacFilterUtilImpl(enforcerUtilImpl, enterpriseEnforcerImpl)
	cdHandlerImpl := pipeline.NewCdHandlerImpl(sugaredLogger, userServiceImpl, cdWorkflowRepositoryImpl, ciArtifactRepositoryImpl, ciPipelineMaterialRepositoryImpl, pipelineRepositoryImpl, environmentRepositoryImpl, ciWorkflowRepositoryImpl, enforcerUtilImpl, resourceGroupServiceImpl, imageTaggingServiceImpl, k8sUtilExtended, customTagServiceImpl, deploymentApprovalRepositoryImpl, resourceFilterServiceImpl, filterEvaluationAuditServiceImpl, requestApprovalUserDataRepositoryImpl, artifactPromotionDataReadServiceImpl, deploymentConfigServiceImpl, helperServiceImpl, workFlowStageStatusServiceImpl, cdWorkflowRunnerServiceImpl)
	appWorkflowServiceImpl := appWorkflow2.NewAppWorkflowServiceImpl(sugaredLogger, appWorkflowRepositoryImpl, ciCdPipelineOrchestratorEnterpriseImpl, ciPipelineRepositoryImpl, pipelineRepositoryImpl, enforcerUtilImpl, resourceGroupServiceImpl, appRepositoryImpl, userAuthServiceImpl, chartServiceImpl, appArtifactManagerImpl, artifactPromotionDataReadServiceImpl, appWorkflowDataReadServiceImpl, pipelineBuilderImpl, deploymentConfigServiceImpl)
	appCloneServiceImpl := appClone.NewAppCloneServiceImpl(sugaredLogger, pipelineBuilderImpl, attributesServiceImpl, chartServiceImpl, configMapServiceImpl, appWorkflowServiceImpl, appListingServiceImpl, propertiesConfigServiceImpl, pipelineStageServiceImpl, ciTemplateReadServiceImpl, appRepositoryImpl, ciPipelineRepositoryImpl, pipelineRepositoryImpl, ciPipelineConfigServiceImpl, gitOpsConfigReadServiceImpl, environmentServiceImpl, chartReadServiceImpl)
	deploymentTemplateRepositoryImpl := repository2.NewDeploymentTemplateRepositoryImpl(db, sugaredLogger)
	deploymentTemplateHistoryReadServiceImpl := read9.NewDeploymentTemplateHistoryReadServiceImpl(sugaredLogger, deploymentTemplateHistoryRepositoryImpl, scopedVariableManagerImpl)
	deploymentTemplateServiceImpl, err := generateManifest.NewDeploymentTemplateServiceImpl(sugaredLogger, chartServiceImpl, chartReadServiceImpl, appListingServiceImpl, deploymentTemplateRepositoryImpl, helmAppServiceImpl, helmAppReadServiceImpl, chartTemplateServiceImpl, helmAppClientImpl, k8sUtilExtended, propertiesConfigServiceImpl, environmentRepositoryImpl, appRepositoryImpl, scopedVariableManagerImpl, chartRefServiceImpl, pipelineOverrideRepositoryImpl, chartRepositoryImpl, cdWorkflowRepositoryImpl, pipelineRepositoryImpl, configUtilMergeUtil, deploymentTemplateHistoryReadServiceImpl, configMapRepositoryImpl, scopedVariableCMCSManagerImpl, envConfigOverrideReadServiceExtendedImpl, approvalPolicyReadServiceImpl, userGroupServiceImpl, deploymentConfigReadServiceImpl, globalPolicyDataManagerImpl)
	if err != nil {
		return nil, err
	}
	imageScanResultReadServiceImpl := read21.NewImageScanResultReadServiceImpl(sugaredLogger, imageScanResultRepositoryImpl)
	gitOpsManifestPushServiceImpl := publish.NewGitOpsManifestPushServiceImpl(sugaredLogger, pipelineStatusTimelineServiceImpl, pipelineOverrideRepositoryImpl, acdConfig, chartRefServiceImpl, gitOpsConfigReadServiceImpl, chartServiceImpl, gitOperationServiceImpl, argoClientWrapperServiceImpl, transactionUtilImpl, deploymentConfigServiceImpl, chartTemplateServiceImpl)
	deploymentTemplateDeploymentTemplateServiceImpl := deploymentTemplate.NewDeploymentTemplateServiceImpl(sugaredLogger, chartRefServiceImpl, chartTemplateServiceImpl, chartRepositoryImpl, deploymentConfigServiceImpl)
	manifestCreationServiceImpl := manifest.NewManifestCreationServiceImpl(sugaredLogger, dockerRegistryIpsConfigServiceImpl, chartRefServiceImpl, scopedVariableCMCSManagerImpl, k8sCommonServiceImpl, deployedAppMetricsServiceImpl, imageDigestPolicyServiceImpl, configUtilMergeUtil, appCrudOperationServiceEnterpriseImpl, deploymentTemplateDeploymentTemplateServiceImpl, argoClientWrapperServiceImpl, configMapHistoryRepositoryImpl, configMapRepositoryImpl, chartRepositoryImpl, envConfigOverrideRepositoryImpl, environmentRepositoryImpl, pipelineRepositoryImpl, ciArtifactRepositoryImpl, pipelineOverrideRepositoryImpl, pipelineStrategyHistoryRepositoryImpl, pipelineConfigRepositoryImpl, deploymentTemplateHistoryRepositoryImpl, deploymentConfigServiceImpl, envConfigOverrideReadServiceExtendedImpl, environmentVariables, chartRefRepositoryImpl)
	configMapHistoryReadServiceImpl := read29.NewConfigMapHistoryReadService(sugaredLogger, configMapHistoryRepositoryImpl, scopedVariableCMCSManagerImpl)
	deployedConfigurationHistoryServiceImpl := history2.NewDeployedConfigurationHistoryServiceImpl(sugaredLogger, userServiceImpl, deploymentTemplateHistoryServiceImpl, pipelineStrategyHistoryServiceImpl, configMapHistoryServiceImpl, cdWorkflowRepositoryImpl, scopedVariableCMCSManagerImpl, deploymentTemplateHistoryReadServiceImpl, configMapHistoryReadServiceImpl)
	userDeploymentRequestRepositoryImpl := repository46.NewUserDeploymentRequestRepositoryImpl(db, transactionUtilImpl)
	userDeploymentRequestServiceImpl := service7.NewUserDeploymentRequestServiceImpl(sugaredLogger, userDeploymentRequestRepositoryImpl)
	imageScanDeployInfoServiceImpl := imageScanning.NewImageScanDeployInfoService(sugaredLogger, imageScanDeployInfoRepositoryImpl)
	helmRepoPushServiceImpl := publish.NewHelmRepoPushServiceImpl(sugaredLogger, helmAppClientImpl, pipelineStatusTimelineServiceImpl)
	chartScanPublishServiceImpl := out.NewChartScanPublishServiceImpl(sugaredLogger, pubSubClientServiceImpl)
	orasPushServiceImpl := publish.NewOrasPushServiceImpl(sugaredLogger)
	devtronResourceTaskRunRepositoryImpl := repository9.NewDevtronResourceTaskRunRepositoryImpl(db, sugaredLogger)
	taskRunTriggerOperationServiceImpl := taskRun.NewTaskRunTriggerOperationsServiceImpl(sugaredLogger, devtronResourceTaskRunRepositoryImpl, dtResRelationReadServiceImpl, readServiceImpl)
	devtronAppsHandlerServiceImpl, err := devtronApps.NewHandlerServiceImpl(sugaredLogger, cdWorkflowCommonServiceImpl, gitOpsManifestPushServiceImpl, gitOpsConfigReadServiceImpl, argoK8sClientImpl, acdConfig, argoClientWrapperServiceImpl, pipelineStatusTimelineServiceImpl, chartTemplateServiceImpl, workflowEventPublishServiceImpl, manifestCreationServiceImpl, deployedConfigurationHistoryServiceImpl, pipelineStageServiceImpl, globalPluginServiceImpl, customTagServiceImpl, pluginInputVariableParserImpl, prePostCdScriptHistoryServiceImpl, scopedVariableCMCSManagerImpl, imageDigestPolicyServiceImpl, userServiceImpl, helmAppServiceImpl, enforcerUtilImpl, userDeploymentRequestServiceImpl, helmAppClientImpl, eventSimpleFactoryImpl, eventRESTClientImpl, environmentVariables, appRepositoryImpl, ciPipelineMaterialRepositoryImpl, imageScanHistoryReadServiceImpl, imageScanDeployInfoReadServiceImpl, imageScanDeployInfoServiceImpl, pipelineRepositoryImpl, pipelineOverrideRepositoryImpl, manifestPushConfigRepositoryImpl, chartRepositoryImpl, environmentRepositoryImpl, cdWorkflowRepositoryImpl, ciWorkflowRepositoryImpl, ciArtifactRepositoryImpl, ciTemplateReadServiceImpl, gitMaterialReadServiceImpl, appLabelRepositoryImpl, ciPipelineRepositoryImpl, appWorkflowRepositoryImpl, dockerArtifactStoreRepositoryImpl, imageScanServiceImpl, k8sUtilExtended, transactionUtilImpl, deploymentConfigServiceImpl, ciCdPipelineOrchestratorEnterpriseImpl, gitOperationServiceImpl, attributesServiceImpl, clusterRepositoryImpl, cdWorkflowRunnerServiceImpl, clusterServiceImplExtended, ciLogServiceImpl, workflowServiceImpl, blobStorageConfigServiceImpl, deploymentEventHandlerImpl, runnable, workflowTriggerAuditServiceImpl, deploymentServiceImpl, helperServiceImpl, artifactApprovalDataReadServiceImpl, mergeUtil, imageTaggingReadServiceImpl, cdPipelineConfigServiceImpl, deploymentApprovalRepositoryImpl, helmRepoPushServiceImpl, resourceFilterServiceImpl, filterEvaluationAuditServiceImpl, deploymentWindowServiceImpl, artifactPromotionDataReadServiceImpl, featureFlagServiceImpl, chartScanPublishServiceImpl, orasPushServiceImpl, mandatoryPluginEnforcementServiceImpl, globalTagServiceImpl, approvalPolicyReadServiceImpl, appListingServiceImpl, globalPolicyDataManagerImpl, taskRunTriggerOperationServiceImpl)
	if err != nil {
		return nil, err
	}
	artifactApprovalActionServiceImpl := action.NewArtifactApprovalActionServiceImpl(sugaredLogger, userGroupServiceImpl, artifactApprovalDataReadServiceImpl, devtronAppsHandlerServiceImpl, eventRESTClientImpl, eventSimpleFactoryImpl, imageTaggingServiceImpl, deploymentApprovalRepositoryImpl, requestApprovalUserDataRepositoryImpl, ciArtifactRepositoryImpl, approvalPolicyReadServiceImpl, pipelineRepositoryImpl)
	deploymentHistoryServiceImpl := cdPipeline.NewDeploymentHistoryServiceImpl(sugaredLogger, cdHandlerImpl, imageTaggingReadServiceImpl, imageTaggingServiceImpl, pipelineRepositoryImpl, devtronResourceTaskRunRepositoryImpl, devtronResourceSchemaRepositoryImpl, readServiceImpl, deployedConfigurationHistoryServiceImpl)
	fileUploadInternalConfig, err := bean3.GetFileUploadConfig()
	if err != nil {
		return nil, err
	}
	fileUploaderServiceImpl := fileUploader.NewServiceImpl(sugaredLogger, fileReferenceRepositoryImpl)
	commonHandlerImpl := fileUploader2.NewCommonHandlerImpl(sugaredLogger, fileUploadInternalConfig, fileUploaderServiceImpl, transactionUtilImpl)
	pipelineConverterFactoryImpl := pipelineConverters.NewPipelineConverterFactory(sugaredLogger, buildPipelineSwitchServiceImpl, materialRepositoryImpl, enterpriseEnforcerImpl, enforcerUtilImpl, appWorkflowRepositoryImpl, ciPipelineConfigServiceImpl, pipelineRepositoryImpl, ciPipelineRepositoryImpl, ciPipelineMaterialRepositoryImpl)
	bulkSwitchCIServiceImpl := bulkSwitchCi.NewBulkSwitchCIServiceImpl(sugaredLogger, pipelineConverterFactoryImpl, pipelineRepositoryImpl, appWorkflowServiceImpl, enforcerUtilImpl, enterpriseEnforcerImpl)
	userAttributesRepositoryImpl := repository2.NewUserAttributesRepositoryImpl(db)
	userAttributesServiceImpl := attributes.NewUserAttributesServiceImpl(sugaredLogger, userAttributesRepositoryImpl)
	globalFlagServiceImpl := globalFlag.NewGlobalFlagServiceImpl(userAttributesServiceImpl, attributesServiceImpl, userServiceImpl, environmentVariables)
	configDraftServiceImpl := drafts.NewConfigDraftServiceImpl(sugaredLogger, configDraftRepositoryImpl, configMapServiceImpl, chartServiceImpl, propertiesConfigServiceImpl, resourceProtectionServiceImpl, userServiceImpl, appRepositoryImpl, environmentRepositoryImpl, chartRepositoryImpl, lockConfigurationServiceImpl, mergeUtil, eventSimpleFactoryImpl, eventRESTClientImpl, deploymentTemplateValidationServiceImpl, deploymentTemplateServiceImpl, envConfigOverrideReadServiceExtendedImpl, requestApprovalUserDataRepositoryImpl, approvalPolicyReadServiceImpl, userGroupServiceImpl, configReadServiceImpl)
	draftAwareConfigServiceImpl := draftAwareConfigService.NewDraftAwareResourceServiceImpl(sugaredLogger, configMapServiceImpl, chartServiceImpl, propertiesConfigServiceImpl, configDraftServiceImpl, globalPolicyDataManagerImpl)
	cdWorkflowRunnerReadServiceImpl := read22.NewCdWorkflowRunnerReadServiceImpl(sugaredLogger, cdWorkflowRepositoryImpl)
	deploymentPipelineStrategyServiceImpl, err := strategy.NewDeploymentPipelineStrategyServiceImpl(sugaredLogger, pipelineRepositoryImpl, pipelineStrategyHistoryRepositoryImpl, deploymentTemplateServiceImpl, k8sCommonServiceImpl, chartReadServiceImpl, cdWorkflowRunnerReadServiceImpl)
	if err != nil {
		return nil, err
	}
	pipelineConfigRestHandlerImpl := configure.NewPipelineRestHandlerImpl(pipelineBuilderImpl, sugaredLogger, deploymentTemplateValidationServiceImpl, chartServiceImpl, devtronAppGitOpConfigServiceImpl, propertiesConfigServiceImpl, userServiceImpl, enterpriseEnforcerImpl, ciHandlerImpl, validate, clientImpl, ciPipelineRepositoryImpl, ciPipelineConfigReadServiceImpl, pipelineRepositoryImpl, enforcerUtilImpl, rbacFilterUtilImpl, environmentServiceImpl, dockerRegistryConfigImpl, cdHandlerImpl, appCloneServiceImpl, appServiceImpl, deploymentTemplateServiceImpl, appWorkflowServiceImpl, gitMaterialReadServiceImpl, policyServiceImpl, imageScanResultReadServiceImpl, ciPipelineMaterialRepositoryImpl, imageTaggingReadServiceImpl, imageTaggingServiceImpl, resourceProtectionServiceImpl, ciArtifactRepositoryImpl, deployedAppMetricsServiceImpl, chartRefServiceImpl, chartReadServiceImpl, chartRefSchemaServiceImpl, artifactApprovalActionServiceImpl, ciCdPipelineOrchestratorEnterpriseImpl, deploymentHistoryServiceImpl, userGroupServiceImpl, gitProviderReadServiceImpl, envConfigOverrideReadServiceExtendedImpl, commonHandlerImpl, teamReadServiceImpl, approvalPolicyReadServiceImpl, bulkSwitchCIServiceImpl, environmentRepositoryImpl, globalFlagServiceImpl, draftAwareConfigServiceImpl, handlerServiceImpl, devtronAppsHandlerServiceImpl, deploymentPipelineStrategyServiceImpl, commonEnforcementUtilImpl)
	commonArtifactServiceImpl := artifacts.NewCommonArtifactServiceImpl(sugaredLogger, ciArtifactRepositoryImpl)
	workflowDagExecutorImpl := dag.NewWorkflowDagExecutorImpl(sugaredLogger, pipelineRepositoryImpl, pipelineOverrideRepositoryImpl, cdWorkflowRepositoryImpl, pubSubClientServiceImpl, ciArtifactRepositoryImpl, enforcerUtilImpl, appWorkflowRepositoryImpl, pipelineStageServiceImpl, ciWorkflowRepositoryImpl, ciPipelineRepositoryImpl, pipelineStageRepositoryImpl, globalPluginRepositoryImpl, deploymentApprovalRepositoryImpl, eventRESTClientImpl, eventSimpleFactoryImpl, customTagServiceImpl, pipelineStatusTimelineServiceImpl, helmAppServiceImpl, cdWorkflowCommonServiceImpl, ciServiceImpl, devtronAppsHandlerServiceImpl, userDeploymentRequestServiceImpl, manifestCreationServiceImpl, commonArtifactServiceImpl, deploymentConfigServiceImpl, imageScanHistoryReadServiceImpl, helperServiceImpl, runnable, imageScanServiceImpl, approvalPolicyReadServiceImpl, cdWorkflowRunnerServiceImpl, k8sUtilExtended, environmentRepositoryImpl, k8sCommonServiceImpl, workflowServiceImpl, handlerServiceImpl, workflowTriggerAuditServiceImpl, fluxApplicationServiceImpl)
	externalCiRestHandlerImpl := restHandler.NewExternalCiRestHandlerImpl(sugaredLogger, validate, userServiceImpl, enterpriseEnforcerImpl, workflowDagExecutorImpl)
	pubSubClientRestHandlerImpl := restHandler.NewPubSubClientRestHandlerImpl(pubSubClientServiceImpl, sugaredLogger, ciCdConfig)
	webhookRouterImpl := router.NewWebhookRouterImpl(gitWebhookRestHandlerImpl, pipelineConfigRestHandlerImpl, externalCiRestHandlerImpl, pubSubClientRestHandlerImpl)
	userAuthHandlerImpl := user2.NewUserAuthHandlerImpl(userAuthServiceImpl, validate, sugaredLogger, enterpriseEnforcerImpl)
	selfRegistrationRolesRepositoryImpl := repository4.NewSelfRegistrationRolesRepositoryImpl(db, sugaredLogger)
	userSelfRegistrationServiceImpl := user.NewUserSelfRegistrationServiceImpl(sugaredLogger, selfRegistrationRolesRepositoryImpl, userServiceImpl, globalAuthorisationConfigServiceImpl)
	userAuthOidcHelperImpl, err := authentication.NewUserAuthOidcHelperImpl(sugaredLogger, userSelfRegistrationServiceImpl, dexConfig, settings, sessionManager)
	if err != nil {
		return nil, err
	}
	userAuthRouterImpl := user2.NewUserAuthRouterImpl(sugaredLogger, userAuthHandlerImpl, userAuthOidcHelperImpl)
	gitRegistryConfigImpl := gitProvider.NewGitRegistryConfigImpl(sugaredLogger, gitProviderRepositoryImpl, clientImpl)
	deleteServiceFullModeImpl := delete2.NewDeleteServiceFullModeImpl(sugaredLogger, gitMaterialReadServiceImpl, gitRegistryConfigImpl, ciTemplateRepositoryImpl, dockerRegistryConfigImpl, dockerArtifactStoreRepositoryImpl, manifestPushConfigRepositoryImpl)
	gitProviderRestHandlerImpl := restHandler.NewGitProviderRestHandlerImpl(dockerRegistryConfigImpl, sugaredLogger, gitRegistryConfigImpl, userServiceImpl, validate, enterpriseEnforcerImpl, deleteServiceFullModeImpl, gitProviderReadServiceImpl)
	gitProviderRouterImpl := router.NewGitProviderRouterImpl(gitProviderRestHandlerImpl)
	gitHostRepositoryImpl := repository47.NewGitHostRepositoryImpl(db)
	gitHostConfigImpl := gitHost.NewGitHostConfigImpl(gitHostRepositoryImpl, sugaredLogger)
	gitHostReadServiceImpl := read30.NewGitHostReadServiceImpl(sugaredLogger, gitHostRepositoryImpl, attributesServiceImpl)
	gitHostRestHandlerImpl := restHandler.NewGitHostRestHandlerImpl(sugaredLogger, gitHostConfigImpl, userServiceImpl, validate, enterpriseEnforcerImpl, clientImpl, gitProviderReadServiceImpl, gitHostReadServiceImpl)
	gitHostRouterImpl := router.NewGitHostRouterImpl(gitHostRestHandlerImpl)
	chartProviderServiceImpl := chartProvider.NewChartProviderServiceImpl(sugaredLogger, chartRepoRepositoryImpl, chartRepositoryServiceImpl, dockerArtifactStoreRepositoryImpl, ociRegistryConfigRepositoryImpl)
	dockerRegRestHandlerExtendedImpl := restHandler.NewDockerRegRestHandlerExtendedImpl(dockerRegistryConfigImpl, sugaredLogger, chartProviderServiceImpl, userServiceImpl, validate, enterpriseEnforcerImpl, deleteServiceExtendedImpl, deleteServiceFullModeImpl)
	dockerRegRouterImpl := router.NewDockerRegRouterImpl(dockerRegRestHandlerExtendedImpl)
	notificationConfigBuilderImpl := notifier.NewNotificationConfigBuilderImpl(sugaredLogger)
	promotionPolicyServiceImpl := artifactPromotion.NewPromotionPolicyServiceImpl(globalPolicyDataManagerImpl, cdPipelineConfigServiceImpl, sugaredLogger, qualifierMappingServiceImpl, resourceFilterEvaluatorImpl, transactionUtilImpl)
	commonPolicyActionsServiceV0Impl := v0.NewCommonPolicyActionsServiceV0(globalPolicyDataManagerImpl, qualifierMappingServiceImpl, cdPipelineConfigServiceImpl, appServiceImpl, environmentServiceImpl, clusterServiceImplExtended, criteriaQualifierMappingServiceImpl, sugaredLogger, transactionUtilImpl)
	approvalRequestServiceImpl := artifactPromotion.NewApprovalRequestServiceImpl(sugaredLogger, ciPipelineConfigServiceImpl, cdPipelineConfigServiceImpl, userServiceImpl, appWorkflowServiceImpl, cdWorkflowCommonServiceImpl, resourceFilterEvaluatorImpl, imageTaggingServiceImpl, imageTaggingReadServiceImpl, artifactPromotionDataReadServiceImpl, workflowDagExecutorImpl, promotionPolicyServiceImpl, commonPolicyActionsServiceV0Impl, pipelineStageServiceImpl, environmentServiceImpl, filterEvaluationAuditServiceImpl, transactionUtilImpl, ciArtifactRepositoryImpl, requestRepositoryImpl, requestApprovalUserDataRepositoryImpl, eventSimpleFactoryImpl, eventRESTClientImpl, teamReadServiceImpl)
	notificationConfigServiceImpl := notifier.NewNotificationConfigServiceImpl(sugaredLogger, notificationSettingsRepositoryImpl, notificationConfigBuilderImpl, ciPipelineRepositoryImpl, pipelineRepositoryImpl, slackNotificationRepositoryImpl, webhookNotificationRepositoryImpl, sesNotificationRepositoryImpl, smtpNotificationRepositoryImpl, environmentRepositoryImpl, appRepositoryImpl, userRepositoryImpl, ciPipelineMaterialRepositoryImpl, configDraftRepositoryImpl, ciArtifactRepositoryImpl, imageTaggingServiceImpl, artifactApprovalActionServiceImpl, evaluatorServiceImpl, approvalRequestServiceImpl, clusterServiceImplExtended, teamReadServiceImpl)
	slackNotificationServiceImpl := notifier.NewSlackNotificationServiceImpl(sugaredLogger, slackNotificationRepositoryImpl, webhookNotificationRepositoryImpl, teamServiceImpl, userRepositoryImpl, notificationSettingsRepositoryImpl)
	webhookNotificationServiceImpl := notifier.NewWebhookNotificationServiceImpl(sugaredLogger, webhookNotificationRepositoryImpl, userRepositoryImpl, notificationSettingsRepositoryImpl)
	sesNotificationServiceImpl := notifier.NewSESNotificationServiceImpl(sugaredLogger, sesNotificationRepositoryImpl, teamServiceImpl, notificationSettingsRepositoryImpl)
	smtpNotificationServiceImpl := notifier.NewSMTPNotificationServiceImpl(sugaredLogger, smtpNotificationRepositoryImpl, notificationSettingsRepositoryImpl)
	approverRbacServiceImpl := rbac3.NewApproverRbacServiceImpl(enterpriseEnforcerImpl, enforcerUtilImpl, sugaredLogger)
	configDraftRestHandlerImpl := drafts2.NewConfigDraftRestHandlerImpl(sugaredLogger, userServiceImpl, enterpriseEnforcerImpl, enforcerUtilImpl, validate, configDraftServiceImpl, approverRbacServiceImpl)
	notificationRestHandlerImpl := restHandler.NewNotificationRestHandlerImpl(dockerRegistryConfigImpl, sugaredLogger, gitRegistryConfigImpl, userServiceImpl, validate, notificationConfigServiceImpl, slackNotificationServiceImpl, webhookNotificationServiceImpl, sesNotificationServiceImpl, smtpNotificationServiceImpl, enterpriseEnforcerImpl, environmentServiceImpl, pipelineRepositoryImpl, enforcerUtilImpl, configDraftRestHandlerImpl, approvalRequestServiceImpl, teamReadServiceImpl)
	notificationRouterImpl := router.NewNotificationRouterImpl(notificationRestHandlerImpl)
	teamRestHandlerImpl := team2.NewTeamRestHandlerImpl(sugaredLogger, teamServiceImpl, userServiceImpl, enterpriseEnforcerImpl, validate, userAuthServiceImpl, deleteServiceExtendedImpl)
	teamRouterImpl := team2.NewTeamRouterImpl(teamRestHandlerImpl)
	policiesCleanUpRepositoryImpl := repository4.NewPoliciesCleanUpRepositoryImpl(db, sugaredLogger)
	cleanUpPoliciesServiceImpl := user.NewCleanUpPoliciesServiceImpl(userAuthRepositoryImpl, sugaredLogger, userRepositoryImpl, roleGroupRepositoryImpl, policiesCleanUpRepositoryImpl, cronLoggerImpl)
	userRestHandlerImpl := user2.NewUserRestHandlerImpl(userServiceImpl, validate, sugaredLogger, enterpriseEnforcerImpl, roleGroupServiceImpl, userCommonServiceImpl, cleanUpPoliciesServiceImpl, commonEnforcementUtilImpl)
	userRouterImpl := user2.NewUserRouterImpl(userRestHandlerImpl)
	chartRefRestHandlerImpl := restHandler.NewChartRefRestHandlerImpl(sugaredLogger, chartRefServiceImpl, chartServiceImpl)
	chartRefRouterImpl := router.NewChartRefRouterImpl(chartRefRestHandlerImpl)
	configMapRestHandlerImpl := restHandler.NewConfigMapRestHandlerImpl(pipelineBuilderImpl, sugaredLogger, chartServiceImpl, userServiceImpl, enterpriseEnforcerImpl, pipelineRepositoryImpl, enforcerUtilImpl, configMapServiceImpl, resourceProtectionServiceImpl, draftAwareConfigServiceImpl)
	configMapRouterImpl := router.NewConfigMapRouterImpl(configMapRestHandlerImpl)
	argoApplicationServiceImpl := argoApplication.NewArgoApplicationServiceImpl(sugaredLogger, clusterRepositoryImpl, k8sUtilExtended, helmAppClientImpl, helmAppServiceImpl, k8sApplicationServiceImpl, argoApplicationConfigServiceImpl, deploymentConfigServiceImpl)
	argoApplicationServiceExtendedImpl, err := argoApplication.NewArgoApplicationServiceExtendedImpl(argoApplicationServiceImpl, environmentServiceImpl, appServiceImpl, argoClientWrapperServiceImpl, acdAuthConfig, resourceTreeServiceV2Impl, argoApplicationReadServiceImpl, clusterServiceImplExtended)
	if err != nil {
		return nil, err
	}
	installedAppResourceServiceImpl := resource.NewInstalledAppResourceServiceImpl(sugaredLogger, installedAppRepositoryImpl, appStoreApplicationVersionRepositoryImpl, argoClientWrapperServiceImpl, acdAuthConfig, installedAppVersionHistoryRepositoryImpl, helmAppServiceImpl, helmAppReadServiceImpl, appStatusServiceImpl, k8sCommonServiceImpl, k8sApplicationServiceImpl, k8sUtilExtended, deploymentConfigServiceImpl, ociRegistryConfigRepositoryImpl, argoApplicationServiceExtendedImpl, clusterReadServiceImpl, fluxApplicationServiceImpl)
	chartGroupEntriesRepositoryImpl := repository48.NewChartGroupEntriesRepositoryImpl(db, sugaredLogger)
	chartGroupReposotoryImpl := repository48.NewChartGroupReposotoryImpl(db, sugaredLogger)
	chartGroupDeploymentRepositoryImpl := repository48.NewChartGroupDeploymentRepositoryImpl(db, sugaredLogger)
	appStoreVersionValuesRepositoryImpl := appStoreValuesRepository.NewAppStoreVersionValuesRepositoryImpl(sugaredLogger, db)
	appStoreRepositoryImpl := appStoreDiscoverRepository.NewAppStoreRepositoryImpl(sugaredLogger, db)
	clusterInstalledAppsRepositoryImpl := repository3.NewClusterInstalledAppsRepositoryImpl(db, sugaredLogger)
	appStoreValuesServiceImpl := service8.NewAppStoreValuesServiceImpl(sugaredLogger, appStoreApplicationVersionRepositoryImpl, installedAppRepositoryImpl, installedAppReadServiceEAImpl, appStoreVersionValuesRepositoryImpl, userServiceImpl)
	appStoreDeploymentCommonServiceImpl := appStoreDeploymentCommon.NewAppStoreDeploymentCommonServiceImpl(sugaredLogger, appStoreApplicationVersionRepositoryImpl, chartTemplateServiceImpl, userServiceImpl, helmAppServiceImpl, installedAppDBServiceImpl)
	fullModeDeploymentServiceImpl := deployment.NewFullModeDeploymentServiceImpl(sugaredLogger, argoK8sClientImpl, acdAuthConfig, chartGroupDeploymentRepositoryImpl, installedAppRepositoryImpl, installedAppVersionHistoryRepositoryImpl, appStoreDeploymentCommonServiceImpl, helmAppServiceImpl, appStatusServiceImpl, pipelineStatusTimelineServiceImpl, userServiceImpl, pipelineStatusTimelineRepositoryImpl, appStoreApplicationVersionRepositoryImpl, argoClientWrapperServiceImpl, acdConfig, gitOperationServiceImpl, gitOpsConfigReadServiceImpl, gitOpsValidationServiceImpl, environmentRepositoryImpl, deploymentConfigServiceImpl, chartTemplateServiceImpl)
	appStoreValidatorEnterpriseImpl := service9.NewAppStoreValidatorEnterpriseImpl(sugaredLogger)
	appStoreDeploymentDBServiceImpl := service9.NewAppStoreDeploymentDBServiceImpl(sugaredLogger, installedAppRepositoryImpl, appStoreApplicationVersionRepositoryImpl, appRepositoryImpl, environmentServiceImpl, installedAppVersionHistoryRepositoryImpl, environmentVariables, gitOpsConfigReadServiceImpl, deploymentTypeOverrideServiceImpl, fullModeDeploymentServiceImpl, appStoreValidatorEnterpriseImpl, installedAppDBServiceImpl, deploymentConfigServiceImpl, clusterReadServiceImpl)
	eaModeDeploymentServiceImpl := deployment2.NewEAModeDeploymentServiceImpl(sugaredLogger, helmAppServiceImpl, helmAppReadServiceImpl, appStoreApplicationVersionRepositoryImpl, helmAppClientImpl, installedAppRepositoryImpl, ociRegistryConfigRepositoryImpl, appStoreDeploymentCommonServiceImpl, remoteConnectionServiceImpl)
	fullModeFluxDeploymentServiceImpl := deployment.NewFullModeFluxDeploymentServiceImpl(sugaredLogger, appStoreDeploymentCommonServiceImpl, deploymentServiceImpl, clusterServiceImplExtended)
	deletePostProcessorEnterpriseImpl := service9.NewDeletePostProcessorEnterpriseImpl(sugaredLogger, internalProcessingServiceImpl)
	appStoreDeploymentServiceImpl := service9.NewAppStoreDeploymentServiceImpl(sugaredLogger, installedAppRepositoryImpl, installedAppDBServiceImpl, appStoreDeploymentDBServiceImpl, chartGroupDeploymentRepositoryImpl, appStoreApplicationVersionRepositoryImpl, appRepositoryImpl, eaModeDeploymentServiceImpl, fullModeDeploymentServiceImpl, fullModeFluxDeploymentServiceImpl, environmentServiceImpl, helmAppServiceImpl, installedAppVersionHistoryRepositoryImpl, environmentVariables, acdConfig, gitOpsConfigReadServiceImpl, deletePostProcessorEnterpriseImpl, appStoreValidatorEnterpriseImpl, deploymentConfigServiceImpl, chartScanPublishServiceImpl, ociRegistryConfigRepositoryImpl, remoteConnectionServiceImpl)
	appStoreAppsEventPublishServiceImpl := out.NewAppStoreAppsEventPublishServiceImpl(sugaredLogger, pubSubClientServiceImpl)
	chartGroupServiceImpl, err := chartGroup.NewChartGroupServiceImpl(sugaredLogger, chartGroupEntriesRepositoryImpl, chartGroupReposotoryImpl, chartGroupDeploymentRepositoryImpl, installedAppRepositoryImpl, appStoreVersionValuesRepositoryImpl, appStoreRepositoryImpl, userAuthServiceImpl, appStoreApplicationVersionRepositoryImpl, environmentServiceImpl, teamRepositoryImpl, clusterInstalledAppsRepositoryImpl, appStoreValuesServiceImpl, appStoreDeploymentServiceImpl, appStoreDeploymentDBServiceImpl, pipelineStatusTimelineServiceImpl, acdConfig, fullModeDeploymentServiceImpl, gitOperationServiceImpl, installedAppDBExtendedServiceImpl, appStoreAppsEventPublishServiceImpl, chartScanPublishServiceImpl, teamReadServiceImpl)
	if err != nil {
		return nil, err
	}
	cdPipelineEventPublishServiceImpl := out.NewCDPipelineEventPublishServiceImpl(sugaredLogger, pubSubClientServiceImpl)
	workflowStatusServiceImpl, err := status2.NewWorkflowStatusServiceImpl(sugaredLogger, workflowDagExecutorImpl, pipelineStatusTimelineServiceImpl, appServiceImpl, appStatusServiceImpl, acdConfig, appServiceConfig, pipelineStatusSyncDetailServiceImpl, argoClientWrapperServiceImpl, cdPipelineEventPublishServiceImpl, cdWorkflowRepositoryImpl, pipelineOverrideRepositoryImpl, installedAppVersionHistoryRepositoryImpl, appRepositoryImpl, environmentRepositoryImpl, installedAppRepositoryImpl, installedAppReadServiceImpl, pipelineStatusTimelineRepositoryImpl, pipelineRepositoryImpl, appListingServiceImpl, deploymentConfigServiceImpl, cdWorkflowRunnerServiceImpl, deploymentEventHandlerImpl)
	if err != nil {
		return nil, err
	}
	cdApplicationStatusUpdateHandlerImpl := cron2.NewCdApplicationStatusUpdateHandlerImpl(sugaredLogger, appServiceConfig, cdWorkflowRepositoryImpl, pipelineRepositoryImpl, installedAppVersionHistoryRepositoryImpl, installedAppReadServiceImpl, cronLoggerImpl, workflowStatusServiceImpl)
	installedAppDeploymentTypeChangeServiceImpl := deploymentTypeChange.NewInstalledAppDeploymentTypeChangeServiceImpl(sugaredLogger, installedAppRepositoryImpl, installedAppVersionHistoryRepositoryImpl, appStatusRepositoryImpl, gitOpsConfigReadServiceImpl, environmentRepositoryImpl, k8sCommonServiceImpl, k8sUtilExtended, fullModeDeploymentServiceImpl, eaModeDeploymentServiceImpl, argoClientWrapperServiceImpl, chartGroupServiceImpl, helmAppServiceImpl, clusterServiceImplExtended, clusterReadServiceImpl, appRepositoryImpl, deploymentConfigServiceImpl, argoApplicationServiceExtendedImpl)
	installedAppRestHandlerImpl := appStore.NewInstalledAppRestHandlerImpl(sugaredLogger, userServiceImpl, enterpriseEnforcerImpl, enforcerUtilImpl, enforcerUtilHelmImpl, installedAppDBExtendedServiceImpl, installedAppResourceServiceImpl, chartGroupServiceImpl, validate, clusterServiceImplExtended, appStoreDeploymentServiceImpl, appStoreDeploymentDBServiceImpl, helmAppClientImpl, cdApplicationStatusUpdateHandlerImpl, installedAppRepositoryImpl, appCrudOperationServiceEnterpriseImpl, installedAppDeploymentTypeChangeServiceImpl, clusterReadServiceImpl)
	appStoreValuesRestHandlerImpl := appStoreValues.NewAppStoreValuesRestHandlerImpl(sugaredLogger, userServiceImpl, appStoreValuesServiceImpl)
	appStoreValuesRouterImpl := appStoreValues.NewAppStoreValuesRouterImpl(appStoreValuesRestHandlerImpl)
	appStoreServiceImpl := service10.NewAppStoreServiceImpl(sugaredLogger, appStoreApplicationVersionRepositoryImpl)
	appStoreRestHandlerImpl := appStoreDiscover.NewAppStoreRestHandlerImpl(sugaredLogger, userServiceImpl, appStoreServiceImpl, enterpriseEnforcerImpl)
	appStoreDiscoverRouterImpl := appStoreDiscover.NewAppStoreDiscoverRouterImpl(appStoreRestHandlerImpl)
	chartProviderRestHandlerImpl := chartProvider2.NewChartProviderRestHandlerImpl(sugaredLogger, userServiceImpl, validate, chartProviderServiceImpl, enterpriseEnforcerImpl)
	chartProviderRouterImpl := chartProvider2.NewChartProviderRouterImpl(chartProviderRestHandlerImpl)
	appStoreDeploymentRestHandlerImpl := appStoreDeployment.NewAppStoreDeploymentRestHandlerImpl(sugaredLogger, userServiceImpl, enterpriseEnforcerImpl, enforcerUtilImpl, enforcerUtilHelmImpl, appStoreDeploymentServiceImpl, appStoreDeploymentDBServiceImpl, validate, helmAppServiceImpl, installedAppDBServiceImpl, attributesServiceImpl, clusterReadServiceImpl)
	appStoreDeploymentRouterImpl := appStoreDeployment.NewAppStoreDeploymentRouterImpl(appStoreDeploymentRestHandlerImpl)
	appStoreStatusTimelineRestHandlerImpl := appStore.NewAppStoreStatusTimelineRestHandlerImpl(sugaredLogger, pipelineStatusTimelineServiceImpl, enforcerUtilImpl, enterpriseEnforcerImpl)
	appStoreRouterImpl := appStore.NewAppStoreRouterImpl(installedAppRestHandlerImpl, appStoreValuesRouterImpl, appStoreDiscoverRouterImpl, chartProviderRouterImpl, appStoreDeploymentRouterImpl, appStoreStatusTimelineRestHandlerImpl)
	appStoreDeploymentCommonServiceEnterpriseImpl := appStoreDeploymentCommon.NewAppStoreDeploymentCommonServiceEnterpriseImpl(appStoreDeploymentCommonServiceImpl, sugaredLogger, chartTemplateServiceImpl)
	fullModeDeploymentServiceEnterpriseImpl := deployment.NewFullModeDeploymentServiceEnterpriseImpl(fullModeDeploymentServiceImpl, appStoreDeploymentCommonServiceEnterpriseImpl, sugaredLogger, installedAppRepositoryImpl, installedAppVersionHistoryRepositoryImpl)
	installedAppRestHandlerEnterpriseImpl := appStore.NewInstalledAppRestHandlerEnterpriseImpl(sugaredLogger, userServiceImpl, enterpriseEnforcerImpl, enforcerUtilImpl, installedAppDBExtendedServiceImpl, fullModeDeploymentServiceEnterpriseImpl, installedAppRestHandlerImpl)
	appStoreRouterEnterpriseImpl := appStore.NewAppStoreRouterEnterpriseImpl(installedAppRestHandlerEnterpriseImpl)
	chartRepositoryRestHandlerImpl := chartRepo2.NewChartRepositoryRestHandlerImpl(sugaredLogger, userServiceImpl, chartRepositoryServiceImpl, enterpriseEnforcerImpl, validate, deleteServiceExtendedImpl, attributesServiceImpl)
	chartRepositoryRouterImpl := chartRepo2.NewChartRepositoryRouterImpl(chartRepositoryRestHandlerImpl)
	lensConfig, err := lens.GetLensConfig()
	if err != nil {
		return nil, err
	}
	lensClientImpl, err := lens.NewLensClientImpl(lensConfig, sugaredLogger)
	if err != nil {
		return nil, err
	}
	releaseDataServiceImpl := app2.NewReleaseDataServiceImpl(pipelineOverrideRepositoryImpl, sugaredLogger, ciPipelineMaterialRepositoryImpl, eventRESTClientImpl, lensClientImpl)
	releaseMetricsRestHandlerImpl := restHandler.NewReleaseMetricsRestHandlerImpl(sugaredLogger, enterpriseEnforcerImpl, releaseDataServiceImpl, userServiceImpl, pipelineRepositoryImpl, enforcerUtilImpl)
	releaseMetricsRouterImpl := router.NewReleaseMetricsRouterImpl(sugaredLogger, releaseMetricsRestHandlerImpl)
	deploymentGroupAppRepositoryImpl := repository2.NewDeploymentGroupAppRepositoryImpl(sugaredLogger, db)
	deploymentGroupServiceImpl := deploymentGroup.NewDeploymentGroupServiceImpl(appRepositoryImpl, sugaredLogger, pipelineRepositoryImpl, ciPipelineRepositoryImpl, deploymentGroupRepositoryImpl, environmentRepositoryImpl, deploymentGroupAppRepositoryImpl, ciArtifactRepositoryImpl, appWorkflowRepositoryImpl, workflowEventPublishServiceImpl, approvalPolicyReadServiceImpl, artifactApprovalDataReadServiceImpl)
	deploymentGroupRestHandlerImpl := restHandler.NewDeploymentGroupRestHandlerImpl(deploymentGroupServiceImpl, sugaredLogger, validate, enterpriseEnforcerImpl, userServiceImpl, enforcerUtilImpl)
	deploymentGroupRouterImpl := router.NewDeploymentGroupRouterImpl(deploymentGroupRestHandlerImpl)
	buildActionImpl := batch.NewBuildActionImpl(pipelineBuilderImpl, sugaredLogger, appRepositoryImpl, appWorkflowRepositoryImpl, ciPipelineRepositoryImpl, gitMaterialReadServiceImpl)
	dataHolderActionImpl := batch.NewDataHolderActionImpl(appRepositoryImpl, configMapServiceImpl, environmentServiceImpl, sugaredLogger)
	deploymentTemplateActionImpl := batch.NewDeploymentTemplateActionImpl(sugaredLogger, appRepositoryImpl, chartServiceImpl)
	deploymentActionImpl := batch.NewDeploymentActionImpl(pipelineBuilderImpl, sugaredLogger, appRepositoryImpl, environmentServiceImpl, appWorkflowRepositoryImpl, ciPipelineRepositoryImpl, pipelineRepositoryImpl, dataHolderActionImpl, deploymentTemplateActionImpl)
	workflowActionImpl := batch.NewWorkflowActionImpl(sugaredLogger, appRepositoryImpl, appWorkflowServiceImpl, buildActionImpl, deploymentActionImpl)
	batchOperationRestHandlerImpl := restHandler.NewBatchOperationRestHandlerImpl(userServiceImpl, enterpriseEnforcerImpl, workflowActionImpl, sugaredLogger, enforcerUtilImpl)
	batchOperationRouterImpl := router.NewBatchOperationRouterImpl(batchOperationRestHandlerImpl, sugaredLogger)
	chartGroupRestHandlerImpl := chartGroup2.NewChartGroupRestHandlerImpl(chartGroupServiceImpl, sugaredLogger, userServiceImpl, enterpriseEnforcerImpl, validate)
	chartGroupRouterImpl := chartGroup2.NewChartGroupRouterImpl(chartGroupRestHandlerImpl)
	imageScanRestHandlerImpl := restHandler.NewImageScanRestHandlerImpl(sugaredLogger, imageScanServiceImpl, userServiceImpl, enterpriseEnforcerImpl, enforcerUtilImpl, environmentServiceImpl)
	imageScanRouterImpl := router.NewImageScanRouterImpl(imageScanRestHandlerImpl)
	policyRestHandlerImpl := restHandler.NewPolicyRestHandlerImpl(sugaredLogger, policyServiceImpl, userServiceImpl, userAuthServiceImpl, enterpriseEnforcerImpl, enforcerUtilImpl, environmentServiceImpl)
	policyRouterImpl := router.NewPolicyRouterImpl(policyRestHandlerImpl)
	gitOpsConfigServiceImpl := gitops.NewGitOpsConfigServiceImpl(sugaredLogger, gitOpsConfigRepositoryImpl, k8sUtilExtended, acdAuthConfig, clusterServiceImplExtended, gitOperationServiceImpl, gitOpsConfigReadServiceImpl, gitOpsValidationServiceImpl, environmentVariables, argoCDConnectionManagerImpl, argoCDConfigGetterImpl, argoClientWrapperServiceImpl, clusterReadServiceImpl, moduleReadServiceImpl)
	gitOpsConfigRestHandlerImpl := restHandler.NewGitOpsConfigRestHandlerImpl(sugaredLogger, moduleReadServiceImpl, gitOpsConfigServiceImpl, userServiceImpl, validate, enterpriseEnforcerImpl)
	gitOpsConfigRouterImpl := router.NewGitOpsConfigRouterImpl(gitOpsConfigRestHandlerImpl)
	dashboardConfig, err := dashboard.GetConfig()
	if err != nil {
		return nil, err
	}
	dashboardRouterImpl, err := dashboard.NewDashboardRouterImpl(sugaredLogger, dashboardConfig)
	if err != nil {
		return nil, err
	}
	attributesRestHandlerImpl := restHandler.NewAttributesRestHandlerImpl(sugaredLogger, enterpriseEnforcerImpl, userServiceImpl, attributesServiceImpl)
	attributesRouterImpl := router.NewAttributesRouterImpl(attributesRestHandlerImpl)
	userAttributesRestHandlerImpl := restHandler.NewUserAttributesRestHandlerImpl(sugaredLogger, enterpriseEnforcerImpl, userServiceImpl, userAttributesServiceImpl)
	userAttributesRouterImpl := router.NewUserAttributesRouterImpl(userAttributesRestHandlerImpl)
	commonRestHandlerImpl := restHandler.NewCommonRestHandlerImpl(sugaredLogger, userServiceImpl, commonServiceImpl)
	commonRouterImpl := router.NewCommonRouterImpl(commonRestHandlerImpl)
	grafanaConfig, err := grafana.GetConfig()
	if err != nil {
		return nil, err
	}
	grafanaRouterImpl, err := router.NewGrafanaRouterImpl(sugaredLogger, grafanaConfig)
	if err != nil {
		return nil, err
	}
	ssoLoginRepositoryImpl := sso.NewSSOLoginRepositoryImpl(db, sugaredLogger)
	ssoLoginServiceImpl := sso.NewSSOLoginServiceImpl(sugaredLogger, ssoLoginRepositoryImpl, k8sUtilExtended, environmentVariables, userAuthOidcHelperImpl, globalAuthorisationConfigServiceImpl)
	ssoLoginRestHandlerImpl := sso2.NewSsoLoginRestHandlerImpl(validate, sugaredLogger, enterpriseEnforcerImpl, userServiceImpl, ssoLoginServiceImpl)
	ssoLoginRouterImpl := sso2.NewSsoLoginRouterImpl(ssoLoginRestHandlerImpl)
	posthogClient, err := telemetry.NewPosthogClient(sugaredLogger)
	if err != nil {
		return nil, err
	}
	readReadServiceImpl := read31.NewReadServiceImpl(sugaredLogger, globalPolicyRepositoryImpl)
	policyEvaluationServiceImpl := release.NewPolicyEvaluationServiceImpl(sugaredLogger, readReadServiceImpl)
	cdWorkflowServiceImpl := cd.NewCdWorkflowServiceImpl(sugaredLogger, cdWorkflowRepositoryImpl)
	devtronResourceServiceExtendedImpl := devtronResource.NewDevtronResourceServiceExtendedImpl(sugaredLogger, devtronResourceRepositoryImpl, devtronResourceSchemaRepositoryImpl, devtronResourceObjectRepositoryImpl, devtronResourceTaskRunRepositoryImpl, devtronResourceObjectAuditRepositoryImpl, dtResObjDepRelationsRepositoryImpl, appRepositoryImpl, pipelineRepositoryImpl, appListingRepositoryImpl, userRepositoryImpl, ciArtifactRepositoryImpl, clusterRepositoryImpl, internalProcessingServiceImpl, readServiceImpl, objectAuditServiceImpl, appArtifactManagerImpl, appWorkflowDataReadServiceImpl, policyEvaluationServiceImpl, ciCdPipelineOrchestratorEnterpriseImpl, devtronAppsHandlerServiceImpl, cdPipelineEventPublishServiceImpl, cdWorkflowRunnerServiceImpl, cdWorkflowRunnerReadServiceImpl, cdWorkflowServiceImpl, environmentServiceImpl, dtResRelationReadServiceImpl, deploymentConfigServiceImpl, appCrudOperationServiceEnterpriseImpl, userServiceImpl, apiTokenServiceImpl, dockerRegistryConfigImpl, teamReadServiceImpl, globalPolicyDataManagerImpl, taskRunTriggerOperationServiceImpl)
	providerIdentifierServiceImpl := providerIdentifier.NewProviderIdentifierServiceImpl(sugaredLogger)
	licenseAttributesRepositoryImpl := repository49.NewLicenseAttributesRepositoryImpl(db)
	licenseAttributesServiceImpl := attributes.NewLicenseAttributesServiceImpl(sugaredLogger, licenseAttributesRepositoryImpl)
	licenseRepoImpl, err := licenseClient.NewLicenseRepoImpl(sugaredLogger, db, serviceImpl, apiTokenSecretServiceImpl, licenseAttributesServiceImpl, k8sUtilExtended)
	if err != nil {
		return nil, err
	}
	reminderThresholdConfig, err := env.GetThresholdReminderConfig()
	if err != nil {
		return nil, err
	}
	licenseManagerConfig, err := licenseClient.GetLicenseManagerConfig()
	if err != nil {
		return nil, err
	}
	licenseServiceImpl, err := licenseClient.NewLicenseServiceImpl(sugaredLogger, licenseRepoImpl, reminderThresholdConfig, licenseManagerConfig, cronLoggerImpl)
	if err != nil {
		return nil, err
	}
	telemetryEventClientImplExtended, err := telemetry2.NewTelemetryEventClientImplExtended(sugaredLogger, httpClient, clusterServiceImplExtended, k8sUtilExtended, acdAuthConfig, environmentServiceImpl, userServiceImpl, appListingRepositoryImpl, posthogClient, serviceImpl, ciPipelineConfigReadServiceImpl, pipelineRepositoryImpl, gitProviderRepositoryImpl, attributesRepositoryImpl, ssoLoginServiceImpl, appRepositoryImpl, ciWorkflowRepositoryImpl, cdWorkflowRepositoryImpl, dockerArtifactStoreRepositoryImpl, gitMaterialReadServiceImpl, ciTemplateRepositoryImpl, chartRepositoryImpl, userAuditServiceImpl, ciBuildConfigServiceImpl, moduleRepositoryImpl, serverDataStoreServerDataStore, helmAppClientImpl, installedAppReadServiceImpl, userAttributesRepositoryImpl, devtronResourceServiceExtendedImpl, providerIdentifierServiceImpl, cronLoggerImpl, gitOpsConfigReadServiceImpl, environmentVariables, globalPolicyDataManagerImpl, licenseServiceImpl, globalPluginRepositoryImpl, cvePolicyRepositoryImpl, defaultAuthPolicyRepositoryImpl, rbacPolicyDataRepositoryImpl, pipelineConfigRepositoryImpl)
	if err != nil {
		return nil, err
	}
	telemetryRestHandlerImpl := restHandler.NewTelemetryRestHandlerImpl(sugaredLogger, telemetryEventClientImplExtended, enterpriseEnforcerImpl, userServiceImpl)
	telemetryRouterImpl := router.NewTelemetryRouterImpl(sugaredLogger, telemetryRestHandlerImpl)
	bulkUpdateRepositoryImpl := bulkUpdate.NewBulkUpdateRepository(db, sugaredLogger)
	deployedAppServiceImpl := deployedApp.NewDeployedAppServiceImpl(sugaredLogger, k8sCommonServiceImpl, devtronAppsHandlerServiceImpl, environmentRepositoryImpl, pipelineRepositoryImpl, cdWorkflowRepositoryImpl, deploymentWindowServiceImpl, approvalPolicyReadServiceImpl, chartRefSchemaServiceImpl, envConfigOverrideReadServiceExtendedImpl, globalPolicyDataManagerImpl)
	bulkUpdateServiceImpl := service11.NewBulkUpdateServiceImpl(bulkUpdateRepositoryImpl, sugaredLogger, environmentRepositoryImpl, pipelineRepositoryImpl, appRepositoryImpl, deploymentTemplateHistoryServiceImpl, configMapHistoryServiceImpl, pipelineBuilderImpl, enforcerUtilImpl, ciHandlerImpl, ciPipelineRepositoryImpl, appWorkflowRepositoryImpl, appWorkflowServiceImpl, scopedVariableManagerImpl, resourceProtectionServiceImpl, deployedAppMetricsServiceImpl, chartRefServiceImpl, deployedAppServiceImpl, cdPipelineEventPublishServiceImpl, handlerServiceImpl, globalPolicyDataManagerImpl)
	bulkUpdateRestHandlerImpl := restHandler.NewBulkUpdateRestHandlerImpl(pipelineBuilderImpl, sugaredLogger, bulkUpdateServiceImpl, chartServiceImpl, propertiesConfigServiceImpl, userServiceImpl, enterpriseEnforcerImpl, ciHandlerImpl, validate, clientImpl, ciPipelineRepositoryImpl, pipelineRepositoryImpl, enforcerUtilImpl, environmentServiceImpl, gitRegistryConfigImpl, dockerRegistryConfigImpl, cdHandlerImpl, appCloneServiceImpl, appWorkflowServiceImpl, materialRepositoryImpl)
	bulkUpdateRouterImpl := router.NewBulkUpdateRouterImpl(bulkUpdateRestHandlerImpl)
	webhookSecretValidatorImpl := gitWebhook.NewWebhookSecretValidatorImpl(sugaredLogger)
	webhookEventDataRepositoryImpl := repository2.NewWebhookEventDataRepositoryImpl(db)
	webhookEventDataConfigImpl := pipeline.NewWebhookEventDataConfigImpl(sugaredLogger, webhookEventDataRepositoryImpl)
	ciPipelineEventPublishServiceImpl := out.NewCIPipelineEventPublishServiceImpl(sugaredLogger, pubSubClientServiceImpl)
	webhookEventHandlerImpl := restHandler.NewWebhookEventHandlerImpl(sugaredLogger, eventRESTClientImpl, webhookSecretValidatorImpl, webhookEventDataConfigImpl, ciPipelineEventPublishServiceImpl, gitHostReadServiceImpl)
	webhookListenerRouterImpl := router.NewWebhookListenerRouterImpl(webhookEventHandlerImpl)
	appFilteringRestHandlerImpl := appList.NewAppFilteringRestHandlerImpl(sugaredLogger, enterpriseEnforcerImpl, userServiceImpl, clusterServiceImplExtended, environmentServiceImpl, teamReadServiceImpl)
	appFilteringRouterImpl := appList2.NewAppFilteringRouterImpl(appFilteringRestHandlerImpl)
	virtualEnvResourceTreeServiceImpl := devtronApp.NewVirtualEnvResourceTreeServiceImpl(sugaredLogger, deploymentEventRepositoryImpl, cdWorkflowRepositoryImpl)
	resourceTreeServiceImpl := resourceTree.NewServiceImpl(sugaredLogger, appListingServiceImpl, appStatusServiceImpl, argoApplicationServiceExtendedImpl, cdApplicationStatusUpdateHandlerImpl, helmAppReadServiceImpl, helmAppServiceImpl, k8sApplicationServiceImpl, k8sCommonServiceImpl, environmentReadServiceImpl, clusterReadServiceImpl, deploymentTemplateServiceImpl, runnable, fluxApplicationServiceImpl)
	appListingRestHandlerImpl := appList.NewAppListingRestHandlerImpl(appListingServiceImpl, enterpriseEnforcerImpl, pipelineBuilderImpl, sugaredLogger, enforcerUtilImpl, deploymentGroupServiceImpl, userServiceImpl, k8sCommonServiceImpl, installedAppDBExtendedServiceImpl, installedAppResourceServiceImpl, pipelineRepositoryImpl, k8sApplicationServiceImpl, deploymentConfigServiceImpl, virtualEnvResourceTreeServiceImpl, featureFlagServiceImpl, approvalPolicyReadServiceImpl, resourceTreeServiceImpl, rbacFilterUtilImpl, globalFlagServiceImpl, globalPolicyDataManagerImpl)
	appListingRouterImpl := appList2.NewAppListingRouterImpl(appListingRestHandlerImpl)
	appInfoRestHandlerImpl := appInfo.NewAppInfoRestHandlerImpl(sugaredLogger, appCrudOperationServiceEnterpriseImpl, userServiceImpl, validate, enforcerUtilImpl, enterpriseEnforcerImpl, helmAppServiceImpl, enforcerUtilHelmImpl, genericNoteServiceImpl, commonEnforcementUtilImpl)
	appInfoRouterImpl := appInfo2.NewAppInfoRouterImpl(sugaredLogger, appInfoRestHandlerImpl)
	pipelineDeploymentConfigServiceImpl := pipeline.NewPipelineDeploymentConfigServiceImpl(sugaredLogger, chartRepositoryImpl, pipelineRepositoryImpl, pipelineConfigRepositoryImpl, configMapRepositoryImpl, scopedVariableCMCSManagerImpl, deployedAppMetricsServiceImpl, chartRefServiceImpl, configMapHistoryReadServiceImpl, envConfigOverrideReadServiceExtendedImpl, mergeUtil)
	pipelineConfigOverrideReadServiceImpl := configHistory.NewPipelineConfigOverrideReadServiceImpl(sugaredLogger, pipelineOverrideRepositoryImpl)
	pipelineStrategyHandlerImpl := devtronApps.NewPipelineStrategyHandlerImpl(sugaredLogger, chartReadServiceImpl, chartRefReadServiceImpl, k8sUtilExtended, environmentReadServiceImpl, clusterReadServiceImpl, k8sResourceHistoryServiceImpl, k8sCommonServiceImpl)
	pipelineTriggerRestHandlerImpl := trigger2.NewPipelineRestHandler(appServiceImpl, userServiceImpl, validate, enterpriseEnforcerImpl, sugaredLogger, enforcerUtilImpl, deploymentGroupServiceImpl, pipelineDeploymentConfigServiceImpl, deployedAppServiceImpl, devtronAppsHandlerServiceImpl, workflowEventPublishServiceImpl, pipelineConfigOverrideReadServiceImpl, commonHandlerImpl, devtronAppsHandlerServiceImpl, pipelineStrategyHandlerImpl)
	sseSSE := sse.NewSSE()
	pipelineTriggerRouterImpl := trigger3.NewPipelineTriggerRouter(pipelineTriggerRestHandlerImpl, sseSSE)
	webhookDataRestHandlerImpl := webhook.NewWebhookDataRestHandlerImpl(sugaredLogger, userServiceImpl, ciPipelineMaterialRepositoryImpl, enforcerUtilImpl, enterpriseEnforcerImpl, clientImpl, webhookEventDataConfigImpl)
	pipelineConfigRouterImpl := configure2.NewPipelineRouterImpl(pipelineConfigRestHandlerImpl, webhookDataRestHandlerImpl)
	prePostCiScriptHistoryRepositoryImpl := repository36.NewPrePostCiScriptHistoryRepositoryImpl(sugaredLogger, db)
	prePostCiScriptHistoryServiceImpl := history2.NewPrePostCiScriptHistoryServiceImpl(sugaredLogger, prePostCiScriptHistoryRepositoryImpl)
	pipelineHistoryRestHandlerImpl := history3.NewPipelineHistoryRestHandlerImpl(sugaredLogger, userServiceImpl, enterpriseEnforcerImpl, pipelineStrategyHistoryServiceImpl, deploymentTemplateHistoryServiceImpl, configMapHistoryServiceImpl, prePostCiScriptHistoryServiceImpl, prePostCdScriptHistoryServiceImpl, enforcerUtilImpl, deployedConfigurationHistoryServiceImpl)
	pipelineHistoryRouterImpl := history4.NewPipelineHistoryRouterImpl(pipelineHistoryRestHandlerImpl)
	pipelineStatusTimelineRestHandlerImpl := status3.NewPipelineStatusTimelineRestHandlerImpl(sugaredLogger, userServiceImpl, pipelineStatusTimelineServiceImpl, enforcerUtilImpl, enterpriseEnforcerImpl, cdApplicationStatusUpdateHandlerImpl, pipelineBuilderImpl)
	pipelineStatusRouterImpl := status4.NewPipelineStatusRouterImpl(pipelineStatusTimelineRestHandlerImpl)
	appWorkflowRestHandlerImpl := workflow.NewAppWorkflowRestHandlerImpl(sugaredLogger, userServiceImpl, appWorkflowServiceImpl, enterpriseEnforcerImpl, pipelineBuilderImpl, appServiceImpl, enforcerUtilImpl, chartServiceImpl, rbacFilterUtilImpl, globalFlagServiceImpl)
	appWorkflowRouterImpl := workflow2.NewAppWorkflowRouterImpl(appWorkflowRestHandlerImpl)
	devtronAppAutoCompleteRestHandlerImpl := pipeline4.NewDevtronAppAutoCompleteRestHandlerImpl(sugaredLogger, userServiceImpl, teamServiceImpl, enterpriseEnforcerImpl, enforcerUtilImpl, devtronAppConfigServiceImpl, environmentServiceImpl, dockerRegistryConfigImpl, gitProviderReadServiceImpl)
	devtronAppAutoCompleteRouterImpl := pipeline5.NewDevtronAppAutoCompleteRouterImpl(devtronAppAutoCompleteRestHandlerImpl)
	restHandlerImpl := artifactPromotionApprovalRequest.NewRestHandlerImpl(approvalRequestServiceImpl, sugaredLogger, userServiceImpl, appServiceImpl, validate, userCommonServiceImpl, enforcerUtilImpl, environmentServiceImpl, appArtifactManagerImpl, enterpriseEnforcerImpl)
	routerImpl := artifactPromotionApprovalRequest.NewRouterImpl(restHandlerImpl, restHandlerImpl)
	appRouterImpl := app4.NewAppRouterImpl(appFilteringRouterImpl, appListingRouterImpl, appInfoRouterImpl, pipelineTriggerRouterImpl, pipelineConfigRouterImpl, pipelineHistoryRouterImpl, pipelineStatusRouterImpl, appWorkflowRouterImpl, devtronAppAutoCompleteRouterImpl, appWorkflowRestHandlerImpl, appListingRestHandlerImpl, appFilteringRestHandlerImpl, routerImpl)
	coreAppRestHandlerImpl := restHandler.NewCoreAppRestHandlerImpl(sugaredLogger, userServiceImpl, validate, enforcerUtilImpl, enterpriseEnforcerImpl, appCrudOperationServiceEnterpriseImpl, pipelineBuilderImpl, gitRegistryConfigImpl, chartServiceImpl, configMapServiceImpl, appListingServiceImpl, propertiesConfigServiceImpl, appWorkflowServiceImpl, appWorkflowRepositoryImpl, environmentRepositoryImpl, configMapRepositoryImpl, chartRepositoryImpl, pipelineStageServiceImpl, ciPipelineRepositoryImpl, gitProviderReadServiceImpl, gitMaterialReadServiceImpl, teamReadServiceImpl, chartReadServiceImpl)
	coreAppRouterImpl := router.NewCoreAppRouterImpl(coreAppRestHandlerImpl)
	helmAppRestHandlerImpl := client4.NewHelmAppRestHandlerImpl(sugaredLogger, helmAppServiceImpl, enterpriseEnforcerImpl, clusterServiceImplExtended, enforcerUtilHelmImpl, appStoreDeploymentServiceImpl, installedAppDBServiceImpl, userServiceImpl, attributesServiceImpl, serverEnvConfigServerEnvConfig, fluxApplicationServiceImpl, argoApplicationServiceExtendedImpl)
	helmAppRouterImpl := client4.NewHelmAppRouterImpl(helmAppRestHandlerImpl)
	k8sApplicationRestHandlerImpl := application3.NewK8sApplicationRestHandlerImpl(sugaredLogger, k8sApplicationServiceImpl, pumpImpl, terminalSessionHandlerImpl, enterpriseEnforcerImpl, enforcerUtilHelmImpl, enforcerUtilImpl, helmAppServiceImpl, userServiceImpl, k8sCommonServiceImpl, validate, environmentVariables, fluxApplicationServiceImpl, argoApplicationReadServiceImpl, clusterReadServiceImpl, clusterRbacServiceImpl)
	k8sApplicationRouterImpl := application3.NewK8sApplicationRouterImpl(k8sApplicationRestHandlerImpl)
	pProfRestHandlerImpl := restHandler.NewPProfRestHandler(userServiceImpl, enterpriseEnforcerImpl)
	pProfRouterImpl := router.NewPProfRouter(sugaredLogger, pProfRestHandlerImpl)
	deploymentConfigRestHandlerImpl := deployment3.NewDeploymentConfigRestHandlerImpl(sugaredLogger, userServiceImpl, enterpriseEnforcerImpl, chartServiceImpl, chartRefServiceImpl, chartRefSchemaServiceImpl, validate)
	deploymentConfigRouterImpl := deployment3.NewDeploymentRouterImpl(deploymentConfigRestHandlerImpl)
	dashboardTelemetryRestHandlerImpl := dashboardEvent.NewDashboardTelemetryRestHandlerImpl(sugaredLogger, telemetryEventClientImplExtended)
	dashboardTelemetryRouterImpl := dashboardEvent.NewDashboardTelemetryRouterImpl(dashboardTelemetryRestHandlerImpl)
	commonDeploymentRestHandlerImpl := appStoreDeployment.NewCommonDeploymentRestHandlerImpl(sugaredLogger, userServiceImpl, enterpriseEnforcerImpl, enforcerUtilImpl, enforcerUtilHelmImpl, appStoreDeploymentServiceImpl, installedAppDBServiceImpl, validate, helmAppServiceImpl, attributesServiceImpl)
	commonDeploymentRouterImpl := appStoreDeployment.NewCommonDeploymentRouterImpl(commonDeploymentRestHandlerImpl)
	externalLinkMonitoringToolRepositoryImpl := externalLink.NewExternalLinkMonitoringToolRepositoryImpl(db)
	externalLinkIdentifierMappingRepositoryImpl := externalLink.NewExternalLinkIdentifierMappingRepositoryImpl(db)
	externalLinkRepositoryImpl := externalLink.NewExternalLinkRepositoryImpl(db)
	externalLinkServiceImpl := externalLink.NewExternalLinkServiceImpl(sugaredLogger, externalLinkMonitoringToolRepositoryImpl, externalLinkIdentifierMappingRepositoryImpl, externalLinkRepositoryImpl)
	externalLinkRestHandlerImpl := externalLink2.NewExternalLinkRestHandlerImpl(sugaredLogger, externalLinkServiceImpl, userServiceImpl, enterpriseEnforcerImpl, enforcerUtilImpl)
	externalLinkRouterImpl := externalLink2.NewExternalLinkRouterImpl(externalLinkRestHandlerImpl)
	globalPluginRestHandlerImpl := restHandler.NewGlobalPluginRestHandler(sugaredLogger, globalPluginServiceImpl, enforcerUtilImpl, enterpriseEnforcerImpl, pipelineBuilderImpl, userServiceImpl)
	globalPluginRouterImpl := router.NewGlobalPluginRouter(sugaredLogger, globalPluginRestHandlerImpl)
	moduleRestHandlerImpl := module2.NewModuleRestHandlerImpl(sugaredLogger, moduleServiceImpl, userServiceImpl, enterpriseEnforcerImpl, validate)
	moduleRouterImpl := module2.NewModuleRouterImpl(moduleRestHandlerImpl)
	serverActionAuditLogRepositoryImpl := server.NewServerActionAuditLogRepositoryImpl(db)
	serverServiceImpl := server.NewServerServiceImpl(sugaredLogger, serverActionAuditLogRepositoryImpl, serverDataStoreServerDataStore, serverEnvConfigServerEnvConfig, helmAppServiceImpl, moduleRepositoryImpl, serverCacheServiceImpl)
	serverRestHandlerImpl := server2.NewServerRestHandlerImpl(sugaredLogger, serverServiceImpl, userServiceImpl, enterpriseEnforcerImpl, validate)
	serverRouterImpl := server2.NewServerRouterImpl(serverRestHandlerImpl)
	apiTokenRestHandlerImpl := apiToken2.NewApiTokenRestHandlerImpl(sugaredLogger, apiTokenServiceImpl, userServiceImpl, enterpriseEnforcerImpl, validate)
	apiTokenRouterImpl := apiToken2.NewApiTokenRouterImpl(apiTokenRestHandlerImpl)
	k8sCapacityServiceImpl := capacity.NewK8sCapacityServiceImpl(sugaredLogger, k8sApplicationServiceImpl, k8sUtilExtended, k8sCommonServiceImpl)
	eksClusterInstaller := installer.NewEKSClusterInstaller(sugaredLogger, clusterRepositoryImpl, k8sUtilExtended, environmentVariables, clusterServiceImplExtended, infrastructureInstallationRepositoryImpl, deleteServiceExtendedImpl, environmentRepositoryImpl)
	installerFactoryImpl := installer.NewInstallerFactoryImpl(eksClusterInstaller)
	infrastructureInstallationPublishServiceImpl := publish2.NewInfrastructureInstallationPublishServiceImpl(sugaredLogger, pubSubClientServiceImpl)
	infrastructureInstallationServiceImpl, err := InfrastructureInstallationService.NewInfrastructureInstallationServiceImpl(sugaredLogger, infrastructureInstallationRepositoryImpl, clusterRepositoryImpl, environmentVariables, chartRepoRepositoryImpl, appStoreRepositoryImpl, appStoreApplicationVersionRepositoryImpl, environmentRepositoryImpl, environmentServiceImpl, teamReadServiceImpl, teamServiceImpl, cronLoggerImpl, k8sUtilExtended, installerFactoryImpl, eaModeDeploymentServiceImpl, infrastructureInstallationVersionsRepositoryImpl, infrastructureInstallationPublishServiceImpl)
	if err != nil {
		return nil, err
	}
	k8sCapacityRestHandlerImpl := capacity2.NewK8sCapacityRestHandlerImpl(sugaredLogger, k8sCapacityServiceImpl, userServiceImpl, enterpriseEnforcerImpl, clusterServiceImplExtended, environmentServiceImpl, clusterRbacServiceImpl, clusterReadServiceImpl, infrastructureInstallationServiceImpl, validate)
	k8sCapacityRouterImpl := capacity2.NewK8sCapacityRouterImpl(k8sCapacityRestHandlerImpl)
	webhookHelmServiceImpl := webhookHelm.NewWebhookHelmServiceImpl(sugaredLogger, helmAppServiceImpl, clusterServiceImplExtended, chartRepositoryServiceImpl, attributesServiceImpl)
	webhookHelmRestHandlerImpl := webhookHelm2.NewWebhookHelmRestHandlerImpl(sugaredLogger, webhookHelmServiceImpl, userServiceImpl, enterpriseEnforcerImpl, validate)
	webhookHelmRouterImpl := webhookHelm2.NewWebhookHelmRouterImpl(webhookHelmRestHandlerImpl)
	globalCMCSRestHandlerImpl := restHandler.NewGlobalCMCSRestHandlerImpl(sugaredLogger, userServiceImpl, validate, enterpriseEnforcerImpl, globalCMCSServiceImpl)
	globalCMCSRouterImpl := router.NewGlobalCMCSRouterImpl(globalCMCSRestHandlerImpl)
	terminalAccessRepositoryImpl := repository2.NewTerminalAccessRepositoryImpl(db, sugaredLogger)
	userTerminalSessionConfig, err := clusterTerminalAccess.GetTerminalAccessConfig()
	if err != nil {
		return nil, err
	}
	userTerminalAccessServiceImpl, err := clusterTerminalAccess.NewUserTerminalAccessServiceImpl(sugaredLogger, terminalAccessRepositoryImpl, userTerminalSessionConfig, k8sCommonServiceImpl, terminalSessionHandlerImpl, k8sCapacityServiceImpl, k8sUtilExtended, cronLoggerImpl)
	if err != nil {
		return nil, err
	}
	userTerminalAccessRestHandlerImpl := terminal2.NewUserTerminalAccessRestHandlerImpl(sugaredLogger, userTerminalAccessServiceImpl, enterpriseEnforcerImpl, userServiceImpl, validate, clusterRbacServiceImpl)
	userTerminalAccessRouterImpl := terminal2.NewUserTerminalAccessRouterImpl(userTerminalAccessRestHandlerImpl)
	jobRouterImpl := router.NewJobRouterImpl(pipelineConfigRestHandlerImpl, appListingRestHandlerImpl)
	ciWorkflowStatusUpdateConfig, err := cron2.GetCiWorkflowStatusUpdateConfig()
	if err != nil {
		return nil, err
	}
	ciStatusUpdateCronImpl := cron2.NewCiStatusUpdateCronImpl(sugaredLogger, appServiceImpl, ciWorkflowStatusUpdateConfig, ciPipelineRepositoryImpl, cronLoggerImpl, workflowDagExecutorImpl)
	resourceGroupRestHandlerImpl := restHandler.NewResourceGroupRestHandlerImpl(sugaredLogger, enterpriseEnforcerImpl, userServiceImpl, resourceGroupServiceImpl, validate)
	resourceGroupingRouterImpl := router.NewResourceGroupingRouterImpl(pipelineConfigRestHandlerImpl, appWorkflowRestHandlerImpl, resourceGroupRestHandlerImpl)
	globalTagRestHandlerImpl := globalTag2.NewGlobalTagRestHandlerImpl(sugaredLogger, userServiceImpl, globalTagServiceImpl, enterpriseEnforcerImpl, validate, teamReadServiceImpl)
	globalTagRouterImpl := globalTag2.NewGlobalTagRouterImpl(globalTagRestHandlerImpl)
	rbacPolicyResourceDetailRepositoryImpl := repository4.NewRbacPolicyResourceDetailRepositoryImpl(sugaredLogger, db)
	rbacRoleResourceDetailRepositoryImpl := repository4.NewRbacRoleResourceDetailRepositoryImpl(sugaredLogger, db)
	rbacRoleAuditRepositoryImpl := repository4.NewRbacRoleAuditRepositoryImpl(sugaredLogger, db)
	rbacRoleAuditServiceImpl := user.NewRbacRoleAuditServiceImpl(sugaredLogger, rbacRoleAuditRepositoryImpl)
	rbacRoleServiceImpl := user.NewRbacRoleServiceImpl(sugaredLogger, rbacPolicyResourceDetailRepositoryImpl, rbacRoleResourceDetailRepositoryImpl, rbacRoleDataRepositoryImpl, rbacPolicyDataRepositoryImpl, rbacDataCacheFactoryImpl, userAuthRepositoryImpl, userCommonServiceImpl, rbacRoleAuditServiceImpl)
	defaultRbacRoleDataRepositoryImpl := repository4.NewDefaultRbacRoleDataRepositoryImpl(sugaredLogger, db)
	defaultRbacRoleServiceImpl := user.NewDefaultRbacRoleServiceImpl(sugaredLogger, defaultRbacRoleDataRepositoryImpl, rbacRoleServiceImpl)
	rbacRoleRestHandlerImpl := user2.NewRbacRoleHandlerImpl(sugaredLogger, validate, rbacRoleServiceImpl, userServiceImpl, enterpriseEnforcerImpl, enforcerUtilImpl, defaultRbacRoleServiceImpl, commonEnforcementUtilImpl)
	rbacRoleRouterImpl := user2.NewRbacRoleRouterImpl(sugaredLogger, validate, rbacRoleRestHandlerImpl)
	globalPolicyRestHandlerImpl := globalPolicy2.NewGlobalPolicyRestHandlerImpl(sugaredLogger, pipelineRepositoryImpl, artifactApprovalDataReadServiceImpl, enforcerUtilImpl)
	globalPolicyRouterImpl := globalPolicy2.NewGlobalPolicyRouterImpl(sugaredLogger, globalPolicyRestHandlerImpl)
	configDraftRouterImpl := drafts2.NewConfigDraftRouterImpl(configDraftRestHandlerImpl)
	resourceProtectionRestHandlerImpl := protect2.NewResourceProtectionRestHandlerImpl(sugaredLogger, resourceProtectionServiceImpl, userServiceImpl, enterpriseEnforcerImpl, enforcerUtilImpl, validate)
	resourceProtectionRouterImpl := protect2.NewResourceProtectionRouterImpl(resourceProtectionRestHandlerImpl)
	scopedVariableRestHandlerImpl := scopedVariable.NewScopedVariableRestHandlerImpl(sugaredLogger, userServiceImpl, validate, pipelineBuilderImpl, enforcerUtilImpl, enterpriseEnforcerImpl, scopedVariableServiceImpl)
	scopedVariableRouterImpl := router.NewScopedVariableRouterImpl(scopedVariableRestHandlerImpl)
	ciTriggerCronConfig, err := cron2.GetCiTriggerCronConfig()
	if err != nil {
		return nil, err
	}
	ciTriggerCronImpl := cron2.NewCiTriggerCronImpl(sugaredLogger, ciTriggerCronConfig, pipelineStageRepositoryImpl, ciArtifactRepositoryImpl, globalPluginRepositoryImpl, cronLoggerImpl, handlerServiceImpl)
	resourceFilterRestHandlerImpl := resourceFilter2.NewResourceFilterRestHandlerImpl(sugaredLogger, userServiceImpl, enforcerUtilImpl, enterpriseEnforcerImpl, resourceFilterEvaluatorImpl, resourceFilterServiceImpl, validate, pipelineRepositoryImpl)
	resourceFilterRouterImpl := router.NewResourceFilterRouterImpl(resourceFilterRestHandlerImpl)
	devtronResourceSchemaAuditRepositoryImpl := repository9.NewDevtronResourceSchemaAuditRepositoryImpl(sugaredLogger, db)
	dtResSchemaServiceImpl := devtronResource.NewDtResSchemaServiceImpl(sugaredLogger, devtronResourceRepositoryImpl, devtronResourceSchemaRepositoryImpl, devtronResourceSchemaAuditRepositoryImpl, devtronResourceObjectRepositoryImpl, devtronResourceServiceExtendedImpl, readServiceImpl)
	devtronResourceRestHandlerImpl := devtronResource2.NewDevtronResourceRestHandlerImpl(sugaredLogger, userServiceImpl, enterpriseEnforcerImpl, enforcerUtilImpl, enforcerUtilHelmImpl, validate, devtronResourceServiceExtendedImpl, dtResSchemaServiceImpl, readServiceImpl, clusterRbacServiceImpl)
	devtronResourceRouterImpl := devtronResource2.NewDevtronResourceRouterImpl(devtronResourceRestHandlerImpl)
	apiReqDecoderServiceImpl := devtronResource.NewAPIReqDecoderServiceImpl(sugaredLogger, pipelineRepositoryImpl)
	historyRestHandlerImpl := devtronResource2.NewHistoryRestHandlerImpl(sugaredLogger, enterpriseEnforcerImpl, deploymentHistoryServiceImpl, apiReqDecoderServiceImpl, enforcerUtilImpl)
	historyRouterImpl := devtronResource2.NewHistoryRouterImpl(historyRestHandlerImpl)
	deploymentConfigurationServiceImpl, err := configDiff.NewDeploymentConfigurationServiceImpl(sugaredLogger, configMapServiceImpl, configDraftServiceImpl, appRepositoryImpl, environmentRepositoryImpl, chartServiceImpl, deploymentTemplateServiceImpl, deploymentTemplateHistoryRepositoryImpl, pipelineStrategyHistoryRepositoryImpl, configMapHistoryRepositoryImpl, scopedVariableCMCSManagerImpl, configMapRepositoryImpl, pipelineDeploymentConfigServiceImpl, chartRefServiceImpl, pipelineRepositoryImpl, deploymentTemplateHistoryReadServiceImpl, configMapHistoryReadServiceImpl, cdWorkflowRepositoryImpl, envConfigOverrideReadServiceExtendedImpl, chartTemplateServiceImpl, helmAppClientImpl, helmAppServiceImpl, k8sUtilExtended, mergeUtil, helmAppReadServiceImpl, chartReadServiceImpl, pipelineConfigRepositoryImpl, userServiceImpl)
	if err != nil {
		return nil, err
	}
	templateServiceImpl := devtronResource.NewTemplateServiceImpl(sugaredLogger, templateRepositoryImpl, readServiceImpl, devtronResourceObjectRepositoryImpl, appRepositoryImpl, appCrudOperationServiceEnterpriseImpl, appListingServiceImpl, ciPipelineRepositoryImpl, pipelineRepositoryImpl, pipelineBuilderImpl, configMapServiceImpl, chartServiceImpl, devtronAppStrategyServiceImpl, deploymentConfigurationServiceImpl, appWorkflowServiceImpl, environmentReadServiceImpl, gitOpsConfigReadServiceImpl, gitProviderReadServiceImpl, chartRepositoryImpl, envConfigOverrideReadServiceExtendedImpl, propertiesConfigServiceImpl, chartRefSchemaServiceImpl, chartRefServiceImpl, mergeUtil, userRepositoryImpl, dockerArtifactStoreRepositoryImpl)
	templateRestHandlerImpl := devtronResource2.NewTemplateRestHandlerImpl(sugaredLogger, enterpriseEnforcerImpl, enforcerUtilImpl, validate, templateServiceImpl, teamReadServiceImpl)
	templateRouterImpl := devtronResource2.NewTemplateRouterImpl(templateRestHandlerImpl)
	authorisationConfigRestHandlerImpl := globalConfig.NewGlobalAuthorisationConfigRestHandlerImpl(validate, sugaredLogger, enterpriseEnforcerImpl, userServiceImpl, globalAuthorisationConfigServiceImpl, userCommonServiceImpl, commonEnforcementUtilImpl)
	authorisationConfigRouterImpl := globalConfig.NewGlobalConfigAuthorisationConfigRouterImpl(authorisationConfigRestHandlerImpl)
	lockConfigRestHandlerImpl := lockConfiguation.NewLockConfigRestHandlerImpl(sugaredLogger, userServiceImpl, enterpriseEnforcerImpl, validate, lockConfigurationServiceImpl, userCommonServiceImpl, enforcerUtilImpl)
	lockConfigurationRouterImpl := lockConfiguation.NewLockConfigurationRouterImpl(lockConfigRestHandlerImpl)
	proxyConfig, err := proxy2.GetProxyConfig()
	if err != nil {
		return nil, err
	}
	proxyRouterImpl, err := proxy2.NewProxyRouterImpl(sugaredLogger, proxyConfig, enterpriseEnforcerImpl)
	if err != nil {
		return nil, err
	}
	imageDigestPolicyRestHandlerImpl, err := imageDigestPolicy2.NewImageDigestPolicyRestHandlerImpl(sugaredLogger, userServiceImpl, enforcerUtilImpl, enterpriseEnforcerImpl, validate, imageDigestPolicyServiceImpl)
	if err != nil {
		return nil, err
	}
	imageDigestPolicyRouterImpl := router.NewImageDigestPolicyRouterImpl(imageDigestPolicyRestHandlerImpl)
	deploymentConfigurationRestHandlerImpl := configDiff2.NewDeploymentConfigurationRestHandlerImpl(sugaredLogger, userServiceImpl, enforcerUtilImpl, deploymentConfigurationServiceImpl, enterpriseEnforcerImpl)
	deploymentConfigurationRouterImpl := configDiff3.NewDeploymentConfigurationRouter(deploymentConfigurationRestHandlerImpl)
	infraConfigRestHandlerImpl := infraConfig.NewInfraConfigRestHandlerImpl(sugaredLogger, infraConfigServiceImpl, userServiceImpl, enterpriseEnforcerImpl, enforcerUtilImpl, validate)
	infraConfigRouterImpl := infraConfig.NewInfraProfileRouterImpl(infraConfigRestHandlerImpl)
	argoApplicationRestHandlerImpl := argoApplication2.NewArgoApplicationRestHandlerImpl(argoApplicationServiceExtendedImpl, argoApplicationReadServiceImpl, sugaredLogger, enterpriseEnforcerImpl)
	argoApplicationRouterImpl := argoApplication2.NewArgoApplicationRouterImpl(argoApplicationRestHandlerImpl)
	fluxApplicationRestHandlerImpl := fluxApplication2.NewFluxApplicationRestHandlerImpl(fluxApplicationServiceImpl, sugaredLogger, enterpriseEnforcerImpl)
	fluxApplicationRouterImpl := fluxApplication2.NewFluxApplicationRouterImpl(fluxApplicationRestHandlerImpl)
	deploymentWindowRestHandlerImpl := deploymentWindow2.NewDeploymentWindowRestHandlerImpl(sugaredLogger, userServiceImpl, enterpriseEnforcerImpl, enforcerUtilImpl, validate, deploymentWindowServiceImpl, cdPipelineConfigServiceImpl)
	deploymentWindowRouterImpl := deploymentWindow2.NewDeploymentWindowRouterImpl(deploymentWindowRestHandlerImpl)
	commonPolicyRestHandlerImpl := commonPolicyActions.NewCommonPolicyRestHandlerImpl(commonPolicyActionsServiceImpl, commonPolicyActionsServiceV0Impl, userServiceImpl, enterpriseEnforcerImpl, enforcerUtilImpl, validate, mandatoryPluginEnforcementServiceImpl, sugaredLogger)
	commonPolicyRouterImpl := commonPolicyActions.NewCommonPolicyRouterImpl(commonPolicyRestHandlerImpl)
	artifactPromotionPolicyRestHandlerImpl := artifactPromotionPolicy.NewArtifactPromotionPolicyRestHandlerImpl(artifactPromotionDataReadServiceImpl, promotionPolicyServiceImpl, userServiceImpl, enterpriseEnforcerImpl, validate, sugaredLogger)
	artifactPromotionPolicyRouterImpl := artifactPromotionPolicy.NewCommonPolicyRouterImpl(artifactPromotionPolicyRestHandlerImpl)
	scanningResultRestHandlerImpl := resourceScan.NewScanningResultRestHandlerImpl(sugaredLogger, userServiceImpl, imageScanServiceImpl, enterpriseEnforcerImpl, enforcerUtilImpl, validate)
	scanningResultRouterImpl := resourceScan.NewScanningResultRouterImpl(scanningResultRestHandlerImpl)
	triggerRepositoryImpl := repository41.NewTriggerRepositoryImpl(db, sugaredLogger)
	interceptedEventsRepositoryImpl := repository41.NewInterceptedEventsRepositoryImpl(db, sugaredLogger)
	webhookRemediationHelper := autoRemediation.NewWebhookRemediationHelper(sugaredLogger, interceptedEventsRepositoryImpl)
	devtronJobRemediationHelper := autoRemediation.NewDevtronJobRemediationHelper(appRepositoryImpl, ciPipelineRepositoryImpl, environmentRepositoryImpl, appWorkflowRepositoryImpl, ciWorkflowRepositoryImpl, sugaredLogger)
	remediationHelperFactory := autoRemediation.NewRemediationHelperFactory(webhookRemediationHelper, devtronJobRemediationHelper)
	watcherServiceImpl := autoRemediation.NewWatcherServiceImpl(k8sEventWatcherRepositoryImpl, triggerRepositoryImpl, interceptedEventsRepositoryImpl, environmentRepositoryImpl, clusterRepositoryImpl, scoopClientGetterImpl, remediationHelperFactory, sugaredLogger)
	scoopServiceImpl := scoop2.NewServiceImpl(sugaredLogger, watcherServiceImpl, ciHandlerImpl, ciPipelineMaterialRepositoryImpl, interceptedEventsRepositoryImpl, clusterReadServiceImpl, attributesServiceImpl, apiTokenServiceImpl, eventRESTClientImpl, eventSimpleFactoryImpl, environmentServiceImpl, handlerServiceImpl)
	orchestratorCreds, err := types.GetOrchestratorCreds()
	if err != nil {
		return nil, err
	}
	scoopRestHandlerImpl := scoop2.NewRestHandler(scoopServiceImpl, watcherServiceImpl, enforcerUtilImpl, userServiceImpl, sugaredLogger, enterpriseEnforcerImpl, orchestratorCreds, environmentServiceImpl)
	watcherRestHandlerImpl := autoRemediation2.NewWatcherRestHandlerImpl(watcherServiceImpl, userServiceImpl, validate, enforcerUtilImpl, enterpriseEnforcerImpl, evaluatorServiceImpl, sugaredLogger)
	scoopRouterImpl := scoop2.NewRouterImpl(scoopRestHandlerImpl, watcherRestHandlerImpl)
	cloningModeFeatureFlagServiceImpl := service6.NewCloningModeFeatureFlagServiceImpl(sugaredLogger, gitMaterialReadServiceImpl, devtronResourceSearchableKeyServiceImpl, qualifierMappingServiceImpl, featureFlagServiceImpl, environmentRepositoryImpl, scopedVariableRepositoryImpl, k8sEventWatcherRepositoryImpl, clientImpl, featureFlagHelperImpl, scopedVariableServiceImpl)
	featureFlagFactoryServiceImpl := service6.NewFeatureFlagFactoryImpl(sugaredLogger, cloningModeFeatureFlagServiceImpl, featureFlagServiceImpl)
	featureFlagRestHandlerImpl := featureFlag.NewFeatureFlagRestHandlerImpl(sugaredLogger, userServiceImpl, validate, pipelineBuilderImpl, enforcerUtilImpl, enterpriseEnforcerImpl, featureFlagServiceImpl, featureFlagFactoryServiceImpl)
	featureFlagRouterImpl := featureFlag.NewFeatureFlagRouterImpl(featureFlagRestHandlerImpl)
	managedResourceServiceImpl := service12.NewManagedResourceServiceImpl(sugaredLogger, enterpriseEnforcerImpl, k8sCommonServiceImpl, k8sApplicationServiceImpl, deploymentTemplateServiceImpl, deploymentConfigServiceImpl, resourceTreeServiceImpl)
	driftRestHandlerImpl := drift.NewDriftRestHandlerImpl(sugaredLogger, enterpriseEnforcerImpl, enforcerUtilImpl, k8sCommonServiceImpl, k8sApplicationServiceImpl, deploymentTemplateServiceImpl, pipelineRepositoryImpl, deploymentConfigServiceImpl, virtualEnvResourceTreeServiceImpl, appListingRestHandlerImpl, managedResourceServiceImpl)
	driftRouterImpl := drift.NewDriftRouterImpl(driftRestHandlerImpl)
	syncDevImageService := syncDevImage.NewServiceImpl(sugaredLogger, cdPipelineConfigServiceImpl, ciServiceImpl, ciArtifactRepositoryImpl, workFlowStageStatusServiceImpl, devtronAppsHandlerServiceImpl, k8sUtilExtended, k8sApplicationServiceImpl, clusterServiceImplExtended, environmentRepositoryImpl, terminalSessionHandlerImpl, handlerServiceImpl)
	syncDevImageRestHandler := restHandler.NewSyncDevImageRestHandlerImpl(sugaredLogger, syncDevImageService, userServiceImpl, enterpriseEnforcerImpl, enforcerUtilImpl)
	syncDevImageRouter := router.NewSyncDevImageRouterImpl(syncDevImageRestHandler)
	migrationHistoryRepositoryImpl := repository50.NewMigrationHistoryRepositoryImpl(sugaredLogger, db)
	migrationServiceImpl := deployEntityMigration.NewMigrationServiceImpl(sugaredLogger, migrationHistoryRepositoryImpl, deploymentConfigServiceImpl, gitOperationServiceImpl, chartTemplateServiceImpl, gitOpsConfigReadServiceImpl, argoClientWrapperServiceImpl, appCrudOperationServiceEnterpriseImpl, deploymentWindowServiceImpl, commonPolicyActionsServiceV0Impl, appRepositoryImpl, pipelineRepositoryImpl, chartRepositoryImpl, environmentServiceImpl, pipelineConfigOverrideReadServiceImpl, cdWorkflowRunnerReadServiceImpl, deploymentTemplateDeploymentTemplateServiceImpl, deploymentTemplateHistoryRepositoryImpl, chartRefServiceImpl, environmentVariables, envConfigOverrideReadServiceExtendedImpl)
	migrationRestHandlerImpl := deployEntityMigration2.NewMigrationRestHandlerImpl(sugaredLogger, userServiceImpl, enterpriseEnforcerImpl, migrationServiceImpl)
	migrationRouterImpl := deployEntityMigration2.NewMigrationRouterImpl(sugaredLogger, migrationRestHandlerImpl)
	systemNetworkControllerRepositoryImpl := repository51.NewSystemNetworkControllerRepositoryImpl(sugaredLogger, db)
	sysNetControllerServiceImpl := systemNetworkController.NewSystemNetworkControllerServiceImpl(sugaredLogger, systemNetworkControllerRepositoryImpl)
	systemNetworkControllerRestHandlerImpl := systemNetworkController2.NewSystemNetworkControllerRestHandlerImpl(sugaredLogger, validate, enterpriseEnforcerImpl, sysNetControllerServiceImpl)
	systemNetworkControllerRouterImpl := systemNetworkController2.NewSystemNetworkControllerRouterImpl(systemNetworkControllerRestHandlerImpl)
	userGroupRestHandlerImpl := userGroup.NewUserGroupRestHandlerImpl(userServiceImpl, validate, sugaredLogger, enterpriseEnforcerImpl, userGroupServiceImpl)
	userGroupRouterImpl := userGroup.NewUserGroupRouterImpl(userGroupRestHandlerImpl)
	deploymentEventRestHandlerImpl := event.NewDeploymentEventRestHandlerImpl(sugaredLogger, userServiceImpl, virtualEnvResourceTreeServiceImpl)
	deploymentEventRouterImpl := event.NewDeploymentEventRouterImpl(deploymentEventRestHandlerImpl)
	silverSurferClientImpl, err := grpc2.NewSilverSurferClientImpl(sugaredLogger)
	if err != nil {
		return nil, err
	}
	clusterUpgradeServiceImpl, err := clusterUpgrade.NewClusterUpgradeServiceImpl(sugaredLogger, silverSurferClientImpl, clusterReadServiceImpl, k8sUtilExtended)
	if err != nil {
		return nil, err
	}
	clusterUpgradeRestHandlerImpl := clusterUpgrade2.NewClusterUpgradeRestHandlerImpl(sugaredLogger, userServiceImpl, enterpriseEnforcerImpl, clusterUpgradeServiceImpl)
	clusterUpgradeRouterImpl := clusterUpgrade2.NewClusterUpgradeRouterImpl(clusterUpgradeRestHandlerImpl)
	clusterConfigMap := bean4.NewClusterConfigMap()
	chatClientImpl, err := chat.NewChatClientImpl(sugaredLogger, clusterReadServiceImpl, k8sUtilExtended, interClusterServiceCommunicationHandlerImpl, clusterConfigMap)
	if err != nil {
		return nil, err
	}
	chatRouterImpl, err := chat2.NewChatRouterImpl(sugaredLogger, chatClientImpl)
	if err != nil {
		return nil, err
	}
	scanToolRestHandlerImpl := scanTool2.NewScanToolRestHandlerImpl(sugaredLogger, userServiceImpl, scanToolMetadataServiceImpl, enterpriseEnforcerImpl, enforcerUtilImpl, validate)
	scanToolRouterImpl := scanTool2.NewScanToolRouterImpl(scanToolRestHandlerImpl)
	userResourceExtendedServiceImpl := userResource.NewUserResourceExtendedServiceImpl(sugaredLogger, teamServiceImpl, environmentServiceImpl, appCrudOperationServiceEnterpriseImpl, chartGroupServiceImpl, appListingServiceImpl, appWorkflowServiceImpl, k8sApplicationServiceImpl, clusterServiceImplExtended, commonEnforcementUtilImpl, enforcerUtilImpl, enterpriseEnforcerImpl)
	userResourceRestHandlerImpl := userResource2.NewUserResourceRestHandler(sugaredLogger, userServiceImpl, userResourceExtendedServiceImpl)
	userResourceRouterImpl := userResource2.NewUserResourceRouterImpl(userResourceRestHandlerImpl)
	licenseHandlerImpl := handler.NewLicenseHandler(sugaredLogger, licenseServiceImpl, userServiceImpl, telemetryEventClientImplExtended)
	licenseRouterImpl := handler.NewLicenseRouterImpl(licenseHandlerImpl)
	infrastructureDeploymentHandlerImpl := infrastructureDeployment.NewInfrastructureDeploymentHandlerImpl(sugaredLogger, infrastructureInstallationServiceImpl, userServiceImpl, enterpriseEnforcerImpl)
	infrastructureDeploymentRouterImpl := infrastructureDeployment.NewInfrastructureDeploymentRouterImpl(infrastructureDeploymentHandlerImpl)
	chartCategoryRepositoryImpl := repository52.NewChartCategoryRepositoryImpl(db, sugaredLogger)
	chartCategoryMappingRepositoryImpl := repository52.NewChartCategoryMappingRepositoryImpl(db, sugaredLogger)
	chartCategoryServiceImpl, err := service13.NewChartCategoryServiceImpl(sugaredLogger, chartCategoryRepositoryImpl, chartCategoryMappingRepositoryImpl, appStoreRepositoryImpl, userAuthServiceImpl, environmentServiceImpl, teamRepositoryImpl)
	if err != nil {
		return nil, err
	}
	chartCategoryRestHandlerImpl := chartCategory.NewChartCategoryRestHandlerImpl(sugaredLogger, userServiceImpl, enterpriseEnforcerImpl, validate, chartCategoryServiceImpl)
	chartCategoryRouterImpl := chartCategory.NewChartCategoryRouterImpl(chartCategoryRestHandlerImpl)
	muxRouter := router.NewMuxRouter(sugaredLogger, environmentRouterImpl, clusterRouterImpl, webhookRouterImpl, userAuthRouterImpl, gitProviderRouterImpl, gitHostRouterImpl, dockerRegRouterImpl, notificationRouterImpl, teamRouterImpl, userRouterImpl, chartRefRouterImpl, configMapRouterImpl, appStoreRouterImpl, appStoreRouterEnterpriseImpl, chartRepositoryRouterImpl, releaseMetricsRouterImpl, deploymentGroupRouterImpl, batchOperationRouterImpl, chartGroupRouterImpl, imageScanRouterImpl, policyRouterImpl, gitOpsConfigRouterImpl, dashboardRouterImpl, attributesRouterImpl, userAttributesRouterImpl, commonRouterImpl, grafanaRouterImpl, ssoLoginRouterImpl, telemetryRouterImpl, telemetryEventClientImplExtended, bulkUpdateRouterImpl, webhookListenerRouterImpl, appRouterImpl, coreAppRouterImpl, helmAppRouterImpl, k8sApplicationRouterImpl, pProfRouterImpl, deploymentConfigRouterImpl, dashboardTelemetryRouterImpl, commonDeploymentRouterImpl, externalLinkRouterImpl, globalPluginRouterImpl, moduleRouterImpl, serverRouterImpl, apiTokenRouterImpl, cdApplicationStatusUpdateHandlerImpl, k8sCapacityRouterImpl, webhookHelmRouterImpl, globalCMCSRouterImpl, userTerminalAccessRouterImpl, jobRouterImpl, ciStatusUpdateCronImpl, resourceGroupingRouterImpl, globalTagRouterImpl, rbacRoleRouterImpl, globalPolicyRouterImpl, configDraftRouterImpl, resourceProtectionRouterImpl, scopedVariableRouterImpl, ciTriggerCronImpl, resourceFilterRouterImpl, devtronResourceRouterImpl, historyRouterImpl, templateRouterImpl, authorisationConfigRouterImpl, lockConfigurationRouterImpl, proxyRouterImpl, imageDigestPolicyRouterImpl, deploymentConfigurationRouterImpl, infraConfigRouterImpl, argoApplicationRouterImpl, fluxApplicationRouterImpl, deploymentWindowRouterImpl, commonPolicyRouterImpl, artifactPromotionPolicyRouterImpl, scanningResultRouterImpl, scoopRouterImpl, featureFlagRouterImpl, driftRouterImpl, syncDevImageRouter, migrationRouterImpl, systemNetworkControllerRouterImpl, userGroupRouterImpl, deploymentEventRouterImpl, clusterUpgradeRouterImpl, chatRouterImpl, scanToolRouterImpl, userResourceRouterImpl, licenseRouterImpl, infrastructureDeploymentRouterImpl, chartCategoryRouterImpl)
	loggingMiddlewareImpl := util4.NewLoggingMiddlewareImpl(userServiceImpl)
	webhookServiceImpl := pipeline.NewWebhookServiceImpl(ciArtifactRepositoryImpl, sugaredLogger, ciPipelineRepositoryImpl, ciWorkflowRepositoryImpl, ciServiceImpl, cdWorkflowCommonServiceImpl)
	workflowEventProcessorImpl, err := in2.NewWorkflowEventProcessorImpl(sugaredLogger, pubSubClientServiceImpl, cdWorkflowServiceImpl, cdWorkflowReadServiceImpl, cdWorkflowRunnerServiceImpl, cdWorkflowRunnerReadServiceImpl, workflowDagExecutorImpl, ciHandlerImpl, cdHandlerImpl, eventSimpleFactoryImpl, eventRESTClientImpl, devtronAppsHandlerServiceImpl, deployedAppServiceImpl, webhookServiceImpl, validate, environmentVariables, cdWorkflowCommonServiceImpl, cdPipelineConfigServiceImpl, userDeploymentRequestServiceImpl, serviceImpl, pipelineRepositoryImpl, ciArtifactRepositoryImpl, cdWorkflowRepositoryImpl, imageScanServiceImpl, pipelineConfigOverrideReadServiceImpl, notificationConfigServiceImpl, policyServiceImpl, ciCacheResourceSelectorImpl, deploymentConfigServiceImpl, handlerServiceImpl, runnable)
	if err != nil {
		return nil, err
	}
	ciPipelineEventProcessorImpl := in2.NewCIPipelineEventProcessorImpl(sugaredLogger, pubSubClientServiceImpl, gitWebhookServiceImpl)
	cdPipelineEventProcessorImpl := in2.NewCDPipelineEventProcessorImpl(sugaredLogger, pubSubClientServiceImpl, cdWorkflowCommonServiceImpl, workflowStatusServiceImpl, devtronAppsHandlerServiceImpl, pipelineRepositoryImpl, installedAppReadServiceImpl)
	deployedApplicationEventProcessorImpl := in2.NewDeployedApplicationEventProcessorImpl(sugaredLogger, pubSubClientServiceImpl, appServiceImpl, gitOpsConfigReadServiceImpl, installedAppDBExtendedServiceImpl, workflowDagExecutorImpl, cdWorkflowCommonServiceImpl, pipelineBuilderImpl, appStoreDeploymentServiceImpl, pipelineRepositoryImpl, installedAppReadServiceImpl, deploymentConfigServiceImpl)
	appStoreAppsEventProcessorImpl := in2.NewAppStoreAppsEventProcessorImpl(sugaredLogger, pubSubClientServiceImpl, chartGroupServiceImpl, installedAppVersionHistoryRepositoryImpl)
	chartScanEventProcessorImpl := in2.NewChartScanEventProcessorImpl(sugaredLogger, pubSubClientServiceImpl, helmAppServiceImpl, helmAppClientImpl, policyServiceImpl, k8sUtilExtended, chartTemplateServiceImpl, helmAppReadServiceImpl)
	centralEventProcessor, err := eventProcessor.NewCentralEventProcessor(sugaredLogger, workflowEventProcessorImpl, ciPipelineEventProcessorImpl, cdPipelineEventProcessorImpl, deployedApplicationEventProcessorImpl, appStoreAppsEventProcessorImpl, chartScanEventProcessorImpl)
	if err != nil {
		return nil, err
	}
	installationEventHandlerImpl, err := eventHandler.NewInstallationEventHandlerImpl(sugaredLogger, infrastructureInstallationRepositoryImpl, installerFactoryImpl, eaModeDeploymentServiceImpl, infrastructureInstallationVersionsRepositoryImpl, k8sUtilExtended)
	if err != nil {
		return nil, err
	}
	infrastructureInstallationEventProcessorImpl, err := subscribe.NewInfrastructureInstallationEventProcessorImpl(sugaredLogger, pubSubClientServiceImpl, installationEventHandlerImpl)
	if err != nil {
		return nil, err
	}
	licenseMiddleware := licenseClient.NewLicenseMiddleware(licenseServiceImpl)
	mainApp := NewApp(muxRouter, sugaredLogger, sseSSE, syncedEnforcer, casbinSyncedEnforcer, db, sessionManager, posthogClient, loggingMiddlewareImpl, userServiceImpl, centralEventProcessor, infrastructureInstallationEventProcessorImpl, pubSubClientServiceImpl, workflowEventProcessorImpl, licenseMiddleware)
	return mainApp, nil
}
