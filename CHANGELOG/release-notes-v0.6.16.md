## v0.6.16

## Bugs
- fix: Clicking on commit info shows incorrect data (#3371)
- fix: Build context disable (#3405)
- fix: build-context-git-material is not being set for cloned apps (#3397)
- fix:  Search option should not be case sensitive in case of clusters, preset values, chart store (#3396)
- fix: preset values update fix (#3361)
- fix: ci pipeline trigger bad gateway (#3386)
- fix: GRPC fetch material excluded flag (#3385)
- fix: job trigger fail (#3383)
- fix: deployment triggered time handling for pre deployed apps (#3378)
- fix: Rbac not working for resources having long names (#3370)
- fix: updated_by and created_by fields not getting updated in database for sso login services (#3368)
- fix: updated rbac for fetching all default role (#3363)
- fix: added deployment_strategy_ref_mapping_removal (#3362)
- fix: optimised ci trigger rbac, updated handling for invalid roles in user creation request (#3316)
- fix: Helm acd scale workload fix (#3340)
- fix: deployment strategy no visible in build and deploy page (#3326)
- fix: Invalidate cache configure bulk build (#3319)
- fix: getting cluster list min api optimized by enforcing env object in batch (#3317)
- fix: helm install error, nil pointer fix (#3310)
- fix: restore deleted sql script (#3306)
- fix: jobs trigger not working (#3296)
- fix: Api optimization fixes (#3292)
- fix: bulk cd trigger argo app unauthorized err (#3290)
- fix: api optimize fixes (#3275)
- fix: installAppVersionRequest id was being set at wrong place (#3411)
## Enhancements
- feat: Change cd deployment type (#3332)
- feat: added Statefulset reference chart (#3338)
- feat: added handling for failed step name in CI messages (#3356)
- feat: git material include exclude feature and partial commit search (#3343)
- feat: added build-context support in ci build (#3329)
- enhancement: made pipeline status crons cascading (#3372)
- enhancement: made revision history limit configurable for helm upgrade (#3353)
- feat: Approval node scripts (#3345)
- feat: show deployment status and timeline for helm apps deployed via gitops (#3299)
- feat:Cluster description note (#3174)
- feat: added api for getting list of all default rbac roles (#3344)
- enhancement: added rbac resource db scripts (#3298)
- feat: added deployedBy field in helm apps deployment history (#3324)
- feat: private chart support for helm apps (#3267)
- feat: add sharding sql scripts (#3302)
- feat: add gRPC client for git-sensor (#3085)
- feat: optimised user addition/creation flows (#3274)
- feat: added description field for environment  (#3020)
- perf: Api optimizations (#3250)
## Documentation
- docs: updated command for generating credentials to add cluster (#3346)
- docs: devtron terminal connection timeout issue on gke cluster (#3260)
- docs: cluster and environment url fix (#3284)
## Others
- Update ci-build-pre-post-plugins.md (#3399)
- task: updated status code for role fetch api (#3395)
- chore: Updated Github ISSUE-TEMPLATE (#3390)
- task: changes for kubelink update application requests (#3359)
- Fix for source type be (#3259)
- Deployment pipeline strategies configurable (#3311)
- Custom app grouping  (#3295)
- chore: added workflow for notification on discord forum ENV_NAME changes (#3301)
- chore: added workflow for notification on discord forum (#3300)


