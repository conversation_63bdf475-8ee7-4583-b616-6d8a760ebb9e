## v0.7.0

## Bugs
- fix: extra labels propagation made env driven (#5274)
- fix: App clone config map fix (#5268)
- fix: latest version in default cluster and on UI (#5259)
- fix: update CVE's severity and store multiple same CVE's in multiple packages (#5168)
- fix: fixing force push for gitops (#5152)
- fix: extra labels propagation based on k8s label regex matching (#5216)
- fix: invalid runner status (#5189)
- fix: revert changes from main (#5206)
- fix: apps and jobs permission (#5110)
- fix: added Copyright (#5172)
- fix:removed unused env var (#5174)
- fix: Handling all cases for 5xx (#5100)
- fix: trivy scan step command fix (#5162)
- fix: added extra args in trivy cmds (#5146)
- fix: proxyRouter empty data err panic handling (#5147)
- fix: handling side-effects for displaying external helm apps with same name across diff namespaces and clusters (#4951)
- fix: fatal log removed (#5043)
- fix: added a check for restricting managers to assign superadmin through permission groups (#5025)
- fix: SHOW_DOCKER_BUILD_ARGS variable not working as expected (#5117)
- fix: dependabot version upgrade (#5089)
- fix: containers are missing from app-details page in argocd app  (#4973)
- fix:resolved PR review comments also remove check for virtual cluster (#5095)
- fix:handled namespace case if deleted by kubectl (#5081)
- fix: oci chart were getting deployed through gitops (#5088)
- fix: argocd config update fix (#5074)
- fix: handle 5xx in fetch resource tree api and cd-trigger (#5050)
- fix: gitops update updated (#5055)
- fix: App create api validations (#5019)
- fix: git material saved in transaction (#5040)
- fix: panic while pulling images (#5036)
- fix: terminal stuck in connecting state (#4989)
- fix: handle for wrong format of k8s version in semvercompare func in cronjob template charts (#5016)
- fix: Dockerfile ubuntu version (#5022)
- fix: application status changes to HIBERNATING, when hibernation fails due to some reason (#5005)
- fix: deleted api token can be reused if created again with same name (#4978)
- fix: Kubelink Requests getting Failed for gRPC method GetAppDetails (#5012)
- fix: terminate sync if in progress  (#4946)
- fix: grpc error handling for TemplateChart req (#4980)
- fix: removed redundant import (#5004)
- fix: image promotion sql script (#4996)
- fix: image-approval-migartion fix (#4994)
- fix: ci-cd count per day in telemetry data (#4931)
## Enhancements
- feat: notifier behind nats (#5185)
- feat: cd pipeline deployment history refactoring (#5200)
- feat: wire nil test in pre ci pipeline (#4858)
- feat: added recovery counter metrics (#5124)
- feat: auto remediation (#5137)
- feat: support for ca cert in trivy (#5064)
- feat: validation for pipeline Type (#4670)
- feat: propagate labels such as envName and projectName (#5063)
- feat: Plugin to trigger Devtron Job (#5053)
- feat: CD Trigger Plugin (#4810)
- feat: Introduction to feasibility in Deployment (#4862)
## Documentation
- doc: Created Resource Watcher Doc (#5193)
- doc: Modified Portforward Section to Kubectl Section (#5236)
- doc: Added enhancements to security doc (#5203)
- docs: update readme to include multi arch flag (#4998)
- docs: config.md updatation for new flag (#5061)
- doc: Fixes in Documentation for May Month (#5150)
- doc: Created Resource Watcher Doc (#5193)
- doc: Modified Portforward Section to Kubectl Section (#5236)
- doc: Added enhancements to security doc (#5203)
- docs: update readme to include multi arch flag (#4998)
- docs: config.md updatation for new flag (#5061)
- doc: Added kubectl port-fwd section in RB (#5139)
- doc: Added Bitbucket Data Center in GitOps doc (#5075)
- doc: Image promotion policy (#4762)
- doc: Revamped Resource Browser Doc (#5035)
- doc: Added Bulk Restart in Application Groups doc (#5080)
- doc: Added new doc in the index (#5029)
- doc: Changes made in the doc according to the newer version (#5024)
- doc: Added Linked CI with Child Info + Runtime Build Parameters (#4991)
## Others
- chore: updated vendor (#5166)
- chore: gitops validation in api (#5082)
- chore: release v2 migration (#5126)
- chore: migration update for remote connection config (#5113)
- chore: added sql for release and release channels (#4898)
- chore: resource scan migration (#4977)
- chore: image promotion migration (#4992)
- misc: uniform GitHub action (#5069)


