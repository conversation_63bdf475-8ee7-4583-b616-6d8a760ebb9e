FROM golang:1.24.0  AS build-env

RUN echo $GOPATH
RUN apt update
RUN apt install git gcc musl-dev make -y
RUN go install github.com/google/wire/cmd/wire@latest
WORKDIR /go/src/github.com/devtron-labs/devtron
ADD . /go/src/github.com/devtron-labs/devtron/
RUN GOOS=linux make build-all

# uncomment this post build arg
FROM ubuntu:22.04@sha256:1b8d8ff4777f36f19bfe73ee4df61e3a0b789caeff29caa019539ec7c9a57f95 as  devtron-all

RUN apt update
RUN apt install ca-certificates git curl -y
RUN apt clean autoclean
RUN apt autoremove -y && rm -rf /var/lib/apt/lists/*

RUN useradd -ms /bin/bash devtron

COPY --from=build-env  /go/src/github.com/devtron-labs/devtron/devtron /home/<USER>/
COPY --from=build-env  /go/src/github.com/devtron-labs/devtron/auth_model.conf /home/<USER>/
#COPY --from=build-env  /go/src/github.com/devtron-labs/devtron/vendor/github.com/argoproj/argo-cd/assets/ /go/src/github.com/devtron-labs/devtron/vendor/github.com/argoproj/argo-cd/assets
COPY --from=build-env  /go/src/github.com/devtron-labs/devtron/argocd-assets/ /go/src/github.com/devtron-labs/devtron/vendor/github.com/argoproj/argo-cd/assets
COPY --from=build-env  /go/src/github.com/devtron-labs/devtron/scripts/devtron-reference-helm-charts /home/<USER>/scripts/devtron-reference-helm-charts
COPY --from=build-env  /go/src/github.com/devtron-labs/devtron/scripts/sql /home/<USER>/scripts/sql
COPY --from=build-env  /go/src/github.com/devtron-labs/devtron/scripts/casbin /home/<USER>/scripts/casbin
COPY --from=build-env  /go/src/github.com/devtron-labs/devtron/scripts/argo-assets/APPLICATION_TEMPLATE.tmpl /home/<USER>/scripts/argo-assets/APPLICATION_TEMPLATE.tmpl
COPY --from=build-env /go/src/github.com/devtron-labs/devtron/scripts/sql /home/<USER>/scripts/sql
COPY --from=build-env /go/src/github.com/devtron-labs/devtron/scripts/casbin /home/<USER>/scripts/casbin

COPY ./git-ask-pass.sh /home/<USER>/git-ask-pass.sh
RUN chmod +x /home/<USER>/git-ask-pass.sh

RUN chown -R devtron:devtron /home/<USER>/devtron
RUN chown -R devtron:devtron /home/<USER>/git-ask-pass.sh
RUN chown -R devtron:devtron /home/<USER>/auth_model.conf
RUN chown -R devtron:devtron /home/<USER>/scripts

USER devtron

WORKDIR /home/<USER>

CMD ["./devtron"]


#FROM alpine:3.15.0 as  devtron-ea

#RUN apk add --no-cache ca-certificates
#COPY --from=build-env  /go/src/github.com/devtron-labs/devtron/auth_model.conf .
#COPY --from=build-env  /go/src/github.com/devtron-labs/devtron/cmd/external-app/devtron-ea .

#COPY --from=build-env  /go/src/github.com/devtron-labs/devtron/vendor/github.com/argoproj/argo-cd/assets/ /go/src/github.com/devtron-labs/devtron/vendor/github.com/argoproj/argo-cd/assets
#COPY --from=build-env  /go/src/github.com/devtron-labs/devtron/scripts/devtron-reference-helm-charts scripts/devtron-reference-helm-charts
#COPY --from=build-env  /go/src/github.com/devtron-labs/devtron/scripts/argo-assets/APPLICATION_TEMPLATE.JSON scripts/argo-assets/APPLICATION_TEMPLATE.JSON

#CMD ["./devtron-ea"]
