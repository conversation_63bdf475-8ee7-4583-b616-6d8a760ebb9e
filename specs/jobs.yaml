openapi: "3.0.0"
info:
  version: 1.0.0
  title: Devtron Labs
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0.html
servers:
  - url: https://api.yourdomain.com
    description: Production server
  - url: http://localhost/orchestrator
    description: Local development server

paths:
  /orchestrator/job:
    post:
      summary: Create or clone a job
      description: Create and clone a job
      operationId: createOrCloneJob
      security:
        - ApiKeyAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateJob"
      responses:
        "200":
          description: Used to give response once a job is created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ActionResponse"
        "400":
          description: Bad request
        "401":
          description: Unauthorized
        "500":
          description: Internal server error
  /orchestrator/job/list:
    post:
      summary: List jobs
      description: Get the list of all the jobs by applying filter
      operationId: listJobs
      security:
        - ApiKeyAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/JobList"
      responses:
        "200":
          description: Used to give response of list of jobs
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/JobListResponse"
        "400":
          description: Bad request
        "401":
          description: Unauthorized
        "500":
          description: Internal server error
  /orchestrator/job/ci-pipeline/list/{jobId}:
    get:
      summary: Get job CI pipeline list
      description: fetch details of job ci-pipelines for the overview page
      operationId: getJobCiPipelineList
      security:
        - ApiKeyAuth: []
      parameters:
        - name: jobId
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Job CI pipeline list
        "400":
          description: Bad request
        "401":
          description: Unauthorized
        "500":
          description: Internal server error

components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: Authorization
  schemas:
    CreateJob:
      type: object
      properties:
        appName:
          type: string
          description: Used to give the name of the job
          example: "my-job-1"
        isJob:
          type: boolean
          description: States whether its a job or an app
          example: true
        teamId:
          type: integer
          description: Used to give team id
          example: 1
        templateId:
          type: integer
          description: Used to give the id of the job it wants to clone
          example: 18
        labels:
          type: array
          items:
            type: object
            properties:
              key:
                type: string
                example: "hello"
              value:
                type: string
                example: "world"
              propogate:
                type: boolean
                example: false
        description:
          type: string
          description: Used to give the description of the job once it is made.
          example: "This is my first Job"
    ActionResponse:
      type: object
      properties:
        id:
          type: integer
          description: Used to give the id of job once its created
          example: 25
        appName:
          type: string
          description: Used to give the name of job once its created
          example: "my-job-1"
        material:
          type: array
          items:
            $ref: '#/components/schemas/gitMaterial'
        teamId:
          type: integer
          description: Used to give the team id
          example: 1
        templateId:
          type: integer
          description: Used to give the templateId
          example: 0
        description:
          type: string
          description: Used to give the description of the job once it is made.
          example: "This is my first Job"
        isJob:
          type: boolean
          description: used to tell whether it is a job or an app
          example: true
    JobList:
      type: object
      properties:
        teams:
          type: array
          items:
            type: integer
          description: used to give the project id
          example: [1,2]
        appStatuses:
          type: array
          items:
            type: string
          description: used to give the filter of app ci-build status
          example: ["Succeeded", "Starting"]
        sortBy:
          type: string
          description: used to give the sort by constraint
          example: "appNameSort"
        sortOrder:
          type: string
          description: used to give the sort order
          example: "ASC"
        offset:
          type: integer
          description: used to give the number from which we want our job (if the offset is 20 means we want list of jobs from 20)
          example: 0
        size:
          type: integer
          description: used to give the number of jobs we want
          example: 20

    JobListResponse:
      type: object
      properties:
        jobContainers:
          type: array
          items:
            $ref: '#/components/schemas/jobContainer'
        jobCount:
          type: integer
# Add missing schemas for gitMaterial, jobContainer, and jobCiPipeline
    gitMaterial:
      type: object
      properties:
        name:
          type: string
        url:
          type: string
        id:
          type: integer
        gitProviderId:
          type: integer
        checkoutPath:
          type: string
        fetchSubmodules:
          type: boolean
        isUsedInCiConfig:
          type: boolean
    jobContainer:
      type: object
      properties:
        jobId:
          type: integer
        jobName:
          type: string
        description:
          type: string
        ciPipelines:
          type: array
          items:
            $ref: '#/components/schemas/jobCiPipeline'
    jobCiPipeline:
      type: object
      properties:
        ciPipelineId:
          type: integer
        status:
          type: string
        lastRunAt:
          type: string
          format: date-time
        lastSuccessAt:
          type: string
          format: date-time








