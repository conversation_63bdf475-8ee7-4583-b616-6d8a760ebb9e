openapi: "3.0.0"
info:
  version: 1.0.0
  title: Devtron Labs
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0.html
servers:
  - url: https://api.yourdomain.com
    description: Production server
security:
  - ApiKeyAuth: []
paths:
  /orchestrator/app-store/installed-app:
    get:
      summary: List deployed charts
      operationId: listDeployedCharts
      description: deployed chart listing, with search filters
      security:
        - ApiKeyAuth: []
      parameters:
        - name: envs
          in: query
          description: environment ids
          required: false
          schema:
            type: array
            items:
              type: string
        - name: chartRepoId
          in: query
          description: chart repo ids
          required: false
          schema:
            type: array
            items:
              type: string
        - name: appStoreName
          in: query
          description: chart name
          required: false
          schema:
            type: string
        - name: appName
          in: query
          description: chart name as app name for devtron
          required: false
          schema:
            type: string
        - name: onlyDeprecated
          in: query
          description: show only deprecated or all
          required: false
          schema:
            type: boolean
        - name: offset
          in: query
          description: offset for result set
          required: false
          schema:
            type: integer
        - name: size
          in: query
          description: total request size.
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: deployed chart listing, with search filters
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: array
                    description: deployed chart listing, with search filters
                    items:
                      $ref: '#/components/schemas/ChartInfo'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /orchestrator/app-store/installed-app/notes:
    get:
      summary: Fetch notes.txt for deployed helm charts
      operationId: fetchNotesTxt
      description: Used to fetch notes.txt for helm charts deployed via gitOps
      security:
        - ApiKeyAuth: []
      parameters:
        - name: env-id
          in: query
          description: it is an environment id of app
          required: true
          schema:
            type: integer
        - name: installed-app-id
          in: query
          description: it is a installed application id
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: if it is able to fetch the notes.txt then status will be ok
          content:
            application/json:
              schema:
                properties:
                  notes:
                    type: string
                    description: it will provide notes
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: error while fetching notes.txt



# components mentioned below
components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: Authorization
  schemas:
    ChartInfo:
      type: object
      required:
        - installedAppId
        - environmentId
        - installedAppVersionId
        - appStoreApplicationVersionId
        - appStoreApplicationName
        - status
        - appName
        - environmentName
        - deployedAt
        - deployedBy
        - readme
        - deprecated
      properties:
        installedAppId:
          type: integer
          description: installed chart id
        environmentId:
          type: integer
          description: environment id
        installedAppVersionId:
          type: integer
          description: installed chart version id
        appStoreApplicationVersionId:
          type: integer
          description: team/project id
        appStoreApplicationName:
          type: string
          description: chart name externally
        chartName:
          type: string
          description: chart repo name
        icon:
          type: string
          description: image
        status:
          type: string
          description: status of deployed chart
        appName:
          type: string
          description: chart name is app name for devtron
        environmentName:
          type: string
          description: env name
        deployedAt:
          type: string
          description: deployement time
        deployedBy:
          type: string
          description: user
        readme:
          type: string
          description: readme
        deprecated:
          type: boolean
          description: is deprecated or not

    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          format: int32
        status:
          type: string
        result:
          type: object
          nullable: true
        errors:
          type: array
          items:
            type: object
            properties:
              userMessage:
                type: string
                nullable: true
              internalMessage:
                type: string
                nullable: true

    Error:
      required:
        - code
        - message
      properties:
        code:
          type: integer
          description: Error code
        message:
          type: string
          description: Error message
