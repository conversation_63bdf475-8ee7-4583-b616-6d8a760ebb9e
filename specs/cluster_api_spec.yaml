openapi: "3.0.0"
info:
  version: 1.0.0
  title: Devtron Labs
paths:
  /orchestrator/cluster:
    put:
      description: Update Cluster
      operationId: UpdateCluster
      requestBody:
        description: A JSON object containing the cluster config
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ClusterBean'
      responses:
        '200':
          description: Successfully update the cluster
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClusterBean'
        '400':
          description: Bad Request. Input Validation(decode) error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    get:
      description: Get Cluster
      operationId: GetCluster
      parameters:
        - name: id
          in: query
          description: cluster id.
          required: true
          schema:
            type: integer
        - name: clusterId
          in: query
          description: comma-separated list of cluster IDs to filter clusters. If not provided, returns all clusters.
          required: false
          schema:
            type: string
            example: "1,2,3"
      responses:
        '200':
          description: Successfully get cluster(s)
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/ClusterBean'
                  - type: array
                    items:
                      $ref: '#/components/schemas/ClusterBean'
        '400':
          description: Bad Request. Input Validation(decode) error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /orchestrator/cluster/auth-list:
    get:
      description: list of accessible cluster
      responses:
        '200':
          description: cluster list
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: array
                    description: namespace list group by cluster
                    items:
                      $ref: '#/components/schemas/Cluster'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
# components mentioned below
components:
  schemas:
    ClusterBean:
      type: object
      properties:
        id:
          type: integer
        cluster_name:
          type: string
        server_url:
          type: string
        prometheus_url:
          type: string
        active:
          type: boolean
        config:
          type: object
          properties:
            bearer_token:
              type: string
              description: it will be empty while fetching, and if no change while updating
        k8sversion:
          type: string
    PrometheusAuth:
      type: object
      properties:
        userName:
          type: string
        password:
          type: string
        tlsClientCert:
          type: string
        tlsClientKey:
          type: string
    DefaultClusterComponent:
      type: object
      properties:
        name:
          type: string
        appId:
          type: integer
        installedAppId:
          type: integer
        envId:
          type: integer
        envname:
          type: string
        status:
          type: string
    Cluster:
      type: object
      required:
        - key
        - value
      properties:
        clusterId:
          type: integer
          description: cluster id
        clusterName:
          type: string
          description: cluster name
        errorInConnecting:
          type: string
          description: error message if cluster failed to connect

    ErrorResponse:
      required:
        - code
        - status
      properties:
        code:
          type: integer
          format: int32
          description: Error code
        status:
          type: string
          description: Error message
        errors:
          type: array
          description: errors
          items:
            $ref: '#/components/schemas/Error'

    Error:
      required:
        - code
        - status
      properties:
        code:
          type: integer
          format: int32
          description: Error internal code
        internalMessage:
          type: string
          description: Error internal message
        userMessage:
          type: string
          description: Error user message