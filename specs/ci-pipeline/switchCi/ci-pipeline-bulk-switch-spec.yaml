openapi: 3.0.3
info:
  title: Bulk Change Ci API
  version: 1.0.0
  description: APIs to check feasibility and execute pipeline type switches.
paths:
  /orchestrator/ci-pipelines/switch/feasibility:
    post:
      summary: Check Feasibility of Pipeline Type Switch
      description: Validates whether the specified pipelines can be switched to the desired type with the given configurations.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FeasibilityRequest'
      responses:
        '200':
          description: Feasibility check results
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SwitchResponse'
  /orchestrator/ci-pipelines/switch:
    post:
      summary: Execute Pipeline Type Switch
      description: Switches the specified pipelines to the desired type with the given configurations.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SwitchRequest'
      responses:
        '200':
          description: Pipeline switch results
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SwitchResponse'
components:
  schemas:
    SourcePipeline:
      type: object
      properties:
        pipelineType:
          type: string
          example: WEBHOOK
        pipelineId:
          type: integer
          example: 21
      required:
        - pipelineType
        - pipelineId
    DestinationPipelineData:
      type: object
      properties:
        pipelineType:
          type: string
          example: LINKED_CD
        customConfigurations:
          type: object
          properties:
            linkedCdEnvironment:
              type: string
              example: env-pre-prod
            ciMaterialSource:
              type: object
              properties:
                type:
                  type: string
                  example: SOURCE_TYPE_BRANCH_FIXED
                value:
                  type: string
                  example: main
                regex:
                  type: string
                  example: ""
              required:
                - type
                - value
      required:
        - pipelineType
    ResponseItem:
      type: object
      properties:
        sourcePipelineId:
          type: integer
          example: 21
        sourcePipelineType:
          type: string
          example: CI_JOB
        status:
          type: string
          enum:
            - Fail
            - Success
        statusDetails:
          type: string
          example: cyclic sources will be formed
      required:
        - sourcePipelineId
        - sourcePipelineType
        - status
    FeasibilityRequest:
      type: object
      properties:
        sourcePipelines:
          type: array
          items:
            $ref: '#/components/schemas/SourcePipeline'
        destinationPipelineData:
          $ref: '#/components/schemas/DestinationPipelineData'
      required:
        - sourcePipelines
        - destinationPipelineData
    SwitchRequest:
      type: object
      properties:
        sourcePipelines:
          type: array
          items:
            $ref: '#/components/schemas/SourcePipeline'
        destinationPipelineData:
          $ref: '#/components/schemas/DestinationPipelineData'
      required:
        - sourcePipelines
        - destinationPipelineData
    SwitchResponse:
      type: object
      properties:
        statusCode:
          type: integer
          example: 200
        result:
          type: array
          items:
            $ref: '#/components/schemas/ResponseItem'

