openapi: "3.0.0"
info:
  version: 1.0.0
  title: Devtron Labs - Application templates
paths:
  /orchestrator/resource/template:
    post:
      description: create template
      requestBody:
        description: A JSON object containing the template config
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TemplateCreateReq'
      responses:
        '200':
          description: Successfully create the template
          content:
            application/json:
              schema:
                type: string
                example: "Template created successfully."
  /orchestrator/resource/template/{kind}/{version}:
    post:
      description: create resource through template
      parameters:
        - name: kind
          in: path
          required: true
          schema:
            type: string
            enum:
              - "devtron-application"
        - name: version
          in: path
          required: true
          schema:
            type: string
            enum:
              - "alpha1"
      requestBody:
        description: A JSON object containing the resource creation request
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResCreateThroughTemplate'
      responses:
        '200':
          description: Successfully create the resource through template
          content:
            application/json:
              schema:
                type: string
                example: "App created successfully."
  /orchestrator/resource/template/options/{kind}/{version}/:
    get:
      summary: Gets all Apps having the none any locked configurations
      responses:
        '200':
          description: Successful Operation
          content:
            applicaton/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/App'
        '404':
          description: Unauthorised
          content:
            applicaton/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Error'
  /orchestrator/resource/template/list/{kind}/{version}:
    get:
      description: List resource templates
      parameters:
        - name: kind
          in: path
          required: true
          schema:
            type: string
            enum:
              - "devtron-application"
        - name: version
          in: path
          required: true
          schema:
            type: string
            enum:
              - "alpha1"
      responses:
        '200':
          description: list response
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: array
                    items:
                      $ref: '#/components/schemas/DevtronResourceTemplateListDto'
  /orchestrator/resource/template/{kind}/{version}/{apiPath}:
    get:
      description: fetch resource object
      parameters:
        - name: kind
          in: path
          required: true
          schema:
            type: string
            enum:
              - "devtron-application"
        - name: version
          in: path
          required: true
          schema:
            type: string
            enum:
              - "alpha1"
        - name: apiPath
          in: path
          required: true
          schema:
            type: string
            enum:
              - "/app/ci-pipeline/{appId}"
      responses:
        '200':
          description: list response
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    $ref: '#/components/schemas/CommonObject'
    delete:
      description: delete resource object
      parameters:
        - name: kind
          in: path
          required: true
          schema:
            type: string
            enum:
              - "devtron-application"
        - name: version
          in: path
          required: true
          schema:
            type: string
            enum:
              - "alpha1"
        - name: apiPath
          in: path
          required: true
          schema:
            type: string
            enum:
              - "/app/ci-pipeline/{appId}"
        - name: id
          description: id of the resource, either this or name or identifier is required. Preferred.
          in: query
          required: false
          schema:
            type: integer
        - name: name
          description: name of the resource, either this or id or identifier is required
            It can be used as query param where name is a unique constraint.
          in: query
          required: false
      responses:
        '200':
          description: delete response
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    $ref: '#/components/schemas/SuccessResponse'
    put:
      description: put template
      parameters:
        - name: kind
          in: path
          required: true
          schema:
            type: string
            enum:
              - "devtron-application"
        - name: version
          in: path
          required: true
          schema:
            type: string
            enum:
              - "alpha1"
        - name: apiPath
          in: path
          required: true
          schema:
            type: string
            enum:
              - "/app/ci-pipeline/{appId}"
      requestBody:
        description: json
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CommonObject'
      responses:
        '200':
          description: list response
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                    example: 200
                  status:
                    type: string
                    description: API status
                    example: "OK"
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    post:
      description: create/ update template entities
      parameters:
        - name: kind
          in: path
          required: true
          schema:
            type: string
            enum:
              - "release-track"
              - "release"
        - name: version
          in: path
          required: true
          schema:
            type: string
            enum:
              - "alpha1"
        - name: apiPath
          in: path
          required: true
          schema:
            type: string
            enum:
              - "/app/ci-pipeline/{appId}"
      requestBody:
        description: json
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CommonObject'
      responses:
        '200':
          description: list response
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                    example: 200
                  status:
                    type: string
                    description: API status
                    example: "OK"
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    patch:
      description: patch resource object
      parameters:
        - name: kind
          in: path
          required: true
          schema:
            type: string
            enum:
              - "devtron-application"
        - name: version
          in: path
          required: true
          schema:
            type: string
            enum:
              - "alpha1"
        - name: apiPath
          in: path
          required: true
          schema:
            type: string
            enum:
              - "/app/ci-pipeline/{appId}"
      requestBody:
        description: json
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CommonObject'
      responses:
        '200':
          description: list response
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                    example: 200
                  status:
                    type: string
                    description: API status
                    example: "OK"
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  schemas:
    App:
      type: object
      required:
        - appName
        - Id
        - CreatedBy
      properties:
        id:
          type: integer
          description: app id
          example: 2

        appName:
          type: string
          description: app name
          example: sampleApp
        description:
          type: string
          description: description of

        createdBy:
          type: string
          description: created by
          example:
            admin
    ResCreateThroughTemplate:
      type: object
      description: create resource(devtron app for now) through template
      properties:
        templateIdentifier:
          type: integer
          description: Identifier of the template
        templateId:
          type: integer
          description: id of the template
        appName:
          type: string
        teamId:
          type: integer
        description:
          type: string
        labels:
          type: array
          items:
            type: object
            properties:
              key:
                type: string
              value:
                type: string
              propagate:
                type: boolean
        templatePatch:
          type: object
          properties:
            gitMaterial:
              type: array
              items:
                type: object
                properties:
                  gitMaterialId:
                    type: integer
                  gitAccountId:
                    type: integer
                  gitMaterialURL:
                    type: string
            buildConfiguration:
              type: object
              properties:
                registryId:
                  type: string
                repository:
                  type: string
            workflows:
              type: object
              properties:
                cd:
                  type: array
                  items:
                    type: object
                    properties:
                      pipelineId:
                        type: integer
                      environmentId:
                        type: integer

    TemplateCreateReq:
      type: object
      properties:
        name:
          type: string
          description: The name of the template
        identifier:
          type: string
          description: A unique identifier for the template
        description:
          type: string
          description: A short description of the template
        sourceDetail:
          type: object
          properties:
            kind:
              type: string
              description: The kind or type of the template source
              enum:
                - "devtron-application"
            version:
              type: string
              description: The version of the template source
              enum:
                - "alpha1"
            id:
              type: integer
              description: The unique identifier of the source
    DevtronResourceTemplateListDto:
      type: object
      allOf:
        - $ref: '#/components/schemas/DevtronResourceTemplateDto'
    DevtronResourceTemplateDto:
      type: object
      required:
        [ id, idType,kind,version ]
      properties:
        id:
          type: integer
        name:
          type: string
        identifier:
          type: string
        description:
          type: string
        createdBy:
          type: string
          description: mail of user who created the template
        isPreset:
          type: boolean
    CommonObject:
      type: object
    ErrorResponse:
      required:
        - code
        - status
      properties:
        code:
          type: integer
          format: int32
          description: Error code
        status:
          type: string
          description: Error message
        errors:
          type: array
          description: errors
          items:
            $ref: '#/components/schemas/Error'
    Error:
      required:
        - code
        - status
      properties:
        code:
          type: integer
          format: int32
          description: Error internal code
        internalMessage:
          type: string
          description: Error internal message
        userMessage:
          type: string
          description: Error user message

    SuccessResponse:
      type: object
      properties:
        success:
          type: boolean