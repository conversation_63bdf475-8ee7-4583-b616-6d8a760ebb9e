openapi: "3.0.0"
info:
  version: 1.0.0
  title: Devtron Labs
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0.html
servers:
  - url: https://api.yourdomain.com
    description: Production server
security:
  - ApiKeyAuth: []
paths:
  /orchestrator/app/autocomplete:
    get:
      summary: List application autocomplete
      operationId: listAppAutocomplete
      description: list of namespaces group by clusters
      parameters:
        - in: query
          name: appName
          example: "abc"
          description: app name, wildcard query
          required: false
          allowEmptyValue: true
          schema:
            type: string
        - in: query
          name: teamId
          example: "1"
          description: project id
          required: false
          allowEmptyValue: false
          schema:
            type: integer
      responses:
        '200':
          description: list response
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: array
                    description: app list
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

# components mentioned below
components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: Authorization
  schemas:
    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          format: int32
        status:
          type: string
        result:
          type: object
          nullable: true
        errors:
          type: array
          items:
            type: object
            properties:
              userMessage:
                type: string
                nullable: true
              internalMessage:
                type: string
                nullable: true

    Error:
      required:
        - code
        - message
      properties:
        code:
          type: integer
          description: Error code
        message:
          type: string
          description: Error message
