openapi: "3.0.0"
info:
  version: 1.0.0
  title: Devtron Labs
paths:
  /orchestrator/app/labels/list:
    get:
      description: this api will return all the labels available in database.
      parameters: [ ]
      responses:
        '200':
          description: list response
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    allOf:
                      - type: object
                        properties:
                          appId:
                            type: integer
                            description: unique application id
                        required:
                          - appId
                      - $ref: '#/components/schemas/AppLabel'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /orchestrator/app/meta/info/{appId}:
    get:
      description: application basic info, projects and labels
      parameters:
        - name: appId
          in: path
          description: application id
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: application basic info, projects and labels
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: object
                    description: meta info project name and labels
                    $ref: '#/components/schemas/AppMetaInfo'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /orchestrator/helm/meta/info/{appId}:
    get:
      description: application info for all types of helm apps
      parameters:
        - name: appId
          in: path
          description: application id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Helm application basic info
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: object
                    description: meta info project name and labels
                    $ref: '#/components/schemas/AppMetaInfo'

# components mentioned below
components:
  schemas:
    AppLabel:
      type: object
      required:
        - key
        - value
      properties:
        key:
          type: string
          description: label key
        value:
          type: string
          description: label value
        propagate:
          type: boolean
          description: Whether to propagate to kubernetes resources

    AppLabels:
      type: object
      required:
        - appId
        - labels
      properties:
        appId:
          type: integer
          description: application id
        labels:
          type: array
          items:
            $ref: '#/components/schemas/AppLabel'

    AppMetaInfo:
      type: object
      required:
        - appId
        - projectId
        - appName
        - projectName
        - createdOn
        - createdBy
        - labels
      properties:
        appId:
          type: integer
          description: app id
        projectId:
          type: integer
          description: team/project id
        appName:
          type: string
          description: app name
        projectName:
          type: string
          description: team/project name
        labels:
          type: array
          items:
            $ref: '#/components/schemas/AppLabel'
        createdOn:
          type: string
          description: app creation date
        createdBy:
          type: string
          description: app created by

    ErrorResponse:
      required:
        - code
        - status
      properties:
        code:
          type: integer
          format: int32
          description: Error code
        status:
          type: string
          description: Error message
        errors:
          type: array
          description: errors
          items:
            $ref: '#/components/schemas/Error'

    Error:
      required:
        - code
        - status
      properties:
        code:
          type: integer
          format: int32
          description: Error internal code
        internalMessage:
          type: string
          description: Error internal message
        userMessage:
          type: string
          description: Error user message