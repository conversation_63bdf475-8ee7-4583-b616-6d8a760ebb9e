openapi: "3.0.0"
info:
  version: 1.0.0
  title: Devtron Labs
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0.html
servers:
  - url: http://localhost/orchestrator
    description: Local development server
security:
  - ApiKeyAuth: []
paths:
  /orchestrator/app/labels/list:
    get:
      summary: List all app labels
      description: This API will return all the labels available in the database.
      operationId: listAppLabels
      security:
        - ApiKeyAuth: []
      parameters: [ ]
      responses:
        '400':
          description: Bad request
        '401':
          description: Unauthorized
        '404':
          description: Not found
        '500':
          description: Internal server error
        '200':
          description: list response
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    allOf:
                      - type: object
                        properties:
                          appId:
                            type: integer
                            description: unique application id
                        required:
                          - appId
                      - $ref: '#/components/schemas/AppLabel'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /orchestrator/app/meta/info/{appId}:
    get:
      summary: Get application meta info
      description: Application basic info, projects and labels
      operationId: getAppMetaInfo
      security:
        - ApiKeyAuth: []
      parameters:
        - name: appId
          in: path
          description: application id
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: application basic info, projects and labels
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: object
                    description: meta info project name and labels
                    $ref: '#/components/schemas/AppMetaInfo'
        '400':
          description: Bad request
        '401':
          description: Unauthorized
        '404':
          description: Not found
        '500':
          description: Internal server error
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /orchestrator/helm/meta/info/{appId}:
    get:
      summary: Get Helm application meta info
      description: Application info for all types of Helm apps
      operationId: getHelmAppMetaInfo
      security:
        - ApiKeyAuth: []
      parameters:
        - name: appId
          in: path
          description: application id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Helm application basic info
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: object
                    description: meta info project name and labels
                    $ref: '#/components/schemas/AppMetaInfo'
        '400':
          description: Bad request
        '401':
          description: Unauthorized
        '404':
          description: Not found
        '500':
          description: Internal server error
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

# components mentioned below
components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: Authorization
  schemas:
    AppLabel:
      type: object
      required:
        - key
        - value
      properties:
        key:
          type: string
          description: label key
        value:
          type: string
          description: label value
        propagate:
          type: boolean
          description: Whether to propagate to kubernetes resources

    AppLabels:
      type: object
      required:
        - appId
        - labels
      properties:
        appId:
          type: integer
          description: application id
        labels:
          type: array
          items:
            $ref: '#/components/schemas/AppLabel'

    AppMetaInfo:
      type: object
      required:
        - appId
        - projectId
        - appName
        - projectName
        - createdOn
        - createdBy
        - labels
      properties:
        appId:
          type: integer
          description: app id
        projectId:
          type: integer
          description: team/project id
        appName:
          type: string
          description: app name
        projectName:
          type: string
          description: team/project name
        labels:
          type: array
          items:
            $ref: '#/components/schemas/AppLabel'
        createdOn:
          type: string
          description: app creation date
        createdBy:
          type: string
          description: app created by

    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          format: int32
        status:
          type: string
        result:
          type: object
          nullable: true
        errors:
          type: array
          items:
            type: object
            properties:
              userMessage:
                type: string
                nullable: true
              internalMessage:
                type: string
                nullable: true

    Error:
      required:
        - code
        - message
      properties:
        code:
          type: integer
          description: Error code
        message:
          type: string
          description: Error message
