apiVersion: batch/v1beta2
kind: Application
spec:
  selectors:
    match:
      project:
        includes:
          names:
            - '%node-js%'
            - 'java-%'
        excludes:
          names:
            - '%go-lang%'
            - '%python%'
      app:
        includes:
          names:
            - 'prod-kafka-%'
            - '%-prod-server'
        excludes:
          names:
            - '%-non-prod-server'
            - '%-dev-server'
      env:
        type: prod|non-prod
        includes:
          names:
            - '%prod-client-%'
            - '%poc-client-%'
        excludes:
          names:
            - '%demo-client-%'
            - '%dev-%'
  deploymentTemplate:
    match:
      include-base-config: true|false
      chart:
        name: RolloutDeployment|Deployment
        version:
          value: 4.20.0
          operator: EQUAL|GREATER|LESS|GREATER_EQUAL|LESS_EQUAL
        custom: true|false
    operation:
      action: update
      field: values|version
      # For action -> update and field -> values, patchJson is required.
      patchJson: '[{ "op": "add", "path": "/{key}", "value": "{value}" },{"op": "replace","path":"/{key}","value": "{value}"}]'
      # For action -> update and field -> version, chartVersion is required.
      # chartVersion should be a valid symbolic chart version.
      chartVersion: "4.20.0"
  configMap:
    spec:
      match:
        include-base-config: true|false
        includes:
          names:
            - '%dashboard%'
            - '%server%'
        excludes:
          names:
            - '%db%'
            - '%micro-service%'
      operation:
        action: create|update|delete
        field: data
        # For action update, patchJson is required.
        patchJson: '[{ "op": "add", "path": "/{key}", "value": "{value}" },{"op": "replace","path":"/{key}","value": "{value}"}]'
        # For action create or delete, value is required.
        # For delete, value is the name of the config-map to be deleted.
        # For create, value is a json object.
        # e.g: "{\"name\": \"server-cm\",\"type\": \"environment\",\"data\": {\"FEAT_TEST_ENABLE\": \"false\",\"LOG_LEVEL\": \"-1\"},\"mergeStrategy\": \"replace\"}"
        value: {{ .Value }}
  secret:
    spec:
      match:
        include-base-config: true|false
        includes:
          names:
            - '%abc%'
            - '%xyz%'
        excludes:
          names:
            - '%abcd%'
            - '%xyza%'
      operation:
        action: create|update|delete
        field: data
        # For action update, patchJson is required.
        patchJson: '[{ "op": "add", "path": "/{key}", "value": "{value}" },{"op": "replace","path":"/{key}","value": "{value}"}]'
        # For action create or delete, value is required.
        # For delete, value is the name of the secret to be deleted.
        # For create, value is a json object.
        # e.g: "{\"name\": \"server-cm\",\"type\": \"environment\",\"data\": {\"DB_PASSWORD\": \"******\",\"ADMIN_PASSWORD\": \"******\"},\"mergeStrategy\": \"replace\"}"
        value: {{ .Value }}