openapi: 3.0.3
servers:
  - url: 'http://127.0.0.1:8080'
info:
  version: 1.0.0
  title: Bulk Edit - Deployment Template, ConfigMaps, Secrets
  license:
    name: Apache-2.0
    url: 'https://www.apache.org/licenses/LICENSE-2.0.html'
tags:
  - name: config
    description: Configuration for Bulk Edit GVKs
  - name: apply
    description: Apply Bulk Edit Specific to GVKs
  - name: dry-run
    description: Dry Run for Bulk Edit Specific to GVKs
paths:
  /orchestrator/batch/{apiVersion}/{kind}/config:
    get:
      tags:
        - config
      description: Returns configuration details for bulk update. This includes the readme, schema json and templates.
      operationId: GetBulkEditConfigByGVK
      parameters:
        - name: apiVersion
          in: path
          required: true
          schema:
            type: string
        - name: kind
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful GET operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkEditConfig'
        '400':
          description: Bad Request. Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /orchestrator/batch/v1beta2/application/dryrun:
    post:
      tags:
        - dry-run
      description: Returns metadata (application name, environment name, resource names) of all the resources to be impacted with bulk update
      operationId: DryRunBulkEditRequest
      requestBody:
        description: A JSON object containing information about the bulk edit operation, including the specification for deployment templates, config maps, and secrets.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkEdit'
      responses:
        '200':
          description: Successfully return all the impacted resource details.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ImpactedObjects'
        '400':
          description: Bad Request. Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /orchestrator/batch/v1beta2/application:
    post:
      tags:
        - apply
      description: Bulk edit all the impacted resources based on the provided specification.
      operationId: ApplyBulkEditRequest
      requestBody:
        description: A JSON object containing information about the bulk edit operation, including the specification for deployment templates, config maps, and secrets.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkEdit'
      responses:
        '200':
          description: Successfully updated all impacted resources.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkEditResponse'
        '400':
          description: Bad Request. Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
components:
  schemas:
    BulkEditConfig:
      type: object
      description: Configuration of different Bulk Edit GVKs
      required:
        - gvk
        - readme
        - schema
      properties:
        gvk:
          type: string
          description: Resource GVK from URL path, i.e., {apiVersion} & {kind}
          x-oapi-codegen-extra-tags:
            validate: "required,min=1,max=250"
        readme:
          type: string
          description: Readme for the bulk edit operation, explaining how to use the API and its parameters.
          x-oapi-codegen-extra-tags:
            validate: "required"
        schema:
          type: object
          description: JSON schema for the bulk edit operation, defining the structure of the request body.
          x-oapi-codegen-extra-tags:
            validate: "required"
        templates:
          type: array
          x-oapi-codegen-extra-tags:
            validate: "dive"
          items:
            $ref: '#/components/schemas/Template'
    Template:
      allOf:
        - $ref: '#/components/schemas/BulkEdit'
        - type: object
          x-oapi-codegen-extra-tags:
            validate: "dive"
          description: List of templates for the bulk edit operation.
          required:
            - metadata
            - readme
            - schema
          properties:
            metadata:
              $ref: '#/components/schemas/Metadata'
            readme:
              type: string
              description: Readme for the bulk edit operation, explaining how to use the API and its parameters.
              x-oapi-codegen-extra-tags:
                validate: "required"
            schema:
              type: object
              description: JSON schema for the bulk edit operation, defining the structure of the request body.
              x-oapi-codegen-extra-tags:
                validate: "required"
    Metadata:
      x-oapi-codegen-extra-tags:
        validate: "dive"
      type: object
      required:
        - name
        - version
      properties:
        name:
          type: string
          description: Name of the template
          x-oapi-codegen-extra-tags:
            validate: "required,min=3,max=250"
        version:
          type: string
          description: Semantic version of the template
          x-oapi-codegen-extra-tags:
            validate: "required,min=1,max=250"
        description:
          type: string
          description: Description of the template
          x-oapi-codegen-extra-tags:
            validate: "required,min=1,max=350"
        createdBy:
          type: string
          description: User who created the template
        createdOn:
          type: string
          format: date-time
          description: Timestamp when the template was created
        modifiedBy:
          type: string
          description: User who modified the template
        modifiedOn:
          type: string
          format: date-time
          description: Timestamp when the template was modified
        deprecated:
          type: boolean
          description: Indicates if the template is deprecated
        deprecatedOn:
          type: string
          format: date-time
          description: Timestamp when the template was deprecated
    BulkEdit:
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      description: Input Script for bulk edit
      required:
        - apiVersion
        - kind
        - spec
      properties:
        apiVersion:
          type: string
          enum: ["batch/v1beta2"]
          x-oapi-codegen-extra-tags:
            validate: "required,oneof=batch/v1beta2"
        kind:
          type: string
          enum: ["application", "Application"]
          x-oapi-codegen-extra-tags:
              validate: "required,oneof=application Application"
        spec:
          $ref: '#/components/schemas/BulkEditSpec'
    BulkEditSpec:
      anyOf:
        - $ref: '#/components/schemas/BulkEditDeploymentTemplateSpec'
        - $ref: '#/components/schemas/BulkEditConfigMapSpec'
        - $ref: '#/components/schemas/BulkEditSecretSpec'
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      required:
        - selectors
      properties:
        selectors:
          $ref: '#/components/schemas/Selectors'
    BulkEditDeploymentTemplateSpec:
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      properties:
        deploymentTemplate:
          $ref: '#/components/schemas/DeploymentTemplate'
    BulkEditConfigMapSpec:
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      properties:
        configMap:
          $ref: '#/components/schemas/ConfigMap'
    BulkEditSecretSpec:
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      properties:
        secret:
          $ref: '#/components/schemas/Secret'
    Selectors:
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      required:
        - match
      properties:
        match:
          $ref: '#/components/schemas/MatchIdentifier'
    MatchIdentifier:
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      anyOf:
        - $ref: '#/components/schemas/ProjectIdentifierDto'
        - $ref: '#/components/schemas/AppIdentifierDto'
        - $ref: '#/components/schemas/EnvIdentifierDto'
    ProjectIdentifierDto:
      type: object
      x-oapi-codegen-extra-tags:
          validate: "dive"
      properties:
        project:
          $ref: '#/components/schemas/NameIdentifier'
    NameIdentifier:
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      properties:
        includes:
          $ref: '#/components/schemas/NameIncludesExcludes'
        excludes:
          $ref: '#/components/schemas/NameIncludesExcludes'
    AppIdentifierDto:
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      properties:
        app:
            $ref: '#/components/schemas/AppIdentifier'
    AppIdentifier:
          type: object
          x-oapi-codegen-extra-tags:
            validate: "dive"
          properties:
            includes:
              $ref: '#/components/schemas/NameIncludesExcludes'
            excludes:
              $ref: '#/components/schemas/NameIncludesExcludes'
    EnvIdentifierDto:
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      properties:
        env:
            $ref: '#/components/schemas/EnvIdentifier'
    EnvIdentifier:
          type: object
          x-oapi-codegen-extra-tags:
            validate: "dive"
          properties:
            type:
              type: string
              x-oapi-codegen-extra-tags:
                validate: "omitempty,oneof=prod non-prod"
              enum:
                - prod
                - non-prod
            includes:
              $ref: '#/components/schemas/NameIncludesExcludes'
            excludes:
              $ref: '#/components/schemas/NameIncludesExcludes'
    DeploymentTemplate:
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      required:
        - spec
      properties:
        spec:
          $ref: '#/components/schemas/DeploymentTemplateSpec'
    DeploymentTemplateSpec:
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      required:
        - operation
      properties:
        match:
          $ref: '#/components/schemas/DeploymentTemplateProperties'
        operation:
          $ref: '#/components/schemas/DeploymentTemplateOperation'
    DeploymentTemplateOperation:
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      required:
        - action
        - field
      properties:
        action:
          type: string
          enum: ["update"]
          x-oapi-codegen-extra-tags:
            validate: "oneof=update"
        field:
          type: string
          enum: ["values", "version"]
          x-oapi-codegen-extra-tags:
            validate: "omitempty,oneof=values version"
        patchJson:
          type: string
        chartVersion:
          type: string
          # [Semantic Versioning Regex pattern](https://semver.org/#is-there-a-suggested-regular-expression-regex-to-check-a-semver-string)
          pattern: "^(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)(?:-((?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\\.(?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\\+([0-9a-zA-Z-]+(?:\\.[0-9a-zA-Z-]+)*))?$"
    DeploymentTemplateProperties:
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      properties:
        include-base-config:
          type: boolean
          description: Indicates whether to include the base deployment template configuration.
        chart:
          $ref: '#/components/schemas/DeploymentChartProperties'
    DeploymentChartProperties:
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      properties:
        name:
          type: string
          x-go-type-skip-optional-pointer: false
        version:
          $ref: '#/components/schemas/ChartVersionProperties'
        custom:
          type: boolean
          x-go-type-skip-optional-pointer: false
    ChartVersionProperties:
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      properties:
        value:
          type: string
        operator:
          type: string
          enum: [ "EQUAL", "GREATER", "LESS", "GREATER_EQUAL", "LESS_EQUAL" ]
          x-oapi-codegen-extra-tags:
            validate: "omitempty,oneof=EQUAL GREATER LESS GREATER_EQUAL LESS_EQUAL"
    ConfigMap:
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      required:
        - spec
      properties:
        spec:
          $ref: '#/components/schemas/CmCsSpec'
    Secret:
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      required:
        - spec
      properties:
        spec:
          $ref: '#/components/schemas/CmCsSpec'
    CmCsSpec:
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      required:
        - operation
      properties:
        match:
          $ref: '#/components/schemas/CmCsProperties'
        operation:
          $ref: '#/components/schemas/CmCsOperation'
    CmCsProperties:
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      properties:
        include-base-config:
          type: boolean
          description: Indicates whether to include the base config map or secret configuration.
        includes:
          $ref: '#/components/schemas/NameIncludesExcludes'
        excludes:
          $ref: '#/components/schemas/NameIncludesExcludes'
    CmCsOperation:
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      required:
        - action
      properties:
        action:
          type: string
          enum: [ "create", "update", "delete" ]
          x-oapi-codegen-extra-tags:
            validate: "oneof=create update delete"
        field:
          type: string
          enum: [ "data" ]
          x-oapi-codegen-extra-tags:
            validate: "omitempty,oneof=data"
        patchJson:
          type: string
        value:
          type: string
    NameIncludesExcludes:
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      required:
        - names
      properties:
        names:
          x-oapi-codegen-extra-tags:
            validate: "dive,min=1"
          type: array
          items:
            type: string
            pattern: "^[a-z%]+[a-z0-9%\\-\\?]*[a-z0-9%]+$"
    ImpactedObjects:
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      properties:
        deploymentTemplate:
          type: array
          items:
            $ref: '#/components/schemas/ImpactedDeploymentTemplate'
        configMap:
          type: array
          items:
            $ref: '#/components/schemas/ImpactedCmCs'
        secret:
          type: array
          items:
            $ref: '#/components/schemas/ImpactedCmCs'
    ImpactedDeploymentTemplate:
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      required:
        - appName
      properties:
        appId:
          type: integer
          x-go-json-ignore: true # This field is not used in the response, an internal field
        envId:
          type: integer
          x-go-json-ignore: true # This field is not used in the response, an internal field
        appName:
          type: string
          x-oapi-codegen-extra-tags:
            validate: "required"
        envName:
          type: string
    ImpactedCmCs:
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      required:
        - appName
        - names
      properties:
        appId:
          type: integer
          x-go-json-ignore: true # This field is not used in the response, an internal field
        envId:
          type: integer
          x-go-json-ignore: true # This field is not used in the response, an internal field
        appName:
          type: string
          x-oapi-codegen-extra-tags:
            validate: "required"
        envName:
          type: string
        names:
          type: array
          x-oapi-codegen-extra-tags:
            validate: "dive,min=1"
          items:
            type: string
    BulkEditResponse:
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      properties:
        deploymentTemplate:
          $ref: '#/components/schemas/DeploymentTemplateResponse'
        configMap:
          $ref: '#/components/schemas/CmCsResponse'
        secret:
          $ref: '#/components/schemas/CmCsResponse'
    DeploymentTemplateResponse:
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      required:
        - message
        - failure
        - successful
      properties:
        message:
          type: array
          items:
            type: string
        failure:
          type: array
          items:
            $ref: '#/components/schemas/AppEnvDetail'
        successful:
          type: array
          items:
            $ref: '#/components/schemas/AppEnvDetail'
    CmCsResponse:
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      required:
        - message
        - failure
        - successful
      properties:
        message:
          type: array
          items:
            type: string
        failure:
          type: array
          items:
            $ref: '#/components/schemas/ObjectsWithAppEnvDetail'
        successful:
          type: array
          items:
            $ref: '#/components/schemas/ObjectsWithAppEnvDetail'
    AppEnvDetail:
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      required:
        - appName
        - message
      properties:
        appName:
          type: string
          x-oapi-codegen-extra-tags:
            validate: "required"
        envName:
          type: string
        message:
          type: string
          x-oapi-codegen-extra-tags:
            validate: "required"
    ObjectsWithAppEnvDetail:
      type: object
      x-oapi-codegen-extra-tags:
        validate: "dive"
      required:
        - appName
        - message
        - names
      properties:
        appName:
          type: string
          x-oapi-codegen-extra-tags:
            validate: "required"
        envName:
          type: string
        message:
          type: string
          x-oapi-codegen-extra-tags:
            validate: "required"
        names:
          type: array
          items:
            type: string
    ErrorResponse:
      type: object
      required:
        - code
        - status
      properties:
        code:
          type: integer
          format: int32
          description: Error code
        status:
          type: string
          description: Error message
        errors:
          type: array
          description: errors
          items:
            $ref: '#/components/schemas/ApiError'
    ApiError:
      type: object
      required:
        - code
        - status
      properties:
        code:
          type: integer
          format: int32
          description: Error internal code
        internalMessage:
          type: string
          description: Error internal message
        userMessage:
          type: string
          description: Error user message