# Bulk Edit - Application (batch/v1beta2)

This feature enables bulk editing of **Deployment Templates**, **ConfigMaps**, and **Secrets** across multiple applications based on rich filtering criteria. 
The new `v1beta2` version brings better separation of concerns, improved selectors, and more granular control over the edit operations.

## Key Improvements over v1beta1

* Explicit `selectors.match` structure with `project`, `app`, and `env` scopes.
* Chart version matching and chart-level filters in Deployment Templates.
* Bulk creation and deletion of ConfigMaps and Secrets.
* Bulk update Deployment Template version without affecting the existing values.

## Basic Example

This example applies bulk edits to applications with the following criteria:

- **Projects:**  
  Targets all applications in the `dev` project, but skips any in the `test` project.

- **Application Names:**  
  Includes apps ending with `-dashboard` or `-server`, but skips any with `demo-` or `test-` in their names.  
  (`%` is a wildcard for any sequence of characters.)

- **Environments:**  
  Only includes environments of type `non-prod` (`env.type = non-prod`).

**What will be updated:**

- **Deployment Templates:**  
  Updates the base Deployment Templates (and any environment-specific overrides) for the selected criteria.
  Sets the number of replicas to `4`.

- **ConfigMaps:**  
  Only affects ConfigMaps starting with `qa-cm-` or `prod-cm-`, and skips any that contain `dev` or `test` in their names.
  Updates the base ConfigMaps (and any environment-specific overrides) for the selected criteria.
  Adds a new key `FEAT_TEST_ENABLE` with value `true` and replaces the existing `LOG_LEVEL` with `-1`.  

- **Secrets:**  
  Only affects Secrets starting with `qa-secret-` or `prod-secret-`, and skips any that contain `dev` or `test` in their names.
  Updates the base Secrets (and any environment-specific overrides) for the selected criteria.
  Adds a new key `DB_PASSWORD` with value `********` and replaces the existing `ADMIN_PASSWORD` with `********`.  

```yaml
apiVersion: batch/v1beta2
kind: Application
spec:
  selectors:
    match:
      project:
        includes:
          names: ["dev"]
        excludes:
          names: ["test"]
      app:
        includes:
          names: ["%-dashboard", "%-server"]
        excludes:
          names: ["%demo-%", "%test-%"]
      env:
        type: non-prod
  deploymentTemplate:
    match:
      include-base-config: true
    operation:
      action: update
      field: values
      patchJson: '[{ "op": "replace", "path": "/replicas", "value": 4 }]'
  configMap:
    spec:
      match:
        include-base-config: true
        includes:
          names: ["qa-cm-%", "prod-cm-%"]
        excludes:
          names: ["%dev%", "%test%"]
      operation:
        action: update
        field: data
        patchJson: '[{ "op": "add", "path": "/FEAT_TEST_ENABLE}", "value": "true" },{"op": "replace","path":"/LOG_LEVEL","value": "-1"}]'
  secret:
    spec:
      match:
        include-base-config: true
        includes:
          names: ["qa-secret-%", "prod-secret-%"]
        excludes:
          names: ["%dev%", "%test%"]
        operation:
          action: update
          field: data
          patchJson: '[{ "op": "add", "path": "/DB_PASSWORD}", "value": "********" },{"op": "replace","path":"/ADMIN_PASSWORD","value": "********"}]'
```

## Field Reference

### Selectors

| Field                     | Description                                                  |
|---------------------------|--------------------------------------------------------------|
| `selectors.match`         | At least one of `project`, `app`, or `env` must be specified |
| `selectors.match.project` | Filter by project name (`includes` and/or `excludes`)        |
| `selectors.match.app`     | Filter by app name (`includes` and/or `excludes`)            |
| `selectors.match.env`     | Filter by environment type and name                          |

### Deployment Template

| Field                                          | Description                                                                                          |
|------------------------------------------------|------------------------------------------------------------------------------------------------------|
| `deploymentTemplate.match.include-base-config` | Whether to include base deployment configs                                                           |
| `deploymentTemplate.match.chart.name`          | Deployment chart type filter                                                                         |
| `deploymentTemplate.match.chart.version.value` | Deployment chart version filtering                                                                   |
| `deploymentTemplate.operation.action`          | Supported: `update`                                                                                  |
| `deploymentTemplate.operation.field`           | Supported: `values`, `version`. For `values`, use `patchJson` and for `version`, use `chartVersion`. |
| `deploymentTemplate.operation.patchJson`       | JSON Patch string to apply (if `field` is `values`)                                                  |
| `deploymentTemplate.operation.chartVersion`    | Chart version to set (if `field` is `version`)                                                       |

### ConfigMap / Secret

| Field                                             | Description                                                                                                                                                                                                                                                                                                                             |
|---------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| `configMap/secret.spec.match.include-base-config` | Whether to include base configs                                                                                                                                                                                                                                                                                                         |
| `configMap/secret.spec.match.includes.names`      | Names to include                                                                                                                                                                                                                                                                                                                        |
| `configMap/secret.spec.match.excludes.names`      | Names to exclude                                                                                                                                                                                                                                                                                                                        |
| `configMap/secret.spec.operation.action`          | Supported: `create`, `update`, `delete`. For `create`/ `delete`, use `value`. For `update`, use `patchJson`.                                                                                                                                                                                                                            |
| `configMap/secret.spec.operation.field`           | Supported: `data`. Requires `patchJson` is specified.                                                                                                                                                                                                                                                                                   |
| `configMap/secret.spec.operation.patchJson`       | JSON Patch string to apply. If `action` is `update`, this is required.                                                                                                                                                                                                                                                                  |
| `configMap/secret.spec.operation.value`           | For `action` is `create`, value is a JSON string representing the ConfigMap/Secret data (e.g: "{\\"name\\":\\"xyz\\",\\"type\\":\\"environment\\",\\"data\\":{\\"FEAT_TEST_ENABLE\\":\\"false\\",\\"LOG_LEVEL\\":\\"-1\\"},\\"mergeStrategy\\":\\"replace\\"}"). For `delete`, the value is the name of the ConfigMap/Secret to delete. |