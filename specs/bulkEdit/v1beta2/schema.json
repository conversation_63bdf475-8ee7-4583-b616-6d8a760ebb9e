{"$schema": "http://json-schema.org/draft-04/schema#", "title": "Bulk Edit Application batch/v1beta2 Schema", "type": "object", "description": "Input Script for bulk edit", "required": ["apiVersion", "kind", "spec"], "properties": {"apiVersion": {"type": "string", "enum": ["batch/v1beta2"], "description": "API version of the bulk edit schema"}, "kind": {"type": "string", "enum": ["application", "Application"], "description": "Resource kind, must be 'application' or 'Application'"}, "spec": {"type": "object", "required": ["selectors"], "description": "Specification for the bulk edit operation", "properties": {"selectors": {"$ref": "#/definitions/Selectors", "description": "Criteria to select target applications, projects, or environments"}, "deploymentTemplate": {"$ref": "#/definitions/DeploymentTemplate", "description": "Deployment template update specification"}, "configMap": {"$ref": "#/definitions/ConfigMap", "description": "ConfigMap update specification"}, "secret": {"$ref": "#/definitions/Secret", "description": "Secret update specification"}}, "anyOf": [{"required": ["deploymentTemplate"]}, {"required": ["configMap"]}, {"required": ["secret"]}], "additionalProperties": false}}, "additionalProperties": false, "definitions": {"Selectors": {"type": "object", "description": "Selectors to filter projects, apps, or environments", "properties": {"match": {"type": "object", "description": "Match criteria for selection", "properties": {"project": {"$ref": "#/definitions/ProjectSelector", "description": "Project selection criteria"}, "app": {"$ref": "#/definitions/AppSelector", "description": "Application selection criteria"}, "env": {"$ref": "#/definitions/EnvSelector", "description": "Environment selection criteria"}}, "anyOf": [{"required": ["project"]}, {"required": ["app"]}, {"required": ["env"]}]}}, "additionalProperties": false}, "ProjectSelector": {"type": "object", "description": "Selector for projects", "properties": {"includes": {"$ref": "#/definitions/NameIncludesExcludes", "description": "Projects to include"}, "excludes": {"$ref": "#/definitions/NameIncludesExcludes", "description": "Projects to exclude"}}, "anyOf": [{"required": ["includes"]}, {"required": ["excludes"]}]}, "AppSelector": {"type": "object", "description": "Selector for applications", "properties": {"includes": {"$ref": "#/definitions/NameIncludesExcludes", "description": "Applications to include"}, "excludes": {"$ref": "#/definitions/NameIncludesExcludes", "description": "Applications to exclude"}}, "anyOf": [{"required": ["includes"]}, {"required": ["excludes"]}]}, "EnvSelector": {"type": "object", "description": "Selector for environments", "properties": {"type": {"type": "string", "enum": ["prod", "non-prod"], "description": "Type of environment: production or non-production"}, "includes": {"$ref": "#/definitions/NameIncludesExcludes", "description": "Environments to include"}, "excludes": {"$ref": "#/definitions/NameIncludesExcludes", "description": "Environments to exclude"}}, "anyOf": [{"required": ["type"]}, {"required": ["includes"]}, {"required": ["excludes"]}]}, "DeploymentTemplate": {"type": "object", "required": ["spec"], "description": "Deployment template update specification", "properties": {"spec": {"type": "object", "required": ["operation"], "description": "Specification for deployment template operation", "properties": {"match": {"type": "object", "description": "Criteria to match deployment templates", "properties": {"include-base-config": {"type": "boolean", "description": "Whether to include base configuration"}, "chart": {"type": "object", "description": "Chart selection criteria", "properties": {"name": {"type": "string", "description": "Deployment chart name"}, "custom": {"type": "boolean", "description": "Whether the deployment chart is user uploaded (Not devtron managed)"}, "version": {"type": "object", "required": ["value", "operator"], "description": "Chart version criteria", "properties": {"value": {"type": "string", "pattern": "^(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)(?:-((?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\\.(?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\\+([0-9a-zA-Z-]+(?:\\.[0-9a-zA-Z-]+)*))?$", "description": "Chart version value (semver)"}, "operator": {"type": "string", "enum": ["EQUAL", "GREATER", "LESS", "GREATER_EQUAL", "LESS_EQUAL"], "description": "Operator for version comparison"}}}}}}}, "operation": {"type": "object", "required": ["action", "field"], "description": "Operation to perform on deployment template", "properties": {"action": {"type": "string", "enum": ["update"], "description": "Action to perform (only 'update' supported)"}, "field": {"type": "string", "enum": ["values", "version"], "description": "Field to update (values or version). For values, use patchJson; for version, use chartVersion"}, "patchJson": {"type": "string", "description": "JSON patch to apply"}, "chartVersion": {"type": "string", "pattern": "^(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)(?:-((?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\\.(?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\\+([0-9a-zA-Z-]+(?:\\.[0-9a-zA-Z-]+)*))?$", "description": "Chart version to update to"}}}}}}}, "ConfigMap": {"type": "object", "required": ["spec"], "description": "ConfigMap update specification", "properties": {"spec": {"type": "object", "required": ["operation"], "description": "Specification for ConfigMap operation", "properties": {"match": {"type": "object", "description": "Criteria to match ConfigMaps", "properties": {"include-base-config": {"type": "boolean", "description": "Whether to include base configuration"}, "includes": {"$ref": "#/definitions/NameIncludesExcludes", "description": "ConfigMaps to include"}, "excludes": {"$ref": "#/definitions/NameIncludesExcludes", "description": "ConfigMaps to exclude"}}}, "operation": {"type": "object", "required": ["action"], "description": "Operation to perform on ConfigMap", "properties": {"action": {"type": "string", "enum": ["create", "update", "delete"], "description": "Action to perform (create, update, delete). For update, use field and patchJson and for create/ delete, use value."}, "field": {"type": "string", "enum": ["data"], "description": "Field to update. Only 'data' is supported. Use patchJson for updates."}, "patchJson": {"type": "string", "description": "JSON patch to apply"}, "value": {"type": "string", "description": "Value to set. For action 'create', this should be a JSON string representing the ConfigMap data and for 'delete', it should be the name of the ConfigMap to delete."}}}}}}}, "Secret": {"type": "object", "required": ["spec"], "description": "Secret update specification", "properties": {"spec": {"type": "object", "required": ["operation"], "description": "Specification for Secret operation", "properties": {"match": {"type": "object", "description": "Criteria to match Secrets", "properties": {"include-base-config": {"type": "boolean", "description": "Whether to include base configuration"}, "includes": {"$ref": "#/definitions/NameIncludesExcludes", "description": "Secrets to include"}, "excludes": {"$ref": "#/definitions/NameIncludesExcludes", "description": "Secrets to exclude"}}}, "operation": {"type": "object", "required": ["action"], "description": "Operation to perform on Secret", "properties": {"action": {"type": "string", "enum": ["create", "update", "delete"], "description": "Action to perform (create, update, delete). For update, use field and patchJson; for create/delete, use value."}, "field": {"type": "string", "enum": ["data"], "description": "Field to update. Only 'data' is supported. Use patchJson for updates."}, "patchJson": {"type": "string", "description": "JSON patch to apply"}, "value": {"type": "string", "description": "Value to set. For action 'create', this should be a JSON string representing the Secret data and for 'delete', it should be the name of the Secret to delete."}}}}}}}, "NameIncludesExcludes": {"type": "object", "required": ["names"], "description": "Names to include or exclude", "properties": {"names": {"type": "array", "items": {"type": "string", "pattern": "^[a-z%]+[a-z0-9%\\-\\?]*[a-z0-9%]+$", "description": "Name pattern"}, "minItems": 1, "description": "List of names"}}, "additionalProperties": false}}}