apiVersion: batch/v1beta1
kind: Application
spec:
  includes:
    names:
      - "%abc%"
      - "%xyz%"
  excludes:
    names:
      - "%abcd%"
      - "%xyza%"
  envIds:
    - 23
  global: false
  deploymentTemplate:
    spec:
      patchJson: '[{ "op": "add", "path": "/MaxSurge", "value": 1 },{"op": "replace","path":"/GracePeriod","value": "30"}]'
  configMap:
    spec:
      names:
        - "configmap1"
        - "configmap2"
        - "configmap3"
      patchJson: '[{ "op": "add", "path": "/{key}", "value": "{value}" },{"op": "replace","path":"/{key}","value": "{value}"}]'
  secret:
    spec:
      names:
        - "secret1"
        - "secret2"
      patchJson: '[{ "op": "add", "path": "/{key}", "value": "{value}" },{"op": "replace","path":"/{key}","value": "{value}"}]'