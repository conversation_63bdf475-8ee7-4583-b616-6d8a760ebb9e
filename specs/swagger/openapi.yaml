openapi: 3.0.0
info:
  version: 1.0.0
  title: Common Devtron automation APIs
servers:
  - url: http://localhost/orchestrator
    description: Local development server
tags:
  - name: Labels
    x-displayName: Labels
  - name: bulk_other
    x-displayName: BulkGetData
  - name: BulkUpdate
    x-displayName: BulkUpdate
  - name: SSO Configuration
    description: Manage Single Sign-On (SSO) provider configurations.
    x-displayName: SSO Configuration
  - name: User Management
    description: Operations related to user accounts (CRUD, listing, bulk actions).
    x-displayName: User Management
  - name: Role Group Management
    description: Operations related to user role groups (CRUD, listing, bulk actions).
    x-displayName: Role Group Management
  - name: RBAC
    description: >-
      Operations related to Role-Based Access Control, like fetching default
      roles.
    x-displayName: RBAC
  - name: Authentication
    description: >-
      Core authentication endpoints including login, token refresh, and auth
      verification.
    x-displayName: Authentication
  - name: Policy Management
    description: Endpoints for managing policies.
    x-displayName: Policy Management
  - name: Cache Management
    description: Endpoints for managing authentication and authorization caches.
    x-displayName: Cache Management
  - name: Cluster Environment
    description: Operations related to clusters and environments
    x-displayName: Cluster Environment
  - name: Cluster Management
    description: Operations related to cluster creation, update, and validation
    x-displayName: Cluster Management
  - name: Environment Management
    description: Operations for creating, updating, and deleting environments
    x-displayName: Environment Management
  - name: Change Chart
    x-displayName: ChangeChartType
  - name: Clone Workflow
    x-displayName: CloneWorkflow (ENT)
  - name: K8s Resource
    description: >-
      APIs for managing Kubernetes resources (get, create, update, delete,
      list).
    x-displayName: K8s Resource
  - name: Workflow Management
    x-displayName: Workflow Management
paths:
  /app/labels/list:
    get:
      summary: List Application Labels
      description: |
        Retrieves a list of application labels. By default, returns all labels. 
        Use the `showPropagatedOnly` parameter to filter for labels where propagate = true.
        
        **Required Token Permission:**
        - Must have **View** access to the applications in scope.
      operationId: getAppLabels
      tags:
        - Labels
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      parameters:
        - name: showPropagatedOnly
          in: query
          description: |
            If true, only returns labels where propagate = true. 
            If false or not provided, all labels are returned.
          required: false
          schema:
            type: boolean
            default: false
          example: false
      responses:
        '200':
          description: Successfully retrieved labels list
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: HTTP status code
                    example: 200
                  status:
                    type: string
                    description: Response status message
                    example: "OK"
                  result:
                    type: array
                    description: Array of label objects
                    items:
                      type: object
                      required:
                        - key
                        - value
                        - propagate
                        - appId
                        - appName
                      properties:
                        key:
                          type: string
                          description: The label key/name
                          example: "environment"
                        value:
                          type: string
                          description: The label value
                          example: "production"
                        propagate:
                          type: boolean
                          description: Whether this label should be propagated
                          example: true
                        appId:
                          type: integer
                          description: The unique identifier of the application
                          example: 1234
                        appName:
                          type: string
                          description: The name of the application
                          example: "web-service"
              examples:
                all_labels:
                  summary: All labels response
                  description: Example response when showPropagatedOnly is false or not provided
                  value:
                    code: 200
                    status: "OK"
                    result:
                      - key: "environment"
                        value: "production"
                        propagate: true
                        appId: 1234
                        appName: "web-service"
                      - key: "team"
                        value: "backend"
                        propagate: false
                        appId: 1234
                        appName: "web-service"
                propagated_only:
                  summary: Propagated labels only
                  description: Example response when showPropagatedOnly is true
                  value:
                    code: 200
                    status: "OK"
                    result:
                      - key: "environment"
                        value: "production"
                        propagate: true
                        appId: 1234
                        appName: "web-service"
        '401':
          description: Authentication required or token invalid
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 401
                  status:
                    type: string
                    example: "Unauthorized"
                  message:
                    type: string
                    example: "Authentication token is required"
        '403':
          description: Insufficient permissions to access the resource
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 403
                  status:
                    type: string
                    example: "Forbidden"
                  message:
                    type: string
                    example: "Token does not have View access to the applications in scope"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 500
                  status:
                    type: string
                    example: "Internal Server Error"
                  message:
                    type: string
                    example: "An unexpected error occurred"
  /batch/{apiVersion}/{kind}/readme:
    get:
      summary: Get Readme for Bulk Update
      description: Returns Readme for bulk update for different resource in the url
      operationId: FindBulkUpdateReadme
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      parameters:
        - name: apiVersion
          in: path
          required: true
          schema:
            type: string
        - name: kind
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful GET operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkUpdateSeeExampleResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      tags:
        - bulk_other
  /batch/v1beta1/application/dryrun:
    post:
      summary: Dry Run for Bulk Application Update
      description: >-
        Returns details(id, name, envId) of all apps to be impacted with bulk
        update
      operationId: GetBulkAppName
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      requestBody:
        description: A JSON object containing information by which apps will be filtered
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkUpdateScript'
      responses:
        '200':
          description: Successfully return all impacted app details.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ImpactedObjectsResponse'
        '400':
          description: Bad Request. Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      tags:
        - bulk_other
  /batch/v1beta1/hibernate:
    post:
      summary: Bulk Hibernate Applications
      description: Bulk Hibernates applications
      operationId: BulkHibernate
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      tags:
        - BulkUpdate
      requestBody:
        description: >-
          A JSON object containing information about applications and
          environments to hibernate.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkActionRequest'
      responses:
        '200':
          description: Successfully hibernated applications.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkActionResponse'
        '400':
          description: Bad Request. Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /batch/v1beta1/unhibernate:
    post:
      summary: Bulk Un-Hibernate Applications
      description: Bulk Un-Hibernates applications
      operationId: BulkUnHibernate
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      tags:
        - BulkUpdate
      requestBody:
        description: >-
          A JSON object containing information about applications and
          environments to un-hibernate.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkActionRequest'
      responses:
        '200':
          description: Successfully un-hibernated applications.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkActionResponse'
        '400':
          description: Bad Request. Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /batch/v1beta1/deploy:
    post:
      summary: Bulk Deploy Applications
      description: Bulk Triggers deployment of applications
      operationId: BulkDeploy
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      tags:
        - BulkUpdate
      requestBody:
        description: A JSON object containing information for bulk deployment.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkDeployRequest'
      responses:
        '200':
          description: Successfully triggered bulk deployment.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkActionResponse'
        '400':
          description: Bad Request. Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /batch/v1beta1/build:
    post:
      summary: Bulk Trigger Application Builds
      description: Bulk Triggers build of applications
      operationId: BulkBuildTrigger
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      tags:
        - BulkUpdate
      requestBody:
        description: A JSON object containing information for bulk build trigger.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkBuildTriggerRequest'
      responses:
        '200':
          description: Successfully triggered bulk build.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkActionResponse'
        '400':
          description: Bad Request. Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /batch/v1beta1/application:
    post:
      summary: Bulk Edit Applications
      description: >-
        Bulk Updates (Edit) all impacted apps. This endpoint can be used for
        bulk editing application configurations like deployment templates,
        configmaps, and secrets.
      operationId: BulkUpdate
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      tags:
        - BulkUpdate
      requestBody:
        description: >-
          A JSON object containing information about update changes and by which
          apps will be filtered for bulk editing.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkUpdateScript'
      responses:
        '200':
          description: Successfully updated all impacted apps.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkUpdateResponse'
        '400':
          description: Bad Request. Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /sso/create:
    post:
      tags:
        - SSO Configuration
      summary: Create SSO Login Configuration
      operationId: CreateSSOLoginConfig
      requestBody:
        $ref: '#/components/requestBodies/SSOLoginDto'
      responses:
        '200':
          $ref: '#/components/responses/SSOLoginConfigResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /sso/update:
    put:
      tags:
        - SSO Configuration
      summary: Update SSO Login Configuration
      operationId: UpdateSSOLoginConfig
      requestBody:
        $ref: '#/components/requestBodies/SSOLoginDto'
      responses:
        '200':
          $ref: '#/components/responses/SSOLoginConfigResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /sso/list:
    get:
      tags:
        - SSO Configuration
      summary: Get All SSO Login Configurations
      operationId: GetAllSSOLoginConfig
      responses:
        '200':
          description: List of SSO configurations.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SSOLoginDto'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /sso/{id}:
    get:
      tags:
        - SSO Configuration
      summary: Get SSO Login Configuration by ID
      operationId: GetSSOLoginConfig
      parameters:
        - $ref: '#/components/parameters/PathId'
      responses:
        '200':
          $ref: '#/components/responses/SSOLoginConfigResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /sso:
    get:
      tags:
        - SSO Configuration
      summary: Get SSO Login Configuration by Name
      operationId: GetSSOLoginConfigByName
      parameters:
        - name: name
          in: query
          required: true
          description: Name of the SSO configuration.
          schema:
            type: string
      responses:
        '200':
          $ref: '#/components/responses/SSOLoginConfigResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /rbac/roles/default:
    get:
      tags:
        - RBAC
      summary: Get All Default Roles
      operationId: GetAllDefaultRoles
      responses:
        '200':
          description: List of default RBAC roles.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RbacRoleDto'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /api/v1/session:
    post:
      tags:
        - Authentication
      summary: User Login
      description: Authenticates a user and returns a session token.
      operationId: LoginHandler
      requestBody:
        description: Username and password for login.
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                username:
                  type: string
                  example: admin
                password:
                  type: string
                  format: password
                  example: password123
              required:
                - username
                - password
      responses:
        '200':
          description: Login successful, token returned.
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
          headers:
            Set-Cookie:
              description: Sets the argocd.token cookie.
              schema:
                type: string
                example: argocd.token=yourtokenvalue; Path=/
        '400':
          $ref: '#/components/responses/BadRequest'
        '403':
          description: Invalid username or password.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /refresh:
    get:
      tags:
        - Authentication
      summary: Refresh Session Token
      operationId: RefreshTokenHandler
      description: Handles token refresh (details depend on OIDC/OAuth flow).
      responses:
        '200':
          description: Token refreshed successfully.
        '401':
          $ref: '#/components/responses/Unauthorized'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /admin/policy/default:
    post:
      tags:
        - Policy Management
      summary: Add Default Policy and Roles
      operationId: AddDefaultPolicyAndRoles
      description: >-
        Creates default policies and roles based on team, app, and environment.
        This is a specialized endpoint.
      parameters:
        - name: team
          in: query
          required: true
          schema:
            type: string
        - name: app
          in: query
          required: true
          schema:
            type: string
        - name: env
          in: query
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Default policies and roles added successfully.
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /devtron/auth/verify:
    get:
      tags:
        - Authentication
      summary: Verify Authentication Status
      operationId: AuthVerification
      responses:
        '200':
          description: Authentication status.
          content:
            application/json:
              schema:
                type: boolean
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /devtron/auth/verify/v2:
    get:
      tags:
        - Authentication
      summary: Verify Authentication Status (V2)
      operationId: AuthVerificationV2
      responses:
        '200':
          description: Detailed authentication status including super admin flag.
          content:
            application/json:
              schema:
                type: object
                properties:
                  isSuperAdmin:
                    type: boolean
                  isVerified:
                    type: boolean
                  emailId:
                    type: string
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /user/v2:
    get:
      tags:
        - User Management
      summary: List Users (V2 - Paginated, Filtered)
      operationId: GetAllUsersV2
      parameters:
        - $ref: '#/components/parameters/SearchKeyQuery'
        - $ref: '#/components/parameters/SortOrderQuery'
        - $ref: '#/components/parameters/SortByQueryUser'
        - $ref: '#/components/parameters/OffsetQuery'
        - $ref: '#/components/parameters/SizeQuery'
      responses:
        '200':
          $ref: '#/components/responses/UserListingResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
    post:
      tags:
        - User Management
      summary: Create User (V2)
      operationId: CreateUserV2
      requestBody:
        $ref: '#/components/requestBodies/UserInfo'
      responses:
        '200':
          $ref: '#/components/responses/UserInfoResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '406':
          $ref: '#/components/responses/NotAcceptable'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
    put:
      tags:
        - User Management
      summary: Update User (V2)
      operationId: UpdateUserV2
      requestBody:
        $ref: '#/components/requestBodies/UserInfo'
      responses:
        '200':
          $ref: '#/components/responses/UserInfoResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '406':
          $ref: '#/components/responses/NotAcceptable'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /user:
    get:
      tags:
        - User Management
      summary: List All Users (V1)
      operationId: GetAllUsers
      responses:
        '200':
          description: List of all users.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserInfo'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
    post:
      tags:
        - User Management
      summary: Create User
      operationId: CreateUser
      requestBody:
        $ref: '#/components/requestBodies/UserInfo'
      responses:
        '200':
          $ref: '#/components/responses/UserInfoResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '406':
          $ref: '#/components/responses/NotAcceptable'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
    put:
      tags:
        - User Management
      summary: Update User
      operationId: UpdateUser
      requestBody:
        $ref: '#/components/requestBodies/UserInfo'
      responses:
        '200':
          $ref: '#/components/responses/UserInfoResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '406':
          $ref: '#/components/responses/NotAcceptable'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /user/{id}:
    get:
      tags:
        - User Management
      summary: Get User by ID
      operationId: GetUserById
      parameters:
        - $ref: '#/components/parameters/PathId'
      responses:
        '200':
          $ref: '#/components/responses/UserInfoResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
    delete:
      tags:
        - User Management
      summary: Delete User by ID
      operationId: DeleteUser
      parameters:
        - $ref: '#/components/parameters/PathId'
      responses:
        '200':
          $ref: '#/components/responses/GenericSuccess'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /user/v2/{id}:
    get:
      tags:
        - User Management
      summary: Get User by ID (V2)
      operationId: GetUserByIdV2
      parameters:
        - $ref: '#/components/parameters/PathId'
      responses:
        '200':
          $ref: '#/components/responses/UserInfoResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /user/bulk:
    delete:
      tags:
        - User Management
      summary: Bulk Delete Users
      operationId: BulkDeleteUsers
      responses:
        '200':
          $ref: '#/components/responses/GenericSuccess'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /user/detail/get:
    get:
      tags:
        - User Management
      summary: List All Detailed Users
      operationId: GetAllDetailedUsers
      responses:
        '200':
          description: List of all users with detailed information.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserInfo'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /user/role/group/v2:
    get:
      tags:
        - Role Group Management
      summary: List Role Groups (V2 - Paginated, Filtered)
      operationId: FetchRoleGroupsV2
      parameters:
        - $ref: '#/components/parameters/SearchKeyQuery'
        - $ref: '#/components/parameters/SortOrderQuery'
        - $ref: '#/components/parameters/SortByQueryRoleGroup'
        - $ref: '#/components/parameters/OffsetQuery'
        - $ref: '#/components/parameters/SizeQuery'
      responses:
        '200':
          $ref: '#/components/responses/RoleGroupListingResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
    post:
      tags:
        - Role Group Management
      summary: Create Role Group (V2)
      operationId: CreateRoleGroupV2
      requestBody:
        $ref: '#/components/requestBodies/RoleGroup'
      responses:
        '200':
          $ref: '#/components/responses/RoleGroupResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
    put:
      tags:
        - Role Group Management
      summary: Update Role Group (V2)
      operationId: UpdateRoleGroupV2
      requestBody:
        $ref: '#/components/requestBodies/RoleGroup'
      responses:
        '200':
          $ref: '#/components/responses/RoleGroupResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /user/role/group:
    get:
      tags:
        - Role Group Management
      summary: List All Role Groups (V1)
      operationId: FetchRoleGroups
      responses:
        '200':
          description: List of all role groups.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleGroup'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
    post:
      tags:
        - Role Group Management
      summary: Create Role Group
      operationId: CreateRoleGroup
      requestBody:
        $ref: '#/components/requestBodies/RoleGroup'
      responses:
        '200':
          $ref: '#/components/responses/RoleGroupResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
    put:
      tags:
        - Role Group Management
      summary: Update Role Group
      operationId: UpdateRoleGroup
      requestBody:
        $ref: '#/components/requestBodies/RoleGroup'
      responses:
        '200':
          $ref: '#/components/responses/RoleGroupResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /user/role/group/{id}:
    get:
      tags:
        - Role Group Management
      summary: Get Role Group by ID
      operationId: FetchRoleGroupById
      parameters:
        - $ref: '#/components/parameters/PathId'
      responses:
        '200':
          $ref: '#/components/responses/RoleGroupResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
    delete:
      tags:
        - Role Group Management
      summary: Delete Role Group by ID
      operationId: DeleteRoleGroup
      parameters:
        - $ref: '#/components/parameters/PathId'
      responses:
        '200':
          $ref: '#/components/responses/GenericSuccess'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /user/role/group/v2/{id}:
    get:
      tags:
        - Role Group Management
      summary: Get Role Group by ID (V2)
      operationId: FetchRoleGroupByIdV2
      parameters:
        - $ref: '#/components/parameters/PathId'
      responses:
        '200':
          $ref: '#/components/responses/RoleGroupResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /user/role/group/detailed/get:
    get:
      tags:
        - Role Group Management
      summary: List All Detailed Role Groups
      operationId: FetchDetailedRoleGroups
      responses:
        '200':
          $ref: '#/components/responses/RoleGroupListingResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /user/role/group/search:
    get:
      tags:
        - Role Group Management
      summary: Search Role Groups by Name
      operationId: FetchRoleGroupsByName
      parameters:
        - name: name
          in: query
          required: true
          description: Name of the role group to search for.
          schema:
            type: string
      responses:
        '200':
          $ref: '#/components/responses/RoleGroupResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /user/role/group/bulk:
    delete:
      tags:
        - Role Group Management
      summary: Bulk Delete Role Groups
      operationId: BulkDeleteRoleGroups
      responses:
        '200':
          $ref: '#/components/responses/GenericSuccess'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /user/check/roles:
    get:
      tags:
        - Authentication
      summary: Check Logged-in User's Roles
      operationId: CheckUserRoles
      parameters:
        - name: appName
          in: query
          required: false
          description: Optional application name to filter roles for.
          schema:
            type: string
      responses:
        '200':
          description: User roles information.
          content:
            application/json:
              schema:
                type: object
                properties:
                  role:
                    type: string
                    nullable: true
                  roles:
                    type: array
                    items:
                      type: string
                  superAdmin:
                    type: boolean
                    nullable: true
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /user/sync/orchestratortocasbin:
    get:
      tags:
        - Policy Management
      summary: Sync Orchestrator to Casbin
      operationId: SyncOrchestratorToCasbin
      description: >-
        Synchronizes policies from orchestrator to Casbin. Requires admin
        privileges.
      responses:
        '200':
          description: Sync status.
          content:
            application/json:
              schema:
                type: boolean
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /user/update/trigger/terminal:
    put:
      tags:
        - Policy Management
      summary: Update Trigger Policy for Terminal Access
      operationId: UpdateTriggerPolicyForTerminalAccess
      description: >-
        Updates trigger policies related to terminal access. Requires global
        update privileges.
      responses:
        '200':
          description: Policy update status.
          content:
            application/json:
              schema:
                type: string
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /user/role/cache:
    get:
      tags:
        - Cache Management
      summary: Get Role Cache Dump
      operationId: GetRoleCacheDump
      description: Retrieves a dump of the role cache. Requires super admin privileges.
      responses:
        '200':
          description: Cache dump data.
          content:
            application/json:
              schema:
                type: object
        '403':
          $ref: '#/components/responses/Forbidden'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /user/role/cache/invalidate:
    get:
      tags:
        - Cache Management
      summary: Invalidate Role Cache
      operationId: InvalidateRoleCache
      description: Clears the role cache. Requires super admin privileges.
      responses:
        '200':
          description: Cache invalidation status.
          content:
            application/json:
              schema:
                type: string
        '403':
          $ref: '#/components/responses/Forbidden'
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /login:
    get:
      tags:
        - Authentication
      summary: OIDC Login Redirect (Informational)
      description: Initiates OIDC login flow. Handled by the OIDC client library.
      operationId: HandleOIDCLogin
      responses:
        '302':
          description: Redirect to OIDC provider.
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /auth/login:
    get:
      tags:
        - Authentication
      summary: OIDC Login Redirect (Informational - Alternate Path)
      description: Initiates OIDC login flow. Handled by the OIDC client library.
      operationId: HandleOIDCAuthLogin
      responses:
        '302':
          description: Redirect to OIDC provider.
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /auth/callback:
    get:
      tags:
        - Authentication
      summary: OIDC Callback (Informational)
      description: >-
        Handles the callback from the OIDC provider. Handled by the OIDC client
        library.
      operationId: HandleOIDCCallback
      responses:
        '302':
          description: Redirect after successful authentication.
        '400':
          description: Error during OIDC callback.
        '500':
          description: Server error during OIDC callback.
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /api/dex/{path}:
    get:
      tags:
        - Authentication
      summary: Dex Proxy (Informational)
      description: Proxies requests to the Dex IdP. Handled by the Dex proxy mechanism.
      operationId: DexProxyHandler
      parameters:
        - name: path
          in: path
          required: true
          description: Path to be proxied to Dex.
          schema:
            type: string
      responses:
        default:
          description: Response from Dex.
      security:
        - bearerAuth: []
        - apiKeyAuth: []
  /orchestrator/env:
    post:
      summary: Create Environment
      description: Create a new environment within a cluster.
      operationId: CreateEnvironment
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      tags:
        - Environment Management
      requestBody:
        description: Environment details
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EnvironmentCreateRequest'
      responses:
        '200':
          description: Successfully created environment
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnvironmentDetail'
        '400':
          description: Bad Request (e.g., validation error)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    put:
      summary: Update Environment
      description: Update an existing environment.
      operationId: UpdateEnvironment
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      tags:
        - Environment Management
      requestBody:
        description: Environment details to update
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EnvironmentUpdateRequest'
      responses:
        '200':
          description: Successfully updated environment
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnvironmentDetail'
        '400':
          description: Bad Request (e.g., validation error, or ID not found)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    get:
      summary: Get Environment by ID
      description: Get detailed information for a specific environment by its ID.
      operationId: GetEnvironmentById
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      tags:
        - Environment Management
      parameters:
        - name: id
          in: query
          required: true
          description: ID of the environment
          schema:
            type: integer
      responses:
        '200':
          description: Successfully retrieved environment details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnvironmentDetail'
        '400':
          description: Bad Request (e.g., invalid ID)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Environment not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /orchestrator/cluster/delete:
    post:
      summary: Delete Cluster
      description: Delete an existing cluster.
      operationId: DeleteCluster
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      tags:
        - Cluster Management
      requestBody:
        description: >-
          A JSON object containing the cluster config (primarily ID is used for
          deletion)
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ClusterBean'
      responses:
        '200':
          description: Successfully deleted the cluster
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Cluster deleted successfully.
        '400':
          description: Bad Request. Input Validation(decode) error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /orchestrator/env/delete:
    post:
      summary: Delete Environment (via POST)
      description: Delete an existing environment using POST method.
      operationId: DeleteEnvironmentViaPost
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      tags:
        - Environment Management
      requestBody:
        description: >-
          A JSON object containing the env config (primarily ID is used for
          deletion)
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EnvironmentDetail'
      responses:
        '200':
          description: Successfully deleted the environment
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Environment deleted successfully.
        '400':
          description: Bad Request. Input Validation(decode) error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /orchestrator/env/clusters:
    get:
      summary: List clusters with their environments
      description: Provides a list of all clusters and the environments within each.
      operationId: getClustersWithEnvironments
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      tags:
        - Cluster Environment
      responses:
        '200':
          description: Successfully retrieved list of clusters and environments
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ClusterWithEnvironments'
        '401':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /orchestrator/cluster/{cluster_id}/env:
    get:
      summary: List environments for a specific cluster
      description: Provides a list of all environments for a given cluster ID.
      operationId: getEnvironmentsForCluster
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      tags:
        - Cluster Environment
      parameters:
        - name: cluster_id
          in: path
          required: true
          description: ID of the cluster
          schema:
            type: integer
      responses:
        '200':
          description: Successfully retrieved list of environments
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Environment'
        '400':
          description: Bad Request. Invalid cluster ID.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Cluster not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /orchestrator/cluster:
    put:
      summary: Update Cluster
      description: Update an existing cluster's configuration.
      operationId: UpdateCluster
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      tags:
        - Cluster Management
      requestBody:
        description: A JSON object containing the cluster config
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ClusterBean'
      responses:
        '200':
          description: Successfully updated the cluster
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClusterBean'
        '400':
          description: Bad Request. Input Validation(decode) error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    get:
      summary: Get Cluster
      description: Get details of a specific cluster by ID.
      operationId: GetCluster
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      tags:
        - Cluster Management
      parameters:
        - name: id
          in: query
          description: cluster id.
          required: true
          schema:
            type: integer
        - name: clusterId
          in: query
          description: comma-separated list of cluster IDs to filter clusters. If not provided, returns all clusters.
          required: false
          schema:
            type: string
            example: "1,2,3"
      responses:
        '200':
          description: Successfully get cluster(s)
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/ClusterBean'
                  - type: array
                    items:
                      $ref: '#/components/schemas/ClusterBean'
        '400':
          description: Bad Request. Input Validation(decode) error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /orchestrator/cluster/auth-list:
    get:
      summary: List Accessible Clusters
      description: list of clusters accessible to the authenticated user.
      operationId: GetAuthClusterList
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      tags:
        - Cluster Management
      responses:
        '200':
          description: cluster list
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: array
                    description: namespace list group by cluster
                    items:
                      $ref: '#/components/schemas/ClusterAuthDetail'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /orchestrator/cluster/validate:
    post:
      summary: Validate Cluster Configuration
      description: Validate a cluster configuration using kubeconfig.
      operationId: ValidateCluster
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      tags:
        - Cluster Management
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                kubeconfig:
                  $ref: '#/components/schemas/Kubeconfig'
              required:
                - kubeconfig
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidateClusterBean'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /orchestrator/cluster/saveClusters:
    post:
      summary: Save Multiple Clusters
      description: Save configurations for multiple clusters.
      operationId: SaveClusters
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      tags:
        - Cluster Management
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/ValidateClusterBean'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ValidateClusterBean'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /orchestrator/app/env/patch:
    patch:
      summary: Patch Application Environment
      description: change the deployment template for an app and environment
      operationId: PatchAppEnv
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      requestBody:
        content:
          application/json:
            schema:
              properties:
                envId:
                  type: integer
                appId:
                  type: integer
                targetChartRefId:
                  type: integer
      responses:
        '200':
          description: patched data
        '422':
          description: bad request
      tags:
        - Change Chart
  /orchestrator/app/workflow/clone:
    post:
      summary: Clone Application Workflow
      description: >-
        Clones an application workflow from a source environment to a target
        environment
      operationId: CloneApplicationWorkflow
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      requestBody:
        description: A JSON object containing the details required to clone the workflow
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CloneApplicationWorkflowRequest'
      responses:
        '200':
          description: Cloning operation response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StandardResponse'
      tags:
        - Clone Workflow
  /orchestrator/k8s/resource:
    post:
      summary: Get Resource Manifest
      description: >-
        This API is used for fetching the manifest of a specified Kubernetes
        resource.
      operationId: getResourceManifest
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      requestBody:
        description: JSON payload specifying the resource to fetch.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceRequestObject'
      responses:
        '200':
          description: Successfully retrieved the resource manifest.
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: Status code of the response.
                  status:
                    type: string
                    description: Status message of the response.
                  result:
                    $ref: '#/components/schemas/ResourceGetResponse'
      tags:
        - K8s Resource
    put:
      summary: Update Resource Manifest
      description: >-
        This API is used for editing the manifest of a specified Kubernetes
        resource.
      operationId: updateResourceManifest
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      requestBody:
        description: JSON payload containing the resource identifier and the patch.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceRequestObject'
      responses:
        '200':
          description: Successfully updated the resource manifest.
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: Status code of the response.
                  status:
                    type: string
                    description: Status message of the response.
                  result:
                    $ref: '#/components/schemas/ResourceGetResponse'
      tags:
        - K8s Resource
  /orchestrator/k8s/resource/create:
    post:
      summary: Create Resource
      description: >-
        This API is used for applying a desired manifest to create a Kubernetes
        resource.
      operationId: createResource
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      requestBody:
        description: JSON payload containing the resource manifest to apply.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceRequestObject'
      responses:
        '200':
          description: Successfully created the resource.
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: Status code of the response.
                  status:
                    type: string
                    description: Status message of the response.
                  result:
                    $ref: '#/components/schemas/ResourceGetResponse'
      tags:
        - K8s Resource
  /orchestrator/k8s/resource/delete:
    post:
      summary: Delete Resource
      description: This API is used for deleting a specified Kubernetes resource.
      operationId: deleteResource
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      requestBody:
        description: JSON payload specifying the resource to delete.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceRequestObject'
      responses:
        '200':
          description: Successfully deleted the resource.
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: Status code of the response.
                  status:
                    type: string
                    description: Status message of the response.
                  result:
                    $ref: '#/components/schemas/ResourceGetResponse'
      tags:
        - K8s Resource
  /orchestrator/k8s/events:
    post:
      summary: Get Resource Events
      description: This API is used for fetching events for Kubernetes resources.
      operationId: getResourceEvents
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      requestBody:
        required: false
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceRequestObject'
      responses:
        '200':
          description: >-
            Successfully retrieved resource events. The response is a stream of
            events.
          content:
            text/event-stream:
              schema:
                $ref: '#/components/schemas/EventsResponseObject'
      tags:
        - K8s Resource
  /orchestrator/k8s/pods/logs/{podName}:
    get:
      summary: Get Pod Logs
      description: >-
        This API is used for fetching logs for a specified container within a
        pod.
      operationId: getPodLogs
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      parameters:
        - name: podName
          in: path
          required: true
          description: Name of the pod.
          schema:
            type: string
        - name: containerName
          in: query
          required: true
          description: Name of the container within the pod.
          schema:
            type: string
        - name: appId
          in: query
          required: false
          description: Application ID.
          schema:
            type: string
        - name: clusterId
          in: query
          required: false
          description: Cluster ID.
          schema:
            type: integer
        - name: namespace
          in: query
          description: Namespace of the pod. Required if clusterId is passed.
          required: false
          schema:
            type: string
        - name: follow
          in: query
          description: Whether to follow the log stream.
          schema:
            type: boolean
            default: false
        - name: sinceSeconds
          in: query
          description: >-
            Return logs newer than a relative duration like 5s, 2m, or 3h.
            Defaults to all logs.
          schema:
            type: integer
        - name: tailLines
          in: query
          description: If set, the number of lines from the end of the logs to show.
          schema:
            type: integer
      responses:
        '200':
          description: >-
            Successfully retrieved pod logs. The response is a stream of log
            lines.
          content:
            text/event-stream:
              schema:
                $ref: '#/components/schemas/LogsResponseObject'
      tags:
        - K8s Resource
  /orchestrator/k8s/pod/exec/session/{identifier}/{namespace}/{pod}/{shell}/{container}:
    get:
      summary: Get Pod Exec Session
      description: >-
        This API establishes a session for executing commands in a pod's
        container (terminal access).
      operationId: getPodExecSession
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      parameters:
        - name: identifier
          in: path
          required: true
          description: Application ID or Cluster ID. Example '2|devtroncd|devtron' or '3'.
          schema:
            type: string
        - name: namespace
          in: path
          required: true
          description: Namespace of the pod.
          schema:
            type: string
            example: devtroncd
        - name: pod
          in: path
          required: true
          description: Name of the pod.
          schema:
            type: string
            example: inception-58d44d99fd-tfw4s
        - name: shell
          in: path
          required: true
          description: Shell to invoke.
          schema:
            type: string
            enum:
              - bash
              - sh
              - powershell
              - cmd
            example: bash
        - name: container
          in: path
          required: true
          description: Name of the container.
          schema:
            type: string
            example: devtron
      responses:
        '200':
          description: Successfully established exec session.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TerminalMessage'
      tags:
        - K8s Resource
  /orchestrator/k8s/api-resources/{clusterId}:
    get:
      summary: Get API Resources
      description: Get all available API resources for a given cluster ID.
      operationId: getApiResources
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      parameters:
        - name: clusterId
          in: path
          description: ID of the cluster.
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Successfully fetched all API resources for the given cluster ID.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllApiResourcesResponse'
      tags:
        - K8s Resource
  /orchestrator/k8s/resource/list:
    post:
      summary: List Resources
      description: >-
        This API is used for fetching a list of Kubernetes resources based on
        the request criteria.
      operationId: listResources
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      requestBody:
        description: JSON payload specifying the criteria for listing resources.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceRequestObject'
      responses:
        '200':
          description: Successfully retrieved the list of resources.
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: Status code of the response.
                  status:
                    type: string
                    description: Status message of the response.
                  result:
                    type: array
                    description: List of resources.
                    items:
                      $ref: '#/components/schemas/ClusterResourceListResponse'
      tags:
        - K8s Resource
  /orchestrator/k8s/resources/rotate:
    post:
      summary: Rotates the Pods
      description: This API is used to rotate (restart) pods for the provided resources.
      operationId: K8sResourceForResources
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      parameters:
        - name: appId
          in: query
          description: Application ID.
          required: true
          schema:
            type: string
      requestBody:
        description: >-
          JSON payload specifying the resources for which pods should be
          rotated.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RotatePodRequest'
      responses:
        '200':
          description: Response array indicating the status of rotation for each resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RotatePodResponse'
      tags:
        - K8s Resource
  /orchestrator/k8s/resources/apply:
    post:
      summary: Apply Resources
      description: >-
        This API is used to apply (create or update) Kubernetes resources in a
        cluster.
      operationId: applyResources
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      requestBody:
        description: JSON payload containing the manifest of resources to apply.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ApplyResourcesRequest'
      responses:
        '200':
          description: >-
            Response array indicating the status of application for each
            resource.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ApplyResourcesResponse'
      tags:
        - K8s Resource
  /orchestrator/app/workflow:
    post:
      summary: Create an application workflow
      description: Creates a new workflow for a given application.
      operationId: CreateAppWorkflow
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      tags:
        - Workflow Management
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AppWorkflowDto'
      responses:
        '200':
          description: Successfully created workflow.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AppWorkflowDto'
        '400':
          description: Bad Request. Validation error or wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized User.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden. User does not have permission.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /orchestrator/app/workflow/{app-wf-id}/app/{app-id}:
    delete:
      summary: Delete an application workflow
      description: Deletes an existing workflow for a given application.
      operationId: DeleteAppWorkflow
      security:
        - bearerAuth: []
        - apiKeyAuth: []
      tags:
        - Workflow Management
      parameters:
        - name: app-wf-id
          in: path
          required: true
          description: ID of the application workflow to delete.
          schema:
            type: integer
        - name: app-id
          in: path
          required: true
          description: ID of the application.
          schema:
            type: integer
      responses:
        '200':
          description: Successfully deleted workflow.
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: OK
        '400':
          description: Bad Request. Invalid path parameters.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized User.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden. User does not have permission.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
components:
  schemas:
    BulkUpdateSeeExampleResponse:
      type: object
      required:
        - Script
      properties:
        resource:
          type: string
          description: Resource from url path, i.e. {apiVersion} & {kind}
        script:
          $ref: '#/components/schemas/BulkUpdateScript'
          description: Input Script for bulk update
        readMe:
          type: string
          description: Readme for bulk update
    BulkUpdateScript:
      type: object
      required:
        - ApiVersion
        - Kind
        - Spec
      properties:
        apiVersion:
          type: string
          description: Api version from url
          example:
            - v1beta1
        kind:
          type: string
          description: Kind
          example:
            - application
        spec:
          $ref: '#/components/schemas/BulkUpdatePayload'
    BulkUpdatePayload:
      type: object
      properties:
        includes:
          $ref: '#/components/schemas/NameIncludesExcludes'
        excludes:
          $ref: '#/components/schemas/NameIncludesExcludes'
        envIds:
          type: array
          items:
            type: integer
          description: All Env Id's for updating dependent apps
        global:
          type: boolean
          description: Global flag for updating dependent apps
        DeploymentTemplate:
          $ref: '#/components/schemas/Tasks'
        ConfigMaps:
          type: object
          properties:
            names:
              type: array
              items:
                type: string
                description: Name of All ConfigMaps to be updated
            tasks:
              $ref: '#/components/schemas/Spec'
        Secrets:
          type: object
          properties:
            names:
              type: array
              items:
                type: string
                description: Name of All Secrets to be updated
            tasks:
              $ref: '#/components/schemas/Spec'
    Tasks:
      type: object
      properties:
        spec:
          $ref: '#/components/schemas/Spec'
          description: Spec of the Task
    Spec:
      type: object
      properties:
        patchData:
          type: string
          description: string with details of the patch to be used for updating
    NameIncludesExcludes:
      type: object
      properties:
        names:
          type: array
          items:
            type: string
          description: All strings of app names to be included/excluded
    ImpactedObjectsResponse:
      type: object
      properties:
        deploymentTemplate:
          type: array
          items:
            $ref: >-
              #/components/schemas/DeploymentTemplateImpactedObjectsResponseForOneApp
        configMap:
          type: array
          items:
            $ref: '#/components/schemas/CmAndSecretImpactedObjectsResponseForOneApp'
        secret:
          type: array
          items:
            $ref: '#/components/schemas/CmAndSecretImpactedObjectsResponseForOneApp'
    DeploymentTemplateImpactedObjectsResponseForOneApp:
      type: object
      properties:
        appId:
          type: integer
          description: Id of the impacted app
        appName:
          type: string
          description: Name of the impacted app
        envId:
          type: string
          description: Env Id of the impacted app
    CmAndSecretImpactedObjectsResponseForOneApp:
      type: object
      properties:
        appId:
          type: integer
          description: Id of the impacted app
        appName:
          type: string
          description: Name of the impacted app
        envId:
          type: string
          description: Env Id of the impacted app
        names:
          type: array
          items:
            type: string
            description: Names of all configmaps/secrets impacted
    BulkUpdateResponse:
      type: object
      properties:
        deploymentTemplate:
          $ref: '#/components/schemas/DeploymentTemplateBulkUpdateResponse'
        configMap:
          $ref: '#/components/schemas/CmAndSecretBulkUpdateResponse'
        secret:
          $ref: '#/components/schemas/CmAndSecretBulkUpdateResponse'
    DeploymentTemplateBulkUpdateResponse:
      type: object
      properties:
        message:
          type: array
          items:
            type: string
          description: All top-level messages in response of bulk update action
        failure:
          type: array
          items:
            $ref: '#/components/schemas/DeploymentTemplateBulkUpdateResponseForOneApp'
          description: Details of all apps on which bulk update failed
        successful:
          type: array
          items:
            $ref: '#/components/schemas/DeploymentTemplateBulkUpdateResponseForOneApp'
          description: Details of all apps on which bulk update applied successfully
    CmAndSecretBulkUpdateResponse:
      type: object
      properties:
        message:
          type: array
          items:
            type: string
          description: All top-level messages in response of bulk update action
        failure:
          type: array
          items:
            $ref: '#/components/schemas/CmAndSecretBulkUpdateResponseForOneApp'
          description: Details of all apps on which bulk update failed
        successful:
          type: array
          items:
            $ref: '#/components/schemas/CmAndSecretBulkUpdateResponseForOneApp'
          description: Details of all apps on which bulk update applied successfully
    DeploymentTemplateBulkUpdateResponseForOneApp:
      type: object
      properties:
        appId:
          type: integer
          description: Id of the concerned app
        appName:
          type: string
          description: Name of the concerned app
        envId:
          type: integer
          description: Env ID of the concerned app
        message:
          type: string
          description: App-level message for the concerned app
    CmAndSecretBulkUpdateResponseForOneApp:
      type: object
      properties:
        appId:
          type: integer
          description: Id of the concerned app
        appName:
          type: string
          description: Name of the concerned app
        envId:
          type: integer
          description: Env ID of the concerned app
        names:
          type: array
          items:
            type: string
          description: Names of all configmaps/secrets
        message:
          type: string
          description: App-level message for the concerned app
    BulkActionRequest:
      type: object
      properties:
        includes:
          $ref: '#/components/schemas/NameIncludesExcludes'
        excludes:
          $ref: '#/components/schemas/NameIncludesExcludes'
        envIds:
          type: array
          items:
            type: integer
          description: All Env Id's for the bulk action
        appIds:
          type: array
          items:
            type: integer
          description: >-
            All App Id's for the bulk action (alternative to includes/excludes
            by name)
        projectIds:
          type: array
          items:
            type: integer
          description: All Project Id's for the bulk action
    BulkActionResponse:
      type: object
      properties:
        message:
          type: array
          items:
            type: string
          description: Top-level messages in response of the bulk action
        failure:
          type: array
          items:
            $ref: '#/components/schemas/BulkActionFailureDetail'
          description: Details of all items on which the bulk action failed
        successful:
          type: array
          items:
            $ref: '#/components/schemas/BulkActionSuccessDetail'
          description: Details of all items on which the bulk action applied successfully
    BulkActionFailureDetail:
      type: object
      properties:
        appId:
          type: integer
        appName:
          type: string
        envId:
          type: integer
        message:
          type: string
    BulkActionSuccessDetail:
      type: object
      properties:
        appId:
          type: integer
        appName:
          type: string
        envId:
          type: integer
        message:
          type: string
    BulkDeployRequest:
      type: object
      required:
        - envIds
      properties:
        includes:
          $ref: '#/components/schemas/NameIncludesExcludes'
        excludes:
          $ref: '#/components/schemas/NameIncludesExcludes'
        envIds:
          type: array
          items:
            type: integer
          description: All Env Id's for the bulk deployment
        appIds:
          type: array
          items:
            type: integer
          description: >-
            All App Id's for the bulk deployment (alternative to
            includes/excludes by name)
        projectIds:
          type: array
          items:
            type: integer
          description: All Project Id's for the bulk deployment
        artifactId:
          type: integer
          description: ID of the CI artifact to be deployed
        releaseId:
          type: integer
          description: ID of the release to be deployed
        deploymentStrategy:
          type: string
          description: Deployment strategy to use (e.g., blue-green, canary, recreate)
    BulkBuildTriggerRequest:
      type: object
      required:
        - ciPipelineId
      properties:
        includes:
          $ref: '#/components/schemas/NameIncludesExcludes'
        excludes:
          $ref: '#/components/schemas/NameIncludesExcludes'
        appIds:
          type: array
          items:
            type: integer
          description: >-
            All App Id's for the bulk build trigger (alternative to
            includes/excludes by name)
        projectIds:
          type: array
          items:
            type: integer
          description: All Project Id's for the bulk build trigger
        ciPipelineId:
          type: integer
          description: ID of the CI pipeline to trigger builds for
    Error:
      required:
        - code
        - message
      properties:
        code:
          type: integer
          description: Error code
        message:
          type: string
          description: Error message
    SSOLoginDto:
      type: object
      properties:
        id:
          type: integer
          format: int32
          readOnly: true
        name:
          type: string
        label:
          type: string
          nullable: true
        url:
          type: string
          format: url
          nullable: true
        config:
          type: object
          description: >-
            Configuration for the SSO provider (Dex connector config).
            Structure varies.
        active:
          type: boolean
      required:
        - name
        - url
        - config
        - active
    UserInfo:
      type: object
      properties:
        id:
          type: integer
          format: int32
          description: >-
            User ID. Should not be set for new user creation if auto-generated.
            Not allowed to be system-admin-userid (1 or 2) by validation.
        email_id:
          type: string
          format: email
          description: >-
            User's email address. Cannot be system admin user email by
            validation.
        roles:
          type: array
          items:
            type: string
          description: >-
            List of direct roles assigned to the user (deprecated in favor of
            roleFilters and userRoleGroups).
          nullable: true
        accessToken:
          type: string
          description: >-
            Access token for the user (typically not part of create/update
            payload, but in response).
          readOnly: true
          nullable: true
        roleFilters:
          type: array
          items:
            $ref: '#/components/schemas/RoleFilter'
        groups:
          type: array
          items:
            type: string
          description: Deprecated field for user groups. Use userRoleGroups instead.
          nullable: true
        userRoleGroups:
          type: array
          items:
            $ref: '#/components/schemas/UserRoleGroup'
          nullable: true
        superAdmin:
          type: boolean
          description: Indicates if the user has super admin privileges.
        lastLoginTime:
          type: string
          format: date-time
          readOnly: true
          description: The time the user last logged in.
      required:
        - email_id
    RoleGroup:
      type: object
      properties:
        id:
          type: integer
          format: int32
          readOnly: true
        name:
          type: string
        description:
          type: string
          nullable: true
        roleFilters:
          type: array
          items:
            $ref: '#/components/schemas/RoleFilter'
        superAdmin:
          type: boolean
          description: Indicates if this role group grants super admin privileges.
      required:
        - name
        - roleFilters
    RoleFilter:
      type: object
      description: Defines a specific permission filter for a role.
      properties:
        entity:
          type: string
          description: >-
            The type of entity this filter applies to (e.g., apps, jobs,
            chart-group, cluster).
          enum:
            - apps
            - jobs
            - chart-group
            - cluster
        team:
          type: string
          description: >-
            Team associated with this permission. Can be empty for some global
            entities.
          nullable: true
        entityName:
          type: string
          description: >-
            Name of the specific entity (e.g., application name, job name, chart
            group name). Can be empty for 'all' access.
          nullable: true
        environment:
          type: string
          description: >-
            Environment associated with this permission. Can be empty if not
            applicable.
          nullable: true
        action:
          type: string
          description: Action permitted (e.g., get, create, update, delete, trigger, *).
        accessType:
          type: string
          description: >-
            Access type, typically for distinguishing app types like devtron-app
            or helm-app.
          enum:
            - devtron-app
            - helm-app
            - ''
          nullable: true
        cluster:
          type: string
          description: Cluster name for cluster-scoped permissions.
          nullable: true
        namespace:
          type: string
          description: Namespace for cluster-scoped permissions.
          nullable: true
        group:
          type: string
          description: API group for K8s resources.
          nullable: true
        kind:
          type: string
          description: Kind of K8s resource.
          nullable: true
        resource:
          type: string
          description: Specific K8s resource name.
          nullable: true
        workflow:
          type: string
          description: Workflow name, applicable if entity is 'jobs'.
          nullable: true
      required:
        - entity
        - action
    UserRoleGroup:
      type: object
      description: Associates a user with a role group.
      properties:
        roleGroup:
          $ref: '#/components/schemas/RoleGroup'
    RbacRoleDto:
      type: object
      description: Represents a default RBAC role.
      properties:
        id:
          type: integer
          format: int32
        roleName:
          type: string
        roleDisplayName:
          type: string
        roleDescription:
          type: string
        entity:
          type: string
          enum:
            - apps
            - cluster
            - chart-group
            - jobs
        accessType:
          type: string
          nullable: true
    BulkDeleteRequest:
      type: object
      properties:
        ids:
          type: array
          items:
            type: integer
            format: int32
          description: List of IDs to delete.
        listingRequest:
          $ref: '#/components/schemas/ListingRequest'
      required:
        - ids
    ListingRequest:
      type: object
      properties:
        searchKey:
          type: string
          nullable: true
        sortOrder:
          type: string
          enum:
            - ASC
            - DESC
          nullable: true
        sortBy:
          type: string
          nullable: true
        offset:
          type: integer
          format: int32
          minimum: 0
          nullable: true
        size:
          type: integer
          format: int32
          minimum: 1
          default: 20
          nullable: true
        showAll:
          type: boolean
          nullable: true
          readOnly: true
    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          format: int32
        status:
          type: string
        result:
          type: object
          nullable: true
        errors:
          type: array
          items:
            type: object
            properties:
              userMessage:
                type: string
                nullable: true
              internalMessage:
                type: string
                nullable: true
    EnvironmentCreateRequest:
      type: object
      required:
        - environment_name
        - cluster_id
      properties:
        environment_name:
          type: string
          maxLength: 50
        cluster_id:
          type: integer
        active:
          type: boolean
          default: true
        default:
          type: boolean
          default: false
        prometheus_endpoint:
          type: string
        namespace:
          type: string
          maxLength: 50
        isClusterCdActive:
          type: boolean
        description:
          type: string
          maxLength: 40
        isVirtualEnvironment:
          type: boolean
          default: false
        allowedDeploymentTypes:
          type: array
          items:
            type: string
            enum:
              - helm
              - argo_cd
    EnvironmentUpdateRequest:
      type: object
      required:
        - id
        - environment_name
        - cluster_id
      properties:
        id:
          type: integer
        environment_name:
          type: string
          maxLength: 50
        cluster_id:
          type: integer
        active:
          type: boolean
        default:
          type: boolean
        prometheus_endpoint:
          type: string
        namespace:
          type: string
          maxLength: 50
        isClusterCdActive:
          type: boolean
        description:
          type: string
          maxLength: 40
        isVirtualEnvironment:
          type: boolean
        allowedDeploymentTypes:
          type: array
          items:
            type: string
            enum:
              - helm
              - argo_cd
    EnvironmentDetail:
      type: object
      properties:
        id:
          type: integer
        environment_name:
          type: string
        cluster_id:
          type: integer
        cluster_name:
          type: string
        active:
          type: boolean
        default:
          type: boolean
        prometheus_endpoint:
          type: string
        namespace:
          type: string
        isClusterCdActive:
          type: boolean
        environmentIdentifier:
          type: string
        description:
          type: string
        appCount:
          type: integer
        isVirtualEnvironment:
          type: boolean
        allowedDeploymentTypes:
          type: array
          items:
            type: string
            enum:
              - helm
              - argo_cd
    ClusterWithEnvironments:
      type: object
      properties:
        id:
          type: integer
          description: Cluster ID
        cluster_name:
          type: string
          description: Name of the cluster
        server_url:
          type: string
          description: Server URL of the cluster
        active:
          type: boolean
          description: Whether the cluster is active
        environments:
          type: array
          items:
            $ref: '#/components/schemas/Environment'
        prometheus_url:
          type: string
          description: URL for Prometheus monitoring
        k8sVersion:
          type: string
          description: Kubernetes version of the cluster
    Environment:
      type: object
      properties:
        environmentId:
          type: integer
          description: Environment ID
        environmentName:
          type: string
          description: Name of the environment
        environmentIdentifier:
          type: string
          description: Unique identifier for the environment
        namespace:
          type: string
          description: Namespace associated with the environment
        active:
          type: boolean
          description: Whether the environment is active
    ClusterBean:
      type: object
      properties:
        id:
          type: integer
        cluster_name:
          type: string
        server_url:
          type: string
        prometheus_url:
          type: string
        active:
          type: boolean
        config:
          type: object
          properties:
            bearer_token:
              type: string
              description: it will be empty while fetching, and if no change while updating
        prometheusAuth:
          $ref: '#/components/schemas/PrometheusAuthGet'
        defaultClusterComponents:
          type: array
          items:
            $ref: '#/components/schemas/DefaultClusterComponentGet'
        k8sversion:
          type: string
    ClusterAuthDetail:
      type: object
      properties:
        clusterId:
          type: integer
          description: cluster id
        clusterName:
          type: string
          description: cluster name
        errorInConnecting:
          type: string
          description: error message if cluster failed to connect
    Kubeconfig:
      type: object
      properties:
        config:
          type: string
    ValidateClusterBean:
      type: object
      required:
        - cluster_name
        - server_url
      properties:
        userInfos:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/UserInfos'
        id:
          type: integer
        cluster_name:
          type: string
        server_url:
          type: string
        prometheus_url:
          type: string
        active:
          type: boolean
        config:
          type: object
          properties:
            bearer_token:
              type: string
              description: it will be empty while fetching, and if no change while updating
            tls_key:
              type: string
              description: it will be empty while fetching, and if no change while updating
            cert_data:
              type: string
              description: it will be empty while fetching, and if no change while updating
            cert_auth_data:
              type: string
              description: it will be empty while fetching, and if no change while updating
        prometheusAuth:
          $ref: '#/components/schemas/PrometheusAuthAdd'
        defaultClusterComponent:
          type: array
          items:
            $ref: '#/components/schemas/DefaultClusterComponentAdd'
        agentInstallationStage:
          type: integer
        k8sVersion:
          type: string
        userName:
          type: string
        insecure-skip-tls-verify:
          type: boolean
    UserInfos:
      type: object
      properties:
        userName:
          type: string
        config:
          type: object
          additionalProperties:
            type: string
        errorInConnecting:
          type: string
    PrometheusAuthAdd:
      type: object
      properties:
        type:
          type: string
          enum:
            - basic
            - bearer
        basic:
          type: object
          properties:
            username:
              type: string
            password:
              type: string
        bearer:
          type: object
          properties:
            token:
              type: string
    PrometheusAuthGet:
      type: object
      properties:
        userName:
          type: string
        password:
          type: string
        tlsClientCert:
          type: string
        tlsClientKey:
          type: string
    DefaultClusterComponentAdd:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        version:
          type: string
        status:
          type: string
        configuration:
          $ref: '#/components/schemas/ComponentConfiguration'
    DefaultClusterComponentGet:
      type: object
      properties:
        name:
          type: string
        appId:
          type: integer
        installedAppId:
          type: integer
        envId:
          type: integer
        envname:
          type: string
        status:
          type: string
    ComponentConfiguration:
      type: object
      properties:
        type:
          type: string
          enum:
            - yaml
            - json
    CloneApplicationWorkflowRequest:
      type: object
      properties:
        appId:
          type: integer
          description: ID of the application
        appName:
          type: string
          description: Name of the application
        sourceEnvironmentId:
          type: integer
          description: ID of the source environment
        sourceEnvironmentName:
          type: string
          description: Name of the source environment
        targetEnvironmentId:
          type: integer
          description: ID of the target environment
        targetEnvironmentName:
          type: string
          description: Name of the target environment
        cloneEnvInSameWorkflow:
          type: boolean
          description: >-
            Flag indicating if the environment should be cloned in the same
            workflow
    CloneApplicationWorkflowResponse:
      type: object
      properties:
        status:
          type: string
          description: Status of the operation
          enum:
            - SUCCESS
            - FAILED
            - SKIPPED
        message:
          type: string
          description: Detailed message about the operation result
      required:
        - status
        - message
    StandardResponse:
      type: object
      properties:
        code:
          type: integer
          description: HTTP status code
          example: 200
        status:
          type: string
          description: HTTP status text
          example: OK
        result:
          type: object
          properties:
            status:
              type: string
              description: Status of the operation
              enum:
                - SUCCESS
                - FAILED
                - SKIPPED
            message:
              type: string
              description: Detailed message about the operation result
          required:
            - status
            - message
      required:
        - code
        - status
        - result
    ResourceInfo:
      type: object
      required:
        - podName
      properties:
        podName:
          type: string
          description: Name of the inception pod.
          example: inception-pod-xyz
    TerminalMessage:
      type: object
      properties:
        Op:
          type: string
          description: Operation type for the terminal session.
          example: stdin
        Data:
          type: string
          description: Data for the terminal session (e.g., command or output).
          example: ls -l
        SessionID:
          type: string
          description: ID of the terminal session.
          example: unique-session-id-123
    ResourceRequestObject:
      type: object
      properties:
        appId:
          type: string
          description: >-
            Application ID. Used when the request is context-specific to an
            application.
          example: my-app/env-1
        clusterId:
          type: number
          description: >-
            Cluster ID. Used when the request is for a direct cluster resource
            (appId is not supplied).
          example: 1
        k8sRequest:
          $ref: '#/components/schemas/K8sRequestObject'
    K8sRequestObject:
      type: object
      properties:
        resourceIdentifier:
          type: object
          properties:
            groupVersionKind:
              $ref: '#/components/schemas/GroupVersionKind'
            namespace:
              type: string
              description: Namespace of the resource.
              example: default
            name:
              type: string
              description: Name of the resource.
              example: my-deployment
          required:
            - name
            - groupVersionKind
        podLogsRequest:
          type: object
          properties:
            containerName:
              type: string
              description: Name of the container for which logs are requested.
              example: my-container
        patch:
          type: string
          description: JSON patch or strategic merge patch to apply to the resource.
          example: '[{"op": "replace", "path": "/spec/replicas", "value": 3}]'
    ResourceGetResponse:
      type: object
      properties:
        manifestResponse:
          $ref: '#/components/schemas/ManifestResponse'
        secretViewAccess:
          type: boolean
          description: >
            Indicates whether a user can see obscured secret values or not. True
            if the user has permission, false otherwise.
          example: true
      required:
        - manifestResponse
        - secretViewAccess
    ManifestResponse:
      type: object
      properties:
        manifest:
          type: object
          description: The Kubernetes resource manifest.
          additionalProperties: true
          example:
            apiVersion: v1
            kind: ConfigMap
            metadata:
              name: my-config
              namespace: default
            data:
              key1: value1
              key2: value2
    EventsResponseObject:
      type: object
      description: Represents a Kubernetes Event object.
      properties:
        metadata:
          $ref: '#/components/schemas/ObjectMeta'
        involvedObject:
          $ref: '#/components/schemas/ObjectReference'
        reason:
          type: string
          description: >-
            Short, machine-understandable string that describes the reason for
            the transition into the object's current status.
          example: Scheduled
        message:
          type: string
          description: A human-readable description of the status of this operation.
          example: Successfully assigned default/my-pod to node-1
        source:
          $ref: '#/components/schemas/EventSource'
        firstTimestamp:
          type: string
          format: date-time
          description: The time at which the event was first recorded.
        lastTimestamp:
          type: string
          format: date-time
          description: >-
            The time at which the most recent occurrence of this event was
            recorded.
        count:
          type: integer
          format: int32
          description: The number of times this event has occurred.
        type:
          type: string
          description: >-
            Type of this event (Normal, Warning), new types could be added in
            the future.
          example: Normal
        eventTime:
          type: string
          format: date-time
          nullable: true
          description: MicroTime is version of Time with microsecond level precision.
        reportingComponent:
          type: string
          example: kubelet
        reportingInstance:
          type: string
          example: node-1
    ObjectMeta:
      type: object
      properties:
        name:
          type: string
        namespace:
          type: string
        uid:
          type: string
        resourceVersion:
          type: string
        creationTimestamp:
          type: string
          format: date-time
    ObjectReference:
      type: object
      properties:
        kind:
          type: string
        namespace:
          type: string
        name:
          type: string
        uid:
          type: string
        apiVersion:
          type: string
        resourceVersion:
          type: string
    EventSource:
      type: object
      properties:
        component:
          type: string
        host:
          type: string
    LogsResponseObject:
      type: object
      properties:
        id:
          type: string
          description: Identifier for the log entry (if provided by the stream).
        type:
          type: string
          description: Type of the log entry (e.g., 'stdout', 'stderr').
        data:
          type: string
          description: The actual log line content.
        time:
          type: string
          format: date-time
          description: Timestamp of the log entry.
    GetAllApiResourcesResponse:
      type: object
      properties:
        apiResources:
          type: array
          items:
            $ref: '#/components/schemas/K8sApiResource'
        allowedAll:
          type: boolean
          description: Whether all API resources are allowed for this user.
          example: true
    K8sApiResource:
      type: object
      properties:
        gvk:
          $ref: '#/components/schemas/GroupVersionKind'
        namespaced:
          type: boolean
          description: Whether this API resource is namespace-scoped or cluster-scoped.
          example: true
    GroupVersionKind:
      type: object
      properties:
        group:
          type: string
          description: Group of the API resource.
          example: apps
        version:
          type: string
          description: Version of the API resource.
          example: v1
        kind:
          type: string
          description: Kind of the API resource.
          example: Deployment
      required:
        - group
        - version
        - kind
    ClusterResourceListResponse:
      type: object
      description: >-
        Represents a list of resources with dynamic headers and corresponding
        data.
      properties:
        headers:
          type: array
          items:
            type: string
          description: >-
            An array of strings representing the column headers for the resource
            list.
          example:
            - NAME
            - NAMESPACE
            - KIND
            - AGE
        data:
          type: array
          items:
            type: object
            additionalProperties:
              type: string
            description: >
              An array of objects. Each object represents a resource, and its
              keys correspond to the 'headers'. The values are the resource's
              data for those headers.
            example:
              - NAME: my-pod-1
                NAMESPACE: default
                KIND: Pod
                AGE: 2d
              - NAME: my-service-abc
                NAMESPACE: kube-system
                KIND: Service
                AGE: 10h
    RotatePodRequest:
      type: object
      properties:
        clusterId:
          type: number
          description: ID of the cluster where resources reside.
          example: 1
        resources:
          type: array
          items:
            type: object
            properties:
              groupVersionKind:
                $ref: '#/components/schemas/GroupVersionKind'
              namespace:
                type: string
                description: Namespace of the resource.
                example: production
              name:
                type: string
                description: Name of the resource (e.g., Deployment, StatefulSet name).
                example: my-app-deployment
            required:
              - name
              - groupVersionKind
              - namespace
      required:
        - clusterId
        - resources
    RotatePodResponse:
      type: object
      properties:
        containsError:
          type: boolean
          description: True if any error occurred during the rotation of one or more pods.
          example: false
        responses:
          type: array
          items:
            type: object
            properties:
              groupVersionKind:
                $ref: '#/components/schemas/GroupVersionKind'
              namespace:
                type: string
                description: Namespace of the resource.
              name:
                type: string
                description: Name of the resource.
              errorResponse:
                type: string
                nullable: true
                description: >-
                  Error message if rotation failed for this specific resource.
                  Otherwise null.
                example: failed to find resource
    ApplyResourcesRequest:
      type: object
      properties:
        clusterId:
          type: number
          description: ID of the cluster where resources will be applied.
          example: 1
        manifest:
          type: string
          description: >
            A string containing one or more Kubernetes resource manifests,
            separated by '---'.
          example: |
            apiVersion: v1
            kind: ConfigMap
            metadata:
              name: my-cm
            data:
              key: value
            ---
            apiVersion: apps/v1
            kind: Deployment
            metadata:
              name: my-app
            spec:
              replicas: 1
              selector:
                matchLabels:
                  app: my-app
              template:
                metadata:
                  labels:
                    app: my-app
                spec:
                  containers:
                  - name: nginx
                    image: nginx
      required:
        - clusterId
        - manifest
    ApplyResourcesResponse:
      type: object
      properties:
        kind:
          type: string
          description: Kind of the resource that was applied.
          example: Deployment
        name:
          type: string
          description: Name of the resource that was applied.
          example: my-app
        error:
          type: string
          nullable: true
          description: >-
            Error message if applying this specific resource failed. Otherwise
            null.
          example: null
        isUpdate:
          type: boolean
          description: >-
            True if the resource was updated, false if it was created (or no
            change).
          example: true
      required:
        - kind
        - name
        - isUpdate
    AppWorkflowDto:
      type: object
      properties:
        id:
          type: integer
          description: ID of the workflow.
          readOnly: true
        name:
          type: string
          description: Name of the workflow.
        appId:
          type: integer
          description: ID of the application this workflow belongs to.
        tree:
          type: array
          items:
            $ref: '#/components/schemas/AppWorkflowMappingDto'
    AppWorkflowMappingDto:
      type: object
      properties:
        id:
          type: integer
          description: ID of the workflow mapping.
          readOnly: true
        appWorkflowId:
          type: integer
          description: ID of the parent application workflow.
        type:
          type: string
          description: Type of the component (e.g., CI_PIPELINE, CD_PIPELINE).
        componentId:
          type: integer
          description: ID of the component (e.g., CiPipelineId, CdPipelineId).
        parentId:
          type: integer
          description: ID of the parent component in the workflow tree.
        parentType:
          type: string
          description: Type of the parent component in the workflow tree.
        deploymentAppDeleteRequest:
          type: boolean
          description: Indicates if a deployment app delete request is associated.
        isLast:
          type: boolean
          description: Indicates if this is the last node in its branch of the tree.
  requestBodies:
    SSOLoginDto:
      description: SSO Login Configuration object
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/SSOLoginDto'
    UserInfo:
      description: User Information object
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/UserInfo'
    RoleGroup:
      description: Role Group object
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/RoleGroup'
    BulkDeleteRequest:
      description: Request for bulk deletion of items.
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/BulkDeleteRequest'
  responses:
    SSOLoginConfigResponse:
      description: Response for SSO Login Configuration.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/SSOLoginDto'
    UserInfoResponse:
      description: Response containing User Information.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/UserInfo'
    UserListingResponse:
      description: Paginated list of users.
      content:
        application/json:
          schema:
            type: object
            properties:
              users:
                type: array
                items:
                  $ref: '#/components/schemas/UserInfo'
              totalCount:
                type: integer
                format: int32
    RoleGroupResponse:
      description: Response containing Role Group information.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/RoleGroup'
    RoleGroupListingResponse:
      description: Paginated list of role groups.
      content:
        application/json:
          schema:
            type: object
            properties:
              roleGroups:
                type: array
                items:
                  $ref: '#/components/schemas/RoleGroup'
              totalCount:
                type: integer
                format: int32
    GenericSuccess:
      description: Generic success response.
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: true
    BadRequest:
      description: Bad request. Invalid input parameters.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    Unauthorized:
      description: Unauthorized. User is not logged in or token is invalid.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    Forbidden:
      description: Forbidden. User does not have permission.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    NotAcceptable:
      description: Not Acceptable. Request cannot be processed.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    InternalServerError:
      description: Internal server error.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
  parameters:
    PathId:
      name: id
      in: path
      required: true
      description: Identifier (typically integer ID).
      schema:
        type: integer
        format: int32
    SearchKeyQuery:
      name: searchKey
      in: query
      required: false
      description: Search term.
      schema:
        type: string
    SortOrderQuery:
      name: sortOrder
      in: query
      required: false
      description: Sort order (ASC or DESC).
      schema:
        type: string
        enum:
          - ASC
          - DESC
    SortByQueryUser:
      name: sortBy
      in: query
      required: false
      description: Field to sort users by (e.g., email_id, last_login).
      schema:
        type: string
        enum:
          - email_id
          - last_login
    SortByQueryRoleGroup:
      name: sortBy
      in: query
      required: false
      description: Field to sort role groups by (e.g., name).
      schema:
        type: string
        enum:
          - name
    OffsetQuery:
      name: offset
      in: query
      required: false
      description: Offset for pagination.
      schema:
        type: integer
        format: int32
        minimum: 0
    SizeQuery:
      name: size
      in: query
      required: false
      description: Number of items per page.
      schema:
        type: integer
        format: int32
        minimum: 1
        default: 20
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token authentication
    apiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
      description: API key authentication
x-tagGroups:
  - name: Devtron Labs Kubernetes API
    tags:
      - Labels
      - bulk_other
      - BulkUpdate
      - SSO Configuration
      - User Management
      - Role Group Management
      - RBAC
      - Authentication
      - Policy Management
      - Cache Management
      - Cluster Environment
      - Cluster Management
      - Environment Management
      - Change Chart
      - Clone Workflow
      - K8s Resource
      - Workflow Management