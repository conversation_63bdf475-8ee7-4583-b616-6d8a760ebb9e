// Code generated by Wire. DO NOT EDIT.

//go:generate go run github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/devtron-labs/authenticator/apiToken"
	"github.com/devtron-labs/authenticator/client"
	"github.com/devtron-labs/authenticator/middleware"
	"github.com/devtron-labs/common-lib-private/recommended/resources"
	k8s2 "github.com/devtron-labs/common-lib-private/utils/k8s"
	"github.com/devtron-labs/common-lib-private/utils/k8s/proxy"
	"github.com/devtron-labs/common-lib-private/utils/ssh"
	"github.com/devtron-labs/common-lib/cloud-provider-identifier"
	"github.com/devtron-labs/common-lib/env"
	"github.com/devtron-labs/common-lib/telemetry"
	"github.com/devtron-labs/common-lib/utils/grpc"
	"github.com/devtron-labs/common-lib/utils/k8s"
	apiToken2 "github.com/devtron-labs/devtron/api/apiToken"
	"github.com/devtron-labs/devtron/api/appStore/chartCategory"
	chartProvider2 "github.com/devtron-labs/devtron/api/appStore/chartProvider"
	"github.com/devtron-labs/devtron/api/appStore/deployment"
	"github.com/devtron-labs/devtron/api/appStore/discover"
	"github.com/devtron-labs/devtron/api/appStore/values"
	argoApplication2 "github.com/devtron-labs/devtron/api/argoApplication"
	"github.com/devtron-labs/devtron/api/auth/authorisation/globalConfig"
	sso2 "github.com/devtron-labs/devtron/api/auth/sso"
	user2 "github.com/devtron-labs/devtron/api/auth/user"
	"github.com/devtron-labs/devtron/api/auth/userGroup"
	chartRepo2 "github.com/devtron-labs/devtron/api/chartRepo"
	chat2 "github.com/devtron-labs/devtron/api/chat"
	bean3 "github.com/devtron-labs/devtron/api/chat/bean"
	cluster2 "github.com/devtron-labs/devtron/api/cluster"
	clusterUpgrade2 "github.com/devtron-labs/devtron/api/clusterUpgrade"
	"github.com/devtron-labs/devtron/api/connector"
	"github.com/devtron-labs/devtron/api/dashboardEvent"
	devtronResource2 "github.com/devtron-labs/devtron/api/devtronResource"
	externalLink2 "github.com/devtron-labs/devtron/api/externalLink"
	fluxApplication2 "github.com/devtron-labs/devtron/api/fluxApplication"
	client3 "github.com/devtron-labs/devtron/api/helm-app"
	"github.com/devtron-labs/devtron/api/helm-app/gRPC"
	"github.com/devtron-labs/devtron/api/helm-app/service"
	read6 "github.com/devtron-labs/devtron/api/helm-app/service/read"
	"github.com/devtron-labs/devtron/api/infrastructureDeployment"
	application2 "github.com/devtron-labs/devtron/api/k8s/application"
	capacity2 "github.com/devtron-labs/devtron/api/k8s/capacity"
	module2 "github.com/devtron-labs/devtron/api/module"
	"github.com/devtron-labs/devtron/api/restHandler"
	"github.com/devtron-labs/devtron/api/restHandler/app/appInfo"
	"github.com/devtron-labs/devtron/api/restHandler/app/appList"
	autoRemediation2 "github.com/devtron-labs/devtron/api/restHandler/autoRemediation"
	"github.com/devtron-labs/devtron/api/router"
	app3 "github.com/devtron-labs/devtron/api/router/app"
	appInfo2 "github.com/devtron-labs/devtron/api/router/app/appInfo"
	appList2 "github.com/devtron-labs/devtron/api/router/app/appList"
	scoop2 "github.com/devtron-labs/devtron/api/scoop"
	server2 "github.com/devtron-labs/devtron/api/server"
	team2 "github.com/devtron-labs/devtron/api/team"
	terminal2 "github.com/devtron-labs/devtron/api/terminal"
	userResource2 "github.com/devtron-labs/devtron/api/userResource"
	webhookHelm2 "github.com/devtron-labs/devtron/api/webhook/helm"
	"github.com/devtron-labs/devtron/cel"
	"github.com/devtron-labs/devtron/client/argocdServer"
	"github.com/devtron-labs/devtron/client/argocdServer/bean"
	"github.com/devtron-labs/devtron/client/argocdServer/config"
	"github.com/devtron-labs/devtron/client/argocdServer/repoCredsK8sClient"
	"github.com/devtron-labs/devtron/client/chat"
	"github.com/devtron-labs/devtron/client/dashboard"
	"github.com/devtron-labs/devtron/client/grafana"
	"github.com/devtron-labs/devtron/client/scoop"
	grpc2 "github.com/devtron-labs/devtron/client/silverSurfer/grpc"
	telemetry2 "github.com/devtron-labs/devtron/client/telemetry"
	"github.com/devtron-labs/devtron/enterprise/pkg/deploymentWindow"
	repository12 "github.com/devtron-labs/devtron/internal/sql/repository"
	"github.com/devtron-labs/devtron/internal/sql/repository/app"
	"github.com/devtron-labs/devtron/internal/sql/repository/appStatus"
	"github.com/devtron-labs/devtron/internal/sql/repository/chartConfig"
	"github.com/devtron-labs/devtron/internal/sql/repository/deploymentConfig"
	repository9 "github.com/devtron-labs/devtron/internal/sql/repository/dockerRegistry"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/internal/util"
	"github.com/devtron-labs/devtron/internal/util/configUtil"
	"github.com/devtron-labs/devtron/licensing/handler"
	"github.com/devtron-labs/devtron/licensing/licenseClient"
	"github.com/devtron-labs/devtron/pkg/InfrastructureInstallationService"
	"github.com/devtron-labs/devtron/pkg/InfrastructureInstallationService/eventHandler"
	"github.com/devtron-labs/devtron/pkg/InfrastructureInstallationService/events/publish"
	"github.com/devtron-labs/devtron/pkg/InfrastructureInstallationService/installer"
	read3 "github.com/devtron-labs/devtron/pkg/InfrastructureInstallationService/read"
	repository10 "github.com/devtron-labs/devtron/pkg/InfrastructureInstallationService/repository"
	"github.com/devtron-labs/devtron/pkg/apiToken"
	repository24 "github.com/devtron-labs/devtron/pkg/apiToken/repository"
	app2 "github.com/devtron-labs/devtron/pkg/app"
	"github.com/devtron-labs/devtron/pkg/app/appDetails"
	"github.com/devtron-labs/devtron/pkg/app/dbMigration"
	repository16 "github.com/devtron-labs/devtron/pkg/appStore/chartGroup/repository"
	"github.com/devtron-labs/devtron/pkg/appStore/chartProvider"
	"github.com/devtron-labs/devtron/pkg/appStore/discover/repository"
	service3 "github.com/devtron-labs/devtron/pkg/appStore/discover/service"
	read5 "github.com/devtron-labs/devtron/pkg/appStore/installedApp/read"
	repository14 "github.com/devtron-labs/devtron/pkg/appStore/installedApp/repository"
	service2 "github.com/devtron-labs/devtron/pkg/appStore/installedApp/service"
	"github.com/devtron-labs/devtron/pkg/appStore/installedApp/service/EAMode"
	"github.com/devtron-labs/devtron/pkg/appStore/installedApp/service/EAMode/deployment"
	"github.com/devtron-labs/devtron/pkg/appStore/installedApp/service/common"
	"github.com/devtron-labs/devtron/pkg/appStore/values/repository"
	service4 "github.com/devtron-labs/devtron/pkg/appStore/values/service"
	"github.com/devtron-labs/devtron/pkg/argoApplication"
	read11 "github.com/devtron-labs/devtron/pkg/argoApplication/read"
	config3 "github.com/devtron-labs/devtron/pkg/argoApplication/read/config"
	"github.com/devtron-labs/devtron/pkg/attributes"
	repository19 "github.com/devtron-labs/devtron/pkg/attributes/repository"
	"github.com/devtron-labs/devtron/pkg/auth/authentication"
	"github.com/devtron-labs/devtron/pkg/auth/authorisation/casbin"
	client2 "github.com/devtron-labs/devtron/pkg/auth/authorisation/casbin/client"
	"github.com/devtron-labs/devtron/pkg/auth/authorisation/globalConfig"
	"github.com/devtron-labs/devtron/pkg/auth/authorisation/globalConfig/repository"
	"github.com/devtron-labs/devtron/pkg/auth/sso"
	"github.com/devtron-labs/devtron/pkg/auth/user"
	repository2 "github.com/devtron-labs/devtron/pkg/auth/user/repository"
	repository5 "github.com/devtron-labs/devtron/pkg/auth/userGroup/repository"
	"github.com/devtron-labs/devtron/pkg/autoRemediation"
	repository26 "github.com/devtron-labs/devtron/pkg/autoRemediation/repository"
	read14 "github.com/devtron-labs/devtron/pkg/build/git/gitMaterial/read"
	repository25 "github.com/devtron-labs/devtron/pkg/build/git/gitMaterial/repository"
	repository27 "github.com/devtron-labs/devtron/pkg/chartCategory/repository"
	service5 "github.com/devtron-labs/devtron/pkg/chartCategory/service"
	"github.com/devtron-labs/devtron/pkg/chartRepo"
	"github.com/devtron-labs/devtron/pkg/chartRepo/repository"
	"github.com/devtron-labs/devtron/pkg/cluster"
	"github.com/devtron-labs/devtron/pkg/cluster/environment"
	read12 "github.com/devtron-labs/devtron/pkg/cluster/environment/read"
	repository11 "github.com/devtron-labs/devtron/pkg/cluster/environment/repository"
	rbac2 "github.com/devtron-labs/devtron/pkg/cluster/rbac"
	read2 "github.com/devtron-labs/devtron/pkg/cluster/read"
	repository7 "github.com/devtron-labs/devtron/pkg/cluster/repository"
	"github.com/devtron-labs/devtron/pkg/clusterTerminalAccess"
	"github.com/devtron-labs/devtron/pkg/clusterUpgrade"
	"github.com/devtron-labs/devtron/pkg/commonService"
	delete2 "github.com/devtron-labs/devtron/pkg/delete"
	"github.com/devtron-labs/devtron/pkg/deployment/common"
	read8 "github.com/devtron-labs/devtron/pkg/deployment/common/read"
	config2 "github.com/devtron-labs/devtron/pkg/deployment/gitOps/config"
	read7 "github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/read"
	"github.com/devtron-labs/devtron/pkg/deployment/providerConfig"
	"github.com/devtron-labs/devtron/pkg/devtronResource"
	"github.com/devtron-labs/devtron/pkg/devtronResource/audit"
	"github.com/devtron-labs/devtron/pkg/devtronResource/in"
	read4 "github.com/devtron-labs/devtron/pkg/devtronResource/read"
	repository13 "github.com/devtron-labs/devtron/pkg/devtronResource/repository"
	"github.com/devtron-labs/devtron/pkg/devtronResource/taskRun"
	"github.com/devtron-labs/devtron/pkg/eventProcessor/out"
	"github.com/devtron-labs/devtron/pkg/externalLink"
	read13 "github.com/devtron-labs/devtron/pkg/fileUploader/read"
	repository23 "github.com/devtron-labs/devtron/pkg/fileUploader/repository"
	"github.com/devtron-labs/devtron/pkg/fluxApplication"
	"github.com/devtron-labs/devtron/pkg/genericNotes"
	repository15 "github.com/devtron-labs/devtron/pkg/genericNotes/repository"
	k8s3 "github.com/devtron-labs/devtron/pkg/k8s"
	"github.com/devtron-labs/devtron/pkg/k8s/application"
	"github.com/devtron-labs/devtron/pkg/k8s/capacity"
	"github.com/devtron-labs/devtron/pkg/k8s/informer"
	"github.com/devtron-labs/devtron/pkg/k8s/krr"
	"github.com/devtron-labs/devtron/pkg/k8s/krr/job"
	read10 "github.com/devtron-labs/devtron/pkg/k8s/krr/read"
	repository18 "github.com/devtron-labs/devtron/pkg/k8s/krr/repository"
	"github.com/devtron-labs/devtron/pkg/kubernetesResourceAuditLogs"
	repository17 "github.com/devtron-labs/devtron/pkg/kubernetesResourceAuditLogs/repository"
	"github.com/devtron-labs/devtron/pkg/module"
	bean2 "github.com/devtron-labs/devtron/pkg/module/bean"
	read9 "github.com/devtron-labs/devtron/pkg/module/read"
	"github.com/devtron-labs/devtron/pkg/module/repo"
	"github.com/devtron-labs/devtron/pkg/module/store"
	"github.com/devtron-labs/devtron/pkg/operationAudit"
	repository3 "github.com/devtron-labs/devtron/pkg/operationAudit/repository"
	"github.com/devtron-labs/devtron/pkg/panel"
	"github.com/devtron-labs/devtron/pkg/panel/repo"
	"github.com/devtron-labs/devtron/pkg/pipeline"
	repository22 "github.com/devtron-labs/devtron/pkg/pipeline/repository"
	"github.com/devtron-labs/devtron/pkg/pipeline/types"
	"github.com/devtron-labs/devtron/pkg/plugin"
	repository21 "github.com/devtron-labs/devtron/pkg/plugin/repository"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/security/imageScanning"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/security/scanTool"
	repository20 "github.com/devtron-labs/devtron/pkg/policyGovernance/security/scanTool/repository"
	"github.com/devtron-labs/devtron/pkg/remoteConnection"
	repository8 "github.com/devtron-labs/devtron/pkg/remoteConnection/repository"
	"github.com/devtron-labs/devtron/pkg/server"
	"github.com/devtron-labs/devtron/pkg/server/config"
	"github.com/devtron-labs/devtron/pkg/server/store"
	"github.com/devtron-labs/devtron/pkg/sql"
	"github.com/devtron-labs/devtron/pkg/team"
	"github.com/devtron-labs/devtron/pkg/team/read"
	repository6 "github.com/devtron-labs/devtron/pkg/team/repository"
	"github.com/devtron-labs/devtron/pkg/terminal"
	"github.com/devtron-labs/devtron/pkg/timeoutWindow"
	repository4 "github.com/devtron-labs/devtron/pkg/timeoutWindow/repository"
	"github.com/devtron-labs/devtron/pkg/ucid"
	"github.com/devtron-labs/devtron/pkg/userResource"
	util3 "github.com/devtron-labs/devtron/pkg/util"
	"github.com/devtron-labs/devtron/pkg/webhook/helm"
	util2 "github.com/devtron-labs/devtron/util"
	"github.com/devtron-labs/devtron/util/commonEnforcementFunctionsUtil"
	"github.com/devtron-labs/devtron/util/cron"
	"github.com/devtron-labs/devtron/util/rbac"
)

// Injectors from wire.go:

func InitializeApp() (*App, error) {
	sqlConfig, err := sql.GetConfig()
	if err != nil {
		return nil, err
	}
	sugaredLogger, err := util.NewSugardLogger()
	if err != nil {
		return nil, err
	}
	db, err := sql.NewDbConnection(sqlConfig, sugaredLogger)
	if err != nil {
		return nil, err
	}
	runtimeConfig, err := client.GetRuntimeConfig()
	if err != nil {
		return nil, err
	}
	k8sClient, err := client.NewK8sClient(runtimeConfig)
	if err != nil {
		return nil, err
	}
	dexConfig, err := client.BuildDexConfig(k8sClient)
	if err != nil {
		return nil, err
	}
	settings, err := client.GetSettings(dexConfig)
	if err != nil {
		return nil, err
	}
	apiTokenSecretStore := apiTokenAuth.InitApiTokenSecretStore()
	sessionManager := middleware.NewSessionManager(settings, dexConfig, apiTokenSecretStore)
	validate, err := util.IntValidator()
	if err != nil {
		return nil, err
	}
	syncedEnforcer, err := casbin.Create()
	if err != nil {
		return nil, err
	}
	casbinSyncedEnforcer := casbin.CreateV2()
	casbinClientConfig, err := client2.GetConfig()
	if err != nil {
		return nil, err
	}
	casbinClientImpl := client2.NewCasbinClientImpl(sugaredLogger, casbinClientConfig)
	casbinServiceImpl := casbin.NewCasbinServiceImpl(sugaredLogger, casbinClientImpl)
	globalAuthorisationConfigRepositoryImpl := repository.NewGlobalAuthorisationConfigRepositoryImpl(sugaredLogger, db)
	globalAuthorisationConfigServiceImpl := auth.NewGlobalAuthorisationConfigServiceImpl(sugaredLogger, globalAuthorisationConfigRepositoryImpl)
	enterpriseEnforcerImpl, err := casbin.NewEnterpriseEnforcerImpl(syncedEnforcer, casbinSyncedEnforcer, sessionManager, sugaredLogger, casbinServiceImpl, globalAuthorisationConfigServiceImpl)
	if err != nil {
		return nil, err
	}
	defaultAuthPolicyRepositoryImpl := repository2.NewDefaultAuthPolicyRepositoryImpl(db, sugaredLogger)
	defaultAuthRoleRepositoryImpl := repository2.NewDefaultAuthRoleRepositoryImpl(db, sugaredLogger)
	userAuthRepositoryImpl := repository2.NewUserAuthRepositoryImpl(db, sugaredLogger, defaultAuthPolicyRepositoryImpl, defaultAuthRoleRepositoryImpl)
	userRepositoryImpl := repository2.NewUserRepositoryImpl(db, sugaredLogger)
	roleGroupRepositoryImpl := repository2.NewRoleGroupRepositoryImpl(db, sugaredLogger)
	rbacPolicyDataRepositoryImpl := repository2.NewRbacPolicyDataRepositoryImpl(sugaredLogger, db)
	rbacRoleDataRepositoryImpl := repository2.NewRbacRoleDataRepositoryImpl(sugaredLogger, db)
	rbacDataCacheFactoryImpl := repository2.NewRbacDataCacheFactoryImpl(sugaredLogger, rbacPolicyDataRepositoryImpl, rbacRoleDataRepositoryImpl)
	userCommonServiceImpl, err := user.NewUserCommonServiceImpl(userAuthRepositoryImpl, sugaredLogger, userRepositoryImpl, roleGroupRepositoryImpl, sessionManager, rbacDataCacheFactoryImpl)
	if err != nil {
		return nil, err
	}
	userAuditRepositoryImpl := repository2.NewUserAuditRepositoryImpl(db)
	userAuditServiceImpl := user.NewUserAuditServiceImpl(sugaredLogger, userAuditRepositoryImpl)
	operationAuditRepositoryImpl := repository3.NewOperationAuditRepositoryImpl(db, sugaredLogger)
	operationAuditServiceImpl := operationAudit.NewOperationAuditServiceImpl(sugaredLogger, operationAuditRepositoryImpl)
	userOperationAuditServiceImpl := operationAudit.NewUserOperationAuditServiceImpl(sugaredLogger, operationAuditServiceImpl)
	roleGroupServiceImpl := user.NewRoleGroupServiceImpl(userAuthRepositoryImpl, sugaredLogger, userRepositoryImpl, roleGroupRepositoryImpl, userCommonServiceImpl, userOperationAuditServiceImpl)
	userAutoAssignGroupMapRepositoryImpl := repository2.NewUserAutoAssignGroupMapRepositoryImpl(db, sugaredLogger)
	timeWindowRepositoryImpl := repository4.NewTimeWindowRepositoryImpl(db, sugaredLogger)
	timeoutWindowResourceMappingRepositoryImpl := repository4.NewTimeoutWindowResourceMappingRepositoryImpl(db, sugaredLogger)
	timeWindowServiceImpl := timeoutWindow.NewTimeWindowServiceImpl(sugaredLogger, timeWindowRepositoryImpl, timeoutWindowResourceMappingRepositoryImpl)
	usergroupRepositoryImpl := repository5.NewUserGroupRepositoryImpl(db, sugaredLogger)
	userGroupMappingRepositoryImpl := repository5.NewUserGroupMappingRepositoryImpl(db, sugaredLogger)
	userGroupServiceImpl := user.NewUserGroupServiceImpl(sugaredLogger, usergroupRepositoryImpl, userRepositoryImpl, userGroupMappingRepositoryImpl)
	userServiceImpl := user.NewUserServiceImpl(userAuthRepositoryImpl, sugaredLogger, userRepositoryImpl, roleGroupRepositoryImpl, sessionManager, userCommonServiceImpl, userAuditServiceImpl, globalAuthorisationConfigServiceImpl, roleGroupServiceImpl, userAutoAssignGroupMapRepositoryImpl, enterpriseEnforcerImpl, timeWindowServiceImpl, userGroupServiceImpl, userOperationAuditServiceImpl)
	ssoLoginRepositoryImpl := sso.NewSSOLoginRepositoryImpl(db, sugaredLogger)
	k8sRuntimeConfig, err := k8s.GetRuntimeConfig()
	if err != nil {
		return nil, err
	}
	sshTunnelWrapperServiceImpl, err := ssh.NewSSHTunnelWrapperServiceImpl(sugaredLogger)
	if err != nil {
		return nil, err
	}
	k8sUtilExtended, err := k8s2.NewK8sUtilExtended(sugaredLogger, k8sRuntimeConfig, sshTunnelWrapperServiceImpl)
	if err != nil {
		return nil, err
	}
	environmentVariables, err := util2.GetEnvironmentVariables()
	if err != nil {
		return nil, err
	}
	selfRegistrationRolesRepositoryImpl := repository2.NewSelfRegistrationRolesRepositoryImpl(db, sugaredLogger)
	userSelfRegistrationServiceImpl := user.NewUserSelfRegistrationServiceImpl(sugaredLogger, selfRegistrationRolesRepositoryImpl, userServiceImpl, globalAuthorisationConfigServiceImpl)
	userAuthOidcHelperImpl, err := authentication.NewUserAuthOidcHelperImpl(sugaredLogger, userSelfRegistrationServiceImpl, dexConfig, settings, sessionManager)
	if err != nil {
		return nil, err
	}
	ssoLoginServiceImpl := sso.NewSSOLoginServiceImpl(sugaredLogger, ssoLoginRepositoryImpl, k8sUtilExtended, environmentVariables, userAuthOidcHelperImpl, globalAuthorisationConfigServiceImpl)
	ssoLoginRestHandlerImpl := sso2.NewSsoLoginRestHandlerImpl(validate, sugaredLogger, enterpriseEnforcerImpl, userServiceImpl, ssoLoginServiceImpl)
	ssoLoginRouterImpl := sso2.NewSsoLoginRouterImpl(ssoLoginRestHandlerImpl)
	teamRepositoryImpl := repository6.NewTeamRepositoryImpl(db)
	loginService := middleware.NewUserLogin(sessionManager, k8sClient)
	userAuthServiceImpl := user.NewUserAuthServiceImpl(userAuthRepositoryImpl, sessionManager, loginService, sugaredLogger, userRepositoryImpl, roleGroupRepositoryImpl, userServiceImpl)
	teamReadServiceImpl := read.NewTeamReadService(sugaredLogger, teamRepositoryImpl)
	teamServiceImpl := team.NewTeamServiceImpl(sugaredLogger, teamRepositoryImpl, userAuthServiceImpl, teamReadServiceImpl)
	clusterRepositoryImpl := repository7.NewClusterRepositoryImpl(db, sugaredLogger)
	syncMap := informer.NewGlobalMapClusterNamespace()
	k8sInformerFactoryImpl := informer.NewK8sInformerFactoryImpl(sugaredLogger, syncMap, k8sUtilExtended)
	remoteConnectionRepositoryImpl := repository8.NewRemoteConnectionRepositoryImpl(db, sugaredLogger)
	dockerArtifactStoreRepositoryImpl := repository9.NewDockerArtifactStoreRepositoryImpl(db)
	remoteConnectionServiceImpl := remoteConnection.NewRemoteConnectionServiceImpl(sugaredLogger, remoteConnectionRepositoryImpl, dockerArtifactStoreRepositoryImpl)
	cronLoggerImpl := cron.NewCronLoggerImpl(sugaredLogger)
	clusterReadServiceImpl := read2.NewClusterReadServiceImpl(sugaredLogger, clusterRepositoryImpl)
	infrastructureInstallationRepositoryImpl := repository10.NewInfrastructureInstallationRepositoryImpl(db, sugaredLogger)
	infrastructureInstallationVersionsRepositoryImpl := repository10.NewInfrastructureInstallationVersionsRepositoryImpl(db, sugaredLogger)
	installationReadServiceImpl := read3.NewInstallationReadServiceImpl(sugaredLogger, infrastructureInstallationRepositoryImpl, infrastructureInstallationVersionsRepositoryImpl)
	clusterCategoryRepositoryImpl := repository7.NewClusterCategoryRepositoryImpl(db, sugaredLogger)
	clusterCategoryMappingRepositoryImpl := repository7.NewClusterCategoryMappingRepositoryImpl(db, sugaredLogger)
	environmentCategoryMappingRepositoryImpl := repository11.NewEnvironmentCategoryMappingRepositoryImpl(db, sugaredLogger)
	clusterCategoryServiceImpl, err := cluster.NewClusterCategoryServiceImpl(sugaredLogger, clusterCategoryRepositoryImpl, clusterCategoryMappingRepositoryImpl, clusterRepositoryImpl, userAuthServiceImpl, environmentCategoryMappingRepositoryImpl)
	if err != nil {
		return nil, err
	}
	clusterServiceImpl, err := cluster.NewClusterServiceImpl(clusterRepositoryImpl, sugaredLogger, k8sUtilExtended, k8sInformerFactoryImpl, userAuthRepositoryImpl, userRepositoryImpl, roleGroupRepositoryImpl, globalAuthorisationConfigServiceImpl, userServiceImpl, remoteConnectionServiceImpl, environmentVariables, cronLoggerImpl, clusterReadServiceImpl, installationReadServiceImpl, clusterCategoryServiceImpl)
	if err != nil {
		return nil, err
	}
	appStatusRepositoryImpl := appStatus.NewAppStatusRepositoryImpl(db, sugaredLogger)
	environmentRepositoryImpl := repository11.NewEnvironmentRepositoryImpl(db, sugaredLogger, appStatusRepositoryImpl)
	attributesRepositoryImpl := repository12.NewAttributesRepositoryImpl(db)
	devtronResourceSchemaRepositoryImpl := repository13.NewDevtronResourceSchemaRepositoryImpl(db, sugaredLogger)
	devtronResourceObjectRepositoryImpl := repository13.NewDevtronResourceObjectRepositoryImpl(sugaredLogger, db)
	devtronResourceRepositoryImpl := repository13.NewDevtronResourceRepositoryImpl(db, sugaredLogger)
	dtResObjDepRelationsRepositoryImpl := repository13.NewDtResObjDepRelationsRepositoryImpl(sugaredLogger, db)
	templateRepositoryImpl := repository13.NewTemplateRepositoryImpl(sugaredLogger, db)
	readServiceImpl, err := read4.NewReadServiceImpl(sugaredLogger, devtronResourceObjectRepositoryImpl, devtronResourceRepositoryImpl, devtronResourceSchemaRepositoryImpl, dtResObjDepRelationsRepositoryImpl, templateRepositoryImpl, userRepositoryImpl)
	if err != nil {
		return nil, err
	}
	devtronResourceObjectAuditRepositoryImpl := repository13.NewDevtronResourceObjectAuditRepositoryImpl(sugaredLogger, db)
	objectAuditServiceImpl := audit.NewObjectAuditServiceImpl(sugaredLogger, devtronResourceObjectAuditRepositoryImpl)
	internalProcessingServiceImpl := in.NewInternalProcessingServiceImpl(sugaredLogger, devtronResourceSchemaRepositoryImpl, devtronResourceObjectRepositoryImpl, readServiceImpl, objectAuditServiceImpl, dtResObjDepRelationsRepositoryImpl)
	httpClient := util.NewHttpClient()
	grafanaClientConfig, err := grafana.GetGrafanaClientConfig()
	if err != nil {
		return nil, err
	}
	attributesServiceImpl := attributes.NewAttributesServiceImpl(sugaredLogger, attributesRepositoryImpl)
	grafanaClientImpl := grafana.NewGrafanaClientImpl(sugaredLogger, httpClient, grafanaClientConfig, attributesServiceImpl)
	environmentCategoryServiceImpl, err := environment.NewEnvironmentCategoryServiceImpl(sugaredLogger, environmentCategoryMappingRepositoryImpl, environmentRepositoryImpl, userAuthServiceImpl)
	if err != nil {
		return nil, err
	}
	environmentServiceImpl := environment.NewEnvironmentServiceImpl(environmentRepositoryImpl, clusterServiceImpl, sugaredLogger, k8sUtilExtended, k8sInformerFactoryImpl, userAuthServiceImpl, attributesRepositoryImpl, internalProcessingServiceImpl, clusterReadServiceImpl, grafanaClientImpl, environmentCategoryServiceImpl)
	chartRepoRepositoryImpl := chartRepoRepository.NewChartRepoRepositoryImpl(db)
	acdAuthConfig, err := util3.GetACDAuthConfig()
	if err != nil {
		return nil, err
	}
	serverEnvConfigServerEnvConfig, err := serverEnvConfig.ParseServerEnvConfig()
	if err != nil {
		return nil, err
	}
	repositoryCredsK8sClientImpl := repoCredsK8sClient.NewRepositoryCredsK8sClientImpl(sugaredLogger, k8sUtilExtended)
	beanConfig, err := bean.GetConfig()
	if err != nil {
		return nil, err
	}
	argoCDConfigGetterImpl := config.NewArgoCDConfigGetter(beanConfig, environmentVariables, acdAuthConfig, clusterReadServiceImpl, sugaredLogger, k8sUtilExtended)
	argoClientWrapperServiceEAImpl := argocdServer.NewArgoClientWrapperServiceEAImpl(sugaredLogger, repositoryCredsK8sClientImpl, argoCDConfigGetterImpl)
	chartRepositoryServiceImpl := chartRepo.NewChartRepositoryServiceImpl(sugaredLogger, chartRepoRepositoryImpl, k8sUtilExtended, acdAuthConfig, httpClient, serverEnvConfigServerEnvConfig, argoClientWrapperServiceEAImpl, clusterReadServiceImpl)
	installedAppRepositoryImpl := repository14.NewInstalledAppRepositoryImpl(sugaredLogger, db)
	helmClientConfig, err := gRPC.GetConfig()
	if err != nil {
		return nil, err
	}
	configuration, err := grpc.GetConfiguration()
	if err != nil {
		return nil, err
	}
	helmAppClientImpl := gRPC.NewHelmAppClientImpl(sugaredLogger, helmClientConfig, configuration)
	pumpImpl := connector.NewPumpImpl(sugaredLogger)
	appRepositoryImpl := app.NewAppRepositoryImpl(db, sugaredLogger)
	installedAppReadServiceEAImpl := read5.NewInstalledAppReadServiceEAImpl(sugaredLogger, installedAppRepositoryImpl)
	dbMigrationServiceImpl := dbMigration.NewDbMigrationServiceImpl(sugaredLogger, appRepositoryImpl, installedAppReadServiceEAImpl)
	enforcerUtilHelmImpl := rbac.NewEnforcerUtilHelmImpl(sugaredLogger, clusterRepositoryImpl, appRepositoryImpl, installedAppRepositoryImpl, dbMigrationServiceImpl, teamReadServiceImpl)
	serverDataStoreServerDataStore := serverDataStore.InitServerDataStore()
	appStoreApplicationVersionRepositoryImpl := appStoreDiscoverRepository.NewAppStoreApplicationVersionRepositoryImpl(sugaredLogger, db)
	pipelineRepositoryImpl := pipelineConfig.NewPipelineRepositoryImpl(db, sugaredLogger)
	helmReleaseConfig, err := service.GetHelmReleaseConfig()
	if err != nil {
		return nil, err
	}
	portForwardManagerImpl, err := proxy.NewPortForwardManagerImpl(sugaredLogger, k8sUtilExtended)
	if err != nil {
		return nil, err
	}
	interClusterServiceCommunicationHandlerImpl, err := proxy.NewInterClusterServiceCommunicationHandlerImpl(sugaredLogger, portForwardManagerImpl)
	if err != nil {
		return nil, err
	}
	scoopClientGetterImpl, err := scoop.NewScoopClientGetter(environmentServiceImpl, sugaredLogger, clusterReadServiceImpl, k8sUtilExtended, interClusterServiceCommunicationHandlerImpl)
	if err != nil {
		return nil, err
	}
	helmAppReadServiceImpl := read6.NewHelmAppReadServiceImpl(sugaredLogger, clusterReadServiceImpl)
	installedAppVersionHistoryRepositoryImpl := repository14.NewInstalledAppVersionHistoryRepositoryImpl(sugaredLogger, db)
	repositoryImpl := deploymentConfig.NewRepositoryImpl(db)
	transactionUtilImpl := sql.NewTransactionUtilImpl(db)
	chartRepositoryImpl := chartRepoRepository.NewChartRepository(db, transactionUtilImpl)
	envConfigOverrideRepositoryImpl := chartConfig.NewEnvConfigOverrideRepository(db)
	mergeUtil := configUtil.MergeUtil{
		Logger: sugaredLogger,
	}
	envConfigOverrideReadServiceImpl := read7.NewEnvConfigOverrideReadServiceImpl(sugaredLogger, envConfigOverrideRepositoryImpl, environmentRepositoryImpl, chartRepositoryImpl, mergeUtil)
	chartRefRepositoryImpl := chartRepoRepository.NewChartRefRepositoryImpl(db)
	deploymentConfigReadServiceImpl := read8.NewDeploymentConfigReadServiceImpl(sugaredLogger, repositoryImpl, environmentVariables, chartRepositoryImpl, pipelineRepositoryImpl, appRepositoryImpl, environmentRepositoryImpl, envConfigOverrideReadServiceImpl)
	deploymentConfigServiceImpl := common.NewDeploymentConfigServiceImpl(repositoryImpl, sugaredLogger, chartRepositoryImpl, pipelineRepositoryImpl, appRepositoryImpl, installedAppReadServiceEAImpl, environmentVariables, envConfigOverrideReadServiceImpl, environmentRepositoryImpl, chartRefRepositoryImpl, deploymentConfigReadServiceImpl, acdAuthConfig)
	installedAppDBServiceImpl := EAMode.NewInstalledAppDBServiceImpl(sugaredLogger, installedAppRepositoryImpl, appRepositoryImpl, userServiceImpl, environmentServiceImpl, installedAppVersionHistoryRepositoryImpl, deploymentConfigServiceImpl)
	helmAppServiceImpl := service.NewHelmAppServiceImpl(sugaredLogger, clusterServiceImpl, helmAppClientImpl, pumpImpl, enforcerUtilHelmImpl, serverDataStoreServerDataStore, serverEnvConfigServerEnvConfig, appStoreApplicationVersionRepositoryImpl, environmentServiceImpl, pipelineRepositoryImpl, installedAppRepositoryImpl, appRepositoryImpl, clusterRepositoryImpl, k8sUtilExtended, helmReleaseConfig, remoteConnectionServiceImpl, acdAuthConfig, scoopClientGetterImpl, helmAppReadServiceImpl, installedAppDBServiceImpl, clusterReadServiceImpl)
	dockerRegistryIpsConfigRepositoryImpl := repository9.NewDockerRegistryIpsConfigRepositoryImpl(db)
	ociRegistryConfigRepositoryImpl := repository9.NewOCIRegistryConfigRepositoryImpl(db)
	dockerRegistryConfigImpl := pipeline.NewDockerRegistryConfigImpl(sugaredLogger, helmAppServiceImpl, dockerArtifactStoreRepositoryImpl, dockerRegistryIpsConfigRepositoryImpl, ociRegistryConfigRepositoryImpl, remoteConnectionServiceImpl, remoteConnectionRepositoryImpl, argoClientWrapperServiceEAImpl)
	deleteServiceImpl := delete2.NewDeleteServiceImpl(sugaredLogger, teamServiceImpl, clusterServiceImpl, environmentServiceImpl, chartRepositoryServiceImpl, installedAppRepositoryImpl, dockerRegistryConfigImpl, dockerArtifactStoreRepositoryImpl, k8sInformerFactoryImpl, infrastructureInstallationRepositoryImpl, k8sUtilExtended, clusterCategoryServiceImpl, environmentCategoryServiceImpl, appRepositoryImpl)
	teamRestHandlerImpl := team2.NewTeamRestHandlerImpl(sugaredLogger, teamServiceImpl, userServiceImpl, enterpriseEnforcerImpl, validate, userAuthServiceImpl, deleteServiceImpl)
	teamRouterImpl := team2.NewTeamRouterImpl(teamRestHandlerImpl)
	userAuthHandlerImpl := user2.NewUserAuthHandlerImpl(userAuthServiceImpl, validate, sugaredLogger, enterpriseEnforcerImpl)
	userAuthRouterImpl := user2.NewUserAuthRouterImpl(sugaredLogger, userAuthHandlerImpl, userAuthOidcHelperImpl)
	policiesCleanUpRepositoryImpl := repository2.NewPoliciesCleanUpRepositoryImpl(db, sugaredLogger)
	cleanUpPoliciesServiceImpl := user.NewCleanUpPoliciesServiceImpl(userAuthRepositoryImpl, sugaredLogger, userRepositoryImpl, roleGroupRepositoryImpl, policiesCleanUpRepositoryImpl, cronLoggerImpl)
	ciPipelineRepositoryImpl := pipelineConfig.NewCiPipelineRepositoryImpl(db, sugaredLogger, transactionUtilImpl)
	enforcerUtilImpl := rbac.NewEnforcerUtilImpl(sugaredLogger, appRepositoryImpl, environmentRepositoryImpl, pipelineRepositoryImpl, ciPipelineRepositoryImpl, clusterRepositoryImpl, enterpriseEnforcerImpl, devtronResourceObjectRepositoryImpl, dtResObjDepRelationsRepositoryImpl, dbMigrationServiceImpl, teamReadServiceImpl)
	commonEnforcementUtilImpl := commonEnforcementFunctionsUtil.NewCommonEnforcementUtilImpl(enterpriseEnforcerImpl, enforcerUtilImpl, sugaredLogger, userServiceImpl, userCommonServiceImpl)
	userRestHandlerImpl := user2.NewUserRestHandlerImpl(userServiceImpl, validate, sugaredLogger, enterpriseEnforcerImpl, roleGroupServiceImpl, userCommonServiceImpl, cleanUpPoliciesServiceImpl, commonEnforcementUtilImpl)
	userRouterImpl := user2.NewUserRouterImpl(userRestHandlerImpl)
	restHandlerImpl := userGroup.NewUserGroupRestHandlerImpl(userServiceImpl, validate, sugaredLogger, enterpriseEnforcerImpl, userGroupServiceImpl)
	routerImpl := userGroup.NewUserGroupRouterImpl(restHandlerImpl)
	moduleRepositoryImpl := moduleRepo.NewModuleRepositoryImpl(db)
	moduleReadServiceImpl := read9.NewModuleReadServiceImpl(sugaredLogger, moduleRepositoryImpl)
	commonBaseServiceImpl := commonService.NewCommonBaseServiceImpl(sugaredLogger, environmentVariables, moduleReadServiceImpl)
	commonRestHandlerImpl := restHandler.NewCommonRestHandlerImpl(sugaredLogger, userServiceImpl, commonBaseServiceImpl)
	commonRouterImpl := router.NewCommonRouterImpl(commonRestHandlerImpl)
	genericNoteRepositoryImpl := repository15.NewGenericNoteRepositoryImpl(db, transactionUtilImpl)
	genericNoteHistoryRepositoryImpl := repository15.NewGenericNoteHistoryRepositoryImpl(db, transactionUtilImpl)
	genericNoteHistoryServiceImpl := genericNotes.NewGenericNoteHistoryServiceImpl(genericNoteHistoryRepositoryImpl, sugaredLogger)
	genericNoteServiceImpl := genericNotes.NewGenericNoteServiceImpl(genericNoteRepositoryImpl, genericNoteHistoryServiceImpl, userRepositoryImpl, sugaredLogger)
	clusterDescriptionRepositoryImpl := repository7.NewClusterDescriptionRepositoryImpl(db, sugaredLogger)
	clusterDescriptionServiceImpl := cluster.NewClusterDescriptionServiceImpl(clusterDescriptionRepositoryImpl, userRepositoryImpl, sugaredLogger)
	clusterRbacServiceImpl := rbac2.NewClusterRbacServiceImpl(environmentServiceImpl, enterpriseEnforcerImpl, enforcerUtilImpl, clusterServiceImpl, sugaredLogger, userServiceImpl, clusterReadServiceImpl)
	panelRepositoryImpl := repo.NewPanelRepositoryImpl(db)
	panelServiceImpl := panel.NewPanelServiceImpl(panelRepositoryImpl, clusterServiceImpl)
	clusterRestHandlerImpl := cluster2.NewClusterRestHandlerImpl(clusterServiceImpl, genericNoteServiceImpl, clusterDescriptionServiceImpl, sugaredLogger, userServiceImpl, validate, enterpriseEnforcerImpl, deleteServiceImpl, environmentServiceImpl, clusterRbacServiceImpl, panelServiceImpl, attributesServiceImpl, clusterReadServiceImpl, clusterCategoryServiceImpl)
	clusterRouterImpl := cluster2.NewClusterRouterImpl(clusterRestHandlerImpl)
	dashboardConfig, err := dashboard.GetConfig()
	if err != nil {
		return nil, err
	}
	dashboardRouterImpl, err := dashboard.NewDashboardRouterImpl(sugaredLogger, dashboardConfig)
	if err != nil {
		return nil, err
	}
	gitOpsConfigRepositoryImpl := repository12.NewGitOpsConfigRepositoryImpl(sugaredLogger, db)
	gitOpsConfigReadServiceImpl := config2.NewGitOpsConfigReadServiceImpl(sugaredLogger, gitOpsConfigRepositoryImpl, userServiceImpl, environmentVariables, moduleReadServiceImpl)
	deploymentTypeOverrideServiceImpl := providerConfig.NewDeploymentTypeOverrideServiceImpl(sugaredLogger, environmentVariables, attributesServiceImpl, environmentServiceImpl)
	chartTemplateServiceImpl := util.NewChartTemplateServiceImpl(sugaredLogger)
	appStoreDeploymentCommonServiceImpl := appStoreDeploymentCommon.NewAppStoreDeploymentCommonServiceImpl(sugaredLogger, appStoreApplicationVersionRepositoryImpl, chartTemplateServiceImpl, userServiceImpl, helmAppServiceImpl, installedAppDBServiceImpl)
	eaModeDeploymentServiceImpl := deployment.NewEAModeDeploymentServiceImpl(sugaredLogger, helmAppServiceImpl, helmAppReadServiceImpl, appStoreApplicationVersionRepositoryImpl, helmAppClientImpl, installedAppRepositoryImpl, ociRegistryConfigRepositoryImpl, appStoreDeploymentCommonServiceImpl, remoteConnectionServiceImpl)
	appStoreValidatorEnterpriseImpl := service2.NewAppStoreValidatorEnterpriseImpl(sugaredLogger)
	appStoreDeploymentDBServiceImpl := service2.NewAppStoreDeploymentDBServiceImpl(sugaredLogger, installedAppRepositoryImpl, appStoreApplicationVersionRepositoryImpl, appRepositoryImpl, environmentServiceImpl, installedAppVersionHistoryRepositoryImpl, environmentVariables, gitOpsConfigReadServiceImpl, deploymentTypeOverrideServiceImpl, eaModeDeploymentServiceImpl, appStoreValidatorEnterpriseImpl, installedAppDBServiceImpl, deploymentConfigServiceImpl, clusterReadServiceImpl)
	chartGroupDeploymentRepositoryImpl := repository16.NewChartGroupDeploymentRepositoryImpl(db, sugaredLogger)
	acdConfig, err := argocdServer.GetACDDeploymentConfig()
	if err != nil {
		return nil, err
	}
	deletePostProcessorEnterpriseImpl := service2.NewDeletePostProcessorEnterpriseImpl(sugaredLogger, internalProcessingServiceImpl)
	chartScanPublishServiceImpl := out.NewChartScanPublishServiceImplEA()
	appStoreDeploymentServiceImpl := service2.NewAppStoreDeploymentServiceImpl(sugaredLogger, installedAppRepositoryImpl, installedAppDBServiceImpl, appStoreDeploymentDBServiceImpl, chartGroupDeploymentRepositoryImpl, appStoreApplicationVersionRepositoryImpl, appRepositoryImpl, eaModeDeploymentServiceImpl, eaModeDeploymentServiceImpl, eaModeDeploymentServiceImpl, environmentServiceImpl, helmAppServiceImpl, installedAppVersionHistoryRepositoryImpl, environmentVariables, acdConfig, gitOpsConfigReadServiceImpl, deletePostProcessorEnterpriseImpl, appStoreValidatorEnterpriseImpl, deploymentConfigServiceImpl, chartScanPublishServiceImpl, ociRegistryConfigRepositoryImpl, remoteConnectionServiceImpl)
	resourceTreeServiceV2Impl := appDetails.NewResourceTreeServiceV2Impl(sugaredLogger, scoopClientGetterImpl, helmAppClientImpl, clusterReadServiceImpl)
	fluxApplicationServiceImpl := fluxApplication.NewFluxApplicationServiceImpl(sugaredLogger, helmAppReadServiceImpl, clusterServiceImpl, helmAppClientImpl, pumpImpl, resourceTreeServiceV2Impl, acdAuthConfig, scoopClientGetterImpl, pipelineRepositoryImpl, installedAppRepositoryImpl)
	k8sResourceHistoryRepositoryImpl := repository17.NewK8sResourceHistoryRepositoryImpl(db, sugaredLogger)
	k8sResourceHistoryServiceImpl := kubernetesResourceAuditLogs.Newk8sResourceHistoryServiceImpl(k8sResourceHistoryRepositoryImpl, sugaredLogger, appRepositoryImpl, environmentRepositoryImpl)
	argoApplicationConfigServiceImpl := config3.NewArgoApplicationConfigServiceImpl(sugaredLogger, k8sUtilExtended, clusterRepositoryImpl)
	k8sCommonServiceImpl := k8s3.NewK8sCommonServiceImpl(sugaredLogger, k8sUtilExtended, k8sResourceHistoryServiceImpl, argoApplicationConfigServiceImpl, clusterReadServiceImpl)
	ephemeralContainersRepositoryImpl := repository7.NewEphemeralContainersRepositoryImpl(db, transactionUtilImpl)
	ephemeralContainerServiceImpl := cluster.NewEphemeralContainerServiceImpl(ephemeralContainersRepositoryImpl, sugaredLogger)
	terminalSessionHandlerImpl := terminal.NewTerminalSessionHandlerImpl(environmentServiceImpl, sugaredLogger, k8sUtilExtended, ephemeralContainerServiceImpl, argoApplicationConfigServiceImpl, clusterReadServiceImpl)
	evaluatorServiceImpl := cel.NewCELServiceImpl(sugaredLogger)
	deploymentWindowServiceImpl := deploymentWindow.NewDeploymentWindowServiceImplEA()
	imageScanServiceImpl := imageScanning.NewImageScanServiceImplEA()
	workloadListConfig, err := resources.NewWorkloadListConfig()
	if err != nil {
		return nil, err
	}
	serviceImpl := resources.NewServiceImpl(sugaredLogger, k8sUtilExtended, workloadListConfig)
	triggerKrrJobImpl, err := job.NewTriggerKrrJobImpl(sugaredLogger, k8sUtilExtended, clusterReadServiceImpl, sqlConfig, cronLoggerImpl, acdAuthConfig, environmentVariables)
	if err != nil {
		return nil, err
	}
	krrServiceImpl := krr.NewServiceImpl(sugaredLogger, triggerKrrJobImpl)
	krrScanRequestRepositoryImpl := repository18.NewKrrScanRequestRepositoryImpl(db)
	krrScanHistoryRepositoryImpl := repository18.NewKrrScanHistoryRepositoryImpl(db)
	krrScanServiceImpl, err := read10.NewKRRScanReadServiceImpl(sugaredLogger, userServiceImpl, krrScanRequestRepositoryImpl, krrScanHistoryRepositoryImpl)
	if err != nil {
		return nil, err
	}
	argoApplicationReadServiceImpl := read11.NewArgoApplicationReadServiceImpl(sugaredLogger, clusterRepositoryImpl, k8sUtilExtended, helmAppClientImpl, helmAppServiceImpl)
	k8sApplicationServiceImpl, err := application.NewK8sApplicationServiceImpl(sugaredLogger, clusterServiceImpl, pumpImpl, helmAppServiceImpl, k8sUtilExtended, acdAuthConfig, k8sResourceHistoryServiceImpl, k8sCommonServiceImpl, terminalSessionHandlerImpl, ephemeralContainerServiceImpl, ephemeralContainersRepositoryImpl, environmentRepositoryImpl, clusterRepositoryImpl, evaluatorServiceImpl, interClusterServiceCommunicationHandlerImpl, deploymentWindowServiceImpl, scoopClientGetterImpl, imageScanServiceImpl, fluxApplicationServiceImpl, enterpriseEnforcerImpl, enforcerUtilImpl, enforcerUtilHelmImpl, serviceImpl, krrServiceImpl, krrScanServiceImpl, triggerKrrJobImpl, argoApplicationReadServiceImpl, clusterReadServiceImpl)
	if err != nil {
		return nil, err
	}
	argoApplicationServiceImpl := argoApplication.NewArgoApplicationServiceImpl(sugaredLogger, clusterRepositoryImpl, k8sUtilExtended, helmAppClientImpl, helmAppServiceImpl, k8sApplicationServiceImpl, argoApplicationConfigServiceImpl, deploymentConfigServiceImpl)
	helmAppRestHandlerImpl := client3.NewHelmAppRestHandlerImpl(sugaredLogger, helmAppServiceImpl, enterpriseEnforcerImpl, clusterServiceImpl, enforcerUtilHelmImpl, appStoreDeploymentServiceImpl, installedAppDBServiceImpl, userServiceImpl, attributesServiceImpl, serverEnvConfigServerEnvConfig, fluxApplicationServiceImpl, argoApplicationServiceImpl)
	helmAppRouterImpl := client3.NewHelmAppRouterImpl(helmAppRestHandlerImpl)
	environmentReadServiceImpl := read12.NewEnvironmentReadServiceImpl(sugaredLogger, environmentRepositoryImpl, environmentCategoryServiceImpl)
	environmentRestHandlerImpl := cluster2.NewEnvironmentRestHandlerImpl(environmentServiceImpl, environmentReadServiceImpl, sugaredLogger, userServiceImpl, validate, enterpriseEnforcerImpl, deleteServiceImpl, k8sUtilExtended, k8sCommonServiceImpl, commonEnforcementUtilImpl, environmentCategoryServiceImpl)
	environmentRouterImpl := cluster2.NewEnvironmentRouterImpl(environmentRestHandlerImpl)
	k8sApplicationRestHandlerImpl := application2.NewK8sApplicationRestHandlerImpl(sugaredLogger, k8sApplicationServiceImpl, pumpImpl, terminalSessionHandlerImpl, enterpriseEnforcerImpl, enforcerUtilHelmImpl, enforcerUtilImpl, helmAppServiceImpl, userServiceImpl, k8sCommonServiceImpl, validate, environmentVariables, fluxApplicationServiceImpl, argoApplicationReadServiceImpl, clusterReadServiceImpl, clusterRbacServiceImpl)
	k8sApplicationRouterImpl := application2.NewK8sApplicationRouterImpl(k8sApplicationRestHandlerImpl)
	chartRepositoryRestHandlerImpl := chartRepo2.NewChartRepositoryRestHandlerImpl(sugaredLogger, userServiceImpl, chartRepositoryServiceImpl, enterpriseEnforcerImpl, validate, deleteServiceImpl, attributesServiceImpl)
	chartRepositoryRouterImpl := chartRepo2.NewChartRepositoryRouterImpl(chartRepositoryRestHandlerImpl)
	appStoreServiceImpl := service3.NewAppStoreServiceImpl(sugaredLogger, appStoreApplicationVersionRepositoryImpl)
	appStoreRestHandlerImpl := appStoreDiscover.NewAppStoreRestHandlerImpl(sugaredLogger, userServiceImpl, appStoreServiceImpl, enterpriseEnforcerImpl)
	appStoreDiscoverRouterImpl := appStoreDiscover.NewAppStoreDiscoverRouterImpl(appStoreRestHandlerImpl)
	appStoreVersionValuesRepositoryImpl := appStoreValuesRepository.NewAppStoreVersionValuesRepositoryImpl(sugaredLogger, db)
	appStoreValuesServiceImpl := service4.NewAppStoreValuesServiceImpl(sugaredLogger, appStoreApplicationVersionRepositoryImpl, installedAppRepositoryImpl, installedAppReadServiceEAImpl, appStoreVersionValuesRepositoryImpl, userServiceImpl)
	appStoreValuesRestHandlerImpl := appStoreValues.NewAppStoreValuesRestHandlerImpl(sugaredLogger, userServiceImpl, appStoreValuesServiceImpl)
	appStoreValuesRouterImpl := appStoreValues.NewAppStoreValuesRouterImpl(appStoreValuesRestHandlerImpl)
	appStoreDeploymentRestHandlerImpl := appStoreDeployment.NewAppStoreDeploymentRestHandlerImpl(sugaredLogger, userServiceImpl, enterpriseEnforcerImpl, enforcerUtilImpl, enforcerUtilHelmImpl, appStoreDeploymentServiceImpl, appStoreDeploymentDBServiceImpl, validate, helmAppServiceImpl, installedAppDBServiceImpl, attributesServiceImpl, clusterReadServiceImpl)
	appStoreDeploymentRouterImpl := appStoreDeployment.NewAppStoreDeploymentRouterImpl(appStoreDeploymentRestHandlerImpl)
	chartProviderServiceImpl := chartProvider.NewChartProviderServiceImpl(sugaredLogger, chartRepoRepositoryImpl, chartRepositoryServiceImpl, dockerArtifactStoreRepositoryImpl, ociRegistryConfigRepositoryImpl)
	chartProviderRestHandlerImpl := chartProvider2.NewChartProviderRestHandlerImpl(sugaredLogger, userServiceImpl, validate, chartProviderServiceImpl, enterpriseEnforcerImpl)
	chartProviderRouterImpl := chartProvider2.NewChartProviderRouterImpl(chartProviderRestHandlerImpl)
	dockerRegRestHandlerImpl := restHandler.NewDockerRegRestHandlerImpl(dockerRegistryConfigImpl, sugaredLogger, chartProviderServiceImpl, userServiceImpl, validate, enterpriseEnforcerImpl, deleteServiceImpl)
	dockerRegRouterImpl := router.NewDockerRegRouterImpl(dockerRegRestHandlerImpl)
	posthogClient, err := telemetry.NewPosthogClient(sugaredLogger)
	if err != nil {
		return nil, err
	}
	ucidServiceImpl := ucid.NewServiceImpl(sugaredLogger, k8sUtilExtended, acdAuthConfig)
	providerIdentifierServiceImpl := providerIdentifier.NewProviderIdentifierServiceImpl(sugaredLogger)
	apiTokenSecretServiceImpl, err := apiToken.NewApiTokenSecretServiceImpl(sugaredLogger, attributesServiceImpl, apiTokenSecretStore)
	if err != nil {
		return nil, err
	}
	licenseAttributesRepositoryImpl := repository19.NewLicenseAttributesRepositoryImpl(db)
	licenseAttributesServiceImpl := attributes.NewLicenseAttributesServiceImpl(sugaredLogger, licenseAttributesRepositoryImpl)
	licenseRepoImpl, err := licenseClient.NewLicenseRepoImpl(sugaredLogger, db, ucidServiceImpl, apiTokenSecretServiceImpl, licenseAttributesServiceImpl, k8sUtilExtended)
	if err != nil {
		return nil, err
	}
	reminderThresholdConfig, err := env.GetThresholdReminderConfig()
	if err != nil {
		return nil, err
	}
	licenseManagerConfig, err := licenseClient.GetLicenseManagerConfig()
	if err != nil {
		return nil, err
	}
	licenseServiceImpl, err := licenseClient.NewLicenseServiceImpl(sugaredLogger, licenseRepoImpl, reminderThresholdConfig, licenseManagerConfig, cronLoggerImpl)
	if err != nil {
		return nil, err
	}
	userAttributesRepositoryImpl := repository12.NewUserAttributesRepositoryImpl(db)
	telemetryEventClientImpl, err := telemetry2.NewTelemetryEventClientImpl(sugaredLogger, httpClient, clusterServiceImpl, k8sUtilExtended, acdAuthConfig, userServiceImpl, attributesRepositoryImpl, ssoLoginServiceImpl, posthogClient, ucidServiceImpl, moduleRepositoryImpl, serverDataStoreServerDataStore, userAuditServiceImpl, helmAppClientImpl, providerIdentifierServiceImpl, cronLoggerImpl, environmentVariables, installedAppReadServiceEAImpl, licenseServiceImpl, userAttributesRepositoryImpl)
	if err != nil {
		return nil, err
	}
	dashboardTelemetryRestHandlerImpl := dashboardEvent.NewDashboardTelemetryRestHandlerImpl(sugaredLogger, telemetryEventClientImpl)
	dashboardTelemetryRouterImpl := dashboardEvent.NewDashboardTelemetryRouterImpl(dashboardTelemetryRestHandlerImpl)
	commonDeploymentRestHandlerImpl := appStoreDeployment.NewCommonDeploymentRestHandlerImpl(sugaredLogger, userServiceImpl, enterpriseEnforcerImpl, enforcerUtilImpl, enforcerUtilHelmImpl, appStoreDeploymentServiceImpl, installedAppDBServiceImpl, validate, helmAppServiceImpl, attributesServiceImpl)
	commonDeploymentRouterImpl := appStoreDeployment.NewCommonDeploymentRouterImpl(commonDeploymentRestHandlerImpl)
	externalLinkMonitoringToolRepositoryImpl := externalLink.NewExternalLinkMonitoringToolRepositoryImpl(db)
	externalLinkIdentifierMappingRepositoryImpl := externalLink.NewExternalLinkIdentifierMappingRepositoryImpl(db)
	externalLinkRepositoryImpl := externalLink.NewExternalLinkRepositoryImpl(db)
	externalLinkServiceImpl := externalLink.NewExternalLinkServiceImpl(sugaredLogger, externalLinkMonitoringToolRepositoryImpl, externalLinkIdentifierMappingRepositoryImpl, externalLinkRepositoryImpl)
	externalLinkRestHandlerImpl := externalLink2.NewExternalLinkRestHandlerImpl(sugaredLogger, externalLinkServiceImpl, userServiceImpl, enterpriseEnforcerImpl, enforcerUtilImpl)
	externalLinkRouterImpl := externalLink2.NewExternalLinkRouterImpl(externalLinkRestHandlerImpl)
	moduleActionAuditLogRepositoryImpl := module.NewModuleActionAuditLogRepositoryImpl(db)
	serverCacheServiceImpl, err := server.NewServerCacheServiceImpl(sugaredLogger, serverEnvConfigServerEnvConfig, serverDataStoreServerDataStore, helmAppServiceImpl)
	if err != nil {
		return nil, err
	}
	moduleEnvConfig, err := bean2.ParseModuleEnvConfig()
	if err != nil {
		return nil, err
	}
	moduleCacheServiceImpl, err := module.NewModuleCacheServiceImpl(sugaredLogger, k8sUtilExtended, moduleEnvConfig, serverEnvConfigServerEnvConfig, serverDataStoreServerDataStore, moduleRepositoryImpl, teamReadServiceImpl)
	if err != nil {
		return nil, err
	}
	moduleServiceHelperImpl := module.NewModuleServiceHelperImpl(serverEnvConfigServerEnvConfig)
	moduleResourceStatusRepositoryImpl := moduleRepo.NewModuleResourceStatusRepositoryImpl(db)
	moduleDataStoreModuleDataStore := moduleDataStore.InitModuleDataStore()
	moduleCronServiceImpl, err := module.NewModuleCronServiceImpl(sugaredLogger, moduleEnvConfig, moduleRepositoryImpl, serverEnvConfigServerEnvConfig, helmAppServiceImpl, moduleServiceHelperImpl, moduleResourceStatusRepositoryImpl, moduleDataStoreModuleDataStore, cronLoggerImpl)
	if err != nil {
		return nil, err
	}
	scanToolMetadataRepositoryImpl := repository20.NewScanToolMetadataRepositoryImpl(db, sugaredLogger)
	globalPluginRepositoryImpl := repository21.NewGlobalPluginRepository(sugaredLogger, db)
	pipelineStageRepositoryImpl := repository22.NewPipelineStageRepository(sugaredLogger, db)
	fileReferenceRepositoryImpl := repository23.NewFileReferenceRepositoryImpl(db)
	fileReferenceReaderImpl := read13.NewFileReferenceReaderImpl(sugaredLogger, fileReferenceRepositoryImpl)
	globalPluginServiceImpl := plugin.NewGlobalPluginService(sugaredLogger, globalPluginRepositoryImpl, pipelineStageRepositoryImpl, userServiceImpl, fileReferenceReaderImpl)
	scanToolMetadataServiceImpl := scanTool.NewScanToolMetadataServiceImpl(sugaredLogger, scanToolMetadataRepositoryImpl, globalPluginServiceImpl, transactionUtilImpl)
	moduleServiceImpl := module.NewModuleServiceImpl(sugaredLogger, serverEnvConfigServerEnvConfig, moduleRepositoryImpl, moduleActionAuditLogRepositoryImpl, helmAppServiceImpl, serverDataStoreServerDataStore, serverCacheServiceImpl, moduleCacheServiceImpl, moduleCronServiceImpl, moduleServiceHelperImpl, moduleResourceStatusRepositoryImpl, scanToolMetadataServiceImpl, environmentVariables, moduleEnvConfig)
	moduleRestHandlerImpl := module2.NewModuleRestHandlerImpl(sugaredLogger, moduleServiceImpl, userServiceImpl, enterpriseEnforcerImpl, validate)
	moduleRouterImpl := module2.NewModuleRouterImpl(moduleRestHandlerImpl)
	serverActionAuditLogRepositoryImpl := server.NewServerActionAuditLogRepositoryImpl(db)
	serverServiceImpl := server.NewServerServiceImpl(sugaredLogger, serverActionAuditLogRepositoryImpl, serverDataStoreServerDataStore, serverEnvConfigServerEnvConfig, helmAppServiceImpl, moduleRepositoryImpl, serverCacheServiceImpl)
	serverRestHandlerImpl := server2.NewServerRestHandlerImpl(sugaredLogger, serverServiceImpl, userServiceImpl, enterpriseEnforcerImpl, validate)
	serverRouterImpl := server2.NewServerRouterImpl(serverRestHandlerImpl)
	apiTokenRepositoryImpl := repository24.NewApiTokenRepositoryImpl(db)
	apiTokenServiceImpl, err := apiToken.NewApiTokenServiceImpl(sugaredLogger, apiTokenSecretServiceImpl, userServiceImpl, userAuditServiceImpl, apiTokenRepositoryImpl)
	if err != nil {
		return nil, err
	}
	apiTokenRestHandlerImpl := apiToken2.NewApiTokenRestHandlerImpl(sugaredLogger, apiTokenServiceImpl, userServiceImpl, enterpriseEnforcerImpl, validate)
	apiTokenRouterImpl := apiToken2.NewApiTokenRouterImpl(apiTokenRestHandlerImpl)
	k8sCapacityServiceImpl := capacity.NewK8sCapacityServiceImpl(sugaredLogger, k8sApplicationServiceImpl, k8sUtilExtended, k8sCommonServiceImpl)
	appStoreRepositoryImpl := appStoreDiscoverRepository.NewAppStoreRepositoryImpl(sugaredLogger, db)
	eksClusterInstaller := installer.NewEKSClusterInstaller(sugaredLogger, clusterRepositoryImpl, k8sUtilExtended, environmentVariables, clusterServiceImpl, infrastructureInstallationRepositoryImpl, deleteServiceImpl, environmentRepositoryImpl)
	installerFactoryImpl := installer.NewInstallerFactoryImpl(eksClusterInstaller)
	installationEventHandlerImpl, err := eventHandler.NewInstallationEventHandlerImpl(sugaredLogger, infrastructureInstallationRepositoryImpl, installerFactoryImpl, eaModeDeploymentServiceImpl, infrastructureInstallationVersionsRepositoryImpl, k8sUtilExtended)
	if err != nil {
		return nil, err
	}
	infrastructureInstallationPublishEAServiceImpl := publish.NewInfrastructureInstallationPublishEAServiceImpl(sugaredLogger, installationEventHandlerImpl)
	infrastructureInstallationServiceImpl, err := InfrastructureInstallationService.NewInfrastructureInstallationServiceImpl(sugaredLogger, infrastructureInstallationRepositoryImpl, clusterRepositoryImpl, environmentVariables, chartRepoRepositoryImpl, appStoreRepositoryImpl, appStoreApplicationVersionRepositoryImpl, environmentRepositoryImpl, environmentServiceImpl, teamReadServiceImpl, teamServiceImpl, cronLoggerImpl, k8sUtilExtended, installerFactoryImpl, eaModeDeploymentServiceImpl, infrastructureInstallationVersionsRepositoryImpl, infrastructureInstallationPublishEAServiceImpl)
	if err != nil {
		return nil, err
	}
	k8sCapacityRestHandlerImpl := capacity2.NewK8sCapacityRestHandlerImpl(sugaredLogger, k8sCapacityServiceImpl, userServiceImpl, enterpriseEnforcerImpl, clusterServiceImpl, environmentServiceImpl, clusterRbacServiceImpl, clusterReadServiceImpl, infrastructureInstallationServiceImpl, validate)
	k8sCapacityRouterImpl := capacity2.NewK8sCapacityRouterImpl(k8sCapacityRestHandlerImpl)
	webhookHelmServiceImpl := webhookHelm.NewWebhookHelmServiceImpl(sugaredLogger, helmAppServiceImpl, clusterServiceImpl, chartRepositoryServiceImpl, attributesServiceImpl)
	webhookHelmRestHandlerImpl := webhookHelm2.NewWebhookHelmRestHandlerImpl(sugaredLogger, webhookHelmServiceImpl, userServiceImpl, enterpriseEnforcerImpl, validate)
	webhookHelmRouterImpl := webhookHelm2.NewWebhookHelmRouterImpl(webhookHelmRestHandlerImpl)
	userAttributesServiceImpl := attributes.NewUserAttributesServiceImpl(sugaredLogger, userAttributesRepositoryImpl)
	userAttributesRestHandlerImpl := restHandler.NewUserAttributesRestHandlerImpl(sugaredLogger, enterpriseEnforcerImpl, userServiceImpl, userAttributesServiceImpl)
	userAttributesRouterImpl := router.NewUserAttributesRouterImpl(userAttributesRestHandlerImpl)
	telemetryRestHandlerImpl := restHandler.NewTelemetryRestHandlerImpl(sugaredLogger, telemetryEventClientImpl, enterpriseEnforcerImpl, userServiceImpl)
	telemetryRouterImpl := router.NewTelemetryRouterImpl(sugaredLogger, telemetryRestHandlerImpl)
	terminalAccessRepositoryImpl := repository12.NewTerminalAccessRepositoryImpl(db, sugaredLogger)
	userTerminalSessionConfig, err := clusterTerminalAccess.GetTerminalAccessConfig()
	if err != nil {
		return nil, err
	}
	userTerminalAccessServiceImpl, err := clusterTerminalAccess.NewUserTerminalAccessServiceImpl(sugaredLogger, terminalAccessRepositoryImpl, userTerminalSessionConfig, k8sCommonServiceImpl, terminalSessionHandlerImpl, k8sCapacityServiceImpl, k8sUtilExtended, cronLoggerImpl)
	if err != nil {
		return nil, err
	}
	userTerminalAccessRestHandlerImpl := terminal2.NewUserTerminalAccessRestHandlerImpl(sugaredLogger, userTerminalAccessServiceImpl, enterpriseEnforcerImpl, userServiceImpl, validate, clusterRbacServiceImpl)
	userTerminalAccessRouterImpl := terminal2.NewUserTerminalAccessRouterImpl(userTerminalAccessRestHandlerImpl)
	attributesRestHandlerImpl := restHandler.NewAttributesRestHandlerImpl(sugaredLogger, enterpriseEnforcerImpl, userServiceImpl, attributesServiceImpl)
	attributesRouterImpl := router.NewAttributesRouterImpl(attributesRestHandlerImpl)
	appLabelRepositoryImpl := pipelineConfig.NewAppLabelRepositoryImpl(db)
	crudOperationServiceConfig, err := app2.GetCrudOperationServiceConfig()
	if err != nil {
		return nil, err
	}
	materialRepositoryImpl := repository25.NewMaterialRepositoryImpl(db)
	gitMaterialReadServiceImpl := read14.NewGitMaterialReadServiceImpl(sugaredLogger, materialRepositoryImpl)
	appCrudOperationServiceImpl := app2.NewAppCrudOperationServiceImpl(appLabelRepositoryImpl, sugaredLogger, appRepositoryImpl, userRepositoryImpl, installedAppRepositoryImpl, teamRepositoryImpl, genericNoteServiceImpl, installedAppDBServiceImpl, crudOperationServiceConfig, dbMigrationServiceImpl, gitMaterialReadServiceImpl, readServiceImpl)
	appInfoRestHandlerImpl := appInfo.NewAppInfoRestHandlerImpl(sugaredLogger, appCrudOperationServiceImpl, userServiceImpl, validate, enforcerUtilImpl, enterpriseEnforcerImpl, helmAppServiceImpl, enforcerUtilHelmImpl, genericNoteServiceImpl, commonEnforcementUtilImpl)
	appInfoRouterImpl := appInfo2.NewAppInfoRouterImpl(sugaredLogger, appInfoRestHandlerImpl)
	appFilteringRestHandlerImpl := appList.NewAppFilteringRestHandlerImpl(sugaredLogger, enterpriseEnforcerImpl, userServiceImpl, clusterServiceImpl, environmentServiceImpl, teamReadServiceImpl)
	appFilteringRouterImpl := appList2.NewAppFilteringRouterImpl(appFilteringRestHandlerImpl)
	appRouterEAModeImpl := app3.NewAppRouterEAModeImpl(appInfoRouterImpl, appFilteringRouterImpl)
	rbacPolicyResourceDetailRepositoryImpl := repository2.NewRbacPolicyResourceDetailRepositoryImpl(sugaredLogger, db)
	rbacRoleResourceDetailRepositoryImpl := repository2.NewRbacRoleResourceDetailRepositoryImpl(sugaredLogger, db)
	rbacRoleAuditRepositoryImpl := repository2.NewRbacRoleAuditRepositoryImpl(sugaredLogger, db)
	rbacRoleAuditServiceImpl := user.NewRbacRoleAuditServiceImpl(sugaredLogger, rbacRoleAuditRepositoryImpl)
	rbacRoleServiceImpl := user.NewRbacRoleServiceImpl(sugaredLogger, rbacPolicyResourceDetailRepositoryImpl, rbacRoleResourceDetailRepositoryImpl, rbacRoleDataRepositoryImpl, rbacPolicyDataRepositoryImpl, rbacDataCacheFactoryImpl, userAuthRepositoryImpl, userCommonServiceImpl, rbacRoleAuditServiceImpl)
	defaultRbacRoleDataRepositoryImpl := repository2.NewDefaultRbacRoleDataRepositoryImpl(sugaredLogger, db)
	defaultRbacRoleServiceImpl := user.NewDefaultRbacRoleServiceImpl(sugaredLogger, defaultRbacRoleDataRepositoryImpl, rbacRoleServiceImpl)
	rbacRoleRestHandlerImpl := user2.NewRbacRoleHandlerImpl(sugaredLogger, validate, rbacRoleServiceImpl, userServiceImpl, enterpriseEnforcerImpl, enforcerUtilImpl, defaultRbacRoleServiceImpl, commonEnforcementUtilImpl)
	rbacRoleRouterImpl := user2.NewRbacRoleRouterImpl(sugaredLogger, validate, rbacRoleRestHandlerImpl)
	authorisationConfigRestHandlerImpl := globalConfig.NewGlobalAuthorisationConfigRestHandlerImpl(validate, sugaredLogger, enterpriseEnforcerImpl, userServiceImpl, globalAuthorisationConfigServiceImpl, userCommonServiceImpl, commonEnforcementUtilImpl)
	authorisationConfigRouterImpl := globalConfig.NewGlobalConfigAuthorisationConfigRouterImpl(authorisationConfigRestHandlerImpl)
	argoApplicationRestHandlerImpl := argoApplication2.NewArgoApplicationRestHandlerImpl(argoApplicationServiceImpl, argoApplicationReadServiceImpl, sugaredLogger, enterpriseEnforcerImpl)
	argoApplicationRouterImpl := argoApplication2.NewArgoApplicationRouterImpl(argoApplicationRestHandlerImpl)
	fluxApplicationRestHandlerImpl := fluxApplication2.NewFluxApplicationRestHandlerImpl(fluxApplicationServiceImpl, sugaredLogger, enterpriseEnforcerImpl)
	fluxApplicationRouterImpl := fluxApplication2.NewFluxApplicationRouterImpl(fluxApplicationRestHandlerImpl)
	devtronResourceTaskRunRepositoryImpl := repository13.NewDevtronResourceTaskRunRepositoryImpl(db, sugaredLogger)
	dtResRelationReadServiceImpl := read4.NewDtResRelationReadServiceImpl(sugaredLogger, dtResObjDepRelationsRepositoryImpl, devtronResourceObjectRepositoryImpl, readServiceImpl)
	taskRunTriggerOperationServiceImpl := taskRun.NewTaskRunTriggerOperationsServiceImpl(sugaredLogger, devtronResourceTaskRunRepositoryImpl, dtResRelationReadServiceImpl, readServiceImpl)
	devtronResourceServiceImpl := devtronResource.NewDevtronResourceServiceImpl(sugaredLogger, devtronResourceRepositoryImpl, devtronResourceSchemaRepositoryImpl, devtronResourceObjectRepositoryImpl, devtronResourceTaskRunRepositoryImpl, devtronResourceObjectAuditRepositoryImpl, dtResObjDepRelationsRepositoryImpl, appRepositoryImpl, pipelineRepositoryImpl, userRepositoryImpl, clusterRepositoryImpl, internalProcessingServiceImpl, readServiceImpl, objectAuditServiceImpl, environmentServiceImpl, dtResRelationReadServiceImpl, deploymentConfigServiceImpl, appCrudOperationServiceImpl, userServiceImpl, apiTokenServiceImpl, dockerRegistryConfigImpl, teamReadServiceImpl, taskRunTriggerOperationServiceImpl)
	devtronResourceSchemaAuditRepositoryImpl := repository13.NewDevtronResourceSchemaAuditRepositoryImpl(sugaredLogger, db)
	dtResSchemaServiceImpl := devtronResource.NewDtResSchemaServiceImpl(sugaredLogger, devtronResourceRepositoryImpl, devtronResourceSchemaRepositoryImpl, devtronResourceSchemaAuditRepositoryImpl, devtronResourceObjectRepositoryImpl, devtronResourceServiceImpl, readServiceImpl)
	devtronResourceRestHandlerImpl := devtronResource2.NewDevtronResourceRestHandlerImpl(sugaredLogger, userServiceImpl, enterpriseEnforcerImpl, enforcerUtilImpl, enforcerUtilHelmImpl, validate, devtronResourceServiceImpl, dtResSchemaServiceImpl, readServiceImpl, clusterRbacServiceImpl)
	devtronResourceRouterImpl := devtronResource2.NewDevtronResourceRouterImpl(devtronResourceRestHandlerImpl)
	silverSurferClientImpl, err := grpc2.NewSilverSurferClientImpl(sugaredLogger)
	if err != nil {
		return nil, err
	}
	clusterUpgradeServiceImpl, err := clusterUpgrade.NewClusterUpgradeServiceImpl(sugaredLogger, silverSurferClientImpl, clusterReadServiceImpl, k8sUtilExtended)
	if err != nil {
		return nil, err
	}
	clusterUpgradeRestHandlerImpl := clusterUpgrade2.NewClusterUpgradeRestHandlerImpl(sugaredLogger, userServiceImpl, enterpriseEnforcerImpl, clusterUpgradeServiceImpl)
	clusterUpgradeRouterImpl := clusterUpgrade2.NewClusterUpgradeRouterImpl(clusterUpgradeRestHandlerImpl)
	clusterConfigMap := bean3.NewClusterConfigMap()
	chatClientImpl, err := chat.NewChatClientImpl(sugaredLogger, clusterReadServiceImpl, k8sUtilExtended, interClusterServiceCommunicationHandlerImpl, clusterConfigMap)
	if err != nil {
		return nil, err
	}
	chatRouterImpl, err := chat2.NewChatRouterImpl(sugaredLogger, chatClientImpl)
	if err != nil {
		return nil, err
	}
	k8sEventWatcherRepositoryImpl := repository26.NewWatcherRepositoryImpl(db, sugaredLogger)
	triggerRepositoryImpl := repository26.NewTriggerRepositoryImpl(db, sugaredLogger)
	interceptedEventsRepositoryImpl := repository26.NewInterceptedEventsRepositoryImpl(db, sugaredLogger)
	webhookRemediationHelper := autoRemediation.NewWebhookRemediationHelper(sugaredLogger, interceptedEventsRepositoryImpl)
	remediationHelperFactory := autoRemediation.NewRemediationHelperFactoryEA(webhookRemediationHelper)
	watcherServiceImpl := autoRemediation.NewWatcherServiceImpl(k8sEventWatcherRepositoryImpl, triggerRepositoryImpl, interceptedEventsRepositoryImpl, environmentRepositoryImpl, clusterRepositoryImpl, scoopClientGetterImpl, remediationHelperFactory, sugaredLogger)
	serviceEAImpl := scoop2.NewServiceEAImpl(sugaredLogger, watcherServiceImpl, interceptedEventsRepositoryImpl)
	orchestratorCreds, err := types.GetOrchestratorCreds()
	if err != nil {
		return nil, err
	}
	scoopRestHandlerImpl := scoop2.NewRestHandler(serviceEAImpl, watcherServiceImpl, enforcerUtilImpl, userServiceImpl, sugaredLogger, enterpriseEnforcerImpl, orchestratorCreds, environmentServiceImpl)
	watcherRestHandlerImpl := autoRemediation2.NewWatcherRestHandlerImpl(watcherServiceImpl, userServiceImpl, validate, enforcerUtilImpl, enterpriseEnforcerImpl, evaluatorServiceImpl, sugaredLogger)
	scoopRouterImpl := scoop2.NewRouterImpl(scoopRestHandlerImpl, watcherRestHandlerImpl)
	userResourceServiceImpl := userResource.NewUserResourceServiceImpl(sugaredLogger, teamServiceImpl, environmentServiceImpl, clusterServiceImpl, k8sApplicationServiceImpl, enforcerUtilImpl, commonEnforcementUtilImpl, enterpriseEnforcerImpl, appCrudOperationServiceImpl)
	userResourceRestHandlerImpl := userResource2.NewUserResourceRestHandler(sugaredLogger, userServiceImpl, userResourceServiceImpl)
	userResourceRouterImpl := userResource2.NewUserResourceRouterImpl(userResourceRestHandlerImpl)
	licenseHandlerImpl := handler.NewLicenseHandler(sugaredLogger, licenseServiceImpl, userServiceImpl, telemetryEventClientImpl)
	licenseRouterImpl := handler.NewLicenseRouterImpl(licenseHandlerImpl)
	infrastructureDeploymentHandlerImpl := infrastructureDeployment.NewInfrastructureDeploymentHandlerImpl(sugaredLogger, infrastructureInstallationServiceImpl, userServiceImpl, enterpriseEnforcerImpl)
	infrastructureDeploymentRouterImpl := infrastructureDeployment.NewInfrastructureDeploymentRouterImpl(infrastructureDeploymentHandlerImpl)
	chartCategoryRepositoryImpl := repository27.NewChartCategoryRepositoryImpl(db, sugaredLogger)
	chartCategoryMappingRepositoryImpl := repository27.NewChartCategoryMappingRepositoryImpl(db, sugaredLogger)
	chartCategoryServiceImpl, err := service5.NewChartCategoryServiceImpl(sugaredLogger, chartCategoryRepositoryImpl, chartCategoryMappingRepositoryImpl, appStoreRepositoryImpl, userAuthServiceImpl, environmentServiceImpl, teamRepositoryImpl)
	if err != nil {
		return nil, err
	}
	chartCategoryRestHandlerImpl := chartCategory.NewChartCategoryRestHandlerImpl(sugaredLogger, userServiceImpl, enterpriseEnforcerImpl, validate, chartCategoryServiceImpl)
	chartCategoryRouterImpl := chartCategory.NewChartCategoryRouterImpl(chartCategoryRestHandlerImpl)
	muxRouter := NewMuxRouter(sugaredLogger, ssoLoginRouterImpl, teamRouterImpl, userAuthRouterImpl, userRouterImpl, routerImpl, commonRouterImpl, clusterRouterImpl, dashboardRouterImpl, helmAppRouterImpl, environmentRouterImpl, k8sApplicationRouterImpl, chartRepositoryRouterImpl, appStoreDiscoverRouterImpl, appStoreValuesRouterImpl, appStoreDeploymentRouterImpl, chartProviderRouterImpl, dockerRegRouterImpl, dashboardTelemetryRouterImpl, commonDeploymentRouterImpl, externalLinkRouterImpl, moduleRouterImpl, serverRouterImpl, apiTokenRouterImpl, k8sCapacityRouterImpl, webhookHelmRouterImpl, userAttributesRouterImpl, telemetryRouterImpl, userTerminalAccessRouterImpl, attributesRouterImpl, appRouterEAModeImpl, rbacRoleRouterImpl, authorisationConfigRouterImpl, argoApplicationRouterImpl, fluxApplicationRouterImpl, devtronResourceRouterImpl, clusterUpgradeRouterImpl, chatRouterImpl, scoopRouterImpl, userResourceRouterImpl, licenseRouterImpl, infrastructureDeploymentRouterImpl, chartCategoryRouterImpl)
	licenseMiddleware := licenseClient.NewLicenseMiddleware(licenseServiceImpl)
	mainApp := NewApp(db, sessionManager, muxRouter, telemetryEventClientImpl, posthogClient, sugaredLogger, userServiceImpl, licenseMiddleware)
	return mainApp, nil
}
