openapi: "3.0.0"
info:
  version: 1.0.0
  title: Devtron Labs
paths:
  /orchestrator/digest-policy :
    get:
      description: list of environments for which digest is enforced
      responses:
        '200':
          description: list response
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: array
                    description: app list
                    items:
                      $ref: '#/components/schemas/ClusterDetails'
    post:
      description: Save pull image using digest enforcement config for particular or all environments of a cluster
      requestBody:
        description: json as request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ClusterDetails'
      responses:
        '200':
          description: list response
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: array
                    description: app list
                    items:
                      $ref: '#/components/schemas/ClusterDetails'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

# components mentioned below
components:
  schemas:
    EnvironmentDetail:
      type: object
      properties:
        clusterId:
          type: integer
          description: cluster id
        environmentIds:
          type: array
          items:
            type: integer
          description: array of environment ids
        policyType:
          type: string
          description: whether it is for all existing plus future environment or for selected environments
          enum: [all_existing_and_future_environments, specific_environments]
    ClusterDetails:
      type: object
      properties:
        clusterDetails:
          items:
            $ref: '#/components/schemas/EnvironmentDetail'
        enableDigestForAllClusters:
          type: boolean
    ErrorResponse:
      required:
        - code
        - status
      properties:
        code:
          type: integer
          format: int32
          description: Error code
        status:
          type: string
          description: Error message
        errors:
          type: array
          description: errors
          items:
            $ref: '#/components/schemas/Error'

    Error:
      required:
        - code
        - status
      properties:
        code:
          type: integer
          format: int32
          description: Error internal code
        internalMessage:
          type: string
          description: Error internal message
        userMessage:
          type: string
          description: Error user message