openapi: 3.0.3
info:
  title: Orchestrator/App/external-helm-release API
  version: 1.0.0
paths:
  /orchestrator/application/external-helm-release:
    get:
      parameters:
        - in: query
          name: envId
          schema:
            type: int
          required: true
          description: EnvironmentId
      summary: list all external helm release for given envId
      responses:
        '200':
          description: List all external helm release, filters out release already linked to cdPipeline or externalPipeline
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                  result:
                    type: object
                    properties:
                      externalReleases:
                        type: array
                        items: string

