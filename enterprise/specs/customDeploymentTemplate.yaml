openapi: 3.0.3
info:
  title: Custom Deployment Template API Sepc
  description: |-
    This documentation consists about all the Apis related to Custom Deployment Template GUI Schema.
  termsOfService: https://devtron.ai/terms-of-use
  contact:
    email: <EMAIL>
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  version: 1.0.11
externalDocs:
  description: Find out more about Devtron
  url: https://devtron.ai
servers:
  - url: https://dashboard.devtron.ai/
tags:
  - name: Chart Ref Schema
    description: Everything about custom JSON schema for charts present in Chart Ref.

paths:
  /orchestrator/deployment/template/schema:
    get:
      tags:
        - Chart Ref Schema
      summary: Gets all Chart Ref Schema
      description: Fetches all Chart Ref Schema with all the levels at which it is applied
      operationId: getChartRefSchemas
      responses:
        '200':
          description: Successful Operation
          content:
            applicaton/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/schemaWithMappings'
        '500':
          description: Internal Server Error
    post:
      tags:
        - Chart Ref Schema
      summary: Creates a new Chart Ref Schema
      description: Creates a new Chart Ref Schema and also applies it to given levels
      operationId: createChartRefSchema
      requestBody:
        description: Create a new Chart Ref Schema with the given names and applies it to given qualifiers
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/schemaWithMappings'
      responses:
        '200':
          description: Successful Operation, return with the created chart ref schema with the levels at which it is applied, in case some qualifiers are wrong they are ignored
        '400':
          description: Invalid body or if a Chart Ref Schema already exists with the given name
        '500':
          description: Internal Server Error
        '401':
          description: Unauthorized
    put:
      tags:
        - Chart Ref Schema
      summary: Updates a particular chart ref schema.
      description: Updates a pre existing schema with given name and replaces the previous mapping with the new one
      operationId: updateChartRefSchema
      requestBody:
        description: Updates a Chart Ref Schema
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/schemaWithMappings'
      responses:
        '200':
          description: Successful Operation, return the Chart Ref Schema with mappings
        '400':
          description: Invalid body or Chart Ref Schema with given name do not exist
        '500':
          description: Internal Server Error
        '401':
          description: Unauthorized
    patch:
      tags:
        - Chart Ref Schema
      summary: Patches a particular Chart Ref Schema
      description: Adds new qualifers to pre existing one, also updates JSON Schema with the new one
      operationId: patchJSONSchema
      requestBody:
        description: Patches a Chart Ref Schema
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/schemaWithMappings'
      responses:
        '200':
          description: Successful Operatio
        '400':
          description: Bad Request, Chart Ref Schema with given name do not exist
        '500':
          description: Internal server error

  /orchestrator/deployment/template/schema/{name}:
    delete:
      tags:
        - Chart Ref Schema
      summary: Deletes a Chart Ref Schema with given name
      description: Deletes a Chart Ref Schema with all its existing mappings at different levels
      operationId: deleteChartRefSchema
      parameters:
        - name: name
          in: path
          description: name of Chart Ref Schema that needs to be deleted
          required: true
          schema:
            type: string
            format: string
      responses:
        '200':
          description: Successful
        '400':
          description: Bad Request ( Cannot find schema with given name )
        '500':
          description: Internal Server Error
        '401':
          description: Unauthorized

components:
  schemas:
    category:
      type: string
      enum: ["APP","ENV","APP","APP-ENV","CHART-REF","CLUSTER","GLOBAL"]
    type:
      type: string
      enum: ["JSON","YAML"]
    appselector:
      type: object
      properties:
        category:
          $ref: "#/components/schemas/category"
        appNames:
          type: array
          items:
            type: string
          example: [my-app, your-app, devtron-app]
    envselector:
      type: object
      properties:
        category:
          # $ref: "#/components/schemas/category"
          type: string
          example: "ENV"
        envNames:
          type: array
          items:
            type: string
          example: ["env-1","my-env","env-2"]
    appenvselector:
      type: object
      properties:
        category:
          # $ref: "#/components/schemas/category"
          type: string
          example: "APP_ENV"
        appEnvNames:
          type: array
          example: [{"appName":"my-app-1","envName":"my-env-devtron"}]
          items:
            type: object
            properties:
              appName:
                type: string
              envName:
                type: string
    chartselector:
      type: object
      properties:
        category:
          # $ref: "#/components/schemas/category"
          type: string
          example: "CHART_REF"
        chartVersions:
          type: array
          items:
            type: object
            properties:
              type:
                type: string
              version:
                type: string
    clusterselector:
      type: object
      properties:
        category:
          # $ref: "#/components/schemas/category"
          type: string
          example: "CLUSTER"
        clusterNames:
          type: array
          items:
            type: string
          example: ["cluster-1","my-cluster","cluster-2"]
    globalselector:
      type: object
      properties:
        category:
          # $ref: "#/components/schemas/category"
          type: string
          example: "GLOBAL"
        global:
          type: boolean
          example: true
    attributeselector:
      type: object
      properties:
        attributeSelector:
          type: object
          anyOf:
            - $ref: '#/components/schemas/appselector'
            - $ref: '#/components/schemas/envselector'
            - $ref: '#/components/schemas/appenvselector'
            - $ref: '#/components/schemas/chartselector'
            - $ref: '#/components/schemas/clusterselector'
            - $ref: '#/components/schemas/globalselector'
    schemaWithMappings:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          description: Name
        schema:
          type: string
          description: JSON Schema
        type:
          type: string
          # ref : '#components/schemas/type'
          description: JSON or YAML
        selector:
          type: array
          items:
            $ref: '#/components/schemas/attributeselector'
          description: Gives the qualifiers and levels at which it is applied.