openapi: 3.0.3
info:
  title: Artifact Promotion Policy
  description: Api Spec for Artifact Promotion Policy
  version: 1.0.0
servers:
  - url: 'http://localhost:8080'
paths:


  #  this api is used to fetch app workflows.
  #  we are adding new field artifactPromotionConfigured in workflows ,
  #  this will indicate if certain workflow contain atleast one environment with promotion policy
  /orchestrator/app/app-wf/view:
    get:
      summary: "Retrieve workflows"
      responses:
        '200':
          description: "Successful response"
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                  status:
                    type: string
                  result:
                    type: object
                    properties:
                      workflows:
                        type: array
                        items:
                          $ref: "#/components/schemas/Workflow"
                      ciConfig:
                        $ref: "#/components/schemas/CiConfig"
                      cdConfig:
                        $ref: "#/components/schemas/CdConfig"
                      externalCiConfig:
                        type: array
                        items:
                          $ref: "#/components/schemas/ExternalCiConfig"

  # /orchestrator/app/cd-pipeline/{cd-pipeline-id}/material:
  # will be adding new flag in the artifact object i.e PromotionDetails
  # /orchestrator/app/cd-pipeline/workflow/list


  /orchestrator/app/artifact/promotion-request:
    # this API is used to fetch the promotion details for a particular promoted artifact
    get:
      description: "this API is used to fetch the promotion details for a particular promoted artifact for the given promotionRequestId"
      parameters:
        - name: promotionRequestId
          description: "id of the request that user has raised"
          in: query
          required: true
          schema:
            type: integer
      responses:
        "400":
          description: "this response is for any un acceptable request payload or query params"
        "404":
          description: "this status code is returned when no request is found against given request id"
        "200":
          description: "successfully fetched artifact promotion meta data"
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  result:
                    $ref: '#/components/schemas/PromotionDetails'

    #  this api can be used to attempt promotion of an artifact on a selected environments of a given app. using action as PROMOTE
    #  can be used to cancel the promotion request using action as CANCEL by sending promotionRequestId in the payload.
    #  can be used to approve the promotion request using action as APPROVE by sending promotionRequestId in the payload.
    post:
      description: "this api can be used to attempt promotion of an artifact on a selected environments of a given app. using action as PROMOTE. \n 
      can be used to cancel the promotion request using action as CANCEL by sending promotionRequestId in the payload. \n 
      can be used to approve the promotion request using action as APPROVE by sending promotionRequestId in the payload."
      requestBody:
        description: " this is the request payload, "
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                sourceName:
                  type: string
                  description: "source name(ci source name for CI/WEBHOOK and envName )"
                sourceType:
                  type: string
                  enum: ["CI","WEBHOOK","ENVIRONMENT"]
                  description: "source type"
                  example: "CI or WEBHOOK or CD"
                action:
                  type: string
                  description: "action can be one of PROMOTE or CANCEL or APPROVE"
                  example: "CANCEL"
                promotionRequestId:
                  type: integer
                  description: "promotion request id, this is optional, required for CANCEL and APPROVE action"
                artifactId:
                  type: integer
                  description: "artifact id"
                workflowId:
                  type: integer
                  description: "workflow id"
                appId:
                  type: integer
                  description: "app id"
                  example: "order-service"
                destinationObjectNames:
                  type: array
                  items:
                    type: string
                    description: "environment names(supported envs only)"
                    example: "[UAT,QA,PROD]"
      responses:
        "403":
          description: "if user does not have permission to approve permission, OR does not have build and deploy permission to request for promotion, OR user is trying to cancel the request that was not created by him."
        "400":
          description: "this response is for any un acceptable request payload"
        "200":
          description: "attempts promoting the artifact to the given environments of the given app"
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/EnvironmentPromotionMetaData"
                description: " environments with detailed message about the action that user has requested"

  #    this api can be used to get the list of environments present in the given workflowId.
  #    if artifactId is provided then we will evaluate the environments policies on the given artifact.
  /orchestrator/app/artifact/promotion-request/env/list:
    get:
      description: "this api can be used to get the list of environments present in the given workflowId. /n if artifactId is provided then this will evaluate the environment policies on the given artifact."
      parameters:
        - name: workflowId
          in: query
          required: true
          schema:
            type: integer
          description: "workflow id"
        - name: artifactId
          in: query
          schema:
            type: integer
          description: "artifact id"

      responses:
        "200":
          description: "gets the list of environments for the given app."
          content:
            application/json:
              schema:
                type: object
                properties:
                  ciSource:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: "ci source id"
                      name:
                        type: string
                        description: "ci source name"
                      type:
                        type: string
                        enum: ["CI","WEBHOOK"]
                        description: "source type like CI, WEBHOOK"
                  environments:
                    type: array
                    items:
                      $ref: "#/components/schemas/EnvironmentPromotionMetaData"
                    description: "list of environments"

  #  this will fetch the artifacts for artifact promotion,
  #  if the resource is CD this will fetch the images that are successfully deployed in that node.(expecting environment name in resourceName param)
  #  for CI | WEBHOOK this will fetch the images that were created on these source.(expecting CI|WEBHOOK pipeline name in resourceName param)

  #  also can be used to fetch pending promotion artifacts for a selected environment.
  #  if resource is PROMOTION_APPROVAL_PENDING_NODE, then this api will fetch the pending promotion requests for the given environment (in resourceName).
  #  also can be used to fetch pending requests waiting for approval on requested user for given workflowId(required)
  #  appName is required param
  /orchestrator/app/artifact/promotion-request/env/approval-metadata:
    get:
      description: "This api is used to get list of envs on which artifact promotion request is raised for given artifact and also if user is allowed to approve promotion request on that environment"
      parameters:
        - name: artifactId
          in: query
          schema:
            type: integer
          description: "artifact id"
      responses:
        "200":
          description: "gets the list of environments for the given app."
          content:
            application/json:
              schema:
                type: object
                properties:
                  environments:
                    type: array
                    items:
                      $ref: "#/components/schemas/EnvironmentApprovalMetaData"
                    description: "list of environments"
  /orchestrator/app/artifact/promotion-request/material:
    get:
      description: "this api will fetch the artifacts for artifact promotion. \n 
      case1 : if the resource param is 'CD' this will fetch the images that are successfully deployed in the CD node of the provided environment name in the resourceName param. /n 
      case2 : if the resource is 'CI' or 'WEBHOOK' this will fetch the images that are created from the ci source node using the ci-pipeline name given in the resourceName param. \n 
      case3 : if the resource is PROMOTION_APPROVAL_PENDING_NODE then this API will fetch the pending promotion requests that are in pending state for the given environment name passed in resourceName param. /n
      case4: if fetchRequestsForMe param is set to true,then  workflowId param is required. in this case the API will fetch the promotion requests that can be approved by the requested user. /n
      Note: for any ambiguous request params the prioritization of the response is w.r.t to the above cases i.e case1 > case2 > case3"
      parameters:
        - name: resource
          in: query
          required: true
          schema:
            type: string
            enum: ["CI","ENVIRONMENT","WEBHOOK","PROMOTION_APPROVAL_PENDING_NODE"]
          description: "resource type, eg: CI,ENVIRONMENT,WEBHOOK,PROMOTION_APPROVAL_PENDING_NODE"
        - name: resourceName
          in: query
          schema:
            type: string
          description: "Name of the resource, for CI or WEBHOOK case resourceName will be name of the ci-pipeline, for other resource types it will be name of the environment"
        - name: appId
          in: query
          schema:
            type: integer
          description: "app Id"
        - name: workflowId
          in: query
          schema:
            type: integer
          description: "workflow id, this is required if pendingForCurrentUser is true"
        - name: pendingForCurrentUser
          in: query
          schema:
            type: boolean
          description: "if this is set to true we will fetch the list of artifacts which are pending for approval of current user"
      responses:
        "400":
          description: "this response is for any un acceptable request payload or query params"
        "200":
          description: "gets the materials for the request"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ArtifactPromotionMaterialResponse"

  /orchestrator/artifact-promotion/policy/{name}:
    get:
      description: "get artifact promotion policy by its name"
      responses:
        "403":
          description: "gets this response if user is not a super admin"
        "404":
          description: "if the policy is not found with the given name"
        "200":
          description: "gets the artifact promotion policy by its name successfully."
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PromotionPolicy"
    put:
      description: "update promotion policy"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PromotionPolicy'
      responses:
        "400":
          description: "this response is for any un acceptable request payload or query params"
        "403":
          description: "gets this response if user is not a super admin"
        "404":
          description: "if the policy is not found with the given name"
        "200":
          description: "successfully updated the policy."
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PromotionPolicy"
    delete:
      description: "Delete the promotion policy"
      responses:
        "403":
          description: "gets this response if user is not a super admin"
        "404":
          description: "if the policy is not found with the given name"
        "200":
          description: "deletes the promotion policy by its name successfully."

  /orchestrator//artifact-promotion/policy/list/min:
    get:
      description: "gets the active policy names"
      responses:
        "403":
          description: "gets this response if user is not a super admin"
        "200":
          description: "successfully fetched the policies"
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  result:
                    type: array
                    items:
                      type: string

  /orchestrator/artifact-promotion/policy:
    get:
      parameters:
        - name: search
          in: query
          schema:
            type: string
            description: "search string is used fetch the polices by matching their names with search string"
        - name: sortBy
          in: query
          schema:
            type: string
            description: "sort by policyName or approverCount configured in that policy, default to policyName"
        - name: sortOrder
          in: query
          schema:
            type: string
            description: "ASC or DESC, default to ASC"
      description: Get promotion policies list
      responses:
        "400":
          description: "this response is for any un acceptable request payload or query params"
        "403":
          description: "gets this response if user is not a super admin"
        "200":
          description: gets the promotion by search param.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PromotionPolicy"
    post:
      description: Create promotion policy
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PromotionPolicy'
      responses:
        "400":
          description: "this response is for any un acceptable request payload or query params"
        "403":
          description: "gets this response if user is not a super admin"
        "200":
          description: policy created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PromotionPolicy"

  /orchestrator/global/policy/artifact-promotion/app-env/list:
    post:
      description: get app and env list with respective policy
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AppEnvPolicyMappingsListFilter'

      responses:
        "400":
          description: "this response is for any un acceptable request payload or query params"
        "403":
          description: "gets this response if user is not a super admin"
        "200":
          description: "successfully fetched filtered app-env-policy mappings"
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  result:
                    type: object
                    properties:
                      totalCount:
                        type: integer
                      # can be named as appEnvironmentPolicyMappings
                      environmentPolicyMappings:
                        type: array
                        items:
                          $ref: '#/components/schemas/AppEnvPolicyContainer'

  /orchestrator/global/policy/artifact-promotion/bulk/apply:
    post:
      description: "used to apply a policy on the selected app-envs or using filtered app-env"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                applicationEnvironments:
                  type: array
                  items:
                    $ref: '#/components/schemas/AppEnvPolicyContainer'
                applyToPolicyNames:
                  type: array
                  description: "policy names to be applied on the selected app-envs, only one policy name is allowed for image promotion"
                  items:
                    type: string
                appEnvPolicyListFilter:
                  $ref: '#/components/schemas/AppEnvPolicyMappingsListFilter'
      responses:
        "400":
          description: "this response is for any un acceptable request payload or query params"
        "403":
          description: "gets this response if user is not a super admin"
        "200":
          description: "successfully applied the profile on the given app-envs or filtered app-envs"

  # this is an util API which fetches the supported filter criteria params supported in CEL expression
  /orchestrator/filters/criteria/metadata:
    get:
      description: "fetches the filter conditions param meta data used in CEL expression"
      responses:
        "400":
          description: "this response is for any un acceptable request payload or query params"
        "200":
          description: "successfully fetched filter condition CEL params"
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  result:
                    type: array
                    items:
                      $ref: '#/components/schemas/FilterCriteria'


components:
  schemas:
    PromotionDetails:
      type: object
      properties:
        source:
          type: string
        sourceType:
          type: string
          enum: ["CI","WEBHOOK","CD"]
        destination:
          type: string
        requestedBy:
          type: string
        approvedUsers:
          type: array
          items:
            type: string
        requestedOn:
          type: string
        promotedOn:
          type: string
        promotionPolicy:
          $ref: '#/components/schemas/PromotionPolicy'

    FilterCriteria:
      type: object
      properties:
        type:
          type: string
        label:
          type: string
        tooltip:
          type: string

    # separate detailed object and meta data object in PromotionPolicy
    PromotionPolicyMeta:
      type: object
      properties:
        name:
          required: true
          type: string
        description:
          type: string
        identifierCount:
          type: integer
          description: "this is an optional key"
    PromotionPolicy:
      type: object
      properties:
        name:
          required: true
          type: string
        description:
          type: string
        conditions:
          type: array
          items:
            $ref: '#/components/schemas/ResourceCondition'
        approvalMetadata:
          type: object
          properties:
            approverCount:
              type: integer
            allowImageBuilderFromApprove:
              type: boolean
              default: false
            allowRequesterFromApprove:
              type: boolean
              default: false
            allowApproverFromDeploy:
              default: false
              type: boolean
    ResourceCondition:
      type: object
      properties:
        conditionType:
          description: "0 or 1"
          type: integer
        expression:
          type: string
        errorMsg:
          type: string


    AppEnvPolicyMappingsListFilter:
      type: object
      properties:
        appNames:
          type: array
          items:
            type: string
        envNames:
          type: array
          items:
            type: string
        policyNames:
          type: array
          items:
            type: string
        sortBy:
          type: string
          description: "sort by applicationName or environmentName, defaults to applicationName"
        sortOrder:
          type: string
          description: "sorting order ASC or DESC , defaults to ASC"
        offset:
          type: integer
          description: "offset on the filtered result defaults to 0"
        size:
          type: integer
          description: "max size of the filtered result set , defaults to 20"
    AppEnvPolicyContainer:
      type: object
      properties:
        appName:
          type: string
        envName:
          type: string
        policyNames:
          description: "for image promotion, we can only send/get one policy name"
          type: array
          items:
            type: string


    ExternalCiConfig:
      type: object
      properties:
        id:
          type: integer
        webhookUrl:
          type: string
        payload:
          type: string
        accessKey:
          type: string
        payloadOption:
          type: string
        schema:
          type: string
        responses:
          type: string
        projectId:
          type: integer
        projectName:
          type: string
        environmentId:
          type: string
        environmentName:
          type: string
        environmentIdentifier:
          type: string
        appId:
          type: integer
        appName:
          type: string
        role:
          type: string
    CdConfig:
      type: object
      properties:
        pipelines:
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
              environmentId:
                type: integer
              environmentName:
                type: string
              description:
                type: string
              triggerType:
                type: string
              name:
                type: string
              deploymentTemplate:
                type: string
              preStage:
                type: object
              postStage:
                type: object
              preStageConfigMapSecretNames:
                type: object
              postStageConfigMapSecretNames:
                type: object
              isClusterCdActive:
                type: boolean
              parentPipelineId:
                type: integer
              parentPipelineType:
                type: string
              deploymentAppType:
                type: string
              userApprovalConfig:
                type: object
              appName:
                type: string
              deploymentAppDeleteRequest:
                type: boolean
              deploymentAppCreated:
                type: boolean
              appId:
                type: integer
              isVirtualEnvironment:
                type: boolean
              helmPackageName:
                type: string
              chartName:
                type: string
              chartBaseVersion:
                type: string
              containerRegistryName:
                type: string
              repoName:
                type: string
              manifestStorageType:
                type: string
              customTag:
                type: object
              customTagStage:
                type: object
              enableCustomTag:
                type: boolean
              isProdEnv:
                type: boolean
              switchFromCiPipelineId:
                type: integer
              addType:
                type: string
              childPipelineId:
                type: integer
              isDigestEnforcedForPipeline:
                type: boolean
              isDigestEnforcedForEnv:
                type: boolean
    CiConfig:
      type: object
      properties:
        ciGitConfiguredId:
          type: integer
        ciPipelines:
          type: array
          items:
            type: object
            properties:
              isManual:
                type: boolean
              dockerArgs:
                type: object
              isExternal:
                type: boolean
              parentCiPipeline:
                type: integer
              parentAppId:
                type: integer
              appId:
                type: integer
              externalCiConfig:
                type: object
              ciMaterial:
                type: array
                items:
                  type: object
                  properties:
                    source:
                      type: object
                      properties:
                        type:
                          type: string
                        value:
                          type: string
                        regex:
                          type: string
                    gitMaterialId:
                      type: integer
                    id:
                      type: integer
                    gitMaterialName:
                      type: string
                    isRegex:
                      type: boolean
              name:
                type: string
              id:
                type: integer
              active:
                type: boolean
              linkedCount:
                type: integer
              pipelineType:
                type: string
              scanEnabled:
                type: boolean
              isDockerConfigOverridden:
                type: boolean
              dockerConfigOverride:
                type: object
              isCITriggerBlocked:
                type: boolean
              lastTriggeredEnvId:
                type: integer
              enableCustomTag:
                type: boolean
    Workflow:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        appId:
          type: integer
        artifactPromotionMetaData:  # Nesting meta info related to image promotion object
          type: object
          properties:
            # Can also derive isConfigured through approvalPendingCount but kept separate key since it seems more readable.
            isConfigured:
              type: boolean
              description: "Indicates if a promotion policy is attached to this workflow"
            isApprovalPendingForPromotion:
              type: integer
              description: "if images are pending for artifact promotion"
        tree:
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
              appWorkflowId:
                type: integer
              type:
                type: string
              componentId:
                type: integer
              parentId:
                type: integer
              parentType:
                type: string
              deploymentAppDeleteRequest:
                type: boolean
              environmentName:
                type: string
              helmPackageName:
                type: string
              isLast:
                type: boolean
    EnvironmentPromotionMetaData:
      type: object
      properties:
        #       environment name
        name:
          type: string
          description: "environment name"
          example: "PROD"
        #  total approval count configured by the policy on this environment.
        #  need this to show no of approvals configured on this environment.
        approvalCount:
          type: integer
          example: "3"
          description: "total approval count configured by the policy on this environment"
        #       will be true only if this environment is governed by some promotion policy.
        promotionPossible:
          type: boolean
          description: "promotion possible or not"
        #       if requested this api with evaluation, then the detailed evaluation message will be sent in this field
        promotionValidationMessage:
          type: string
          example: "promotion unsuccessful as the artifact is blocked by the promotion policy"
          description: "promotion evaluation message, detailed evaluation message for respective state"
        #       if requested this api with evaluation, then the evaluation state will be sent in this field
        promotionValidationState:
          type: string
          example: "PROMOTION-SUCCESSFUL or PROMOTION-APPROVAL-PENDING or PROMOTION-BLOCKED or CD-PIPELINE-DELETED"
          description: "promotion evaluation state, eg: imagePromoted,imageSentForApproval,imageAlreadyPromoted,errored"
        isVirtualEnvironment:
          type: boolean
          description: "If true then this environment is a virtual environment"
    EnvironmentApprovalMetaData:
      type: object
      properties:
        #       environment name
        name:
          type: string
          description: "environment name"
          example: "PROD"
        approvalAllowed:
          type: boolean
          description: "If user have permission to approve on this environment. User will not have access if he is not an image promoter or configured global image promotion policy restricts him to do so"
        reason:
          type: string
          description: "if approval is not allowed, this field will contain reason that why user cannot approve artifact on this env"
    PromotionApprovalMetaData:
      type: object
      properties:
        approvalRequestId:
          type: integer
          description: "approval request id"
        approvalRuntimeState:
          type: string
          example: "AWAITING or PROMOTED or CANCELLED or STALE"
          description: "approval runtime state"
        approvedUserData:
          type: array
          items:
            type: string
          example: ["<EMAIL>"]
        requestedUserData:
          type: string
          example: "<EMAIL>"
        promotedFrom:
          type: string
          description: "promoted from env or ci pipeline"
        promotedFromType:
          type: string
          description: "promote from type eg: CI, ENVIRONMENT"
    ImageComment:
      type: object
      properties:
        id:
          type: integer
          description: "id"
        comment:
          type: string
          description: "comment"
        artifactId:
          type: integer
          description: "artifact id"
    ReleaseTag:
      type: object
      properties:
        id:
          type: integer
          description: "id"
        tag:
          type: string
          description: "tag"
        appId:
          type: integer
          description: "app id"
        artifactId:
          type: integer
          description: "artifact id"
        deleted:
          type: boolean
          description: "deleted"
    Artifact:
      type: object
      properties:
        id:
          type: integer
          description: "id"
        image:
          type: string
          description: "image"
        image_digest:
          type: string
          description: "image digest"
        material_info:
          type: string
          description: "material info"
        data_source:
          type: string
          description: "data source"
        vulnerable:
          type: boolean
          description: "vulnerable"
        scanEnabled:
          type: boolean
          description: "scan enabled"
        scanned:
          type: boolean
          description: "scanned"
        promotionApprovalMetaData:
          $ref: "#/components/schemas/PromotionApprovalMetaData"
        deployed:
          type: boolean
          description: "deployed"
        deployed_time:
          type: string
          description: "deployed time"
        imageComment:
          $ref: "#/components/schemas/ImageComment"
        imageReleaseTags:
          type: array
          items:
            $ref: "#/components/schemas/ReleaseTag"
          description: "image release tags"
        deployedOnEnvironments:
          description: "this will contain the list of environments that this artifact is currently been deployed and successfully completed the post cd if configured in their respective environments."
          type: array
          items:
            type: string


    ArtifactPromotionMaterialResponse:
      type: object
      properties:
        imagePromotionApproverEmails:
          type: array
          items:
            type: string
          description: " user emails who can approve the artifact promotion for the given pipeline(derived from appName and envName)"
        hideImageTaggingHardDelete:
          required: true
          type: boolean
          description: "imageTags can be hard deleted or not"
        tagsEditable:
          type: boolean
          description: "imageTags can be editable or not"
        appReleaseTagNames:
          type: array
          items:
            type: string
          description: "app release tag names"
        ciArtifacts:
          type: array
          items:
            $ref: "#/components/schemas/Artifact"
          description: "ci artifacts"
        totalCount:
          type: integer
          description: "total count of the tags"
        isApprovalPendingForPromotion:
          type: boolean
          description: "if true, one or more artifact promotion request is pending for approval by user"

