/*
 * Copyright (c) 2024. Devtron Inc.
 */

package commonPolicyActions

import (
	"fmt"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/model"
	"github.com/gorilla/mux"
)

type CommonPolicyRouter interface {
	InitCommonPolicyRouter(router *mux.Router)
}

type CommonPolicyRouterImpl struct {
	restHandler CommonPolicyRestHandler
}

func NewCommonPolicyRouterImpl(restHandler CommonPolicyRestHandler) *CommonPolicyRouterImpl {
	return &CommonPolicyRouterImpl{
		restHandler: restHandler,
	}
}

func (r *CommonPolicyRouterImpl) InitCommonPolicyRouter(router *mux.Router) {
	router.Path(fmt.Sprintf("/{%s}/app-env/list", model.PathVariablePolicyTypeVariable)).HandlerFunc(r.restHandler.ListAppEnvPolicies).
		Methods("POST")
	router.Path(fmt.Sprintf("/{%s}/bulk/apply", model.PathVariablePolicyTypeVariable)).HandlerFunc(r.restHandler.ApplyPolicyToIdentifiers).
		Methods("POST")
	router.Path("/{policyOf}/{apiVersion}").HandlerFunc(r.restHandler.CreatePolicy).
		Methods("POST")
	router.Path("/{policyOf}/{apiVersion}").HandlerFunc(r.restHandler.UpdatePolicy).
		Methods("PUT")
	router.Path("/{policyOf}/{apiVersion}/list").HandlerFunc(r.restHandler.GetPoliciesList).
		Methods("GET")
	router.Path("/{policyOf}/{apiVersion}").HandlerFunc(r.restHandler.GetPolicyDetail).
		Methods("GET")
	router.Path("/{policyOf}/{apiVersion}").HandlerFunc(r.restHandler.DeletePolicy).
		Methods("DELETE")

	router.Path("/handle-approval-config-corrupt-data").
		HandlerFunc(r.restHandler.HandleCorruptedApprovalConfigData).
		Methods("POST")

	router.Path("/{apiVersion}/selectors/config").HandlerFunc(r.restHandler.ApplyPolicyToIdentifiersAlpha1).
		Methods("POST")
	router.Path("/{apiVersion}/selectors/config").HandlerFunc(r.restHandler.UpdateApplyPolicyToIdentifiersAlpha1).
		Methods("PUT")
	router.Path("/{apiVersion}/selectors/config/bulk").HandlerFunc(r.restHandler.PatchApplyPolicyToIdentifiersBulkAlpha1).
		Methods("PATCH")
	router.Path("/{apiVersion}/selectors/config").HandlerFunc(r.restHandler.PatchApplyPolicyToIdentifiersAlpha1).
		Methods("PATCH")
	router.Path("/{apiVersion}/selectors/config").HandlerFunc(r.restHandler.GetAppliedPolicyDetail).
		Methods("GET")
	router.Path("/{apiVersion}/selectors/config").HandlerFunc(r.restHandler.DeleteAppliedPolicies).
		Methods("DELETE")
	router.Path("/{apiVersion}/selectors/config/list").HandlerFunc(r.restHandler.GetAllAppliedPolicies).
		Methods("POST")
	// Deprecated API method for getting all applied policies. Use the new "POST" method instead.
	router.Path("/{apiVersion}/selectors/config/list").HandlerFunc(r.restHandler.GetAllAppliedPoliciesOld).
		Methods("GET")

	// selectors/filter/list is used for listing all applied selectors for a given policy
	// currently only used for branch-fix and branch regex policies
	// use to populate the filter list directly from the qualifier mapping data
	// refer - mandatory-plugin-applied-config.yaml for API details
	router.Path("/{apiVersion}/selectors/filter/list").HandlerFunc(r.restHandler.ListAllAppliedSelectors).
		Methods("GET")

	// plugin should have been {policyOf}, but we currently only support this end point for plugin.
	// this should be replaced, when we support this endpoint for other policies
	router.Path("/plugin/{apiVersion}/offending/{resourceKind:[a-zA-Z0-9/-]+}/list").HandlerFunc(r.restHandler.ListOffendingResourceList).
		Methods("GET")

	router.Path("/plugin/{apiVersion}/offending/{resourceKind:[a-zA-Z0-9/-]+}").HandlerFunc(r.restHandler.EnforcementInfoOfPolicyOnResource).
		Methods("GET")

	//exception users end points
	router.Path("/{policyOf}/{apiVersion}/exceptions").HandlerFunc(r.restHandler.GetExceptionUsersProfile).
		Methods("GET")
	router.Path("/{policyOf}/{apiVersion}/exceptions").HandlerFunc(r.restHandler.PatchExceptionUsersProfile).
		Methods("PATCH")

}
