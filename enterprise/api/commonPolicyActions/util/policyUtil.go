package util

import (
	bean2 "github.com/devtron-labs/devtron/enterprise/api/commonPolicyActions/bean"
	"github.com/devtron-labs/devtron/internal/util"
	"github.com/devtron-labs/devtron/pkg/devtronResource/bean"
	"net/http"
	"strings"
)

func DecodeSelectorTypeQueryString(selectorTypeQuery string) (bean.DtResSearchableKeyName, error) {
	objs := strings.Split(selectorTypeQuery, "|")
	if len(objs) != 2 {
		return "", util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.InvalidSelectorQuery, bean.InvalidSelectorQuery)
	}
	return decodeSelectorType(objs[0], objs[1])
}

func decodeSelectorType(kind, attribute string) (bean.DtResSearchableKeyName, error) {
	switch kind {
	case bean.DevtronResourceCiPipeline.ToString():
		if attribute == bean.Branch.ToString() {
			return bean.DEVTRON_RESOURCE_SEARCHABLE_KEY_CI_PIPELINE_BRANCH, nil
		} else if attribute == bean.BranchRegex.ToString() {
			return bean.DEVTRON_RESOURCE_SEARCHABLE_KEY_CI_PIPELINE_BRANCH_REGEX, nil
		}
	}
	return "", util.GetApiErrorAdapter(http.StatusBadRequest, "400", bean.UnsupportedSelectorQuery, bean.UnsupportedSelectorQuery)
}

func FetchApiVersionAndPolicyOfFromPathParams(pathParamsMap map[string]string) (string, string) {
	apiVersion := pathParamsMap[bean2.ApiVersion]
	apiVersion = strings.TrimSpace(apiVersion)
	policyOf := pathParamsMap[bean2.PolicyOf]
	policyOf = strings.TrimSpace(policyOf)
	return apiVersion, policyOf
}
