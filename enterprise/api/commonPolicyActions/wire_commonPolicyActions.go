/*
 * Copyright (c) 2024. Devtron Inc.
 */

package commonPolicyActions

import (
	"github.com/devtron-labs/devtron/pkg/policyGovernance/common/alpha1"
	v0 "github.com/devtron-labs/devtron/pkg/policyGovernance/common/v0"
	"github.com/google/wire"
)

var CommonPolicyActionWireSet = wire.NewSet(
	v0.NewCommonPolicyActionsServiceV0,
	wire.Bind(new(v0.CommonPolicyActionsServiceV0), new(*v0.CommonPolicyActionsServiceV0Impl)),
	wire.Bind(new(v0.CommonPolicyApplyEventHandlerV0), new(*v0.CommonPolicyActionsServiceV0Impl)),

	alpha1.NewCommonPolicyActionsServiceImpl,
	wire.Bind(new(alpha1.CommonPolicyActionsService), new(*alpha1.CommonPolicyActionsServiceImpl)),
	wire.Bind(new(alpha1.CustomPolicyDataHandler), new(*alpha1.CommonPolicyActionsServiceImpl)),
	NewCommonPolicyRestHandlerImpl,
	wire.Bind(new(CommonPolicyRestHandler), new(*CommonPolicyRestHandlerImpl)),
	NewCommonPolicyRouterImpl,
	wire.Bind(new(CommonPolicyRouter), new(*CommonPolicyRouterImpl)),
)
