package bean

import (
	apiBean "github.com/devtron-labs/devtron/api/devtronResource/bean"
	dtResourceBean "github.com/devtron-labs/devtron/pkg/devtronResource/bean"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/model"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/plugin/alpha1/bean"
)

type ResourceIdentifiersQuery struct {
	ResourceIdentifier []string `schema:"resourceIdentifier"`
}

// ResourceIdentifierDetails : This struct is used to enforce the policy on the resources
//
// Branches :
//   - can be empty or list of branches
//   - If Branches is empty, ResourceIdentifierDetails will be used to enforce the policy on existing branch values
//   - If Branches will be required to enforce the policy on bean.DevtronResourceCiPipeline resource creation/ branch update
//
// PluginApplyStage :
//   - can be empty or bean.PluginApplyStagePreCd or bean.PluginApplyStagePostCd
//   - If PluginApplyStage is empty, ResourceIdentifierDetails will be used to enforce both PreCd and PostCd
//   - If PluginApplyStage is bean.PluginApplyStagePreCd, ResourceIdentifierDetails will be used to enforce only PreCd
//   - If PluginApplyStage is bean.PluginApplyStagePostCd, ResourceIdentifierDetails will be used to enforce only PostCd
//   - PluginApplyStage is not applicable for bean.DevtronResourceCiPipeline
//
// ApplicationKindCriteria :
//   - can be empty or list of kind criteria
//   - required for bean.DevtronResourceCiPipeline resource creation
//   - mandatory for bean.DevtronResourceCdPipeline resource enforcement
//
// CiPipelineKindCriteria :
//   - can be empty or list of kind criteria
//   - required for existing bean.DevtronResourceCiPipeline resource enforcement
//
// EnvKindCriteria :
//   - can be empty or list of kind criteria
//   - mandatory for bean.DevtronResourceCdPipeline resource enforcement
type ResourceIdentifierDetails struct {
	Branches                []string
	PluginApplyStage        bean.PluginApplyStage
	ApplicationKindCriteria *dtResourceBean.FilterCriteriaDecoderMulti
	CiPipelineKindCriteria  *dtResourceBean.FilterCriteriaDecoderMulti
	EnvKindCriteria         *dtResourceBean.FilterCriteriaDecoderMulti
}

type SelectorConfig struct {
	Kind model.PathVariablePolicyType `schema:"kind" json:"kind"`
}

type PolicyListQuery struct {
	Lite bool `schema:"lite"`
}

type MandatorySelectorConfigQuery struct {
	// todo: might be array
	Kind model.PathVariablePolicyType `schema:"kind,required"`
}

type GetSelectorConfigQuery struct {
	SelectorConfig
	Identifier int `schema:"identifier,required"`
}

type GetAllSelectorConfigQuery struct {
	SelectorConfig
	apiBean.GetHistoryQueryParams
}

type ListAllAppliedSelectorsQuery struct {
	MandatorySelectorConfigQuery
	Selectors []string `schema:"selector,required"`
}

type DeleteAppliedPolicyRequest struct {
	SelectorConfig
	Identifiers []int `schema:"identifier" json:"identifiers"`
}

type ListAppliedPoliciesFilter struct {
	FilterCriteria []string `json:"filterCriteria"`
}

const (
	ApiVersion = "apiVersion"
	PolicyOf   = "policyOf"
)
