/*
 * Copyright (c) 2024. Devtron Inc.
 */

package commonPolicyActions

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/devtron-labs/devtron/api/restHandler/common"
	bean2 "github.com/devtron-labs/devtron/enterprise/api/commonPolicyActions/bean"
	util3 "github.com/devtron-labs/devtron/enterprise/api/commonPolicyActions/util"
	util4 "github.com/devtron-labs/devtron/internal/util"
	"github.com/devtron-labs/devtron/pkg/auth/authorisation/casbin"
	"github.com/devtron-labs/devtron/pkg/auth/user"
	"github.com/devtron-labs/devtron/pkg/bean/common/patchQuery"
	bean3 "github.com/devtron-labs/devtron/pkg/devtronResource/bean"
	util2 "github.com/devtron-labs/devtron/pkg/devtronResource/util"
	"github.com/devtron-labs/devtron/pkg/globalPolicy/bean"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/adapter"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/read"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/common/alpha1"
	validator2 "github.com/devtron-labs/devtron/pkg/policyGovernance/common/alpha1/validator"
	v0 "github.com/devtron-labs/devtron/pkg/policyGovernance/common/v0"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/model"
	pluginV1alpha1 "github.com/devtron-labs/devtron/pkg/policyGovernance/plugin/alpha1"
	bean4 "github.com/devtron-labs/devtron/pkg/policyGovernance/plugin/alpha1/bean"
	"github.com/devtron-labs/devtron/pkg/resourceQualifiers"
	"github.com/devtron-labs/devtron/util"
	"github.com/devtron-labs/devtron/util/rbac"
	"github.com/gorilla/mux"
	"github.com/gorilla/schema"
	"go.uber.org/zap"
	"golang.org/x/exp/maps"
	"gopkg.in/go-playground/validator.v9"
	"io"
	"net/http"
	"slices"
	"strings"
)

type ListResponse struct {
	TotalCount                   int                           `json:"totalCount"`
	AppEnvironmentPolicyMappings []model.AppEnvPolicyContainer `json:"appEnvironmentPolicyMappings"`
	AppliedPolicyNames           []string                      `json:"appliedPolicyNames"`
}

type CommonPolicyRestHandler interface {
	ListAppEnvPolicies(w http.ResponseWriter, r *http.Request)
	ApplyPolicyToIdentifiers(w http.ResponseWriter, r *http.Request)
	CreatePolicy(w http.ResponseWriter, r *http.Request)
	UpdatePolicy(w http.ResponseWriter, r *http.Request)
	GetPoliciesList(w http.ResponseWriter, r *http.Request)
	GetPolicyDetail(w http.ResponseWriter, r *http.Request)
	DeletePolicy(w http.ResponseWriter, r *http.Request)

	HandleCorruptedApprovalConfigData(w http.ResponseWriter, r *http.Request)
	ApplyPolicyToIdentifiersAlpha1(w http.ResponseWriter, r *http.Request)
	UpdateApplyPolicyToIdentifiersAlpha1(w http.ResponseWriter, r *http.Request)
	PatchApplyPolicyToIdentifiersBulkAlpha1(w http.ResponseWriter, r *http.Request)
	PatchApplyPolicyToIdentifiersAlpha1(w http.ResponseWriter, r *http.Request)
	GetAppliedPolicyDetail(w http.ResponseWriter, r *http.Request)
	// Deprecated: method GetAllAppliedPoliciesOld, use GetAllAppliedPolicies instead
	GetAllAppliedPoliciesOld(w http.ResponseWriter, r *http.Request)
	// GetAllAppliedPolicies returns all applied policies, based on the filter criteria.
	// Earlier, this was implemented through GET request GetAllAppliedPoliciesOld, but now it is a POST request.
	// This is because the filter criteria can be complex and can't be passed through query params in all cases.
	GetAllAppliedPolicies(w http.ResponseWriter, r *http.Request)
	ListAllAppliedSelectors(w http.ResponseWriter, r *http.Request)
	DeleteAppliedPolicies(w http.ResponseWriter, r *http.Request)

	ListOffendingResourceList(w http.ResponseWriter, r *http.Request)
	EnforcementInfoOfPolicyOnResource(w http.ResponseWriter, r *http.Request)

	//ExceptionUsersProfiles
	GetExceptionUsersProfile(w http.ResponseWriter, r *http.Request)
	PatchExceptionUsersProfile(w http.ResponseWriter, r *http.Request)
}

type CommonPolicyRestHandlerImpl struct {
	commonPolicyActionService   alpha1.CommonPolicyActionsService
	commonPolicyActionServiceV0 v0.CommonPolicyActionsServiceV0

	offendingPolicyResourcesService pluginV1alpha1.MandatoryPluginEnforcementService
	userService                     user.UserService
	enforcer                        casbin.Enforcer
	enforcerUtil                    rbac.EnforcerUtil
	validator                       *validator.Validate
	logger                          *zap.SugaredLogger
}

func NewCommonPolicyRestHandlerImpl(commonPolicyActionService alpha1.CommonPolicyActionsService,
	commonPolicyActionServiceV0 v0.CommonPolicyActionsServiceV0,
	userService user.UserService,
	enforcer casbin.Enforcer,
	enforcerUtil rbac.EnforcerUtil,
	validator *validator.Validate,
	offendingPolicyResourcesService pluginV1alpha1.MandatoryPluginEnforcementService,
	logger *zap.SugaredLogger) *CommonPolicyRestHandlerImpl {
	return &CommonPolicyRestHandlerImpl{
		commonPolicyActionService:       commonPolicyActionService,
		commonPolicyActionServiceV0:     commonPolicyActionServiceV0,
		offendingPolicyResourcesService: offendingPolicyResourcesService,
		userService:                     userService,
		enforcer:                        enforcer,
		enforcerUtil:                    enforcerUtil,
		validator:                       validator,
		logger:                          logger,
	}
}

func (handler *CommonPolicyRestHandlerImpl) ListAppEnvPolicies(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		// todo: create constant error message and resp
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	token := r.Header.Get("token")
	if ok := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionDelete, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}

	vars := mux.Vars(r)
	policyTypeVar := vars[model.PathVariablePolicyTypeVariable]
	policyType := model.PathVariablePolicyType(policyTypeVar)
	if !slices.ContainsFunc(model.ExistingPolicyTypes, func(typ model.PathVariablePolicyType) bool {
		return policyType == typ
	}) {
		common.WriteJsonResp(w, errors.New("profileType not found"), nil, http.StatusNotFound)
		return
	}

	payload := &model.AppEnvPolicyMappingsListFilter{}
	decoder := json.NewDecoder(r.Body)
	err = decoder.Decode(payload)
	if err != nil {
		handler.logger.Errorw("error in decoding the request payload", "err", err, "requestBody", r.Body)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	payload.PolicyType = policyType.ToGlobalPolicyType()
	payload.PolicyVersion = policyType.GetGlobalPolicyLatestVersion()
	err = handler.validator.Struct(payload)
	if err != nil {
		handler.logger.Errorw("error in validating the request payload", "err", err, "payload", payload)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	result, totalCount, policyNames, err := handler.commonPolicyActionServiceV0.ListAppEnvPolicies(payload)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	response := &ListResponse{
		TotalCount:                   totalCount,
		AppEnvironmentPolicyMappings: result,
		AppliedPolicyNames:           policyNames,
	}

	common.WriteJsonResp(w, nil, response, http.StatusOK)
}

func (handler *CommonPolicyRestHandlerImpl) ApplyPolicyToIdentifiers(w http.ResponseWriter, r *http.Request) {

	ctx := util.NewRequestCtx(r.Context())

	token := r.Header.Get("token")
	if ok := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionDelete, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}

	vars := mux.Vars(r)
	policyTypeVar := vars[model.PathVariablePolicyTypeVariable]
	policyType := model.PathVariablePolicyType(policyTypeVar)
	if !slices.ContainsFunc(model.ExistingPolicyTypes, func(typ model.PathVariablePolicyType) bool {
		return policyType == typ
	}) {
		common.WriteJsonResp(w, errors.New("profileType not found"), nil, http.StatusNotFound)
		return
	}

	payload := &model.BulkPromotionPolicyApplyRequest{}
	decoder := json.NewDecoder(r.Body)
	err := decoder.Decode(payload)
	if err != nil {
		handler.logger.Errorw("error in decoding the request payload", "err", err, "requestBody", r.Body)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	payload.PolicyType = policyType.ToGlobalPolicyType()
	payload.PolicyVersion = policyType.GetGlobalPolicyLatestVersion()
	err = handler.validator.Struct(payload)
	if err != nil {
		handler.logger.Errorw("error in validating the request payload", "err", err, "payload", payload)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	if payload.PolicyType == bean.GLOBAL_POLICY_TYPE_IMAGE_PROMOTION_POLICY {
		if len(payload.ApplyToPolicyNames) > 1 {
			common.WriteJsonResp(w, errors.New("cannot apply multiple image promotion policies"), nil, http.StatusBadRequest)
			return
		}
	}

	err = handler.commonPolicyActionServiceV0.ApplyPolicyToIdentifiers(ctx, payload)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	common.WriteJsonResp(w, nil, nil, http.StatusOK)
}

func (handler *CommonPolicyRestHandlerImpl) CreatePolicy(w http.ResponseWriter, r *http.Request) {

	ctx := util.NewRequestCtx(r.Context())

	if ok := handler.enforcer.Enforce(ctx.GetToken(), casbin.ResourceGlobal, casbin.ActionCreate, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}

	vars := mux.Vars(r)
	apiVersion := vars["apiVersion"]
	apiVersion = strings.TrimSpace(apiVersion)
	policyOf := vars["policyOf"]
	policyOf = strings.TrimSpace(policyOf)

	policy := &model.GlobalPolicyModel{}
	decoder := json.NewDecoder(r.Body)
	err := decoder.Decode(policy)

	if err != nil {
		handler.logger.Errorw("error in decoding the request payload", "requestBody", r.Body, "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	policy.UserId = ctx.GetUserId()
	policy.ApiVersion = apiVersion
	policy.PolicyOf = model.GetPathVariablePolicyType(policyOf).ToGlobalPolicyType()
	policy.Version = model.GetPathVariablePolicyType(policyOf).GetGlobalPolicyLatestVersion().ToString()

	err = handler.validator.Struct(policy)
	if err != nil {
		handler.logger.Errorw("error in validating the request payload", "policy", policy, "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	err = handler.commonPolicyActionService.CreateGlobalPolicy(ctx, policy)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, nil, http.StatusOK)
}

func (handler *CommonPolicyRestHandlerImpl) UpdatePolicy(w http.ResponseWriter, r *http.Request) {
	ctx := util.NewRequestCtx(r.Context())

	if ok := handler.enforcer.Enforce(ctx.GetToken(), casbin.ResourceGlobal, casbin.ActionUpdate, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}

	vars := mux.Vars(r)
	apiVersion := vars["apiVersion"]
	apiVersion = strings.TrimSpace(apiVersion)
	policyOf := vars["policyOf"]
	policyOf = strings.TrimSpace(policyOf)

	queryParams := r.URL.Query()
	policyName := queryParams.Get("identifier")

	policy := &model.GlobalPolicyModel{}
	decoder := json.NewDecoder(r.Body)
	err := decoder.Decode(policy)
	if err != nil {
		handler.logger.Errorw("error in decoding the request payload", "requestBody", r.Body, "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	policy.UserId = ctx.GetUserId()
	policy.ApiVersion = apiVersion
	policy.PolicyOf = model.GetPathVariablePolicyType(policyOf).ToGlobalPolicyType()
	policy.Version = model.GetPathVariablePolicyType(policyOf).GetGlobalPolicyLatestVersion().ToString()

	err = handler.validator.Struct(policy)
	if err != nil {
		handler.logger.Errorw("error in validating the request payload", "policy", policy, "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	err = handler.commonPolicyActionService.UpdateGlobalPolicy(ctx, policyName, policy)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, nil, http.StatusOK)

}

func (handler *CommonPolicyRestHandlerImpl) DeletePolicy(w http.ResponseWriter, r *http.Request) {
	ctx := util.NewRequestCtx(r.Context())
	if ok := handler.enforcer.Enforce(ctx.GetToken(), casbin.ResourceGlobal, casbin.ActionDelete, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return

	}

	vars := mux.Vars(r)
	queryParams := r.URL.Query()
	policyName := queryParams.Get("identifier")
	apiVersion := vars["apiVersion"]
	apiVersion = strings.TrimSpace(apiVersion)

	policyOf := vars["policyOf"]
	policyOf = strings.TrimSpace(policyOf)
	policyType := model.GetPathVariablePolicyType(policyOf)
	if len(policyType) == 0 {
		handler.logger.Errorw("given kind of policy isn't supported yet", "policyOf", policyOf)
		common.WriteJsonResp(w, errors.New("given kind of policy isn't supported yet"), nil, http.StatusBadRequest)
		return
	}
	err := handler.commonPolicyActionService.DeletePolicy(ctx, policyName, policyType)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, nil, http.StatusOK)
}

func (handler *CommonPolicyRestHandlerImpl) GetPoliciesList(w http.ResponseWriter, r *http.Request) {
	ctx := util.NewRequestCtx(r.Context())

	if ok := handler.enforcer.Enforce(ctx.GetToken(), casbin.ResourceGlobal, casbin.ActionGet, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}

	vars := mux.Vars(r)
	policyOf := vars["policyOf"]
	policyOf = strings.TrimSpace(policyOf)
	policyType := model.GetPathVariablePolicyType(policyOf)
	v := r.URL.Query()
	if !v.Has("lite") {
		v.Set("lite", "true") // default value is set to TRUE, for backward compatibility
	}
	var schemaDecoder = schema.NewDecoder()
	schemaDecoder.IgnoreUnknownKeys(true)
	queryParams := bean2.PolicyListQuery{}
	err := schemaDecoder.Decode(&queryParams, v)
	if err != nil {
		handler.logger.Errorw("error in parsing query param", "query", v, "err", err)
		return
	}
	policies, err := handler.commonPolicyActionService.GetPoliciesList(ctx, policyType, queryParams.Lite)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, policies, http.StatusOK)
}

func (handler *CommonPolicyRestHandlerImpl) GetPolicyDetail(w http.ResponseWriter, r *http.Request) {
	ctx := util.NewRequestCtx(r.Context())
	if ok := handler.enforcer.Enforce(ctx.GetToken(), casbin.ResourceGlobal, casbin.ActionGet, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}

	vars := mux.Vars(r)
	policyOf := vars["policyOf"]
	policyOf = strings.TrimSpace(policyOf)

	queryParams := r.URL.Query()
	policyName := queryParams.Get("identifier")

	policyDetail, err := handler.commonPolicyActionService.GetPolicyDetail(ctx, policyName, model.GetPathVariablePolicyType(policyOf))
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, policyDetail, http.StatusOK)
}

func (handler *CommonPolicyRestHandlerImpl) ApplyPolicyToIdentifiersAlpha1(w http.ResponseWriter, r *http.Request) {
	ctx := util.NewRequestCtx(r.Context())
	if ok := handler.enforcer.Enforce(ctx.GetToken(), casbin.ResourceGlobal, casbin.ActionGet, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	reqBody := &model.ProfileSelectorDto{}
	decoder := json.NewDecoder(r.Body)
	err := decoder.Decode(reqBody)
	if err != nil {
		handler.logger.Errorw("error in decoding the request payload", "requestBody", r.Body, "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	handler.logger.Infow("apply policy to identifiers", "reqBody", reqBody)
	err = handler.commonPolicyActionService.ApplyPolicyToIdentifiersAlpha1(ctx, reqBody)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, nil, http.StatusOK)
}

func (handler *CommonPolicyRestHandlerImpl) HandleCorruptedApprovalConfigData(w http.ResponseWriter, r *http.Request) {
	v := r.URL.Query()
	apply := false
	if v.Get("apply") == "true" {
		apply = true
	}

	ctx := util.NewRequestCtx(r.Context())
	if ok := handler.enforcer.Enforce(ctx.GetToken(), casbin.ResourceGlobal, casbin.ActionUpdate, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}

	corruptedMappings, err := handler.commonPolicyActionService.GetCorruptedApprovalPolicyMetaData()
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	profileSelectorDtos, err := read.GetProfileSelectors(corruptedMappings)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	if !apply {
		common.WriteJsonResp(w, nil, profileSelectorDtos, http.StatusOK)
		return
	}

	handler.logger.Infow(resourceQualifiers.ApprovalConfigCorruptionLog("handling approval config corrupt data for approval policies"), "corruptedMappings", corruptedMappings, "profileSelectorDtos", profileSelectorDtos)
	err = handler.commonPolicyActionService.HandleCorruptedApprovalConfigData(ctx, profileSelectorDtos)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, nil, http.StatusOK)
}

func (handler *CommonPolicyRestHandlerImpl) UpdateApplyPolicyToIdentifiersAlpha1(w http.ResponseWriter, r *http.Request) {
	ctx := util.NewRequestCtx(r.Context())
	if ok := handler.enforcer.Enforce(ctx.GetToken(), casbin.ResourceGlobal, casbin.ActionGet, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	var schemaDecoder = schema.NewDecoder()
	schemaDecoder.IgnoreUnknownKeys(true)
	queryParams := bean2.SelectorConfig{}
	err := schemaDecoder.Decode(&queryParams, r.URL.Query())
	if err != nil {
		handler.logger.Errorw("error in parsing query param", "err", err)
		return
	}
	reqBody := &model.ProfileSelectorDto{}
	decoder := json.NewDecoder(r.Body)
	err = decoder.Decode(reqBody)
	if err != nil {
		handler.logger.Errorw("error in decoding the request payload", "requestBody", r.Body, "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	handler.logger.Infow("update apply policy to identifiers", "queryParams", queryParams, "reqBody", reqBody)
	err = handler.commonPolicyActionService.UpdateApplyPolicyToIdentifiersAlpha1(ctx, reqBody, &queryParams)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, nil, http.StatusOK)
}

func (handler *CommonPolicyRestHandlerImpl) PatchApplyPolicyToIdentifiersBulkAlpha1(w http.ResponseWriter, r *http.Request) {
	ctx := util.NewRequestCtx(r.Context())
	if ok := handler.enforcer.Enforce(ctx.GetToken(), casbin.ResourceGlobal, casbin.ActionGet, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	var schemaDecoder = schema.NewDecoder()
	schemaDecoder.IgnoreUnknownKeys(true)
	queryParams := bean2.SelectorConfig{}
	err := schemaDecoder.Decode(&queryParams, r.URL.Query())
	if err != nil {
		handler.logger.Errorw("error in parsing query param", "err", err)
		return
	}
	reqBody := &model.PatchBulkProfileSelectorRequest{}
	decoder := json.NewDecoder(r.Body)
	err = decoder.Decode(reqBody)
	if err != nil {
		handler.logger.Errorw("error in decoding the request payload", "requestBody", r.Body, "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	handler.logger.Infow("patch bulk apply policy to identifiers", "queryParams", queryParams, "reqBody", reqBody)
	err = handler.commonPolicyActionService.PatchApplyPolicyToIdentifiersBulkAlpha1(ctx, reqBody, &queryParams, nil)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, nil, http.StatusOK)
}

func (handler *CommonPolicyRestHandlerImpl) PatchApplyPolicyToIdentifiersAlpha1(w http.ResponseWriter, r *http.Request) {
	ctx := util.NewRequestCtx(r.Context())
	if ok := handler.enforcer.Enforce(ctx.GetToken(), casbin.ResourceGlobal, casbin.ActionGet, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	var schemaDecoder = schema.NewDecoder()
	schemaDecoder.IgnoreUnknownKeys(true)
	queryParams := bean2.SelectorConfig{}
	err := schemaDecoder.Decode(&queryParams, r.URL.Query())
	if err != nil {
		handler.logger.Errorw("error in parsing query param", "err", err)
		return
	}
	reqBody := &model.PatchProfileSelectorRequest{}
	decoder := json.NewDecoder(r.Body)
	err = decoder.Decode(reqBody)
	if err != nil {
		handler.logger.Errorw("error in decoding the request payload", "requestBody", r.Body, "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	if reqBody.Selector == 0 {
		//either user has not given any selector or using older UI
		err = util4.DefaultApiError().WithHttpStatusCode(http.StatusBadRequest).WithInternalMessage("no selector provided in payload. Use */bulk API for bulk update")
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	reqBody.PolicyOperation = patchQuery.Replace
	handler.logger.Infow("patch apply policy to identifiers", "queryParams", queryParams, "reqBody", reqBody)
	err = handler.commonPolicyActionService.PatchApplyPolicyToIdentifiersAlpha1(ctx, reqBody, &queryParams)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, nil, http.StatusOK)
}

func (handler *CommonPolicyRestHandlerImpl) GetAppliedPolicyDetail(w http.ResponseWriter, r *http.Request) {
	ctx := util.NewRequestCtx(r.Context())
	if ok := handler.enforcer.Enforce(ctx.GetToken(), casbin.ResourceGlobal, casbin.ActionGet, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	var schemaDecoder = schema.NewDecoder()
	schemaDecoder.IgnoreUnknownKeys(true)
	queryParams := bean2.GetSelectorConfigQuery{}
	err := schemaDecoder.Decode(&queryParams, r.URL.Query())
	if err != nil {
		handler.logger.Errorw("error in parsing query param", "err", err)
		return
	}
	handler.logger.Infow("get applied policy detail", "queryParams", queryParams)
	resp, err := handler.commonPolicyActionService.GetAppliedPolicyDetail(ctx, &queryParams)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	if resp == nil {
		common.WriteJsonResp(w, err, nil, http.StatusNotFound)
		return
	}
	common.WriteJsonResp(w, nil, resp, http.StatusOK)
}

// Deprecated: method GetAllAppliedPoliciesOld, TODO: remove this method
func (handler *CommonPolicyRestHandlerImpl) GetAllAppliedPoliciesOld(w http.ResponseWriter, r *http.Request) {
	ctx := util.NewRequestCtx(r.Context())
	if ok := handler.enforcer.Enforce(ctx.GetToken(), casbin.ResourceGlobal, casbin.ActionGet, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	v := r.URL.Query()
	var schemaDecoder = schema.NewDecoder()
	schemaDecoder.IgnoreUnknownKeys(true)
	queryParams := bean2.GetAllSelectorConfigQuery{}
	err := schemaDecoder.Decode(&queryParams, v)
	if err != nil {
		handler.logger.Errorw("error in parsing query param", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	handler.logger.Infow("get all applied policies", "queryParams", queryParams)
	filter := &model.PolicyAppEnvFilterRequest{}
	for _, criteria := range queryParams.FilterCriteria {
		criteriaDecoder, err := util2.DecodeFilterCriteriaStringMulti(criteria)
		if err != nil {
			handler.logger.Errorw("error encountered in applyFilterCriteriaOnResourceObjects", "queryParams", queryParams, "err", err)
			common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
			return
		}
		filter = adapter.UpdateFiltersForCriteriaMultiQuery(criteriaDecoder, filter)
	}
	resp, err := handler.commonPolicyActionService.GetAllAppliedPolicies(ctx, queryParams.Kind, filter)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, resp, http.StatusOK)
}

func (handler *CommonPolicyRestHandlerImpl) GetAllAppliedPolicies(w http.ResponseWriter, r *http.Request) {
	ctx := util.NewRequestCtx(r.Context())
	if ok := handler.enforcer.Enforce(ctx.GetToken(), casbin.ResourceGlobal, casbin.ActionGet, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	v := r.URL.Query()
	var schemaDecoder = schema.NewDecoder()
	schemaDecoder.IgnoreUnknownKeys(true)
	queryParams := bean2.MandatorySelectorConfigQuery{}
	err := schemaDecoder.Decode(&queryParams, v)
	if err != nil {
		handler.logger.Errorw("error in parsing query param", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	handler.logger.Infow("get all applied policies", "queryParams", queryParams)
	payload := bean2.ListAppliedPoliciesFilter{}
	decoder := json.NewDecoder(r.Body)
	err = decoder.Decode(&payload)
	if err != nil {
		handler.logger.Errorw("error in decoding the request payload", "err", err, "requestBody", r.Body)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	filter := &model.PolicyAppEnvFilterRequest{}
	for _, filterCriteria := range payload.FilterCriteria {
		criteriaDecoder, err := util2.DecodeFilterCriteriaString(filterCriteria, true)
		if err != nil {
			handler.logger.Errorw("error encountered in applyFilterCriteriaOnResourceObjects", "queryParams", queryParams, "err", err)
			common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
			return
		}
		filter = adapter.UpdateFiltersForCriteriaQuery(criteriaDecoder, filter)
	}
	resp, err := handler.commonPolicyActionService.GetAllAppliedPolicies(ctx, queryParams.Kind, filter)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, resp, http.StatusOK)
}

func (handler *CommonPolicyRestHandlerImpl) ListAllAppliedSelectors(w http.ResponseWriter, r *http.Request) {
	ctx := util.NewRequestCtx(r.Context())
	if ok := handler.enforcer.Enforce(ctx.GetToken(), casbin.ResourceGlobal, casbin.ActionGet, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	v := r.URL.Query()
	var schemaDecoder = schema.NewDecoder()
	schemaDecoder.IgnoreUnknownKeys(true)
	queryParams := bean2.ListAllAppliedSelectorsQuery{}
	err := schemaDecoder.Decode(&queryParams, v)
	if err != nil {
		handler.logger.Errorw("error in parsing query param", "err", err)
		return
	}
	handler.logger.Infow("get all applied selectors", "queryParams", queryParams)
	var searchableKeys []bean3.DtResSearchableKeyName
	for _, selectorKind := range queryParams.Selectors {
		searchableKey, err := util3.DecodeSelectorTypeQueryString(selectorKind)
		if err != nil {
			handler.logger.Errorw("error in decoding selector type query string", "selectorKind", selectorKind, "err", err)
			common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
			return
		}
		searchableKeys = append(searchableKeys, searchableKey)
	}
	resp, err := handler.commonPolicyActionService.ListAllAppliedSelectors(ctx, queryParams.Kind, searchableKeys)
	if err != nil {
		handler.logger.Errorw("error in getting all applied selectors", "queryParams", queryParams, "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, resp, http.StatusOK)
}

func (handler *CommonPolicyRestHandlerImpl) getDeleteAppliedPolicyRequest(r *http.Request) (*bean2.DeleteAppliedPolicyRequest, error) {
	request := bean2.DeleteAppliedPolicyRequest{}
	jsonDecoder := json.NewDecoder(r.Body)
	err := jsonDecoder.Decode(&request)
	if err != nil && err != io.EOF {
		handler.logger.Errorw("error in decoding the request payload", "err", err, "requestBody", r.Body)
		return nil, err
	} else if err == io.EOF {
		var schemaDecoder = schema.NewDecoder()
		schemaDecoder.IgnoreUnknownKeys(true)
		err = schemaDecoder.Decode(&request, r.URL.Query())
		if err != nil {
			handler.logger.Errorw("error in parsing query param", "err", err)
			return nil, err
		}
	}
	return &request, err
}

func (handler *CommonPolicyRestHandlerImpl) DeleteAppliedPolicies(w http.ResponseWriter, r *http.Request) {
	ctx := util.NewRequestCtx(r.Context())
	if ok := handler.enforcer.Enforce(ctx.GetToken(), casbin.ResourceGlobal, casbin.ActionDelete, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	request, err := handler.getDeleteAppliedPolicyRequest(r)
	if err != nil {
		handler.logger.Errorw("error in parsing request", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	handler.logger.Infow("delete applied policies", "request", request)
	err = handler.commonPolicyActionService.DeleteResourceQualifiersMappingCriteria(ctx, request)
	if err != nil {
		handler.logger.Errorw("error in deleting resources", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, nil, http.StatusOK)
}

func (handler *CommonPolicyRestHandlerImpl) handleEnforcementRBAC(ctx *util.RequestCtx, resourceKind bean3.DtResKind, resourceIdentifierDetails *bean2.ResourceIdentifierDetails) (statusCode int, err error) {
	if resourceKind == bean3.DevtronResourceCiPipeline {
		// find application id
		if resourceIdentifierDetails.ApplicationKindCriteria != nil {
			if len(resourceIdentifierDetails.ApplicationKindCriteria.Values) != 1 {
				return http.StatusBadRequest, errors.New("invalid applicationKindCriteria. bulk action is not supported")
			}
			appName := resourceIdentifierDetails.ApplicationKindCriteria.Values[0]
			appRbacName := handler.enforcerUtil.GetAppRBACName(appName)
			if authorised := handler.enforcer.Enforce(ctx.GetToken(), casbin.ResourceApplications, casbin.ActionGet, appRbacName); !authorised {
				return http.StatusForbidden, errors.New("unauthorized")
			}
		} else if resourceIdentifierDetails.CiPipelineKindCriteria != nil {
			ciPipelineIds := resourceIdentifierDetails.CiPipelineKindCriteria.ValueIntegers
			appRbacObjects := handler.enforcerUtil.GetAppObjectByCiPipelineIds(ciPipelineIds)
			rbacResp := handler.enforcer.EnforceInBatch(ctx.GetToken(), casbin.ResourceApplications, casbin.ActionGet, maps.Values(appRbacObjects))
			for _, authorised := range rbacResp {
				if !authorised {
					return http.StatusForbidden, errors.New("unauthorized")
				}
			}
		} else {
			return http.StatusBadRequest, errors.New("insufficient resourceIdentifier")
		}
	} else if resourceKind == bean3.DevtronResourceCdPipeline {
		if resourceIdentifierDetails.ApplicationKindCriteria == nil || resourceIdentifierDetails.EnvKindCriteria == nil {
			return http.StatusBadRequest, errors.New("insufficient resourceIdentifier")
		}
		if len(resourceIdentifierDetails.ApplicationKindCriteria.Values) != 1 || len(resourceIdentifierDetails.EnvKindCriteria.Values) != 1 {
			return http.StatusBadRequest, errors.New("invalid applicationKindCriteria or envKindCriteria. bulk action is not supported")
		}
		appName := resourceIdentifierDetails.ApplicationKindCriteria.Values[0]
		envName := resourceIdentifierDetails.EnvKindCriteria.Values[0]
		appRbacName := handler.enforcerUtil.GetAppRBACName(appName)
		envRbacName := handler.enforcerUtil.GetEnvRBACNameByAppAndEnvName(appName, envName)
		if authorised := handler.enforcer.Enforce(ctx.GetToken(), casbin.ResourceApplications, casbin.ActionGet, appRbacName); !authorised {
			return http.StatusForbidden, errors.New("unauthorized")
		}
		if authorised := handler.enforcer.Enforce(ctx.GetToken(), casbin.ResourceEnvironment, casbin.ActionGet, envRbacName); !authorised {
			return http.StatusForbidden, errors.New("unauthorized")
		}

	} else {
		return http.StatusBadRequest, errors.New("resource kind not supported yet")
	}
	return
}

func (handler *CommonPolicyRestHandlerImpl) EnforcementInfoOfPolicyOnResource(w http.ResponseWriter, r *http.Request) {
	ctx := util.NewRequestCtx(r.Context())
	vars := mux.Vars(r)
	apiVersion := vars["apiVersion"]
	apiVersion = strings.TrimSpace(apiVersion)
	resourceKind := bean3.ToDtResKind(vars["resourceKind"])
	if resourceKind != bean3.DevtronResourceCdPipeline && resourceKind != bean3.DevtronResourceCiPipeline {
		common.WriteJsonResp(w, errors.New(fmt.Sprintf("resourceKind: %s not supported", resourceKind)), nil, http.StatusBadRequest)
		return
	}
	var schemaDecoder = schema.NewDecoder()
	schemaDecoder.IgnoreUnknownKeys(true)
	queryParams := bean2.ResourceIdentifiersQuery{}
	err := schemaDecoder.Decode(&queryParams, r.URL.Query())
	if err != nil {
		handler.logger.Errorw("error in parsing query param", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	if len(queryParams.ResourceIdentifier) == 0 {
		common.WriteJsonResp(w, errors.New("empty resource identifiers"), nil, http.StatusBadRequest)
		return
	}
	filterCriteriaList := make([]*bean3.FilterCriteriaDecoderMulti, 0)
	resourceIdentifierDetails := &bean2.ResourceIdentifierDetails{}
	for _, resourceIdentifier := range queryParams.ResourceIdentifier {
		filterCriteria, err := util2.DecodeFilterCriteriaStringMulti(resourceIdentifier)
		if err != nil {
			common.WriteJsonResp(w, errors.New(fmt.Sprintf("invalid resourceIdentifier: %s", resourceIdentifier)), nil, http.StatusBadRequest)
			return
		}
		resourceIdentifierDetails = adapter.UpdateResourceIdentifierDetails(filterCriteria, resourceIdentifierDetails)
		filterCriteriaList = append(filterCriteriaList, filterCriteria)
	}

	// handle rbac
	statusCode, err := handler.handleEnforcementRBAC(ctx, resourceKind, resourceIdentifierDetails)
	if err != nil {
		common.WriteJsonResp(w, err, nil, statusCode)
		return
	}
	// handle rbac done

	resp, err := handler.offendingPolicyResourcesService.EnforcementInfoForResource(resourceKind, resourceIdentifierDetails)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	common.WriteJsonResp(w, nil, resp, http.StatusOK)
	return
}

func (handler *CommonPolicyRestHandlerImpl) ListOffendingResourceList(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	apiVersion := vars["apiVersion"]
	apiVersion = strings.TrimSpace(apiVersion)
	resourceKind := bean3.ToDtResKind(vars["resourceKind"])
	if resourceKind != (bean3.DevtronResourceDevtronApplicationFull) {
		common.WriteJsonResp(w, errors.New(fmt.Sprintf("resourceKind: %s not supported", resourceKind)), nil, http.StatusBadRequest)
		return
	}

	var schemaDecoder = schema.NewDecoder()
	schemaDecoder.IgnoreUnknownKeys(true)
	queryParams := bean4.OffendingApplicationRequest{}
	err := schemaDecoder.Decode(&queryParams, r.URL.Query())
	if err != nil {
		handler.logger.Errorw("error in parsing query param", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	resp, err := handler.offendingPolicyResourcesService.GetNonCompliantApps(&queryParams)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, resp, http.StatusOK)
}

func (handler *CommonPolicyRestHandlerImpl) GetExceptionUsersProfile(w http.ResponseWriter, r *http.Request) {
	ctx := util.NewRequestCtx(r.Context())
	if ok := handler.enforcer.Enforce(ctx.GetToken(), casbin.ResourceGlobal, casbin.ActionCreate, casbin.StarObject); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}

	apiVersion, policyOf := util3.FetchApiVersionAndPolicyOfFromPathParams(mux.Vars(r))
	policyType := model.GetPathVariablePolicyType(policyOf)

	err := validator2.ValidateExceptionUsersRequest(apiVersion, policyType)
	if err != nil {
		handler.logger.Errorw("error in validating get exception users request", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	resp, err := handler.commonPolicyActionService.GetExceptionUsersProfileList(ctx, policyType.ToGlobalPolicyType())
	if err != nil {
		handler.logger.Errorw("service err, GetExceptionUsersProfileList", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, resp, http.StatusOK)

}

func (handler *CommonPolicyRestHandlerImpl) PatchExceptionUsersProfile(w http.ResponseWriter, r *http.Request) {
	ctx := util.NewRequestCtx(r.Context())
	if ok := handler.enforcer.Enforce(ctx.GetToken(), casbin.ResourceGlobal, casbin.ActionCreate, casbin.StarObject); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}

	apiVersion, policyOf := util3.FetchApiVersionAndPolicyOfFromPathParams(mux.Vars(r))
	policyType := model.GetPathVariablePolicyType(policyOf)
	err := validator2.ValidateExceptionUsersRequest(apiVersion, policyType)
	if err != nil {
		handler.logger.Errorw("error in validating patch exception users request", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	patchPayload := &model.ExceptionUsersPatchPayload{}
	decoder := json.NewDecoder(r.Body)
	err = decoder.Decode(patchPayload)
	if err != nil {
		handler.logger.Errorw("error in decoding the patch payload", "requestBody", r.Body, "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	policyRevision, err := handler.commonPolicyActionService.PatchExceptionUsers(ctx, patchPayload)
	if err != nil {
		handler.logger.Errorw("service err, PatchExceptionUsers", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, policyRevision, http.StatusOK)

}
