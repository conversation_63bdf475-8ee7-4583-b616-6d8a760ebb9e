/*
 * Copyright (c) 2024. Devtron Inc.
 */

package drafts

import (
	"github.com/devtron-labs/devtron/enterprise/pkg/drafts"
	rbac2 "github.com/devtron-labs/devtron/enterprise/pkg/drafts/rbac"
	"github.com/devtron-labs/devtron/enterprise/pkg/drafts/repository"
	"github.com/google/wire"
)

var DraftsWireSet = wire.NewSet(
	repository.NewConfigDraftRepositoryImpl,
	wire.Bind(new(repository.ConfigDraftRepository), new(*repository.ConfigDraftRepositoryImpl)),
	drafts.NewConfigDraftServiceImpl,
	wire.Bind(new(drafts.ConfigDraftService), new(*drafts.ConfigDraftServiceImpl)),
	NewConfigDraftRestHandlerImpl,
	wire.Bind(new(ConfigDraftRestHandler), new(*ConfigDraftRestHandlerImpl)),
	NewConfigDraftRouterImpl,
	wire.Bind(new(ConfigDraftRouter), new(*ConfigDraftRouterImpl)),
	rbac2.NewApproverRbacServiceImpl,
	wire.Bind(new(rbac2.ApproverRbacService), new(*rbac2.ApproverRbacServiceImpl)),
)
