/*
 * Copyright (c) 2024. Devtron Inc.
 */

package lockConfiguation

import (
	"encoding/json"
	"errors"
	"github.com/devtron-labs/devtron/api/restHandler/common"
	"github.com/devtron-labs/devtron/enterprise/pkg/lockConfiguration"
	"github.com/devtron-labs/devtron/pkg/auth/authorisation/casbin"
	"github.com/devtron-labs/devtron/pkg/auth/user"
	beans "github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/bean"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/lockConfiguration/bean"
	bean2 "github.com/devtron-labs/devtron/pkg/resourceQualifiers"
	"github.com/devtron-labs/devtron/util"
	"github.com/devtron-labs/devtron/util/rbac"
	"go.uber.org/zap"
	"gopkg.in/go-playground/validator.v9"
	"net/http"
	"strconv"
)

type LockConfigRestHandler interface {
	GetLockConfig(w http.ResponseWriter, r *http.Request)
	CreateLockConfig(w http.ResponseWriter, r *http.Request)
	DeleteLockConfig(w http.ResponseWriter, r *http.Request)
}

type LockConfigRestHandlerImpl struct {
	logger                   *zap.SugaredLogger
	userService              user.UserService
	enforcer                 casbin.Enforcer
	validator                *validator.Validate
	lockConfigurationService lockConfiguration.LockConfigurationService
	userCommonService        user.UserCommonService
	enforcerUtil             rbac.EnforcerUtil
}

func NewLockConfigRestHandlerImpl(logger *zap.SugaredLogger,
	userService user.UserService,
	enforcer casbin.Enforcer,
	validator *validator.Validate,
	lockConfigurationService lockConfiguration.LockConfigurationService,
	userCommonService user.UserCommonService,
	enforcerUtil rbac.EnforcerUtil) *LockConfigRestHandlerImpl {
	return &LockConfigRestHandlerImpl{
		logger:                   logger,
		userService:              userService,
		enforcer:                 enforcer,
		validator:                validator,
		lockConfigurationService: lockConfigurationService,
		userCommonService:        userCommonService,
		enforcerUtil:             enforcerUtil,
	}

}

func (handler LockConfigRestHandlerImpl) GetLockConfig(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userService.GetLoggedInUser(r)
	if err != nil || userId == 0 {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	token := r.Header.Get("token")
	isAuthorised := false
	var envId, appId int
	//checking approval access if appId, envId query params are received, if yes then won't check the lengthier rbac
	if appIdStr := r.URL.Query().Get("appId"); len(appIdStr) > 0 {
		if appId, err = strconv.Atoi(appIdStr); err != nil {
			common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
			return
		} else {
			envId = bean2.BaseDeploymentTemplateInt
			if envIdStr := r.URL.Query().Get("envId"); len(envIdStr) > 0 {
				if envId, err = strconv.Atoi(envIdStr); err != nil {
					common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
					return
				}
				if envId == beans.BASE_CONFIG_ENV_ID {
					envId = bean2.BaseDeploymentTemplateInt
				}
			}
			if handler.checkForConfigApproverAccess(envId, appId, token) {
				isAuthorised = true
			}
		}
	}
	if !isAuthorised {
		isAuthorised, err = handler.userService.IsUserAdminOrManagerForAnyApp(userId, token)
		if err != nil {
			common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
			return
		}
		//still not authorised, throw error
		if !isAuthorised {
			common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
			return
		}
	}
	resp, err := handler.lockConfigurationService.GetLockConfiguration(appId, envId)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	if resp == nil {
		//send empty response
		resp = &bean.LockConfigResponse{
			Paths: []string{},
		}
	}
	common.WriteJsonResp(w, err, resp, http.StatusOK)
}

func (handler LockConfigRestHandlerImpl) CreateLockConfig(w http.ResponseWriter, r *http.Request) {
	ctx := util.NewRequestCtx(r.Context())
	userId, err := handler.userService.GetLoggedInUser(r)

	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}

	// handle super-admin RBAC
	token := r.Header.Get("token")
	if ok := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionUpdate, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}

	// decode request
	decoder := json.NewDecoder(r.Body)
	var request *bean.LockConfigRequest
	err = decoder.Decode(&request)
	if err != nil {
		handler.logger.Errorw("err in decoding request in LockConfigRequest", "err", err, "body", r.Body)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	// validate request
	err = handler.validator.Struct(request)
	if err != nil {
		handler.logger.Errorw("validation err in LockConfigRequest", "err", err, "request", request)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	// service call
	err = handler.lockConfigurationService.SaveLockConfiguration(ctx, request)
	if err != nil {
		handler.logger.Errorw("service err, SaveLockConfiguration", "err", err, "payload", request)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, request, http.StatusOK)
}

func (handler LockConfigRestHandlerImpl) DeleteLockConfig(w http.ResponseWriter, r *http.Request) {
	ctx := util.NewRequestCtx(r.Context())
	userId, err := handler.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	// handle super-admin RBAC
	token := r.Header.Get("token")
	if ok := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionUpdate, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	// service call
	err = handler.lockConfigurationService.DeleteActiveLockConfiguration(ctx)
	if err != nil {
		handler.logger.Errorw("service err, DeleteActiveLockConfiguration", "err", err, "userId", userId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, nil, http.StatusOK)
}

func (handler LockConfigRestHandlerImpl) CheckAdminAuth(resource, token string, object string) bool {
	if ok := handler.enforcer.Enforce(token, resource, casbin.ActionCreate, object); !ok {
		return false
	}
	return true
}

func (handler LockConfigRestHandlerImpl) checkForConfigApproverAccess(envId int, appId int, token string) bool {
	var object string
	if envId > 0 {
		object = handler.enforcerUtil.GetTeamEnvRBACNameByAppId(appId, envId)
	} else {
		object = handler.enforcerUtil.GetTeamNoEnvRBACNameByAppId(appId)
	}
	return handler.enforcer.Enforce(token, casbin.ResourceConfig, casbin.ActionApprove, object)
}
