/*
 * Copyright (c) 2024. Devtron Inc.
 */

package lockConfiguation

import (
	"github.com/devtron-labs/devtron/enterprise/pkg/lockConfiguration"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/lockConfiguration/repository"
	"github.com/google/wire"
)

var LockConfigWireSet = wire.NewSet(
	repository.NewRepositoryImpl,
	wire.Bind(new(repository.LockConfigurationRepository), new(*repository.RepositoryImpl)),
	lockConfiguration.NewLockConfigurationServiceImpl,
	wire.Bind(new(lockConfiguration.LockConfigurationService), new(*lockConfiguration.LockConfigurationServiceImpl)),
	NewLockConfigRestHandlerImpl,
	wire.Bind(new(LockConfigRestHandler), new(*LockConfigRestHandlerImpl)),
	NewLockConfigurationRouterImpl,
	wire.Bind(new(LockConfigurationRouter), new(*LockConfigurationRouterImpl)),
)
