/*
 * Copyright (c) 2024. Devtron Inc.
 */

package protect

import (
	"github.com/devtron-labs/devtron/enterprise/pkg/protect"
	"github.com/devtron-labs/devtron/enterprise/pkg/protect/repository"
	"github.com/google/wire"
)

var ProtectWireSet = wire.NewSet(
	repository.NewResourceProtectionRepositoryImpl,
	wire.Bind(new(repository.ResourceProtectionRepository), new(*repository.ResourceProtectionRepositoryImpl)),
	protect.NewResourceProtectionServiceImpl,
	wire.Bind(new(protect.ResourceProtectionService), new(*protect.ResourceProtectionServiceImpl)),
	NewResourceProtectionRestHandlerImpl,
	wire.Bind(new(ResourceProtectionRestHandler), new(*ResourceProtectionRestHandlerImpl)),
	NewResourceProtectionRouterImpl,
	wire.Bind(new(ResourceProtectionRouter), new(*ResourceProtectionRouterImpl)),
)
