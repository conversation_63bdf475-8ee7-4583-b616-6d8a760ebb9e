/*
 * Copyright (c) 2024. Devtron Inc.
 */

package protect

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/devtron-labs/devtron/api/restHandler/common"
	"github.com/devtron-labs/devtron/enterprise/pkg/protect"
	"github.com/devtron-labs/devtron/enterprise/pkg/protect/bean"
	"github.com/devtron-labs/devtron/pkg/auth/authorisation/casbin"
	"github.com/devtron-labs/devtron/pkg/auth/user"
	"github.com/devtron-labs/devtron/pkg/auth/user/util"
	"github.com/devtron-labs/devtron/util/rbac"
	"go.uber.org/zap"
	"gopkg.in/go-playground/validator.v9"
	"net/http"
)

type ResourceProtectionRestHandler interface {
	// deprecated
	ConfigureResourceProtect(w http.ResponseWriter, r *http.Request)

	GetResourceProtectMetadataV1(w http.ResponseWriter, r *http.Request)
	GetResourceProtectMetadataV2(w http.ResponseWriter, r *http.Request)

	GetResourceProtectMetadataForEnvV1(w http.ResponseWriter, r *http.Request)
	GetResourceProtectMetadataForEnvV2(w http.ResponseWriter, r *http.Request)
}

type ResourceProtectionRestHandlerImpl struct {
	logger                    *zap.SugaredLogger
	userService               user.UserService
	enforcer                  casbin.Enforcer
	enforcerUtil              rbac.EnforcerUtil
	validator                 *validator.Validate
	resourceProtectionService protect.ResourceProtectionService
}

func NewResourceProtectionRestHandlerImpl(logger *zap.SugaredLogger, resourceProtectionService protect.ResourceProtectionService,
	userService user.UserService, enforcer casbin.Enforcer, enforcerUtil rbac.EnforcerUtil,
	validator *validator.Validate) *ResourceProtectionRestHandlerImpl {
	return &ResourceProtectionRestHandlerImpl{
		logger:                    logger,
		userService:               userService,
		enforcer:                  enforcer,
		enforcerUtil:              enforcerUtil,
		validator:                 validator,
		resourceProtectionService: resourceProtectionService,
	}
}

// deprecated
// ConfigureResourceProtect
func (handler *ResourceProtectionRestHandlerImpl) ConfigureResourceProtect(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	var request bean.ResourceProtectModel
	decoder := json.NewDecoder(r.Body)
	err = decoder.Decode(&request)
	if err != nil {
		handler.logger.Errorw("err in decoding request in ResourceProtectModel", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	// validate request
	err = handler.validator.Struct(request)
	if err != nil {
		handler.logger.Errorw("validation err in ResourceProtectModel", "err", err, "request", request)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	token := r.Header.Get("token")
	if ok := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionCreate, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	request.UserId = userId
	// writes are disabled for this flow
	common.WriteJsonResp(w, nil, nil, http.StatusOK)
}

func (handler *ResourceProtectionRestHandlerImpl) GetResourceProtectMetadataV2(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	appId, err := common.ExtractIntQueryParam(w, r, "appId", 0)
	if err != nil {
		return
	}

	token := r.Header.Get("token")
	object := handler.enforcerUtil.GetAppRBACNameByAppId(appId)
	if ok := handler.enforcer.Enforce(token, casbin.ResourceApplications, casbin.ActionGet, object); !ok {
		common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
		return
	}

	ctx := r.Context()
	isSuperAdmin := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionCreate, "*")
	userMetadata := util.GetUserMetadata(r.Context(), userId, isSuperAdmin)
	protectModels, err := handler.resourceProtectionService.GetEnhancedResourceProtectMetadataV2(ctx, appId, userMetadata)
	if err != nil {
		handler.logger.Errorw("error occurred while fetching resource protection", "err", err, "appId", appId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, protectModels, http.StatusOK)

}

func (handler *ResourceProtectionRestHandlerImpl) GetResourceProtectMetadataV1(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	appId, err := common.ExtractIntQueryParam(w, r, "appId", 0)
	if err != nil {
		return
	}

	token := r.Header.Get("token")
	object := handler.enforcerUtil.GetAppRBACNameByAppId(appId)
	if ok := handler.enforcer.Enforce(token, casbin.ResourceApplications, casbin.ActionGet, object); !ok {
		common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
		return
	}
	ctx := r.Context()
	isSuperAdmin := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionCreate, "*")
	userMetadata := util.GetUserMetadata(r.Context(), userId, isSuperAdmin)

	protectModels, err := handler.resourceProtectionService.GetResourceProtectMetadataV1(ctx, appId, userMetadata)
	if err != nil {
		handler.logger.Errorw("error occurred while fetching resource protection", "err", err, "appId", appId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, protectModels, http.StatusOK)

}

func (handler *ResourceProtectionRestHandlerImpl) GetResourceProtectMetadataForEnvV2(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	envId, err := common.ExtractIntQueryParam(w, r, "envId", 0)
	if err != nil {
		return
	}

	token := r.Header.Get("token")
	var rbacObjectArray []string
	rbacObjectVsAppIdMap := make(map[string]int)

	ctx := r.Context()
	isSuperAdmin := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionCreate, "*")
	userMetadata := util.GetUserMetadata(r.Context(), userId, isSuperAdmin)

	resourceProtectionDtos, err := handler.resourceProtectionService.GetEnhancedResourceProtectionEnabledForEnvV2(ctx, envId, userMetadata)
	if err != nil {
		handler.logger.Errorw("error occurred while fetching resource protection", "err", err, "envId", envId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	resourceProtectionDtosMap := make(map[int]*bean.ResourceProtectModel)
	for _, resourceProtectionDto := range resourceProtectionDtos {
		rbacObject := handler.enforcerUtil.GetTeamEnvRBACNameByAppId(resourceProtectionDto.AppId, envId)
		rbacObjectArray = append(rbacObjectArray, rbacObject)
		rbacObjectVsAppIdMap[rbacObject] = resourceProtectionDto.AppId
		resourceProtectionDtosMap[resourceProtectionDto.AppId] = resourceProtectionDto
	}

	rbacResponse := handler.enforcer.EnforceInBatch(token, casbin.ResourceApplications, casbin.ActionGet, rbacObjectArray)
	responseDtos := make([]*bean.ResourceProtectModel, 0)
	for rbacObj := range rbacResponse {
		appId := rbacObjectVsAppIdMap[rbacObj]
		responseDtos = append(responseDtos, resourceProtectionDtosMap[appId])
	}

	if len(responseDtos) == 0 {
		common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
		return
	}

	common.WriteJsonResp(w, nil, responseDtos, http.StatusOK)

}

func (handler *ResourceProtectionRestHandlerImpl) GetResourceProtectMetadataForEnvV1(w http.ResponseWriter, r *http.Request) {
	token := r.Header.Get("token")
	userId, err := handler.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	envId, err := common.ExtractIntQueryParam(w, r, "envId", 0)
	if err != nil {
		return
	}

	var rbacObjectArray []string
	rbacObjectVsAppIdMap := make(map[string]int)

	ctx := r.Context()
	isSuperAdmin := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionCreate, "*")
	userMetadata := util.GetUserMetadata(r.Context(), userId, isSuperAdmin)
	appStatues := handler.resourceProtectionService.ResourceProtectionEnabledForEnvV1(ctx, envId, userMetadata)
	for appId, _ := range appStatues {
		rbacObject := handler.enforcerUtil.GetTeamEnvRBACNameByAppId(appId, envId)
		rbacObjectArray = append(rbacObjectArray, rbacObject)
		rbacObjectVsAppIdMap[rbacObject] = appId
	}
	rbacResponse := handler.enforcer.EnforceInBatch(token, casbin.ResourceApplications, casbin.ActionGet, rbacObjectArray)

	appStatusResponse := make(map[int]bool)
	for rbacObj := range rbacResponse {
		appId := rbacObjectVsAppIdMap[rbacObj]
		appStatusResponse[appId] = appStatues[appId]
	}
	if len(appStatusResponse) == 0 && len(appStatues) != 0 {
		common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
		return
	}

	common.WriteJsonResp(w, nil, appStatusResponse, http.StatusOK)

}
