/*
 * Copyright (c) 2024. Devtron Inc.
 */

package protect

import "github.com/gorilla/mux"

type ResourceProtectionRouter interface {
	InitResourceProtectionRouter(protectRouter *mux.Router)
}

type ResourceProtectionRouterImpl struct {
	resourceProtectionRestHandler ResourceProtectionRestHandler
}

func NewResourceProtectionRouterImpl(resourceProtectionRestHandler ResourceProtectionRestHandler) *ResourceProtectionRouterImpl {
	return &ResourceProtectionRouterImpl{resourceProtectionRestHandler: resourceProtectionRestHandler}
}

func (router *ResourceProtectionRouterImpl) InitResourceProtectionRouter(protectRouter *mux.Router) {
	protectRouter.Path("").HandlerFunc(router.resourceProtectionRestHandler.ConfigureResourceProtect).
		Methods("POST")
	protectRouter.Path("").HandlerFunc(router.resourceProtectionRestHandler.GetResourceProtectMetadataV1).
		Queries("appId", "{appId}").
		Methods("GET")
	protectRouter.Path("/env").HandlerFunc(router.resourceProtectionRestHandler.GetResourceProtectMetadataForEnvV1).
		Queries("envId", "{envId}").
		Methods("GET")

	protectRouter.Path("/v2").HandlerFunc(router.resourceProtectionRestHandler.GetResourceProtectMetadataV2).
		Queries("appId", "{appId}").
		Methods("GET")
	protectRouter.Path("/v2/env").HandlerFunc(router.resourceProtectionRestHandler.GetResourceProtectMetadataForEnvV2).
		Queries("envId", "{envId}").
		Methods("GET")
}
