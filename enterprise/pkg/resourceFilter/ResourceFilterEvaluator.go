/*
 * Copyright (c) 2024. Devtron Inc.
 */

package resourceFilter

import (
	"github.com/devtron-labs/devtron/cel"
	"github.com/devtron-labs/devtron/util/expressionEvaluator"
	"go.uber.org/zap"
)

type ResourceFilterEvaluator interface {
	EvaluateFilter(filterConditions []expressionEvaluator.ResourceCondition, expressionMetadata cel.ExpressionMetadata) (bool, error)
	ValidateCELRequest(request expressionEvaluator.ValidateRequestResponse) (expressionEvaluator.ValidateRequestResponse, bool)
}

type ResourceFilterEvaluatorImpl struct {
	logger       *zap.SugaredLogger
	celEvaluator cel.EvaluatorService
}

func NewResourceFilterEvaluatorImpl(logger *zap.SugaredLogger, celEvaluator cel.EvaluatorService) (*ResourceFilterEvaluatorImpl, error) {
	return &ResourceFilterEvaluatorImpl{
		logger:       logger,
		celEvaluator: celEvaluator,
	}, nil
}

func (impl *ResourceFilterEvaluatorImpl) EvaluateFilter(filterConditions []expressionEvaluator.ResourceCondition, expressionMetadata cel.ExpressionMetadata) (bool, error) {
	exprResponse := expressionResponse{}
	for _, resourceCondition := range filterConditions {
		expression := resourceCondition.Expression
		celRequest := cel.Request{
			Expression:         expression,
			ExpressionMetadata: expressionMetadata,
		}
		response, err := impl.celEvaluator.EvaluateForBool(celRequest)
		if err != nil {
			return false, err
		}
		if resourceCondition.IsFailCondition() {
			exprResponse.blockConditionAvail = true
			exprResponse.blockResponse = response
		} else {
			exprResponse.allowConditionAvail = true
			exprResponse.allowResponse = response
		}
	}
	return exprResponse.getFinalResponse(), nil
}

func (impl *ResourceFilterEvaluatorImpl) ValidateCELRequest(request expressionEvaluator.ValidateRequestResponse) (expressionEvaluator.ValidateRequestResponse, bool) {
	errored := false
	params := []cel.ExpressionParam{
		{
			ParamName: cel.ContainerRepo,
			Type:      cel.ParamTypeString,
		},
		{
			ParamName: cel.ContainerImage,
			Type:      cel.ParamTypeString,
		},
		{
			ParamName: cel.ContainerImageTag,
			Type:      cel.ParamTypeString,
		},
		{
			ParamName: cel.ImageLabels,
			Type:      cel.ParamTypeList,
		},
		{
			ParamName: cel.GitCommitDetails,
			Type:      cel.ParamTypeMapStringToAny,
		},
	}

	for i, e := range request.Conditions {
		validateExpression := cel.Request{
			Expression:         e.Expression,
			ExpressionMetadata: cel.ExpressionMetadata{Params: params},
		}
		_, _, err := impl.celEvaluator.Validate(validateExpression)
		if err != nil {
			errored = true
			e.ErrorMsg = err.Error()
		}
		request.Conditions[i] = e
	}

	return request, errored
}
