// Code generated by mockery v2.14.0. DO NOT EDIT.

package mocks

import (
	"github.com/devtron-labs/devtron/enterprise/pkg/drafts/bean"
	drafts "github.com/devtron-labs/devtron/enterprise/pkg/drafts/repository/bean"
	mock "github.com/stretchr/testify/mock"
)

// ConfigDraftRepository is an autogenerated mock type for the ConfigDraftRepository type
type ConfigDraftRepository struct {
	mock.Mock
}

// CreateConfigDraft provides a mock function with given fields: request
func (_m *ConfigDraftRepository) CreateConfigDraft(request bean.ConfigDraftRequest) (*bean.ConfigDraftResponse, error) {
	ret := _m.Called(request)

	var r0 *bean.ConfigDraftResponse
	if rf, ok := ret.Get(0).(func(bean.ConfigDraftRequest) *bean.ConfigDraftResponse); ok {
		r0 = rf(request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bean.ConfigDraftResponse)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(bean.ConfigDraftRequest) error); ok {
		r1 = rf(request)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DeleteComment provides a mock function with given fields: draftId, draftCommentId, userId
func (_m *ConfigDraftRepository) DeleteComment(draftId int, draftCommentId int, userId int32) (int, error) {
	ret := _m.Called(draftId, draftCommentId, userId)

	var r0 int
	if rf, ok := ret.Get(0).(func(int, int, int32) int); ok {
		r0 = rf(draftId, draftCommentId, userId)
	} else {
		r0 = ret.Get(0).(int)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, int, int32) error); ok {
		r1 = rf(draftId, draftCommentId, userId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DiscardDrafts provides a mock function with given fields: appId, envId, userId
func (_m *ConfigDraftRepository) DiscardDrafts(appId int, envId int, userId int32) error {
	ret := _m.Called(appId, envId, userId)

	var r0 error
	if rf, ok := ret.Get(0).(func(int, int, int32) error); ok {
		r0 = rf(appId, envId, userId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetDraftMetadata provides a mock function with given fields: appId, envId, resourceType
func (_m *ConfigDraftRepository) GetDraftMetadata(appId int, envId int, resourceType bean.DraftResourceType) ([]*drafts.DraftDto, error) {
	ret := _m.Called(appId, envId, resourceType)

	var r0 []*drafts.DraftDto
	if rf, ok := ret.Get(0).(func(int, int, bean.DraftResourceType) []*drafts.DraftDto); ok {
		r0 = rf(appId, envId, resourceType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*drafts.DraftDto)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, int, bean.DraftResourceType) error); ok {
		r1 = rf(appId, envId, resourceType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDraftMetadataById provides a mock function with given fields: draftId
func (_m *ConfigDraftRepository) GetDraftMetadataById(draftId int) (*drafts.DraftDto, error) {
	ret := _m.Called(draftId)

	var r0 *drafts.DraftDto
	if rf, ok := ret.Get(0).(func(int) *drafts.DraftDto); ok {
		r0 = rf(draftId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drafts.DraftDto)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(draftId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDraftMetadataForAppAndEnv provides a mock function with given fields: appId, envIds
func (_m *ConfigDraftRepository) GetDraftMetadataForAppAndEnv(appId int, envIds []int) ([]*drafts.DraftDto, error) {
	ret := _m.Called(appId, envIds)

	var r0 []*drafts.DraftDto
	if rf, ok := ret.Get(0).(func(int, []int) []*drafts.DraftDto); ok {
		r0 = rf(appId, envIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*drafts.DraftDto)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, []int) error); ok {
		r1 = rf(appId, envIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDraftVersionById provides a mock function with given fields: draftVersionId
func (_m *ConfigDraftRepository) GetDraftVersionById(draftVersionId int) (*drafts.DraftVersion, error) {
	ret := _m.Called(draftVersionId)

	var r0 *drafts.DraftVersion
	if rf, ok := ret.Get(0).(func(int) *drafts.DraftVersion); ok {
		r0 = rf(draftVersionId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drafts.DraftVersion)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(draftVersionId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDraftVersionComments provides a mock function with given fields: draftId
func (_m *ConfigDraftRepository) GetDraftVersionComments(draftId int) ([]*drafts.DraftVersionComment, error) {
	ret := _m.Called(draftId)

	var r0 []*drafts.DraftVersionComment
	if rf, ok := ret.Get(0).(func(int) []*drafts.DraftVersionComment); ok {
		r0 = rf(draftId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*drafts.DraftVersionComment)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(draftId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDraftVersionCommentsCount provides a mock function with given fields: draftId
func (_m *ConfigDraftRepository) GetDraftVersionCommentsCount(draftId int) (int, error) {
	ret := _m.Called(draftId)

	var r0 int
	if rf, ok := ret.Get(0).(func(int) int); ok {
		r0 = rf(draftId)
	} else {
		r0 = ret.Get(0).(int)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(draftId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDraftVersionsMetadata provides a mock function with given fields: draftId
func (_m *ConfigDraftRepository) GetDraftVersionsMetadata(draftId int) ([]*drafts.DraftVersion, error) {
	ret := _m.Called(draftId)

	var r0 []*drafts.DraftVersion
	if rf, ok := ret.Get(0).(func(int) []*drafts.DraftVersion); ok {
		r0 = rf(draftId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*drafts.DraftVersion)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(draftId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLatestConfigDraft provides a mock function with given fields: draftId
func (_m *ConfigDraftRepository) GetLatestConfigDraft(draftId int) (*drafts.DraftVersion, error) {
	ret := _m.Called(draftId)

	var r0 *drafts.DraftVersion
	if rf, ok := ret.Get(0).(func(int) *drafts.DraftVersion); ok {
		r0 = rf(draftId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drafts.DraftVersion)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(draftId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLatestConfigDraftByName provides a mock function with given fields: appId, envId, resourceName, resourceType
func (_m *ConfigDraftRepository) GetLatestConfigDraftByName(appId int, envId int, resourceName string, resourceType bean.DraftResourceType) (*drafts.DraftVersion, error) {
	ret := _m.Called(appId, envId, resourceName, resourceType)

	var r0 *drafts.DraftVersion
	if rf, ok := ret.Get(0).(func(int, int, string, bean.DraftResourceType) *drafts.DraftVersion); ok {
		r0 = rf(appId, envId, resourceName, resourceType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drafts.DraftVersion)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, int, string, bean.DraftResourceType) error); ok {
		r1 = rf(appId, envId, resourceName, resourceType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLatestDraftVersion provides a mock function with given fields: draftId
func (_m *ConfigDraftRepository) GetLatestDraftVersion(draftId int) (*drafts.DraftVersion, error) {
	ret := _m.Called(draftId)

	var r0 *drafts.DraftVersion
	if rf, ok := ret.Get(0).(func(int) *drafts.DraftVersion); ok {
		r0 = rf(draftId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*drafts.DraftVersion)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(draftId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SaveDraftVersion provides a mock function with given fields: draftVersionDto
func (_m *ConfigDraftRepository) SaveDraftVersion(draftVersionDto *drafts.DraftVersion) (int, error) {
	ret := _m.Called(draftVersionDto)

	var r0 int
	if rf, ok := ret.Get(0).(func(*drafts.DraftVersion) int); ok {
		r0 = rf(draftVersionDto)
	} else {
		r0 = ret.Get(0).(int)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(*drafts.DraftVersion) error); ok {
		r1 = rf(draftVersionDto)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SaveDraftVersionComment provides a mock function with given fields: draftVersionComment
func (_m *ConfigDraftRepository) SaveDraftVersionComment(draftVersionComment *drafts.DraftVersionComment) error {
	ret := _m.Called(draftVersionComment)

	var r0 error
	if rf, ok := ret.Get(0).(func(*drafts.DraftVersionComment) error); ok {
		r0 = rf(draftVersionComment)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateDraftState provides a mock function with given fields: draftId, draftState, userId
func (_m *ConfigDraftRepository) UpdateDraftState(draftId int, draftState bean.DraftState, userId int32) error {
	ret := _m.Called(draftId, draftState, userId)

	var r0 error
	if rf, ok := ret.Get(0).(func(int, bean.DraftState, int32) error); ok {
		r0 = rf(draftId, draftState, userId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewConfigDraftRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewConfigDraftRepository creates a new instance of ConfigDraftRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewConfigDraftRepository(t mockConstructorTestingTNewConfigDraftRepository) *ConfigDraftRepository {
	mock := &ConfigDraftRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
