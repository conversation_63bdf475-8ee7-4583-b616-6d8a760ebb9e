/*
 * Copyright (c) 2024. Devtron Inc.
 */

package repository

import (
	"errors"
	"github.com/devtron-labs/devtron/enterprise/pkg/drafts/adaptors"
	"github.com/devtron-labs/devtron/enterprise/pkg/drafts/bean"
	bean2 "github.com/devtron-labs/devtron/enterprise/pkg/drafts/repository/bean"
	"github.com/go-pg/pg"
	"go.uber.org/zap"
	"time"
)

type ConfigDraftRepository interface {
	CreateConfigDraft(request bean.ConfigDraftRequest) (*bean.ConfigDraftResponse, error)
	GetLatestDraftVersion(draftId int) (*bean2.DraftVersion, error)
	SaveDraftVersionComment(draftVersionComment *bean2.DraftVersionComment) error
	SaveDraftVersion(draftVersionDto *bean2.DraftVersion) (int, error)
	GetDraftMetadataById(draftId int) (*bean2.DraftDto, error)
	UpdateDraftState(draftId int, draftState bean.DraftState, userId int32) error
	GetDraftVersionsMetadata(draftId int) ([]*bean2.DraftVersion, error)
	GetDraftVersionComments(draftId int) ([]*bean2.DraftVersionComment, error)
	GetDraftVersionCommentsCount(draftId int) (int, error)
	GetLatestConfigDraft(draftId int) (*bean2.DraftVersion, error)
	GetLatestConfigDraftByName(appId, envId int, resourceName string, resourceType bean.DraftResourceType) (*bean2.DraftVersion, error)
	GetDraftMetadataForAppAndEnv(appId int, envIds []int, draftResourceTypes ...bean.DraftResourceType) ([]*bean2.DraftDto, error)
	GetDraftMetadata(appId int, envId int, resourceType bean.DraftResourceType) ([]*bean2.DraftDto, error)
	GetDraftVersionById(draftVersionId int) (*bean2.DraftVersion, error)
	DeleteComment(draftId int, draftCommentId int, userId int32) (int, error)
	DiscardDraftsByResourceType(resourceType bean.DraftResourceType, appId int, envId int, userId int32) error
	DiscardDraftsInTx(tx *pg.Tx, resourceType bean.DraftResourceType, appId int, envId int, userId int32) error
	DiscardDraftsByResourceName(resourceType bean.DraftResourceType, appId int, envId int, userId int32, resourceName string) error
	GetDraftComments(draftVersionId int) (*bean2.DraftVersionComment, error)
	GetDraftVersionsByAppAndEnvId(appId, envId int) ([]*bean2.DraftVersion, error)
	GetDraftVersionsByAppEnvIdAndResourceType(appId, envId int, resourceType bean.DraftResourceType) ([]*bean2.DraftVersion, error)
	GetAllEnvLevelDraftsForApp(appId int, resourceName string, resourceType bean.DraftResourceType) ([]*bean2.DraftVersion, error)
}

type ConfigDraftRepositoryImpl struct {
	logger       *zap.SugaredLogger
	dbConnection *pg.DB
}

func NewConfigDraftRepositoryImpl(logger *zap.SugaredLogger, dbConnection *pg.DB) *ConfigDraftRepositoryImpl {
	return &ConfigDraftRepositoryImpl{
		logger:       logger,
		dbConnection: dbConnection,
	}
}

func (repo *ConfigDraftRepositoryImpl) CreateConfigDraft(request bean.ConfigDraftRequest) (*bean.ConfigDraftResponse, error) {
	// check draft already exists for this name
	exists, err := repo.checkDraftAlreadyExists(request)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, errors.New("draft-exists-already")
	}
	draftState := bean.InitDraftState
	metadataDto := adaptors.GetDraftDto(request)
	err = repo.dbConnection.Insert(metadataDto)
	if err != nil {
		repo.logger.Errorw("error occurred while creating config draft", "err", err, "metadataDto", metadataDto)
		return nil, err
	}

	draftMetadataId := metadataDto.Id
	repo.logger.Debugw("going to save draft version now", "draftId", draftMetadataId)

	draftVersionDto := adaptors.GetDraftVersionDto(request, draftMetadataId, metadataDto.CreatedOn)
	draftVersionId, err := repo.SaveDraftVersion(draftVersionDto)
	if err != nil {
		return nil, err
	}

	if len(request.UserComment) > 0 {
		draftVersionCommentDto := adaptors.GetDraftVersionComment(request, draftMetadataId, draftVersionId, metadataDto.CreatedOn)
		err = repo.SaveDraftVersionComment(draftVersionCommentDto)
		if err != nil {
			return nil, err
		}
	}

	return &bean.ConfigDraftResponse{DraftId: draftMetadataId, DraftVersionId: draftVersionId, DraftState: draftState}, nil
}

func (repo *ConfigDraftRepositoryImpl) GetLatestDraftVersion(draftId int) (*bean2.DraftVersion, error) {
	draftVersionDto := &bean2.DraftVersion{}
	err := repo.dbConnection.Model(draftVersionDto).Column("draft_version.*", "Draft").Where("draft_id = ?", draftId).
		Order("id desc").Limit(1).Select()
	if err != nil {
		if err == pg.ErrNoRows {
			repo.logger.Errorw("no draft version found ", "draftId", draftId)
		} else {
			repo.logger.Errorw("error occurred while fetching latest draft version", "draftId", draftId, "err", err)
		}
	}
	return draftVersionDto, err
}

func (repo *ConfigDraftRepositoryImpl) SaveDraftVersionComment(draftVersionComment *bean2.DraftVersionComment) error {
	draftVersionComment.CreatedOn = time.Now()
	err := repo.dbConnection.Insert(draftVersionComment)
	if err != nil {
		repo.logger.Errorw("error occurred while saving draft version comment", "draftVersionId", draftVersionComment.DraftVersionId, "err", err)
	}
	return err
}

func (repo *ConfigDraftRepositoryImpl) SaveDraftVersion(draftVersionDto *bean2.DraftVersion) (int, error) {
	draftVersionDto.CreatedOn = time.Now()
	err := repo.dbConnection.Insert(draftVersionDto)
	if err != nil {
		repo.logger.Errorw("error occurred while saving draft version comment", "draftMetadataId", draftVersionDto.DraftId, "err", err)
	}
	return draftVersionDto.Id, err

}

func (repo *ConfigDraftRepositoryImpl) GetDraftMetadataById(draftId int) (*bean2.DraftDto, error) {
	draftMetadataDto := &bean2.DraftDto{}
	err := repo.dbConnection.Model(draftMetadataDto).Where("id = ?", draftId).Select()
	if err != nil {
		repo.logger.Errorw("error occurred while fetching draft metadata", "draftId", draftId, "err", err)
		return nil, err
	}
	return draftMetadataDto, err
}

func (repo *ConfigDraftRepositoryImpl) UpdateDraftState(draftId int, draftState bean.DraftState, userId int32) error {
	draftMetadataDto := &bean2.DraftDto{}
	result, err := repo.dbConnection.Model(draftMetadataDto).Set("draft_state = ?", draftState).Set("updated_on = ?", time.Now()).
		Set("updated_by = ?", userId).Where("id = ?", draftId).Update()
	if err != nil {
		return err
	}
	if result.RowsAffected() == 0 {
		return errors.New("no-record-found")
	}
	return nil
}

func (repo *ConfigDraftRepositoryImpl) GetDraftVersionsMetadata(draftId int) ([]*bean2.DraftVersion, error) {
	var draftVersions []*bean2.DraftVersion
	err := repo.dbConnection.Model(&draftVersions).Column("id", "user_id", "created_on").Where("draft_id = ?", draftId).
		Order("id desc").Select()
	if err != nil && err != pg.ErrNoRows {
		repo.logger.Errorw("error occurred while fetching draft versions", "draftId", draftId, "err", err)
	} else {
		err = nil //ignoring noRows Error
	}
	return draftVersions, err
}

func (repo *ConfigDraftRepositoryImpl) GetDraftVersionComments(draftId int) ([]*bean2.DraftVersionComment, error) {
	var draftComments []*bean2.DraftVersionComment
	err := repo.dbConnection.Model(&draftComments).
		Where("draft_id = ?", draftId).
		Where("active = ?", true).
		Order("id desc").Select()
	if err != nil && err != pg.ErrNoRows {
		repo.logger.Errorw("error occurred while fetching draft comments", "draftId", draftId, "err", err)
	} else {
		err = nil //ignoring noRows Error
	}
	return draftComments, err
}
func (repo *ConfigDraftRepositoryImpl) GetDraftComments(draftVersionId int) (*bean2.DraftVersionComment, error) {
	draftComment := &bean2.DraftVersionComment{}
	err := repo.dbConnection.Model(draftComment).
		Where("draft_version_id = ?", draftVersionId).
		Where("active = ?", true).Select()
	if err != nil {
		repo.logger.Errorw("error occurred while fetching draft comment", "draftVersionId", draftVersionId, "err", err)
		return draftComment, err
	}
	return draftComment, err
}

func (repo *ConfigDraftRepositoryImpl) GetDraftVersionCommentsCount(draftId int) (int, error) {
	count, err := repo.dbConnection.Model(&bean2.DraftVersionComment{}).
		Where("draft_id = ?", draftId).
		Where("active = ?", true).
		Order("id desc").Count()
	if err != nil && err != pg.ErrNoRows {
		repo.logger.Errorw("error occurred while fetching draft comments", "draftId", draftId, "err", err)
	} else {
		err = nil //ignoring noRows Error
	}
	return count, err
}

func (repo *ConfigDraftRepositoryImpl) GetLatestConfigDraftByName(appId, envId int, resourceName string, resourceType bean.DraftResourceType) (*bean2.DraftVersion, error) {
	draftVersion := &bean2.DraftVersion{}
	err := repo.dbConnection.Model(draftVersion).Column("draft_version.*", "Draft").
		//Join("INNER JOIN draft ON draft.id = draft_version.draft_id").
		Where("draft.app_id = ?", appId).
		Where("draft.env_id = ?", envId).
		Where("draft.resource_name = ?", resourceName).
		Where("draft.resource = ?", resourceType).
		Order("draft_version.id desc").Limit(1).Select()
	if err != nil {
		repo.logger.Errorw("error occurred while fetching latest draft version", "resourceName", resourceName,
			"resourceType", resourceType, "err", err)
		return nil, err
	}
	return draftVersion, nil
}

func (repo *ConfigDraftRepositoryImpl) GetLatestConfigDraft(draftId int) (*bean2.DraftVersion, error) {
	draftVersion := &bean2.DraftVersion{}
	err := repo.dbConnection.Model(draftVersion).Column("draft_version.*", "Draft").Where("draft_id = ?", draftId).
		Order("id desc").Limit(1).Select()
	if err != nil {
		repo.logger.Errorw("error occurred while fetching latest draft version", "draftId", draftId, "err", err)
		return nil, err
	}
	return draftVersion, nil
}

func (repo *ConfigDraftRepositoryImpl) GetDraftVersionsByAppAndEnvId(appId, envId int) ([]*bean2.DraftVersion, error) {
	var draftVersions []*bean2.DraftVersion
	err := repo.dbConnection.Model(&draftVersions).Column("draft_version.*", "Draft").
		Where("draft.app_id = ?", appId).
		Where("draft.env_id = ?", envId).
		Where("draft.draft_state not in (?)", pg.In(bean.GetTerminalDraftStates())).
		Where("draft_version.created_on in (select max(dv.created_on) from draft_version dv inner join draft d on dv.draft_id = d.id group by d.resource, d.resource_name,d.app_id ,d.env_id)").
		Select()
	if err != nil {
		repo.logger.Errorw("error occurred while fetching draft versions by appId and envId", "appId", appId, "envId", envId, "err", err)
		return nil, err
	}
	return draftVersions, nil
}

func (repo *ConfigDraftRepositoryImpl) GetDraftMetadataForAppAndEnv(appId int, envIds []int, resourceTypes ...bean.DraftResourceType) ([]*bean2.DraftDto, error) {
	var draftMetadataDtos []*bean2.DraftDto
	query := repo.dbConnection.Model(&draftMetadataDtos).Where("app_id = ?", appId).Where("env_id in (?)", pg.In(envIds)).
		Where("draft_state in (?)", pg.In(bean.GetNonTerminalDraftStates()))

	if len(resourceTypes) > 0 {
		query.Where("resource IN (?)", pg.In(resourceTypes))
	}

	err := query.Select()
	if err != nil && err != pg.ErrNoRows {
		repo.logger.Errorw("error occurred while fetching draft metadata", "appId", appId, "envIds", envIds, "err", err)
	} else {
		err = nil //ignoring noRows Error
	}
	return draftMetadataDtos, err
}

func (repo *ConfigDraftRepositoryImpl) GetDraftMetadata(appId int, envId int, resourceType bean.DraftResourceType) ([]*bean2.DraftDto, error) {
	var draftMetadataDtos []*bean2.DraftDto
	err := repo.dbConnection.Model(&draftMetadataDtos).Where("app_id = ?", appId).Where("env_id = ?", envId).
		Where("resource = ?", resourceType).Where("draft_state in (?)", pg.In(bean.GetNonTerminalDraftStates())).Select()
	if err != nil && err != pg.ErrNoRows {
		repo.logger.Errorw("error occurred while fetching draft metadata", "appId", appId, "envId", envId, "resourceType", resourceType, "err", err)
	} else {
		err = nil //ignoring noRows Error
	}
	return draftMetadataDtos, err
}

func (repo *ConfigDraftRepositoryImpl) GetDraftVersionById(draftVersionId int) (*bean2.DraftVersion, error) {
	var draftVersion = bean2.DraftVersion{}
	err := repo.dbConnection.Model(&draftVersion).
		Column("draft_version.*", "Draft").
		Where("draft_version.id = ?", draftVersionId).
		Order("draft_version.id desc").Select()

	if err != nil {
		repo.logger.Errorw("error occurred while fetching draft version", "draftVersionId", draftVersionId, "err", err)
		return nil, err
	}

	return &draftVersion, nil
}

func (repo *ConfigDraftRepositoryImpl) DeleteComment(draftId int, draftCommentId int, userId int32) (int, error) {
	draftVersionComment := &bean2.DraftVersionComment{}
	result, err := repo.dbConnection.Model(draftVersionComment).Set("active = ?", false).Set("updated_on = ?", time.Now()).
		Where("id = ?", draftCommentId).
		Where("draft_id = ?", draftId).
		Where("created_by = ?", userId).
		Update()
	if err != nil {
		repo.logger.Errorw("error occurred while deleting draft", "draftId", draftId, "draftCommentId", draftCommentId, "err", err)
		return 0, err
	}
	return result.RowsAffected(), nil
}

func (repo *ConfigDraftRepositoryImpl) DiscardDraftsByResourceType(resourceType bean.DraftResourceType, appId int, envId int, userId int32) error {
	draftsDto := &bean2.DraftDto{}
	_, err := repo.dbConnection.Model(draftsDto).Set("draft_state = ?", bean.DiscardedDraftState).
		Set("updated_on = ?", time.Now()).Set("updated_by = ?", userId).
		Where("app_id = ?", appId).Where("env_id = ?", envId).
		Where("draft_state in (?)", pg.In(bean.GetNonTerminalDraftStates())).
		Where("resource = ?", resourceType).
		Update()
	if err != nil {
		repo.logger.Errorw("error occurred while discarding drafts", "appId", appId, "envId", envId, "err", err)
	}
	return err
}

func (repo *ConfigDraftRepositoryImpl) DiscardDraftsInTx(tx *pg.Tx, resourceType bean.DraftResourceType, appId int, envId int, userId int32) error {
	draftsDto := &bean2.DraftDto{}
	_, err := tx.Model(draftsDto).Set("draft_state = ?", bean.DiscardedDraftState).
		Set("updated_on = ?", time.Now()).Set("updated_by = ?", userId).
		Where("app_id = ?", appId).Where("env_id = ?", envId).
		Where("draft_state in (?)", pg.In(bean.GetNonTerminalDraftStates())).
		Where("resource = ?", resourceType).
		Update()
	if err != nil {
		repo.logger.Errorw("error occurred while discarding drafts", "appId", appId, "envId", envId, "err", err)
	}
	return err
}

func (repo *ConfigDraftRepositoryImpl) DiscardDraftsByResourceName(resourceType bean.DraftResourceType, appId int, envId int, userId int32, resourceName string) error {
	draftsDto := &bean2.DraftDto{}
	_, err := repo.dbConnection.Model(draftsDto).Set("draft_state = ?", bean.DiscardedDraftState).
		Set("updated_on = ?", time.Now()).Set("updated_by = ?", userId).
		Where("app_id = ?", appId).Where("env_id = ?", envId).
		Where("draft_state in (?)", pg.In(bean.GetNonTerminalDraftStates())).
		Where("resource = ?", resourceType).
		Where("resource_name = ?", resourceName).
		Update()
	if err != nil {
		repo.logger.Errorw("error occurred while discarding drafts", "appId", appId, "envId", envId, "err", err)
	}
	return err
}

func (repo *ConfigDraftRepositoryImpl) checkDraftAlreadyExists(request bean.ConfigDraftRequest) (bool, error) {
	resourceName := request.ResourceName
	resourceType := request.Resource
	appId := request.AppId
	envId := request.EnvId
	count, err := repo.dbConnection.Model(&bean2.DraftDto{}).
		Where("resource = ?", resourceType).
		Where("resource_name = ?", resourceName).
		Where("app_id = ?", appId).
		Where("env_id = ?", envId).
		Where("draft_state in (?)", pg.In(bean.GetNonTerminalDraftStates())).
		Count()
	if err != nil && err != pg.ErrNoRows {
		repo.logger.Errorw("error occurred while checking draft already exists", "appId", appId, "envId", envId, "err", err)
	} else {
		err = nil //ignoring noRows Error
	}
	return count > 0, err
}

func (repo *ConfigDraftRepositoryImpl) GetDraftVersionsByAppEnvIdAndResourceType(appId, envId int, resourceType bean.DraftResourceType) ([]*bean2.DraftVersion, error) {
	var draftVersions []*bean2.DraftVersion
	err := repo.dbConnection.Model(&draftVersions).Column("draft_version.*", "Draft").
		Where("draft.app_id = ?", appId).
		Where("draft.env_id = ?", envId).
		Where("draft.resource = ?", resourceType).
		Where("draft.draft_state not in (?)", pg.In(bean.GetTerminalDraftStates())).
		Where("draft_version.created_on in (select max(dv.created_on) from draft_version dv inner join draft d on dv.draft_id = d.id group by d.resource, d.resource_name,d.app_id ,d.env_id)").
		Select()
	if err != nil {
		repo.logger.Errorw("error occurred while fetching draft versions by appId and envId", "appId", appId, "envId", envId, "err", err)
		return nil, err
	}
	return draftVersions, nil
}

func (repo *ConfigDraftRepositoryImpl) GetAllEnvLevelDraftsForApp(appId int, resourceName string, resourceType bean.DraftResourceType) ([]*bean2.DraftVersion, error) {

	var drafts []*bean2.DraftVersion

	// draftIdsQueryCondition query will fetch all the latest draft versions of all the drafts
	// that are in non-terminal state having given resource name and type in an app across all env's(exclude base config)
	draftIdsQueryCondition := `draft_version.id IN (SELECT MAX(dv.id) FROM draft_version dv 
                        INNER JOIN draft d ON dv.draft_id = d.id AND d.draft_state NOT IN (?)
                        WHERE d.app_id = ? AND d.env_id > 0 AND d.resource = ? AND d.resource_name = ?
                        GROUP BY d.id)`
	err := repo.dbConnection.Model(&drafts).
		Column("draft_version.*", "Draft").
		Where(draftIdsQueryCondition, pg.In(bean.GetTerminalDraftStates()), appId, resourceType, resourceName).
		Select()
	if err != nil && err != pg.ErrNoRows {
		repo.logger.Errorw("error in getting drafts by appId", "appId", appId, "err", err)
		return nil, err
	}
	return drafts, nil

}
