/*
 * Copyright (c) 2024. Devtron Inc.
 */

package bean

import (
	"github.com/devtron-labs/devtron/enterprise/pkg/drafts/bean"
	"github.com/devtron-labs/devtron/pkg/sql"
	"time"
)

type DraftDto struct {
	tableName    struct{}               `sql:"draft" pg:",discard_unknown_columns"`
	Id           int                    `sql:"id,pk"`
	AppId        int                    `sql:"app_id,notnull"`
	EnvId        int                    `sql:"env_id,notnull"`
	Resource     bean.DraftResourceType `sql:"resource,notnull"`
	ResourceName string                 `sql:"resource_name,notnull"`
	DraftState   bean.DraftState        `sql:"draft_state"`
	sql.AuditLog
}

type DraftVersion struct {
	tableName struct{}            `sql:"draft_version" pg:",discard_unknown_columns"`
	Id        int                 `sql:"id,pk"`
	DraftId   int                 `sql:"draft_id,notnull"`
	Data      string              `sql:"data,notnull"`
	Action    bean.ResourceAction `sql:"action,notnull"`
	UserId    int32               `sql:"user_id,notnull"`
	CreatedOn time.Time           `sql:"created_on,type:timestamptz"`
	Draft     *DraftDto
}

type DraftVersionComment struct {
	tableName      struct{} `sql:"draft_version_comment" pg:",discard_unknown_columns"`
	Id             int      `sql:"id,pk"`
	DraftId        int      `sql:"draft_id,notnull"`
	DraftVersionId int      `sql:"draft_version_id"`
	Comment        string   `sql:"comment"`
	Active         bool     `sql:"active"`
	sql.AuditLog
}
