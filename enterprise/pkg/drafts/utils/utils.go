package utils

import (
	"encoding/json"
	"github.com/devtron-labs/devtron/pkg/bean/configMapBean"
)

func GetKeyValMapForConfigDataList(configDataList []*configMapBean.ConfigData) (map[string]map[string]string, error) {
	keyValMapForSecretConfig := make(map[string]map[string]string)
	for _, secretConfigData := range configDataList {
		if secretConfigData.IsESOExternalSecretType() || secretConfigData.External {
			continue
		}
		secretRawData := secretConfigData.DefaultData
		if len(secretConfigData.Data) > 0 {
			secretRawData = secretConfigData.Data
		}
		var secretData map[string]string
		if err := json.Unmarshal(secretRawData, &secretData); err != nil {
			return nil, err
		}
		for key, val := range secretData {
			if keyValMapForSecretConfig[secretConfigData.Name] == nil {
				keyValMapForSecretConfig[secretConfigData.Name] = make(map[string]string)
			}
			keyValMapForSecretConfig[secretConfigData.Name][key] = val
		}
	}
	return keyValMapForSecretConfig, nil
}
