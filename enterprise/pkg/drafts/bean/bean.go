/*
 * Copyright (c) 2024. Devtron Inc.
 */

package bean

import (
	"github.com/devtron-labs/devtron/client/events/notificationBean"
	beans "github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/bean"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/lockConfiguration/bean"
	"time"
)

const (
	LastVersionOutdated                  = "last-version-outdated"
	DraftAlreadyInTerminalState          = "already-in-terminal-state"
	ApprovalRequestNotRaised             = "approval-request-not-raised"
	UserContributedToDraft               = "user-committed-to-draft"
	TemplateOutdated                     = "template-outdated"
	FailedToDeleteComment                = "failed to delete comment"
	ConfigProtectionDisabled             = "approval policy of config protection not configured"
	DraftNotAllowedForBaseConfigOfNewApp = "cannot create draft for the base deployment for newly created apps"
)

type DraftResourceType uint8

const (
	CMDraftResource            DraftResourceType = 1
	CSDraftResource            DraftResourceType = 2
	DeploymentTemplateResource DraftResourceType = 3
)

func (draftType DraftResourceType) GetDraftResourceType() ResourceType {
	switch draftType {
	case CMDraftResource:
		return CM
	case CSDraftResource:
		return CS
	case DeploymentTemplateResource:
		return DeploymentTemplate
	}
	return ""
}

type ResourceAction uint8

const (
	AddResourceAction    ResourceAction = 1
	UpdateResourceAction ResourceAction = 2
	DeleteResourceAction ResourceAction = 3
)

type DraftState uint8

// we should introduce new
const (
	InitDraftState DraftState = iota + 1
	DiscardedDraftState
	PublishedDraftState
	AwaitApprovalDraftState
)

func (state DraftState) GetConfigState() ConfigState {
	var configState ConfigState
	switch state {
	case InitDraftState:
		configState = DraftConfigState
	case AwaitApprovalDraftState:
		configState = AwaitApprovalConfigState
	}
	return configState
}

func (state DraftState) IsTerminal() bool {
	return state == DiscardedDraftState || state == PublishedDraftState
}
func GetNonTerminalDraftStates() []int {
	return []int{int(InitDraftState), int(AwaitApprovalDraftState)}
}

func GetTerminalDraftStates() []int {
	return []int{int(DiscardedDraftState), int(PublishedDraftState)}
}

type ConfigDraftRequest struct {
	AppId                     int                       `json:"appId" validate:"number,required"`
	EnvId                     int                       `json:"envId"`
	Resource                  DraftResourceType         `json:"resource"`
	ResourceName              string                    `json:"resourceName"`
	Action                    ResourceAction            `json:"action"`
	Data                      string                    `json:"data" validate:"min=1"`
	UserComment               string                    `json:"userComment"`
	ChangeProposed            bool                      `json:"changeProposed"`
	UserId                    int32                     `json:"-"`
	ProtectNotificationConfig ProtectNotificationConfig `json:"protectNotificationConfig"`
	CreatedOn                 time.Time                 `json:"-"`
}
type ProtectNotificationConfig struct {
	EmailIds []string `json:"emailIds"`
}

func (request ConfigDraftRequest) TransformDraftRequestForNotification() notificationBean.ConfigDataForNotification {
	return notificationBean.ConfigDataForNotification{
		AppId:        request.AppId,
		EnvId:        request.EnvId,
		Resource:     notificationBean.ResourceType(request.Resource.GetDraftResourceType()),
		ResourceName: request.ResourceName,
		UserComment:  request.UserComment,
		UserId:       request.UserId,
		EmailIds:     request.ProtectNotificationConfig.GetEmailIdsForProtectConfig(),
	}
}

func (protectNotificationConfig ProtectNotificationConfig) GetEmailIdsForProtectConfig() []string {
	return protectNotificationConfig.EmailIds
}

type ConfigDraftResponse struct {
	ConfigDraftRequest
	*bean.LockValidateErrorResponse
	DraftId                int                         `json:"draftId"`
	DraftVersionId         int                         `json:"draftVersionId"`
	DraftState             DraftState                  `json:"draftState"`
	Approvers              []string                    `json:"approvers"`
	CanApprove             *bool                       `json:"canApprove,omitempty"`
	CommentsCount          int                         `json:"commentsCount"`
	DataEncrypted          bool                        `json:"dataEncrypted"`
	IsAppAdmin             bool                        `json:"isAppAdmin"`
	DraftResolvedValue     string                      `json:"draftResolvedValue"`
	DraftResolvePatchValue string                      `json:"draftResolvePatchValue"`
	UserApprovalMetadata   *beans.UserApprovalMetadata `json:"userApprovalMetadata"`
	// RequestedUserId is user id of the user who made the API request
	RequestedUserId int32 `json:"requestedUserId"`
}

type DraftCountResponse struct {
	AppId       int `json:"appId"`
	EnvId       int `json:"envId"`
	DraftsCount int `json:"draftsCount"`
}

type ConfigDraftVersionRequest struct {
	DraftId                   int                       `json:"draftId" validate:"number,required"`
	LastDraftVersionId        int                       `json:"lastDraftVersionId" validate:"number,required"`
	Action                    ResourceAction            `json:"action"`
	Data                      string                    `json:"data"`
	UserComment               string                    `json:"userComment"`
	ChangeProposed            bool                      `json:"changeProposed"`
	UserId                    int32                     `json:"-"`
	ProtectNotificationConfig ProtectNotificationConfig `json:"protectNotificationConfig"`
}

type DraftVersionMetadataResponse struct {
	DraftId       int                     `json:"draftId"`
	DraftVersions []*DraftVersionMetadata `json:"versionMetadata"`
}

type DraftVersionMetadata struct {
	DraftVersionId int       `json:"draftVersionId"`
	UserId         int32     `json:"userId"`
	UserEmail      string    `json:"userEmail"`
	ActivityTime   time.Time `json:"activityTime"`
}

type DraftVersionCommentResponse struct {
	DraftId              int                       `json:"draftId"`
	DraftVersionComments []DraftVersionCommentBean `json:"versionComments"`
}

type DraftVersionCommentBean struct {
	DraftVersionId int                   `json:"draftVersionId"`
	UserComments   []UserCommentMetadata `json:"userComments"`
}

type UserCommentMetadata struct {
	CommentId   int       `json:"commentId"`
	UserId      int32     `json:"userId"`
	UserEmail   string    `json:"userEmail"`
	CommentedAt time.Time `json:"commentedAt"`
	Comment     string    `json:"comment"`
}

type AppConfigDraft struct {
	DraftId      int               `json:"draftId"`
	Resource     DraftResourceType `json:"resourceType"`
	ResourceName string            `json:"resourceName"`
	DraftState   DraftState        `json:"draftState"`
}

type DraftVersionResponse struct {
	DraftVersionId                  int `json:"draftVersionId"`
	*bean.LockValidateErrorResponse     // check if got error of lock config
}

type ConfigState string

const (
	DraftConfigState         ConfigState = "Draft"
	AwaitApprovalConfigState ConfigState = "ApprovalPending"
	PublishedConfigState     ConfigState = "Published"
)

type ResourceType string

const (
	CM                 ResourceType = "ConfigMap"
	CS                 ResourceType = "Secret"
	DeploymentTemplate ResourceType = "Deployment Template"
)

func (r ResourceType) ToString() string {
	return string(r)
}

type DraftQueryParams struct {
	AppId        int
	EnvId        int
	ResourceName string
	ResourceType DraftResourceType
	UserId       int32
}
