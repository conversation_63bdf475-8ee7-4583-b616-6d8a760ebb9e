package helpers

import (
	"encoding/json"
	"github.com/devtron-labs/devtron/pkg/bean/configMapBean"
)

func FetchUnmarshalledDraftData(draftCsData string) (*configMapBean.ConfigDataRequest, error) {
	configDataRequest := &configMapBean.ConfigDataRequest{}
	err := json.Unmarshal([]byte(draftCsData), configDataRequest)
	if err != nil {
		return configDataRequest, err
	}
	return configDataRequest, nil
}
