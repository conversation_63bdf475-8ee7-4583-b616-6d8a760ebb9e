package adaptors

import (
	"github.com/devtron-labs/devtron/enterprise/pkg/drafts/bean"
	bean2 "github.com/devtron-labs/devtron/enterprise/pkg/drafts/repository/bean"
	bean3 "github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/bean"
	"time"
)

func GetDraftDto(request bean.ConfigDraftRequest) *bean2.DraftDto {
	draftState := bean.InitDraftState
	if proposed := request.ChangeProposed; proposed {
		draftState = bean.AwaitApprovalDraftState
	}
	metadataDto := &bean2.DraftDto{
		AppId:        request.AppId,
		EnvId:        request.EnvId,
		Resource:     request.Resource,
		ResourceName: request.ResourceName,
		DraftState:   draftState,
	}
	currentTime := time.Now()
	metadataDto.CreatedOn = currentTime
	metadataDto.UpdatedOn = currentTime
	metadataDto.CreatedBy = request.UserId
	metadataDto.UpdatedBy = request.UserId
	return metadataDto
}

func GetDraftVersionDto(request bean.ConfigDraftRequest, draftMetadataId int, timestamp time.Time) *bean2.DraftVersion {
	draftVersionDto := &bean2.DraftVersion{
		DraftId:   draftMetadataId,
		Action:    request.Action,
		Data:      request.Data,
		UserId:    request.UserId,
		CreatedOn: timestamp,
	}
	return draftVersionDto
}

func GetDraftVersionComment(request bean.ConfigDraftRequest, draftMetadataId, draftVersionId int, timestamp time.Time) *bean2.DraftVersionComment {
	draftVersionCommentDto := &bean2.DraftVersionComment{}
	draftVersionCommentDto.DraftId = draftMetadataId
	draftVersionCommentDto.DraftVersionId = draftVersionId
	draftVersionCommentDto.Comment = request.UserComment
	draftVersionCommentDto.Active = true
	draftVersionCommentDto.CreatedBy = request.UserId
	draftVersionCommentDto.UpdatedBy = request.UserId
	draftVersionCommentDto.CreatedOn = timestamp
	draftVersionCommentDto.UpdatedOn = timestamp
	return draftVersionCommentDto
}

func GetDraftVersionDtoFromVersionRequest(request bean.ConfigDraftVersionRequest, currentTime time.Time) *bean2.DraftVersion {
	draftVersionDto := &bean2.DraftVersion{}
	draftVersionDto.DraftId = request.DraftId
	draftVersionDto.Data = request.Data
	draftVersionDto.Action = request.Action
	draftVersionDto.UserId = request.UserId
	draftVersionDto.CreatedOn = currentTime
	return draftVersionDto
}

func GetDraftVersionCommentFromVersionRequest(request bean.ConfigDraftVersionRequest, lastDraftVersionId int, currentTime time.Time) *bean2.DraftVersionComment {
	draftVersionCommentDto := &bean2.DraftVersionComment{}
	draftVersionCommentDto.DraftId = request.DraftId
	draftVersionCommentDto.DraftVersionId = lastDraftVersionId
	draftVersionCommentDto.Comment = request.UserComment
	draftVersionCommentDto.Active = true
	draftVersionCommentDto.CreatedBy = request.UserId
	draftVersionCommentDto.UpdatedBy = request.UserId
	draftVersionCommentDto.CreatedOn = currentTime
	draftVersionCommentDto.UpdatedOn = currentTime
	return draftVersionCommentDto
}

// below contains adaptors to convert db models to data transfer objects

func ConvertToAppConfigDraft(dto *bean2.DraftDto) bean.AppConfigDraft {
	appConfigDraft := bean.AppConfigDraft{
		DraftId:      dto.Id,
		Resource:     dto.Resource,
		ResourceName: dto.ResourceName,
		DraftState:   dto.DraftState,
	}
	return appConfigDraft
}

func ConvertToDraftVersionMetadata(dto *bean2.DraftVersion) *bean.DraftVersionMetadata {
	draftVersionMetadata := &bean.DraftVersionMetadata{
		DraftVersionId: dto.Id,
		UserId:         dto.UserId,
		ActivityTime:   dto.CreatedOn,
	}
	return draftVersionMetadata
}

func ConvertToConfigDraft(dto *bean2.DraftVersion) *bean.ConfigDraftResponse {
	configDraftResponse := &bean.ConfigDraftResponse{
		DraftId:        dto.DraftId,
		DraftVersionId: dto.Id,
	}
	configDraftResponse.Data = dto.Data
	configDraftResponse.Action = dto.Action
	configDraftResponse.UserId = dto.UserId
	configDraftResponse.CreatedOn = dto.CreatedOn
	if draftsDto := dto.Draft; draftsDto != nil {
		configDraftResponse.AppId = draftsDto.AppId
		configDraftResponse.EnvId = draftsDto.EnvId
		configDraftResponse.Resource = draftsDto.Resource
		configDraftResponse.ResourceName = draftsDto.ResourceName
		configDraftResponse.DraftState = draftsDto.DraftState
	}
	return configDraftResponse
}

func ConvertToDraftVersionComment(dto *bean2.DraftVersionComment) bean.UserCommentMetadata {
	userComment := bean.UserCommentMetadata{
		CommentId:   dto.Id,
		UserId:      dto.CreatedBy,
		CommentedAt: dto.CreatedOn,
		Comment:     dto.Comment,
	}
	return userComment
}

func GetApprovalResourceKind(resourceType bean.DraftResourceType) bean3.ApprovalFor {
	switch resourceType {
	case bean.CMDraftResource:
		return bean3.APPROVAL_FOR_CONFIGURATION_CM
	case bean.CSDraftResource:
		return bean3.APPROVAL_FOR_CONFIGURATION_CS
	case bean.DeploymentTemplateResource:
		return bean3.APPROVAL_FOR_CONFIGURATION_DT
	}
	return bean3.APPROVAL_FOR_CONFIGURATION_DT
}

func GetApprovalResourceType(resourceKind bean3.ApprovalFor) bean.DraftResourceType {
	switch resourceKind {
	case bean3.APPROVAL_FOR_CONFIGURATION_CM:
		return bean.CMDraftResource
	case bean3.APPROVAL_FOR_CONFIGURATION_CS:
		return bean.CSDraftResource
	case bean3.APPROVAL_FOR_CONFIGURATION_DT:
		return bean.DeploymentTemplateResource
	}
	return bean.DeploymentTemplateResource
}
