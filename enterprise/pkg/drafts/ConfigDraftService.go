/*
 * Copyright (c) 2024. Devtron Inc.
 */

package drafts

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/devtron-labs/devtron/enterprise/pkg/drafts/adaptors"
	bean5 "github.com/devtron-labs/devtron/enterprise/pkg/drafts/bean"
	"github.com/devtron-labs/devtron/enterprise/pkg/drafts/helpers"
	"github.com/devtron-labs/devtron/enterprise/pkg/drafts/repository"
	bean6 "github.com/devtron-labs/devtron/enterprise/pkg/drafts/repository/bean"
	utils2 "github.com/devtron-labs/devtron/enterprise/pkg/drafts/utils"
	bean10 "github.com/devtron-labs/devtron/enterprise/pkg/protect/bean"
	"github.com/devtron-labs/devtron/internal/constants"
	"github.com/devtron-labs/devtron/internal/sql/models"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/internal/util/configUtil"
	bean4 "github.com/devtron-labs/devtron/pkg/auth/user/bean"
	"github.com/devtron-labs/devtron/pkg/bean/configMapBean"
	bean8 "github.com/devtron-labs/devtron/pkg/chart/bean"
	repository2 "github.com/devtron-labs/devtron/pkg/cluster/environment/repository"
	utils3 "github.com/devtron-labs/devtron/pkg/config/configDiff/utils"
	configRead "github.com/devtron-labs/devtron/pkg/config/read"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/read"
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deploymentTemplate/validator"
	"github.com/devtron-labs/devtron/pkg/generateManifest"
	bean9 "github.com/devtron-labs/devtron/pkg/generateManifest/bean"
	beans2 "github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/bean"
	approvalConfigRead "github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/read"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/utils"
	bean3 "github.com/devtron-labs/devtron/pkg/policyGovernance/lockConfiguration/bean"
	model2 "github.com/devtron-labs/devtron/pkg/policyGovernance/model"
	bean7 "github.com/devtron-labs/devtron/pkg/resourceQualifiers"
	util3 "github.com/devtron-labs/devtron/util"
	"net/http"
	"time"

	client "github.com/devtron-labs/devtron/client/events"
	"github.com/devtron-labs/devtron/enterprise/pkg/lockConfiguration"
	"github.com/devtron-labs/devtron/enterprise/pkg/protect"
	"github.com/devtron-labs/devtron/internal/sql/repository/app"
	"github.com/devtron-labs/devtron/internal/util"

	"github.com/devtron-labs/devtron/pkg/auth/user"
	"github.com/devtron-labs/devtron/pkg/chart"
	chartRepoRepository "github.com/devtron-labs/devtron/pkg/chartRepo/repository"
	"github.com/devtron-labs/devtron/pkg/pipeline"
	"github.com/devtron-labs/devtron/pkg/pipeline/bean"
	globalUtil "github.com/devtron-labs/devtron/util"
	util2 "github.com/devtron-labs/devtron/util/event"
	"github.com/go-pg/pg"
	errors1 "github.com/juju/errors"
	"go.uber.org/zap"
	"golang.org/x/exp/slices"
	"k8s.io/utils/pointer"
)

type ConfigDraftService interface {
	CreateDraft(request bean5.ConfigDraftRequest, token string) (*bean5.ConfigDraftResponse, error)
	AddDraftVersion(request bean5.ConfigDraftVersionRequest, token string) (*bean5.ConfigDraftResponse, error)
	UpdateDraftState(draftId int, draftVersionId int, toUpdateDraftState bean5.DraftState, userId int32) (*bean6.DraftVersion, error)
	GetDraftVersionMetadata(draftId int) (*bean5.DraftVersionMetadataResponse, error) // would return version timestamp and user email id
	GetDraftComments(draftId int) (*bean5.DraftVersionCommentResponse, error)
	GetDrafts(appId int, envId int, resourceType bean5.DraftResourceType, userId int32) ([]bean5.AppConfigDraft, error)
	GetDraftMetadata(appId int, envId int) ([]bean5.AppConfigDraft, error)
	GetDraftById(ctx context.Context, draftId int) (*bean5.ConfigDraftResponse, error) //  need to send ** in case of view only user for Secret data
	GetDraftByName(ctx context.Context, draftQueryParams *bean5.DraftQueryParams, token string, appAdminUser bool) (*bean5.ConfigDraftResponse, error)
	ApproveDraft(ctx context.Context, draftId int, draftVersionId int, userId int32) (*bean5.DraftVersionResponse, error)
	DeleteComment(draftId int, draftCommentId int, userId int32) error
	GetDraftsCount(appId int, envIds []int) ([]*bean5.DraftCountResponse, error)
	ValidateLockDraft(request bean5.ConfigDraftRequest, token string) (*bean3.LockValidateErrorResponse, error)
	CheckIfUserHasApprovalAccess(appId int, envId int, token string) bool
	EncryptCSData(draftCsData string) string
	GetDraftsByAppAndEnvId(reqCtx context.Context, appId int, envId int, userId int32) ([]*bean5.ConfigDraftResponse, error)
	GetDraftByAppEnvIdAndResourceType(ctx context.Context, appId int, envId int, userId int32, resourceType bean5.DraftResourceType) ([]*bean5.ConfigDraftResponse, error)
	CompareDraftWithLastSavedAndMaskCSData(draftResourceName, draftCsData string, envId, appId int) string
	DiscardUnPublishedDraftsByResourceTypeAndName(resourceKind beans2.ApprovalFor, appId, envId int, resourceName string) error
}

type ConfigDraftServiceImpl struct {
	logger                                  *zap.SugaredLogger
	configDraftRepository                   repository.ConfigDraftRepository
	configMapService                        pipeline.ConfigMapService
	chartService                            chart.ChartService
	propertiesConfigService                 pipeline.PropertiesConfigService
	resourceProtectionService               protect.ResourceProtectionService
	userService                             user.UserService
	appRepo                                 app.AppRepository
	envRepository                           repository2.EnvironmentRepository
	chartRepository                         chartRepoRepository.ChartRepository
	lockedConfigService                     lockConfiguration.LockConfigurationService
	mergeUtil                               configUtil.MergeUtil
	eventFactory                            client.EventFactory
	eventClient                             client.EventClient
	deploymentTemplateValidationService     validator.DeploymentTemplateValidationService
	deploymentTemplateService               generateManifest.DeploymentTemplateService
	envConfigOverrideReadService            read.EnvConfigOverrideService
	requestApprovalUserDataRepo             pipelineConfig.RequestApprovalUserdataRepository
	approvalConfigurationEnforcementService approvalConfigRead.ApprovalPolicyReadService
	userGroupService                        user.UserGroupService
	configReadService                       configRead.ConfigReadService
}

func NewConfigDraftServiceImpl(logger *zap.SugaredLogger, configDraftRepository repository.ConfigDraftRepository, configMapService pipeline.ConfigMapService, chartService chart.ChartService,
	propertiesConfigService pipeline.PropertiesConfigService, resourceProtectionService protect.ResourceProtectionService,
	userService user.UserService, appRepo app.AppRepository, envRepository repository2.EnvironmentRepository,
	chartRepository chartRepoRepository.ChartRepository,
	lockedConfigService lockConfiguration.LockConfigurationService,
	mergeUtil configUtil.MergeUtil, eventFactory client.EventFactory, eventClient client.EventClient,
	deploymentTemplateValidationService validator.DeploymentTemplateValidationService,
	deploymentTemplateService generateManifest.DeploymentTemplateService,
	envConfigOverrideReadService read.EnvConfigOverrideService,
	requestApprovalUserDataRepo pipelineConfig.RequestApprovalUserdataRepository,
	approvalConfigurationEnforcementService approvalConfigRead.ApprovalPolicyReadService,
	userGroupService user.UserGroupService,
	configReadService configRead.ConfigReadService,
) *ConfigDraftServiceImpl {
	draftServiceImpl := &ConfigDraftServiceImpl{
		logger:                                  logger,
		configDraftRepository:                   configDraftRepository,
		configMapService:                        configMapService,
		chartService:                            chartService,
		propertiesConfigService:                 propertiesConfigService,
		resourceProtectionService:               resourceProtectionService,
		userService:                             userService,
		appRepo:                                 appRepo,
		envRepository:                           envRepository,
		eventFactory:                            eventFactory,
		eventClient:                             eventClient,
		chartRepository:                         chartRepository,
		lockedConfigService:                     lockedConfigService,
		mergeUtil:                               mergeUtil,
		deploymentTemplateValidationService:     deploymentTemplateValidationService,
		deploymentTemplateService:               deploymentTemplateService,
		envConfigOverrideReadService:            envConfigOverrideReadService,
		requestApprovalUserDataRepo:             requestApprovalUserDataRepo,
		approvalConfigurationEnforcementService: approvalConfigurationEnforcementService,
		userGroupService:                        userGroupService,
		configReadService:                       configReadService,
	}
	configMapService.RegisterListener(draftServiceImpl)
	return draftServiceImpl
}

func (impl *ConfigDraftServiceImpl) OnStateChange(appId int, envId int, state bean10.ProtectionState, userId int32) {
	//impl.logger.Debugw("resource protection state change event received", "appId", appId, "envId", envId, "state", state)
	//if state == bean10.DisabledProtectionState {
	//	_ = impl.configDraftRepository.DiscardDrafts(appId, envId, userId)
	//}
}

func (impl *ConfigDraftServiceImpl) CreateDraft(request bean5.ConfigDraftRequest, token string) (*bean5.ConfigDraftResponse, error) {
	resourceType := request.Resource
	resourceAction := request.Action
	envId := request.EnvId
	appId := request.AppId
	protectionEnabled := impl.resourceProtectionService.ResourceProtectionEnabled(adaptors.GetApprovalResourceKind(resourceType), appId, envId, true, false)
	if !protectionEnabled {
		return nil, util.DefaultApiError().WithUserMessage(bean5.ConfigProtectionDisabled).WithHttpStatusCode(http.StatusConflict).WithCode(constants.ApprovalConfigDependentActionFailure)
	}
	validateResp, draftData, err := impl.validateDraftData(request.AppId, envId, resourceType, resourceAction, request.Data, token, request.UserId)
	if err != nil {
		return nil, err
	}
	if validateResp != nil {
		return &bean5.ConfigDraftResponse{LockValidateErrorResponse: validateResp}, nil
	}
	//assign latest data
	request.Data = draftData
	draft, err := impl.configDraftRepository.CreateConfigDraft(request)
	if err != nil {
		return nil, err
	}

	go impl.performNotificationConfigAction(request, draft.DraftId, draft.DraftVersionId)
	return draft, err

}

func (impl *ConfigDraftServiceImpl) performNotificationConfigAction(request bean5.ConfigDraftRequest, draftId int, DraftVersionId int) {

	if len(request.ProtectNotificationConfig.EmailIds) == 0 {
		return
	}
	eventType := util2.ConfigApproval
	event, _ := impl.eventFactory.Build(eventType, nil, request.AppId, &request.EnvId, "")
	draftRequest := request.TransformDraftRequestForNotification()
	events := impl.eventFactory.BuildExtraProtectConfigData(event, draftRequest, draftId, DraftVersionId)
	for _, evnt := range events {
		_, evtErr := impl.eventClient.WriteNotificationEvent(evnt)
		if evtErr != nil {
			impl.logger.Errorw("unable to send notification for protect config approval", "error", evtErr)
		}
	}
}
func (impl *ConfigDraftServiceImpl) AddDraftVersion(request bean5.ConfigDraftVersionRequest, token string) (*bean5.ConfigDraftResponse, error) {
	draftId := request.DraftId
	latestDraftVersion, err := impl.configDraftRepository.GetLatestDraftVersion(draftId)
	if err != nil {
		return nil, err
	}
	draftDto := latestDraftVersion.Draft
	protectionEnabled := impl.resourceProtectionService.ResourceProtectionEnabled(adaptors.GetApprovalResourceKind(draftDto.Resource), draftDto.AppId, draftDto.EnvId, true, false)
	if !protectionEnabled {
		return nil, util.DefaultApiError().WithUserMessage(bean5.ConfigProtectionDisabled).WithHttpStatusCode(http.StatusConflict).WithCode(constants.ApprovalConfigDependentActionFailure)
	}
	lastDraftVersionId := request.LastDraftVersionId
	if latestDraftVersion.Id > lastDraftVersionId {
		return nil, errors.New(bean5.LastVersionOutdated)
	}

	currentTime := time.Now()
	if len(request.Data) > 0 {

		lockConfig, draftData, err := impl.validateDraftData(draftDto.AppId, draftDto.EnvId, draftDto.Resource, request.Action, request.Data, token, request.UserId)
		if err != nil {
			return nil, err
		}
		if lockConfig != nil {
			return &bean5.ConfigDraftResponse{DraftVersionId: lastDraftVersionId, LockValidateErrorResponse: lockConfig}, nil
		}
		//assign latest draftData
		request.Data = draftData
		draftVersionDto := adaptors.GetDraftVersionDtoFromVersionRequest(request, currentTime)
		draftVersionId, err := impl.configDraftRepository.SaveDraftVersion(draftVersionDto)
		if err != nil {
			return nil, err
		}
		lastDraftVersionId = draftVersionId
	}

	if len(request.UserComment) > 0 {
		draftVersionCommentDto := adaptors.GetDraftVersionCommentFromVersionRequest(request, lastDraftVersionId, currentTime)
		err = impl.configDraftRepository.SaveDraftVersionComment(draftVersionCommentDto)
		if err != nil {
			return nil, err
		}
	}
	if proposed := request.ChangeProposed; proposed {
		err = impl.configDraftRepository.UpdateDraftState(draftId, bean5.AwaitApprovalDraftState, request.UserId)
		if err != nil {
			return nil, err
		}
	}
	impl.performNotificationConfigActionForVersion(request, draftId, lastDraftVersionId)
	return &bean5.ConfigDraftResponse{DraftVersionId: lastDraftVersionId}, nil
}

func (impl *ConfigDraftServiceImpl) performNotificationConfigActionForVersion(request bean5.ConfigDraftVersionRequest, draftId int, draftVersionId int) {
	draftData, err := impl.configDraftRepository.GetDraftMetadataById(draftId)
	if err != nil {
		impl.logger.Errorw("error in performing notification event for config draft version ", "err", err, "draftData", draftData)
		return
	}

	config := bean5.ConfigDraftRequest{
		AppId:                     draftData.AppId,
		EnvId:                     draftData.EnvId,
		Resource:                  draftData.Resource,
		ResourceName:              draftData.ResourceName,
		UserComment:               request.UserComment,
		UserId:                    request.UserId,
		ProtectNotificationConfig: request.ProtectNotificationConfig,
	}
	go impl.performNotificationConfigAction(config, draftId, draftVersionId)

}

func (impl *ConfigDraftServiceImpl) UpdateDraftState(draftId int, draftVersionId int, toUpdateDraftState bean5.DraftState, userId int32) (*bean6.DraftVersion, error) {
	impl.logger.Infow("updating draft state", "draftId", draftId, "toUpdateDraftState", toUpdateDraftState, "userId", userId)
	// check app config draft is enabled or not ??
	latestDraftVersion, err := impl.validateDraftAction(draftId, draftVersionId, toUpdateDraftState, userId)
	if err != nil {
		return nil, err
	}
	err = impl.configDraftRepository.UpdateDraftState(draftId, toUpdateDraftState, userId)
	return latestDraftVersion, err
}

func (impl *ConfigDraftServiceImpl) validateDraftAction(draftId int, draftVersionId int, toUpdateDraftState bean5.DraftState, userId int32) (*bean6.DraftVersion, error) {
	latestDraftVersion, err := impl.configDraftRepository.GetLatestConfigDraft(draftId)
	if err != nil {
		return nil, err
	}
	if latestDraftVersion.Id != draftVersionId { // needed for current scope
		return nil, errors.New(bean5.LastVersionOutdated)
	}
	draftMetadataDto, err := impl.configDraftRepository.GetDraftMetadataById(draftId)
	if err != nil {
		return nil, err
	}
	draftCurrentState := draftMetadataDto.DraftState
	if draftCurrentState.IsTerminal() {
		impl.logger.Errorw("draft is already in terminal state", "draftId", draftId, "draftCurrentState", draftCurrentState)
		return nil, &DraftApprovalValidationError{
			Err:        errors.New(bean5.DraftAlreadyInTerminalState),
			DraftState: draftCurrentState,
		}
	}
	if toUpdateDraftState == bean5.PublishedDraftState {
		if draftCurrentState != bean5.AwaitApprovalDraftState {
			impl.logger.Errorw("draft is not in await Approval state", "draftId", draftId, "draftCurrentState", draftCurrentState)
			return nil, errors.New(bean5.ApprovalRequestNotRaised)
		} else {
			contributedToDraft, err := impl.checkUserContributedToDraft(draftId, userId)
			if err != nil {
				return nil, err
			}
			if contributedToDraft {
				impl.logger.Errorw("user contributed to this draft", "draftId", draftId, "userId", userId)
				return nil, &DraftApprovalValidationError{
					Err:        errors.New(bean5.UserContributedToDraft),
					DraftState: draftCurrentState,
				}
			}
		}
	}
	return latestDraftVersion, nil
}

func (impl *ConfigDraftServiceImpl) GetDraftVersionMetadata(draftId int) (*bean5.DraftVersionMetadataResponse, error) {
	draftVersionDtos, err := impl.configDraftRepository.GetDraftVersionsMetadata(draftId)
	if err != nil {
		return nil, err
	}
	var draftVersions []*bean5.DraftVersionMetadata
	for _, draftVersionDto := range draftVersionDtos {
		versionMetadata := adaptors.ConvertToDraftVersionMetadata(draftVersionDto)
		draftVersions = append(draftVersions, versionMetadata)
	}
	err = impl.updateWithUserMetadata(draftVersions)
	if err != nil {
		return nil, errors.New("failed to fetch")
	}
	response := &bean5.DraftVersionMetadataResponse{}
	response.DraftId = draftId
	response.DraftVersions = draftVersions
	return response, nil
}

func (impl *ConfigDraftServiceImpl) GetDraftComments(draftId int) (*bean5.DraftVersionCommentResponse, error) {
	draftComments, err := impl.configDraftRepository.GetDraftVersionComments(draftId)
	if err != nil {
		return nil, err
	}
	var userIds []int32
	for _, draftComment := range draftComments {
		userIds = append(userIds, draftComment.CreatedBy)
	}
	userMetadataMap, err := impl.getUserMetadata(userIds)
	if err != nil {
		return nil, err
	}
	draftVersionVsComments := make(map[int][]bean5.UserCommentMetadata)
	for _, draftComment := range draftComments {
		draftVersionId := draftComment.DraftVersionId
		userComment := adaptors.ConvertToDraftVersionComment(draftComment)
		if userInfo, found := userMetadataMap[userComment.UserId]; found {
			userComment.UserEmail = userInfo.EmailId
		}
		commentMetadataArray := draftVersionVsComments[draftVersionId]
		commentMetadataArray = append(commentMetadataArray, userComment)
		draftVersionVsComments[draftVersionId] = commentMetadataArray
	}
	var draftVersionComments []bean5.DraftVersionCommentBean
	for draftVersionId, userComments := range draftVersionVsComments {
		versionComment := bean5.DraftVersionCommentBean{
			DraftVersionId: draftVersionId,
			UserComments:   userComments,
		}
		draftVersionComments = append(draftVersionComments, versionComment)
	}
	response := &bean5.DraftVersionCommentResponse{
		DraftId:              draftId,
		DraftVersionComments: draftVersionComments,
	}
	return response, nil
}

func (impl *ConfigDraftServiceImpl) GetDrafts(appId int, envId int, resourceType bean5.DraftResourceType, userId int32) ([]bean5.AppConfigDraft, error) {
	draftMetadataDtos, err := impl.configDraftRepository.GetDraftMetadata(appId, envId, resourceType)
	if err != nil {
		return nil, err
	}
	appConfigDrafts := getAppConfigDrafts(draftMetadataDtos)
	return appConfigDrafts, nil
}

func getAppConfigDrafts(draftMetadataDtos []*bean6.DraftDto) []bean5.AppConfigDraft {
	var appConfigDrafts []bean5.AppConfigDraft
	for _, draftMetadataDto := range draftMetadataDtos {
		appConfigDraft := adaptors.ConvertToAppConfigDraft(draftMetadataDto)
		appConfigDrafts = append(appConfigDrafts, appConfigDraft)
	}
	return appConfigDrafts
}
func (impl *ConfigDraftServiceImpl) GetDraftMetadata(appId int, envId int) ([]bean5.AppConfigDraft, error) {
	resp, err := impl.resourceProtectionService.ResourceProtectionEnabledMap([]beans2.ApprovalFor{beans2.APPROVAL_FOR_CONFIGURATION_DT, beans2.APPROVAL_FOR_CONFIGURATION_CM, beans2.APPROVAL_FOR_CONFIGURATION_CS}, appId, envId)
	if err != nil {
		impl.logger.Errorw("error in fetching ResourceProtectionEnabledMap", "appId", appId, "envId", envId)
		return nil, err
	}
	approvalCOnfiguredResources := make([]bean5.DraftResourceType, 0)
	for resourceKind, approvalConfigured := range resp {
		if approvalConfigured {
			approvalCOnfiguredResources = append(approvalCOnfiguredResources, adaptors.GetApprovalResourceType(resourceKind))
		}
	}
	// if no resource have approval config , no need to get the drafts as the drafts are no longer relevant
	if len(approvalCOnfiguredResources) == 0 {
		return []bean5.AppConfigDraft{}, nil
	}

	draftMetadataDtos, err := impl.configDraftRepository.GetDraftMetadataForAppAndEnv(appId, []int{envId}, approvalCOnfiguredResources...)
	if err != nil {
		impl.logger.Errorw("error in fetching draftMetadataDtos", "appId", appId, "envId", envId)
		return nil, err
	}
	appConfigDrafts := getAppConfigDrafts(draftMetadataDtos)
	return appConfigDrafts, nil
}

func (impl *ConfigDraftServiceImpl) GetDraftByName(ctx context.Context, draftQueryParams *bean5.DraftQueryParams, token string, appAdminUser bool) (*bean5.ConfigDraftResponse, error) {
	draftVersion, err := impl.configDraftRepository.GetLatestConfigDraftByName(draftQueryParams.AppId, draftQueryParams.EnvId, draftQueryParams.ResourceName, draftQueryParams.ResourceType)
	if err != nil && err != pg.ErrNoRows {
		return nil, err
	}
	draftResponse := &bean5.ConfigDraftResponse{}
	if draftVersion == nil {
		draftResponse.Approvers = impl.getApproversData(draftQueryParams.AppId, draftQueryParams.EnvId, token)
		userGroupIdentifierNameMappings, err := impl.userGroupService.ListIdentifierAndName()
		if err != nil {
			impl.logger.Errorw("error occurred while fetching userGroups metadata", "err", err)
			return nil, err
		}

		userApprovalMetadataReq := beans2.NewApprovalUsersAndUserGroupsMetadataRequest(userGroupIdentifierNameMappings)
		userApprovalMetadataReq, err = impl.setApprovalAccessUsersData(userApprovalMetadataReq, draftResponse.Approvers)
		if err != nil {
			impl.logger.Errorw("error in setting approval access users info in approval metadata request", "err", err)
			return nil, err
		}
		configDataWithApprovalUsers, err := impl.getUserApprovalConfigDtoWithApprovedUsers(userApprovalMetadataReq, draftQueryParams.ResourceType, draftQueryParams.AppId, draftQueryParams.EnvId)
		if err != nil {
			impl.logger.Errorw("error in setting approval access users info in approval metadata request", "err", err)
			return nil, err
		}

		draftResponse.UserApprovalMetadata = &beans2.UserApprovalMetadata{
			ApprovalConfigV2: configDataWithApprovalUsers,
		}
		return draftResponse, nil
	}

	reqCtx := util3.NewRequestCtx(ctx)
	draftResponse = adaptors.ConvertToConfigDraft(draftVersion)
	err = impl.updateDraftResponse(reqCtx, draftResponse.DraftId, draftResponse, token)
	if err != nil {
		return nil, err
	}
	if draftResponse.EnvId > 0 {
		var processedData string
		processedData, err := impl.processDraftDataByMergeStrategy(draftResponse)
		if err != nil {
			impl.logger.Errorw("error in getting processed data by merge strategy")
			return nil, err
		}
		draftResponse.Data = processedData
	}
	if draftResponse.Resource != bean5.DeploymentTemplateResource {
		envId := 0
		if draftQueryParams.EnvId > 0 {
			envId = draftQueryParams.EnvId
		}
		deploymentTemplateRequest := bean9.DeploymentTemplateRequest{
			AppId:           draftQueryParams.AppId,
			EnvId:           envId,
			RequestDataMode: bean9.Values,
		}
		resolvedTemplate, _, err := impl.deploymentTemplateService.ResolveTemplateVariables(ctx, draftResponse.Data, deploymentTemplateRequest)
		if err != nil {
			impl.logger.Errorw("error in getting resolved data for cm draft data ", "appid", draftQueryParams.AppId, "err", err)
			return nil, err
		}
		draftResponse.DraftResolvedValue = resolvedTemplate
	}
	if draftResponse.Resource == bean5.CSDraftResource && !appAdminUser {
		// not an admin and config approver, protecting secret data
		draftResponse.Data = impl.CompareDraftWithLastSavedAndMaskCSData(draftResponse.ResourceName, draftResponse.Data, draftResponse.EnvId, draftResponse.AppId)
		draftResponse.DraftResolvedValue = impl.EncryptCSData(draftResponse.DraftResolvedValue)
		draftResponse.DataEncrypted = true
	}

	return draftResponse, nil
}

func (impl *ConfigDraftServiceImpl) EncryptCSData(draftCsData string) string {
	draftSecretConfig, err := helpers.FetchUnmarshalledDraftData(draftCsData)
	if err != nil {
		impl.logger.Errorw("error occurred while FetchUnmarshalledDraftData", "err", err)
		return draftCsData
	}
	configData := draftSecretConfig.ConfigData
	var configDataResponse []*configMapBean.ConfigData
	for _, data := range configData {
		_ = impl.configMapService.EncryptCSData(data)
		configDataResponse = append(configDataResponse, data)
	}
	draftSecretConfig.ConfigData = configDataResponse
	encryptedCSData, err := json.Marshal(draftSecretConfig)
	if err != nil {
		impl.logger.Errorw("error occurred while marshalling config data request, so returning original data", "err", err)
		return draftCsData
	}
	return string(encryptedCSData)
}

func (impl *ConfigDraftServiceImpl) processDraftDataByMergeStrategy(draftResponse *bean5.ConfigDraftResponse) (string, error) {
	var (
		processedData string
		err           error
	)

	if draftResponse.EnvId == beans2.BASE_CONFIG_ENV_ID {
		return draftResponse.Data, nil
	}

	if draftResponse.Resource == bean5.DeploymentTemplateResource {
		processedData, err = impl.ProcessDraftDataForDTByMergeStrategy(draftResponse.AppId, draftResponse.Data)
		if err != nil {
			impl.logger.Errorw("error in processing data for DT by merge strategy", "draftId", draftResponse.DraftId, "appId", draftResponse.AppId, "err", err)
			return "", err
		}
	} else {
		processedData, err = impl.processDraftDataForCMCSByMergeStrategy(draftResponse)
		if err != nil {
			impl.logger.Errorw("error in processing data for CM/CS by merge strategy", "draftId", draftResponse.DraftId, "appId", draftResponse.AppId, "err", err)
			return "", err
		}
	}
	return processedData, nil
}

func (impl *ConfigDraftServiceImpl) processDraftDataForCMCSByMergeStrategy(draftResponse *bean5.ConfigDraftResponse) (string, error) {
	var resourceType configMapBean.ResourceType
	if draftResponse.Resource == bean5.CMDraftResource {
		resourceType = configMapBean.CM
	} else if draftResponse.Resource == bean5.CSDraftResource {
		resourceType = configMapBean.CS
	}
	configDataReq := &configMapBean.ConfigDataRequest{}
	err := json.Unmarshal([]byte(draftResponse.ConfigDraftRequest.Data), configDataReq)
	if err != nil {
		impl.logger.Errorw("error occurred while unmarshalling draftData of env deployment template", "appId", draftResponse.AppId, "err", err)
		return "", err
	}
	for i, configData := range configDataReq.ConfigData {
		if configData.MergeStrategy == models.MERGE_STRATEGY_PATCH {
			patchData := configData.Data
			mergedData, err := impl.configMapService.GetMergedValuesForCMCS(draftResponse.AppId, configData.Name, resourceType, patchData)
			if err != nil {
				impl.logger.Errorw("error in getting merged values for CM CS", "appId", draftResponse.AppId, "err", err)
				return "", err
			}
			configData.PatchData = patchData
			configData.Data = mergedData
		}
		configDataReq.ConfigData[i] = configData
	}
	processedData, err := json.Marshal(configDataReq)
	if err != nil {
		impl.logger.Errorw("error in marshaling configMap data", "appId", draftResponse.AppId, "envId", draftResponse.EnvId, "err", err)
		return "", err
	}
	return string(processedData), nil
}

func (impl *ConfigDraftServiceImpl) ProcessDraftDataForDTByMergeStrategy(appId int, draftData string) (string, error) {
	envConfigProperties := &bean.EnvironmentProperties{}
	err := json.Unmarshal([]byte(draftData), envConfigProperties)
	if err != nil {
		impl.logger.Errorw("error occurred while unmarshalling draftData of env deployment template", "appId", appId, "err", err)
		return "", err
	}
	envConfigProperties.SanitizeEnvOverrideValues()
	overrideValues, patchValues, err := impl.envConfigOverrideReadService.GetRuntimeValueForEnvOverrideByAppIdUnResolved(envConfigProperties.MergeStrategy, appId, string(envConfigProperties.EnvOverrideValues))
	if err != nil {
		impl.logger.Errorw("error in getting merged override value")
	}
	envConfigProperties.EnvOverrideValues = json.RawMessage(overrideValues)
	envConfigProperties.EnvOverridePatchValues = json.RawMessage(patchValues)

	processedData, err := json.Marshal(envConfigProperties)
	if err != nil {
		impl.logger.Errorw("error in marshaling envConfigProperties", "appId", appId, "envId", envConfigProperties.EnvironmentId, "err", err)
		return "", err
	}
	return string(processedData), nil
}

func (impl *ConfigDraftServiceImpl) GetDraftById(ctx context.Context, draftId int) (*bean5.ConfigDraftResponse, error) {
	configDraft, err := impl.configDraftRepository.GetLatestConfigDraft(draftId)
	if err != nil {
		return nil, err
	}
	draftResponse := adaptors.ConvertToConfigDraft(configDraft)
	mergedData, err := impl.processDraftDataByMergeStrategy(draftResponse)
	if err != nil {
		impl.logger.Errorw("error in processing draft data by merge strategy", "draftId", draftId, "err", err)
		return nil, err
	}
	draftResponse.Data = mergedData
	reqCtx := util3.NewRequestCtx(ctx)
	err = impl.updateDraftResponse(reqCtx, draftId, draftResponse, reqCtx.GetToken())
	if err != nil {
		return nil, err
	}
	return draftResponse, nil
}

func (impl *ConfigDraftServiceImpl) GetDraftsByAppAndEnvId(ctx context.Context, appId int, envId int, userId int32) ([]*bean5.ConfigDraftResponse, error) {
	draftVersions, err := impl.configDraftRepository.GetDraftVersionsByAppAndEnvId(appId, envId)
	if err != nil {
		impl.logger.Errorw("error in getting draft versions by app and env id", "appId", appId, "envId", envId, "err", err)
		return nil, err
	}

	reqCtx := util3.NewRequestCtx(ctx)
	configDraftResp := make([]*bean5.ConfigDraftResponse, 0, len(draftVersions))
	for _, draftVersion := range draftVersions {
		draftResponse := adaptors.ConvertToConfigDraft(draftVersion)
		err = impl.updateDraftResponse(reqCtx, draftVersion.DraftId, draftResponse, "")
		if err != nil {
			impl.logger.Errorw("error in getting draft response from draftId", "appId", appId, "envId", envId, "err", err)
			return nil, err
		}
		mergedData, err := impl.processDraftDataByMergeStrategy(draftResponse)
		if err != nil {
			impl.logger.Errorw("error in processing draft data by merge strategy", "appId", appId, "err", err)
			return nil, err
		}
		draftResponse.Data = mergedData
		configDraftResp = append(configDraftResp, draftResponse)
	}
	return configDraftResp, nil
}

func (impl *ConfigDraftServiceImpl) GetDraftByAppEnvIdAndResourceType(ctx context.Context, appId int, envId int, userId int32, resourceType bean5.DraftResourceType) ([]*bean5.ConfigDraftResponse, error) {
	draftVersions, err := impl.configDraftRepository.GetDraftVersionsByAppEnvIdAndResourceType(appId, envId, resourceType)
	if err != nil {
		impl.logger.Errorw("error in getting draft versions by app and env id", "appId", appId, "envId", envId, "err", err)
		return nil, err
	}
	reqCtx := util3.NewRequestCtx(ctx)
	draftResp := make([]*bean5.ConfigDraftResponse, 0, len(draftVersions))
	for _, draftVersion := range draftVersions {
		draftResponse := adaptors.ConvertToConfigDraft(draftVersion)

		err = impl.updateDraftResponse(reqCtx, draftVersion.DraftId, draftResponse, "")
		if err != nil {
			impl.logger.Errorw("error in getting draft response from draftId", "appId", appId, "envId", envId, "err", err)
			return nil, err
		}
		mergedData, err := impl.processDraftDataByMergeStrategy(draftResponse)
		if err != nil {
			impl.logger.Errorw("error in processing draft data by merge strategy", "appId", appId, "err", err)
			return nil, err
		}
		draftResponse.Data = mergedData
		draftResp = append(draftResp, draftResponse)
	}
	return draftResp, nil
}

func (impl *ConfigDraftServiceImpl) updateDraftResponse(ctx *util3.RequestCtx, draftId int, draftResponse *bean5.ConfigDraftResponse, token string) error {
	draftResponse.Approvers = impl.getApproversData(draftResponse.AppId, draftResponse.EnvId, token)
	userContributedToDraft, err := impl.checkUserContributedToDraft(draftId, ctx.GetUserId())
	if err != nil {
		return err
	}
	draftResponse.CanApprove = pointer.Bool(!userContributedToDraft)
	commentsCount, err := impl.configDraftRepository.GetDraftVersionCommentsCount(draftId)
	if err != nil {
		return err
	}
	draftResponse.CommentsCount = commentsCount
	requestedUserEmail, err := impl.userService.GetEmailById(draftResponse.UserId)
	if err != nil {
		impl.logger.Errorw("error in getting user email using user id", "userId", draftResponse.UserId, "err", err)
		return err
	}
	// for now we are not setting the userEmail as this is not required for now.
	userApprovalMetadata, err := impl.getUserApprovalMetadata(ctx.GetUserEmailId(), draftResponse.DraftVersionId, draftResponse.AppId, draftResponse.EnvId, draftResponse.Resource, draftResponse.UserId, requestedUserEmail, draftResponse.Approvers)
	if err != nil {
		impl.logger.Errorw("error int fetching user approval metadata for the draft", "draftVersionId", draftResponse.DraftVersionId, "err", err)
		return err
	}
	userApprovalMetadata.RequestedUserData.UserActionTime = draftResponse.CreatedOn
	draftResponse.RequestedUserId = ctx.GetUserId()
	draftResponse.UserApprovalMetadata = userApprovalMetadata
	return nil
}

func (impl *ConfigDraftServiceImpl) DeleteComment(draftId int, draftCommentId int, userId int32) error {
	deletedCount, err := impl.configDraftRepository.DeleteComment(draftId, draftCommentId, userId)
	if err != nil {
		return err
	}
	if deletedCount == 0 {
		return errors.New(bean5.FailedToDeleteComment)
	}
	return nil
}

func (impl *ConfigDraftServiceImpl) registerUserApproval(userId int32, draftVersionId int) error {
	draftApprovalData := &pipelineConfig.RequestApprovalUserData{
		RequestType:       models.CONFIG_APPROVAL,
		ApprovalRequestId: draftVersionId,
		UserId:            userId,
		UserResponse:      beans2.APPROVED,
	}
	draftApprovalData.CreatedBy = userId
	draftApprovalData.UpdatedBy = userId
	err := impl.requestApprovalUserDataRepo.SaveRequestApprovalUserData(draftApprovalData)
	if err != nil {
		impl.logger.Errorw("error occurred while saving user approval data", "draftVersionId", draftVersionId, "err", err)
		return err
	}
	return nil
}

func (impl *ConfigDraftServiceImpl) ApproveDraft(ctx context.Context, draftId int, draftVersionId int, userId int32) (*bean5.DraftVersionResponse, error) {
	impl.logger.Infow("approving draft", "draftId", draftId, "draftVersionId", draftVersionId, "userId", userId)

	draftVersion, err := impl.validateDraftAction(draftId, draftVersionId, bean5.PublishedDraftState, userId)
	if err != nil {
		return nil, err
	}

	err = impl.registerUserApproval(userId, draftVersionId)
	if err != nil {
		impl.logger.Errorw("error in approving the draft", "draftId", draftId, "draftVersionId", draftVersionId, "err", err)
		return nil, err
	}

	draftVersionResponse := &bean5.DraftVersionResponse{}
	draftVersionResponse.DraftVersionId = draftVersionId
	canPublishDraft, err := impl.canPublishTheDraft(draftVersionId, draftVersion.Draft.AppId, draftVersion.Draft.EnvId, draftVersion.Draft.Resource)
	if err != nil {
		impl.logger.Errorw("error in publishing the draft", "draftId", draftId, "draftVersionId", draftVersionId, "err", err)
		return nil, err
	}

	if canPublishDraft {
		draftData := draftVersion.Data
		draftsDto := draftVersion.Draft
		draftResourceType := draftsDto.Resource
		toUpdateDraftState := bean5.PublishedDraftState
		if draftResourceType == bean5.CMDraftResource || draftResourceType == bean5.CSDraftResource {
			// todo: add draft version id in cmcs history
			err = impl.handleCmCsData(draftResourceType, draftsDto, draftData, draftVersion.UserId, draftVersion.Action)
			if err != nil {
				impl.logger.Errorw("error in handling cm/cs data using draft", "draftId", draftId, "draftVersionId", draftVersionId, "userId", userId, "err", err)
				return nil, err
			}
		} else {
			// todo: add draft version id in deployment_template history
			lockValidateResponse, err := impl.handleDeploymentTemplate(ctx, draftsDto.AppId, draftsDto.EnvId, draftData, draftVersion.UserId, userId, draftVersion.Action)
			if err != nil {
				impl.logger.Errorw("error in handling deployment template data using draft", "draftId", draftId, "draftVersionId", draftVersionId, "userId", userId, "err", err)
				return nil, err
			}

			if lockValidateResponse != nil {
				draftVersionResponse.LockValidateErrorResponse = lockValidateResponse
			}
		}

		err = impl.configDraftRepository.UpdateDraftState(draftId, toUpdateDraftState, userId)
		if err != nil {
			impl.logger.Errorw("error in updating draft state", "draftId", draftId, "toUpdateDraftState", toUpdateDraftState, "userId", userId, "err", err)
			return draftVersionResponse, err
		}
	}
	return draftVersionResponse, nil
}

func (impl *ConfigDraftServiceImpl) handleCmCsData(draftResource bean5.DraftResourceType, draftDto *bean6.DraftDto, draftData string, userId int32, action bean5.ResourceAction) error {
	// if envId is -1 then it is base Configuration else Env level config
	appId := draftDto.AppId
	envId := draftDto.EnvId
	configDataRequest := &configMapBean.ConfigDataRequest{}
	err := json.Unmarshal([]byte(draftData), configDataRequest)
	if err != nil {
		impl.logger.Errorw("error occurred while unmarshalling draftData of CM/CS", "appId", appId, "envId", envId, "err", err)
		return err
	}
	configDataRequest.UserId = userId // setting draftVersion userId
	isCm := draftResource == bean5.CMDraftResource
	// NOTE: this id is not reliable, do not use this as source of truth if this info is parsed from draft.
	configDataRequest.Id = 0
	if isCm {
		if envId == beans2.BASE_CONFIG_ENV_ID {
			if action == bean5.DeleteResourceAction {
				_, err = impl.configMapService.CMGlobalDeleteByAppId(draftDto.ResourceName, appId, userId)
			} else {
				_, err = impl.configMapService.CMGlobalAddUpdate(configDataRequest)
			}
		} else {
			if action == bean5.DeleteResourceAction {
				_, err = impl.configMapService.CMEnvironmentDeleteByAppIdAndEnvId(draftDto.ResourceName, appId, envId, userId)
			} else {
				_, err = impl.configMapService.CMEnvironmentAddUpdate(configDataRequest)
			}
		}
	} else {
		if envId == beans2.BASE_CONFIG_ENV_ID {
			if action == bean5.DeleteResourceAction {
				_, err = impl.configMapService.CSGlobalDeleteByAppId(draftDto.ResourceName, appId, userId)
			} else {
				_, err = impl.configMapService.CSGlobalAddUpdate(configDataRequest)
			}
		} else {
			if action == bean5.DeleteResourceAction {
				_, err = impl.configMapService.CSEnvironmentDeleteByAppIdAndEnvId(draftDto.ResourceName, appId, envId, userId)
			} else {
				_, err = impl.configMapService.CSEnvironmentAddUpdate(configDataRequest)
			}
		}
	}
	if err != nil {
		impl.logger.Errorw("error occurred while adding/updating/deleting config", "isCm", isCm, "action", action, "appId", appId, "envId", envId, "err", err)
	}
	return err
}

func (impl *ConfigDraftServiceImpl) handleDeploymentTemplate(ctx context.Context, appId int, envId int, draftData string, draftUserId, loggedInUserId int32, action bean5.ResourceAction) (*bean3.LockValidateErrorResponse, error) {
	token := ctx.Value("token").(string)
	var err error
	var lockValidateResp *bean3.LockValidateErrorResponse
	lockDraftValidateReq := bean5.ConfigDraftRequest{
		Action:   action,
		AppId:    appId,
		EnvId:    envId,
		Data:     draftData,
		UserId:   loggedInUserId,
		Resource: bean5.DeploymentTemplateResource,
	}
	//getting lock config keys
	validateLockResp, err := impl.ValidateLockDraft(lockDraftValidateReq, token)
	if err != nil {
		impl.logger.Errorw("error in getting lock draft validate state", "lockDraftValidateReq", lockDraftValidateReq, "err", err)
		return nil, err
	}
	if validateLockResp != nil && validateLockResp.IsLockConfigError == true {
		impl.logger.Warnw("skipping deployment template handling for approval, violating lock config", "lockDraftValidateReq", lockDraftValidateReq)
		return nil, util.GetApiErrorAdapter(http.StatusUnprocessableEntity, "429", "cannot approve, lock config violated", "cannot approve, lock config violated")
	}
	if envId == beans2.BASE_CONFIG_ENV_ID {
		lockValidateResp, err = impl.handleBaseDeploymentTemplate(appId, envId, draftData, draftUserId, action, ctx)
		if err != nil {
			return nil, err
		}
	} else {
		lockValidateResp, err = impl.handleEnvLevelTemplate(appId, envId, draftData, draftUserId, action, ctx, token)
		if err != nil {
			return nil, err
		}
	}
	return lockValidateResp, nil
}

func (impl *ConfigDraftServiceImpl) handleBaseDeploymentTemplate(appId int, envId int, draftData string, userId int32, action bean5.ResourceAction, ctx context.Context) (*bean3.LockValidateErrorResponse, error) {
	templateRequest := bean8.TemplateRequest{}
	var templateValidated bool
	err := json.Unmarshal([]byte(draftData), &templateRequest)
	if err != nil {
		impl.logger.Errorw("error occurred while unmarshalling draftData of deployment template", "appId", appId, "envId", envId, "err", err)
		return nil, err
	}

	env, _ := impl.envRepository.FindById(envId)
	//VARIABLE_RESOLVE
	scope := bean7.Scope{
		AppId:     appId,
		EnvId:     envId,
		ClusterId: env.ClusterId,
	}

	if !templateRequest.SaveEligibleChanges {
		templateValidated, err = impl.deploymentTemplateValidationService.DeploymentTemplateValidate(ctx, templateRequest.ValuesOverride, templateRequest.ChartRefId, scope)
		if err != nil {
			return nil, err
		}
		if !templateValidated {
			return nil, errors.New(bean5.TemplateOutdated)
		}
	}
	templateRequest.UserId = userId
	var createResp *bean8.TemplateResponse
	var lockValidateResp *bean3.LockValidateErrorResponse
	if action == bean5.AddResourceAction {
		createResp, err = impl.chartService.Create(templateRequest, ctx)
	} else {
		createResp, err = impl.chartService.UpdateAppOverride(ctx, &templateRequest, ctx.Value("token").(string))
	}
	if createResp != nil {
		lockValidateResp = createResp.LockValidateErrorResponse
	}
	return lockValidateResp, err
}

func (impl *ConfigDraftServiceImpl) handleEnvLevelTemplate(appId int, envId int, draftData string, userId int32, action bean5.ResourceAction, ctx context.Context, token string) (*bean3.LockValidateErrorResponse, error) {
	envConfigProperties := &bean.EnvironmentProperties{}
	err := json.Unmarshal([]byte(draftData), envConfigProperties)
	if err != nil {
		impl.logger.Errorw("error occurred while unmarshalling draftData of env deployment template", "appId", appId, "envId", envId, "err", err)
		return nil, err
	}
	envConfigProperties.SanitizeEnvOverrideValues()
	if len(envConfigProperties.MergeStrategy) == 0 {
		envConfigProperties.MergeStrategy = models.MERGE_STRATEGY_REPLACE
	}
	var updateResp *bean.EnvironmentUpdateResponse
	var lockValidateResp *bean3.LockValidateErrorResponse
	if action == bean5.AddResourceAction || action == bean5.UpdateResourceAction {
		var templateValidated bool
		envConfigProperties.UserId = userId
		envConfigProperties.EnvironmentId = envId
		chartRefId := envConfigProperties.ChartRefId

		//VARIABLE_RESOLVE
		env, _ := impl.envRepository.FindById(envId)
		scope := bean7.Scope{
			AppId:     appId,
			EnvId:     envId,
			ClusterId: env.ClusterId,
		}
		if !envConfigProperties.SaveEligibleChanges {
			valuesForValidation, _, err := impl.envConfigOverrideReadService.GetRuntimeValueForUnsavedEnvOverrideResolved(envConfigProperties.MergeStrategy, nil, appId, string(envConfigProperties.EnvOverrideValues), scope)
			if err != nil {
				impl.logger.Errorw("error in  getting values for validation", "appId", appId, "err", err)

			}
			templateValidated, err = impl.deploymentTemplateValidationService.DeploymentTemplateValidateWithoutResolution(ctx, json.RawMessage(valuesForValidation), chartRefId)
			if err != nil {
				return nil, err
			}
			if !templateValidated {
				return nil, errors.New(bean5.TemplateOutdated)
			}
		}
		if action == bean5.AddResourceAction {
			//TODO code duplicated, needs refactoring
			updateResp, err = impl.createEnvLevelDeploymentTemplate(ctx, appId, envId, envConfigProperties, userId)
		} else {
			updateResp, err = impl.propertiesConfigService.UpdateEnvironmentProperties(appId, envId, envConfigProperties, token, userId)
		}
		if err != nil {
			impl.logger.Errorw("service err, EnvConfigOverrideUpdate", "appId", appId, "envId", envId, "err", err, "payload", envConfigProperties)
		}
		if updateResp != nil {
			lockValidateResp = updateResp.LockValidateErrorResponse
		}
	} else {
		id := envConfigProperties.Id
		_, err = impl.propertiesConfigService.ResetEnvironmentProperties(id, userId)
		if err != nil {
			impl.logger.Errorw("error occurred while deleting env level Deployment template", "id", id, "err", err)
		}
	}
	return lockValidateResp, err
}

func (impl *ConfigDraftServiceImpl) createEnvLevelDeploymentTemplate(ctx context.Context, appId int, envId int, envConfigProperties *bean.EnvironmentProperties, userId int32) (*bean.EnvironmentUpdateResponse, error) {
	createResp, err := impl.propertiesConfigService.CreateEnvironmentProperties(ctx, appId, envConfigProperties)
	if err != nil {
		if err.Error() == bean4.NOCHARTEXIST {
			err = impl.createMissingChart(ctx, appId, envId, envConfigProperties, userId)
			if err == nil {
				createResp, err = impl.propertiesConfigService.CreateEnvironmentProperties(ctx, appId, envConfigProperties)
			}
		}
	}
	return createResp, err
}

func (impl *ConfigDraftServiceImpl) createMissingChart(ctx context.Context, appId int, envId int, envConfigProperties *bean.EnvironmentProperties, userId int32) error {
	appMetrics := false
	if envConfigProperties.AppMetrics != nil {
		appMetrics = *envConfigProperties.AppMetrics
	}
	templateRequest := bean8.TemplateRequest{
		AppId:               appId,
		ChartRefId:          envConfigProperties.ChartRefId,
		ValuesOverride:      globalUtil.GetEmptyJSON(),
		UserId:              userId,
		IsAppMetricsEnabled: appMetrics,
	}
	_, err := impl.chartService.CreateChartFromEnvOverride(ctx, templateRequest)
	if err != nil {
		impl.logger.Errorw("service err, EnvConfigOverrideCreate from draft", "appId", appId, "envId", envId, "err", err, "payload", envConfigProperties)
	}
	return err
}

func (impl *ConfigDraftServiceImpl) updateWithUserMetadata(versions []*bean5.DraftVersionMetadata) error {
	var userIds []int32
	for _, versionMetadata := range versions {
		userIds = append(userIds, versionMetadata.UserId)
	}
	userIdVsUserInfoMap, err := impl.getUserMetadata(userIds)
	if err != nil {
		return err
	}
	for _, versionMetadata := range versions {
		if userInfo, found := userIdVsUserInfoMap[versionMetadata.UserId]; found {
			versionMetadata.UserEmail = userInfo.EmailId
		}
	}
	return nil
}

func (impl *ConfigDraftServiceImpl) getUserMetadata(userIds []int32) (map[int32]bean4.UserInfo, error) {
	userInfos, err := impl.userService.GetByIds(userIds)
	if err != nil {
		return nil, err
	}
	userIdVsUserInfoMap := make(map[int32]bean4.UserInfo, len(userIds))
	for _, userInfo := range userInfos {
		userIdVsUserInfoMap[userInfo.Id] = userInfo
	}
	return userIdVsUserInfoMap, nil
}

func (impl *ConfigDraftServiceImpl) CheckIfUserHasApprovalAccess(appId int, envId int, token string) bool {
	email, _, err := impl.userService.GetEmailAndVersionFromToken(token)
	if err != nil {
		return false
	}
	if slices.Contains(impl.getApproversData(appId, envId, token), email) {
		return true
	} else {
		return false
	}
}

func (impl *ConfigDraftServiceImpl) getApproversData(appId int, envId int, token string) []string {
	var approvers []string
	application, err := impl.appRepo.FindAppAndTeamByAppId(appId)
	if err != nil {
		return approvers
	}
	var appName = application.AppName
	var env *repository2.Environment
	envIdentifier := ""
	if envId > 0 {
		env, err = impl.envRepository.FindById(envId)
		if err != nil {
			return approvers
		}
		envIdentifier = env.EnvironmentIdentifier
	}
	approvers, err = impl.userService.GetUserByEnvAndApprovalAction(appName, envIdentifier, application.Team.Name, bean4.ConfigApprover, token)
	if err != nil {
		impl.logger.Errorw("error occurred while fetching config approval emails, so sending empty approvers list", "err", err)
	}
	return approvers
}

func (impl *ConfigDraftServiceImpl) checkUserContributedToDraft(draftId int, userId int32) (bool, error) {
	versionsMetadata, err := impl.configDraftRepository.GetDraftVersionsMetadata(draftId)
	if err != nil {
		return false, err
	}
	for _, versionMetadata := range versionsMetadata {
		if versionMetadata.UserId == userId {
			return true, nil
		}
	}
	return false, nil
}

func (impl *ConfigDraftServiceImpl) GetDraftsCount(appId int, envIds []int) ([]*bean5.DraftCountResponse, error) {
	var draftCountResponse []*bean5.DraftCountResponse
	draftDtos, err := impl.configDraftRepository.GetDraftMetadataForAppAndEnv(appId, envIds)
	if err != nil {
		return draftCountResponse, err
	}
	draftCountMap := make(map[int]int, len(draftDtos))
	for _, draftDto := range draftDtos {
		envId := draftDto.EnvId
		count := draftCountMap[envId]
		count++
		draftCountMap[envId] = count
	}
	for envId, count := range draftCountMap {
		draftCountResponse = append(draftCountResponse, &bean5.DraftCountResponse{AppId: appId, EnvId: envId, DraftsCount: count})
	}
	return draftCountResponse, nil
}

func (impl *ConfigDraftServiceImpl) validateDraftData(appId int, envId int, resourceType bean5.DraftResourceType, action bean5.ResourceAction, draftData, token string, userId int32) (*bean3.LockValidateErrorResponse, string, error) {
	if resourceType == bean5.CMDraftResource || resourceType == bean5.CSDraftResource {
		return nil, draftData, impl.validateCmCs(action, draftData)
	}
	return impl.validateDeploymentTemplate(appId, envId, action, draftData, userId, token)
}

func (impl *ConfigDraftServiceImpl) validateCmCs(resourceAction bean5.ResourceAction, draftData string) error {
	configDataRequest := &configMapBean.ConfigDataRequest{}
	err := json.Unmarshal([]byte(draftData), configDataRequest)
	if err != nil {
		impl.logger.Errorw("error occurred while unmarshalling draftData of CM/CS", "err", err)
		return err
	}
	if resourceAction == bean5.AddResourceAction || resourceAction == bean5.UpdateResourceAction {
		configData := configDataRequest.ConfigData[0]
		_, err = impl.configReadService.ValidateConfigData(configData)
	} else {
		configId := configDataRequest.Id
		if configId == 0 {
			impl.logger.Errorw("error occurred while validating CM/CS ", "id", configId)
			err = errors.New("invalid config id")
		}
	}
	return err
}

func (impl *ConfigDraftServiceImpl) validateDeploymentTemplate(appId int, envId int, resourceAction bean5.ResourceAction, draftData string, userId int32, token string) (*bean3.LockValidateErrorResponse, string, error) {
	if envId == beans2.BASE_CONFIG_ENV_ID {
		templateRequest := bean8.TemplateRequest{}
		var templateValidated bool
		err := json.Unmarshal([]byte(draftData), &templateRequest)
		if err != nil {
			impl.logger.Errorw("error occurred while unmarshalling draftData of deployment template", "envId", envId, "err", err)
			return nil, draftData, err
		}
		// TODO add a cache to check lock
		savedLatestChart, err := impl.chartRepository.FindLatestChartForAppByAppId(templateRequest.AppId)
		if err != nil && !errors.Is(err, pg.ErrNoRows) {
			return nil, draftData, err
		}

		if errors.Is(err, pg.ErrNoRows) {
			return nil, draftData, errors.New(bean5.DraftNotAllowedForBaseConfigOfNewApp)
		}

		if templateRequest.SaveEligibleChanges {
			revertedValues, err := impl.lockedConfigService.RevertChangesInLockedFields(string(templateRequest.ValuesOverride), savedLatestChart.GlobalOverride, token, int(userId), appId, envId)
			if err != nil {
				impl.logger.Errorw("error in syncing locked fields of config present in request and saved in DB", "appId", appId, "envId", envId, "err", err)
				return nil, "", err
			}
			templateRequest.ValuesOverride = json.RawMessage(revertedValues)
			templateByte, err := json.Marshal(templateRequest)
			if err != nil {
				return nil, draftData, err
			}
			draftData = string(templateByte)
		}

		lockConfigErrorResponse, err := impl.lockedConfigService.HandleLockConfiguration(string(templateRequest.ValuesOverride), savedLatestChart.GlobalOverride, token, int(userId), appId, envId)
		if err != nil {
			return nil, draftData, err
		}
		if lockConfigErrorResponse != nil {
			return lockConfigErrorResponse, draftData, nil
		}
		var clusterId int
		//VARIABLE_RESOLVE
		env, err := impl.envRepository.FindById(envId)
		if err != nil && !util.IsErrNoRows(err) {
			impl.logger.Errorw("error in finding env by envId", "envId", envId, "err", err)
			return nil, draftData, err
		} else if util.IsErrNoRows(err) {
			// this can be the case of base configuration where envId = -1
			clusterId = 0
		} else {
			clusterId = env.ClusterId
		}
		scope := bean7.Scope{
			AppId:     templateRequest.AppId,
			EnvId:     envId,
			ClusterId: clusterId,
		}
		templateValidated, err = impl.deploymentTemplateValidationService.DeploymentTemplateValidate(context.Background(), templateRequest.ValuesOverride, templateRequest.ChartRefId, scope)
		if err != nil {
			return nil, draftData, err
		}
		if !templateValidated {
			return nil, draftData, errors.New(bean5.TemplateOutdated)
		}

	} else {
		envConfigProperties := &bean.EnvironmentProperties{}
		err := json.Unmarshal([]byte(draftData), envConfigProperties)
		if err != nil {
			impl.logger.Errorw("error occurred while unmarshalling draftData of env deployment template", "envId", envId, "err", err)
			return nil, draftData, err
		}
		envConfigProperties.SanitizeEnvOverrideValues()

		if envConfigProperties.MergeStrategy == models.MERGE_STRATEGY_REPLACE &&
			globalUtil.IsEmptyJSON(envConfigProperties.EnvOverrideValues) {
			return nil, draftData, fmt.Errorf("env override values cannot be empty for replace strategy")
		}

		if resourceAction == bean5.AddResourceAction || resourceAction == bean5.UpdateResourceAction {
			var currentEnvOverrideValues json.RawMessage
			currentLatestChart, err := impl.envConfigOverrideReadService.FindLatestChartForAppByAppIdAndEnvId(appId, envId)

			if err != nil && !errors1.IsNotFound(err) {
				return nil, draftData, err
			}

			if errors1.IsNotFound(err) {
				emptyJSON, err := globalUtil.GetEmptyJsonObjectString()
				if err != nil {
					impl.logger.Errorw("error in getting empty json object", "err", err)
					return nil, draftData, err
				}
				currentEnvOverrideValues = []byte(emptyJSON)
			} else {
				currentEnvOverrideValues = []byte(currentLatestChart.GetDBOverrideValuesByMergeStrategy())
			}

			if envConfigProperties.SaveEligibleChanges {

				eligibleValues, err := impl.lockedConfigService.RevertChangesInLockedFields(string(envConfigProperties.EnvOverrideValues), string(currentEnvOverrideValues), token, int(userId), appId, envId)
				if err != nil {
					impl.logger.Errorw("error in syncing locked fields of config present in request and saved in DB", "appId", appId, "envId", envId, "err", err)
					return nil, "", err
				}

				envConfigProperties.EnvOverrideValues = json.RawMessage(eligibleValues)

				envConfigByte, err := json.Marshal(envConfigProperties)
				if err != nil {
					return nil, draftData, err
				}
				draftData = string(envConfigByte)
			}

			lockConfigErrorResponse, err := impl.lockedConfigService.HandleLockConfiguration(string(envConfigProperties.EnvOverrideValues), string(currentEnvOverrideValues), token, int(userId), appId, envId)

			if err != nil {
				return nil, draftData, err
			}
			if lockConfigErrorResponse != nil {
				return lockConfigErrorResponse, draftData, nil
			}
			//VARIABLE_RESOLVE
			env, _ := impl.envRepository.FindById(envId)
			scope := bean7.Scope{
				AppId:     appId,
				EnvId:     envId,
				ClusterId: env.ClusterId,
			}

			chartRefId := envConfigProperties.ChartRefId

			valuesForValidation, _, err := impl.envConfigOverrideReadService.GetRuntimeValueForUnsavedEnvOverrideResolved(envConfigProperties.MergeStrategy, nil, appId, string(envConfigProperties.EnvOverrideValues), scope)
			if err != nil {
				impl.logger.Errorw("error in getting values for deploymentTemplate validation", "appId", appId, "err", err)
			}
			templateValidated, err := impl.deploymentTemplateValidationService.DeploymentTemplateValidateWithoutResolution(context.Background(), json.RawMessage(valuesForValidation), chartRefId)
			if err != nil {
				return nil, draftData, err
			}
			if !templateValidated {
				return nil, draftData, errors.New(bean5.TemplateOutdated)
			}
		} else {
			id := envConfigProperties.Id
			if id == 0 {
				impl.logger.Errorw("error occurred while validating CM/CS ", "id", id)
				err = errors.New("invalid template ref id")
			}
		}
	}
	return nil, draftData, nil
}

func (impl *ConfigDraftServiceImpl) checkLockConfiguration(appId int, envId int, resourceAction bean5.ResourceAction, draftData string, userId int32, token string) (*bean3.LockValidateErrorResponse, error) {
	if envId == beans2.BASE_CONFIG_ENV_ID {
		templateRequest := bean8.TemplateRequest{}
		err := json.Unmarshal([]byte(draftData), &templateRequest)
		if err != nil {
			impl.logger.Errorw("error occurred while unmarshalling draftData of deployment template", "envId", envId, "err", err)
			return nil, err
		}
		// TODO add a cache to check lock
		savedLatestChart, err := impl.chartRepository.FindLatestChartForAppByAppId(templateRequest.AppId)
		if err != nil {
			return nil, err
		}

		lockConfigErrorResponse, err := impl.lockedConfigService.HandleLockConfiguration(string(templateRequest.ValuesOverride), savedLatestChart.GlobalOverride, token, int(userId), appId, envId)
		if err != nil {
			return nil, err
		}
		if lockConfigErrorResponse != nil {
			return lockConfigErrorResponse, nil
		}
	} else {
		envConfigProperties := &bean.EnvironmentProperties{}
		err := json.Unmarshal([]byte(draftData), envConfigProperties)
		if err != nil {
			impl.logger.Errorw("error occurred while unmarshalling draftData of env deployment template", "envId", envId, "err", err)
			return nil, err
		}
		envConfigProperties.SanitizeEnvOverrideValues()
		if resourceAction == bean5.AddResourceAction || resourceAction == bean5.UpdateResourceAction {
			var currentEnvOverrideValues json.RawMessage
			currentLatestChart, err := impl.envConfigOverrideReadService.FindLatestChartForAppByAppIdAndEnvId(appId, envId)
			if err != nil && !errors1.IsNotFound(err) {
				return nil, err
			}
			if errors1.IsNotFound(err) {
				chart, err := impl.chartRepository.FindLatestChartForAppByAppId(appId)
				if err != nil {
					return nil, err
				}
				if envConfigProperties.MergeStrategy == models.MERGE_STRATEGY_REPLACE {
					currentEnvOverrideValues = []byte(chart.GlobalOverride)
				} else {
					emptyJSON, err := globalUtil.GetEmptyJsonObjectString()
					if err != nil {
						impl.logger.Errorw("error in getting empty json object", "err", err)
						return nil, err
					}
					currentEnvOverrideValues = []byte(emptyJSON)
				}
			} else {
				currentEnvOverrideValues = []byte(currentLatestChart.GetDBOverrideValuesByMergeStrategy())
			}
			lockConfigErrorResponse, err := impl.lockedConfigService.HandleLockConfiguration(string(envConfigProperties.EnvOverrideValues), string(currentEnvOverrideValues), token, int(userId), appId, envId)
			if err != nil {
				return nil, err
			}
			if lockConfigErrorResponse != nil {
				return lockConfigErrorResponse, nil
			}
		}

	}
	return &bean3.LockValidateErrorResponse{
		IsLockConfigError: false,
	}, nil
}

func (impl *ConfigDraftServiceImpl) ValidateLockDraft(request bean5.ConfigDraftRequest, token string) (*bean3.LockValidateErrorResponse, error) {
	resourceAction := request.Action
	envId := request.EnvId
	appId := request.AppId
	resourceKind := adaptors.GetApprovalResourceKind(request.Resource)
	protectionEnabled := impl.resourceProtectionService.ResourceProtectionEnabled(resourceKind, appId, envId, true, false)
	if !protectionEnabled {
		return nil, util.DefaultApiError().WithUserMessage(bean5.ConfigProtectionDisabled).WithHttpStatusCode(http.StatusConflict).WithCode(constants.ApprovalConfigDependentActionFailure)
	}
	return impl.checkLockConfiguration(request.AppId, envId, resourceAction, request.Data, request.UserId, token)
}

func (impl *ConfigDraftServiceImpl) CheckValidRequest(appId int, name string, resourceType bean5.DraftResourceType) (bool, error) {

	// if protection isn't enabled, discard all the non-terminal state drafts and return valid response
	impl.resourceProtectionService.ResourceProtectionEnabled(adaptors.GetApprovalResourceKind(resourceType), appId, -1, true, false)

	resourceProtectionStates, err := impl.resourceProtectionService.GetResourceProtectMetadataV2(appId)
	if err != nil {
		impl.logger.Errorw("error in getting resource protection state", "appId", appId, "err", err)
		return false, err
	}

	envIdToResourceProtection := make(map[int]*bean10.ResourceProtectModel)
	for _, state := range resourceProtectionStates {
		envIdToResourceProtection[state.EnvId] = state
	}

	drafts, err := impl.configDraftRepository.GetAllEnvLevelDraftsForApp(appId, name, resourceType)
	if err != nil {
		impl.logger.Errorw("error in getting all drafts by appId", "appId", appId, "err", err)
		return false, err
	}

	for _, draft := range drafts {
		if resourceProtection, ok := envIdToResourceProtection[draft.Draft.EnvId]; ok {
			if resourceProtection.ProtectionState == bean10.DisabledProtectionState {
				// if none of the resource is protected continue
				continue
			}
			// check if resource present in request is protected
			isResourceProtected := false
			for _, approvalConfig := range resourceProtection.ApprovalConfigurations {
				if approvalConfig.Kind == beans2.APPROVAL_FOR_CONFIGURATION_CM && resourceType == bean5.CMDraftResource {
					isResourceProtected = true
				} else if approvalConfig.Kind == beans2.APPROVAL_FOR_CONFIGURATION_CS && resourceType == bean5.CSDraftResource {
					isResourceProtected = true
				}
			}
			if !isResourceProtected {
				continue
			}
		}

		if draft == nil || (draft != nil && draft.Id == 0) || draft.Draft.DraftState.IsTerminal() {
			continue
		}

		configDataRequest := &configMapBean.ConfigDataRequest{}
		err = json.Unmarshal([]byte(draft.Data), configDataRequest)
		if err != nil {
			impl.logger.Errorw("error occurred while unmarshalling draftData of CS", "err", err)
			return false, err
		}

		for _, cm := range configDataRequest.ConfigData {
			configMapDraftExistsWithSameNameAndMergeTypeAsPatch := (draft.Draft.Resource == resourceType) && (cm.Name == name) && (cm.MergeStrategy == models.MERGE_STRATEGY_PATCH)
			if configMapDraftExistsWithSameNameAndMergeTypeAsPatch {
				invalidDeleteReqMsg := fmt.Sprintf("cannot delete secret as draft with patch strategy exist at environment -%s ", name)
				return false, util.NewApiError(http.StatusBadRequest, invalidDeleteReqMsg, invalidDeleteReqMsg)
			}
		}
	}

	return true, nil
}

func (impl *ConfigDraftServiceImpl) compareAndFetchMaskedDraftCSData(draftCsData string, lastSavedSecretConfigData *configMapBean.ConfigDataRequest) string {
	draftSecretConfig, err := helpers.FetchUnmarshalledDraftData(draftCsData)
	if err != nil {
		impl.logger.Errorw("error occurred while FetchUnmarshalledDraftData", "err", err)
		return draftCsData
	}
	lastSavedSecretKeyValMap, err := utils2.GetKeyValMapForConfigDataList(lastSavedSecretConfigData.ConfigData)
	if err != nil {
		impl.logger.Errorw("error in getting key val map for secret config data", "err", err)
		return draftCsData
	}
	err = utils3.CompareAndMaskSecretValuesInConfigData(draftSecretConfig.ConfigData, lastSavedSecretKeyValMap)
	if err != nil {
		impl.logger.Errorw("error in comparing draft config secret data with last saved data ", "err", err)
		return draftCsData
	}

	encryptedCSData, err := json.Marshal(draftSecretConfig)
	if err != nil {
		impl.logger.Errorw("error occurred while marshalling config data request, so returning original data", "err", err)
		return draftCsData
	}
	return string(encryptedCSData)
}

func (impl *ConfigDraftServiceImpl) CompareDraftWithLastSavedAndMaskCSData(draftResourceName, draftCsData string, envId, appId int) string {
	lastSavedSecretConfigData, err := impl.configMapService.FetchCSByNameAppIdAndEnvId(draftResourceName, appId, envId)
	if err != nil {
		impl.logger.Errorw("error occurred while fetching CS by resource name, appId and envId", "resourceName", draftResourceName, "appId", appId, "envId", envId, "err", err)
		return draftCsData
	}
	maskedDraftCSData := impl.compareAndFetchMaskedDraftCSData(draftCsData, lastSavedSecretConfigData)

	return maskedDraftCSData
}

func (impl *ConfigDraftServiceImpl) getUserApprovalMetadata(currentUserEmail string, draftVersionId int, appId, envId int, resourceType bean5.DraftResourceType, requestedUserId int32, requestedUserEmail string, approvalPermissionUsersList []string) (*beans2.UserApprovalMetadata, error) {
	metadata := &beans2.UserApprovalMetadata{
		ApprovalRequestId: draftVersionId,
		RequestedUserData: beans2.UserApprovalData{
			UserId:    requestedUserId,
			UserEmail: requestedUserEmail,
		},
	}

	userGroupIdentifierNameMappings, err := impl.userGroupService.ListIdentifierAndName()
	if err != nil {
		impl.logger.Errorw("error occurred while fetching userGroups metadata", "err", err)
		return nil, err
	}

	userApprovalMetadataReq := beans2.NewApprovalUsersAndUserGroupsMetadataRequest(userGroupIdentifierNameMappings)
	userApprovalMetadataReq, err = impl.setApprovalAccessUsersData(userApprovalMetadataReq, approvalPermissionUsersList)
	if err != nil {
		impl.logger.Errorw("error in setting approval access users info in approval metadata request", "draftVersionId", draftVersionId, "err", err)
		return nil, err
	}
	userApprovalMetadataReq, err = impl.setApprovedUsersData(userApprovalMetadataReq, draftVersionId)
	if err != nil {
		impl.logger.Errorw("error in setting approved users info in approval metadata request", "draftVersionId", draftVersionId, "err", err)
		return nil, err
	}

	configDataWithApprovalUsers, err := impl.getUserApprovalConfigDtoWithApprovedUsers(userApprovalMetadataReq, resourceType, appId, envId)
	if err != nil {
		impl.logger.Errorw("error in getting the user approval config data with approved users", "draftVersionId", draftVersionId, "resourceType", resourceType, "err", err)
		return nil, err
	}

	metadata.ApprovalRuntimeState = beans2.RequestedApprovalState
	if configDataWithApprovalUsers.Satisfied() {
		metadata.ApprovalRuntimeState = beans2.ApprovedApprovalState
	}
	metadata.HasCurrentUserApproved = pointer.Bool(userApprovalMetadataReq.HasApproved(currentUserEmail))
	metadata.CanCurrentUserApprove = pointer.Bool(userApprovalMetadataReq.CanApprove(currentUserEmail) && utils.UserCanApprove(configDataWithApprovalUsers, currentUserEmail))
	metadata.ApprovalConfigV2 = configDataWithApprovalUsers
	return metadata, nil
}

func (impl *ConfigDraftServiceImpl) canPublishTheDraft(draftVersionId int, appId, envId int, resourceType bean5.DraftResourceType) (bool, error) {
	userGroupIdentifierNameMappings, err := impl.userGroupService.ListIdentifierAndName()
	if err != nil {
		impl.logger.Errorw("error occurred while fetching userGroups metadata", "err", err)
		return false, err
	}

	userApprovalMetadataReq := beans2.NewApprovalUsersAndUserGroupsMetadataRequest(userGroupIdentifierNameMappings)

	userApprovalMetadataReq, err = impl.setApprovedUsersData(userApprovalMetadataReq, draftVersionId)
	if err != nil {
		impl.logger.Errorw("error in setting approved users info in approval metadata request", "draftVersionId", draftVersionId, "err", err)
		return false, err
	}

	userApprovalConfigDto, err := impl.getUserApprovalConfigDtoWithApprovedUsers(userApprovalMetadataReq, resourceType, appId, envId)
	if err != nil {
		impl.logger.Errorw("error in getting the user approval config data with approved users", "draftVersionId", draftVersionId, "err", err)
		return false, err
	}
	return userApprovalConfigDto.Satisfied(), nil
}

func (impl *ConfigDraftServiceImpl) setApprovalAccessUsersData(approvalMetadataReq *beans2.ApprovalUsersAndUserGroupsMetadataReq, approvalAccessUserEmails []string) (*beans2.ApprovalUsersAndUserGroupsMetadataReq, error) {
	approvalAccessUserGroupMappings, err := impl.userGroupService.GetUserGroupAndUserMappingsByUserEmails(approvalAccessUserEmails)
	if err != nil {
		impl.logger.Errorw("service err, GetUserGroupAndUserMappingsByUserEmails", "approvalAccessUserEmails", approvalAccessUserEmails, "err", err)
		return nil, err
	}

	return approvalMetadataReq.
		WithApprovalAccessUsersInfo(beans2.NewApprovalAccessUsersInfo(approvalAccessUserEmails, approvalAccessUserGroupMappings)), nil
}

func (impl *ConfigDraftServiceImpl) setApprovedUsersData(approvalMetadataReq *beans2.ApprovalUsersAndUserGroupsMetadataReq, draftVersionId int) (*beans2.ApprovalUsersAndUserGroupsMetadataReq, error) {
	requestApprovalUserData, err := impl.requestApprovalUserDataRepo.FetchApprovedDataByApprovalId(draftVersionId, models.CONFIG_APPROVAL)
	if err != nil {
		impl.logger.Errorw("error in getting the request apprpoval user data for the config", "draftVersionId", draftVersionId, "err", err)
		return nil, err
	}

	requestApprovedUserIds := make([]int32, 0)
	userResponses := make([]*beans2.UserResponse, 0)
	for _, userResp := range requestApprovalUserData {
		if userResp.UserResponse != beans2.APPROVED {
			continue
		}
		userResponses = append(userResponses, &beans2.UserResponse{UserId: userResp.UserId, UserResponse: userResp.UserResponse, UserEmailId: userResp.User.EmailId, UserDeleted: !userResp.User.Active})
		requestApprovedUserIds = append(requestApprovedUserIds, userResp.UserId)
	}

	approvedUserGroupMappings, err := impl.userGroupService.GetByUserIds(requestApprovedUserIds)
	if err != nil {
		impl.logger.Errorw("error occurred while fetching userGroups", "approvedUserIds", requestApprovedUserIds, "err", err)
		return nil, err
	}
	approvalMetadataReq = approvalMetadataReq.WithApprovedUsersInfo(beans2.NewApprovedUsersInfo(userResponses, approvedUserGroupMappings))
	return approvalMetadataReq, nil
}

func (impl *ConfigDraftServiceImpl) getUserApprovalConfigDtoWithApprovedUsers(approvalMetadataReq *beans2.ApprovalUsersAndUserGroupsMetadataReq, resourceType bean5.DraftResourceType, appId, envId int) (*beans2.UserApprovalConfigDTO, error) {
	resourceKind := adaptors.GetApprovalResourceKind(resourceType)
	scopeWithApprovalConfigs, err := impl.approvalConfigurationEnforcementService.GetConfigurationsByAppAndEnvIdBulk(model2.APPLY_POLICY_APPROVAL_CONFIGURATION, []*beans2.Scope{{AppId: appId, EnvId: envId}})
	if err != nil {
		impl.logger.Errorw("error in fetching the approval config of config protection", "err", err)
		return nil, err
	}

	if len(scopeWithApprovalConfigs) == 0 {
		return nil, util.DefaultApiError().WithUserMessage(bean5.ConfigProtectionDisabled).WithHttpStatusCode(http.StatusConflict).WithCode(constants.ApprovalConfigDependentActionFailure)
	}

	approvalConfigurationMap := scopeWithApprovalConfigs[0].ApplyStageApprovalConfigs
	if approvalConfigurationMap == nil || approvalConfigurationMap[resourceKind] == nil {
		return nil, util.DefaultApiError().WithUserMessage(bean5.ConfigProtectionDisabled).WithHttpStatusCode(http.StatusConflict).WithCode(constants.ApprovalConfigDependentActionFailure)
	}

	approvalConfiguration := approvalConfigurationMap[resourceKind]
	responseDto := utils.GetDtoFromConfigAndUserResponse(resourceKind, approvalConfiguration, approvalMetadataReq)
	return responseDto, nil
}

// DiscardUnPublishedDraftsByResourceTypeAndName will discard a single resource's draft of the given resourceType drafts that are in not terminal state
// for given App and Env and resourceName
func (impl *ConfigDraftServiceImpl) DiscardUnPublishedDraftsByResourceTypeAndName(resourceKind beans2.ApprovalFor, appId, envId int, resourceName string) error {
	impl.logger.Infow("express edit has been made by exception user, so discarding all previous un published drafts", "appId", appId, "envId", envId, "resourceKind", resourceKind, "resourceName", resourceName)
	return impl.configDraftRepository.DiscardDraftsByResourceName(adaptors.GetApprovalResourceType(resourceKind), appId, envId, bean4.SYSTEM_USER_ID, resourceName)
}
