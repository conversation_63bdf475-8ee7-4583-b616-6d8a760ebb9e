package rbac

import (
	"github.com/devtron-labs/devtron/pkg/auth/authorisation/casbin"
	"github.com/devtron-labs/devtron/util/rbac"
	"go.uber.org/zap"
)

type ApproverRbacService interface {
	CheckForApproverAccessByEnvAndAppId(envId int, appId int, token string) (notAnApprover bool)
	CheckForApproverAccessByEnvAndAppName(envName, appName string, token string) (notAnApprover bool)
}

type ApproverRbacServiceImpl struct {
	logger       *zap.SugaredLogger
	enforcer     casbin.Enforcer
	enforcerUtil rbac.EnforcerUtil
}

func NewApproverRbacServiceImpl(
	enforcer casbin.Enforcer,
	enforcerUtil rbac.EnforcerUtil,
	logger *zap.SugaredLogger,
) *ApproverRbacServiceImpl {
	clusterRbacService := &ApproverRbacServiceImpl{
		logger:       logger,
		enforcer:     enforcer,
		enforcerUtil: enforcerUtil,
	}

	return clusterRbacService
}

func (impl *ApproverRbacServiceImpl) CheckForApproverAccessByEnvAndAppId(envId int, appId int, token string) (notAnApprover bool) {
	var object string
	if envId > 0 {
		object = impl.enforcerUtil.GetTeamEnvRBACNameByAppId(appId, envId)
	} else {
		object = impl.enforcerUtil.GetTeamNoEnvRBACNameByAppId(appId)
	}
	if ok := impl.enforcer.Enforce(token, casbin.ResourceConfig, casbin.ActionApprove, object); !ok {
		notAnApprover = true
	}
	return notAnApprover
}

func (impl *ApproverRbacServiceImpl) CheckForApproverAccessByEnvAndAppName(envName, appName string, token string) (notAnApprover bool) {
	var object string
	if len(envName) > 0 {
		object = impl.enforcerUtil.GetTeamEnvRBACNameByAppAndEnvName(appName, envName)
	} else {
		object = impl.enforcerUtil.GetTeamNoEnvRBACNameByAppName(appName)
	}
	if ok := impl.enforcer.Enforce(token, casbin.ResourceConfig, casbin.ActionApprove, object); !ok {
		notAnApprover = true
	}
	return notAnApprover
}
