package lockConfiguration

import (
	"encoding/json"
	"github.com/devtron-labs/devtron/internal/util"
	"github.com/devtron-labs/devtron/pkg/devtronResource/read"
	repository8 "github.com/devtron-labs/devtron/pkg/devtronResource/repository"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/common/alpha1"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/lockConfiguration/adapter/service"
	repository26 "github.com/devtron-labs/devtron/pkg/policyGovernance/lockConfiguration/repository"
	"github.com/devtron-labs/devtron/pkg/resourceQualifiers"
	"github.com/devtron-labs/devtron/pkg/sql"
	"reflect"
	"testing"
)

func TestLockConfig(t *testing.T) {

	sugaredLogger, _ := util.NewSugardLogger()
	mergeUtil := util.MergeUtil{Logger: sugaredLogger}

	sqlConfig, _ := sql.GetConfig()
	db, err := sql.NewDbConnection(sqlConfig, sugaredLogger)
	if err != nil {
		t.Fail()
		return
	}
	devtronResourceSearchableKeyRepositoryImpl := repository8.NewDevtronResourceSearchableKeyRepositoryImpl(sugaredLogger, db)
	devtronResourceSearchableKey, _ := read.NewDevtronResourceSearchableKeyServiceImpl(sugaredLogger, devtronResourceSearchableKeyRepositoryImpl)
	commonPolicyEventHandler := alpha1.NewCommonPolicyActionsServiceImpl(nil, nil, nil, nil, nil, nil, nil, nil, nil, sugaredLogger, nil, devtronResourceSearchableKey)
	validate, err := util.IntValidator()
	if err != nil {
		t.Fail()
	}
	transactionUtilImpl := sql.NewTransactionUtilImpl(db)
	qualifiersMappingRepositoryImpl, err := resourceQualifiers.NewQualifiersMappingRepositoryImpl(db, sugaredLogger, transactionUtilImpl)
	if err != nil {
		t.Fail()
		return
	}
	devtronResourceSearchableKeyServiceImpl, err := read.NewDevtronResourceSearchableKeyServiceImpl(sugaredLogger, devtronResourceSearchableKeyRepositoryImpl)
	if err != nil {
		t.Fail()
		return
	}
	qualifierMappingServiceImpl, err := resourceQualifiers.NewQualifierMappingServiceImpl(sugaredLogger, qualifiersMappingRepositoryImpl, devtronResourceSearchableKeyServiceImpl)
	if err != nil {
		t.Fail()
		return
	}
	repositoryRepositoryImpl := repository26.NewRepositoryImpl(db)
	lockConfigDataAdaptor := service.NewLockConfigurationDataAdaptorImpl(sugaredLogger, validate, qualifierMappingServiceImpl, repositoryRepositoryImpl)
	lockConfigService := NewLockConfigurationServiceImpl(sugaredLogger, nil, nil, nil, mergeUtil, nil, nil, lockConfigDataAdaptor, commonPolicyEventHandler)

	type args struct {
		currentDoc  map[string]interface{}
		savedDoc    map[string]interface{}
		lockedPaths []string
	}

	tests := []struct {
		Name string
		args args
		want map[string]interface{}
	}{
		{
			Name: "simple json expression",
			args: args{
				currentDoc: map[string]interface{}{
					"a": "b",
				},
				savedDoc: map[string]interface{}{
					"a": "c",
				},
				lockedPaths: []string{".a"},
			},
			want: map[string]interface{}{
				"a": "c",
			},
		},
		{
			Name: "simple json expression nested key",
			args: args{
				currentDoc: map[string]interface{}{
					"a": map[string]interface{}{
						"b": "curr-val",
					},
				},
				savedDoc: map[string]interface{}{
					"a": map[string]interface{}{
						"b": "saved-val",
					},
				},
				lockedPaths: []string{".a.b"},
			},
			want: map[string]interface{}{
				"a": map[string]interface{}{
					"b": "saved-val",
				},
			},
		},
		{
			Name: "simple json expression: path not exist in current",
			args: args{
				currentDoc: map[string]interface{}{},
				savedDoc: map[string]interface{}{
					"a": map[string]interface{}{
						"b": "saved-val",
					},
				},
				lockedPaths: []string{".a.b"},
			},
			want: map[string]interface{}{
				"a": map[string]interface{}{
					"b": "saved-val",
				},
			},
		},
		{
			Name: "simple json expression: path not exist in saved",
			args: args{
				currentDoc: map[string]interface{}{
					"a": map[string]interface{}{
						"b": "curr-val",
					},
				},
				savedDoc: map[string]interface{}{
					"a": map[string]interface{}{},
				},
				lockedPaths: []string{".a.b"},
			},
			want: map[string]interface{}{
				"a": map[string]interface{}{},
			},
		},
		{
			Name: "filter json expression",
			args: args{
				currentDoc: map[string]interface{}{
					"a": map[string]interface{}{
						"b": 6,
					},
					"c": map[string]interface{}{
						"b": 7,
					},
				},
				savedDoc: map[string]interface{}{
					"a": map[string]interface{}{
						"b": 5,
					},
					"c": map[string]interface{}{
						"b": 7,
					},
				},
				lockedPaths: []string{"$..[?(@.b < 6)]"},
			},
			want: map[string]interface{}{
				"a": map[string]interface{}{
					"b": 5,
				},
				"c": map[string]interface{}{
					"b": 7,
				},
			},
		},
		{
			Name: "filter json expression: path doesn't exist in current",
			args: args{
				currentDoc: map[string]interface{}{
					"a": map[string]interface{}{},
					"c": map[string]interface{}{
						"b": 7,
					},
				},
				savedDoc: map[string]interface{}{
					"a": map[string]interface{}{
						"b": 5,
					},
					"c": map[string]interface{}{
						"b": 7,
					},
				},
				lockedPaths: []string{"$..[?(@.b < 6)]"},
			},
			want: map[string]interface{}{
				"a": map[string]interface{}{
					"b": 5,
				},
				"c": map[string]interface{}{
					"b": 7,
				},
			},
		},
		{
			Name: "filter json expression: path doesn't exist in saved",
			args: args{
				currentDoc: map[string]interface{}{
					"a": map[string]interface{}{
						"b": 5,
					},
					"c": map[string]interface{}{
						"b": 7,
					},
				},
				savedDoc: map[string]interface{}{
					"a": map[string]interface{}{},
					"c": map[string]interface{}{
						"b": 7,
					},
				},
				lockedPaths: []string{"$..[?(@.b < 6)]"},
			},
			want: map[string]interface{}{
				"a": map[string]interface{}{},
				"c": map[string]interface{}{
					"b": 7,
				},
			},
		},
		{
			Name: "array value locked",
			args: args{
				currentDoc: map[string]interface{}{
					"a": []int{2, 2, 2},
					"c": map[string]interface{}{
						"b": 7,
					},
				},
				savedDoc: map[string]interface{}{
					"a": []int{1, 2},
					"c": map[string]interface{}{
						"b": 7,
					},
				},
				lockedPaths: []string{"a[0]"},
			},
			want: map[string]interface{}{
				"a": []int{1, 2, 2},
				"c": map[string]interface{}{
					"b": 7,
				},
			},
		},
		{
			Name: "array key locked",
			args: args{
				currentDoc: map[string]interface{}{
					"a": []int{2, 2, 2},
					"c": map[string]interface{}{
						"b": 8,
					},
				},
				savedDoc: map[string]interface{}{
					"a": []int{1, 2},
					"c": map[string]interface{}{
						"b": 7,
					},
				},
				lockedPaths: []string{".a"},
			},
			want: map[string]interface{}{
				"a": []int{1, 2},
				"c": map[string]interface{}{
					"b": 8,
				},
			},
		},
		{
			Name: "array slice locked",
			args: args{
				currentDoc: map[string]interface{}{
					"a": []int{2, 2, 2},
					"c": map[string]interface{}{
						"b": 7,
					},
				},
				savedDoc: map[string]interface{}{
					"a": []int{1, 2},
					"c": map[string]interface{}{
						"b": 7,
					},
				},
				lockedPaths: []string{"a[0:3]"},
			},
			want: map[string]interface{}{
				"a": []int{1, 2},
				"c": map[string]interface{}{
					"b": 7,
				},
			},
		},
		{
			Name: "particular key in array locked",
			args: args{
				currentDoc: map[string]interface{}{
					"a": []map[string]interface{}{
						{
							"k":  "v00",
							"k1": "v3",
						},
						{
							"k":  "v22",
							"k1": "v4",
						},
					},
					"c": map[string]interface{}{
						"b": 8,
					},
				},
				savedDoc: map[string]interface{}{
					"a": []map[string]interface{}{
						{
							"k":  "v0",
							"k1": "v1",
						},
						{
							"k":  "v2",
							"k1": "v4",
						},
					},
					"c": map[string]interface{}{
						"b": 7,
					},
				},
				lockedPaths: []string{".a[*].k"},
			},
			want: map[string]interface{}{
				"a": []map[string]interface{}{
					{
						"k":  "v0",
						"k1": "v3",
					},
					{
						"k":  "v2",
						"k1": "v4",
					},
				},
				"c": map[string]interface{}{
					"b": 8,
				},
			},
		},
		{
			Name: "slice is locked and field removed from array",
			args: args{
				currentDoc: map[string]interface{}{
					"a": []int{0, 1},
					"c": map[string]interface{}{
						"b": 8,
					},
				},
				savedDoc: map[string]interface{}{
					"a": []int{0, 1, 2, 3, 4},
					"c": map[string]interface{}{
						"b": 8,
					},
				},
				lockedPaths: []string{"a[0:5]"},
			},
			want: map[string]interface{}{
				"a": []int{0, 1, 2, 3, 4},
				"c": map[string]interface{}{
					"b": 8,
				},
			},
		},
		{
			Name: "slice is locked and field added in array",
			args: args{
				currentDoc: map[string]interface{}{
					"a": []int{0, 1, 2},
					"c": map[string]interface{}{
						"b": 4,
					},
				},
				savedDoc: map[string]interface{}{
					"a": []int{0, 1},
					"c": map[string]interface{}{
						"b": 8,
					},
				},
				lockedPaths: []string{"a[0:3]"},
			},
			want: map[string]interface{}{
				"a": []int{0, 1},
				"c": map[string]interface{}{
					"b": 4,
				},
			},
		},
	}

	for _, test := range tests {
		t.Run(test.Name, func(t *testing.T) {
			current, saved, want := getConfigString(test.args.currentDoc, test.args.savedDoc, test.want)
			got, err := lockConfigService.syncChanges(current, saved, test.args.lockedPaths)
			if err != nil {
				return
			}
			if !reflect.DeepEqual(got, want) {
				t.Errorf("syncChanges() err: ")
				return
			}
		})
	}

}

func getConfigString(current, saved, want map[string]interface{}) (string, string, string) {
	currentConfig, _ := json.Marshal(current)
	savedConfig, _ := json.Marshal(saved)
	wantConfig, _ := json.Marshal(want)
	return string(currentConfig), string(savedConfig), string(wantConfig)
}
