/*
 * Copyright (c) 2024. Devtron Inc.
 */

package lockConfiguration

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/caarlos0/env/v6"
	"github.com/devtron-labs/devtron/internal/util/configUtil"
	"github.com/devtron-labs/devtron/pkg/auth/user"
	bean2 "github.com/devtron-labs/devtron/pkg/globalPolicy/bean"
	bean4 "github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/bean"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/common/alpha1"
	policyUtil "github.com/devtron-labs/devtron/pkg/policyGovernance/common/alpha1/util"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/lockConfiguration/adapter/service"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/lockConfiguration/bean"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/lockConfiguration/repository"
	util3 "github.com/devtron-labs/devtron/pkg/policyGovernance/lockConfiguration/util"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/model"
	"github.com/devtron-labs/devtron/pkg/resourceQualifiers"
	util2 "github.com/devtron-labs/devtron/util"
	jsonpatch "github.com/evanphx/json-patch/v5"
	"github.com/ohler55/ojg/jp"
	"github.com/ohler55/ojg/oj"
	"go.uber.org/zap"
	"reflect"
	"slices"
	"sort"
	"strings"
)

type LockConfigurationService interface {
	GetLockConfiguration(appId int, envId int) (*bean.LockConfigResponse, error)

	// Deprecated: This method is deprecated in view of CommonPolicyActionService.CreateGlobalPolicy
	SaveLockConfiguration(ctx *util2.RequestCtx, req *bean.LockConfigRequest) error

	// Deprecated: This method is deprecated in view of CommonPolicyActionService.DeleteGlobalPolicy
	DeleteActiveLockConfiguration(ctx *util2.RequestCtx) error

	HandleLockConfiguration(currentConfig, savedConfig, token string, userId, appId, envId int) (*bean.LockValidateErrorResponse, error)

	//RevertChangesInLockedFields make lock keys values equal to already saved value
	RevertChangesInLockedFields(currentConfig, savedConfig, token string, userId, appId, envId int) (string, error)
}

type LockConfigurationServiceConfig struct {
	ArrayDiffMemoization bool `env:"ARRAY_DIFF_MEMOIZATION" envDefault:"false"`
}

type LockConfigurationServiceImpl struct {
	logger                         *zap.SugaredLogger
	lockConfigurationRepository    repository.LockConfigurationRepository
	userService                    user.UserService
	mergeUtil                      configUtil.MergeUtil
	lockConfigurationServiceConfig *LockConfigurationServiceConfig
	qualifierMappingGroupService   resourceQualifiers.CriteriaQualifierMappingService
	qualifierMappingService        resourceQualifiers.QualifierMappingService
	commonPolicyActionService      alpha1.CommonPolicyActionsService
}

func NewLockConfigurationServiceImpl(logger *zap.SugaredLogger,
	lockConfigurationRepository repository.LockConfigurationRepository,
	userService user.UserService, commonPolicyActionService alpha1.CommonPolicyActionsService,
	mergeUtil configUtil.MergeUtil, qualifierMappingGroupService resourceQualifiers.CriteriaQualifierMappingService,
	qualifierMappingService resourceQualifiers.QualifierMappingService,
	lockConfigurationDataAdaptor service.LockConfigurationDataAdaptor,
	commonPolicyEventHandler alpha1.CustomPolicyDataHandler) *LockConfigurationServiceImpl {
	config := &LockConfigurationServiceConfig{}
	err := env.Parse(config)
	if err != nil {
		logger.Warnw("error in initialising UserTerminalSessionConfig but continuing with rest of initialisation", err)
	}
	logger.Infow("env var ", "ARRAY_DIFF_MEMOIZATION", config.ArrayDiffMemoization)
	lockConfigService := &LockConfigurationServiceImpl{
		logger:                         logger,
		lockConfigurationRepository:    lockConfigurationRepository,
		userService:                    userService,
		mergeUtil:                      mergeUtil,
		lockConfigurationServiceConfig: config,
		qualifierMappingGroupService:   qualifierMappingGroupService,
		commonPolicyActionService:      commonPolicyActionService,
		qualifierMappingService:        qualifierMappingService,
	}
	commonPolicyEventHandler.RegisterPreOpGlobalPolicyHandler(bean2.LOCK_CONFIGURATION, lockConfigurationDataAdaptor.HandleLockConfigPreOp)
	commonPolicyEventHandler.RegisterPolicyBeanToDtoConverter(bean2.LOCK_CONFIGURATION, lockConfigurationDataAdaptor.LockConfigBeanToDtoConverter)
	commonPolicyEventHandler.RegisterPolicyDtoToBeanConverter(bean2.LOCK_CONFIGURATION, lockConfigurationDataAdaptor.LockConfigDtoToBeanConverter)
	commonPolicyEventHandler.RegisterPolicyDefinitionTypeFunc(bean2.LOCK_CONFIGURATION, lockConfigurationDataAdaptor.GetLockConfigPolicyDefinitionType)
	commonPolicyEventHandler.RegisterOldDataMigrationFunc(bean2.LOCK_CONFIGURATION, lockConfigurationDataAdaptor.HandleOldData)
	return lockConfigService
}

func (impl *LockConfigurationServiceImpl) GetLockConfiguration(appId int, envId int) (*bean.LockConfigResponse, error) {
	impl.logger.Infow("Getting active lock configuration", "appId", appId, "envId", envId)
	//handle case for change of base-deployment-template Id
	if envId == bean4.BASE_CONFIG_ENV_ID {
		envId = resourceQualifiers.BaseDeploymentTemplateInt
	}
	//prepare scope
	scopes, err := impl.commonPolicyActionService.PrepareScopeForPolicy(appId, envId)
	if err != nil {
		impl.logger.Errorw("Error in preparing scopes for getting lock configuration", "err", err)
		return nil, err
	}

	//fetch qualifier mappings and group
	qualifierMappingGroups, err := impl.qualifierMappingGroupService.GetCriteriaRQMForScopesAndResource(policyUtil.GetApplyPolicyTypeForPathVariablePolicyType(model.LockConfiguration), scopes, nil)
	if err != nil {
		impl.logger.Errorw("error in getting qualifier mapping groups", "err", err)
		return nil, err
	}
	if len(qualifierMappingGroups) > 0 {
		//filter based on kind matching
		qualifiedMappingGroups := make([]*resourceQualifiers.CriteriaQualifierMappingModel, 0)

		allQualifierPolicyIds := make([]int, 0)
		for _, qualifierMappingGroup := range qualifierMappingGroups {
			isQualified := true
			IdentifierKindIds := make([]int, 0)
			for _, qualifierMapping := range qualifierMappingGroup.QualifierMappings {
				IdentifierKindIds = append(IdentifierKindIds, qualifierMapping.IdentifierKey)
			}

			//case when one of the kind is missed
			for _, identifierKind := range qualifierMappingGroup.JsonDataValue.IdentifierKinds {
				if !slices.Contains(IdentifierKindIds, identifierKind) {
					isQualified = false
				}
			}

			if isQualified {
				qualifiedMappingGroups = append(qualifiedMappingGroups, qualifierMappingGroup)
				for _, profile := range qualifierMappingGroup.JsonDataValue.Profiles {
					if profile.Type == string(model.LockConfiguration) {
						allQualifierPolicyIds = append(allQualifierPolicyIds, profile.Identifiers...)
					}
				}
			}
		}

		if len(allQualifierPolicyIds) == 0 {
			impl.logger.Infow("no qualifier policy found")
			return nil, nil
		}
		//store final groups and merge all json paths
		qualifiedPolicyDetails, err := impl.commonPolicyActionService.GetPolicyDetailByIds(allQualifierPolicyIds)
		if err != nil {
			impl.logger.Errorw("Error in getting policyDetail by Ids")
			return nil, err
		}
		allJsonPaths := make([]string, 0)
		for _, tmpPolicy := range qualifiedPolicyDetails {
			rules := tmpPolicy.Spec.Definition["rules"]
			ruleValues, ok := rules.([]bean.LockConfig)

			if !ok {
				return nil, errors.New("invalid rules definition")
			}
			for _, lockConfig := range ruleValues {
				if lockConfig.Type != "" {
					allJsonPaths = append(allJsonPaths, lockConfig.Paths.Deny...)
				}
			}
		}

		if len(allJsonPaths) == 0 {
			return nil, nil
		} else {
			//remove duplicates
			allJsonPathsDeDup := make([]string, 0)
			for _, jsonPath := range allJsonPaths {
				if !slices.Contains(allJsonPathsDeDup, jsonPath) {
					allJsonPathsDeDup = append(allJsonPathsDeDup, jsonPath)
				}
			}
			return &bean.LockConfigResponse{
				ContainAllowedPaths: false,
				Paths:               allJsonPathsDeDup,
			}, nil
		}
	}

	return &bean.LockConfigResponse{}, nil
}

func (impl *LockConfigurationServiceImpl) SaveLockConfiguration(ctx *util2.RequestCtx, req *bean.LockConfigRequest) error {

	policyDetail, err := impl.commonPolicyActionService.GetPolicyDetail(ctx, util3.TmpGlobalPolicyName, model.LockConfiguration)
	if err != nil {
		impl.logger.Errorw("Error in getting policy detail", "policyName", util3.TmpGlobalPolicyName, "err", err)
		return err
	}

	if policyDetail == nil {
		//create a new policy if not exists with name tmpGlobalPolicyName
		err := impl.commonPolicyActionService.CreateGlobalPolicy(ctx, util3.GetNewPolicyForGivenPaths(ctx, req.Config))
		if err != nil {
			impl.logger.Errorw("Error in creating global policy for migration", "payload", req, "err", err)
			return err
		}
	} else {
		//if exist then update that policy with name tmpGlobalPolicyName
		err := impl.commonPolicyActionService.UpdateGlobalPolicy(ctx, util3.TmpGlobalPolicyName, util3.GetNewPolicyForGivenPaths(ctx, req.Config))
		if err != nil {
			impl.logger.Errorw("Error in updating global policy for migration", "payload", req, "err", err)
			return err
		}
	}

	//apply that policy globally
	policy, err := util3.GetDummyGlobalAppliedRequest()
	if err != nil {
		impl.logger.Errorw("error in creating dummy policy data", "payload", req, "err", err)
		return err
	}
	err = impl.commonPolicyActionService.ApplyPolicyToIdentifiersAlpha1(ctx, policy)
	if err != nil {
		impl.logger.Errorw("Error in applying global policy", "err", err)
		return err
	}

	//delete old policy
	err = impl.lockConfigurationRepository.DeleteActiveLockConfigs(int(ctx.GetUserId()))
	if err != nil {
		impl.logger.Errorw("Error in deleting old policy", "policyName", util3.TmpGlobalPolicyName, "err", err)
		return err
	}
	return err
}

func (impl *LockConfigurationServiceImpl) DeleteActiveLockConfiguration(ctx *util2.RequestCtx) error {
	//delete old data
	err := impl.lockConfigurationRepository.DeleteActiveLockConfigs(int(ctx.GetUserId()))
	if err != nil {
		impl.logger.Errorw("Error in deleting old policy", "policyName", util3.TmpGlobalPolicyName, "err", err)
		return err
	}

	// simply delete global policy with name tmpGlobalPolicyName
	err = impl.commonPolicyActionService.DeletePolicy(ctx, util3.TmpGlobalPolicyName, model.LockConfiguration)
	if err != nil {
		impl.logger.Errorw("Error in deleting policy", "policyName", util3.TmpGlobalPolicyName, "err", err)
		return err
	}
	return nil
}

func (impl *LockConfigurationServiceImpl) HandleLockConfiguration(currentConfig, savedConfig, token string, userId, appId, envId int) (*bean.LockValidateErrorResponse, error) {
	//handle migration
	err := impl.handleOldData(userId)

	if err != nil {
		//TODO: return error ??
		impl.logger.Errorw("Error in handling old data", "err", err)
	}

	isSuperAdmin, _, err := impl.userService.IsSuperAdmin(int32(userId), token)
	if err != nil || isSuperAdmin {
		return nil, err
	}

	lockConfig, err := impl.GetLockConfiguration(appId, envId)
	if err != nil {
		impl.logger.Errorw("error in getting active lock configuration", "err", err)
		return nil, err
	}
	if lockConfig == nil || len(lockConfig.Paths) == 0 {
		return nil, nil
	}

	var savedConfigMap map[string]interface{}
	var currentConfigMap map[string]interface{}

	err = json.Unmarshal([]byte(savedConfig), &savedConfigMap)
	if err != nil {
		impl.logger.Errorw("Error in umMarshal data", "err", err, "savedConfig", savedConfig)
		return nil, err
	}
	err = json.Unmarshal([]byte(currentConfig), &currentConfigMap)
	if err != nil {
		impl.logger.Errorw("Error in umMarshal data", "err", err, "currentConfig", currentConfig)
		return nil, err
	}
	var lockedConfigChangedPaths []string
	if lockConfig.ContainAllowedPaths {
		// Will add in v2 of this feature
	} else {
		lockedConfigChangedPaths, err = checkLockedChanges(currentConfig, savedConfig, lockConfig.Paths)
		if err != nil {
			impl.logger.Errorw("Error in checking locked changes", "lockConfig.Paths", lockConfig.Paths, "err", err)
			return nil, err
		}
	}
	if lockedConfigChangedPaths != nil && len(lockedConfigChangedPaths) > 0 {
		lockConfigErrorResponse := bean.GetLockConfigErrorResponse(lockedConfigChangedPaths)
		return lockConfigErrorResponse, nil
	}
	return nil, nil
}

func (impl *LockConfigurationServiceImpl) handleOldData(userId int) error {
	ctxReq := context.WithValue(context.Background(), "userId", int32(userId))
	ctx := util2.NewRequestCtx(ctxReq)
	return impl.commonPolicyActionService.HandleOldData(ctx, bean2.LOCK_CONFIGURATION)
}

// Here we are checking whether the values at lockedPath in currentConfig & savedConfig are same or not
func checkLockedChanges(currentConfig, savedConfig string, lockedConfigJsonPaths []string) ([]string, error) {
	currentConfigParsed, err := oj.ParseString(currentConfig)
	if err != nil {
		return nil, err
	}
	savedConfigParsed, err := oj.ParseString(savedConfig)
	if err != nil {
		return nil, err
	}
	jsonPaths := make([]string, 0)
	for _, lockedConfigJsonPath := range lockedConfigJsonPaths {
		parsedLockedConfigJsonPath, err := jp.ParseString(lockedConfigJsonPath)
		if err != nil {
			return nil, err
		}
		currentConfigValue := parsedLockedConfigJsonPath.Get(currentConfigParsed)
		savedConfigValue := parsedLockedConfigJsonPath.Get(savedConfigParsed)
		// Sort slices before comparison
		sort.Slice(currentConfigValue, func(i, j int) bool {
			return fmt.Sprintf("%v", currentConfigValue[i]) < fmt.Sprintf("%v", currentConfigValue[j])
		})
		sort.Slice(savedConfigValue, func(i, j int) bool {
			return fmt.Sprintf("%v", savedConfigValue[i]) < fmt.Sprintf("%v", savedConfigValue[j])
		})
		if !reflect.DeepEqual(currentConfigValue, savedConfigValue) {
			jsonPaths = append(jsonPaths, lockedConfigJsonPath)
		}
	}
	return jsonPaths, nil
}

func (impl *LockConfigurationServiceImpl) RevertChangesInLockedFields(currentConfig, savedConfig, token string, userId, appId, envId int) (string, error) {
	err := impl.handleOldData(userId)

	if err != nil {
		//TODO: return error ??
		impl.logger.Errorw("Error in handling old data", "err", err)
	}

	isSuperAdmin, _, err := impl.userService.IsSuperAdmin(int32(userId), token)
	if err != nil || isSuperAdmin {
		return currentConfig, err
	}

	lockConfig, err := impl.GetLockConfiguration(appId, envId)
	if err != nil {
		impl.logger.Errorw("error in getting active lock configuration", "err", err)
		return "", err
	}
	if lockConfig == nil || len(lockConfig.Paths) == 0 {
		return "", nil
	}

	revertedConfig, err := impl.syncChanges(currentConfig, savedConfig, lockConfig.Paths)
	if err != nil {
		impl.logger.Errorw("Error in checking locked changes", "lockConfig.Paths", lockConfig.Paths, "err", err)
		return "", err
	}

	return revertedConfig, nil
}

func (impl *LockConfigurationServiceImpl) syncChanges(currentConfig, savedConfig string, lockedConfigJsonPaths []string) (string, error) {

	var savedDocument map[string]interface{}
	err := json.Unmarshal([]byte(savedConfig), &savedDocument)
	if err != nil {
		impl.logger.Errorw("error in marshalling saved config", "err", err)
		return "", err
	}

	var currentDocument map[string]interface{}
	err = json.Unmarshal([]byte(currentConfig), &currentDocument)
	if err != nil {
		impl.logger.Errorw("error in marshalling saved config", "err", err)
		return "", err
	}

	savedConfigParsed, err := oj.ParseString(savedConfig)
	if err != nil {
		return "", err
	}

	currentConfigParsed, err := oj.ParseString(currentConfig)
	if err != nil {
		return "", err
	}

	for _, lockedConfigJsonPath := range lockedConfigJsonPaths {

		parsedLockedConfigJsonPath, err := jp.ParseString(lockedConfigJsonPath)
		if err != nil {
			return "", err
		}

		savedNormalizedLockedPaths := parsedLockedConfigJsonPath.Locate(savedDocument, -1)
		currentNormalizedLockedPaths := parsedLockedConfigJsonPath.Locate(currentDocument, -1)

		// reversing slice because in case if slice is locked (jsonExpression = a[0:5])
		//and item is added in slice which is not present in saved config, we will remove item from end to ensure that items are not moved,
		//we will always remove element from the last otherwise remove operation will cause shift in values
		slices.Reverse(currentNormalizedLockedPaths)

		for _, currentPath := range currentNormalizedLockedPaths {
			savedValue := currentPath.Get(savedConfigParsed)
			currentValue := currentPath.Get(currentConfigParsed)

			if len(savedValue) == 0 && len(currentValue) != 0 {
				currentConfigParsed, err = currentPath.Remove(currentConfigParsed)
				if err != nil {
					return "", err
				}
			} else if !reflect.DeepEqual(savedValue, currentValue) {
				err = currentPath.Set(currentConfigParsed, savedValue[0])
				if err != nil {
					return "", err
				}
			}
		}

		for _, savedPath := range savedNormalizedLockedPaths {
			savedValue := savedPath.Get(savedConfigParsed)
			currentValue := savedPath.Get(currentConfigParsed)
			if !reflect.DeepEqual(savedValue, currentValue) {
				err = savedPath.Set(currentConfigParsed, savedValue[0])
				if err != nil {
					if strings.Contains(err.Error(), "can not follow out of bounds array index at") {
						parent := savedPath[0 : len(savedPath)-1]
						currentConfigParsed, err = parent.Modify(currentConfigParsed, func(element any) (altered any, changed bool) {
							if elementArr, ok := element.([]any); ok {
								elementArr = append(elementArr, savedValue[0])
								return elementArr, true
							}
							return nil, false
						})
						if err != nil {
							return "", err
						}
					}
				}
			}
		}
	}

	currentConfigParsedJSON := oj.JSON(currentConfigParsed)

	// calculating revert patch and applying it again to original config in order to preserve original order
	revertPatch, err := jsonpatch.CreateMergePatch([]byte(currentConfig), []byte(currentConfigParsedJSON))
	if err != nil {
		impl.logger.Errorw("error in calculating revert patch", "err", err)
		return "", err
	}

	currentConfigWithLockChangesReverted, err := impl.mergeUtil.JsonPatchV2([]byte(currentConfig), revertPatch)
	if err != nil {
		impl.logger.Errorw("error in patching reverted lock config on current config", "err", err)
		return "", err
	}

	return string(currentConfigWithLockChangesReverted), nil
}
