/*
 * Copyright (c) 2020-2024. Devtron Inc.
 */

package pipeline

import (
	"github.com/devtron-labs/devtron/enterprise/pkg/globalTag"
	"github.com/devtron-labs/devtron/pkg/bean"
	"github.com/devtron-labs/devtron/pkg/pipeline"
)

type CiCdPipelineOrchestratorEnterpriseImpl struct {
	globalTagService globalTag.GlobalTagService
	*pipeline.CiCdPipelineOrchestratorImpl
}

func NewCiCdPipelineOrchestratorEnterpriseImpl(
	ciCdPipelineOrchestratorImpl *pipeline.CiCdPipelineOrchestratorImpl,
	globalTagService globalTag.GlobalTagService) *CiCdPipelineOrchestratorEnterpriseImpl {
	return &CiCdPipelineOrchestratorEnterpriseImpl{
		CiCdPipelineOrchestratorImpl: ciCdPipelineOrchestratorImpl,
		globalTagService:             globalTagService,
	}
}

func (impl *CiCdPipelineOrchestratorEnterpriseImpl) CreateApp(createRequest *bean.CreateAppDTO) (*bean.CreateAppDTO, error) {
	// validate mandatory labels against project
	var appLabels []*bean.Label
	for _, label := range createRequest.AppLabels {
		appLabels = append(appLabels, &bean.Label{
			Key:       label.Key,
			Value:     label.Value,
			Propagate: label.Propagate,
		})
	}
	err := impl.globalTagService.ValidateMandatoryLabelsForProject(createRequest.TeamId, appLabels)
	if err != nil {
		return nil, err
	}

	// call forward
	return impl.CiCdPipelineOrchestratorImpl.CreateApp(createRequest)
}
