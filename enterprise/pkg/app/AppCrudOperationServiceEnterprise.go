/*
 * Copyright (c) 2020-2024. Devtron Inc.
 */

package app

import (
	"github.com/devtron-labs/devtron/enterprise/pkg/globalTag"
	"github.com/devtron-labs/devtron/internal/sql/repository/app"
	app2 "github.com/devtron-labs/devtron/pkg/app"
	"github.com/devtron-labs/devtron/pkg/bean"
	"go.uber.org/zap"
)

type AppCrudOperationServiceEnterpriseImpl struct {
	logger           *zap.SugaredLogger
	globalTagService globalTag.GlobalTagService
	appRepository    app.AppRepository
	*app2.AppCrudOperationServiceImpl
}

func NewAppCrudOperationServiceEnterpriseImpl(
	appCrudOperationServiceImpl *app2.AppCrudOperationServiceImpl,
	logger *zap.SugaredLogger, appRepository app.AppRepository,
	globalTagService globalTag.GlobalTagService,
) *AppCrudOperationServiceEnterpriseImpl {
	return &AppCrudOperationServiceEnterpriseImpl{
		AppCrudOperationServiceImpl: appCrudOperationServiceImpl,
		logger:                      logger,
		globalTagService:            globalTagService,
		appRepository:               appRepository,
	}
}

func (impl *AppCrudOperationServiceEnterpriseImpl) UpdateApp(request *bean.CreateAppDTO) (*bean.CreateAppDTO, error) {
	// validate mandatory labels against project
	// if project is changed, then no need to validate mandatory tags against new project
	app, err := impl.appRepository.FindById(request.Id)
	if err != nil {
		impl.logger.Errorw("error in fetching app", "error", err)
		return nil, err
	}
	teamId := request.TeamId
	if teamId == 0 {
		teamId = app.TeamId
	}
	if app.TeamId == teamId {
		var appLabels []*bean.Label
		for _, label := range request.AppLabels {
			appLabels = append(appLabels, &bean.Label{
				Key:       label.Key,
				Value:     label.Value,
				Propagate: label.Propagate,
			})
		}
		err := impl.globalTagService.ValidateMandatoryLabelsForProject(teamId, appLabels)
		if err != nil {
			return nil, err
		}
	}
	// call forward
	return impl.AppCrudOperationServiceImpl.UpdateApp(request)
}
