/*
 * Copyright (c) 2024. Devtron Inc.
 */

package globalTag

import "github.com/devtron-labs/devtron/pkg/valueConstraint/bean"

type GlobalTagDto struct {
	Id                     int                      `json:"id,notnull"`
	Key                    string                   `json:"key,notnull"`
	Description            string                   `json:"description,notnull"`
	MandatoryProjectIdsCsv string                   `json:"mandatoryProjectIdsCsv"`
	Propagate              bool                     `json:"propagate"`
	DeploymentPolicy       DeploymentPolicy         `json:"deploymentPolicy,notnull"`
	ValueConstraint        *bean.ValueConstraintDto `json:"valueConstraint"`
	CreatedOnInMs          int64                    `json:"createdOnInMs,notnull"`
	UpdatedOnInMs          int64                    `json:"updatedOnInMs"`
}

type GlobalTagDtoForProject struct {
	Key              string                   `json:"key,notnull"`
	Description      string                   `json:"description,notnull"`
	IsMandatory      bool                     `json:"isMandatory,notnull"`
	Propagate        bool                     `json:"propagate"`
	DeploymentPolicy DeploymentPolicy         `json:"deploymentPolicy,notnull"`
	ValueConstraint  *bean.ValueConstraintDto `json:"valueConstraint"`
}

type CreateGlobalTagsRequest struct {
	Tags []*CreateGlobalTagDto `json:"tags,notnull" validate:"dive"`
}

type DeploymentPolicy string

type CreateGlobalTagDto struct {
	Key                    string                   `json:"key,notnull" validate:"required,min=1"`
	Description            string                   `json:"description"`
	MandatoryProjectIdsCsv string                   `json:"mandatoryProjectIdsCsv"`
	Propagate              bool                     `json:"propagate"`
	DeploymentPolicy       DeploymentPolicy         `json:"deploymentPolicy" validate:"oneof=allow block block-prod block-non-prod"`
	ValueConstraint        *bean.ValueConstraintDto `json:"valueConstraint"`
}

type DeleteGlobalTagsRequest struct {
	Ids []int `json:"ids,notnull"`
}

type UpdateGlobalTagsRequest struct {
	Tags []*UpdateGlobalTagDto `json:"tags,notnull" validate:"dive"`
}

type UpdateGlobalTagDto struct {
	Id int `json:"id,notnull"`
	CreateGlobalTagDto
}
