/*
 * Copyright (c) 2020-2024. Devtron Inc.
 */

package globalTag

import (
	"fmt"
	"github.com/devtron-labs/devtron/pkg/bean"
	"github.com/devtron-labs/devtron/pkg/sql"
	"github.com/devtron-labs/devtron/pkg/valueConstraint"
	valueConstraintBean "github.com/devtron-labs/devtron/pkg/valueConstraint/bean"
	"github.com/go-pg/pg"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"k8s.io/apimachinery/pkg/util/validation"
	"strconv"
	"strings"
	"time"
)

type GlobalTagService interface {
	GetAllActiveTags() ([]*GlobalTagDto, error)
	GetActiveTagById(tagId int) (*GlobalTagDto, error)
	GetAllActiveTagsForProject(projectId int) ([]*GlobalTagDtoForProject, error)
	CreateTags(request *CreateGlobalTagsRequest, createdBy int32) error
	UpdateTags(request *UpdateGlobalTagsRequest, updatedBy int32) error
	DeleteTags(request *DeleteGlobalTagsRequest, deletedBy int32) error
	ValidateMandatoryLabelsForProject(projectId int, appLabels []*bean.Label) (err error)
	ValidateTagDeploymentPolicy(projectId int, isProd bool, labels map[string]string) error
	ValidateCreateOrUpdateGlobalTagsRequest(tags []*CreateGlobalTagDto) error
}

type GlobalTagServiceImpl struct {
	logger              *zap.SugaredLogger
	globalTagRepository GlobalTagRepository
	constraintService   valueConstraint.ConstraintService
}

func NewGlobalTagServiceImpl(logger *zap.SugaredLogger, globalTagRepository GlobalTagRepository, constraintService valueConstraint.ConstraintService) *GlobalTagServiceImpl {
	return &GlobalTagServiceImpl{
		logger:              logger,
		globalTagRepository: globalTagRepository,
		constraintService:   constraintService,
	}
}

func (impl GlobalTagServiceImpl) GetAllActiveTags() ([]*GlobalTagDto, error) {
	impl.logger.Info("Getting all active global tags")

	// get from DB
	globalTagsFromDb, err := impl.globalTagRepository.FindAllActive()
	if err != nil && err != pg.ErrNoRows {
		impl.logger.Errorw("error while getting all active global tags from DB", "error", err)
		return nil, err
	}

	// convert to DTO
	globalTags := make([]*GlobalTagDto, 0)
	for _, globalTagFromDb := range globalTagsFromDb {
		globalTag := impl.convertGlobalTagDtoFromDBObject(globalTagFromDb)
		globalTags = append(globalTags, globalTag)
	}

	return globalTags, nil
}

func (impl GlobalTagServiceImpl) GetActiveTagById(tagId int) (*GlobalTagDto, error) {
	impl.logger.Infow("Getting active global tags", "tagId", tagId)

	// get from DB
	globalTagFromDb, err := impl.globalTagRepository.FindActiveById(tagId)
	if err != nil {
		impl.logger.Errorw("error while getting active global tag from DB", "tagId", tagId, "error", err)
		return nil, err
	}

	if globalTagFromDb == nil || globalTagFromDb.Id == 0 {
		errorMsg := fmt.Sprintf("Global tag not found for tagId - %d", tagId)
		return nil, errors.New(errorMsg)
	}

	// convert to DTO
	globalTag := impl.convertGlobalTagDtoFromDBObject(globalTagFromDb)

	return globalTag, nil
}

func (impl GlobalTagServiceImpl) GetAllActiveTagsForProject(projectId int) ([]*GlobalTagDtoForProject, error) {
	impl.logger.Infow("Getting all active global tags", "projectId", projectId)

	// get from DB
	globalTagsFromDb, err := impl.globalTagRepository.FindAllActive()
	if err != nil && err != pg.ErrNoRows {
		impl.logger.Errorw("error while getting all active global tags from DB", "error", err)
		return nil, err
	}

	// convert to DTO
	globalTags := make([]*GlobalTagDtoForProject, 0)
	for _, globalTagFromDb := range globalTagsFromDb {
		isMandatory := CheckIfTagIsMandatoryForProject(globalTagFromDb.MandatoryProjectIdsCsv, projectId)
		var valueConstraint *valueConstraintBean.ValueConstraintDto
		if globalTagFromDb.ValueConstraint != nil {
			valueConstraint = &valueConstraintBean.ValueConstraintDto{
				Choices:          globalTagFromDb.ValueConstraint.Choices,
				BlockCustomValue: globalTagFromDb.ValueConstraint.BlockCustomValue,
			}
		}
		globalTag := &GlobalTagDtoForProject{
			Key:              globalTagFromDb.Key,
			Description:      globalTagFromDb.Description,
			IsMandatory:      isMandatory,
			Propagate:        globalTagFromDb.Propagate,
			ValueConstraint:  valueConstraint,
			DeploymentPolicy: globalTagFromDb.DeploymentPolicy,
		}
		globalTags = append(globalTags, globalTag)
	}

	return globalTags, nil
}

func (impl GlobalTagServiceImpl) ValidateCreateOrUpdateGlobalTagsRequest(tags []*CreateGlobalTagDto) error {
	tagKeysMap := make(map[string]bool)
	//return if no tags
	if len(tags) == 0 {
		return errors.New("Validation error - no tags found in the request")
	}
	// validations
	for _, tag := range tags {
		key := strings.TrimSpace(tag.Key)

		// check if empty key
		if len(key) == 0 {
			return errors.New("Validation error - empty key found in the request")
		}

		// Check if array has same key or not - if same key found - return error
		if _, ok := tagKeysMap[key]; ok {
			errorMsg := fmt.Sprintf("Validation error - Duplicate tag - %s found in request", key)
			impl.logger.Errorw("Validation error while creating global tags. duplicate tag found", "tag", key)
			return errors.New(errorMsg)
		}

		// check kubernetes label key validation logic
		errs := validation.IsQualifiedName(key)
		if len(errs) > 0 {
			errorMsg := fmt.Sprintf("Validation error - tag - %s is not satisfying the label key criteria", key)
			impl.logger.Errorw("error while checking if tag key valid", "errors", errs, "key", key)
			return errors.New(errorMsg)
		}

		// check for special check where key "devtron.ai/*" can not be propagated as labels
		if strings.HasPrefix(key, "devtron.ai/") {
			if tag.Propagate {
				errorMsg := fmt.Sprintf("Validation error - tag - %s should not be propagated as label", key)
				impl.logger.Errorw("error while checking if tag should be propagated", "key", key)
				return errors.New(errorMsg)
			}
		}

		// check for mandatory project ids in comma seperated format
		if len(tag.MandatoryProjectIdsCsv) > 0 {
			mandatoryProjectIds := strings.Split(tag.MandatoryProjectIdsCsv, ",")
			// check for valid integers
			for _, projectId := range mandatoryProjectIds {
				if _, err := strconv.Atoi(projectId); err != nil {
					errorMsg := fmt.Sprintf("Validation error - invalid project id - %s found in mandatory project ids", projectId)
					impl.logger.Errorw("error while checking if mandatory project id valid", "error", err, "projectId", projectId)
					return errors.New(errorMsg)
				}
			}
		}

		// check for suggested tag that deployment policy must be set to allow
		if len(tag.MandatoryProjectIdsCsv) == 0 && tag.DeploymentPolicy != Allow {
			errorMsg := fmt.Sprintf("Validation error - deployment policy must be set to allow for suggested tag - %s", key)
			impl.logger.Errorw("error while checking deployment policy for suggested tag", "key", key)
			return errors.New(errorMsg)
		}

		// set in map
		tagKeysMap[key] = true
	}
	return nil
}

func (impl GlobalTagServiceImpl) CreateTags(request *CreateGlobalTagsRequest, createdBy int32) error {
	impl.logger.Infow("Creating Global tags", "request", request, "createdBy", createdBy)
	var globalTagsToSave []*GlobalTag

	for _, tag := range request.Tags {
		// Check if key exists with active true - if exists - return error
		exists, err := impl.globalTagRepository.CheckKeyExistsForAnyActiveTag(tag.Key)
		if err != nil {
			impl.logger.Errorw("error while checking if tag key exists in DB with active true", "error", err, "key", tag.Key)
			return err
		}
		if exists {
			errorMsg := fmt.Sprintf("Validation error - tag - %s already exists", tag.Key)
			impl.logger.Errorw("Validation error while creating global tags. tag already exists", "tag", tag.Key)
			return errors.New(errorMsg)
		}
	}
	// initiate TX
	dbConnection := impl.globalTagRepository.GetConnection()
	tx, err := dbConnection.Begin()
	if err != nil {
		return err
	}
	// Rollback tx on error.
	defer tx.Rollback()

	valueConstraintsMap, err := impl.validateValueConstraints(request.Tags, createdBy)
	if err != nil {
		return err
	}
	updatedValueConstraints, err := impl.constraintService.UpsertValueConstraint(valueConstraintsMap, valueConstraintBean.GlobalTag, tx)
	if err != nil {
		impl.logger.Errorw("error in upserting variable value constraints", "err", err, "valueConstraintsMap", valueConstraintsMap)
		return err
	}

	for _, tag := range request.Tags {
		// insert in slice to save in DB
		valueConstraintId := 0
		if constraint, ok := updatedValueConstraints[tag.Key]; ok {
			valueConstraintId = constraint.Id
		}
		globalTagsToSave = append(globalTagsToSave, &GlobalTag{
			Key:                    tag.Key,
			MandatoryProjectIdsCsv: tag.MandatoryProjectIdsCsv,
			Description:            tag.Description,
			Propagate:              tag.Propagate,
			DeploymentPolicy:       tag.DeploymentPolicy,
			ValueConstraintId:      valueConstraintId,
			Active:                 true,
			AuditLog:               sql.AuditLog{CreatedOn: time.Now(), CreatedBy: createdBy},
		})
	}

	err = impl.globalTagRepository.Save(globalTagsToSave, tx)
	if err != nil {
		impl.logger.Errorw("error while saving global tags", "error", err)
		return err
	}

	// commit TX
	err = tx.Commit()
	if err != nil {
		return err
	}
	return nil
}

func (impl GlobalTagServiceImpl) validateValueConstraints(tags []*CreateGlobalTagDto, createdBy int32) (map[string]*valueConstraintBean.ValueConstraintDto, error) {
	valueConstraintsMap := make(map[string]*valueConstraintBean.ValueConstraintDto)
	for _, v := range tags {
		if v.ValueConstraint == nil {
			continue
		}
		// validation to check unique choices in value constraint
		if len(v.ValueConstraint.Choices) > 0 {
			choicesMap := make(map[string]bool)
			for _, choice := range v.ValueConstraint.Choices {
				choice = strings.TrimSpace(choice)
				if len(choice) == 0 {
					return nil, errors.New("Validation error - empty choice found in the request")
				}
				if _, ok := choicesMap[choice]; ok {
					errorMsg := fmt.Sprintf("Validation error - Duplicate choice - %s found in request", choice)
					impl.logger.Errorw("Validation error while creating global tags. duplicate choice found", "choice", choice)
					return nil, errors.New(errorMsg)
				}
				choicesMap[choice] = true
			}
		}
		v.ValueConstraint.Id = 0 // setting id to 0 to create new entry
		v.ValueConstraint.UserId = createdBy
		valueConstraintsMap[v.Key] = v.ValueConstraint
	}
	return valueConstraintsMap, nil
}

func (impl GlobalTagServiceImpl) UpdateTags(request *UpdateGlobalTagsRequest, updatedBy int32) error {
	impl.logger.Infow("Updating Global tags", "request", request, "updatedBy", updatedBy)

	tagsForValidation := make([]*CreateGlobalTagDto, 0)
	for _, tag := range request.Tags {
		tagsForValidation = append(tagsForValidation, &CreateGlobalTagDto{
			Key:                    tag.Key,
			Description:            tag.Description,
			MandatoryProjectIdsCsv: tag.MandatoryProjectIdsCsv,
			Propagate:              tag.Propagate,
			DeploymentPolicy:       tag.DeploymentPolicy,
			ValueConstraint:        tag.ValueConstraint,
		})
	}
	// initiate TX
	dbConnection := impl.globalTagRepository.GetConnection()
	tx, err := dbConnection.Begin()
	if err != nil {
		return err
	}
	// Rollback tx on error.
	defer tx.Rollback()

	// iterate -  get from DB and update in DB
	for _, tag := range request.Tags {
		key := strings.TrimSpace(tag.Key)

		tagId := tag.Id
		globalTagFromDb, err := impl.globalTagRepository.FindActiveById(tagId)
		if err != nil {
			impl.logger.Errorw("error while getting active global tag from DB", "error", err, "tagId", tagId)
			return err
		}

		// Check if key exists with active true - if exists - return error
		if globalTagFromDb.Key != key {
			exists, err := impl.globalTagRepository.CheckKeyExistsForAnyActiveTagExcludeTagId(key, tagId)
			if err != nil {
				impl.logger.Errorw("error while checking if tag key exists in DB with active true", "error", err, "key", key)
				return err
			}
			if exists {
				errorMsg := fmt.Sprintf("Validation error - tag - %s already exists", key)
				impl.logger.Errorw("Validation error while updating global tag. tag already exists", "tag", key)
				return errors.New(errorMsg)
			}
		}

		_, err = impl.validateValueConstraints(tagsForValidation, updatedBy)
		if err != nil {
			return err
		}

		err = impl.constraintService.DeleteValueConstraintByIds(tx, []int{globalTagFromDb.ValueConstraintId}, valueConstraintBean.GlobalTag, updatedBy)
		if err != nil {
			impl.logger.Errorw("error in deleting variable value constraints", "err", err, "valueConstraintId", globalTagFromDb.ValueConstraintId)
			return err
		}

		if tag.ValueConstraint != nil {
			tag.ValueConstraint.UserId = updatedBy
			constraintMap, err := impl.constraintService.UpsertValueConstraint(map[string]*valueConstraintBean.ValueConstraintDto{key: tag.ValueConstraint}, valueConstraintBean.GlobalTag, tx)
			if err != nil {
				impl.logger.Errorw("error in upserting variable value constraints", "err", err, "valueConstraintsMap", tag.ValueConstraint)
				return err
			}
			globalTagFromDb.ValueConstraintId = constraintMap[key].Id
		}
		globalTagFromDb.Key = key
		globalTagFromDb.Description = tag.Description
		globalTagFromDb.MandatoryProjectIdsCsv = tag.MandatoryProjectIdsCsv
		globalTagFromDb.DeploymentPolicy = tag.DeploymentPolicy
		globalTagFromDb.Propagate = tag.Propagate
		globalTagFromDb.UpdatedBy = updatedBy
		globalTagFromDb.UpdatedOn = time.Now()

		err = impl.globalTagRepository.Update(globalTagFromDb, tx)
		if err != nil {
			impl.logger.Errorw("error while updating global tag in DB", "error", err, "tagId", tagId)
			return err
		}
	}

	// commit TX
	err = tx.Commit()
	if err != nil {
		return err
	}
	return nil
}

func (impl GlobalTagServiceImpl) DeleteTags(request *DeleteGlobalTagsRequest, deletedBy int32) error {
	impl.logger.Infow("Deleting Global tags", "request", request, "deletedBy", deletedBy)

	// get from DB
	var ids []int
	for _, id := range request.Ids {
		ids = append(ids, id)
	}

	// if no ids found - return
	if len(ids) == 0 {
		return errors.New("Validation error - no ids found in the request")
	}

	globalTagsFromDb, err := impl.globalTagRepository.FindAllActiveByIds(ids)
	if err != nil && err != pg.ErrNoRows {
		impl.logger.Errorw("error while getting active global tags from DB", "error", err, "ids", ids)
		return err
	}

	// initiate TX
	dbConnection := impl.globalTagRepository.GetConnection()
	tx, err := dbConnection.Begin()
	if err != nil {
		return err
	}
	// Rollback tx on error.
	defer tx.Rollback()

	constraintValueIds := make([]int, 0)
	// iterate and mark inactive in DB
	for _, globalTagFromDb := range globalTagsFromDb {
		globalTagFromDb.Active = false
		globalTagFromDb.UpdatedOn = time.Now()
		globalTagFromDb.UpdatedBy = deletedBy
		err = impl.globalTagRepository.Update(globalTagFromDb, tx)
		if err != nil {
			impl.logger.Errorw("error while deleting global tag", "error", err, "id", globalTagFromDb.Id)
			return err
		}

		constraintValueIds = append(constraintValueIds, globalTagFromDb.ValueConstraintId)
	}

	err = impl.constraintService.DeleteValueConstraintByIds(tx, constraintValueIds, valueConstraintBean.GlobalTag, deletedBy)
	if err != nil {
		impl.logger.Errorw("error in deleting variable value constraints", "err", err, "valueConstraintIds", constraintValueIds)
	}

	// commit TX
	err = tx.Commit()
	if err != nil {
		return err
	}
	return nil
}

func (impl GlobalTagServiceImpl) ValidateMandatoryLabelsForProject(projectId int, appLabels []*bean.Label) error {
	impl.logger.Infow("Validating labels", "projectId", projectId, "labels", appLabels)

	tags, err := impl.GetAllActiveTagsForProject(projectId)
	if err != nil {
		return err
	}

	labelMap := make(map[string]*bean.Label)
	for _, label := range appLabels {
		labelMap[label.Key] = label
	}

	err = CheckIfMandatoryLabelsProvided(labelMap, tags)
	if err != nil {
		impl.logger.Errorw("error in validating labels", "error", err)
		return err
	}
	// validation to check if provided label falls in de-cleared choices for tags
	for _, tag := range tags {
		if tag.ValueConstraint.IsCustomValueBlocked() && tag.IsMandatory {
			choices := tag.ValueConstraint.GetChoices()
			// case to handle a state where user has deleted the choices but CustomValueBlocked is still true
			// we get this state when user deletes all given choices from UI, UI sends empty array for choices
			if len(choices) == 0 {
				continue
			}
			label := labelMap[tag.Key]
			if !isValidChoice(label.Value, choices) {
				errorMsg := fmt.Sprintf("Validation error - value for tag - %s is not valid choice", label.Value)
				impl.logger.Errorw("error in validating labels", "error", errorMsg)
				return errors.New(errorMsg)
			}
		}
	}
	return nil
}

func (impl GlobalTagServiceImpl) ValidateTagDeploymentPolicy(projectId int, isProd bool, labels map[string]string) error {
	// check in mandatory tags if deployment policy is blocking the trigger
	tags, err := impl.GetAllActiveTagsForProject(projectId)
	if err != nil {
		return err
	}
	// check deployment policy as per given envType
	for _, tag := range tags {
		if tag.IsMandatory {
			switch tag.DeploymentPolicy {
			case Block:
				if !CheckIfTagIsPresentInLabels(labels, tag) {
					return fmt.Errorf("Deployment blocked due to mandatory tag - %s", tag.Key)
				}
			case BlockProd:
				if isProd && !CheckIfTagIsPresentInLabels(labels, tag) {
					return fmt.Errorf("Deployment blocked due to mandatory tag - %s", tag.Key)
				}
			case BlockNonProd:
				if !isProd && !CheckIfTagIsPresentInLabels(labels, tag) {
					return fmt.Errorf("Deployment blocked due to mandatory tag - %s", tag.Key)
				}
			case Allow:
				// do nothing
			}
		}
	}
	return nil
}

// CheckIfTagIsPresentInLabels check if tag is present in labels and validates if value is present in choices
func CheckIfTagIsPresentInLabels(labels map[string]string, tag *GlobalTagDtoForProject) bool {
	_, found := labels[tag.Key]
	if found {
		//check if label value is present in choices
		if tag.ValueConstraint != nil && tag.ValueConstraint.BlockCustomValue {
			choices := tag.ValueConstraint.Choices
			if isValidChoice(labels[tag.Key], choices) {
				return true
			}
			return false
		}
		return found
	}
	return found
}

func (impl GlobalTagServiceImpl) convertGlobalTagDtoFromDBObject(globalTagFromDb *GlobalTag) *GlobalTagDto {
	var valueConstraint valueConstraintBean.ValueConstraintDto
	if globalTagFromDb.ValueConstraint != nil {
		valueConstraint = valueConstraintBean.ValueConstraintDto{
			Choices:          globalTagFromDb.ValueConstraint.Choices,
			BlockCustomValue: globalTagFromDb.ValueConstraint.BlockCustomValue,
		}
	}
	globalTag := &GlobalTagDto{
		Id:                     globalTagFromDb.Id,
		Key:                    globalTagFromDb.Key,
		Description:            globalTagFromDb.Description,
		MandatoryProjectIdsCsv: globalTagFromDb.MandatoryProjectIdsCsv,
		Propagate:              globalTagFromDb.Propagate,
		DeploymentPolicy:       globalTagFromDb.DeploymentPolicy,
		ValueConstraint:        &valueConstraint,
		CreatedOnInMs:          globalTagFromDb.CreatedOn.UnixMilli(),
	}
	if !globalTagFromDb.UpdatedOn.IsZero() {
		globalTag.UpdatedOnInMs = globalTagFromDb.UpdatedOn.UnixMilli()
	}
	return globalTag
}
