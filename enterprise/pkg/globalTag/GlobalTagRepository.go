/*
 * Copyright (c) 2020-2024. Devtron Inc.
 */

package globalTag

import (
	"github.com/devtron-labs/devtron/pkg/sql"
	valueConstraintRepo "github.com/devtron-labs/devtron/pkg/valueConstraint/repository"
	"github.com/go-pg/pg"
)

const (
	Allow        DeploymentPolicy = "allow"
	Block        DeploymentPolicy = "block"
	BlockProd    DeploymentPolicy = "block-prod"
	BlockNonProd DeploymentPolicy = "block-non-prod"
)

type GlobalTag struct {
	tableName              struct{}         `sql:"global_tag"`
	Id                     int              `sql:"id,pk"`
	Key                    string           `sql:"key, notnull"`
	MandatoryProjectIdsCsv string           `sql:"mandatory_project_ids_csv"`
	Propagate              bool             `sql:"propagate"`
	Description            string           `sql:"description, notnull"`
	Active                 bool             `sql:"active"`
	DeploymentPolicy       DeploymentPolicy `sql:"deployment_policy,notnull" validate:"oneof=allow block block-prod block-non-prod"`
	ValueConstraintId      int              `sql:"value_constraint_id"`
	ValueConstraint        *valueConstraintRepo.ValueConstraint
	sql.AuditLog
}

type GlobalTagRepository interface {
	GetConnection() *pg.DB
	FindAllActive() ([]*GlobalTag, error)
	CheckKeyExistsForAnyActiveTag(key string) (bool, error)
	CheckKeyExistsForAnyActiveTagExcludeTagId(key string, tagId int) (bool, error)
	FindAllActiveByIds(ids []int) ([]*GlobalTag, error)
	FindActiveById(id int) (*GlobalTag, error)
	Save(globalTags []*GlobalTag, tx *pg.Tx) error
	Update(globalTag *GlobalTag, tx *pg.Tx) error
}

type GlobalTagRepositoryImpl struct {
	dbConnection *pg.DB
}

func NewGlobalTagRepositoryImpl(dbConnection *pg.DB) *GlobalTagRepositoryImpl {
	return &GlobalTagRepositoryImpl{dbConnection: dbConnection}
}

func (impl GlobalTagRepositoryImpl) GetConnection() *pg.DB {
	return impl.dbConnection
}

func (impl GlobalTagRepositoryImpl) FindAllActive() ([]*GlobalTag, error) {
	var globalTags []*GlobalTag
	err := impl.dbConnection.Model(&globalTags).
		Column("global_tag.*", "ValueConstraint").
		Where("active IS TRUE").
		Select()
	return globalTags, err
}

func (impl GlobalTagRepositoryImpl) CheckKeyExistsForAnyActiveTag(key string) (bool, error) {
	var globalTag *GlobalTag
	exists, err := impl.dbConnection.Model(globalTag).
		Where("active IS TRUE").
		Where("key = ?", key).
		Exists()
	return exists, err
}

func (impl GlobalTagRepositoryImpl) CheckKeyExistsForAnyActiveTagExcludeTagId(key string, tagId int) (bool, error) {
	var globalTag *GlobalTag
	exists, err := impl.dbConnection.Model(globalTag).
		Where("active IS TRUE").
		Where("key = ?", key).
		Where("id != ?", tagId).
		Exists()
	return exists, err
}

func (impl GlobalTagRepositoryImpl) FindAllActiveByIds(ids []int) ([]*GlobalTag, error) {
	if len(ids) == 0 {
		return []*GlobalTag{}, nil
	}
	var globalTags []*GlobalTag
	err := impl.dbConnection.Model(&globalTags).
		Column("global_tag.*", "ValueConstraint").
		Where("global_tag.id in (?)", pg.In(ids)).
		Where("active IS TRUE").
		Select()
	return globalTags, err
}

func (impl GlobalTagRepositoryImpl) FindActiveById(id int) (*GlobalTag, error) {
	globalTag := &GlobalTag{}
	err := impl.dbConnection.Model(globalTag).
		Column("global_tag.*", "ValueConstraint").
		Where("active IS TRUE").
		Where("global_tag.id = ?", id).
		Select()
	return globalTag, err
}

func (impl GlobalTagRepositoryImpl) Save(globalTags []*GlobalTag, tx *pg.Tx) error {
	return tx.Insert(&globalTags)
}

func (impl GlobalTagRepositoryImpl) Update(globalTag *GlobalTag, tx *pg.Tx) error {
	return tx.Update(globalTag)
}
