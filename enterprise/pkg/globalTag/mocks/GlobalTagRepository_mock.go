// Code generated by mockery v2.15.0. DO NOT EDIT.

package mocks

import (
	"github.com/devtron-labs/devtron/enterprise/pkg/globalTag"
	mock "github.com/stretchr/testify/mock"

	pg "github.com/go-pg/pg"
)

// GlobalTagRepository is an autogenerated mock type for the GlobalTagRepository type
type GlobalTagRepository struct {
	mock.Mock
}

// CheckKeyExistsForAnyActiveTag provides a mock function with given fields: key
func (_m *GlobalTagRepository) CheckKeyExistsForAnyActiveTag(key string) (bool, error) {
	ret := _m.Called(key)

	var r0 bool
	if rf, ok := ret.Get(0).(func(string) bool); ok {
		r0 = rf(key)
	} else {
		r0 = ret.Get(0).(bool)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(key)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindActiveById provides a mock function with given fields: id
func (_m *GlobalTagRepository) FindActiveById(id int) (*globalTag.GlobalTag, error) {
	ret := _m.Called(id)

	var r0 *globalTag.GlobalTag
	if rf, ok := ret.Get(0).(func(int) *globalTag.GlobalTag); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*globalTag.GlobalTag)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAllActive provides a mock function with given fields:
func (_m *GlobalTagRepository) FindAllActive() ([]*globalTag.GlobalTag, error) {
	ret := _m.Called()

	var r0 []*globalTag.GlobalTag
	if rf, ok := ret.Get(0).(func() []*globalTag.GlobalTag); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*globalTag.GlobalTag)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAllActiveByIds provides a mock function with given fields: ids
func (_m *GlobalTagRepository) FindAllActiveByIds(ids []int) ([]*globalTag.GlobalTag, error) {
	ret := _m.Called(ids)

	var r0 []*globalTag.GlobalTag
	if rf, ok := ret.Get(0).(func([]int) []*globalTag.GlobalTag); ok {
		r0 = rf(ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*globalTag.GlobalTag)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetConnection provides a mock function with given fields:
func (_m *GlobalTagRepository) GetConnection() *pg.DB {
	ret := _m.Called()

	var r0 *pg.DB
	if rf, ok := ret.Get(0).(func() *pg.DB); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pg.DB)
		}
	}

	return r0
}

// Save provides a mock function with given fields: globalTags, tx
func (_m *GlobalTagRepository) Save(globalTags []*globalTag.GlobalTag, tx *pg.Tx) error {
	ret := _m.Called(globalTags, tx)

	var r0 error
	if rf, ok := ret.Get(0).(func([]*globalTag.GlobalTag, *pg.Tx) error); ok {
		r0 = rf(globalTags, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Update provides a mock function with given fields: _a0, tx
func (_m *GlobalTagRepository) Update(_a0 *globalTag.GlobalTag, tx *pg.Tx) error {
	ret := _m.Called(_a0, tx)

	var r0 error
	if rf, ok := ret.Get(0).(func(*globalTag.GlobalTag, *pg.Tx) error); ok {
		r0 = rf(_a0, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewGlobalTagRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewGlobalTagRepository creates a new instance of GlobalTagRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewGlobalTagRepository(t mockConstructorTestingTNewGlobalTagRepository) *GlobalTagRepository {
	mock := &GlobalTagRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
