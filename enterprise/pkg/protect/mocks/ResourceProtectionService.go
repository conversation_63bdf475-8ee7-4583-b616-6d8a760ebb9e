// Code generated by mockery v2.14.0. DO NOT EDIT.

package mocks

import (
	protect "github.com/devtron-labs/devtron/enterprise/pkg/protect"
	"github.com/devtron-labs/devtron/enterprise/pkg/protect/bean"
	mock "github.com/stretchr/testify/mock"
)

// ResourceProtectionService is an autogenerated mock type for the ResourceProtectionService type
type ResourceProtectionService struct {
	mock.Mock
}

// ConfigureResourceProtection provides a mock function with given fields: request
func (_m *ResourceProtectionService) ConfigureResourceProtection(request *bean.ResourceProtectModel) error {
	ret := _m.Called(request)

	var r0 error
	if rf, ok := ret.Get(0).(func(*bean.ResourceProtectModel) error); ok {
		r0 = rf(request)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetResourceProtectMetadata provides a mock function with given fields: appId
func (_m *ResourceProtectionService) GetResourceProtectMetadata(appId int) ([]*bean.ResourceProtectModel, error) {
	ret := _m.Called(appId)

	var r0 []*bean.ResourceProtectModel
	if rf, ok := ret.Get(0).(func(int) []*bean.ResourceProtectModel); ok {
		r0 = rf(appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*bean.ResourceProtectModel)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RegisterListener provides a mock function with given fields: listener
func (_m *ResourceProtectionService) RegisterListener(listener protect.ResourceProtectionUpdateListener) {
	_m.Called(listener)
}

// ResourceProtectionEnabled provides a mock function with given fields: appId, envId
func (_m *ResourceProtectionService) ResourceProtectionEnabled(appId int, envId int) bool {
	ret := _m.Called(appId, envId)

	var r0 bool
	if rf, ok := ret.Get(0).(func(int, int) bool); ok {
		r0 = rf(appId, envId)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// ResourceProtectionEnabledForEnv provides a mock function with given fields: envId
func (_m *ResourceProtectionService) ResourceProtectionEnabledForEnv(envId int) map[int]bool {
	ret := _m.Called(envId)

	var r0 map[int]bool
	if rf, ok := ret.Get(0).(func(int) map[int]bool); ok {
		r0 = rf(envId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[int]bool)
		}
	}

	return r0
}

type mockConstructorTestingTNewResourceProtectionService interface {
	mock.TestingT
	Cleanup(func())
}

// NewResourceProtectionService creates a new instance of ResourceProtectionService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewResourceProtectionService(t mockConstructorTestingTNewResourceProtectionService) *ResourceProtectionService {
	mock := &ResourceProtectionService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
