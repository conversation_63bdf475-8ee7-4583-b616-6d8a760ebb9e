/*
 * Copyright (c) 2024. Devtron Inc.
 */

package bean

import (
	approvalConfigCommonBeans "github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/bean"
)

type ProtectionState int

const (
	EnabledProtectionState  ProtectionState = 1
	DisabledProtectionState ProtectionState = 2
	MigratedProtectionState ProtectionState = 3
)

type ResourceType int

const (
	ConfigProtectionResourceType ResourceType = 1
)

type ResourceProtectModel struct {
	AppId           int             `json:"appId" validate:"number,required"`
	EnvId           int             `json:"envId" validate:"number,required"`
	ProtectionState ProtectionState `json:"state" validate:"number,required"`
	// todo: should add userApprovalConfig data here
	ApprovalConfigurations []*approvalConfigCommonBeans.UserApprovalConfigDTO `json:"approvalConfigurations"`
	UserId                 int32                                              `json:"-"`
}
