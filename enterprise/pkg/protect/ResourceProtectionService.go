/*
 * Copyright (c) 2024. Devtron Inc.
 */

package protect

import (
	"context"
	"github.com/devtron-labs/devtron/enterprise/pkg/drafts/adaptors"
	"github.com/devtron-labs/devtron/enterprise/pkg/drafts/repository"
	"github.com/devtron-labs/devtron/enterprise/pkg/protect/bean"
	appRepository "github.com/devtron-labs/devtron/internal/sql/repository/app"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/pkg/auth/user"
	bean2 "github.com/devtron-labs/devtron/pkg/auth/user/bean"
	chartRepoRepository "github.com/devtron-labs/devtron/pkg/chartRepo/repository"
	beans2 "github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/bean"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/read"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/utils"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/common/alpha1"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/common/alpha1/types"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/model"
	"github.com/go-pg/pg"
	"go.uber.org/zap"
)

type ResourceProtectionService interface {
	DiscardDraftInTx(tx *pg.Tx, resourceType beans2.ApprovalFor, appId int, envId int, userId int32) error

	GetResourceProtectMetadataV1(ctx context.Context, appId int, userMetadata *bean2.UserMetadata) ([]*bean.ResourceProtectModel, error)
	GetEnhancedResourceProtectMetadataV2(ctx context.Context, appId int, userMetadata *bean2.UserMetadata) ([]*bean.ResourceProtectModel, error)
	GetResourceProtectMetadataV2(appId int) ([]*bean.ResourceProtectModel, error)
	ResourceProtectionEnabled(resourceKind beans2.ApprovalFor, appId, envId int, discardUnpublishedDraftsOnDisableState, isExpressEdit bool) bool
	ResourceProtectionEnabledForEnvV1(ctx context.Context, envId int, userMetadata *bean2.UserMetadata) map[int]bool
	GetEnhancedResourceProtectionEnabledForEnvV2(ctx context.Context, envId int, userMetadata *bean2.UserMetadata) ([]*bean.ResourceProtectModel, error)
	ResourceProtectionEnabledMap(resourceKinds []beans2.ApprovalFor, appId, envId int) (map[beans2.ApprovalFor]bool, error)
}

type ResourceProtectionServiceImpl struct {
	logger                                  *zap.SugaredLogger
	approvalConfigurationEnforcementService read.ApprovalPolicyReadService
	pipelineRepo                            pipelineConfig.PipelineRepository
	configDraftRepository                   repository.ConfigDraftRepository
	userGroupService                        user.UserGroupService
	chartRepo                               chartRepoRepository.ChartRepository
	appRepo                                 appRepository.AppRepository
	commonPolicyActionsService              alpha1.CommonPolicyActionsService
}

func NewResourceProtectionServiceImpl(logger *zap.SugaredLogger,
	approvalConfigurationEnforcementService read.ApprovalPolicyReadService,
	pipelineRepo pipelineConfig.PipelineRepository,
	configDraftRepository repository.ConfigDraftRepository,
	chartRepo chartRepoRepository.ChartRepository,
	appRepo appRepository.AppRepository,
	userGroupService user.UserGroupService,
	commonPolicyActionsService alpha1.CommonPolicyActionsService,
) *ResourceProtectionServiceImpl {
	return &ResourceProtectionServiceImpl{
		logger:                                  logger,
		approvalConfigurationEnforcementService: approvalConfigurationEnforcementService,
		configDraftRepository:                   configDraftRepository,
		pipelineRepo:                            pipelineRepo,
		userGroupService:                        userGroupService,
		chartRepo:                               chartRepo,
		appRepo:                                 appRepo,
		commonPolicyActionsService:              commonPolicyActionsService,
	}
}

func (impl *ResourceProtectionServiceImpl) DiscardDraft(resourceType beans2.ApprovalFor, appId, envId int, userId int32) error {
	return impl.discardUnPublishedDrafts(resourceType, appId, envId, userId)
}

func (impl *ResourceProtectionServiceImpl) DiscardDraftInTx(tx *pg.Tx, resourceType beans2.ApprovalFor, appId int, envId int, userId int32) error {
	return impl.discardUnPublishedDraftsInTx(tx, resourceType, appId, envId, userId)
}

func (impl *ResourceProtectionServiceImpl) GetResourceProtectMetadataV2(appId int) ([]*bean.ResourceProtectModel, error) {
	pipelineMins, err := impl.pipelineRepo.FindPipelineIdsByAppIds([]int{appId})
	if err != nil {
		impl.logger.Errorw("error in getting the pipelines for the app", "appId", appId, "err", err)
		return nil, err
	}

	baseChartExistsForApps, err := impl.chartRepo.FindLatestChartExistsForAppByAppIds([]int{appId})
	if err != nil {
		impl.logger.Errorw("error in getting the latest chart for the app", "appId", appId, "err", err)
		return nil, err
	}

	scopes := []*beans2.Scope{{AppId: appId, EnvId: -1}}
	for _, pipeline := range pipelineMins {
		scopes = append(scopes, &beans2.Scope{AppId: appId, EnvId: pipeline.EnvironmentId})
	}

	responses, err := impl.getResourceProtectionInfoByScopes(scopes)
	if err != nil {
		return responses, err
	}

	for _, resp := range responses {
		if exists := baseChartExistsForApps[resp.AppId]; !exists {
			configs := make([]*beans2.UserApprovalConfigDTO, 0)
			for _, config := range resp.ApprovalConfigurations {
				if config.Kind != beans2.APPROVAL_FOR_CONFIGURATION_DT {
					configs = append(configs, config)
				}
			}
			resp.ApprovalConfigurations = configs
		}
	}
	return responses, nil
}

func (impl *ResourceProtectionServiceImpl) GetEnhancedResourceProtectMetadataV2(ctx context.Context, appId int, userMetadata *bean2.UserMetadata) ([]*bean.ResourceProtectModel, error) {
	pipelineMins, err := impl.pipelineRepo.FindPipelineIdsByAppIds([]int{appId})
	if err != nil {
		impl.logger.Errorw("error in getting the pipelines for the app", "appId", appId, "err", err)
		return nil, err
	}

	baseChartExistsForApps, err := impl.chartRepo.FindLatestChartExistsForAppByAppIds([]int{appId})
	if err != nil {
		impl.logger.Errorw("error in getting the latest chart for the app", "appId", appId, "err", err)
		return nil, err
	}

	scopes := []*beans2.Scope{{AppId: appId, EnvId: -1}}
	for _, pipeline := range pipelineMins {
		scopes = append(scopes, &beans2.Scope{AppId: appId, EnvId: pipeline.EnvironmentId})
	}

	responses, err := impl.getResourceProtectionInfoByScopes(scopes)
	if err != nil {
		return responses, err
	}

	for _, resp := range responses {
		if exists := baseChartExistsForApps[resp.AppId]; !exists {
			configs := make([]*beans2.UserApprovalConfigDTO, 0)
			for _, config := range resp.ApprovalConfigurations {
				if config.Kind != beans2.APPROVAL_FOR_CONFIGURATION_DT {
					configs = append(configs, config)
				}
			}
			resp.ApprovalConfigurations = configs
		}
	}
	var exceptionUserActionFunc types.ExceptionUserActionFuncType = func(isUserException bool) {
		for _, item := range responses {
			for _, config := range item.ApprovalConfigurations {
				config.IsExceptionUser = isUserException
			}
		}
	}
	_, err = impl.commonPolicyActionsService.PerformExceptionUserActionHookForPolicyType(ctx, exceptionUserActionFunc, model.APPLY_POLICY_APPROVAL_CONFIGURATION, userMetadata)
	if err != nil {
		impl.logger.Errorw("error in embedding exception user flag in response configs", "err", err)
		return nil, err
	}
	return responses, nil
}

func (impl *ResourceProtectionServiceImpl) getResourceProtectionInfoByScopes(scopes []*beans2.Scope) ([]*bean.ResourceProtectModel, error) {
	res := make([]*bean.ResourceProtectModel, 0)
	scopeWithResourceVsApprovalConfigs, err := impl.approvalConfigurationEnforcementService.GetConfigurationsByAppAndEnvIdBulk(model.APPLY_POLICY_APPROVAL_CONFIGURATION, scopes)
	if err != nil {
		impl.logger.Errorw("error in getting the scopeWise resource protection approval configuration info", "scope", scopes, "err", err)
		return nil, err
	}

	if len(scopeWithResourceVsApprovalConfigs) == 0 {
		return res, nil
	}

	userGroupIdentifierNameMappings, err := impl.userGroupService.ListIdentifierAndName()
	if err != nil {
		impl.logger.Errorw("error occurred while fetching userGroups metadata", "err", err)
		return nil, err
	}

	userApprovalMetadataRequest := beans2.NewApprovalUsersAndUserGroupsMetadataRequest(userGroupIdentifierNameMappings)
	for _, scope := range scopeWithResourceVsApprovalConfigs {
		resourceVsApprovalConfigs := scope.ApplyStageApprovalConfigs

		approvalConfigs := make([]*beans2.UserApprovalConfigDTO, 0)
		for resourceKind, config := range resourceVsApprovalConfigs {
			approvalConfigData := utils.GetDtoFromConfigAndUserResponse(resourceKind, config, userApprovalMetadataRequest)
			approvalConfigs = append(approvalConfigs, approvalConfigData)
		}

		protectionState := bean.DisabledProtectionState
		if len(approvalConfigs) > 0 {
			protectionState = bean.EnabledProtectionState
		}

		res = append(res, &bean.ResourceProtectModel{
			AppId:                  scope.AppId,
			EnvId:                  scope.EnvId,
			ProtectionState:        protectionState,
			ApprovalConfigurations: approvalConfigs,
		})
	}
	return res, nil
}

func (impl *ResourceProtectionServiceImpl) GetResourceProtectMetadataV1(ctx context.Context, appId int, userMetadata *bean2.UserMetadata) ([]*bean.ResourceProtectModel, error) {
	return impl.GetEnhancedResourceProtectMetadataV2(ctx, appId, userMetadata)
}

func (impl *ResourceProtectionServiceImpl) ResourceProtectionEnabledMap(resourceKinds []beans2.ApprovalFor, appId, envId int) (map[beans2.ApprovalFor]bool, error) {
	respMap := make(map[beans2.ApprovalFor]bool)
	yes, err := impl.appRepo.IsCustomDevtronApp(appId)
	if err != nil {
		impl.logger.Errorw("error in finding app type", "appId", appId, "err", err)
		return respMap, err
	}

	if !yes {
		return respMap, nil
	}

	baseChartExistsForApps, err := impl.chartRepo.FindLatestChartExistsForAppByAppIds([]int{appId})
	if err != nil {
		impl.logger.Errorw("error in getting the latest chart for the app", "appId", appId, "err", err)
		return respMap, err
	}

	resourceProtectionDtos, err := impl.getResourceProtectionInfoByScopes([]*beans2.Scope{{AppId: appId, EnvId: envId}})
	if err != nil {
		impl.logger.Errorw("error in fetching the resource enabled status for a resource with appId and envId", "resourceKinds", resourceKinds, "appId", appId, "envId", envId, "err", err)
		return respMap, err
	}

	if len(resourceProtectionDtos) == 0 {
		return respMap, nil
	}

	resourceProtectionDto := resourceProtectionDtos[0]
	for _, givenResourceKind := range resourceKinds {
		if givenResourceKind == beans2.APPROVAL_FOR_CONFIGURATION_DT {
			if exists := baseChartExistsForApps[appId]; !exists {
				// if base config is not yet saved for this app, then we should not protect the resource
				continue
			}
		}
		protectionEnabled := false
		for _, config := range resourceProtectionDto.ApprovalConfigurations {
			if config.Kind == givenResourceKind {
				protectionEnabled = true
			}
		}

		respMap[givenResourceKind] = protectionEnabled
		if !protectionEnabled {
			err = impl.DiscardDraft(givenResourceKind, appId, envId, bean2.SystemUserId)
			if err != nil {
				impl.logger.Errorw("error in discarding drafts via discard draft hook", "resourceKind", givenResourceKind, "appId", appId, "envId", envId, "err", err)
			}
		}
	}

	return respMap, nil
}

func (impl *ResourceProtectionServiceImpl) ResourceProtectionEnabled(resourceKind beans2.ApprovalFor, appId, envId int, discardUnpublishedDraftsOnDisableState, isExpressEdit bool) bool {

	yes, err := impl.appRepo.IsCustomDevtronApp(appId)
	if err != nil {
		impl.logger.Errorw("error in finding app type", "appId", appId, "err", err)
		return false
	}

	if !yes {
		return false
	}
	if isExpressEdit {
		// if user exception user and express edit mode is enabled then they are allowed to publish changes
		return false
	}
	givenResourceKind := resourceKind

	if givenResourceKind == beans2.APPROVAL_FOR_CONFIGURATION_DT {
		baseChartExistsForApps, err := impl.chartRepo.FindLatestChartExistsForAppByAppIds([]int{appId})
		if err != nil {
			impl.logger.Errorw("error in getting the latest chart for the app", "appId", appId, "err", err)
			return false
		}

		if exists := baseChartExistsForApps[appId]; !exists {
			// if base config is not yet saved for this app, then we should not protect the resource
			return false
		}
	}

	resourceProtectionDtos, err := impl.getResourceProtectionInfoByScopes([]*beans2.Scope{{AppId: appId, EnvId: envId}})
	if err != nil {
		impl.logger.Errorw("error in fetching the resource enabled status for a resource with appId and envId", "resourceKind", resourceKind, "appId", appId, "envId", envId, "err", err)
		return false
	}

	protectionEnabled := false
	if len(resourceProtectionDtos) > 0 {
		resourceProtectionDto := resourceProtectionDtos[0]
		for _, config := range resourceProtectionDto.ApprovalConfigurations {
			if config.Kind == givenResourceKind {
				protectionEnabled = true
				break
			}
		}
	}

	if !protectionEnabled && discardUnpublishedDraftsOnDisableState {
		err = impl.DiscardDraft(resourceKind, appId, envId, bean2.SystemUserId)
		if err != nil {
			impl.logger.Errorw("error in discarding drafts via discard draft hook", "resourceKind", resourceKind, "appId", appId, "envId", envId, "err", err)
		}
	}
	return protectionEnabled
}

func (impl *ResourceProtectionServiceImpl) ResourceProtectionEnabledForEnvV1(ctx context.Context, envId int, userMetadata *bean2.UserMetadata) map[int]bool {
	appVsState := make(map[int]bool)
	protectionDtos, err := impl.GetEnhancedResourceProtectionEnabledForEnvV2(ctx, envId, userMetadata)
	if err == nil {
		for _, protectionDto := range protectionDtos {
			appVsState[protectionDto.AppId] = protectionDto.ProtectionState == bean.EnabledProtectionState
		}
	}
	return appVsState
}

func (impl *ResourceProtectionServiceImpl) GetEnhancedResourceProtectionEnabledForEnvV2(ctx context.Context, envId int, userMetadata *bean2.UserMetadata) ([]*bean.ResourceProtectModel, error) {
	pipelineMins, err := impl.pipelineRepo.FindPipelineIdsByEnvIds([]int{envId})
	if err != nil {
		impl.logger.Errorw("error in getting the pipelines for the env", "envId", envId, "err", err)
		return nil, err
	}

	scopes := make([]*beans2.Scope, 0)
	for _, pipeline := range pipelineMins {
		scopes = append(scopes, &beans2.Scope{AppId: pipeline.AppId, EnvId: pipeline.EnvironmentId})
	}
	responses, err := impl.getResourceProtectionInfoByScopes(scopes)
	if err != nil {
		impl.logger.Errorw("error in getting resource protection info by scopes", "scopes", scopes, "err", err)
		return nil, err
	}
	var exceptionUserActionFunc types.ExceptionUserActionFuncType = func(isUserException bool) {
		for _, item := range responses {
			for _, config := range item.ApprovalConfigurations {
				config.IsExceptionUser = isUserException
			}
		}
	}
	_, err = impl.commonPolicyActionsService.PerformExceptionUserActionHookForPolicyType(ctx, exceptionUserActionFunc, model.APPLY_POLICY_APPROVAL_CONFIGURATION, userMetadata)
	if err != nil {
		impl.logger.Errorw("error in embedding exception user flag in response configs", "err", err)
		return nil, err
	}
	return responses, nil
}

// discardUnPublishedDrafts will discard the given resourceType drafts that are in not terminal state for given App and Env
func (impl *ResourceProtectionServiceImpl) discardUnPublishedDrafts(resourceKind beans2.ApprovalFor, appId, envId int, userId int32) error {
	impl.logger.Debugw("discarding the un published drafts", "appId", appId, "envId", envId, "resourceKind", resourceKind)
	return impl.configDraftRepository.DiscardDraftsByResourceType(adaptors.GetApprovalResourceType(resourceKind), appId, envId, userId)
}

// discardUnPublishedDraftsInTx will discard the given resourceType drafts, that are in not terminal state for given the App
func (impl *ResourceProtectionServiceImpl) discardUnPublishedDraftsInTx(tx *pg.Tx, resourceKind beans2.ApprovalFor, appId int, envId int, userId int32) error {
	impl.logger.Debugw("discarding the un published drafts", "appId", appId, "envId", envId, "resourceKind", resourceKind)
	return impl.configDraftRepository.DiscardDraftsInTx(tx, adaptors.GetApprovalResourceType(resourceKind), appId, envId, userId)
}
