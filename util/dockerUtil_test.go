package util

import "testing"

func TestExtractImageRepoAndTag(t *testing.T) {

	tests := []struct {
		name           string
		imagePath      string
		expectedRepo   string
		expectedTag    string
		expectedDigest string
	}{
		{
			name:           "emptyString",
			imagePath:      "",
			expectedRepo:   "",
			expectedTag:    "",
			expectedDigest: "",
		},
		{
			name:           "withoutDomain",
			imagePath:      "httpd",
			expectedRepo:   "docker.io/library/httpd",
			expectedTag:    "latest",
			expectedDigest: "",
		},
		{
			name:           "withoutTag",
			imagePath:      "docker.io/library/httpd",
			expectedRepo:   "docker.io/library/httpd",
			expectedTag:    "latest",
			expectedDigest: "",
		},
		{
			name:           "withOnlyTag",
			imagePath:      "docker.io/library/foo:123",
			expectedRepo:   "docker.io/library/foo",
			expectedTag:    "123",
			expectedDigest: "",
		},
		{
			name:           "withOnlyDigest",
			imagePath:      "quay.io/devtron/test@sha256:45b23dee08af5e43a7fea6c4cf9c25ccf269ee113168c19722f87876677c5cb2",
			expectedRepo:   "quay.io/devtron/test",
			expectedTag:    "",
			expectedDigest: "sha256:45b23dee08af5e43a7fea6c4cf9c25ccf269ee113168c19722f87876677c5cb2",
		},
		{
			name:           "withTagAndDigest",
			imagePath:      "quay.io/devtron/test:latest@sha256:45b23dee08af5e43a7fea6c4cf9c25ccf269ee113168c19722f87876677c5cb2",
			expectedRepo:   "quay.io/devtron/test",
			expectedTag:    "",
			expectedDigest: "sha256:45b23dee08af5e43a7fea6c4cf9c25ccf269ee113168c19722f87876677c5cb2",
		},
		{
			name:           "withPort",
			imagePath:      "test.com:5000/hello-world:latest",
			expectedRepo:   "test.com:5000/hello-world",
			expectedTag:    "latest",
			expectedDigest: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repoData, _ := ExtractImageRepoAndTag(tt.imagePath)
			if repoData.Repo != tt.expectedRepo ||
				repoData.Tag != tt.expectedTag ||
				repoData.Digest != tt.expectedDigest {
				t.Errorf("TestExtractImageRepoAndTag() gotRepo = %v, wantRepo %v ;  gotTag = %v, wantTag %v ",
					repoData.Repo, tt.expectedRepo,
					repoData.Tag, tt.expectedTag)
			}
		})
	}
}
