package util

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestIsEmptyJSON(t *testing.T) {
	type args struct {
		s []byte
	}
	tests := []struct {
		name string
		args args
		want bool
	}{

		{
			name: "Empty JSON",
			args: args{
				s: []byte(""),
			},
			want: true,
		},

		{
			name: "Empty JSON",
			args: args{
				s: []byte("{}"),
			},
			want: true,
		},

		{
			name: "Non-empty JSON",
			args: args{
				s: []byte(`{"key": "value"}`),
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, IsEmptyJSON(tt.args.s), "IsEmptyJSON(%v)", tt.args.s)
		})
	}
}
