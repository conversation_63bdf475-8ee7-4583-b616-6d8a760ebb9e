package filter

import (
	"github.com/devtron-labs/devtron/pkg/auth/authorisation/casbin"
	"github.com/devtron-labs/devtron/util/rbac"
	"golang.org/x/exp/maps"
)

type RbacFilterUtil interface {
	FilterAuthorizedResources(envIds []int, appId int, token string) []int
}

type RbacFilterUtilImpl struct {
	enforcerUtil rbac.EnforcerUtil
	enforcer     casbin.Enforcer
}

func NewRbacFilterUtilImpl(enforcerUtil rbac.EnforcerUtil, enforcer casbin.Enforcer) *RbacFilterUtilImpl {
	return &RbacFilterUtilImpl{enforcerUtil: enforcerUtil, enforcer: enforcer}
}

func (impl *RbacFilterUtilImpl) FilterAuthorizedResources(envIds []int, appId int, token string) []int {
	objects, _ := impl.enforcerUtil.GetRbacObjectsByEnvIdsAndAppId(envIds, appId)
	rbacObjectArr := maps.Values(objects)

	authorizedResourceIds := make([]int, 0)
	results := impl.enforcer.EnforceInBatch(token, casbin.ResourceEnvironment, casbin.ActionGet, rbacObjectArr)
	for _, resourceId := range envIds {
		resourceObject := objects[resourceId]
		if results[resourceObject] {
			authorizedResourceIds = append(authorizedResourceIds, resourceId)
		}
	}
	return authorizedResourceIds
}
