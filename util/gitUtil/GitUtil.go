/*
 * Copyright (c) 2024. Devtron Inc.
 */

package gitUtil

import (
	"fmt"
	"strings"
)

func GetGitRepoNameFromGitRepoUrl(gitRepoUrl string) string {
	gitRepoUrl = gitRepoUrl[strings.LastIndex(gitRepoUrl, "/")+1:]
	return strings.TrimSuffix(gitRepoUrl, ".git")
}

// RemoveGitSuffix removes the .git suffix from a URL if it exists.
func RemoveGitSuffix(url string) string {
	return strings.TrimSuffix(url, ".git")
}

func GetRefBranchHead(branch string) string {
	return fmt.Sprintf("refs/heads/%s", branch)
}
