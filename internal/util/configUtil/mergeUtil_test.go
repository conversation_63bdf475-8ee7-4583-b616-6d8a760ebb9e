package configUtil

import (
	"encoding/json"
	"github.com/devtron-labs/devtron/internal/util"
	"reflect"
	"testing"
)

func TestApplyPatch(t *testing.T) {

	logger, err := util.NewSugardLogger()
	if err != nil {
		return
	}
	mergeUtil := MergeUtil{Logger: logger}

	type args struct {
		TargetDoc map[string]interface{}
		PatchDoc  map[string]interface{}
	}

	tests := []struct {
		Name string
		args args
		want map[string]interface{}
	}{
		{
			Name: "empty patch",
			args: args{
				TargetDoc: map[string]interface{}{
					"a": "b",
					"b": "c",
				},
				PatchDoc: map[string]interface{}{},
			},
			want: map[string]interface{}{
				"a": "b",
				"b": "c",
			},
		},
		{
			Name: "replacement",
			args: args{
				TargetDoc: map[string]interface{}{
					"a": "b",
					"b": "c",
				},
				PatchDoc: map[string]interface{}{
					"a": "c",
				},
			},
			want: map[string]interface{}{
				"a": "c",
				"b": "c",
			},
		},
		{
			Name: "replacement in nested object",
			args: args{
				TargetDoc: map[string]interface{}{
					"a": map[string]interface{}{
						"nested": "val",
					},
					"b": "c",
				},
				PatchDoc: map[string]interface{}{
					"a": map[string]interface{}{
						"nested": "replace-val",
					},
				},
			},
			want: map[string]interface{}{
				"a": map[string]interface{}{
					"nested": "replace-val",
				},
				"b": "c",
			},
		},
		{
			Name: "replacement in nested object with multiple keys",
			args: args{
				TargetDoc: map[string]interface{}{
					"a": map[string]interface{}{
						"nested":  "val",
						"nested2": "val2",
					},
					"b": "c",
				},
				PatchDoc: map[string]interface{}{
					"a": map[string]interface{}{
						"nested":  "replace-val",
						"nested2": "replace-val-2",
					},
				},
			},
			want: map[string]interface{}{
				"a": map[string]interface{}{
					"nested":  "replace-val",
					"nested2": "replace-val-2",
				},
				"b": "c",
			},
		},
		{
			Name: "add new key",
			args: args{
				TargetDoc: map[string]interface{}{
					"a": map[string]interface{}{
						"nested": "val",
					},
					"b": "c",
				},
				PatchDoc: map[string]interface{}{
					"c": "d",
				},
			},
			want: map[string]interface{}{
				"a": map[string]interface{}{
					"nested": "val",
				},
				"b": "c",
				"c": "d",
			},
		},
		{
			Name: "add multiple new keys",
			args: args{
				TargetDoc: map[string]interface{}{
					"a": "b",
				},
				PatchDoc: map[string]interface{}{
					"b": "c",
					"c": "d",
				},
			},
			want: map[string]interface{}{
				"a": "b",
				"b": "c",
				"c": "d",
			},
		},
		{
			Name: "add new key in nested object ",
			args: args{
				TargetDoc: map[string]interface{}{
					"a": map[string]interface{}{
						"nested": "val",
					},
					"b": "c",
				},
				PatchDoc: map[string]interface{}{
					"a": map[string]interface{}{
						"nested":  "val",
						"nested2": "val2",
					},
				},
			},
			want: map[string]interface{}{
				"a": map[string]interface{}{
					"nested":  "val",
					"nested2": "val2",
				},
				"b": "c",
			},
		},
		{
			Name: "add new key in nested object if key does not exist",
			args: args{
				TargetDoc: map[string]interface{}{
					"b": "c",
					"a": map[string]interface{}{
						"nested":  "val",
						"nested2": "val2",
					},
				},
				PatchDoc: map[string]interface{}{
					"a": map[string]interface{}{
						"nested3": map[string]interface{}{
							"nested3double": "val3",
						},
					},
				},
			},
			want: map[string]interface{}{
				"a": map[string]interface{}{
					"nested":  "val",
					"nested2": "val2",
					"nested3": map[string]interface{}{
						"nested3double": "val3",
					},
				},
				"b": "c",
			},
		},
		{
			Name: "add nested object if parent key does not exist",
			args: args{
				TargetDoc: map[string]interface{}{
					"b": "c",
					"a": map[string]interface{}{
						"nested":  "val",
						"nested2": "val2",
					},
				},
				PatchDoc: map[string]interface{}{
					"c": map[string]interface{}{
						"nested3": "val3",
					},
				},
			},
			want: map[string]interface{}{
				"a": map[string]interface{}{
					"nested":  "val",
					"nested2": "val2",
				},
				"b": "c",
				"c": map[string]interface{}{
					"nested3": "val3",
				},
			},
		},
		{
			Name: "add empty Object",
			args: args{
				TargetDoc: map[string]interface{}{},
				PatchDoc: map[string]interface{}{
					"a": map[string]interface{}{},
				},
			},
			want: map[string]interface{}{
				"a": map[string]interface{}{},
			},
		},
		{
			Name: "replace and add simultaneously",
			args: args{
				TargetDoc: map[string]interface{}{
					"a": map[string]interface{}{
						"nested":  "val",
						"nested2": "val2",
					},
					"b": "c",
				},
				PatchDoc: map[string]interface{}{
					"a": map[string]interface{}{
						"nested":  "replace-val",
						"nested2": "replace-val-2",
						"nested3": "added-val",
					},
				},
			},
			want: map[string]interface{}{
				"a": map[string]interface{}{
					"nested":  "replace-val",
					"nested2": "replace-val-2",
					"nested3": "added-val",
				},
				"b": "c",
			},
		},
		{
			Name: "with array object",
			args: args{
				TargetDoc: map[string]interface{}{
					"b": "c",
					"a": map[string]interface{}{
						"nested":  []string{"v1", "v2"},
						"nested2": "val2",
					},
				},
				PatchDoc: map[string]interface{}{
					"a": map[string]interface{}{
						"nested":  []string{"v3", "v4"},
						"nested2": "val2",
					},
				},
			},
			want: map[string]interface{}{
				"a": map[string]interface{}{
					"nested":  []interface{}{"v3", "v4"},
					"nested2": "val2",
				},
				"b": "c",
			},
		},
		{
			Name: "with null value", //if there is a null value in patch object, it should not be removed from final merged values
			args: args{
				TargetDoc: map[string]interface{}{
					"b": "c",
					"a": map[string]interface{}{
						"nested":  []string{"v1", "v2"},
						"nested2": "val2",
					},
				},
				PatchDoc: map[string]interface{}{
					"a": map[string]interface{}{
						"nested":  []string{"v3", "v4"},
						"nested2": "val2",
					},
					"b": nil,
				},
			},
			want: map[string]interface{}{
				"a": map[string]interface{}{
					"nested":  []interface{}{"v3", "v4"},
					"nested2": "val2",
				},
				"b": nil,
			},
		},
		{
			Name: "escaping special characters ", //if there is a null value in patch object, it should not be removed from final merged values
			args: args{
				TargetDoc: map[string]interface{}{
					"a~b/c": "c",
					"a": map[string]interface{}{
						"nested":  []string{"v1", "v2"},
						"nested2": "val2",
					},
				},
				PatchDoc: map[string]interface{}{
					"a": map[string]interface{}{
						"nested":  []string{"v3", "v4"},
						"nested2": "val2",
					},
					"a~b/c": "d",
				},
			},
			want: map[string]interface{}{
				"a": map[string]interface{}{
					"nested":  []interface{}{"v3", "v4"},
					"nested2": "val2",
				},
				"a~b/c": "d",
			},
		},
	}

	for _, tt := range tests {

		t.Run(tt.Name, func(t *testing.T) {
			target, patch, _ := getJson(tt.args.TargetDoc, tt.args.PatchDoc, tt.want)
			if got, err := mergeUtil.ApplyPatch(target, patch); err != nil {
				t.Errorf("ApplyPatch() err: %s", err.Error())
			} else if err == nil {
				gotMap := make(map[string]interface{})
				_ = json.Unmarshal(got, &gotMap)
				if !reflect.DeepEqual(gotMap, tt.want) {
					t.Errorf("SyncChanges() = %s, want %s", got, tt.want)
				}
			}

		})
	}

}

func getJson(targetDoc, patchDoc, mergedDoc map[string]interface{}) ([]byte, []byte, []byte) {
	target, _ := json.Marshal(targetDoc)
	patch, _ := json.Marshal(patchDoc)
	merged, _ := json.Marshal(mergedDoc)
	return target, patch, merged
}
