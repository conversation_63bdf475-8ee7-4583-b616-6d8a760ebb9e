/*
 * Copyright (c) 2020-2024. Devtron Inc.
 */

package configUtil

import (
	"encoding/json"
	"github.com/devtron-labs/devtron/api/bean"
	"github.com/devtron-labs/devtron/internal/sql/models"
	globalUtil "github.com/devtron-labs/devtron/util"
	jsonpatch "github.com/evanphx/json-patch"
	jsonpatchV5 "github.com/evanphx/json-patch/v5"
	"go.uber.org/zap"
	"golang.org/x/exp/slices"
	"log"
	"strings"
)

type MergeUtil struct {
	Logger *zap.SugaredLogger
}

/*
//returns json representation of merged values
func (m MergeUtil) MergeOverride(helmValues string, override []byte) ([]byte, error) {
	cf, err := conflate.FromData([]byte(helmValues), override)
	if err != nil {
		m.Logger.Errorw("error in merging config",
			"original", helmValues,
			"override", override,
			"error", err)
		return nil, err
	}
	jsonBytes, err := cf.MarshalJ<PERSON>()
	if err != nil {
		m.Logger.Errorw("error in marshaling yaml ",
			"cf", cf,
			"error", err)
		return nil, err
	}
	dst := new(bytes.Buffer)
	err = json.Compact(dst, jsonBytes)
	if err != nil {
		return nil, err
	}
	jsonBytes = dst.Bytes()
	m.Logger.Infow("merged config ",
		"original", helmValues,
		"override", override,
		"yaml", jsonBytes,
	)
	return jsonBytes, nil
}

func (m MergeUtil) MergeOverrideVal(data ...[]byte) ([]byte, error) {
	cf, err := conflate.FromData(data...)
	if err != nil {
		m.Logger.Errorw("error in merging config",
			"val", data,
			"error", err)
		return nil, err
	}
	jsonBytes, err := cf.MarshalJSON()
	if err != nil {
		m.Logger.Errorw("error in marshaling yaml ",
			"cf", cf,
			"error", err)
		return nil, err
	}
	dst := new(bytes.Buffer)
	err = json.Compact(dst, jsonBytes)
	if err != nil {
		return nil, err
	}
	jsonBytes = dst.Bytes()
	return jsonBytes, nil
}
*/
//merges two json objects
func (m MergeUtil) JsonPatch(target, patch []byte) (data []byte, err error) {
	data, err = jsonpatch.MergePatch(target, patch)
	if err != nil {
		m.Logger.Debugw("error in merging json ", "target", target, "patch", patch, "err", err)
	}
	return data, err
}

// JsonPatchV2 uses newer version of json path, new version retains order in merged document
// keys having null value in patch will be removed in merged document,
// if this behaviour is not wanted use ApplyPatch
func (m MergeUtil) JsonPatchV2(target, patch []byte) (data []byte, err error) {

	data, err = jsonpatchV5.MergePatch(target, patch)
	if err != nil {
		m.Logger.Errorw("error in applying patch operations to target", "err", err)
		return nil, err
	}

	return data, nil
}

func (m MergeUtil) ApplyPatch(target, patch []byte) (data []byte, err error) {
	patchMap := make(map[string]interface{})
	err = json.Unmarshal(patch, &patchMap)
	if err != nil {
		m.Logger.Errorw("error in unmarshal map", "err", err)
		return nil, err
	}
	targetMap := make(map[string]interface{})
	err = json.Unmarshal(target, &targetMap)
	if err != nil {
		m.Logger.Errorw("error in unmarshal map", "err", err)
		return nil, err
	}
	patchOperations, err := getPatchOperations(patchMap, targetMap)
	if err != nil {
		m.Logger.Errorw("error in getting JSON patch operations array", "err", err)
		return nil, err
	}
	data, err = patchOperations.Apply(target)
	if err != nil {
		m.Logger.Errorw("error in applying patch operations to target", "err", err)
		return nil, err
	}
	return data, nil
}

type PatchOperation struct {
	Op    string      `json:"op"`
	Path  string      `json:"path"`
	Value interface{} `json:"value"`
}

func getPatchOperations(patchDoc, targetDoc map[string]interface{}) (jsonpatchV5.Patch, error) {
	patchOperations, err := convertToJSONPatch(targetDoc, patchDoc, "")
	if err != nil {
		return nil, err
	}
	patchOperationsJSON, err := json.Marshal(patchOperations)
	if err != nil {
		return nil, err
	}
	patch, err := jsonpatchV5.DecodePatch(patchOperationsJSON)
	if err != nil {
		return nil, err
	}
	return patch, nil
}

func convertToJSONPatch(original map[string]interface{}, patch map[string]interface{}, path string) ([]PatchOperation, error) {
	var jsonPatch []PatchOperation

	for patchKey, patchValue := range patch {

		patchKey = escapeJSONPointer(patchKey)

		currentPath := path + "/" + patchKey

		originalValue, patchKeyExistsInOriginal := original[patchKey]

		if !patchKeyExistsInOriginal {
			//patch key does not exist in original, irrespective of the type of patch value (map or non-map) adding it
			jsonPatch = append(jsonPatch, PatchOperation{
				Op:    "add",
				Path:  currentPath,
				Value: patchValue,
			})
		} else {
			//patch key exists in original, check the type of the patchValue and act accordingly
			patchValueMap, isPatchValueAMap := patchValue.(map[string]interface{})
			originalValueMap, isOriginalValueAMap := originalValue.(map[string]interface{})
			if isPatchValueAMap && isOriginalValueAMap {
				//patchValue and originalValue is json(map), cannot be directly added. Recur and update one by one
				operations, err := convertToJSONPatch(originalValueMap, patchValueMap, currentPath)
				if err != nil {
					return nil, err
				}
				jsonPatch = append(jsonPatch, operations...)
			} else {
				//either patchValue or originalValue is not json(map) meaning is of type (string, number, boolean, array). Can be directly replaced
				jsonPatch = append(jsonPatch, PatchOperation{
					Op:    "replace",
					Path:  currentPath,
					Value: patchValue,
				})
			}
		}
	}
	return jsonPatch, nil
}

func (m MergeUtil) ConfigMapMerge(appLevelConfigMapJson string, envLevelConfigMapJson string) (data string, err error) {
	appLevelConfigMap := bean.ConfigMapJson{}
	envLevelConfigMap := bean.ConfigMapJson{}
	configResponse := bean.ConfigMapJson{}
	if appLevelConfigMapJson != "" {
		err = json.Unmarshal([]byte(appLevelConfigMapJson), &appLevelConfigMap)
		if err != nil {
			m.Logger.Debugw("error in Unmarshal ", "appLevelConfigMapJson", appLevelConfigMapJson, "envLevelConfigMapJson", envLevelConfigMapJson, "err", err)
		}
	}
	if envLevelConfigMapJson != "" {
		err = json.Unmarshal([]byte(envLevelConfigMapJson), &envLevelConfigMap)
		if err != nil {
			m.Logger.Debugw("error in Unmarshal ", "appLevelConfigMapJson", appLevelConfigMapJson, "envLevelConfigMapJson", envLevelConfigMapJson, "err", err)
		}
	}
	if len(appLevelConfigMap.Maps) > 0 || len(envLevelConfigMap.Maps) > 0 {
		configResponse.Enabled = true
	}

	configResponse.Maps, err = mergeConfigMapsAndSecrets(envLevelConfigMap.Maps, appLevelConfigMap.Maps)
	if err != nil {
		m.Logger.Errorw("error in merging cm cs", "err", err)
	}
	byteData, err := json.Marshal(configResponse)
	if err != nil {
		m.Logger.Debugw("error in marshal ", "err", err)
	}
	return string(byteData), err
}

func escapeJSONPointer(key string) string {
	//if key has / or ~ it should be escaped by following JSON patch convention
	key = strings.ReplaceAll(key, "~", "~0")
	key = strings.ReplaceAll(key, "/", "~1")
	return key
}

func (m MergeUtil) ConfigSecretMergeForJob(appLevelSecretJson, envLevelSecretJson string) (data string, err error) {
	return m.configSecretMerge(appLevelSecretJson, envLevelSecretJson, "", true)
}

func (m MergeUtil) ConfigSecretMergeForCDStages(appLevelSecretJson, envLevelSecretJson, chartVersion string) (data string, err error) {
	return m.configSecretMerge(appLevelSecretJson, envLevelSecretJson, chartVersion, false)
}

func (m MergeUtil) configSecretMerge(appLevelSecretJson, envLevelSecretJson, chartVersion string, isJob bool) (data string, err error) {
	var chartMajorVersion, chartMinorVersion int
	if chartVersion != "" {
		chartMajorVersion, chartMinorVersion, err = globalUtil.ExtractChartVersion(chartVersion)
		if err != nil {
			m.Logger.Errorw("chart version parsing", "err", err)
			return "", err
		}
	}
	appLevelSecret := bean.ConfigSecretJson{}
	if appLevelSecretJson != "" {
		err = json.Unmarshal([]byte(appLevelSecretJson), &appLevelSecret)
		if err != nil {
			m.Logger.Debugw("error in Unmarshal ", "appLevelSecretJson", appLevelSecretJson, "envLevelSecretJson", envLevelSecretJson, "err", err)
		}
	}
	envLevelSecret := bean.ConfigSecretJson{}
	if envLevelSecretJson != "" {
		err = json.Unmarshal([]byte(envLevelSecretJson), &envLevelSecret)
		if err != nil {
			m.Logger.Debugw("error in Unmarshal ", "appLevelSecretJson", appLevelSecretJson, "envLevelSecretJson", envLevelSecretJson, "err", err)
		}
	}
	secretResponse := bean.ConfigSecretJson{}
	if len(appLevelSecret.Secrets) > 0 || len(envLevelSecret.Secrets) > 0 {
		secretResponse.Enabled = true
	}

	finalCMCS, err := mergeConfigMapsAndSecrets(envLevelSecret.GetDereferencedSecrets(), appLevelSecret.GetDereferencedSecrets())
	if err != nil {
		m.Logger.Errorw("error in merging cm cs", "err", err)
		return "", err
	}
	for _, finalMap := range finalCMCS {
		finalMap = m.processExternalSecrets(finalMap, chartMajorVersion, chartMinorVersion, isJob)
	}
	secretResponse.SetReferencedSecrets(finalCMCS)
	byteData, err := json.Marshal(secretResponse)
	if err != nil {
		m.Logger.Debugw("error in marshal ", "err", err)
		return "", err
	}
	return string(byteData), err
}

func mergeConfigMapsAndSecrets(envLevelCMCS []bean.ConfigSecretMap, appLevelSecretCMCS []bean.ConfigSecretMap) ([]bean.ConfigSecretMap, error) {
	envCMCSNames := make([]string, 0)
	appLevelCMCSMap := make(map[string]bean.ConfigSecretMap)
	var finalCMCS []bean.ConfigSecretMap
	for _, item := range envLevelCMCS {
		envCMCSNames = append(envCMCSNames, item.Name)
	}
	for _, item := range appLevelSecretCMCS {
		appLevelCMCSMap[item.Name] = item
	}
	for _, item := range appLevelSecretCMCS {
		//else ignoring this value as override from configB
		if !slices.Contains(envCMCSNames, item.Name) {
			finalCMCS = append(finalCMCS, item)
		}
	}
	for _, item := range envLevelCMCS {
		if appCMCS, ok := appLevelCMCSMap[item.Name]; ok {
			if item.MergeStrategy == models.MERGE_STRATEGY_PATCH {
				patchData := item.Data
				mergedData, err := jsonpatchV5.MergePatch(appCMCS.Data, patchData)
				if err != nil {
					log.Printf("error in merging patch data with base data: %s", err.Error())
					return nil, err
				}
				item.PatchData = patchData
				item.Data = mergedData
			}
		}
		finalCMCS = append(finalCMCS, item)
	}
	return finalCMCS, nil
}

func (m MergeUtil) processExternalSecrets(secret bean.ConfigSecretMap, chartMajorVersion int, chartMinorVersion int, isJob bool) bean.ConfigSecretMap {
	if secret.ExternalType == globalUtil.AWSSecretsManager || secret.ExternalType == globalUtil.AWSSystemManager || secret.ExternalType == globalUtil.HashiCorpVault {
		if secret.SecretData != nil && ((chartMajorVersion <= 3 && chartMinorVersion < 8) || isJob) {
			var es []map[string]interface{}
			esNew := make(map[string]interface{})
			err := json.Unmarshal(secret.SecretData, &es)
			if err != nil {
				m.Logger.Debugw("error in Unmarshal ", "SecretData", secret.SecretData, "external secret", es, "err", err)
			}
			for _, item := range es {
				keyProp := item["name"].(string)
				valueProp := item["key"]
				esNew[keyProp] = valueProp
			}
			byteData, err := json.Marshal(esNew)
			if err != nil {
				m.Logger.Debugw("error in marshal ", "err", err)
			}
			secret.Data = byteData
			secret.SecretData = nil
		}
	}
	return secret
}
