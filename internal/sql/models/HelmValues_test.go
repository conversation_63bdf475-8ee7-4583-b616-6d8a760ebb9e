/*
 * Copyright (c) 2020-2024. Devtron Inc.
 */

package models

/*
func TestDuplicateHelmValuesSave(t *testing.T) {

	hv := &HelmValues{
		AppName:           "demo-app-5",
		TargetEnvironment: "prod-env",
		Values: `{
    "image": {
      "tag": "1.2.0",
      "image":"nginx"
    }
  }`,

		UpdatedBy: 2,
		UpdatedOn: time.Now(),
		CreatedOn: time.Now(),
		CreatedBy: 3,

		Active: true,
	}

	err := AddHelmValues(hv)
	err1 := AddHelmValues(hv)
	assert.NoError(t, err)
	assert.Error(t, err1)
}

func TestGetHelmValues(t *testing.T) {
	hv, err := GetHelmValues("demo-app", "test-env-1")
	assert.NoError(t, err)
	assert.NotNil(t, hv)

}

func TestCreate(t *testing.T) {
	err := createAuthor()
	assert.NoError(t, err)
}
*/
