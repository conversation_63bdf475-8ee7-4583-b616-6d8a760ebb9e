package repository

import (
	"fmt"
	"github.com/devtron-labs/devtron/internal/util"
	util2 "github.com/devtron-labs/devtron/pkg/auth/user/util"
	"github.com/devtron-labs/devtron/pkg/sql"
	"testing"
)

func TestUserAuthRepo(t *testing.T) {
	t.Run("testing lower email case", func(t *testing.T) {
		sugaredLogger, _ := util.NewSugardLogger()
		sqlConfig, _ := sql.GetConfig()
		db, _ := sql.NewDbConnection(sqlConfig, sugaredLogger)
		userAttributesRepositoryImpl := NewUserAttributesRepositoryImpl(db)
		baseEmail := "Abcd@gmail"
		emailId := util2.ApiTokenPrefix + baseEmail
		dataByEmailId, err := userAttributesRepositoryImpl.GetUserDataByEmailId(emailId)
		//if err != nil {
		//	fmt.Println("err", err)
		//	return
		//}
		fmt.Println("data", dataByEmailId)

		userAttr := &UserAttributesDao{
			EmailId: baseEmail,
			Key:     "hello",
		}
		dataValueByKey, err := userAttributesRepositoryImpl.GetDataValueByKey(userAttr)
		if err != nil {
			fmt.Println("err", err)
			return
		}
		fmt.Println("data", dataValueByKey)
	})
}
