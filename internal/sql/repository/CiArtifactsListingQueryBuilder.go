/*
 * Copyright (c) 2024. Devtron Inc.
 */

package repository

import (
	"fmt"
	"github.com/devtron-labs/devtron/api/bean"
	"github.com/devtron-labs/devtron/internal/sql/repository/helper"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/artifactPromotion/constants"
	"github.com/go-pg/pg"
)

const EmptyLikeRegex = "%%"

func BuildQueryForParentTypeCIOrWebhook(listingFilterOpts bean.ArtifactsListFilterOptions, isApprovalNode bool) (string, []interface{}) {
	commonPaginatedQueryPart, commonPaginatedQueryParams := " cia.image LIKE ?", []interface{}{listingFilterOpts.SearchString}
	orderByClause := " ORDER BY cia.id DESC"
	limitOffsetQueryPart, limitOffsetQueryParams := fmt.Sprintf(" LIMIT ? OFFSET ?"), []interface{}{listingFilterOpts.Limit, listingFilterOpts.Offset}
	finalQuery := ""
	var finalQueryParams []interface{}
	var remainingQueryParams []interface{}
	commonApprovalNodeSubQueryPart, commonApprovalNodeSubQueryParams := "cia.id NOT IN "+
		" ( "+
		" SELECT DISTINCT dar.ci_artifact_id "+
		" FROM deployment_approval_request dar "+
		" WHERE dar.pipeline_id = ? "+
		" AND dar.active=true "+
		" AND dar.artifact_deployment_triggered = false"+
		" ) AND ", []interface{}{listingFilterOpts.PipelineId}

	if listingFilterOpts.ParentStageType == bean.CI_WORKFLOW_TYPE {
		selectQuery := " SELECT cia.* "
		remainingQuery := " FROM ci_artifact cia" +
			" INNER JOIN ci_pipeline cp ON (cp.id=cia.pipeline_id or (cp.id=cia.component_id and cia.data_source='post_ci' ) )" +
			" INNER JOIN pipeline p ON (p.ci_pipeline_id = cp.id and p.id=? )" +
			" WHERE "
		remainingQueryParams = []interface{}{listingFilterOpts.PipelineId}
		if isApprovalNode {
			remainingQuery += commonApprovalNodeSubQueryPart
			remainingQueryParams = append(remainingQueryParams, commonApprovalNodeSubQueryParams...)
		} else if len(listingFilterOpts.ExcludeArtifactIds) > 0 {
			remainingQuery += "cia.id NOT IN (?) AND "
			remainingQueryParams = append(remainingQueryParams, pg.In(listingFilterOpts.ExcludeArtifactIds))
		}

		countQuery := " SELECT count(cia.id)  as total_count"
		totalCountQuery := countQuery + remainingQuery + commonPaginatedQueryPart
		selectQuery = fmt.Sprintf("%s,(%s) ", selectQuery, totalCountQuery)
		finalQuery = selectQuery + remainingQuery + commonPaginatedQueryPart + orderByClause + limitOffsetQueryPart
	} else if listingFilterOpts.ParentStageType == bean.WEBHOOK_WORKFLOW_TYPE {
		selectQuery := " SELECT cia.* "
		remainingQuery := " FROM ci_artifact cia " +
			" WHERE cia.external_ci_pipeline_id = ? AND "
		remainingQueryParams = []interface{}{listingFilterOpts.ParentId}
		if isApprovalNode {
			remainingQuery += commonApprovalNodeSubQueryPart
			remainingQueryParams = append(remainingQueryParams, commonApprovalNodeSubQueryParams...)
		} else if len(listingFilterOpts.ExcludeArtifactIds) > 0 {
			remainingQuery += "cia.id NOT IN (?) AND "
			remainingQueryParams = append(remainingQueryParams, pg.In(listingFilterOpts.ExcludeArtifactIds))
		}

		countQuery := " SELECT count(cia.id)  as total_count"
		totalCountQuery := countQuery + remainingQuery + commonPaginatedQueryPart
		selectQuery = fmt.Sprintf("%s,(%s) ", selectQuery, totalCountQuery)
		finalQuery = selectQuery + remainingQuery + commonPaginatedQueryPart + orderByClause + limitOffsetQueryPart
	}
	finalQueryParams = append(finalQueryParams, remainingQueryParams...)
	finalQueryParams = append(finalQueryParams, commonPaginatedQueryParams...)
	finalQueryParams = append(finalQueryParams, remainingQueryParams...)
	finalQueryParams = append(finalQueryParams, commonPaginatedQueryParams...)
	finalQueryParams = append(finalQueryParams, limitOffsetQueryParams...)
	return finalQuery, finalQueryParams
}

func BuildQueryForArtifactsForCdStage(listingFilterOptions bean.ArtifactsListFilterOptions, isApprovalNode bool) string {
	if listingFilterOptions.UseCdStageQueryV2 {
		return buildQueryForArtifactsForCdStageV2(listingFilterOptions, isApprovalNode)
	}

	// TODO: revisit this condition (cd_workflow.pipeline_id= %v and cd_workflow_runner.workflow_type = '%v' )
	// TODO: remove below code
	commonQuery := " from ci_artifact LEFT JOIN cd_workflow ON ci_artifact.id = cd_workflow.ci_artifact_id" +
		" LEFT JOIN cd_workflow_runner ON cd_workflow_runner.cd_workflow_id=cd_workflow.id " +
		" Where (((cd_workflow_runner.id in (select MAX(cd_workflow_runner.id) OVER (PARTITION BY cd_workflow.ci_artifact_id) FROM cd_workflow_runner inner join cd_workflow on cd_workflow.id=cd_workflow_runner.cd_workflow_id))" +
		" AND ((cd_workflow.pipeline_id= %v and cd_workflow_runner.workflow_type = '%v' ) OR (cd_workflow.pipeline_id = %v AND cd_workflow_runner.workflow_type = '%v' AND cd_workflow_runner.status IN ('Healthy','Succeeded') )))" +
		" OR (ci_artifact.component_id = %v  and ci_artifact.data_source= '%v' ))" +
		" AND (ci_artifact.image LIKE '%v' )"

	commonQuery = fmt.Sprintf(commonQuery, listingFilterOptions.PipelineId, listingFilterOptions.StageType, listingFilterOptions.ParentId, listingFilterOptions.ParentStageType, listingFilterOptions.ParentId, listingFilterOptions.PluginStage, listingFilterOptions.SearchString)
	if isApprovalNode {
		commonQuery = commonQuery + fmt.Sprintf(" AND ( cd_workflow.ci_artifact_id NOT IN (SELECT DISTINCT dar.ci_artifact_id FROM deployment_approval_request dar WHERE dar.pipeline_id = %v AND dar.active=true AND dar.artifact_deployment_triggered = false))", listingFilterOptions.PipelineId)
	} else if len(listingFilterOptions.ExcludeArtifactIds) > 0 {
		commonQuery = commonQuery + fmt.Sprintf(" AND ( cd_workflow.ci_artifact_id NOT IN (%v))", helper.GetCommaSepratedString(listingFilterOptions.ExcludeArtifactIds))
	}

	totalCountQuery := "SELECT COUNT(DISTINCT ci_artifact.id) as total_count " + commonQuery
	selectQuery := fmt.Sprintf("SELECT DISTINCT(ci_artifact.id) , (%v) ", totalCountQuery)
	// GroupByQuery := " GROUP BY cia.id "
	limitOffSetQuery := fmt.Sprintf(" order by ci_artifact.id desc LIMIT %v OFFSET %v", listingFilterOptions.Limit, listingFilterOptions.Offset)

	// finalQuery := selectQuery + commonQuery + GroupByQuery + limitOffSetQuery
	finalQuery := selectQuery + commonQuery + limitOffSetQuery
	return finalQuery
}

func buildQueryForArtifactsForCdStageV2(listingFilterOptions bean.ArtifactsListFilterOptions, isApprovalNode bool) string {
	whereCondition := fmt.Sprintf(" WHERE ( id IN ("+
		" SELECT DISTINCT(cd_workflow.ci_artifact_id) as ci_artifact_id "+
		" FROM cd_workflow_runner"+
		" INNER JOIN cd_workflow ON cd_workflow.id = cd_workflow_runner.cd_workflow_id "+
		" AND (cd_workflow.pipeline_id = %d OR cd_workflow.pipeline_id = %d)"+
		"    WHERE ("+
		"            (cd_workflow.pipeline_id = %d AND cd_workflow_runner.workflow_type = '%s')"+
		"            OR"+
		"            (cd_workflow.pipeline_id = %d"+
		"                AND cd_workflow_runner.workflow_type = '%s'"+
		"                AND cd_workflow_runner.status IN ('Healthy','Succeeded')"+
		"           )"+
		"      )   ) ", listingFilterOptions.PipelineId, listingFilterOptions.ParentId, listingFilterOptions.PipelineId, listingFilterOptions.StageType, listingFilterOptions.ParentId, listingFilterOptions.ParentStageType)

	// promoted artifacts
	// destination pipeline-id and artifact-id are indexed
	if listingFilterOptions.ParentStageType != bean.CD_WORKFLOW_TYPE_PRE && listingFilterOptions.StageType != bean.CD_WORKFLOW_TYPE_POST {
		whereCondition = fmt.Sprintf(" %s OR id in (select artifact_id from artifact_promotion_approval_request where status=%d and destination_pipeline_id = %d)", whereCondition, constants.PROMOTED, listingFilterOptions.PipelineId)
	}
	// plugin artifacts
	whereCondition = fmt.Sprintf(" %s OR (ci_artifact.component_id = %d  AND ci_artifact.data_source= '%s' ))", whereCondition, listingFilterOptions.ParentId, listingFilterOptions.PluginStage)

	if isApprovalNode {
		whereCondition = whereCondition + fmt.Sprintf(" AND ( ci_artifact.id NOT IN (SELECT DISTINCT dar.ci_artifact_id FROM deployment_approval_request dar WHERE dar.pipeline_id = %d AND dar.active=true AND dar.artifact_deployment_triggered = false))", listingFilterOptions.PipelineId)
	} else if len(listingFilterOptions.ExcludeArtifactIds) > 0 {
		whereCondition = whereCondition + fmt.Sprintf(" AND ( ci_artifact.id NOT IN (%s))", helper.GetCommaSepratedString(listingFilterOptions.ExcludeArtifactIds))
	}

	if listingFilterOptions.SearchString != EmptyLikeRegex {
		whereCondition = whereCondition + fmt.Sprintf(" AND ci_artifact.image LIKE '%s' ", listingFilterOptions.SearchString)
	}

	selectQuery := fmt.Sprintf(" SELECT ci_artifact.* ,COUNT(id) OVER() AS total_count " +
		" FROM ci_artifact")
	ordeyByAndPaginated := fmt.Sprintf(" ORDER BY id DESC LIMIT %d OFFSET %d ", listingFilterOptions.Limit, listingFilterOptions.Offset)
	finalQuery := selectQuery + whereCondition + ordeyByAndPaginated
	return finalQuery
}

func BuildQueryForArtifactsForRollback(listingFilterOptions bean.ArtifactsListFilterOptions) string {
	commonQuery := " FROM cd_workflow_runner cdwr " +
		" INNER JOIN cd_workflow cdw ON cdw.id=cdwr.cd_workflow_id " +
		" INNER JOIN ci_artifact cia ON cia.id=cdw.ci_artifact_id " +
		" WHERE cdw.pipeline_id=%v AND cdwr.workflow_type = '%v' "

	commonQuery = fmt.Sprintf(commonQuery, listingFilterOptions.PipelineId, listingFilterOptions.StageType)
	if listingFilterOptions.SearchString != EmptyLikeRegex {
		commonQuery += fmt.Sprintf(" AND cia.image LIKE '%v' ", listingFilterOptions.SearchString)
	}
	if len(listingFilterOptions.ExcludeWfrIds) > 0 {
		commonQuery = fmt.Sprintf(" %s AND cdwr.id NOT IN (%s)", commonQuery, helper.GetCommaSepratedString(listingFilterOptions.ExcludeWfrIds))
	}
	totalCountQuery := " SELECT COUNT(cia.id) as total_count " + commonQuery
	orderByQuery := " ORDER BY cdwr.id DESC "
	limitOffsetQuery := fmt.Sprintf("LIMIT %v OFFSET %v", listingFilterOptions.Limit, listingFilterOptions.Offset)
	finalQuery := fmt.Sprintf(" SELECT cdwr.id as cd_workflow_runner_id,cdwr.triggered_by,cdwr.started_on,cia.*,(%s) ", totalCountQuery) + commonQuery + orderByQuery + limitOffsetQuery
	return finalQuery
}

func BuildQueryForApprovedArtifactsForRollback(listingFilterOpts bean.ArtifactsListFilterOptions, approvedApprovalRequestIds []int) string {

	// 1) fetch all the approval requests using pipelineId and the request_approval_user_data for deployment approval type.
	// (inner join deployment_approval_request inner join request_approval_user_data inner join user)
	// 2) get user groups of all user ids (have an api to get this data)
	// 3) now evalute this data against the approval config and get the approved request ids (runtime evaluation)
	// filter out the approved request ids and share these ids as IN query.

	requestIdsStr := helper.GetCommaSepratedString(approvedApprovalRequestIds)
	commonQuery := " FROM cd_workflow_runner cdwr " +
		"   INNER JOIN cd_workflow cdw ON cdw.id=cdwr.cd_workflow_id" +
		"	INNER JOIN ci_artifact cia ON cia.id=cdw.ci_artifact_id" +
		"	INNER JOIN deployment_approval_request dar ON dar.ci_artifact_id = cdw.ci_artifact_id" +
		"   WHERE dar.id IN (%s) AND cdw.pipeline_id = %v" +
		"   AND cdwr.workflow_type = '%v'"
	if listingFilterOpts.SearchString != EmptyLikeRegex {
		commonQuery += fmt.Sprintf(" AND cia.image LIKE '%v' ", listingFilterOpts.SearchString)
	}

	commonQuery = fmt.Sprintf(commonQuery, requestIdsStr, listingFilterOpts.PipelineId, listingFilterOpts.StageType)
	if len(listingFilterOpts.ExcludeWfrIds) > 0 {
		commonQuery = fmt.Sprintf(" %s AND cdwr.id NOT IN (%s)", commonQuery, helper.GetCommaSepratedString(listingFilterOpts.ExcludeWfrIds))
	}

	totalCountQuery := " SELECT COUNT(cia.id) as total_count " + commonQuery
	orderByQuery := " ORDER BY cdwr.id DESC "
	limitOffsetQuery := fmt.Sprintf("LIMIT %v OFFSET %v ", listingFilterOpts.Limit, listingFilterOpts.Offset)
	finalQuery := fmt.Sprintf(" SELECT cdwr.id as cd_workflow_runner_id,cdwr.triggered_by,cdwr.started_on,cia.*,(%s) ", totalCountQuery) + commonQuery + orderByQuery + limitOffsetQuery
	return finalQuery
}
