// Code generated by mockery v2.14.0. DO NOT EDIT.

package mocks

import (
	pg "github.com/go-pg/pg"
	mock "github.com/stretchr/testify/mock"

	repository "github.com/devtron-labs/devtron/internal/sql/repository/dockerRegistry"
)

// DockerArtifactStoreRepository is an autogenerated mock type for the DockerArtifactStoreRepository type
type DockerArtifactStoreRepository struct {
	mock.Mock
}

// Delete provides a mock function with given fields: storeId
func (_m *DockerArtifactStoreRepository) Delete(storeId string) error {
	ret := _m.Called(storeId)

	var r0 error
	if rf, ok := ret.Get(0).(func(string) error); ok {
		r0 = rf(storeId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// FindActiveDefaultStore provides a mock function with given fields:
func (_m *DockerArtifactStoreRepository) FindActiveDefaultStore() (*repository.DockerArtifactStore, error) {
	ret := _m.Called()

	var r0 *repository.DockerArtifactStore
	if rf, ok := ret.Get(0).(func() *repository.DockerArtifactStore); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.DockerArtifactStore)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAll provides a mock function with given fields:
func (_m *DockerArtifactStoreRepository) FindAll() ([]repository.DockerArtifactStore, error) {
	ret := _m.Called()

	var r0 []repository.DockerArtifactStore
	if rf, ok := ret.Get(0).(func() []repository.DockerArtifactStore); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]repository.DockerArtifactStore)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAllActiveForAutocomplete provides a mock function with given fields:
func (_m *DockerArtifactStoreRepository) FindAllActiveForAutocomplete() ([]repository.DockerArtifactStore, error) {
	ret := _m.Called()

	var r0 []repository.DockerArtifactStore
	if rf, ok := ret.Get(0).(func() []repository.DockerArtifactStore); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]repository.DockerArtifactStore)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindInactive provides a mock function with given fields: storeId
func (_m *DockerArtifactStoreRepository) FindInactive(storeId string) (bool, error) {
	ret := _m.Called(storeId)

	var r0 bool
	if rf, ok := ret.Get(0).(func(string) bool); ok {
		r0 = rf(storeId)
	} else {
		r0 = ret.Get(0).(bool)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(storeId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindOne provides a mock function with given fields: storeId
func (_m *DockerArtifactStoreRepository) FindOne(storeId string) (*repository.DockerArtifactStore, error) {
	ret := _m.Called(storeId)

	var r0 *repository.DockerArtifactStore
	if rf, ok := ret.Get(0).(func(string) *repository.DockerArtifactStore); ok {
		r0 = rf(storeId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.DockerArtifactStore)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(storeId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindOneInactive provides a mock function with given fields: storeId
func (_m *DockerArtifactStoreRepository) FindOneInactive(storeId string) (*repository.DockerArtifactStore, error) {
	ret := _m.Called(storeId)

	var r0 *repository.DockerArtifactStore
	if rf, ok := ret.Get(0).(func(string) *repository.DockerArtifactStore); ok {
		r0 = rf(storeId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.DockerArtifactStore)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(storeId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetConnection provides a mock function with given fields:
func (_m *DockerArtifactStoreRepository) GetConnection() *pg.DB {
	ret := _m.Called()

	var r0 *pg.DB
	if rf, ok := ret.Get(0).(func() *pg.DB); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pg.DB)
		}
	}

	return r0
}

// MarkRegistryDeleted provides a mock function with given fields: artifactStore, tx
func (_m *DockerArtifactStoreRepository) MarkRegistryDeleted(artifactStore *repository.DockerArtifactStore, tx *pg.Tx) error {
	ret := _m.Called(artifactStore, tx)

	var r0 error
	if rf, ok := ret.Get(0).(func(*repository.DockerArtifactStore, *pg.Tx) error); ok {
		r0 = rf(artifactStore, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Save provides a mock function with given fields: artifactStore, tx
func (_m *DockerArtifactStoreRepository) Save(artifactStore *repository.DockerArtifactStore, tx *pg.Tx) error {
	ret := _m.Called(artifactStore, tx)

	var r0 error
	if rf, ok := ret.Get(0).(func(*repository.DockerArtifactStore, *pg.Tx) error); ok {
		r0 = rf(artifactStore, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Update provides a mock function with given fields: artifactStore, tx
func (_m *DockerArtifactStoreRepository) Update(artifactStore *repository.DockerArtifactStore, tx *pg.Tx) error {
	ret := _m.Called(artifactStore, tx)

	var r0 error
	if rf, ok := ret.Get(0).(func(*repository.DockerArtifactStore, *pg.Tx) error); ok {
		r0 = rf(artifactStore, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewDockerArtifactStoreRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewDockerArtifactStoreRepository creates a new instance of DockerArtifactStoreRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewDockerArtifactStoreRepository(t mockConstructorTestingTNewDockerArtifactStoreRepository) *DockerArtifactStoreRepository {
	mock := &DockerArtifactStoreRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
