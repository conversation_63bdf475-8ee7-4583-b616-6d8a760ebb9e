// Code generated by mockery v2.42.0. DO NOT EDIT.

package mocks

import (
	context "context"

	pg "github.com/go-pg/pg"
	mock "github.com/stretchr/testify/mock"

	repository "github.com/devtron-labs/devtron/internal/sql/repository/imageTagging"
)

// ImageTaggingRepository is an autogenerated mock type for the ImageTaggingRepository type
type ImageTaggingRepository struct {
	mock.Mock
}

// CommitTx provides a mock function with given fields: tx
func (_m *ImageTaggingRepository) CommitTx(tx *pg.Tx) error {
	ret := _m.Called(tx)

	if len(ret) == 0 {
		panic("no return value specified for CommitTx")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*pg.Tx) error); ok {
		r0 = rf(tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteReleaseTagInBulk provides a mock function with given fields: tx, imageTags
func (_m *ImageTaggingRepository) DeleteReleaseTagInBulk(tx *pg.Tx, imageTags []*repository.ImageTag) error {
	ret := _m.Called(tx, imageTags)

	if len(ret) == 0 {
		panic("no return value specified for DeleteReleaseTagInBulk")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*pg.Tx, []*repository.ImageTag) error); ok {
		r0 = rf(tx, imageTags)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetImageComment provides a mock function with given fields: artifactId
func (_m *ImageTaggingRepository) GetImageComment(artifactId int) (repository.ImageComment, error) {
	ret := _m.Called(artifactId)

	if len(ret) == 0 {
		panic("no return value specified for GetImageComment")
	}

	var r0 repository.ImageComment
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (repository.ImageComment, error)); ok {
		return rf(artifactId)
	}
	if rf, ok := ret.Get(0).(func(int) repository.ImageComment); ok {
		r0 = rf(artifactId)
	} else {
		r0 = ret.Get(0).(repository.ImageComment)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(artifactId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetImageCommentsByArtifactIds provides a mock function with given fields: artifactIds
func (_m *ImageTaggingRepository) GetImageCommentsByArtifactIds(artifactIds []int) ([]*repository.ImageComment, error) {
	ret := _m.Called(artifactIds)

	if len(ret) == 0 {
		panic("no return value specified for GetImageCommentsByArtifactIds")
	}

	var r0 []*repository.ImageComment
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*repository.ImageComment, error)); ok {
		return rf(artifactIds)
	}
	if rf, ok := ret.Get(0).(func([]int) []*repository.ImageComment); ok {
		r0 = rf(artifactIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.ImageComment)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(artifactIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetTagsByAppId provides a mock function with given fields: appId
func (_m *ImageTaggingRepository) GetTagsByAppId(appId int) ([]*repository.ImageTag, error) {
	ret := _m.Called(appId)

	if len(ret) == 0 {
		panic("no return value specified for GetTagsByAppId")
	}

	var r0 []*repository.ImageTag
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]*repository.ImageTag, error)); ok {
		return rf(appId)
	}
	if rf, ok := ret.Get(0).(func(int) []*repository.ImageTag); ok {
		r0 = rf(appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.ImageTag)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetTagsByArtifactId provides a mock function with given fields: artifactId
func (_m *ImageTaggingRepository) GetTagsByArtifactId(artifactId int) ([]*repository.ImageTag, error) {
	ret := _m.Called(artifactId)

	if len(ret) == 0 {
		panic("no return value specified for GetTagsByArtifactId")
	}

	var r0 []*repository.ImageTag
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]*repository.ImageTag, error)); ok {
		return rf(artifactId)
	}
	if rf, ok := ret.Get(0).(func(int) []*repository.ImageTag); ok {
		r0 = rf(artifactId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.ImageTag)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(artifactId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetTagsForAppIds provides a mock function with given fields: ctx, appIds
func (_m *ImageTaggingRepository) GetTagsForAppIds(ctx context.Context, appIds []int) ([]*repository.ImageTag, error) {
	ret := _m.Called(ctx, appIds)

	if len(ret) == 0 {
		panic("no return value specified for GetTagsForAppIds")
	}

	var r0 []*repository.ImageTag
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int) ([]*repository.ImageTag, error)); ok {
		return rf(ctx, appIds)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int) []*repository.ImageTag); ok {
		r0 = rf(ctx, appIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.ImageTag)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int) error); ok {
		r1 = rf(ctx, appIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RollbackTx provides a mock function with given fields: tx
func (_m *ImageTaggingRepository) RollbackTx(tx *pg.Tx) error {
	ret := _m.Called(tx)

	if len(ret) == 0 {
		panic("no return value specified for RollbackTx")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*pg.Tx) error); ok {
		r0 = rf(tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveAuditLogsInBulk provides a mock function with given fields: tx, imageTaggingAudit
func (_m *ImageTaggingRepository) SaveAuditLogsInBulk(tx *pg.Tx, imageTaggingAudit []*repository.ImageTaggingAudit) error {
	ret := _m.Called(tx, imageTaggingAudit)

	if len(ret) == 0 {
		panic("no return value specified for SaveAuditLogsInBulk")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*pg.Tx, []*repository.ImageTaggingAudit) error); ok {
		r0 = rf(tx, imageTaggingAudit)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveImageComment provides a mock function with given fields: tx, imageComment
func (_m *ImageTaggingRepository) SaveImageComment(tx *pg.Tx, imageComment *repository.ImageComment) error {
	ret := _m.Called(tx, imageComment)

	if len(ret) == 0 {
		panic("no return value specified for SaveImageComment")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*pg.Tx, *repository.ImageComment) error); ok {
		r0 = rf(tx, imageComment)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveReleaseTagsInBulk provides a mock function with given fields: tx, imageTags
func (_m *ImageTaggingRepository) SaveReleaseTagsInBulk(tx *pg.Tx, imageTags []*repository.ImageTag) error {
	ret := _m.Called(tx, imageTags)

	if len(ret) == 0 {
		panic("no return value specified for SaveReleaseTagsInBulk")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*pg.Tx, []*repository.ImageTag) error); ok {
		r0 = rf(tx, imageTags)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// StartTx provides a mock function with given fields:
func (_m *ImageTaggingRepository) StartTx() (*pg.Tx, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for StartTx")
	}

	var r0 *pg.Tx
	var r1 error
	if rf, ok := ret.Get(0).(func() (*pg.Tx, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() *pg.Tx); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pg.Tx)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateImageComment provides a mock function with given fields: tx, imageComment
func (_m *ImageTaggingRepository) UpdateImageComment(tx *pg.Tx, imageComment *repository.ImageComment) error {
	ret := _m.Called(tx, imageComment)

	if len(ret) == 0 {
		panic("no return value specified for UpdateImageComment")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*pg.Tx, *repository.ImageComment) error); ok {
		r0 = rf(tx, imageComment)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateReleaseTagInBulk provides a mock function with given fields: tx, imageTags
func (_m *ImageTaggingRepository) UpdateReleaseTagInBulk(tx *pg.Tx, imageTags []*repository.ImageTag) error {
	ret := _m.Called(tx, imageTags)

	if len(ret) == 0 {
		panic("no return value specified for UpdateReleaseTagInBulk")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*pg.Tx, []*repository.ImageTag) error); ok {
		r0 = rf(tx, imageTags)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewImageTaggingRepository creates a new instance of ImageTaggingRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewImageTaggingRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *ImageTaggingRepository {
	mock := &ImageTaggingRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
