/*
 * Copyright (c) 2020-2024. Devtron Inc.
 */

package repository

import (
	"fmt"
	"github.com/devtron-labs/devtron/internal/util"
	"github.com/devtron-labs/devtron/pkg/sql"
	"github.com/go-pg/pg"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestMaterialInfo_Parse(t *testing.T) {
	tests := []struct {
		name    string
		info    string
		wantErr bool
	}{
		{
			name:    "single",
			wantErr: false,
			info: `[
  {
    "material": {
      "git-configuration": {
        "shallow-clone": false,
        "branch": "master",
        "url": "https://github.com/gocd-demo/node-bulletin-board.git"
      },
      "type": "git"
    },
    "changed": false,
    "modifications": [
      {
        "revision": "992382abb91a664b751cd5d2a6eb154915fcd6aa",
        "modified-time": "Jan 16, 2019 3:22:47 PM",
        "data": {}
      }
    ]
  }
]`,
		},
		{
			name:    "multi",
			wantErr: false,
			info: `[
    {
      "material": {
        "git-configuration": {
          "shallow-clone": false,
          "branch": "master",
          "url": "https://github.com/gocd-demo/node-bulletin-board.git"
        },
        "type": "git"
      },
      "changed": false,
      "modifications": [
        {
          "revision": "992382abb91a664b751cd5d2a6eb154915fcd6aa",
          "modified-time": "Jan 16, 2019 3:22:47 PM",
          "data": {}
        }
      ]
    },
    {
      "material": {
        "plugin-id": "git.fb",
        "scm-configuration": {
          "url": "https://github.com/kumarnishant/dem-app.git",
          "defaultBranch": "master",
          "branchwhitelist": "dev*"
        },
        "type": "scm"
      },
      "changed": true,
      "modifications": [
        {
          "revision": "92235640cb6aad48164eeda37b108a8f45d095d7",
          "modified-time": "Apr 26, 2019 9:20:07 AM",
          "nrevision":"abc",
          "data": {
            "CURRENT_BRANCH": "master"
          }
        }
      ]
    }
  ]`,
		},
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mi := &CiArtifact{MaterialInfo: tt.info, DataSource: "GOCD"}
			got, err := mi.ParseMaterialInfo()
			if (err != nil) != tt.wantErr {
				t.Errorf("MaterialInfo.ParseGocdInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			fmt.Println(got)
		})
	}
}

func TestIsArtifactAvailableForDeployment(t *testing.T) {
	// Define test cases
	tests := []struct {
		name              string
		pipelineId        int
		parentPipelineId  int
		artifact          *CiArtifact
		parentStage       string
		pluginStage       string
		deployStage       string
		expectedAvailable bool
		expectedError     error
	}{
		{
			name:             "Artifact available for deployment - conditions met",
			pipelineId:       2,
			parentPipelineId: 3,
			artifact: &CiArtifact{
				Id:          3,
				ComponentId: 3, // matches parentPipelineId
				DataSource:  "pre_cd",
			},
			parentStage:       "pre_cd",
			pluginStage:       "pre_cd",
			deployStage:       "DEPLOY",
			expectedAvailable: true,
			expectedError:     nil,
		},
		{
			name:             "Artifact not available - component id mismatch",
			pipelineId:       4,
			parentPipelineId: 3,
			artifact: &CiArtifact{
				Id:          2,
				ComponentId: 1, // does not match parentPipelineId
				DataSource:  "CI_RUNNER",
			},
			parentStage:       "CI",
			pluginStage:       "",
			deployStage:       "DEPLOY",
			expectedAvailable: false,
			expectedError:     nil,
		},
		{
			name:             "Artifact not available - database query error",
			pipelineId:       2,
			parentPipelineId: 3,
			artifact: &CiArtifact{
				Id:          9,
				ComponentId: 7, // matches parentPipelineId
				DataSource:  "WEBHOOK",
			},
			parentStage:       "pre_cd",
			pluginStage:       "pre_cd",
			deployStage:       "DEPLOY",
			expectedAvailable: false,
			expectedError:     pg.ErrNoRows, // Simulating no rows found error
		},
		{
			name:             "Artifact available via promotion",
			pipelineId:       5,
			parentPipelineId: 4,
			artifact: &CiArtifact{
				Id:          8,
				ComponentId: 2, // matches parentPipelineId
				DataSource:  "post_cd",
			},
			parentStage:       "post_cd",
			pluginStage:       "post_cd",
			deployStage:       "DEPLOY",
			expectedAvailable: true,
			expectedError:     nil,
		},
	}

	// Loop through test cases
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			logger, err := util.NewSugardLogger()
			if err != nil {
				t.Error(err)
			}
			config, err := sql.GetConfig()
			if err != nil {
				t.Error(err)
			}
			db, err := sql.NewDbConnection(config, logger)
			if err != nil {
				t.Fatalf("error connecting to the database: %v", err)
			}
			defer db.Close()

			impl := CiArtifactRepositoryImpl{
				logger:       logger,
				dbConnection: db,
			}
			available, err := impl.IsArtifactAvailableForDeployment(tt.pipelineId, tt.parentPipelineId, tt.artifact, tt.parentStage, tt.pluginStage, tt.deployStage)
			// matching the result with expected result
			assert.Equal(t, tt.expectedAvailable, available)
			if tt.expectedError != nil {
				assert.Equal(t, tt.expectedError, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
