// Code generated by mockery v2.20.0. DO NOT EDIT.

package mocks

import (
	chartConfig "github.com/devtron-labs/devtron/internal/sql/repository/chartConfig/configMapRepository"
	mock "github.com/stretchr/testify/mock"
)

// ConfigMapRepository is an autogenerated mock type for the ConfigMapRepository type
type ConfigMapRepository struct {
	mock.Mock
}

// CreateAppLevel provides a mock function with given fields: model
func (_m *ConfigMapRepository) CreateAppLevel(model *chartConfig.ConfigMapAppModel) (*chartConfig.ConfigMapAppModel, error) {
	ret := _m.Called(model)

	var r0 *chartConfig.ConfigMapAppModel
	var r1 error
	if rf, ok := ret.Get(0).(func(*chartConfig.ConfigMapAppModel) (*chartConfig.ConfigMapAppModel, error)); ok {
		return rf(model)
	}
	if rf, ok := ret.Get(0).(func(*chartConfig.ConfigMapAppModel) *chartConfig.ConfigMapAppModel); ok {
		r0 = rf(model)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartConfig.ConfigMapAppModel)
		}
	}

	if rf, ok := ret.Get(1).(func(*chartConfig.ConfigMapAppModel) error); ok {
		r1 = rf(model)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateEnvLevel provides a mock function with given fields: model
func (_m *ConfigMapRepository) CreateEnvLevel(model *chartConfig.ConfigMapEnvModel) (*chartConfig.ConfigMapEnvModel, error) {
	ret := _m.Called(model)

	var r0 *chartConfig.ConfigMapEnvModel
	var r1 error
	if rf, ok := ret.Get(0).(func(*chartConfig.ConfigMapEnvModel) (*chartConfig.ConfigMapEnvModel, error)); ok {
		return rf(model)
	}
	if rf, ok := ret.Get(0).(func(*chartConfig.ConfigMapEnvModel) *chartConfig.ConfigMapEnvModel); ok {
		r0 = rf(model)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartConfig.ConfigMapEnvModel)
		}
	}

	if rf, ok := ret.Get(1).(func(*chartConfig.ConfigMapEnvModel) error); ok {
		r1 = rf(model)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllAppLevel provides a mock function with given fields:
func (_m *ConfigMapRepository) GetAllAppLevel() ([]chartConfig.ConfigMapAppModel, error) {
	ret := _m.Called()

	var r0 []chartConfig.ConfigMapAppModel
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]chartConfig.ConfigMapAppModel, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []chartConfig.ConfigMapAppModel); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]chartConfig.ConfigMapAppModel)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllEnvLevel provides a mock function with given fields:
func (_m *ConfigMapRepository) GetAllEnvLevel() ([]chartConfig.ConfigMapEnvModel, error) {
	ret := _m.Called()

	var r0 []chartConfig.ConfigMapEnvModel
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]chartConfig.ConfigMapEnvModel, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []chartConfig.ConfigMapEnvModel); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]chartConfig.ConfigMapEnvModel)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByAppIdAndEnvIdEnvLevel provides a mock function with given fields: appId, envId
func (_m *ConfigMapRepository) GetByAppIdAndEnvIdEnvLevel(appId int, envId int) (*chartConfig.ConfigMapEnvModel, error) {
	ret := _m.Called(appId, envId)

	var r0 *chartConfig.ConfigMapEnvModel
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int) (*chartConfig.ConfigMapEnvModel, error)); ok {
		return rf(appId, envId)
	}
	if rf, ok := ret.Get(0).(func(int, int) *chartConfig.ConfigMapEnvModel); ok {
		r0 = rf(appId, envId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartConfig.ConfigMapEnvModel)
		}
	}

	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(appId, envId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByAppIdAppLevel provides a mock function with given fields: appId
func (_m *ConfigMapRepository) GetByAppIdAppLevel(appId int) (*chartConfig.ConfigMapAppModel, error) {
	ret := _m.Called(appId)

	var r0 *chartConfig.ConfigMapAppModel
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*chartConfig.ConfigMapAppModel, error)); ok {
		return rf(appId)
	}
	if rf, ok := ret.Get(0).(func(int) *chartConfig.ConfigMapAppModel); ok {
		r0 = rf(appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartConfig.ConfigMapAppModel)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByIdAppLevel provides a mock function with given fields: id
func (_m *ConfigMapRepository) GetByIdAppLevel(id int) (*chartConfig.ConfigMapAppModel, error) {
	ret := _m.Called(id)

	var r0 *chartConfig.ConfigMapAppModel
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*chartConfig.ConfigMapAppModel, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) *chartConfig.ConfigMapAppModel); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartConfig.ConfigMapAppModel)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByIdEnvLevel provides a mock function with given fields: id
func (_m *ConfigMapRepository) GetByIdEnvLevel(id int) (*chartConfig.ConfigMapEnvModel, error) {
	ret := _m.Called(id)

	var r0 *chartConfig.ConfigMapEnvModel
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*chartConfig.ConfigMapEnvModel, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) *chartConfig.ConfigMapEnvModel); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartConfig.ConfigMapEnvModel)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetEnvLevelByAppId provides a mock function with given fields: appId
func (_m *ConfigMapRepository) GetEnvLevelByAppId(appId int) ([]*chartConfig.ConfigMapEnvModel, error) {
	ret := _m.Called(appId)

	var r0 []*chartConfig.ConfigMapEnvModel
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]*chartConfig.ConfigMapEnvModel, error)); ok {
		return rf(appId)
	}
	if rf, ok := ret.Get(0).(func(int) []*chartConfig.ConfigMapEnvModel); ok {
		r0 = rf(appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*chartConfig.ConfigMapEnvModel)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateAppLevel provides a mock function with given fields: model
func (_m *ConfigMapRepository) UpdateAppLevel(model *chartConfig.ConfigMapAppModel) (*chartConfig.ConfigMapAppModel, error) {
	ret := _m.Called(model)

	var r0 *chartConfig.ConfigMapAppModel
	var r1 error
	if rf, ok := ret.Get(0).(func(*chartConfig.ConfigMapAppModel) (*chartConfig.ConfigMapAppModel, error)); ok {
		return rf(model)
	}
	if rf, ok := ret.Get(0).(func(*chartConfig.ConfigMapAppModel) *chartConfig.ConfigMapAppModel); ok {
		r0 = rf(model)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartConfig.ConfigMapAppModel)
		}
	}

	if rf, ok := ret.Get(1).(func(*chartConfig.ConfigMapAppModel) error); ok {
		r1 = rf(model)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateEnvLevel provides a mock function with given fields: model
func (_m *ConfigMapRepository) UpdateEnvLevel(model *chartConfig.ConfigMapEnvModel) (*chartConfig.ConfigMapEnvModel, error) {
	ret := _m.Called(model)

	var r0 *chartConfig.ConfigMapEnvModel
	var r1 error
	if rf, ok := ret.Get(0).(func(*chartConfig.ConfigMapEnvModel) (*chartConfig.ConfigMapEnvModel, error)); ok {
		return rf(model)
	}
	if rf, ok := ret.Get(0).(func(*chartConfig.ConfigMapEnvModel) *chartConfig.ConfigMapEnvModel); ok {
		r0 = rf(model)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartConfig.ConfigMapEnvModel)
		}
	}

	if rf, ok := ret.Get(1).(func(*chartConfig.ConfigMapEnvModel) error); ok {
		r1 = rf(model)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewConfigMapRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewConfigMapRepository creates a new instance of ConfigMapRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewConfigMapRepository(t mockConstructorTestingTNewConfigMapRepository) *ConfigMapRepository {
	mock := &ConfigMapRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
