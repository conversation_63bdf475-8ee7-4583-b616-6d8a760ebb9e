// Code generated by mockery v2.14.0. DO NOT EDIT.

package mocks

import (
	chartConfig "github.com/devtron-labs/devtron/internal/sql/repository/chartConfig"
	mock "github.com/stretchr/testify/mock"

	pg "github.com/go-pg/pg"
)

// EnvConfigOverrideRepository is an autogenerated mock type for the EnvConfigOverrideRepository type
type EnvConfigOverrideRepository struct {
	mock.Mock
}

// ActiveEnvConfigOverride provides a mock function with given fields: appId, environmentId
func (_m *EnvConfigOverrideRepository) ActiveEnvConfigOverride(appId int, environmentId int) (*chartConfig.EnvConfigOverride, error) {
	ret := _m.Called(appId, environmentId)

	var r0 *chartConfig.EnvConfigOverride
	if rf, ok := ret.Get(0).(func(int, int) *chartConfig.EnvConfigOverride); ok {
		r0 = rf(appId, environmentId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartConfig.EnvConfigOverride)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(appId, environmentId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Delete provides a mock function with given fields: envConfigOverride
func (_m *EnvConfigOverrideRepository) Delete(envConfigOverride *chartConfig.EnvConfigOverride) error {
	ret := _m.Called(envConfigOverride)

	var r0 error
	if rf, ok := ret.Get(0).(func(*chartConfig.EnvConfigOverride) error); ok {
		r0 = rf(envConfigOverride)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// FindChartByAppIdAndEnvIdAndChartRefId provides a mock function with given fields: appId, targetEnvironmentId, chartRefId
func (_m *EnvConfigOverrideRepository) FindChartByAppIdAndEnvIdAndChartRefId(appId int, targetEnvironmentId int, chartRefId int) (*chartConfig.EnvConfigOverride, error) {
	ret := _m.Called(appId, targetEnvironmentId, chartRefId)

	var r0 *chartConfig.EnvConfigOverride
	if rf, ok := ret.Get(0).(func(int, int, int) *chartConfig.EnvConfigOverride); ok {
		r0 = rf(appId, targetEnvironmentId, chartRefId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartConfig.EnvConfigOverride)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, int, int) error); ok {
		r1 = rf(appId, targetEnvironmentId, chartRefId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindChartForAppByAppIdAndEnvId provides a mock function with given fields: appId, targetEnvironmentId
func (_m *EnvConfigOverrideRepository) FindChartForAppByAppIdAndEnvId(appId int, targetEnvironmentId int) (*chartConfig.EnvConfigOverride, error) {
	ret := _m.Called(appId, targetEnvironmentId)

	var r0 *chartConfig.EnvConfigOverride
	if rf, ok := ret.Get(0).(func(int, int) *chartConfig.EnvConfigOverride); ok {
		r0 = rf(appId, targetEnvironmentId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartConfig.EnvConfigOverride)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(appId, targetEnvironmentId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindChartRefIdsForLatestChartForAppByAppIdAndEnvIds provides a mock function with given fields: appId, targetEnvironmentIds
func (_m *EnvConfigOverrideRepository) FindChartRefIdsForLatestChartForAppByAppIdAndEnvIds(appId int, targetEnvironmentIds []int) (map[int]int, error) {
	ret := _m.Called(appId, targetEnvironmentIds)

	var r0 map[int]int
	if rf, ok := ret.Get(0).(func(int, []int) map[int]int); ok {
		r0 = rf(appId, targetEnvironmentIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[int]int)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, []int) error); ok {
		r1 = rf(appId, targetEnvironmentIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindLatestChartForAppByAppIdAndEnvId provides a mock function with given fields: appId, targetEnvironmentId
func (_m *EnvConfigOverrideRepository) FindLatestChartForAppByAppIdAndEnvId(appId int, targetEnvironmentId int) (*chartConfig.EnvConfigOverride, error) {
	ret := _m.Called(appId, targetEnvironmentId)

	var r0 *chartConfig.EnvConfigOverride
	if rf, ok := ret.Get(0).(func(int, int) *chartConfig.EnvConfigOverride); ok {
		r0 = rf(appId, targetEnvironmentId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartConfig.EnvConfigOverride)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(appId, targetEnvironmentId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Get provides a mock function with given fields: id
func (_m *EnvConfigOverrideRepository) Get(id int) (*chartConfig.EnvConfigOverride, error) {
	ret := _m.Called(id)

	var r0 *chartConfig.EnvConfigOverride
	if rf, ok := ret.Get(0).(func(int) *chartConfig.EnvConfigOverride); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartConfig.EnvConfigOverride)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByAppIdEnvIdAndChartRefId provides a mock function with given fields: appId, envId, chartRefId
func (_m *EnvConfigOverrideRepository) GetByAppIdEnvIdAndChartRefId(appId int, envId int, chartRefId int) (*chartConfig.EnvConfigOverride, error) {
	ret := _m.Called(appId, envId, chartRefId)

	var r0 *chartConfig.EnvConfigOverride
	if rf, ok := ret.Get(0).(func(int, int, int) *chartConfig.EnvConfigOverride); ok {
		r0 = rf(appId, envId, chartRefId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartConfig.EnvConfigOverride)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, int, int) error); ok {
		r1 = rf(appId, envId, chartRefId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByChartAndEnvironment provides a mock function with given fields: chartId, targetEnvironmentId
func (_m *EnvConfigOverrideRepository) GetByChartAndEnvironment(chartId int, targetEnvironmentId int) (*chartConfig.EnvConfigOverride, error) {
	ret := _m.Called(chartId, targetEnvironmentId)

	var r0 *chartConfig.EnvConfigOverride
	if rf, ok := ret.Get(0).(func(int, int) *chartConfig.EnvConfigOverride); ok {
		r0 = rf(chartId, targetEnvironmentId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartConfig.EnvConfigOverride)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(chartId, targetEnvironmentId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByEnvironment provides a mock function with given fields: targetEnvironmentId
func (_m *EnvConfigOverrideRepository) GetByEnvironment(targetEnvironmentId int) ([]chartConfig.EnvConfigOverride, error) {
	ret := _m.Called(targetEnvironmentId)

	var r0 []chartConfig.EnvConfigOverride
	if rf, ok := ret.Get(0).(func(int) []chartConfig.EnvConfigOverride); ok {
		r0 = rf(targetEnvironmentId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]chartConfig.EnvConfigOverride)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(targetEnvironmentId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetEnvConfigByChartId provides a mock function with given fields: chartId
func (_m *EnvConfigOverrideRepository) GetEnvConfigByChartId(chartId int) ([]chartConfig.EnvConfigOverride, error) {
	ret := _m.Called(chartId)

	var r0 []chartConfig.EnvConfigOverride
	if rf, ok := ret.Get(0).(func(int) []chartConfig.EnvConfigOverride); ok {
		r0 = rf(chartId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]chartConfig.EnvConfigOverride)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(chartId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Save provides a mock function with given fields: _a0
func (_m *EnvConfigOverrideRepository) Save(_a0 *chartConfig.EnvConfigOverride) error {
	ret := _m.Called(_a0)

	var r0 error
	if rf, ok := ret.Get(0).(func(*chartConfig.EnvConfigOverride) error); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveWithTxn provides a mock function with given fields: model, tx
func (_m *EnvConfigOverrideRepository) SaveWithTxn(model *chartConfig.EnvConfigOverride, tx *pg.Tx) error {
	ret := _m.Called(model, tx)

	var r0 error
	if rf, ok := ret.Get(0).(func(*chartConfig.EnvConfigOverride, *pg.Tx) error); ok {
		r0 = rf(model, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Update provides a mock function with given fields: envConfigOverride
func (_m *EnvConfigOverrideRepository) Update(envConfigOverride *chartConfig.EnvConfigOverride) (*chartConfig.EnvConfigOverride, error) {
	ret := _m.Called(envConfigOverride)

	var r0 *chartConfig.EnvConfigOverride
	if rf, ok := ret.Get(0).(func(*chartConfig.EnvConfigOverride) *chartConfig.EnvConfigOverride); ok {
		r0 = rf(envConfigOverride)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartConfig.EnvConfigOverride)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(*chartConfig.EnvConfigOverride) error); ok {
		r1 = rf(envConfigOverride)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateEnvConfigStatus provides a mock function with given fields: config
func (_m *EnvConfigOverrideRepository) UpdateEnvConfigStatus(config *chartConfig.EnvConfigOverride) error {
	ret := _m.Called(config)

	var r0 error
	if rf, ok := ret.Get(0).(func(*chartConfig.EnvConfigOverride) error); ok {
		r0 = rf(config)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateProperties provides a mock function with given fields: config
func (_m *EnvConfigOverrideRepository) UpdateProperties(config *chartConfig.EnvConfigOverride) error {
	ret := _m.Called(config)

	var r0 error
	if rf, ok := ret.Get(0).(func(*chartConfig.EnvConfigOverride) error); ok {
		r0 = rf(config)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateWithTxn provides a mock function with given fields: envConfigOverride, tx
func (_m *EnvConfigOverrideRepository) UpdateWithTxn(envConfigOverride *chartConfig.EnvConfigOverride, tx *pg.Tx) (*chartConfig.EnvConfigOverride, error) {
	ret := _m.Called(envConfigOverride, tx)

	var r0 *chartConfig.EnvConfigOverride
	if rf, ok := ret.Get(0).(func(*chartConfig.EnvConfigOverride, *pg.Tx) *chartConfig.EnvConfigOverride); ok {
		r0 = rf(envConfigOverride, tx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartConfig.EnvConfigOverride)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(*chartConfig.EnvConfigOverride, *pg.Tx) error); ok {
		r1 = rf(envConfigOverride, tx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewEnvConfigOverrideRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewEnvConfigOverrideRepository creates a new instance of EnvConfigOverrideRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewEnvConfigOverrideRepository(t mockConstructorTestingTNewEnvConfigOverrideRepository) *EnvConfigOverrideRepository {
	mock := &EnvConfigOverrideRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
