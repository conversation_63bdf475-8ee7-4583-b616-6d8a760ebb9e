// Code generated by mockery v2.20.0. DO NOT EDIT.

package mocks

import (
	chartConfig "github.com/devtron-labs/devtron/internal/sql/repository/chartConfig"
	mock "github.com/stretchr/testify/mock"

	models "github.com/devtron-labs/devtron/internal/sql/models"
)

// PipelineOverrideRepository is an autogenerated mock type for the PipelineOverrideRepository type
type PipelineOverrideRepository struct {
	mock.Mock
}

// FetchHelmTypePipelineOverridesForStatusUpdate provides a mock function with given fields:
func (_m *PipelineOverrideRepository) FetchHelmTypePipelineOverridesForStatusUpdate() ([]*chartConfig.PipelineOverride, error) {
	ret := _m.Called()

	var r0 []*chartConfig.PipelineOverride
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]*chartConfig.PipelineOverride, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []*chartConfig.PipelineOverride); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*chartConfig.PipelineOverride)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindById provides a mock function with given fields: id
func (_m *PipelineOverrideRepository) FindById(id int) (*chartConfig.PipelineOverride, error) {
	ret := _m.Called(id)

	var r0 *chartConfig.PipelineOverride
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*chartConfig.PipelineOverride, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) *chartConfig.PipelineOverride); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartConfig.PipelineOverride)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByPipelineTriggerGitHash provides a mock function with given fields: gitHash
func (_m *PipelineOverrideRepository) FindByPipelineTriggerGitHash(gitHash string) (*chartConfig.PipelineOverride, error) {
	ret := _m.Called(gitHash)

	var r0 *chartConfig.PipelineOverride
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*chartConfig.PipelineOverride, error)); ok {
		return rf(gitHash)
	}
	if rf, ok := ret.Get(0).(func(string) *chartConfig.PipelineOverride); ok {
		r0 = rf(gitHash)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartConfig.PipelineOverride)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(gitHash)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindLatestByAppIdAndEnvId provides a mock function with given fields: appId, environmentId, deploymentAppType
func (_m *PipelineOverrideRepository) FindLatestByAppIdAndEnvId(appId int, environmentId int, deploymentAppType string) (*chartConfig.PipelineOverride, error) {
	ret := _m.Called(appId, environmentId, deploymentAppType)

	var r0 *chartConfig.PipelineOverride
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int, string) (*chartConfig.PipelineOverride, error)); ok {
		return rf(appId, environmentId, deploymentAppType)
	}
	if rf, ok := ret.Get(0).(func(int, int, string) *chartConfig.PipelineOverride); ok {
		r0 = rf(appId, environmentId, deploymentAppType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartConfig.PipelineOverride)
		}
	}

	if rf, ok := ret.Get(1).(func(int, int, string) error); ok {
		r1 = rf(appId, environmentId, deploymentAppType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindLatestByCdWorkflowId provides a mock function with given fields: cdWorkflowId
func (_m *PipelineOverrideRepository) FindLatestByCdWorkflowId(cdWorkflowId int) (*chartConfig.PipelineOverride, error) {
	ret := _m.Called(cdWorkflowId)

	var r0 *chartConfig.PipelineOverride
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*chartConfig.PipelineOverride, error)); ok {
		return rf(cdWorkflowId)
	}
	if rf, ok := ret.Get(0).(func(int) *chartConfig.PipelineOverride); ok {
		r0 = rf(cdWorkflowId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartConfig.PipelineOverride)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(cdWorkflowId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAllRelease provides a mock function with given fields: appId, environmentId
func (_m *PipelineOverrideRepository) GetAllRelease(appId int, environmentId int) ([]*chartConfig.PipelineOverride, error) {
	ret := _m.Called(appId, environmentId)

	var r0 []*chartConfig.PipelineOverride
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int) ([]*chartConfig.PipelineOverride, error)); ok {
		return rf(appId, environmentId)
	}
	if rf, ok := ret.Get(0).(func(int, int) []*chartConfig.PipelineOverride); ok {
		r0 = rf(appId, environmentId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*chartConfig.PipelineOverride)
		}
	}

	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(appId, environmentId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByDeployedImage provides a mock function with given fields: appId, environmentId, images
func (_m *PipelineOverrideRepository) GetByDeployedImage(appId int, environmentId int, images []string) (*chartConfig.PipelineOverride, error) {
	ret := _m.Called(appId, environmentId, images)

	var r0 *chartConfig.PipelineOverride
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int, []string) (*chartConfig.PipelineOverride, error)); ok {
		return rf(appId, environmentId, images)
	}
	if rf, ok := ret.Get(0).(func(int, int, []string) *chartConfig.PipelineOverride); ok {
		r0 = rf(appId, environmentId, images)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartConfig.PipelineOverride)
		}
	}

	if rf, ok := ret.Get(1).(func(int, int, []string) error); ok {
		r1 = rf(appId, environmentId, images)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByPipelineIdAndReleaseNo provides a mock function with given fields: pipelineId, releaseNo
func (_m *PipelineOverrideRepository) GetByPipelineIdAndReleaseNo(pipelineId int, releaseNo int) ([]*chartConfig.PipelineOverride, error) {
	ret := _m.Called(pipelineId, releaseNo)

	var r0 []*chartConfig.PipelineOverride
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int) ([]*chartConfig.PipelineOverride, error)); ok {
		return rf(pipelineId, releaseNo)
	}
	if rf, ok := ret.Get(0).(func(int, int) []*chartConfig.PipelineOverride); ok {
		r0 = rf(pipelineId, releaseNo)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*chartConfig.PipelineOverride)
		}
	}

	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(pipelineId, releaseNo)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCurrentPipelineReleaseCounter provides a mock function with given fields: pipelineId
func (_m *PipelineOverrideRepository) GetCurrentPipelineReleaseCounter(pipelineId int) (int, error) {
	ret := _m.Called(pipelineId)

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (int, error)); ok {
		return rf(pipelineId)
	}
	if rf, ok := ret.Get(0).(func(int) int); ok {
		r0 = rf(pipelineId)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(pipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLatestConfigByEnvironmentConfigOverrideId provides a mock function with given fields: envConfigOverrideId
func (_m *PipelineOverrideRepository) GetLatestConfigByEnvironmentConfigOverrideId(envConfigOverrideId int) (*chartConfig.PipelineOverride, error) {
	ret := _m.Called(envConfigOverrideId)

	var r0 *chartConfig.PipelineOverride
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*chartConfig.PipelineOverride, error)); ok {
		return rf(envConfigOverrideId)
	}
	if rf, ok := ret.Get(0).(func(int) *chartConfig.PipelineOverride); ok {
		r0 = rf(envConfigOverrideId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartConfig.PipelineOverride)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(envConfigOverrideId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLatestConfigByRequestIdentifier provides a mock function with given fields: requestIdentifier
func (_m *PipelineOverrideRepository) GetLatestConfigByRequestIdentifier(requestIdentifier string) (*chartConfig.PipelineOverride, error) {
	ret := _m.Called(requestIdentifier)

	var r0 *chartConfig.PipelineOverride
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*chartConfig.PipelineOverride, error)); ok {
		return rf(requestIdentifier)
	}
	if rf, ok := ret.Get(0).(func(string) *chartConfig.PipelineOverride); ok {
		r0 = rf(requestIdentifier)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartConfig.PipelineOverride)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(requestIdentifier)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLatestRelease provides a mock function with given fields: appId, environmentId
func (_m *PipelineOverrideRepository) GetLatestRelease(appId int, environmentId int) (*chartConfig.PipelineOverride, error) {
	ret := _m.Called(appId, environmentId)

	var r0 *chartConfig.PipelineOverride
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int) (*chartConfig.PipelineOverride, error)); ok {
		return rf(appId, environmentId)
	}
	if rf, ok := ret.Get(0).(func(int, int) *chartConfig.PipelineOverride); ok {
		r0 = rf(appId, environmentId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*chartConfig.PipelineOverride)
		}
	}

	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(appId, environmentId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLatestReleaseByPipelineIds provides a mock function with given fields: pipelineIds
func (_m *PipelineOverrideRepository) GetLatestReleaseByPipelineIds(pipelineIds []int) ([]*chartConfig.PipelineOverride, error) {
	ret := _m.Called(pipelineIds)

	var r0 []*chartConfig.PipelineOverride
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*chartConfig.PipelineOverride, error)); ok {
		return rf(pipelineIds)
	}
	if rf, ok := ret.Get(0).(func([]int) []*chartConfig.PipelineOverride); ok {
		r0 = rf(pipelineIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*chartConfig.PipelineOverride)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(pipelineIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLatestReleaseDeploymentType provides a mock function with given fields: pipelineIds
func (_m *PipelineOverrideRepository) GetLatestReleaseDeploymentType(pipelineIds []int) ([]*chartConfig.PipelineOverride, error) {
	ret := _m.Called(pipelineIds)

	var r0 []*chartConfig.PipelineOverride
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*chartConfig.PipelineOverride, error)); ok {
		return rf(pipelineIds)
	}
	if rf, ok := ret.Get(0).(func([]int) []*chartConfig.PipelineOverride); ok {
		r0 = rf(pipelineIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*chartConfig.PipelineOverride)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(pipelineIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Save provides a mock function with given fields: _a0
func (_m *PipelineOverrideRepository) Save(_a0 *chartConfig.PipelineOverride) error {
	ret := _m.Called(_a0)

	var r0 error
	if rf, ok := ret.Get(0).(func(*chartConfig.PipelineOverride) error); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Update provides a mock function with given fields: pipelineOverride
func (_m *PipelineOverrideRepository) Update(pipelineOverride *chartConfig.PipelineOverride) error {
	ret := _m.Called(pipelineOverride)

	var r0 error
	if rf, ok := ret.Get(0).(func(*chartConfig.PipelineOverride) error); ok {
		r0 = rf(pipelineOverride)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateStatusByRequestIdentifier provides a mock function with given fields: requestId, newStatus
func (_m *PipelineOverrideRepository) UpdateStatusByRequestIdentifier(requestId string, newStatus models.ChartStatus) (int, error) {
	ret := _m.Called(requestId, newStatus)

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(string, models.ChartStatus) (int, error)); ok {
		return rf(requestId, newStatus)
	}
	if rf, ok := ret.Get(0).(func(string, models.ChartStatus) int); ok {
		r0 = rf(requestId, newStatus)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(string, models.ChartStatus) error); ok {
		r1 = rf(requestId, newStatus)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewPipelineOverrideRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewPipelineOverrideRepository creates a new instance of PipelineOverrideRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewPipelineOverrideRepository(t mockConstructorTestingTNewPipelineOverrideRepository) *PipelineOverrideRepository {
	mock := &PipelineOverrideRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
