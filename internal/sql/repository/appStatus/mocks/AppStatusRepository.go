// Code generated by mockery v2.14.1. DO NOT EDIT.

package mocks

import (
	appStatus "github.com/devtron-labs/devtron/internal/sql/repository/appStatus"
	mock "github.com/stretchr/testify/mock"

	pg "github.com/go-pg/pg"
)

// AppStatusRepository is an autogenerated mock type for the AppStatusRepository type
type AppStatusRepository struct {
	mock.Mock
}

// Create provides a mock function with given fields: container
func (_m *AppStatusRepository) Create(container appStatus.AppStatusContainer) error {
	ret := _m.Called(container)

	var r0 error
	if rf, ok := ret.Get(0).(func(appStatus.AppStatusContainer) error); ok {
		r0 = rf(container)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Delete provides a mock function with given fields: tx, appId, envId
func (_m *AppStatusRepository) Delete(tx *pg.Tx, appId int, envId int) error {
	ret := _m.Called(tx, appId, envId)

	var r0 error
	if rf, ok := ret.Get(0).(func(*pg.Tx, int, int) error); ok {
		r0 = rf(tx, appId, envId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteWithAppId provides a mock function with given fields: tx, appId
func (_m *AppStatusRepository) DeleteWithAppId(tx *pg.Tx, appId int) error {
	ret := _m.Called(tx, appId)

	var r0 error
	if rf, ok := ret.Get(0).(func(*pg.Tx, int) error); ok {
		r0 = rf(tx, appId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteWithEnvId provides a mock function with given fields: tx, envId
func (_m *AppStatusRepository) DeleteWithEnvId(tx *pg.Tx, envId int) error {
	ret := _m.Called(tx, envId)

	var r0 error
	if rf, ok := ret.Get(0).(func(*pg.Tx, int) error); ok {
		r0 = rf(tx, envId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Get provides a mock function with given fields: appId, envId
func (_m *AppStatusRepository) Get(appId int, envId int) (appStatus.AppStatusContainer, error) {
	ret := _m.Called(appId, envId)

	var r0 appStatus.AppStatusContainer
	if rf, ok := ret.Get(0).(func(int, int) appStatus.AppStatusContainer); ok {
		r0 = rf(appId, envId)
	} else {
		r0 = ret.Get(0).(appStatus.AppStatusContainer)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(appId, envId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetConnection provides a mock function with given fields:
func (_m *AppStatusRepository) GetConnection() *pg.DB {
	ret := _m.Called()

	var r0 *pg.DB
	if rf, ok := ret.Get(0).(func() *pg.DB); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pg.DB)
		}
	}

	return r0
}

// Update provides a mock function with given fields: container
func (_m *AppStatusRepository) Update(container appStatus.AppStatusContainer) error {
	ret := _m.Called(container)

	var r0 error
	if rf, ok := ret.Get(0).(func(appStatus.AppStatusContainer) error); ok {
		r0 = rf(container)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewAppStatusRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewAppStatusRepository creates a new instance of AppStatusRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewAppStatusRepository(t mockConstructorTestingTNewAppStatusRepository) *AppStatusRepository {
	mock := &AppStatusRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
