// Code generated by mockery v2.14.0. DO NOT EDIT.

package mocks

import (
	appWorkflow "github.com/devtron-labs/devtron/internal/sql/repository/appWorkflow"
	mock "github.com/stretchr/testify/mock"

	pg "github.com/go-pg/pg"
)

// AppWorkflowRepository is an autogenerated mock type for the AppWorkflowRepository type
type AppWorkflowRepository struct {
	mock.Mock
}

// DeleteAppWorkflow provides a mock function with given fields: _a0, tx
func (_m *AppWorkflowRepository) DeleteAppWorkflow(_a0 *appWorkflow.AppWorkflow, tx *pg.Tx) error {
	ret := _m.Called(_a0, tx)

	var r0 error
	if rf, ok := ret.Get(0).(func(*appWorkflow.AppWorkflow, *pg.Tx) error); ok {
		r0 = rf(_a0, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteAppWorkflowMapping provides a mock function with given fields: _a0, tx
func (_m *AppWorkflowRepository) DeleteAppWorkflowMapping(_a0 *appWorkflow.AppWorkflowMapping, tx *pg.Tx) error {
	ret := _m.Called(_a0, tx)

	var r0 error
	if rf, ok := ret.Get(0).(func(*appWorkflow.AppWorkflowMapping, *pg.Tx) error); ok {
		r0 = rf(_a0, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteAppWorkflowMappingsByCdPipelineId provides a mock function with given fields: pipelineId, tx
func (_m *AppWorkflowRepository) DeleteAppWorkflowMappingsByCdPipelineId(pipelineId int, tx *pg.Tx) error {
	ret := _m.Called(pipelineId, tx)

	var r0 error
	if rf, ok := ret.Get(0).(func(int, *pg.Tx) error); ok {
		r0 = rf(pipelineId, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// FindAllWFMappingsByAppId provides a mock function with given fields: appId
func (_m *AppWorkflowRepository) FindAllWFMappingsByAppId(appId int) ([]*appWorkflow.AppWorkflowMapping, error) {
	ret := _m.Called(appId)

	var r0 []*appWorkflow.AppWorkflowMapping
	if rf, ok := ret.Get(0).(func(int) []*appWorkflow.AppWorkflowMapping); ok {
		r0 = rf(appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*appWorkflow.AppWorkflowMapping)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAllWfsHavingCdPipelinesFromSpecificEnvsOnly provides a mock function with given fields: envIds, appIds
func (_m *AppWorkflowRepository) FindAllWfsHavingCdPipelinesFromSpecificEnvsOnly(envIds []int, appIds []int) ([]*appWorkflow.AppWorkflowMapping, error) {
	ret := _m.Called(envIds, appIds)

	var r0 []*appWorkflow.AppWorkflowMapping
	if rf, ok := ret.Get(0).(func([]int, []int) []*appWorkflow.AppWorkflowMapping); ok {
		r0 = rf(envIds, appIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*appWorkflow.AppWorkflowMapping)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func([]int, []int) error); ok {
		r1 = rf(envIds, appIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByAppId provides a mock function with given fields: appId
func (_m *AppWorkflowRepository) FindByAppId(appId int) ([]*appWorkflow.AppWorkflow, error) {
	ret := _m.Called(appId)

	var r0 []*appWorkflow.AppWorkflow
	if rf, ok := ret.Get(0).(func(int) []*appWorkflow.AppWorkflow); ok {
		r0 = rf(appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*appWorkflow.AppWorkflow)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByAppIds provides a mock function with given fields: appIds
func (_m *AppWorkflowRepository) FindByAppIds(appIds []int) ([]*appWorkflow.AppWorkflow, error) {
	ret := _m.Called(appIds)

	var r0 []*appWorkflow.AppWorkflow
	if rf, ok := ret.Get(0).(func([]int) []*appWorkflow.AppWorkflow); ok {
		r0 = rf(appIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*appWorkflow.AppWorkflow)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(appIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByCDPipelineIds provides a mock function with given fields: cdPipelineIds
func (_m *AppWorkflowRepository) FindByCDPipelineIds(cdPipelineIds []int) ([]*appWorkflow.AppWorkflowMapping, error) {
	ret := _m.Called(cdPipelineIds)

	var r0 []*appWorkflow.AppWorkflowMapping
	if rf, ok := ret.Get(0).(func([]int) []*appWorkflow.AppWorkflowMapping); ok {
		r0 = rf(cdPipelineIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*appWorkflow.AppWorkflowMapping)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(cdPipelineIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByComponent provides a mock function with given fields: id, componentType
func (_m *AppWorkflowRepository) FindByComponent(id int, componentType string) ([]*appWorkflow.AppWorkflowMapping, error) {
	ret := _m.Called(id, componentType)

	var r0 []*appWorkflow.AppWorkflowMapping
	if rf, ok := ret.Get(0).(func(int, string) []*appWorkflow.AppWorkflowMapping); ok {
		r0 = rf(id, componentType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*appWorkflow.AppWorkflowMapping)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, string) error); ok {
		r1 = rf(id, componentType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindById provides a mock function with given fields: id
func (_m *AppWorkflowRepository) FindById(id int) (*appWorkflow.AppWorkflow, error) {
	ret := _m.Called(id)

	var r0 *appWorkflow.AppWorkflow
	if rf, ok := ret.Get(0).(func(int) *appWorkflow.AppWorkflow); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*appWorkflow.AppWorkflow)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByIdAndAppId provides a mock function with given fields: id, appId
func (_m *AppWorkflowRepository) FindByIdAndAppId(id int, appId int) (*appWorkflow.AppWorkflow, error) {
	ret := _m.Called(id, appId)

	var r0 *appWorkflow.AppWorkflow
	if rf, ok := ret.Get(0).(func(int, int) *appWorkflow.AppWorkflow); ok {
		r0 = rf(id, appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*appWorkflow.AppWorkflow)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(id, appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByIds provides a mock function with given fields: ids
func (_m *AppWorkflowRepository) FindByIds(ids []int) (*appWorkflow.AppWorkflow, error) {
	ret := _m.Called(ids)

	var r0 *appWorkflow.AppWorkflow
	if rf, ok := ret.Get(0).(func([]int) *appWorkflow.AppWorkflow); ok {
		r0 = rf(ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*appWorkflow.AppWorkflow)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByNameAndAppId provides a mock function with given fields: name, appId
func (_m *AppWorkflowRepository) FindByNameAndAppId(name string, appId int) (*appWorkflow.AppWorkflow, error) {
	ret := _m.Called(name, appId)

	var r0 *appWorkflow.AppWorkflow
	if rf, ok := ret.Get(0).(func(string, int) *appWorkflow.AppWorkflow); ok {
		r0 = rf(name, appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*appWorkflow.AppWorkflow)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string, int) error); ok {
		r1 = rf(name, appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByTypeAndComponentId provides a mock function with given fields: wfId, componentId, componentType
func (_m *AppWorkflowRepository) FindByTypeAndComponentId(wfId int, componentId int, componentType string) (*appWorkflow.AppWorkflowMapping, error) {
	ret := _m.Called(wfId, componentId, componentType)

	var r0 *appWorkflow.AppWorkflowMapping
	if rf, ok := ret.Get(0).(func(int, int, string) *appWorkflow.AppWorkflowMapping); ok {
		r0 = rf(wfId, componentId, componentType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*appWorkflow.AppWorkflowMapping)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, int, string) error); ok {
		r1 = rf(wfId, componentId, componentType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByWorkflowId provides a mock function with given fields: workflowId
func (_m *AppWorkflowRepository) FindByWorkflowId(workflowId int) ([]*appWorkflow.AppWorkflowMapping, error) {
	ret := _m.Called(workflowId)

	var r0 []*appWorkflow.AppWorkflowMapping
	if rf, ok := ret.Get(0).(func(int) []*appWorkflow.AppWorkflowMapping); ok {
		r0 = rf(workflowId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*appWorkflow.AppWorkflowMapping)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(workflowId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByWorkflowIds provides a mock function with given fields: workflowIds
func (_m *AppWorkflowRepository) FindByWorkflowIds(workflowIds []int) ([]*appWorkflow.AppWorkflowMapping, error) {
	ret := _m.Called(workflowIds)

	var r0 []*appWorkflow.AppWorkflowMapping
	if rf, ok := ret.Get(0).(func([]int) []*appWorkflow.AppWorkflowMapping); ok {
		r0 = rf(workflowIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*appWorkflow.AppWorkflowMapping)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(workflowIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindChildCDIdsByParentCDPipelineId provides a mock function with given fields: cdPipelineId
func (_m *AppWorkflowRepository) FindChildCDIdsByParentCDPipelineId(cdPipelineId int) ([]int, error) {
	ret := _m.Called(cdPipelineId)

	var r0 []int
	if rf, ok := ret.Get(0).(func(int) []int); ok {
		r0 = rf(cdPipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(cdPipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindCiPipelineIdsFromAppWfIds provides a mock function with given fields: appWfIds
func (_m *AppWorkflowRepository) FindCiPipelineIdsFromAppWfIds(appWfIds []int) ([]int, error) {
	ret := _m.Called(appWfIds)

	var r0 []int
	if rf, ok := ret.Get(0).(func([]int) []int); ok {
		r0 = rf(appWfIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(appWfIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindMappingByAppIds provides a mock function with given fields: appIds
func (_m *AppWorkflowRepository) FindMappingByAppIds(appIds []int) ([]*appWorkflow.AppWorkflowMapping, error) {
	ret := _m.Called(appIds)

	var r0 []*appWorkflow.AppWorkflowMapping
	if rf, ok := ret.Get(0).(func([]int) []*appWorkflow.AppWorkflowMapping); ok {
		r0 = rf(appIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*appWorkflow.AppWorkflowMapping)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(appIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindMappingsOfWfWithSpecificCIPipelineIds provides a mock function with given fields: ciPipelineIds
func (_m *AppWorkflowRepository) FindMappingsOfWfWithSpecificCIPipelineIds(ciPipelineIds []int) ([]*appWorkflow.AppWorkflowMapping, error) {
	ret := _m.Called(ciPipelineIds)

	var r0 []*appWorkflow.AppWorkflowMapping
	if rf, ok := ret.Get(0).(func([]int) []*appWorkflow.AppWorkflowMapping); ok {
		r0 = rf(ciPipelineIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*appWorkflow.AppWorkflowMapping)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(ciPipelineIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindWFAllMappingByWorkflowId provides a mock function with given fields: workflowId
func (_m *AppWorkflowRepository) FindWFAllMappingByWorkflowId(workflowId int) ([]*appWorkflow.AppWorkflowMapping, error) {
	ret := _m.Called(workflowId)

	var r0 []*appWorkflow.AppWorkflowMapping
	if rf, ok := ret.Get(0).(func(int) []*appWorkflow.AppWorkflowMapping); ok {
		r0 = rf(workflowId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*appWorkflow.AppWorkflowMapping)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(workflowId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindWFCDMappingByCDPipelineId provides a mock function with given fields: cdPipelineId
func (_m *AppWorkflowRepository) FindWFCDMappingByCDPipelineId(cdPipelineId int) (*appWorkflow.AppWorkflowMapping, error) {
	ret := _m.Called(cdPipelineId)

	var r0 *appWorkflow.AppWorkflowMapping
	if rf, ok := ret.Get(0).(func(int) *appWorkflow.AppWorkflowMapping); ok {
		r0 = rf(cdPipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*appWorkflow.AppWorkflowMapping)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(cdPipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindWFCDMappingByCIPipelineId provides a mock function with given fields: ciPipelineId
func (_m *AppWorkflowRepository) FindWFCDMappingByCIPipelineId(ciPipelineId int) ([]*appWorkflow.AppWorkflowMapping, error) {
	ret := _m.Called(ciPipelineId)

	var r0 []*appWorkflow.AppWorkflowMapping
	if rf, ok := ret.Get(0).(func(int) []*appWorkflow.AppWorkflowMapping); ok {
		r0 = rf(ciPipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*appWorkflow.AppWorkflowMapping)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(ciPipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindWFCDMappingByCIPipelineIds provides a mock function with given fields: ciPipelineIds
func (_m *AppWorkflowRepository) FindWFCDMappingByCIPipelineIds(ciPipelineIds []int) ([]*appWorkflow.AppWorkflowMapping, error) {
	ret := _m.Called(ciPipelineIds)

	var r0 []*appWorkflow.AppWorkflowMapping
	if rf, ok := ret.Get(0).(func([]int) []*appWorkflow.AppWorkflowMapping); ok {
		r0 = rf(ciPipelineIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*appWorkflow.AppWorkflowMapping)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(ciPipelineIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindWFCDMappingByExternalCiId provides a mock function with given fields: externalCiId
func (_m *AppWorkflowRepository) FindWFCDMappingByExternalCiId(externalCiId int) ([]*appWorkflow.AppWorkflowMapping, error) {
	ret := _m.Called(externalCiId)

	var r0 []*appWorkflow.AppWorkflowMapping
	if rf, ok := ret.Get(0).(func(int) []*appWorkflow.AppWorkflowMapping); ok {
		r0 = rf(externalCiId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*appWorkflow.AppWorkflowMapping)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(externalCiId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindWFCDMappingByExternalCiIdByIdsIn provides a mock function with given fields: externalCiId
func (_m *AppWorkflowRepository) FindWFCDMappingByExternalCiIdByIdsIn(externalCiId []int) ([]*appWorkflow.AppWorkflowMapping, error) {
	ret := _m.Called(externalCiId)

	var r0 []*appWorkflow.AppWorkflowMapping
	if rf, ok := ret.Get(0).(func([]int) []*appWorkflow.AppWorkflowMapping); ok {
		r0 = rf(externalCiId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*appWorkflow.AppWorkflowMapping)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(externalCiId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindWFCDMappingByParentCDPipelineId provides a mock function with given fields: cdPipelineId
func (_m *AppWorkflowRepository) FindWFCDMappingByParentCDPipelineId(cdPipelineId int) ([]*appWorkflow.AppWorkflowMapping, error) {
	ret := _m.Called(cdPipelineId)

	var r0 []*appWorkflow.AppWorkflowMapping
	if rf, ok := ret.Get(0).(func(int) []*appWorkflow.AppWorkflowMapping); ok {
		r0 = rf(cdPipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*appWorkflow.AppWorkflowMapping)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(cdPipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindWFCIMappingByCIPipelineId provides a mock function with given fields: ciPipelineId
func (_m *AppWorkflowRepository) FindWFCIMappingByCIPipelineId(ciPipelineId int) ([]*appWorkflow.AppWorkflowMapping, error) {
	ret := _m.Called(ciPipelineId)

	var r0 []*appWorkflow.AppWorkflowMapping
	if rf, ok := ret.Get(0).(func(int) []*appWorkflow.AppWorkflowMapping); ok {
		r0 = rf(ciPipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*appWorkflow.AppWorkflowMapping)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(ciPipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindWFCIMappingByWorkflowId provides a mock function with given fields: workflowId
func (_m *AppWorkflowRepository) FindWFCIMappingByWorkflowId(workflowId int) ([]*appWorkflow.AppWorkflowMapping, error) {
	ret := _m.Called(workflowId)

	var r0 []*appWorkflow.AppWorkflowMapping
	if rf, ok := ret.Get(0).(func(int) []*appWorkflow.AppWorkflowMapping); ok {
		r0 = rf(workflowId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*appWorkflow.AppWorkflowMapping)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(workflowId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetParentDetailsByPipelineId provides a mock function with given fields: pipelineId
func (_m *AppWorkflowRepository) GetParentDetailsByPipelineId(pipelineId int) (*appWorkflow.AppWorkflowMapping, error) {
	ret := _m.Called(pipelineId)

	var r0 *appWorkflow.AppWorkflowMapping
	if rf, ok := ret.Get(0).(func(int) *appWorkflow.AppWorkflowMapping); ok {
		r0 = rf(pipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*appWorkflow.AppWorkflowMapping)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(pipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SaveAppWorkflow provides a mock function with given fields: wf
func (_m *AppWorkflowRepository) SaveAppWorkflow(wf *appWorkflow.AppWorkflow) (*appWorkflow.AppWorkflow, error) {
	ret := _m.Called(wf)

	var r0 *appWorkflow.AppWorkflow
	if rf, ok := ret.Get(0).(func(*appWorkflow.AppWorkflow) *appWorkflow.AppWorkflow); ok {
		r0 = rf(wf)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*appWorkflow.AppWorkflow)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(*appWorkflow.AppWorkflow) error); ok {
		r1 = rf(wf)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SaveAppWorkflowMapping provides a mock function with given fields: wf, tx
func (_m *AppWorkflowRepository) SaveAppWorkflowMapping(wf *appWorkflow.AppWorkflowMapping, tx *pg.Tx) (*appWorkflow.AppWorkflowMapping, error) {
	ret := _m.Called(wf, tx)

	var r0 *appWorkflow.AppWorkflowMapping
	if rf, ok := ret.Get(0).(func(*appWorkflow.AppWorkflowMapping, *pg.Tx) *appWorkflow.AppWorkflowMapping); ok {
		r0 = rf(wf, tx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*appWorkflow.AppWorkflowMapping)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(*appWorkflow.AppWorkflowMapping, *pg.Tx) error); ok {
		r1 = rf(wf, tx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SaveAppWorkflowWithTx provides a mock function with given fields: wf, tx
func (_m *AppWorkflowRepository) SaveAppWorkflowWithTx(wf *appWorkflow.AppWorkflow, tx *pg.Tx) (*appWorkflow.AppWorkflow, error) {
	ret := _m.Called(wf, tx)

	var r0 *appWorkflow.AppWorkflow
	if rf, ok := ret.Get(0).(func(*appWorkflow.AppWorkflow, *pg.Tx) *appWorkflow.AppWorkflow); ok {
		r0 = rf(wf, tx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*appWorkflow.AppWorkflow)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(*appWorkflow.AppWorkflow, *pg.Tx) error); ok {
		r1 = rf(wf, tx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateAppWorkflow provides a mock function with given fields: wf
func (_m *AppWorkflowRepository) UpdateAppWorkflow(wf *appWorkflow.AppWorkflow) (*appWorkflow.AppWorkflow, error) {
	ret := _m.Called(wf)

	var r0 *appWorkflow.AppWorkflow
	if rf, ok := ret.Get(0).(func(*appWorkflow.AppWorkflow) *appWorkflow.AppWorkflow); ok {
		r0 = rf(wf)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*appWorkflow.AppWorkflow)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(*appWorkflow.AppWorkflow) error); ok {
		r1 = rf(wf)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewAppWorkflowRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewAppWorkflowRepository creates a new instance of AppWorkflowRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewAppWorkflowRepository(t mockConstructorTestingTNewAppWorkflowRepository) *AppWorkflowRepository {
	mock := &AppWorkflowRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
