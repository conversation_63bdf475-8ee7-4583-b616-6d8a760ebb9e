// Code generated by mockery v2.14.0. DO NOT EDIT.

package mocks

import (
	"github.com/devtron-labs/devtron/pkg/deployment/manifest/deployedAppMetrics/repository"
	mock "github.com/stretchr/testify/mock"
)

// EnvLevelAppMetricsRepository is an autogenerated mock type for the EnvLevelAppMetricsRepository type
type EnvLevelAppMetricsRepository struct {
	mock.Mock
}

// Delete provides a mock function with given fields: metrics
func (_m *EnvLevelAppMetricsRepository) Delete(metrics *repository.EnvLevelAppMetrics) error {
	ret := _m.Called(metrics)

	var r0 error
	if rf, ok := ret.Get(0).(func(*repository.EnvLevelAppMetrics) error); ok {
		r0 = rf(metrics)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// FindByAppId provides a mock function with given fields: appId
func (_m *EnvLevelAppMetricsRepository) FindByAppId(appId int) ([]*repository.EnvLevelAppMetrics, error) {
	ret := _m.Called(appId)

	var r0 []*repository.EnvLevelAppMetrics
	if rf, ok := ret.Get(0).(func(int) []*repository.EnvLevelAppMetrics); ok {
		r0 = rf(appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.EnvLevelAppMetrics)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByAppIdAndEnvId provides a mock function with given fields: appId, envId
func (_m *EnvLevelAppMetricsRepository) FindByAppIdAndEnvId(appId int, envId int) (*repository.EnvLevelAppMetrics, error) {
	ret := _m.Called(appId, envId)

	var r0 *repository.EnvLevelAppMetrics
	if rf, ok := ret.Get(0).(func(int, int) *repository.EnvLevelAppMetrics); ok {
		r0 = rf(appId, envId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.EnvLevelAppMetrics)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(appId, envId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByAppIdAndEnvIds provides a mock function with given fields: appId, envIds
func (_m *EnvLevelAppMetricsRepository) FindByAppIdAndEnvIds(appId int, envIds []int) ([]*repository.EnvLevelAppMetrics, error) {
	ret := _m.Called(appId, envIds)

	var r0 []*repository.EnvLevelAppMetrics
	if rf, ok := ret.Get(0).(func(int, []int) []*repository.EnvLevelAppMetrics); ok {
		r0 = rf(appId, envIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.EnvLevelAppMetrics)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, []int) error); ok {
		r1 = rf(appId, envIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Save provides a mock function with given fields: metrics
func (_m *EnvLevelAppMetricsRepository) Save(metrics *repository.EnvLevelAppMetrics) error {
	ret := _m.Called(metrics)

	var r0 error
	if rf, ok := ret.Get(0).(func(*repository.EnvLevelAppMetrics) error); ok {
		r0 = rf(metrics)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Update provides a mock function with given fields: metrics
func (_m *EnvLevelAppMetricsRepository) Update(metrics *repository.EnvLevelAppMetrics) error {
	ret := _m.Called(metrics)

	var r0 error
	if rf, ok := ret.Get(0).(func(*repository.EnvLevelAppMetrics) error); ok {
		r0 = rf(metrics)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewEnvLevelAppMetricsRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewEnvLevelAppMetricsRepository creates a new instance of EnvLevelAppMetricsRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewEnvLevelAppMetricsRepository(t mockConstructorTestingTNewEnvLevelAppMetricsRepository) *EnvLevelAppMetricsRepository {
	mock := &EnvLevelAppMetricsRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
