// Code generated by mockery v2.20.0. DO NOT EDIT.

package mocks

import (
	repository "github.com/devtron-labs/devtron/internal/sql/repository"
	mock "github.com/stretchr/testify/mock"
)

// SMTPNotificationRepository is an autogenerated mock type for the SMTPNotificationRepository type
type SMTPNotificationRepository struct {
	mock.Mock
}

// FindAll provides a mock function with given fields:
func (_m *SMTPNotificationRepository) FindAll() ([]*repository.SMTPConfig, error) {
	ret := _m.Called()

	var r0 []*repository.SMTPConfig
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]*repository.SMTPConfig, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []*repository.SMTPConfig); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.SMTPConfig)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByIds provides a mock function with given fields: ids
func (_m *SMTPNotificationRepository) FindByIds(ids []*int) ([]*repository.SMTPConfig, error) {
	ret := _m.Called(ids)

	var r0 []*repository.SMTPConfig
	var r1 error
	if rf, ok := ret.Get(0).(func([]*int) ([]*repository.SMTPConfig, error)); ok {
		return rf(ids)
	}
	if rf, ok := ret.Get(0).(func([]*int) []*repository.SMTPConfig); ok {
		r0 = rf(ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.SMTPConfig)
		}
	}

	if rf, ok := ret.Get(1).(func([]*int) error); ok {
		r1 = rf(ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByIdsIn provides a mock function with given fields: ids
func (_m *SMTPNotificationRepository) FindByIdsIn(ids []int) ([]*repository.SMTPConfig, error) {
	ret := _m.Called(ids)

	var r0 []*repository.SMTPConfig
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*repository.SMTPConfig, error)); ok {
		return rf(ids)
	}
	if rf, ok := ret.Get(0).(func([]int) []*repository.SMTPConfig); ok {
		r0 = rf(ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.SMTPConfig)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByTeamIdOrOwnerId provides a mock function with given fields: ownerId
func (_m *SMTPNotificationRepository) FindByTeamIdOrOwnerId(ownerId int32) ([]*repository.SMTPConfig, error) {
	ret := _m.Called(ownerId)

	var r0 []*repository.SMTPConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(int32) ([]*repository.SMTPConfig, error)); ok {
		return rf(ownerId)
	}
	if rf, ok := ret.Get(0).(func(int32) []*repository.SMTPConfig); ok {
		r0 = rf(ownerId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.SMTPConfig)
		}
	}

	if rf, ok := ret.Get(1).(func(int32) error); ok {
		r1 = rf(ownerId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindDefault provides a mock function with given fields:
func (_m *SMTPNotificationRepository) FindDefault() (*repository.SMTPConfig, error) {
	ret := _m.Called()

	var r0 *repository.SMTPConfig
	var r1 error
	if rf, ok := ret.Get(0).(func() (*repository.SMTPConfig, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() *repository.SMTPConfig); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.SMTPConfig)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindOne provides a mock function with given fields: id
func (_m *SMTPNotificationRepository) FindOne(id int) (*repository.SMTPConfig, error) {
	ret := _m.Called(id)

	var r0 *repository.SMTPConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*repository.SMTPConfig, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) *repository.SMTPConfig); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.SMTPConfig)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MarkSMTPConfigDeleted provides a mock function with given fields: smtpConfig
func (_m *SMTPNotificationRepository) MarkSMTPConfigDeleted(smtpConfig *repository.SMTPConfig) error {
	ret := _m.Called(smtpConfig)

	var r0 error
	if rf, ok := ret.Get(0).(func(*repository.SMTPConfig) error); ok {
		r0 = rf(smtpConfig)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveSMTPConfig provides a mock function with given fields: smtpConfig
func (_m *SMTPNotificationRepository) SaveSMTPConfig(smtpConfig *repository.SMTPConfig) (*repository.SMTPConfig, error) {
	ret := _m.Called(smtpConfig)

	var r0 *repository.SMTPConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(*repository.SMTPConfig) (*repository.SMTPConfig, error)); ok {
		return rf(smtpConfig)
	}
	if rf, ok := ret.Get(0).(func(*repository.SMTPConfig) *repository.SMTPConfig); ok {
		r0 = rf(smtpConfig)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.SMTPConfig)
		}
	}

	if rf, ok := ret.Get(1).(func(*repository.SMTPConfig) error); ok {
		r1 = rf(smtpConfig)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateSMTPConfig provides a mock function with given fields: smtpConfig
func (_m *SMTPNotificationRepository) UpdateSMTPConfig(smtpConfig *repository.SMTPConfig) (*repository.SMTPConfig, error) {
	ret := _m.Called(smtpConfig)

	var r0 *repository.SMTPConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(*repository.SMTPConfig) (*repository.SMTPConfig, error)); ok {
		return rf(smtpConfig)
	}
	if rf, ok := ret.Get(0).(func(*repository.SMTPConfig) *repository.SMTPConfig); ok {
		r0 = rf(smtpConfig)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.SMTPConfig)
		}
	}

	if rf, ok := ret.Get(1).(func(*repository.SMTPConfig) error); ok {
		r1 = rf(smtpConfig)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateSMTPConfigDefault provides a mock function with given fields:
func (_m *SMTPNotificationRepository) UpdateSMTPConfigDefault() (bool, error) {
	ret := _m.Called()

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func() (bool, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewSMTPNotificationRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewSMTPNotificationRepository creates a new instance of SMTPNotificationRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewSMTPNotificationRepository(t mockConstructorTestingTNewSMTPNotificationRepository) *SMTPNotificationRepository {
	mock := &SMTPNotificationRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
