// Code generated by mockery v2.30.1. DO NOT EDIT.

package mocks

import (
	pg "github.com/go-pg/pg"
	mock "github.com/stretchr/testify/mock"

	repository "github.com/devtron-labs/devtron/internal/sql/repository"
)

// AttributesRepository is an autogenerated mock type for the AttributesRepository type
type AttributesRepository struct {
	mock.Mock
}

// FindActiveList provides a mock function with given fields:
func (_m *AttributesRepository) FindActiveList() ([]*repository.Attributes, error) {
	ret := _m.Called()

	var r0 []*repository.Attributes
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]*repository.Attributes, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []*repository.Attributes); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.Attributes)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindById provides a mock function with given fields: id
func (_m *AttributesRepository) FindById(id int) (*repository.Attributes, error) {
	ret := _m.Called(id)

	var r0 *repository.Attributes
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*repository.Attributes, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) *repository.Attributes); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.Attributes)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByKey provides a mock function with given fields: key
func (_m *AttributesRepository) FindByKey(key string) (*repository.Attributes, error) {
	ret := _m.Called(key)

	var r0 *repository.Attributes
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*repository.Attributes, error)); ok {
		return rf(key)
	}
	if rf, ok := ret.Get(0).(func(string) *repository.Attributes); ok {
		r0 = rf(key)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.Attributes)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(key)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetConnection provides a mock function with given fields:
func (_m *AttributesRepository) GetConnection() *pg.DB {
	ret := _m.Called()

	var r0 *pg.DB
	if rf, ok := ret.Get(0).(func() *pg.DB); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pg.DB)
		}
	}

	return r0
}

// Save provides a mock function with given fields: model, tx
func (_m *AttributesRepository) Save(model *repository.Attributes, tx *pg.Tx) (*repository.Attributes, error) {
	ret := _m.Called(model, tx)

	var r0 *repository.Attributes
	var r1 error
	if rf, ok := ret.Get(0).(func(*repository.Attributes, *pg.Tx) (*repository.Attributes, error)); ok {
		return rf(model, tx)
	}
	if rf, ok := ret.Get(0).(func(*repository.Attributes, *pg.Tx) *repository.Attributes); ok {
		r0 = rf(model, tx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.Attributes)
		}
	}

	if rf, ok := ret.Get(1).(func(*repository.Attributes, *pg.Tx) error); ok {
		r1 = rf(model, tx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Update provides a mock function with given fields: model, tx
func (_m *AttributesRepository) Update(model *repository.Attributes, tx *pg.Tx) error {
	ret := _m.Called(model, tx)

	var r0 error
	if rf, ok := ret.Get(0).(func(*repository.Attributes, *pg.Tx) error); ok {
		r0 = rf(model, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewAttributesRepository creates a new instance of AttributesRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewAttributesRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *AttributesRepository {
	mock := &AttributesRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
