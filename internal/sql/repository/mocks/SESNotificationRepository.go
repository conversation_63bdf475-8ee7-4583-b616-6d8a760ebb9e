// Code generated by mockery v2.20.0. DO NOT EDIT.

package mocks

import (
	repository "github.com/devtron-labs/devtron/internal/sql/repository"
	mock "github.com/stretchr/testify/mock"
)

// SESNotificationRepository is an autogenerated mock type for the SESNotificationRepository type
type SESNotificationRepository struct {
	mock.Mock
}

// FindAll provides a mock function with given fields:
func (_m *SESNotificationRepository) FindAll() ([]*repository.SESConfig, error) {
	ret := _m.Called()

	var r0 []*repository.SESConfig
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]*repository.SESConfig, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []*repository.SESConfig); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.SESConfig)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByIds provides a mock function with given fields: ids
func (_m *SESNotificationRepository) FindByIds(ids []*int) ([]*repository.SESConfig, error) {
	ret := _m.Called(ids)

	var r0 []*repository.SESConfig
	var r1 error
	if rf, ok := ret.Get(0).(func([]*int) ([]*repository.SESConfig, error)); ok {
		return rf(ids)
	}
	if rf, ok := ret.Get(0).(func([]*int) []*repository.SESConfig); ok {
		r0 = rf(ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.SESConfig)
		}
	}

	if rf, ok := ret.Get(1).(func([]*int) error); ok {
		r1 = rf(ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByIdsIn provides a mock function with given fields: ids
func (_m *SESNotificationRepository) FindByIdsIn(ids []int) ([]*repository.SESConfig, error) {
	ret := _m.Called(ids)

	var r0 []*repository.SESConfig
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*repository.SESConfig, error)); ok {
		return rf(ids)
	}
	if rf, ok := ret.Get(0).(func([]int) []*repository.SESConfig); ok {
		r0 = rf(ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.SESConfig)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByTeamIdOrOwnerId provides a mock function with given fields: ownerId
func (_m *SESNotificationRepository) FindByTeamIdOrOwnerId(ownerId int32) ([]*repository.SESConfig, error) {
	ret := _m.Called(ownerId)

	var r0 []*repository.SESConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(int32) ([]*repository.SESConfig, error)); ok {
		return rf(ownerId)
	}
	if rf, ok := ret.Get(0).(func(int32) []*repository.SESConfig); ok {
		r0 = rf(ownerId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.SESConfig)
		}
	}

	if rf, ok := ret.Get(1).(func(int32) error); ok {
		r1 = rf(ownerId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindDefault provides a mock function with given fields:
func (_m *SESNotificationRepository) FindDefault() (*repository.SESConfig, error) {
	ret := _m.Called()

	var r0 *repository.SESConfig
	var r1 error
	if rf, ok := ret.Get(0).(func() (*repository.SESConfig, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() *repository.SESConfig); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.SESConfig)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindOne provides a mock function with given fields: id
func (_m *SESNotificationRepository) FindOne(id int) (*repository.SESConfig, error) {
	ret := _m.Called(id)

	var r0 *repository.SESConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*repository.SESConfig, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) *repository.SESConfig); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.SESConfig)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MarkSESConfigDeleted provides a mock function with given fields: sesConfig
func (_m *SESNotificationRepository) MarkSESConfigDeleted(sesConfig *repository.SESConfig) error {
	ret := _m.Called(sesConfig)

	var r0 error
	if rf, ok := ret.Get(0).(func(*repository.SESConfig) error); ok {
		r0 = rf(sesConfig)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveSESConfig provides a mock function with given fields: sesConfig
func (_m *SESNotificationRepository) SaveSESConfig(sesConfig *repository.SESConfig) (*repository.SESConfig, error) {
	ret := _m.Called(sesConfig)

	var r0 *repository.SESConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(*repository.SESConfig) (*repository.SESConfig, error)); ok {
		return rf(sesConfig)
	}
	if rf, ok := ret.Get(0).(func(*repository.SESConfig) *repository.SESConfig); ok {
		r0 = rf(sesConfig)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.SESConfig)
		}
	}

	if rf, ok := ret.Get(1).(func(*repository.SESConfig) error); ok {
		r1 = rf(sesConfig)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateSESConfig provides a mock function with given fields: sesConfig
func (_m *SESNotificationRepository) UpdateSESConfig(sesConfig *repository.SESConfig) (*repository.SESConfig, error) {
	ret := _m.Called(sesConfig)

	var r0 *repository.SESConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(*repository.SESConfig) (*repository.SESConfig, error)); ok {
		return rf(sesConfig)
	}
	if rf, ok := ret.Get(0).(func(*repository.SESConfig) *repository.SESConfig); ok {
		r0 = rf(sesConfig)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.SESConfig)
		}
	}

	if rf, ok := ret.Get(1).(func(*repository.SESConfig) error); ok {
		r1 = rf(sesConfig)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateSESConfigDefault provides a mock function with given fields:
func (_m *SESNotificationRepository) UpdateSESConfigDefault() (bool, error) {
	ret := _m.Called()

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func() (bool, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewSESNotificationRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewSESNotificationRepository creates a new instance of SESNotificationRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewSESNotificationRepository(t mockConstructorTestingTNewSESNotificationRepository) *SESNotificationRepository {
	mock := &SESNotificationRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
