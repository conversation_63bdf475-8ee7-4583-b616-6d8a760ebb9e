// Code generated by mockery v2.31.4. DO NOT EDIT.

package mocks

import (
	context "context"
	bean "github.com/devtron-labs/devtron/api/bean/AppView"

	helper "github.com/devtron-labs/devtron/internal/sql/repository/helper"

	mock "github.com/stretchr/testify/mock"
)

// AppListingRepository is an autogenerated mock type for the AppListingRepository type
type AppListingRepository struct {
	mock.Mock
}

// DeploymentDetailByArtifactId provides a mock function with given fields: ciArtifactId, envId
func (_m *AppListingRepository) DeploymentDetailByArtifactId(ciArtifactId int, envId int) (bean.DeploymentDetailContainer, error) {
	ret := _m.Called(ciArtifactId, envId)

	var r0 bean.DeploymentDetailContainer
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int) (bean.DeploymentDetailContainer, error)); ok {
		return rf(ciArtifactId, envId)
	}
	if rf, ok := ret.Get(0).(func(int, int) bean.DeploymentDetailContainer); ok {
		r0 = rf(ciArtifactId, envId)
	} else {
		r0 = ret.Get(0).(bean.DeploymentDetailContainer)
	}

	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(ciArtifactId, envId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchAppDetail provides a mock function with given fields: ctx, appId, envId
func (_m *AppListingRepository) FetchAppDetail(ctx context.Context, appId int, envId int) (bean.AppDetailContainer, error) {
	ret := _m.Called(ctx, appId, envId)

	var r0 bean.AppDetailContainer
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int, int) (bean.AppDetailContainer, error)); ok {
		return rf(ctx, appId, envId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int, int) bean.AppDetailContainer); ok {
		r0 = rf(ctx, appId, envId)
	} else {
		r0 = ret.Get(0).(bean.AppDetailContainer)
	}

	if rf, ok := ret.Get(1).(func(context.Context, int, int) error); ok {
		r1 = rf(ctx, appId, envId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchAppStageStatus provides a mock function with given fields: appId, appType
func (_m *AppListingRepository) FetchAppStageStatus(appId int, appType int) ([]bean.AppStageStatus, error) {
	ret := _m.Called(appId, appType)

	var r0 []bean.AppStageStatus
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int) ([]bean.AppStageStatus, error)); ok {
		return rf(appId, appType)
	}
	if rf, ok := ret.Get(0).(func(int, int) []bean.AppStageStatus); ok {
		r0 = rf(appId, appType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]bean.AppStageStatus)
		}
	}

	if rf, ok := ret.Get(1).(func(int, int) error); ok {
		r1 = rf(appId, appType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchAppTriggerView provides a mock function with given fields: appId
func (_m *AppListingRepository) FetchAppTriggerView(appId int) ([]bean.TriggerView, error) {
	ret := _m.Called(appId)

	var r0 []bean.TriggerView
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]bean.TriggerView, error)); ok {
		return rf(appId)
	}
	if rf, ok := ret.Get(0).(func(int) []bean.TriggerView); ok {
		r0 = rf(appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]bean.TriggerView)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchAppsByEnvironment provides a mock function with given fields: appListingFilter
func (_m *AppListingRepository) FetchAppsByEnvironment(appListingFilter helper.AppListingFilter) ([]*bean.AppEnvironmentContainer, error) {
	ret := _m.Called(appListingFilter)

	var r0 []*bean.AppEnvironmentContainer
	var r1 error
	if rf, ok := ret.Get(0).(func(helper.AppListingFilter) ([]*bean.AppEnvironmentContainer, error)); ok {
		return rf(appListingFilter)
	}
	if rf, ok := ret.Get(0).(func(helper.AppListingFilter) []*bean.AppEnvironmentContainer); ok {
		r0 = rf(appListingFilter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*bean.AppEnvironmentContainer)
		}
	}

	if rf, ok := ret.Get(1).(func(helper.AppListingFilter) error); ok {
		r1 = rf(appListingFilter)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchAppsByEnvironmentV2 provides a mock function with given fields: appListingFilter
func (_m *AppListingRepository) FetchAppsByEnvironmentV2(appListingFilter helper.AppListingFilter) ([]*bean.AppEnvironmentContainer, int, error) {
	ret := _m.Called(appListingFilter)

	var r0 []*bean.AppEnvironmentContainer
	var r1 int
	var r2 error
	if rf, ok := ret.Get(0).(func(helper.AppListingFilter) ([]*bean.AppEnvironmentContainer, int, error)); ok {
		return rf(appListingFilter)
	}
	if rf, ok := ret.Get(0).(func(helper.AppListingFilter) []*bean.AppEnvironmentContainer); ok {
		r0 = rf(appListingFilter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*bean.AppEnvironmentContainer)
		}
	}

	if rf, ok := ret.Get(1).(func(helper.AppListingFilter) int); ok {
		r1 = rf(appListingFilter)
	} else {
		r1 = ret.Get(1).(int)
	}

	if rf, ok := ret.Get(2).(func(helper.AppListingFilter) error); ok {
		r2 = rf(appListingFilter)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// FetchJobs provides a mock function with given fields: appIds, statuses, environmentIds, sortOrder
func (_m *AppListingRepository) FetchJobs(appIds []int, statuses []string, environmentIds []int, sortOrder string) ([]*bean.JobListingContainer, error) {
	ret := _m.Called(appIds, statuses, environmentIds, sortOrder)

	var r0 []*bean.JobListingContainer
	var r1 error
	if rf, ok := ret.Get(0).(func([]int, []string, []int, string) ([]*bean.JobListingContainer, error)); ok {
		return rf(appIds, statuses, environmentIds, sortOrder)
	}
	if rf, ok := ret.Get(0).(func([]int, []string, []int, string) []*bean.JobListingContainer); ok {
		r0 = rf(appIds, statuses, environmentIds, sortOrder)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*bean.JobListingContainer)
		}
	}

	if rf, ok := ret.Get(1).(func([]int, []string, []int, string) error); ok {
		r1 = rf(appIds, statuses, environmentIds, sortOrder)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchJobsLastSucceededOn provides a mock function with given fields: ciPipelineIDs
func (_m *AppListingRepository) FetchJobsLastSucceededOn(ciPipelineIDs []int) ([]*bean.CiPipelineLastSucceededTime, error) {
	ret := _m.Called(ciPipelineIDs)

	var r0 []*bean.CiPipelineLastSucceededTime
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*bean.CiPipelineLastSucceededTime, error)); ok {
		return rf(ciPipelineIDs)
	}
	if rf, ok := ret.Get(0).(func([]int) []*bean.CiPipelineLastSucceededTime); ok {
		r0 = rf(ciPipelineIDs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*bean.CiPipelineLastSucceededTime)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(ciPipelineIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchMinDetailOtherEnvironment provides a mock function with given fields: appId
func (_m *AppListingRepository) FetchMinDetailOtherEnvironment(appId int) ([]*bean.Environment, error) {
	ret := _m.Called(appId)

	var r0 []*bean.Environment
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]*bean.Environment, error)); ok {
		return rf(appId)
	}
	if rf, ok := ret.Get(0).(func(int) []*bean.Environment); ok {
		r0 = rf(appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*bean.Environment)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchOtherEnvironment provides a mock function with given fields: appId
func (_m *AppListingRepository) FetchOtherEnvironment(appId int) ([]*bean.Environment, error) {
	ret := _m.Called(appId)

	var r0 []*bean.Environment
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]*bean.Environment, error)); ok {
		return rf(appId)
	}
	if rf, ok := ret.Get(0).(func(int) []*bean.Environment); ok {
		r0 = rf(appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*bean.Environment)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchOverviewAppsByEnvironment provides a mock function with given fields: envId, limit, offset
func (_m *AppListingRepository) FetchOverviewAppsByEnvironment(envId int, limit int, offset int) ([]*bean.AppEnvironmentContainer, error) {
	ret := _m.Called(envId, limit, offset)

	var r0 []*bean.AppEnvironmentContainer
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int, int) ([]*bean.AppEnvironmentContainer, error)); ok {
		return rf(envId, limit, offset)
	}
	if rf, ok := ret.Get(0).(func(int, int, int) []*bean.AppEnvironmentContainer); ok {
		r0 = rf(envId, limit, offset)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*bean.AppEnvironmentContainer)
		}
	}

	if rf, ok := ret.Get(1).(func(int, int, int) error); ok {
		r1 = rf(envId, limit, offset)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchOverviewCiPipelines provides a mock function with given fields: jobId
func (_m *AppListingRepository) FetchOverviewCiPipelines(jobId int) ([]*bean.JobListingContainer, error) {
	ret := _m.Called(jobId)

	var r0 []*bean.JobListingContainer
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]*bean.JobListingContainer, error)); ok {
		return rf(jobId)
	}
	if rf, ok := ret.Get(0).(func(int) []*bean.JobListingContainer); ok {
		r0 = rf(jobId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*bean.JobListingContainer)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(jobId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAppCount provides a mock function with given fields: isProd
func (_m *AppListingRepository) FindAppCount(isProd bool) (int, error) {
	ret := _m.Called(isProd)

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(bool) (int, error)); ok {
		return rf(isProd)
	}
	if rf, ok := ret.Get(0).(func(bool) int); ok {
		r0 = rf(isProd)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(bool) error); ok {
		r1 = rf(isProd)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// PrometheusApiByEnvId provides a mock function with given fields: id
func (_m *AppListingRepository) PrometheusApiByEnvId(id int) (*string, error) {
	ret := _m.Called(id)

	var r0 *string
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*string, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) *string); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*string)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewAppListingRepository creates a new instance of AppListingRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewAppListingRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *AppListingRepository {
	mock := &AppListingRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
