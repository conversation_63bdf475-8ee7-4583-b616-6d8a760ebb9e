/*
 * Copyright (c) 2024. Devtron Inc.
 */

package pipelineConfig

import (
	"errors"
	"github.com/devtron-labs/devtron/api/bean"
	"github.com/devtron-labs/devtron/internal/sql/models"
	"github.com/devtron-labs/devtron/pkg/auth/user"
	"github.com/devtron-labs/devtron/pkg/auth/userGroup/beans"
	beans3 "github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/bean"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/utils"
	"github.com/devtron-labs/devtron/util"
	"time"

	repository2 "github.com/devtron-labs/devtron/internal/sql/repository"
	"github.com/devtron-labs/devtron/pkg/sql"
	"github.com/go-pg/pg"
	"go.uber.org/zap"
)

type DeploymentApprovalRepository interface {
	FetchApprovalDataForArtifacts(artifactIds []int, pipelineId int) ([]*DeploymentApprovalRequest, error)
	FetchApprovalPendingArtifacts(approvalUsersAndUserGroupsMetadataReq *beans3.ApprovalUsersAndUserGroupsMetadataReq, artifactListingFilterOpts *bean.ArtifactsListFilterOptions, approvalConfig *beans3.UserApprovalConfig) ([]*DeploymentApprovalRequest, int, error)
	FetchById(requestId int) (*DeploymentApprovalRequest, error)
	FetchWithPipelineAndArtifactDetails(requestId int) (*DeploymentApprovalRequest, error)
	Save(deploymentApprovalRequest *DeploymentApprovalRequest) error
	Update(deploymentApprovalRequest *DeploymentApprovalRequest) error
	ConsumeApprovalRequest(requestId int) error
	FetchLatestDeploymentByArtifactIds(pipelineId int, artifactIds []int) ([]*DeploymentApprovalRequest, error)
	FetchUnDeployedApprovedRequests(approvalUsersAndUserGroupsMetadataReq *beans3.ApprovalUsersAndUserGroupsMetadataReq, pipelineId int, approvalConfig *beans3.UserApprovalConfig) ([]int, error)
}

type DeploymentApprovalRepositoryImpl struct {
	dbConnection               *pg.DB
	logger                     *zap.SugaredLogger
	resourceApprovalRepository RequestApprovalUserdataRepository
	userGroupService           user.UserGroupService
}

func NewDeploymentApprovalRepositoryImpl(dbConnection *pg.DB, logger *zap.SugaredLogger, resourceApprovalRepository RequestApprovalUserdataRepository, userGroupService user.UserGroupService) *DeploymentApprovalRepositoryImpl {
	return &DeploymentApprovalRepositoryImpl{dbConnection: dbConnection, logger: logger, resourceApprovalRepository: resourceApprovalRepository, userGroupService: userGroupService}
}

type RequestWithApprovalUserData struct {
	RequestId    int                     `sql:"request_id"`
	UserId       int32                   `sql:"user_id"`
	UserEmail    string                  `sql:"user_email"`
	UserActive   bool                    `sql:"user_active"`
	UserResponse beans3.ApprovalResponse `sql:"user_response"`
}

type DeploymentApprovalRequest struct {
	tableName                   struct{} `sql:"deployment_approval_request" pg:",discard_unknown_columns"`
	Id                          int      `sql:"id,pk"`
	PipelineId                  int      `sql:"pipeline_id"`
	ArtifactId                  int      `sql:"ci_artifact_id"`
	Active                      bool     `sql:"active,notnull"` // user can cancel request anytime
	ArtifactDeploymentTriggered bool     `sql:"artifact_deployment_triggered"`
	Pipeline                    *Pipeline
	CiArtifact                  *repository2.CiArtifact
	UserEmail                   string                     `sql:"-"` // used for internal purpose
	DeploymentApprovalUserData  []*RequestApprovalUserData `sql:"-"`
	sql.AuditLog
}

// FetchUnDeployedApprovedRequests
// step1(Query) => fetch all the approval requests using pipelineId and the request_approval_user_data for deployment approval type and request not consumed.
// (deployment_approval_request inner join request_approval_user_data inner join user)
// step2(Query) => get user groups of all user ids (have an api to get this data)
// step3 => now evaluate this data against the approval config and get the approved request ids (runtime evaluation)
func (impl *DeploymentApprovalRepositoryImpl) FetchUnDeployedApprovedRequests(metadataRequest *beans3.ApprovalUsersAndUserGroupsMetadataReq, pipelineId int, approvalConfig *beans3.UserApprovalConfig) ([]int, error) {
	var requestsMetaData []*RequestWithApprovalUserData
	// this query will fetch the requests which got atleast one user response and is active and deployment not triggered
	query := "SELECT dar.id AS request_id,raud.user_id,raud.user_response,u.email_id AS user_email, u.active AS user_active" +
		" FROM deployment_approval_request dar" +
		" INNER JOIN request_approval_user_data raud ON dar.id = raud.approval_request_id AND raud.request_type = ?" +
		" INNER JOIN users u ON raud.user_id = u.id " +
		" WHERE dar.pipeline_id = ? AND dar.active = true AND dar.artifact_deployment_triggered = false"
	_, err := impl.dbConnection.Query(&requestsMetaData, query, models.DEPLOYMENT_APPROVAL, pipelineId)
	if err != nil && err != pg.ErrNoRows {
		impl.logger.Errorw("error occurred while fetching artifacts", "pipelineId", pipelineId, "err", err)
		return nil, err
	}

	requestIdMap := make(map[int]*beans3.ResourceApprovalRequest)
	var requestIds []int
	for _, requestMeta := range requestsMetaData {
		requestId := requestMeta.RequestId
		requestIdMap[requestId] = &beans3.ResourceApprovalRequest{Id: requestId, UserResponses: make([]*beans3.UserResponse, 0)}
		requestIds = append(requestIds, requestId)
	}

	for _, requestMeta := range requestsMetaData {
		approvalUsers := requestIdMap[requestMeta.RequestId].UserResponses
		approvalUsers = append(approvalUsers, &beans3.UserResponse{UserId: requestMeta.UserId, UserResponse: requestMeta.UserResponse, UserEmailId: requestMeta.UserEmail, UserDeleted: !requestMeta.UserActive})
		requestIdMap[requestMeta.RequestId].UserResponses = approvalUsers
	}

	var requestApprovedUserIds []int32
	for _, approvalRequest := range requestIdMap {
		for _, user := range approvalRequest.UserResponses {
			requestApprovedUserIds = append(requestApprovedUserIds, user.UserId)
		}
	}

	approvedUserGroupMappings, err := impl.userGroupService.GetByUserIds(requestApprovedUserIds)
	if err != nil {
		impl.logger.Errorw("error occurred while fetching userGroups", "approvedUserIds", requestApprovedUserIds, "err", err)
		return nil, err
	}

	approvedRequestIds := make([]int, 0)
	for _, request := range requestIdMap {
		metadataRequest = metadataRequest.WithApprovedUsersInfo(beans3.NewApprovedUsersInfo(request.UserResponses, approvedUserGroupMappings))
		approvalConfigData := utils.GetDtoFromConfigAndUserResponse(beans3.APPROVAL_FOR_DEPLOYMENT, approvalConfig, metadataRequest)
		if approvalConfigData.Satisfied() {
			approvedRequestIds = append(approvedRequestIds, request.Id)
		}
	}

	return approvedRequestIds, nil

}

func (impl *DeploymentApprovalRepositoryImpl) FetchApprovalPendingArtifacts(metadataRequest *beans3.ApprovalUsersAndUserGroupsMetadataReq, artifactListingFilterOpts *bean.ArtifactsListFilterOptions, approvalConfig *beans3.UserApprovalConfig) ([]*DeploymentApprovalRequest, int, error) {

	approvedRequestIds, err := impl.FetchUnDeployedApprovedRequests(metadataRequest, artifactListingFilterOpts.PipelineId, approvalConfig)
	if err != nil {
		impl.logger.Errorw("error occurred while fetching approved request ids", "pipelineId", artifactListingFilterOpts.PipelineId, "err", err)
		return nil, 0, err
	}

	pendingRequests := make([]*DeploymentApprovalRequest, 0)
	// 4) use them in below query (below db call)
	artifactListingFilterOpts.SearchString = util.GetLIKEClauseQueryParam(artifactListingFilterOpts.SearchString)
	finalQuery := impl.dbConnection.Model(&pendingRequests).
		Column("deployment_approval_request.*", "CiArtifact").
		Join("JOIN ci_artifact ca ON ca.id = deployment_approval_request.ci_artifact_id").
		Where("deployment_approval_request.pipeline_id = ?", artifactListingFilterOpts.PipelineId).
		Where("deployment_approval_request.active=true").
		Where("deployment_approval_request.artifact_deployment_triggered=false").
		Where("ci_artifact.image LIKE ? ", artifactListingFilterOpts.SearchString)

	if len(approvedRequestIds) > 0 {
		finalQuery.Where("deployment_approval_request.id NOT IN (?)", pg.In(approvedRequestIds))
	}

	totalCount, err := finalQuery.Count()
	if err != nil {
		impl.logger.Errorw("error occurred while fetching total count", "pipelineId", artifactListingFilterOpts.PipelineId, "err", err)
		return nil, 0, err
	}

	err = finalQuery.
		Limit(artifactListingFilterOpts.Limit).
		Offset(artifactListingFilterOpts.Offset).
		Select()

	if err != nil && err != pg.ErrNoRows {
		impl.logger.Errorw("error occurred while fetching artifacts", "pipelineId", artifactListingFilterOpts.PipelineId, "err", err)
		return nil, 0, err
	}

	return pendingRequests, totalCount, nil
}

func (impl *DeploymentApprovalRepositoryImpl) FetchLatestDeploymentByArtifactIds(pipelineId int, artifactIds []int) ([]*DeploymentApprovalRequest, error) {
	var requests []*DeploymentApprovalRequest
	if len(artifactIds) == 0 {
		return requests, nil
	}

	query := `with minimal_pcos as (select max(pco.id) as id from pipeline_config_override pco where pco.pipeline_id  = ? and pco.ci_artifact_id in (?) group by pco.ci_artifact_id)
	select pco.ci_artifact_id,pco.created_on from pipeline_config_override pco where pco.id in (select id from minimal_pcos); `

	_, err := impl.dbConnection.Query(&requests, query, pipelineId, pg.In(artifactIds))
	if err != nil && err != pg.ErrNoRows {
		impl.logger.Errorw("error occurred while fetching latest deployment by artifact ids", "pipelineId", pipelineId, "artifactIds", artifactIds, "err", err)
		return nil, err
	}

	return requests, nil
}

func (impl *DeploymentApprovalRepositoryImpl) FetchApprovalDataForArtifacts(artifactIds []int, pipelineId int) ([]*DeploymentApprovalRequest, error) {
	impl.logger.Debugw("fetching approval data for artifacts", "ids", artifactIds, "pipelineId", pipelineId)
	if len(artifactIds) == 0 {
		return []*DeploymentApprovalRequest{}, nil
	}
	var requests []*DeploymentApprovalRequest
	err := impl.dbConnection.
		Model(&requests).
		// Column("deployment_approval_request.*", /*"RequestApprovalUserData", "RequestApprovalUserData.User"*/).
		Where("ci_artifact_id in (?) ", pg.In(artifactIds)).
		Where("pipeline_id = ?", pipelineId).
		Where("artifact_deployment_triggered = ?", false).
		Where("active = ?", true).
		Select()
	if err != nil && err != pg.ErrNoRows {
		impl.logger.Errorw("error occurred while fetching artifacts", "pipelineId", pipelineId, "err", err)
		return nil, err
	}
	requestIdMap := make(map[int]*DeploymentApprovalRequest)
	var requestIds []int
	for _, request := range requests {
		requestId := request.Id
		requestIdMap[requestId] = request
		requestIds = append(requestIds, requestId)
	}
	if len(requestIds) > 0 {
		usersData, err := impl.resourceApprovalRepository.FetchApprovalDataForRequests(requestIds, models.DEPLOYMENT_APPROVAL)
		if err != nil {
			return requests, err
		}
		for _, userData := range usersData {
			approvalRequestId := userData.ApprovalRequestId
			deploymentApprovalRequest := requestIdMap[approvalRequestId]
			approvalUsers := deploymentApprovalRequest.DeploymentApprovalUserData
			approvalUsers = append(approvalUsers, userData)
			deploymentApprovalRequest.DeploymentApprovalUserData = approvalUsers
		}
	}
	return requests, nil
}

func (impl *DeploymentApprovalRepositoryImpl) FetchWithPipelineAndArtifactDetails(requestId int) (*DeploymentApprovalRequest, error) {
	request := &DeploymentApprovalRequest{Id: requestId}
	err := impl.dbConnection.
		Model(request).
		Column("deployment_approval_request.*", "Pipeline", "CiArtifact").
		Where("active = ?", true).WherePK().Select()
	if err != nil {
		impl.logger.Errorw("error occurred while fetching request data", "id", requestId, "err", err)
		return nil, err
	}
	return request, nil
}

func (impl *DeploymentApprovalRepositoryImpl) FetchById(requestId int) (*DeploymentApprovalRequest, error) {
	request := &DeploymentApprovalRequest{Id: requestId}
	err := impl.dbConnection.
		Model(request).Where("active = ?", true).WherePK().Select()
	if err != nil {
		impl.logger.Errorw("error occurred while fetching request data", "id", requestId, "err", err)
		return nil, err
	}
	return request, nil
}

func (impl *DeploymentApprovalRepositoryImpl) ConsumeApprovalRequest(requestId int) error {
	request, err := impl.FetchById(requestId)
	if err != nil && err != pg.ErrNoRows {
		impl.logger.Errorw("error occurred while fetching approval request", "requestId", requestId, "err", err)
		return err
	} else if err == pg.ErrNoRows {
		return errors.New("approval request not raised for this artifact")
	}
	request.ArtifactDeploymentTriggered = true
	return impl.Update(request)
}

func (impl *DeploymentApprovalRepositoryImpl) Save(deploymentApprovalRequest *DeploymentApprovalRequest) error {
	currentTime := time.Now()
	deploymentApprovalRequest.CreatedOn = currentTime
	deploymentApprovalRequest.UpdatedOn = currentTime
	return impl.dbConnection.Insert(deploymentApprovalRequest)
}

func (impl *DeploymentApprovalRepositoryImpl) Update(deploymentApprovalRequest *DeploymentApprovalRequest) error {
	deploymentApprovalRequest.UpdatedOn = time.Now()
	return impl.dbConnection.Update(deploymentApprovalRequest)
}

func (request *DeploymentApprovalRequest) ConvertToApprovalMetadata(approvedUserGroupMappings map[int32][]*beans.UserGroupDTO, approvalConfigHistory *beans3.UserApprovalConfig) *beans3.UserApprovalMetadata {
	approvalMetadata := &beans3.UserApprovalMetadata{ApprovalRequestId: request.Id}
	requestedUserData := beans3.UserApprovalData{DataId: request.Id}
	requestedUserData.UserId = request.CreatedBy
	requestedUserData.UserEmail = request.UserEmail
	requestedUserData.UserActionTime = request.CreatedOn
	approvalMetadata.RequestedUserData = requestedUserData
	var userApprovalData []beans3.UserApprovalData

	for _, approvalUser := range request.DeploymentApprovalUserData {
		userData := beans3.UserApprovalData{DataId: approvalUser.Id, UserId: approvalUser.UserId, UserEmail: approvalUser.User.EmailId, UserResponse: approvalUser.UserResponse,
			UserActionTime: approvalUser.CreatedOn,
		}
		// for old approvals, we don't have user group information as userGroups story was not developed yet
		if approvedUserGroupMappings != nil {
			userData.UserGroups = approvedUserGroupMappings[approvalUser.UserId]
		}
		userApprovalData = append(userApprovalData, userData)
	}

	// get userGroup identifier to usegroup name map.
	// get approved responses
	// and their user group mappings

	// approval config will be nil for old data
	approvalMetadata.ApprovalConfig = approvalConfigHistory
	approvalMetadata.ApprovalUsersData = userApprovalData
	return approvalMetadata
}

func (request *DeploymentApprovalRequest) GetApprovedCount() int {
	count := 0
	for _, approvalUser := range request.DeploymentApprovalUserData {
		if approvalUser.UserResponse == beans3.APPROVED {
			count++
		}
	}
	return count
}
