/*
 * Copyright (c) 2024. Devtron Inc.
 */

package pipelineConfig

import (
	"github.com/devtron-labs/devtron/internal/sql/models"
	"github.com/devtron-labs/devtron/pkg/auth/user/repository"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/bean"
	"github.com/devtron-labs/devtron/pkg/sql"
	"github.com/go-pg/pg"
	"go.uber.org/zap"
	"time"
)

type Constraint string

const UNIQUE_USER_REQUEST_ACTION Constraint = "unique_user_request_action"

type RequestApprovalUserData struct {
	tableName         struct{}              `sql:"request_approval_user_data" pg:",discard_unknown_columns"`
	Id                int                   `sql:"id,pk"`
	RequestType       models.RequestType    `sql:"request_type"`
	ApprovalRequestId int                   `sql:"approval_request_id"` // keep in mind foreign key constraint
	UserId            int32                 `sql:"user_id"`             // keep in mid foreign key constraint
	UserResponse      bean.ApprovalResponse `sql:"user_response"`
	Comments          string                `sql:"comments"`
	User              *repository.UserModel
	sql.AuditLog
}

type RequestApprovalUserdataRepository interface {
	SaveRequestApprovalUserData(userData *RequestApprovalUserData) error
	FetchApprovalDataForRequests(requestIds []int, requestType models.RequestType) ([]*RequestApprovalUserData, error)
	FetchApprovedDataByApprovalId(approvalRequestId int, requestType models.RequestType) ([]*RequestApprovalUserData, error)
}

type RequestApprovalUserDataRepositoryImpl struct {
	dbConnection *pg.DB
	logger       *zap.SugaredLogger
}

func NewRequestApprovalUserDataRepositoryImpl(dbConnection *pg.DB, logger *zap.SugaredLogger) *RequestApprovalUserDataRepositoryImpl {
	return &RequestApprovalUserDataRepositoryImpl{dbConnection: dbConnection, logger: logger}
}

func (impl *RequestApprovalUserDataRepositoryImpl) FetchApprovalDataForRequests(requestIds []int, requestType models.RequestType) ([]*RequestApprovalUserData, error) {
	var usersData []*RequestApprovalUserData
	if len(requestIds) == 0 {
		return usersData, nil
	}
	err := impl.dbConnection.
		Model(&usersData).
		Column("request_approval_user_data.*", "User").
		Where("approval_request_id in (?) ", pg.In(requestIds)).
		Where("request_type = ?", requestType).
		Select()
	if err != nil && err != pg.ErrNoRows {
		impl.logger.Errorw("error occurred while fetching artifacts", "requestIds", requestIds, "err", err)
		return nil, err
	}
	return usersData, nil
}

func (impl *RequestApprovalUserDataRepositoryImpl) FetchApprovedDataByApprovalId(approvalRequestId int, requestType models.RequestType) ([]*RequestApprovalUserData, error) {
	var results []*RequestApprovalUserData
	err := impl.dbConnection.
		Model(&results).
		Column("request_approval_user_data.*", "User").
		Where("request_approval_user_data.approval_request_id = ? ", approvalRequestId).
		Where("request_approval_user_data.request_type = ?", requestType).
		Select()
	if err != nil && err != pg.ErrNoRows {
		impl.logger.Errorw("error occurred while fetching artifacts", "results", results, "err", err)
		return nil, err
	}
	return results, nil

}

func (impl *RequestApprovalUserDataRepositoryImpl) SaveRequestApprovalUserData(userData *RequestApprovalUserData) error {
	currentTime := time.Now()
	userData.CreatedOn = currentTime
	userData.UpdatedOn = currentTime
	return impl.dbConnection.Insert(userData)
}
