// Code generated by mockery v2.14.0. DO NOT EDIT.

package mocks

import (
	pipelineConfig "github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	mock "github.com/stretchr/testify/mock"
)

// MaterialRepository is an autogenerated mock type for the MaterialRepository type
type MaterialRepository struct {
	mock.Mock
}

// FindByAppId provides a mock function with given fields: appId
func (_m *MaterialRepository) FindByAppId(appId int) ([]*pipelineConfig.GitMaterial, error) {
	ret := _m.Called(appId)

	var r0 []*pipelineConfig.GitMaterial
	if rf, ok := ret.Get(0).(func(int) []*pipelineConfig.GitMaterial); ok {
		r0 = rf(appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.GitMaterial)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByAppIdAndCheckoutPath provides a mock function with given fields: appId, checkoutPath
func (_m *MaterialRepository) FindByAppIdAndCheckoutPath(appId int, checkoutPath string) (*pipelineConfig.GitMaterial, error) {
	ret := _m.Called(appId, checkoutPath)

	var r0 *pipelineConfig.GitMaterial
	if rf, ok := ret.Get(0).(func(int, string) *pipelineConfig.GitMaterial); ok {
		r0 = rf(appId, checkoutPath)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.GitMaterial)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, string) error); ok {
		r1 = rf(appId, checkoutPath)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByAppIds provides a mock function with given fields: appIds
func (_m *MaterialRepository) FindByAppIds(appIds []int) ([]*pipelineConfig.GitMaterial, error) {
	ret := _m.Called(appIds)

	var r0 []*pipelineConfig.GitMaterial
	if rf, ok := ret.Get(0).(func([]int) []*pipelineConfig.GitMaterial); ok {
		r0 = rf(appIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.GitMaterial)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(appIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByGitProviderId provides a mock function with given fields: gitProviderId
func (_m *MaterialRepository) FindByGitProviderId(gitProviderId int) ([]*pipelineConfig.GitMaterial, error) {
	ret := _m.Called(gitProviderId)

	var r0 []*pipelineConfig.GitMaterial
	if rf, ok := ret.Get(0).(func(int) []*pipelineConfig.GitMaterial); ok {
		r0 = rf(gitProviderId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.GitMaterial)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(gitProviderId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindById provides a mock function with given fields: Id
func (_m *MaterialRepository) FindById(Id int) (*pipelineConfig.GitMaterial, error) {
	ret := _m.Called(Id)

	var r0 *pipelineConfig.GitMaterial
	if rf, ok := ret.Get(0).(func(int) *pipelineConfig.GitMaterial); ok {
		r0 = rf(Id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.GitMaterial)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(Id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindNumberOfAppsWithGitRepo provides a mock function with given fields: appIds
func (_m *MaterialRepository) FindNumberOfAppsWithGitRepo(appIds []int) (int, error) {
	ret := _m.Called(appIds)

	var r0 int
	if rf, ok := ret.Get(0).(func([]int) int); ok {
		r0 = rf(appIds)
	} else {
		r0 = ret.Get(0).(int)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(appIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MarkMaterialDeleted provides a mock function with given fields: material
func (_m *MaterialRepository) MarkMaterialDeleted(material *pipelineConfig.GitMaterial) error {
	ret := _m.Called(material)

	var r0 error
	if rf, ok := ret.Get(0).(func(*pipelineConfig.GitMaterial) error); ok {
		r0 = rf(material)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MaterialExists provides a mock function with given fields: url
func (_m *MaterialRepository) MaterialExists(url string) (bool, error) {
	ret := _m.Called(url)

	var r0 bool
	if rf, ok := ret.Get(0).(func(string) bool); ok {
		r0 = rf(url)
	} else {
		r0 = ret.Get(0).(bool)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(url)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SaveMaterial provides a mock function with given fields: material
func (_m *MaterialRepository) SaveMaterial(material *pipelineConfig.GitMaterial) error {
	ret := _m.Called(material)

	var r0 error
	if rf, ok := ret.Get(0).(func(*pipelineConfig.GitMaterial) error); ok {
		r0 = rf(material)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Update provides a mock function with given fields: materials
func (_m *MaterialRepository) Update(materials []*pipelineConfig.GitMaterial) error {
	ret := _m.Called(materials)

	var r0 error
	if rf, ok := ret.Get(0).(func([]*pipelineConfig.GitMaterial) error); ok {
		r0 = rf(materials)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateMaterial provides a mock function with given fields: material
func (_m *MaterialRepository) UpdateMaterial(material *pipelineConfig.GitMaterial) error {
	ret := _m.Called(material)

	var r0 error
	if rf, ok := ret.Get(0).(func(*pipelineConfig.GitMaterial) error); ok {
		r0 = rf(material)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateMaterialScmId provides a mock function with given fields: material
func (_m *MaterialRepository) UpdateMaterialScmId(material *pipelineConfig.GitMaterial) error {
	ret := _m.Called(material)

	var r0 error
	if rf, ok := ret.Get(0).(func(*pipelineConfig.GitMaterial) error); ok {
		r0 = rf(material)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewMaterialRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewMaterialRepository creates a new instance of MaterialRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMaterialRepository(t mockConstructorTestingTNewMaterialRepository) *MaterialRepository {
	mock := &MaterialRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
