// Code generated by mockery v2.42.0. DO NOT EDIT.

package mocks

import (
	pipelineConfig "github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	mock "github.com/stretchr/testify/mock"
)

// CiWorkflowRepository is an autogenerated mock type for the CiWorkflowRepository type
type CiWorkflowRepository struct {
	mock.Mock
}

// ExistsByStatus provides a mock function with given fields: status
func (_m *CiWorkflowRepository) ExistsByStatus(status string) (bool, error) {
	ret := _m.Called(status)

	if len(ret) == 0 {
		panic("no return value specified for ExistsByStatus")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (bool, error)); ok {
		return rf(status)
	}
	if rf, ok := ret.Get(0).(func(string) bool); ok {
		r0 = rf(status)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(status)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FIndCiWorkflowStatusesByAppId provides a mock function with given fields: appId
func (_m *CiWorkflowRepository) FIndCiWorkflowStatusesByAppId(appId int) ([]*pipelineConfig.CiWorkflowStatus, error) {
	ret := _m.Called(appId)

	if len(ret) == 0 {
		panic("no return value specified for FIndCiWorkflowStatusesByAppId")
	}

	var r0 []*pipelineConfig.CiWorkflowStatus
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]*pipelineConfig.CiWorkflowStatus, error)); ok {
		return rf(appId)
	}
	if rf, ok := ret.Get(0).(func(int) []*pipelineConfig.CiWorkflowStatus); ok {
		r0 = rf(appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.CiWorkflowStatus)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAllLastTriggeredWorkflowByArtifactId provides a mock function with given fields: ciArtifactId
func (_m *CiWorkflowRepository) FindAllLastTriggeredWorkflowByArtifactId(ciArtifactId []int) ([]*pipelineConfig.CiWorkflow, error) {
	ret := _m.Called(ciArtifactId)

	if len(ret) == 0 {
		panic("no return value specified for FindAllLastTriggeredWorkflowByArtifactId")
	}

	var r0 []*pipelineConfig.CiWorkflow
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*pipelineConfig.CiWorkflow, error)); ok {
		return rf(ciArtifactId)
	}
	if rf, ok := ret.Get(0).(func([]int) []*pipelineConfig.CiWorkflow); ok {
		r0 = rf(ciArtifactId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.CiWorkflow)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(ciArtifactId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAllTriggeredWorkflowCountInLast24Hour provides a mock function with given fields:
func (_m *CiWorkflowRepository) FindAllTriggeredWorkflowCountInLast24Hour() (int, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for FindAllTriggeredWorkflowCountInLast24Hour")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func() (int, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() int); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindBuildTypeAndStatusDataOfLast1Day provides a mock function with given fields:
func (_m *CiWorkflowRepository) FindBuildTypeAndStatusDataOfLast1Day() []*pipelineConfig.BuildTypeCount {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for FindBuildTypeAndStatusDataOfLast1Day")
	}

	var r0 []*pipelineConfig.BuildTypeCount
	if rf, ok := ret.Get(0).(func() []*pipelineConfig.BuildTypeCount); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.BuildTypeCount)
		}
	}

	return r0
}

// FindById provides a mock function with given fields: id
func (_m *CiWorkflowRepository) FindById(id int) (*pipelineConfig.CiWorkflow, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for FindById")
	}

	var r0 *pipelineConfig.CiWorkflow
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*pipelineConfig.CiWorkflow, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) *pipelineConfig.CiWorkflow); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.CiWorkflow)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByName provides a mock function with given fields: name
func (_m *CiWorkflowRepository) FindByName(name string) (*pipelineConfig.CiWorkflow, error) {
	ret := _m.Called(name)

	if len(ret) == 0 {
		panic("no return value specified for FindByName")
	}

	var r0 *pipelineConfig.CiWorkflow
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*pipelineConfig.CiWorkflow, error)); ok {
		return rf(name)
	}
	if rf, ok := ret.Get(0).(func(string) *pipelineConfig.CiWorkflow); ok {
		r0 = rf(name)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.CiWorkflow)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(name)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByPipelineId provides a mock function with given fields: pipelineId, offset, size
func (_m *CiWorkflowRepository) FindByPipelineId(pipelineId int, offset int, size int) ([]pipelineConfig.WorkflowWithArtifact, error) {
	ret := _m.Called(pipelineId, offset, size)

	if len(ret) == 0 {
		panic("no return value specified for FindByPipelineId")
	}

	var r0 []pipelineConfig.WorkflowWithArtifact
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int, int) ([]pipelineConfig.WorkflowWithArtifact, error)); ok {
		return rf(pipelineId, offset, size)
	}
	if rf, ok := ret.Get(0).(func(int, int, int) []pipelineConfig.WorkflowWithArtifact); ok {
		r0 = rf(pipelineId, offset, size)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]pipelineConfig.WorkflowWithArtifact)
		}
	}

	if rf, ok := ret.Get(1).(func(int, int, int) error); ok {
		r1 = rf(pipelineId, offset, size)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByStatusesIn provides a mock function with given fields: activeStatuses
func (_m *CiWorkflowRepository) FindByStatusesIn(activeStatuses []string) ([]*pipelineConfig.CiWorkflow, error) {
	ret := _m.Called(activeStatuses)

	if len(ret) == 0 {
		panic("no return value specified for FindByStatusesIn")
	}

	var r0 []*pipelineConfig.CiWorkflow
	var r1 error
	if rf, ok := ret.Get(0).(func([]string) ([]*pipelineConfig.CiWorkflow, error)); ok {
		return rf(activeStatuses)
	}
	if rf, ok := ret.Get(0).(func([]string) []*pipelineConfig.CiWorkflow); ok {
		r0 = rf(activeStatuses)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.CiWorkflow)
		}
	}

	if rf, ok := ret.Get(1).(func([]string) error); ok {
		r1 = rf(activeStatuses)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindCiWorkflowGitTriggersById provides a mock function with given fields: id
func (_m *CiWorkflowRepository) FindCiWorkflowGitTriggersById(id int) (*pipelineConfig.CiWorkflow, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for FindCiWorkflowGitTriggersById")
	}

	var r0 *pipelineConfig.CiWorkflow
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*pipelineConfig.CiWorkflow, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) *pipelineConfig.CiWorkflow); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.CiWorkflow)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindCiWorkflowGitTriggersByIds provides a mock function with given fields: ids
func (_m *CiWorkflowRepository) FindCiWorkflowGitTriggersByIds(ids []int) ([]*pipelineConfig.CiWorkflow, error) {
	ret := _m.Called(ids)

	if len(ret) == 0 {
		panic("no return value specified for FindCiWorkflowGitTriggersByIds")
	}

	var r0 []*pipelineConfig.CiWorkflow
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*pipelineConfig.CiWorkflow, error)); ok {
		return rf(ids)
	}
	if rf, ok := ret.Get(0).(func([]int) []*pipelineConfig.CiWorkflow); ok {
		r0 = rf(ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.CiWorkflow)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindLastTriggeredWorkflow provides a mock function with given fields: pipelineId
func (_m *CiWorkflowRepository) FindLastTriggeredWorkflow(pipelineId int) (*pipelineConfig.CiWorkflow, error) {
	ret := _m.Called(pipelineId)

	if len(ret) == 0 {
		panic("no return value specified for FindLastTriggeredWorkflow")
	}

	var r0 *pipelineConfig.CiWorkflow
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*pipelineConfig.CiWorkflow, error)); ok {
		return rf(pipelineId)
	}
	if rf, ok := ret.Get(0).(func(int) *pipelineConfig.CiWorkflow); ok {
		r0 = rf(pipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.CiWorkflow)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(pipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindLastTriggeredWorkflowByArtifactId provides a mock function with given fields: ciArtifactId
func (_m *CiWorkflowRepository) FindLastTriggeredWorkflowByArtifactId(ciArtifactId int) (*pipelineConfig.CiWorkflow, error) {
	ret := _m.Called(ciArtifactId)

	if len(ret) == 0 {
		panic("no return value specified for FindLastTriggeredWorkflowByArtifactId")
	}

	var r0 *pipelineConfig.CiWorkflow
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*pipelineConfig.CiWorkflow, error)); ok {
		return rf(ciArtifactId)
	}
	if rf, ok := ret.Get(0).(func(int) *pipelineConfig.CiWorkflow); ok {
		r0 = rf(ciArtifactId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.CiWorkflow)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(ciArtifactId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindLastTriggeredWorkflowByCiIds provides a mock function with given fields: pipelineId
func (_m *CiWorkflowRepository) FindLastTriggeredWorkflowByCiIds(pipelineId []int) ([]*pipelineConfig.CiWorkflow, error) {
	ret := _m.Called(pipelineId)

	if len(ret) == 0 {
		panic("no return value specified for FindLastTriggeredWorkflowByCiIds")
	}

	var r0 []*pipelineConfig.CiWorkflow
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*pipelineConfig.CiWorkflow, error)); ok {
		return rf(pipelineId)
	}
	if rf, ok := ret.Get(0).(func([]int) []*pipelineConfig.CiWorkflow); ok {
		r0 = rf(pipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.CiWorkflow)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(pipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindLastTriggeredWorkflowGitTriggersByArtifactId provides a mock function with given fields: ciArtifactId
func (_m *CiWorkflowRepository) FindLastTriggeredWorkflowGitTriggersByArtifactId(ciArtifactId int) (*pipelineConfig.CiWorkflow, error) {
	ret := _m.Called(ciArtifactId)

	if len(ret) == 0 {
		panic("no return value specified for FindLastTriggeredWorkflowGitTriggersByArtifactId")
	}

	var r0 *pipelineConfig.CiWorkflow
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*pipelineConfig.CiWorkflow, error)); ok {
		return rf(ciArtifactId)
	}
	if rf, ok := ret.Get(0).(func(int) *pipelineConfig.CiWorkflow); ok {
		r0 = rf(ciArtifactId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.CiWorkflow)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(ciArtifactId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindLastTriggeredWorkflowGitTriggersByArtifactIds provides a mock function with given fields: ciArtifactIds
func (_m *CiWorkflowRepository) FindLastTriggeredWorkflowGitTriggersByArtifactIds(ciArtifactIds []int) ([]*pipelineConfig.WorkflowWithArtifact, error) {
	ret := _m.Called(ciArtifactIds)

	if len(ret) == 0 {
		panic("no return value specified for FindLastTriggeredWorkflowGitTriggersByArtifactIds")
	}

	var r0 []*pipelineConfig.WorkflowWithArtifact
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*pipelineConfig.WorkflowWithArtifact, error)); ok {
		return rf(ciArtifactIds)
	}
	if rf, ok := ret.Get(0).(func([]int) []*pipelineConfig.WorkflowWithArtifact); ok {
		r0 = rf(ciArtifactIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.WorkflowWithArtifact)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(ciArtifactIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindRetriedWorkflowCountByReferenceId provides a mock function with given fields: id
func (_m *CiWorkflowRepository) FindRetriedWorkflowCountByReferenceId(id int) (int, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for FindRetriedWorkflowCountByReferenceId")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (int, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) int); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SaveWorkFlow provides a mock function with given fields: wf
func (_m *CiWorkflowRepository) SaveWorkFlow(wf *pipelineConfig.CiWorkflow) error {
	ret := _m.Called(wf)

	if len(ret) == 0 {
		panic("no return value specified for SaveWorkFlow")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*pipelineConfig.CiWorkflow) error); ok {
		r0 = rf(wf)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateArtifactUploaded provides a mock function with given fields: id, isUploaded
func (_m *CiWorkflowRepository) UpdateArtifactUploaded(id int, isUploaded bool) error {
	ret := _m.Called(id, isUploaded)

	if len(ret) == 0 {
		panic("no return value specified for UpdateArtifactUploaded")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int, bool) error); ok {
		r0 = rf(id, isUploaded)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateWorkFlow provides a mock function with given fields: wf
func (_m *CiWorkflowRepository) UpdateWorkFlow(wf *pipelineConfig.CiWorkflow) error {
	ret := _m.Called(wf)

	if len(ret) == 0 {
		panic("no return value specified for UpdateWorkFlow")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*pipelineConfig.CiWorkflow) error); ok {
		r0 = rf(wf)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewCiWorkflowRepository creates a new instance of CiWorkflowRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewCiWorkflowRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *CiWorkflowRepository {
	mock := &CiWorkflowRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
