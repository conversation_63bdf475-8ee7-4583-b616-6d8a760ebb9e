/*
 * Copyright (c) 2020-2024. Devtron Inc.
 */

package repository

import (
	"github.com/devtron-labs/devtron/internal/sql/constants"
	"github.com/devtron-labs/devtron/pkg/sql"
	"github.com/go-pg/pg"
	"go.uber.org/zap"
)

type GitOpsConfigRepository interface {
	CreateGitOpsConfig(model *GitOpsConfig, tx *pg.Tx) (*GitOpsConfig, error)
	UpdateGitOpsConfig(model *GitOpsConfig, tx *pg.Tx) error
	GetGitOpsConfigById(id int) (*GitOpsConfig, error)
	GetAllGitOpsConfig() ([]*GitOpsConfig, error)
	GetAllGitOpsConfigCount() (int, error)
	GetActiveGitOpsConfigByProvider(provider string) (*GitOpsConfig, error)
	GetGitOpsConfigByProvider(provider string) (*GitOpsConfig, error)
	CheckIfGitOpsProviderExist(provider string) (bool, error)
	GetGitOpsConfigActive() (*GitOpsConfig, error)
	GetConnection() *pg.DB
	GetEmailIdFromActiveGitOpsConfig() (string, error)
	GetLastSavedConfigByProviders(providers []string) (*GitOpsConfig, error)
}

type GitOpsConfigRepositoryImpl struct {
	dbConnection *pg.DB
	logger       *zap.SugaredLogger
}

type GitOpsConfig struct {
	tableName             struct{}           `sql:"gitops_config" pg:",discard_unknown_columns"`
	Id                    int                `sql:"id,pk"`
	Provider              string             `sql:"provider"`
	Username              string             `sql:"username"`
	Token                 string             `sql:"token"`
	GitLabGroupId         string             `sql:"gitlab_group_id"`
	GitHubOrgId           string             `sql:"github_org_id"`
	AzureProject          string             `sql:"azure_project"`
	Host                  string             `sql:"host"`
	Active                bool               `sql:"active,notnull"`
	AllowCustomRepository bool               `sql:"allow_custom_repository,notnull"`
	BitBucketWorkspaceId  string             `sql:"bitbucket_workspace_id"`
	BitBucketProjectKey   string             `sql:"bitbucket_project_key"`
	EmailId               string             `sql:"email_id"`
	EnableTLSVerification bool               `sql:"enable_tls_verification"`
	TlsCert               string             `sql:"tls_cert"`
	TlsKey                string             `sql:"tls_key"`
	CaCert                string             `sql:"ca_cert"`
	AuthMode              constants.AuthMode `sql:"auth_mode"`
	SshKey                string             `sql:"ssh_key"`
	SshHost               string             `sql:"ssh_host"`
	sql.AuditLog
}

func NewGitOpsConfigRepositoryImpl(logger *zap.SugaredLogger, dbConnection *pg.DB) *GitOpsConfigRepositoryImpl {
	return &GitOpsConfigRepositoryImpl{dbConnection: dbConnection, logger: logger}
}

func (impl *GitOpsConfigRepositoryImpl) GetConnection() *pg.DB {
	return impl.dbConnection
}

func (impl *GitOpsConfigRepositoryImpl) CreateGitOpsConfig(model *GitOpsConfig, tx *pg.Tx) (*GitOpsConfig, error) {
	err := tx.Insert(model)
	if err != nil {
		impl.logger.Error(err)
		return model, err
	}
	return model, nil
}
func (impl *GitOpsConfigRepositoryImpl) UpdateGitOpsConfig(model *GitOpsConfig, tx *pg.Tx) error {
	err := tx.Update(model)
	if err != nil {
		impl.logger.Error(err)
		return err
	}
	return nil
}
func (impl *GitOpsConfigRepositoryImpl) GetGitOpsConfigById(id int) (*GitOpsConfig, error) {
	var model GitOpsConfig
	err := impl.dbConnection.Model(&model).Where("id = ?", id).Select()
	return &model, err
}
func (impl *GitOpsConfigRepositoryImpl) GetAllGitOpsConfig() ([]*GitOpsConfig, error) {
	var userModel []*GitOpsConfig
	err := impl.dbConnection.Model(&userModel).Order("updated_on desc").Select()
	return userModel, err
}
func (impl *GitOpsConfigRepositoryImpl) GetAllGitOpsConfigCount() (int, error) {
	cnt, err := impl.dbConnection.Model(&GitOpsConfig{}).Count()
	return cnt, err
}

func (impl *GitOpsConfigRepositoryImpl) GetActiveGitOpsConfigByProvider(provider string) (*GitOpsConfig, error) {
	var model GitOpsConfig
	query := impl.dbConnection.Model(&model).Where("provider = ?", provider).Where("active = ?", true)
	err := query.Select()
	return &model, err
}

func (impl *GitOpsConfigRepositoryImpl) GetGitOpsConfigByProvider(provider string) (*GitOpsConfig, error) {
	var model GitOpsConfig
	err := impl.dbConnection.Model(&model).Where("provider = ?", provider).Select()
	return &model, err
}

func (impl *GitOpsConfigRepositoryImpl) CheckIfGitOpsProviderExist(provider string) (bool, error) {
	found, err := impl.dbConnection.Model((*GitOpsConfig)(nil)).
		Where("provider = ?", provider).
		Exists()
	return found, err
}

func (impl *GitOpsConfigRepositoryImpl) GetGitOpsConfigActive() (*GitOpsConfig, error) {
	var model GitOpsConfig
	err := impl.dbConnection.Model(&model).Where("active = ?", true).Limit(1).Select()
	return &model, err
}

func (impl *GitOpsConfigRepositoryImpl) GetEmailIdFromActiveGitOpsConfig() (string, error) {
	var emailId string
	err := impl.dbConnection.Model((*GitOpsConfig)(nil)).Column("email_id").
		Where("active = ?", true).Select(&emailId)
	return emailId, err
}

func (impl *GitOpsConfigRepositoryImpl) GetLastSavedConfigByProviders(providers []string) (*GitOpsConfig, error) {
	if len(providers) == 0 {
		return nil, nil
	}
	var model GitOpsConfig
	err := impl.dbConnection.Model(&model).Where("provider in (?)", pg.In(providers)).Order("updated_on DESC").Limit(1).Select()
	return &model, err
}
