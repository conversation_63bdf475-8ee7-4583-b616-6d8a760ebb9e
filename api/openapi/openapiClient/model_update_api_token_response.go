/*
Devtron Labs

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

API version: 1.0.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package openapi

import (
	"encoding/json"
)

// UpdateApiTokenResponse struct for UpdateApiTokenResponse
type UpdateApiTokenResponse struct {
	// success or failure
	Success *bool `json:"success,omitempty"`
	// Token of that api-token
	Token *string `json:"token,omitempty"`
}

// NewUpdateApiTokenResponse instantiates a new UpdateApiTokenResponse object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewUpdateApiTokenResponse() *UpdateApiTokenResponse {
	this := UpdateApiTokenResponse{}
	return &this
}

// NewUpdateApiTokenResponseWithDefaults instantiates a new UpdateApiTokenResponse object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewUpdateApiTokenResponseWithDefaults() *UpdateApiTokenResponse {
	this := UpdateApiTokenResponse{}
	return &this
}

// GetSuccess returns the Success field value if set, zero value otherwise.
func (o *UpdateApiTokenResponse) GetSuccess() bool {
	if o == nil || o.Success == nil {
		var ret bool
		return ret
	}
	return *o.Success
}

// GetSuccessOk returns a tuple with the Success field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UpdateApiTokenResponse) GetSuccessOk() (*bool, bool) {
	if o == nil || o.Success == nil {
		return nil, false
	}
	return o.Success, true
}

// HasSuccess returns a boolean if a field has been set.
func (o *UpdateApiTokenResponse) HasSuccess() bool {
	if o != nil && o.Success != nil {
		return true
	}

	return false
}

// SetSuccess gets a reference to the given bool and assigns it to the Success field.
func (o *UpdateApiTokenResponse) SetSuccess(v bool) {
	o.Success = &v
}

// GetToken returns the Token field value if set, zero value otherwise.
func (o *UpdateApiTokenResponse) GetToken() string {
	if o == nil || o.Token == nil {
		var ret string
		return ret
	}
	return *o.Token
}

// GetTokenOk returns a tuple with the Token field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UpdateApiTokenResponse) GetTokenOk() (*string, bool) {
	if o == nil || o.Token == nil {
		return nil, false
	}
	return o.Token, true
}

// HasToken returns a boolean if a field has been set.
func (o *UpdateApiTokenResponse) HasToken() bool {
	if o != nil && o.Token != nil {
		return true
	}

	return false
}

// SetToken gets a reference to the given string and assigns it to the Token field.
func (o *UpdateApiTokenResponse) SetToken(v string) {
	o.Token = &v
}

func (o UpdateApiTokenResponse) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Success != nil {
		toSerialize["success"] = o.Success
	}
	if o.Token != nil {
		toSerialize["token"] = o.Token
	}
	return json.Marshal(toSerialize)
}

type NullableUpdateApiTokenResponse struct {
	value *UpdateApiTokenResponse
	isSet bool
}

func (v NullableUpdateApiTokenResponse) Get() *UpdateApiTokenResponse {
	return v.value
}

func (v *NullableUpdateApiTokenResponse) Set(val *UpdateApiTokenResponse) {
	v.value = val
	v.isSet = true
}

func (v NullableUpdateApiTokenResponse) IsSet() bool {
	return v.isSet
}

func (v *NullableUpdateApiTokenResponse) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableUpdateApiTokenResponse(val *UpdateApiTokenResponse) *NullableUpdateApiTokenResponse {
	return &NullableUpdateApiTokenResponse{value: val, isSet: true}
}

func (v NullableUpdateApiTokenResponse) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableUpdateApiTokenResponse) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


