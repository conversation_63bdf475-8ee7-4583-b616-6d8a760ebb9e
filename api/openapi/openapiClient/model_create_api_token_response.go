/*
Devtron Labs

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

API version: 1.0.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package openapi

import (
	"encoding/json"
)

// CreateApiTokenResponse struct for CreateApiTokenResponse
type CreateApiTokenResponse struct {
	// Id of the api token created
	Id *int `json:"id"`
	// success or failure
	Success *bool `json:"success,omitempty"`
	// Token of that api-token
	Token *string `json:"token,omitempty"`
	// User Id associated with api-token
	UserId *int32 `json:"userId,omitempty"`
	// EmailId of that api-token user
	UserIdentifier *string `json:"userIdentifier,omitempty"`
}

// NewCreateApiTokenResponse instantiates a new CreateApiTokenResponse object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewCreateApiTokenResponse() *CreateApiTokenResponse {
	this := CreateApiTokenResponse{}
	return &this
}

// NewCreateApiTokenResponseWithDefaults instantiates a new CreateApiTokenResponse object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewCreateApiTokenResponseWithDefaults() *CreateApiTokenResponse {
	this := CreateApiTokenResponse{}
	return &this
}

// GetSuccess returns the Success field value if set, zero value otherwise.
func (o *CreateApiTokenResponse) GetSuccess() bool {
	if o == nil || o.Success == nil {
		var ret bool
		return ret
	}
	return *o.Success
}

// GetSuccessOk returns a tuple with the Success field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CreateApiTokenResponse) GetSuccessOk() (*bool, bool) {
	if o == nil || o.Success == nil {
		return nil, false
	}
	return o.Success, true
}

// HasSuccess returns a boolean if a field has been set.
func (o *CreateApiTokenResponse) HasSuccess() bool {
	if o != nil && o.Success != nil {
		return true
	}

	return false
}

// SetSuccess gets a reference to the given bool and assigns it to the Success field.
func (o *CreateApiTokenResponse) SetSuccess(v bool) {
	o.Success = &v
}

// GetToken returns the Token field value if set, zero value otherwise.
func (o *CreateApiTokenResponse) GetToken() string {
	if o == nil || o.Token == nil {
		var ret string
		return ret
	}
	return *o.Token
}

// GetTokenOk returns a tuple with the Token field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CreateApiTokenResponse) GetTokenOk() (*string, bool) {
	if o == nil || o.Token == nil {
		return nil, false
	}
	return o.Token, true
}

// HasToken returns a boolean if a field has been set.
func (o *CreateApiTokenResponse) HasToken() bool {
	if o != nil && o.Token != nil {
		return true
	}

	return false
}

// SetToken gets a reference to the given string and assigns it to the Token field.
func (o *CreateApiTokenResponse) SetToken(v string) {
	o.Token = &v
}

// GetUserId returns the UserId field value if set, zero value otherwise.
func (o *CreateApiTokenResponse) GetUserId() int32 {
	if o == nil || o.UserId == nil {
		var ret int32
		return ret
	}
	return *o.UserId
}

// GetUserIdOk returns a tuple with the UserId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CreateApiTokenResponse) GetUserIdOk() (*int32, bool) {
	if o == nil || o.UserId == nil {
		return nil, false
	}
	return o.UserId, true
}

// HasUserId returns a boolean if a field has been set.
func (o *CreateApiTokenResponse) HasUserId() bool {
	if o != nil && o.UserId != nil {
		return true
	}

	return false
}

// SetUserId gets a reference to the given int32 and assigns it to the UserId field.
func (o *CreateApiTokenResponse) SetUserId(v int32) {
	o.UserId = &v
}

// GetUserIdentifier returns the UserIdentifier field value if set, zero value otherwise.
func (o *CreateApiTokenResponse) GetUserIdentifier() string {
	if o == nil || o.UserIdentifier == nil {
		var ret string
		return ret
	}
	return *o.UserIdentifier
}

// GetUserIdentifierOk returns a tuple with the UserIdentifier field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CreateApiTokenResponse) GetUserIdentifierOk() (*string, bool) {
	if o == nil || o.UserIdentifier == nil {
		return nil, false
	}
	return o.UserIdentifier, true
}

// HasUserIdentifier returns a boolean if a field has been set.
func (o *CreateApiTokenResponse) HasUserIdentifier() bool {
	if o != nil && o.UserIdentifier != nil {
		return true
	}

	return false
}

// SetUserIdentifier gets a reference to the given string and assigns it to the UserIdentifier field.
func (o *CreateApiTokenResponse) SetUserIdentifier(v string) {
	o.UserIdentifier = &v
}

func (o CreateApiTokenResponse) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.Success != nil {
		toSerialize["success"] = o.Success
	}
	if o.Token != nil {
		toSerialize["token"] = o.Token
	}
	if o.UserId != nil {
		toSerialize["userId"] = o.UserId
	}
	if o.UserIdentifier != nil {
		toSerialize["userIdentifier"] = o.UserIdentifier
	}
	if o.Id != nil {
		toSerialize["id"] = o.Id
	}
	return json.Marshal(toSerialize)
}

type NullableCreateApiTokenResponse struct {
	value *CreateApiTokenResponse
	isSet bool
}

func (v NullableCreateApiTokenResponse) Get() *CreateApiTokenResponse {
	return v.value
}

func (v *NullableCreateApiTokenResponse) Set(val *CreateApiTokenResponse) {
	v.value = val
	v.isSet = true
}

func (v NullableCreateApiTokenResponse) IsSet() bool {
	return v.isSet
}

func (v *NullableCreateApiTokenResponse) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableCreateApiTokenResponse(val *CreateApiTokenResponse) *NullableCreateApiTokenResponse {
	return &NullableCreateApiTokenResponse{value: val, isSet: true}
}

func (v NullableCreateApiTokenResponse) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableCreateApiTokenResponse) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
