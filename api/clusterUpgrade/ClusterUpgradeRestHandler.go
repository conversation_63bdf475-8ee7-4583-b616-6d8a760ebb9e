package clusterUpgrade

import (
	"context"
	"errors"
	"github.com/devtron-labs/devtron/api/restHandler/common"
	"github.com/devtron-labs/devtron/pkg/auth/authorisation/casbin"
	"github.com/devtron-labs/devtron/pkg/auth/user"
	"github.com/devtron-labs/devtron/pkg/clusterUpgrade"
	"github.com/devtron-labs/devtron/pkg/clusterUpgrade/utils"
	"go.uber.org/zap"
	"net/http"
	"strconv"
)

type ClusterUpgradeRestHandler interface {
	ScanClusterForK8sVersionUpgrade(w http.ResponseWriter, r *http.Request)
	GetK8sVersionsList(w http.ResponseWriter, r *http.Request)
}

type ClusterUpgradeRestHandlerImpl struct {
	logger                *zap.SugaredLogger
	userService           user.UserService
	enforcer              casbin.Enforcer
	clusterUpgradeService clusterUpgrade.ClusterUpgradeService
}

func NewClusterUpgradeRestHandlerImpl(
	logger *zap.SugaredLogger,
	userService user.UserService,
	enforcer casbin.Enforcer,
	clusterUpgradeService clusterUpgrade.ClusterUpgradeService,
) *ClusterUpgradeRestHandlerImpl {
	return &ClusterUpgradeRestHandlerImpl{
		logger:                logger,
		userService:           userService,
		enforcer:              enforcer,
		clusterUpgradeService: clusterUpgradeService,
	}
}

func (impl *ClusterUpgradeRestHandlerImpl) ScanClusterForK8sVersionUpgrade(w http.ResponseWriter, r *http.Request) {
	userId, err := impl.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	v := r.URL.Query()
	targetK8sVersion := v.Get("targetK8sVersion")
	if len(targetK8sVersion) == 0 {
		common.WriteJsonResp(w, err, "please provide targetK8sVersion", http.StatusBadRequest)
		return
	}
	var clusterId int
	clusterIdStr := v.Get("clusterId")
	if clusterIdStr != "" {
		clusterId, err = strconv.Atoi(clusterIdStr)
		if err != nil {
			common.WriteJsonResp(w, err, "please send valid cluster Id", http.StatusBadRequest)
			return
		}
	}
	//rbac block starts from here
	token := r.Header.Get("token")
	if ok := impl.enforcer.Enforce(token, casbin.ResourceCluster, casbin.ActionCreate, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	//rbac block ends here

	res, err := impl.clusterUpgradeService.GetConsolidatedRespForClusterK8sVersionUpgradeAndPDB(context.Background(), targetK8sVersion, clusterId)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, res, http.StatusOK)
}

func (impl *ClusterUpgradeRestHandlerImpl) GetK8sVersionsList(w http.ResponseWriter, r *http.Request) {
	userId, err := impl.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	v := r.URL.Query()
	sourceK8sVersion := v.Get("sourceK8sVersion")
	if len(sourceK8sVersion) == 0 {
		common.WriteJsonResp(w, err, "please provide sourceK8sVersion", http.StatusBadRequest)
		return
	}
	reqSemverVersion, err := utils.ValidateIfSourceK8sVersionIsSemver(sourceK8sVersion)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	//rbac block starts from here
	token := r.Header.Get("token")
	if ok := impl.enforcer.Enforce(token, casbin.ResourceCluster, casbin.ActionCreate, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	//rbac block ends here

	res, err := impl.clusterUpgradeService.GetK8sVersionsList(reqSemverVersion)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, res, http.StatusOK)
}
