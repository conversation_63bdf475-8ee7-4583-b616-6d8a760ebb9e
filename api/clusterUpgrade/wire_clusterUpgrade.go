package clusterUpgrade

import (
	"github.com/devtron-labs/devtron/pkg/clusterUpgrade"
	"github.com/google/wire"
)

var ClusterUpgradeWireSet = wire.NewSet(
	clusterUpgrade.NewClusterUpgradeServiceImpl,
	wire.Bind(new(clusterUpgrade.ClusterUpgradeService), new(*clusterUpgrade.ClusterUpgradeServiceImpl)),
	NewClusterUpgradeRestHandlerImpl,
	wire.Bind(new(ClusterUpgradeRestHandler), new(*ClusterUpgradeRestHandlerImpl)),
	NewClusterUpgradeRouterImpl,
	wire.Bind(new(ClusterUpgradeRouter), new(*ClusterUpgradeRouterImpl)),
)

// minimal wire to be used with EA
var ClusterUpgradeWireSetEa = wire.NewSet(
	clusterUpgrade.NewClusterUpgradeServiceImpl,
	wire.Bind(new(clusterUpgrade.ClusterUpgradeService), new(*clusterUpgrade.ClusterUpgradeServiceImpl)),
	NewClusterUpgradeRestHandlerImpl,
	wire.Bind(new(ClusterUpgradeRestHandler), new(*ClusterUpgradeRestHandlerImpl)),
	NewClusterUpgradeRouterImpl,
	wire.Bind(new(ClusterUpgradeRouter), new(*ClusterUpgradeRouterImpl)),
)
