/*
 * Copyright (c) 2024. Devtron Inc.
 */

package router

import "github.com/gorilla/mux"

func (router BulkUpdateRouterImpl) initBulkUpdateRouterEnt(bulkRouter *mux.Router) {
	bulkRouter.Path("/{apiVersion:[a-zA-Z0-9/-]+}/{kind:[a-zA-Z0-9]+}/config").HandlerFunc(router.restHandler.GetBulkEditConfigV1Beta2).Methods("GET")
}

func (router BulkUpdateRouterImpl) initV1beta2RouterEnt(bulkRouter *mux.Router) {
	bulkRouter.Path("/v1beta2/application/dryrun").HandlerFunc(router.restHandler.DryRunBulkEditV1Beta2).Methods("POST")
	bulkRouter.Path("/v1beta2/application").HandlerFunc(router.restHandler.BulkEditV1Beta2).Methods("POST")
}
