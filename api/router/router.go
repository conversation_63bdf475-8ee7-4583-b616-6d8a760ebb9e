/*
 * Copyright (c) 2020-2024. Devtron Inc.
 */

package router

import (
	"encoding/json"
	"github.com/devtron-labs/devtron/api/apiToken"
	"github.com/devtron-labs/devtron/api/appStore"
	"github.com/devtron-labs/devtron/api/appStore/chartCategory"
	"github.com/devtron-labs/devtron/api/appStore/chartGroup"
	appStoreDeployment "github.com/devtron-labs/devtron/api/appStore/deployment"
	"github.com/devtron-labs/devtron/api/argoApplication"
	"github.com/devtron-labs/devtron/api/auth/authorisation/globalConfig"
	"github.com/devtron-labs/devtron/api/auth/sso"
	"github.com/devtron-labs/devtron/api/auth/user"
	"github.com/devtron-labs/devtron/api/auth/userGroup"
	"github.com/devtron-labs/devtron/api/chartRepo"
	"github.com/devtron-labs/devtron/api/chat"
	"github.com/devtron-labs/devtron/api/cluster"
	"github.com/devtron-labs/devtron/api/clusterUpgrade"
	"github.com/devtron-labs/devtron/api/dashboardEvent"
	"github.com/devtron-labs/devtron/api/deployment"
	"github.com/devtron-labs/devtron/api/deployment/deployEntityMigration"
	"github.com/devtron-labs/devtron/api/deployment/event"
	"github.com/devtron-labs/devtron/api/devtronResource"
	"github.com/devtron-labs/devtron/api/drift"
	"github.com/devtron-labs/devtron/api/externalLink"
	"github.com/devtron-labs/devtron/api/featureFlag"
	fluxApplication2 "github.com/devtron-labs/devtron/api/fluxApplication"
	"github.com/devtron-labs/devtron/api/globalPolicy"
	client "github.com/devtron-labs/devtron/api/helm-app"
	"github.com/devtron-labs/devtron/api/infraConfig"
	"github.com/devtron-labs/devtron/api/infrastructureDeployment"
	"github.com/devtron-labs/devtron/api/k8s/application"
	"github.com/devtron-labs/devtron/api/k8s/capacity"
	"github.com/devtron-labs/devtron/api/module"
	"github.com/devtron-labs/devtron/api/restHandler/common"
	"github.com/devtron-labs/devtron/api/router/app"
	"github.com/devtron-labs/devtron/api/router/app/configDiff"
	"github.com/devtron-labs/devtron/api/scanTool"
	"github.com/devtron-labs/devtron/api/scoop"
	"github.com/devtron-labs/devtron/api/server"
	"github.com/devtron-labs/devtron/api/systemNetworkController"
	"github.com/devtron-labs/devtron/api/team"
	terminal2 "github.com/devtron-labs/devtron/api/terminal"
	"github.com/devtron-labs/devtron/api/userResource"
	webhookHelm "github.com/devtron-labs/devtron/api/webhook/helm"
	"github.com/devtron-labs/devtron/client/cron"
	"github.com/devtron-labs/devtron/client/dashboard"
	"github.com/devtron-labs/devtron/client/proxy"
	"github.com/devtron-labs/devtron/client/telemetry"
	"github.com/devtron-labs/devtron/enterprise/api/artifactPromotionPolicy"
	"github.com/devtron-labs/devtron/enterprise/api/commonPolicyActions"
	"github.com/devtron-labs/devtron/enterprise/api/deploymentWindow"
	"github.com/devtron-labs/devtron/enterprise/api/drafts"
	"github.com/devtron-labs/devtron/enterprise/api/globalTag"
	"github.com/devtron-labs/devtron/enterprise/api/lockConfiguation"
	"github.com/devtron-labs/devtron/enterprise/api/protect"
	"github.com/devtron-labs/devtron/enterprise/api/resourceScan"
	"github.com/devtron-labs/devtron/licensing/handler"
	"github.com/devtron-labs/devtron/pkg/terminal"
	"github.com/devtron-labs/devtron/util"
	"github.com/gorilla/mux"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"go.uber.org/zap"
	"net/http"
)

type MuxRouter struct {
	logger                             *zap.SugaredLogger
	Router                             *mux.Router
	JobRouter                          JobRouter
	EnvironmentClusterMappingsRouter   cluster.EnvironmentRouter
	ClusterRouter                      cluster.ClusterRouter
	WebHookRouter                      WebhookRouter
	UserAuthRouter                     user.UserAuthRouter
	GitProviderRouter                  GitProviderRouter
	GitHostRouter                      GitHostRouter
	DockerRegRouter                    DockerRegRouter
	NotificationRouter                 NotificationRouter
	TeamRouter                         team.TeamRouter
	UserRouter                         user.UserRouter
	ChartRefRouter                     ChartRefRouter
	ConfigMapRouter                    ConfigMapRouter
	AppStoreRouter                     appStore.AppStoreRouter
	AppStoreRouterEnterprise           appStore.AppStoreRouterEnterprise
	ChartRepositoryRouter              chartRepo.ChartRepositoryRouter
	ReleaseMetricsRouter               ReleaseMetricsRouter
	deploymentGroupRouter              DeploymentGroupRouter
	chartGroupRouter                   chartGroup.ChartGroupRouter
	batchOperationRouter               BatchOperationRouter
	imageScanRouter                    ImageScanRouter
	policyRouter                       PolicyRouter
	gitOpsConfigRouter                 GitOpsConfigRouter
	dashboardRouter                    dashboard.DashboardRouter
	proxyRouter                        proxy.ProxyRouter
	attributesRouter                   AttributesRouter
	userAttributesRouter               UserAttributesRouter
	commonRouter                       CommonRouter
	grafanaRouter                      GrafanaRouter
	ssoLoginRouter                     sso.SsoLoginRouter
	telemetryRouter                    TelemetryRouter
	telemetryWatcher                   telemetry.TelemetryEventClient
	bulkUpdateRouter                   BulkUpdateRouter
	WebhookListenerRouter              WebhookListenerRouter
	appRouter                          app.AppRouter
	coreAppRouter                      CoreAppRouter
	helmAppRouter                      client.HelmAppRouter
	k8sApplicationRouter               application.K8sApplicationRouter
	pProfRouter                        PProfRouter
	deploymentConfigRouter             deployment.DeploymentConfigRouter
	dashboardTelemetryRouter           dashboardEvent.DashboardTelemetryRouter
	commonDeploymentRouter             appStoreDeployment.CommonDeploymentRouter
	globalPluginRouter                 GlobalPluginRouter
	externalLinkRouter                 externalLink.ExternalLinkRouter
	moduleRouter                       module.ModuleRouter
	serverRouter                       server.ServerRouter
	apiTokenRouter                     apiToken.ApiTokenRouter
	helmApplicationStatusUpdateHandler cron.CdApplicationStatusUpdateHandler
	k8sCapacityRouter                  capacity.K8sCapacityRouter
	webhookHelmRouter                  webhookHelm.WebhookHelmRouter
	globalCMCSRouter                   GlobalCMCSRouter
	userTerminalAccessRouter           terminal2.UserTerminalAccessRouter
	ciStatusUpdateCron                 cron.CiStatusUpdateCron
	resourceGroupingRouter             ResourceGroupingRouter
	globalTagRouter                    globalTag.GlobalTagRouter
	rbacRoleRouter                     user.RbacRoleRouter
	globalPolicyRouter                 globalPolicy.GlobalPolicyRouter
	configDraftRouter                  drafts.ConfigDraftRouter
	resourceProtectionRouter           protect.ResourceProtectionRouter
	scopedVariableRouter               ScopedVariableRouter
	featureFlagRouter                  featureFlag.FeatureFlagRouter
	driftRouter                        drift.DriftRouter
	ciTriggerCron                      cron.CiTriggerCron
	resourceFilterRouter               ResourceFilterRouter
	devtronResourceRouter              devtronResource.DevtronResourceRouter
	devtronResourceHistoryRouter       devtronResource.HistoryRouter
	devtronResourceTemplateRouter      devtronResource.TemplateRouter
	globalAuthorisationConfigRouter    globalConfig.AuthorisationConfigRouter
	lockConfigurationRouter            lockConfiguation.LockConfigurationRouter
	imageDigestPolicyRouter            ImageDigestPolicyRouter
	deploymentConfigurationRouter      configDiff.DeploymentConfigurationRouter
	infraConfigRouter                  infraConfig.InfraConfigRouter
	argoApplicationRouter              argoApplication.ArgoApplicationRouter
	fluxApplicationRouter              fluxApplication2.FluxApplicationRouter
	commonPolicyRouter                 commonPolicyActions.CommonPolicyRouter
	deploymentWindowRouter             deploymentWindow.DeploymentWindowRouter
	artifactPromotionPolicy            artifactPromotionPolicy.Router
	scanningResultRouter               resourceScan.ScanningResultRouter
	scoopRouter                        scoop.Router
	syncDevImageRouter                 SyncDevImageRouter
	deployEntityMigrationRouter        deployEntityMigration.MigrationRouter
	systemNetworkControllerRouter      systemNetworkController.SysNetControllerRouter
	userGroupRouter                    userGroup.Router
	deploymentEventRouter              event.DeploymentEventRouter
	clusterUpgradeRouter               clusterUpgrade.ClusterUpgradeRouter
	chatRouter                         chat.ChatRouter
	scanToolMetadataRouter             scanTool.ScanToolRouter
	userResourceRouter                 userResource.Router
	licenseRouter                      handler.LicenseRouter
	infrastructureDeploymentRouter     infrastructureDeployment.InfrastructureDeploymentRouter
	chartCategoryRouter                chartCategory.ChartCategoryRouter
}

func NewMuxRouter(logger *zap.SugaredLogger,
	EnvironmentClusterMappingsRouter cluster.EnvironmentRouter, ClusterRouter cluster.ClusterRouter,
	WebHookRouter WebhookRouter, UserAuthRouter user.UserAuthRouter,
	GitProviderRouter GitProviderRouter, GitHostRouter GitHostRouter,
	DockerRegRouter DockerRegRouter,
	NotificationRouter NotificationRouter,
	TeamRouter team.TeamRouter,
	UserRouter user.UserRouter,
	ChartRefRouter ChartRefRouter, ConfigMapRouter ConfigMapRouter, AppStoreRouter appStore.AppStoreRouter, AppStoreRouterEnterprise appStore.AppStoreRouterEnterprise, chartRepositoryRouter chartRepo.ChartRepositoryRouter,
	ReleaseMetricsRouter ReleaseMetricsRouter, deploymentGroupRouter DeploymentGroupRouter, batchOperationRouter BatchOperationRouter,
	chartGroupRouter chartGroup.ChartGroupRouter, imageScanRouter ImageScanRouter,
	policyRouter PolicyRouter, gitOpsConfigRouter GitOpsConfigRouter, dashboardRouter dashboard.DashboardRouter, attributesRouter AttributesRouter, userAttributesRouter UserAttributesRouter,
	commonRouter CommonRouter, grafanaRouter GrafanaRouter, ssoLoginRouter sso.SsoLoginRouter, telemetryRouter TelemetryRouter, telemetryWatcher telemetry.TelemetryEventClient, bulkUpdateRouter BulkUpdateRouter, webhookListenerRouter WebhookListenerRouter, appRouter app.AppRouter,
	coreAppRouter CoreAppRouter, helmAppRouter client.HelmAppRouter, k8sApplicationRouter application.K8sApplicationRouter,
	pProfRouter PProfRouter, deploymentConfigRouter deployment.DeploymentConfigRouter, dashboardTelemetryRouter dashboardEvent.DashboardTelemetryRouter,
	commonDeploymentRouter appStoreDeployment.CommonDeploymentRouter, externalLinkRouter externalLink.ExternalLinkRouter,
	globalPluginRouter GlobalPluginRouter, moduleRouter module.ModuleRouter,
	serverRouter server.ServerRouter, apiTokenRouter apiToken.ApiTokenRouter,
	helmApplicationStatusUpdateHandler cron.CdApplicationStatusUpdateHandler, k8sCapacityRouter capacity.K8sCapacityRouter,
	webhookHelmRouter webhookHelm.WebhookHelmRouter, globalCMCSRouter GlobalCMCSRouter,
	userTerminalAccessRouter terminal2.UserTerminalAccessRouter,
	jobRouter JobRouter, ciStatusUpdateCron cron.CiStatusUpdateCron, resourceGroupingRouter ResourceGroupingRouter,
	globalTagRouter globalTag.GlobalTagRouter, rbacRoleRouter user.RbacRoleRouter,
	globalPolicyRouter globalPolicy.GlobalPolicyRouter, configDraftRouter drafts.ConfigDraftRouter, resourceProtectionRouter protect.ResourceProtectionRouter,
	scopedVariableRouter ScopedVariableRouter, ciTriggerCron cron.CiTriggerCron,
	resourceFilterRouter ResourceFilterRouter,
	devtronResourceRouter devtronResource.DevtronResourceRouter,
	devtronResourceHistoryRouter devtronResource.HistoryRouter,
	devtronResourceTemplateRouter devtronResource.TemplateRouter,
	globalAuthorisationConfigRouter globalConfig.AuthorisationConfigRouter,
	lockConfigurationRouter lockConfiguation.LockConfigurationRouter,
	proxyRouter proxy.ProxyRouter,
	imageDigestPolicyRouter ImageDigestPolicyRouter,
	deploymentConfigurationRouter configDiff.DeploymentConfigurationRouter,
	infraConfigRouter infraConfig.InfraConfigRouter,
	argoApplicationRouter argoApplication.ArgoApplicationRouter, fluxApplicationRouter fluxApplication2.FluxApplicationRouter, deploymentWindowRouter deploymentWindow.DeploymentWindowRouter,
	commonPolicyRouter commonPolicyActions.CommonPolicyRouter,
	artifactPromotionPolicy artifactPromotionPolicy.Router,
	scanningResultRouter resourceScan.ScanningResultRouter,
	scoopRouter scoop.Router, featureFlagRouter featureFlag.FeatureFlagRouter, driftRouter drift.DriftRouter, syncDevImageRouter SyncDevImageRouter,
	deployEntityMigrationRouter deployEntityMigration.MigrationRouter, systemNetworkControllerRouter systemNetworkController.SysNetControllerRouter, userGroupRouter userGroup.Router,
	deploymentEventRouter event.DeploymentEventRouter,
	clusterUpgradeRouter clusterUpgrade.ClusterUpgradeRouter,
	chatRouter chat.ChatRouter,
	scanToolMetadataRouter scanTool.ScanToolRouter,
	userResourceRouter userResource.Router,
	licenseRouter handler.LicenseRouter,
	infrastructureDeploymentRouter infrastructureDeployment.InfrastructureDeploymentRouter,
	chartCategoryRouter chartCategory.ChartCategoryRouter,
) *MuxRouter {

	return &MuxRouter{
		Router:                             mux.NewRouter(),
		EnvironmentClusterMappingsRouter:   EnvironmentClusterMappingsRouter,
		ClusterRouter:                      ClusterRouter,
		WebHookRouter:                      WebHookRouter,
		UserAuthRouter:                     UserAuthRouter,
		DockerRegRouter:                    DockerRegRouter,
		GitProviderRouter:                  GitProviderRouter,
		GitHostRouter:                      GitHostRouter,
		NotificationRouter:                 NotificationRouter,
		TeamRouter:                         TeamRouter,
		logger:                             logger,
		UserRouter:                         UserRouter,
		ChartRefRouter:                     ChartRefRouter,
		ConfigMapRouter:                    ConfigMapRouter,
		AppStoreRouter:                     AppStoreRouter,
		AppStoreRouterEnterprise:           AppStoreRouterEnterprise,
		ChartRepositoryRouter:              chartRepositoryRouter,
		ReleaseMetricsRouter:               ReleaseMetricsRouter,
		deploymentGroupRouter:              deploymentGroupRouter,
		batchOperationRouter:               batchOperationRouter,
		chartGroupRouter:                   chartGroupRouter,
		imageScanRouter:                    imageScanRouter,
		policyRouter:                       policyRouter,
		gitOpsConfigRouter:                 gitOpsConfigRouter,
		attributesRouter:                   attributesRouter,
		userAttributesRouter:               userAttributesRouter,
		dashboardRouter:                    dashboardRouter,
		proxyRouter:                        proxyRouter,
		commonRouter:                       commonRouter,
		grafanaRouter:                      grafanaRouter,
		ssoLoginRouter:                     ssoLoginRouter,
		telemetryRouter:                    telemetryRouter,
		telemetryWatcher:                   telemetryWatcher,
		bulkUpdateRouter:                   bulkUpdateRouter,
		WebhookListenerRouter:              webhookListenerRouter,
		appRouter:                          appRouter,
		coreAppRouter:                      coreAppRouter,
		helmAppRouter:                      helmAppRouter,
		k8sApplicationRouter:               k8sApplicationRouter,
		pProfRouter:                        pProfRouter,
		deploymentConfigRouter:             deploymentConfigRouter,
		dashboardTelemetryRouter:           dashboardTelemetryRouter,
		commonDeploymentRouter:             commonDeploymentRouter,
		externalLinkRouter:                 externalLinkRouter,
		globalPluginRouter:                 globalPluginRouter,
		moduleRouter:                       moduleRouter,
		serverRouter:                       serverRouter,
		apiTokenRouter:                     apiTokenRouter,
		helmApplicationStatusUpdateHandler: helmApplicationStatusUpdateHandler,
		k8sCapacityRouter:                  k8sCapacityRouter,
		webhookHelmRouter:                  webhookHelmRouter,
		globalCMCSRouter:                   globalCMCSRouter,
		userTerminalAccessRouter:           userTerminalAccessRouter,
		ciStatusUpdateCron:                 ciStatusUpdateCron,
		JobRouter:                          jobRouter,
		resourceGroupingRouter:             resourceGroupingRouter,
		globalTagRouter:                    globalTagRouter,
		rbacRoleRouter:                     rbacRoleRouter,
		globalPolicyRouter:                 globalPolicyRouter,
		scopedVariableRouter:               scopedVariableRouter,
		ciTriggerCron:                      ciTriggerCron,
		configDraftRouter:                  configDraftRouter,
		resourceProtectionRouter:           resourceProtectionRouter,
		resourceFilterRouter:               resourceFilterRouter,
		devtronResourceRouter:              devtronResourceRouter,
		devtronResourceHistoryRouter:       devtronResourceHistoryRouter,
		devtronResourceTemplateRouter:      devtronResourceTemplateRouter,
		globalAuthorisationConfigRouter:    globalAuthorisationConfigRouter,
		lockConfigurationRouter:            lockConfigurationRouter,
		imageDigestPolicyRouter:            imageDigestPolicyRouter,
		deploymentConfigurationRouter:      deploymentConfigurationRouter,
		infraConfigRouter:                  infraConfigRouter,
		argoApplicationRouter:              argoApplicationRouter,
		fluxApplicationRouter:              fluxApplicationRouter,
		deploymentWindowRouter:             deploymentWindowRouter,
		commonPolicyRouter:                 commonPolicyRouter,
		artifactPromotionPolicy:            artifactPromotionPolicy,
		scanningResultRouter:               scanningResultRouter,
		scoopRouter:                        scoopRouter,
		deployEntityMigrationRouter:        deployEntityMigrationRouter,
		featureFlagRouter:                  featureFlagRouter,
		driftRouter:                        driftRouter,
		syncDevImageRouter:                 syncDevImageRouter,
		systemNetworkControllerRouter:      systemNetworkControllerRouter,
		userGroupRouter:                    userGroupRouter,
		deploymentEventRouter:              deploymentEventRouter,
		clusterUpgradeRouter:               clusterUpgradeRouter,
		chatRouter:                         chatRouter,
		scanToolMetadataRouter:             scanToolMetadataRouter,
		userResourceRouter:                 userResourceRouter,
		licenseRouter:                      licenseRouter,
		infrastructureDeploymentRouter:     infrastructureDeploymentRouter,
		chartCategoryRouter:                chartCategoryRouter,
	}
}

func (r MuxRouter) Init() {
	r.Router.PathPrefix("/orchestrator/api/vi/pod/exec/ws").Handler(terminal.CreateAttachHandler("/orchestrator/api/vi/pod/exec/ws"))

	r.Router.StrictSlash(true)
	r.Router.Handle("/metrics", promhttp.Handler())
	// prometheus.MustRegister(pipeline.CiTriggerCounter)
	// prometheus.MustRegister(app.CdTriggerCounter)
	r.Router.Path("/health").HandlerFunc(func(writer http.ResponseWriter, request *http.Request) {
		writer.Header().Set("Content-Type", "application/json")
		writer.WriteHeader(200)
		response := common.Response{}
		response.Code = 200
		response.Result = "OK"
		b, err := json.Marshal(response)
		if err != nil {
			b = []byte("OK")
			r.logger.Errorw("Unexpected error in apiError", "err", err)
		}
		_, _ = writer.Write(b)
	})

	r.Router.Path("/orchestrator/version").HandlerFunc(func(writer http.ResponseWriter, request *http.Request) {
		writer.Header().Set("Content-Type", "application/json")
		writer.WriteHeader(200)
		response := common.Response{}
		response.Code = 200
		response.Result = util.GetDevtronVersion()
		b, err := json.Marshal(response)
		if err != nil {
			b = []byte("OK")
			r.logger.Errorw("Unexpected error in apiError", "err", err)
		}
		_, _ = writer.Write(b)
	})
	coreAppRouter := r.Router.PathPrefix("/orchestrator/core").Subrouter()
	r.coreAppRouter.initCoreAppRouter(coreAppRouter)

	appSubRouter := r.Router.PathPrefix("/orchestrator/app").Subrouter()
	r.appRouter.InitAppRouter(appSubRouter)

	jobConfigRouter := r.Router.PathPrefix("/orchestrator/job").Subrouter()
	r.JobRouter.InitJobRouter(jobConfigRouter)

	environmentClusterMappingsRouter := r.Router.PathPrefix("/orchestrator/env").Subrouter()
	r.EnvironmentClusterMappingsRouter.InitEnvironmentClusterMappingsRouter(environmentClusterMappingsRouter)
	r.resourceGroupingRouter.InitResourceGroupingRouter(environmentClusterMappingsRouter)

	clusterRouter := r.Router.PathPrefix("/orchestrator/cluster").Subrouter()
	r.ClusterRouter.InitClusterRouter(clusterRouter)
	r.clusterUpgradeRouter.InitClusterUpgradeRouter(clusterRouter)

	webHookRouter := r.Router.PathPrefix("/orchestrator/webhook").Subrouter()
	r.WebHookRouter.intWebhookRouter(webHookRouter)

	rootRouter := r.Router.PathPrefix("/orchestrator").Subrouter()
	r.UserAuthRouter.InitUserAuthRouter(rootRouter)

	resourceFilterRouter := r.Router.PathPrefix("/orchestrator/filters").Subrouter()
	r.resourceFilterRouter.InitResourceFilterRouter(resourceFilterRouter)

	imageDigestPolicyRouter := r.Router.PathPrefix("/orchestrator/digest-policy").Subrouter()
	r.imageDigestPolicyRouter.initImageDigestPolicyRouter(imageDigestPolicyRouter)

	gitRouter := r.Router.PathPrefix("/orchestrator/git").Subrouter()
	r.GitProviderRouter.InitGitProviderRouter(gitRouter)
	r.GitHostRouter.InitGitHostRouter(gitRouter)

	dockerRouter := r.Router.PathPrefix("/orchestrator/docker").Subrouter()
	r.DockerRegRouter.InitDockerRegRouter(dockerRouter)

	notificationRouter := r.Router.PathPrefix("/orchestrator/notification").Subrouter()
	r.NotificationRouter.InitNotificationRegRouter(notificationRouter)

	teamRouter := r.Router.PathPrefix("/orchestrator/team").Subrouter()
	r.TeamRouter.InitTeamRouter(teamRouter)

	userRouter := r.Router.PathPrefix("/orchestrator/user").Subrouter()
	r.UserRouter.InitUserRouter(userRouter)

	userGroupRouter := r.Router.PathPrefix("/orchestrator/user-group").Subrouter()
	r.userGroupRouter.InitUserGroupRouter(userGroupRouter)

	chartRefRouter := r.Router.PathPrefix("/orchestrator/chartref").Subrouter()
	r.ChartRefRouter.initChartRefRouter(chartRefRouter)

	configRouter := r.Router.PathPrefix("/orchestrator/config").Subrouter()
	r.ConfigMapRouter.initConfigMapRouter(configRouter)
	r.deploymentConfigurationRouter.InitDeploymentConfigurationRouter(configRouter)

	appStoreRouter := r.Router.PathPrefix("/orchestrator/app-store").Subrouter()
	r.AppStoreRouter.Init(appStoreRouter)
	r.AppStoreRouterEnterprise.Init(appStoreRouter)

	chartRepoRouter := r.Router.PathPrefix("/orchestrator/chart-repo").Subrouter()
	r.ChartRepositoryRouter.Init(chartRepoRouter)

	deploymentMetricsRouter := r.Router.PathPrefix("/orchestrator/deployment-metrics").Subrouter()
	r.ReleaseMetricsRouter.initReleaseMetricsRouter(deploymentMetricsRouter)

	deploymentGroupRouter := r.Router.PathPrefix("/orchestrator/deployment-group").Subrouter()
	r.deploymentGroupRouter.initDeploymentGroupRouter(deploymentGroupRouter)

	r.batchOperationRouter.initBatchOperationRouter(rootRouter)

	chartGroupRouter := r.Router.PathPrefix("/orchestrator/chart-group").Subrouter()
	r.chartGroupRouter.InitChartGroupRouter(chartGroupRouter)

	imageScanRouter := r.Router.PathPrefix("/orchestrator/security/scan").Subrouter()
	r.imageScanRouter.InitImageScanRouter(imageScanRouter)
	r.scanToolMetadataRouter.InitScanToolMetadataRouter(imageScanRouter)

	policyRouter := r.Router.PathPrefix("/orchestrator/security/policy").Subrouter()
	r.policyRouter.InitPolicyRouter(policyRouter)

	gitOpsRouter := r.Router.PathPrefix("/orchestrator/gitops").Subrouter()
	r.gitOpsConfigRouter.InitGitOpsConfigRouter(gitOpsRouter)

	attributeRouter := r.Router.PathPrefix("/orchestrator/attributes").Subrouter()
	r.attributesRouter.InitAttributesRouter(attributeRouter)

	userAttributeRouter := r.Router.PathPrefix("/orchestrator/attributes/user").Subrouter()
	r.userAttributesRouter.InitUserAttributesRouter(userAttributeRouter)

	dashboardRouter := r.Router.PathPrefix("/dashboard").Subrouter()
	r.dashboardRouter.InitDashboardRouter(dashboardRouter)

	proxyRouter := r.Router.PathPrefix("/proxy").Subrouter()
	r.proxyRouter.InitProxyRouter(proxyRouter)

	grafanaRouter := r.Router.PathPrefix("/grafana").Subrouter()
	r.grafanaRouter.initGrafanaRouter(grafanaRouter)

	r.Router.Path("/").HandlerFunc(func(writer http.ResponseWriter, request *http.Request) {
		http.Redirect(writer, request, "/dashboard", 301)
	})

	commonRouter := r.Router.PathPrefix("/orchestrator/global").Subrouter()
	r.commonRouter.InitCommonRouter(commonRouter)
	r.scopedVariableRouter.InitScopedVariableRouter(commonRouter)
	r.featureFlagRouter.InitFeatureFlagRouter(commonRouter)

	driftRouter := r.Router.PathPrefix("/orchestrator/drift").Subrouter()
	r.driftRouter.InitRoutes(driftRouter)

	ssoLoginRouter := r.Router.PathPrefix("/orchestrator/sso").Subrouter()
	r.ssoLoginRouter.InitSsoLoginRouter(ssoLoginRouter)

	telemetryRouter := r.Router.PathPrefix("/orchestrator/telemetry").Subrouter()
	r.telemetryRouter.InitTelemetryRouter(telemetryRouter)

	bulkUpdateRouter := r.Router.PathPrefix("/orchestrator/batch").Subrouter()
	r.bulkUpdateRouter.initBulkUpdateRouter(bulkUpdateRouter)

	webhookListenerRouter := r.Router.PathPrefix("/orchestrator/webhook/git").Subrouter()
	r.WebhookListenerRouter.InitWebhookListenerRouter(webhookListenerRouter)

	k8sApp := r.Router.PathPrefix("/orchestrator/k8s").Subrouter()
	r.k8sApplicationRouter.InitK8sApplicationRouter(k8sApp)

	pProfListenerRouter := r.Router.PathPrefix("/orchestrator/debug/pprof").Subrouter()
	r.pProfRouter.initPProfRouter(pProfListenerRouter)

	globalPluginRouter := r.Router.PathPrefix("/orchestrator/plugin/global").Subrouter()
	r.globalPluginRouter.initGlobalPluginRouter(globalPluginRouter)

	//  deployment router starts
	deploymentConfigSubRouter := r.Router.PathPrefix("/orchestrator/deployment/template").Subrouter()
	r.deploymentConfigRouter.Init(deploymentConfigSubRouter)
	// deployment router ends

	//  dashboard event router starts
	dashboardTelemetryRouter := r.Router.PathPrefix("/orchestrator/dashboard-event").Subrouter()
	r.dashboardTelemetryRouter.Init(dashboardTelemetryRouter)
	// dashboard event router ends

	// GitOps,Acd + HelmCLi both apps deployment related api's
	applicationSubRouter := r.Router.PathPrefix("/orchestrator/application").Subrouter()
	r.commonDeploymentRouter.Init(applicationSubRouter)
	// this router must placed after commonDeploymentRouter
	r.helmAppRouter.InitAppListRouter(applicationSubRouter)

	externalLinkRouter := r.Router.PathPrefix("/orchestrator/external-links").Subrouter()
	r.externalLinkRouter.InitExternalLinkRouter(externalLinkRouter)

	// module router
	moduleRouter := r.Router.PathPrefix("/orchestrator/module").Subrouter()
	r.moduleRouter.Init(moduleRouter)

	// server router
	serverRouter := r.Router.PathPrefix("/orchestrator/server").Subrouter()
	r.serverRouter.Init(serverRouter)

	// api-token router
	apiTokenRouter := r.Router.PathPrefix("/orchestrator/api-token").Subrouter()
	r.apiTokenRouter.InitApiTokenRouter(apiTokenRouter)

	k8sCapacityApp := r.Router.PathPrefix("/orchestrator/k8s/capacity").Subrouter()
	r.k8sCapacityRouter.InitK8sCapacityRouter(k8sCapacityApp)

	// webhook helm app router
	webhookHelmRouter := r.Router.PathPrefix("/orchestrator/webhook/helm").Subrouter()
	r.webhookHelmRouter.InitWebhookHelmRouter(webhookHelmRouter)

	globalCMCSRouter := r.Router.PathPrefix("/orchestrator/global/cm-cs").Subrouter()
	r.globalCMCSRouter.initGlobalCMCSRouter(globalCMCSRouter)

	userTerminalAccessRouter := r.Router.PathPrefix("/orchestrator/user/terminal").Subrouter()
	r.userTerminalAccessRouter.InitTerminalAccessRouter(userTerminalAccessRouter)

	// global-tags router
	globalTagSubRouter := r.Router.PathPrefix("/orchestrator/global-tag").Subrouter()
	r.globalTagRouter.InitGlobalTagRouter(globalTagSubRouter)

	// lock configuration
	lockConfigurationRouter := r.Router.PathPrefix("/orchestrator/config/lock").Subrouter()
	r.lockConfigurationRouter.InitLockConfigurationRouter(lockConfigurationRouter)

	rbacRoleRouter := r.Router.PathPrefix("/orchestrator/rbac/role").Subrouter()
	r.rbacRoleRouter.InitRbacRoleRouter(rbacRoleRouter)

	globalPolicyRouter := r.Router.PathPrefix("/orchestrator/policy").Subrouter()
	r.globalPolicyRouter.InitGlobalPolicyRouter(globalPolicyRouter)
	draftRouter := r.Router.PathPrefix("/orchestrator/draft").Subrouter()
	r.configDraftRouter.InitConfigDraftRouter(draftRouter)

	protectRouter := r.Router.PathPrefix("/orchestrator/protect").Subrouter()
	r.resourceProtectionRouter.InitResourceProtectionRouter(protectRouter)

	devtronResourceHistoryRouter := r.Router.PathPrefix("/orchestrator/resource/history").Subrouter()
	r.devtronResourceHistoryRouter.InitDtResourceHistoryRouter(devtronResourceHistoryRouter)

	templateRouter := r.Router.PathPrefix("/orchestrator/resource/template").Subrouter()
	r.devtronResourceTemplateRouter.InitTemplateRouter(templateRouter)

	devtronResourceRouter := r.Router.PathPrefix("/orchestrator/resource").Subrouter()
	r.devtronResourceRouter.InitDevtronResourceRouter(devtronResourceRouter)

	systemNetworkControllerRouter := r.Router.PathPrefix("/orchestrator/system-network-controller").Subrouter()
	r.systemNetworkControllerRouter.InitSystemNetworkControllerRouter(systemNetworkControllerRouter)

	globalAuthorisationConfigRouter := r.Router.PathPrefix("/orchestrator/authorisation").Subrouter()
	r.globalAuthorisationConfigRouter.InitAuthorisationConfigRouter(globalAuthorisationConfigRouter)

	userResourcesRouter := r.Router.PathPrefix("/orchestrator/user/resource").Subrouter()
	r.userResourceRouter.InitUserResourceRouter(userResourcesRouter)

	infraConfigRouter := r.Router.PathPrefix("/orchestrator/infra-config").Subrouter()
	r.infraConfigRouter.InitInfraConfigRouter(infraConfigRouter)

	argoApplicationRouter := r.Router.PathPrefix("/orchestrator/argo-application").Subrouter()
	r.argoApplicationRouter.InitArgoApplicationRouter(argoApplicationRouter)

	fluxApplicationRouter := r.Router.PathPrefix("/orchestrator/flux-application").Subrouter()
	r.fluxApplicationRouter.InitFluxApplicationRouter(fluxApplicationRouter)

	commonPolicyRouter := r.Router.PathPrefix("/orchestrator/global/policy").Subrouter()
	r.commonPolicyRouter.InitCommonPolicyRouter(commonPolicyRouter)

	deploymentWindowRouter := r.Router.PathPrefix("/orchestrator/deployment-window").Subrouter()
	r.deploymentWindowRouter.InitDeploymentWindowRouter(deploymentWindowRouter)

	artifactPromotionPolicyRouter := r.Router.PathPrefix("/orchestrator/artifact-promotion/policy").Subrouter()
	r.artifactPromotionPolicy.InitRouter(artifactPromotionPolicyRouter)

	scanResultRouter := r.Router.PathPrefix("/orchestrator/scan-result").Subrouter()
	r.scanningResultRouter.InitScanningResultRouter(scanResultRouter)

	scoopRouter := r.Router.PathPrefix("/orchestrator/scoop").Subrouter()
	r.scoopRouter.InitScoopRouter(scoopRouter)

	devImageSyncRouter := r.Router.PathPrefix("/orchestrator/dev").Subrouter()
	r.syncDevImageRouter.InitSyncDevImageRouter(devImageSyncRouter)

	deployEntityMigrationRouter := r.Router.PathPrefix("/orchestrator/migration/deploy").Subrouter() //if more entity migrations are introduced, to move deploy router inside that
	r.deployEntityMigrationRouter.InitDeployEntityMigrationRouter(deployEntityMigrationRouter)

	deploymentEventRouter := r.Router.PathPrefix("/orchestrator/deployment-event").Subrouter()
	r.deploymentEventRouter.Init(deploymentEventRouter)

	chatRouter := r.Router.PathPrefix("/orchestrator/intelligence").Subrouter()
	r.chatRouter.InitChatRouter(chatRouter)

	licenseRouter := r.Router.PathPrefix("/orchestrator/license").Subrouter()
	r.licenseRouter.InitLicenseRouter(licenseRouter)

	infrastructureRouter := r.Router.PathPrefix("/orchestrator/infrastructure").Subrouter()
	r.infrastructureDeploymentRouter.Init(infrastructureRouter)

	chartCategoryRouter := r.Router.PathPrefix("/orchestrator/chart-category").Subrouter()
	r.chartCategoryRouter.InitChartCategoryRouter(chartCategoryRouter)

}
