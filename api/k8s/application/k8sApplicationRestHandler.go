/*
 * Copyright (c) 2024. Devtron Inc.
 */

package application

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/devtron-labs/common-lib-private/utils"
	"github.com/devtron-labs/devtron/api/bean"
	"github.com/devtron-labs/devtron/api/bean/AppView"
	client "github.com/devtron-labs/devtron/api/helm-app/service"
	rbac2 "github.com/devtron-labs/devtron/pkg/cluster/rbac"
	clusterRead "github.com/devtron-labs/devtron/pkg/cluster/read"
	helper2 "github.com/devtron-labs/devtron/pkg/fluxApplication/helper"
	util3 "github.com/devtron-labs/devtron/pkg/k8s/application/util"
	"github.com/go-pg/pg"
	"go.opentelemetry.io/otel"
	"gopkg.in/go-playground/validator.v9"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"net/http"
	"path/filepath"
	"runtime/debug"
	"strconv"
	"strings"

	util4 "github.com/devtron-labs/common-lib/utils/k8s"
	k8sCommonBean "github.com/devtron-labs/common-lib/utils/k8s/commonBean"
	"github.com/devtron-labs/common-lib/utils/k8sObjectsUtil"
	"github.com/devtron-labs/devtron/api/connector"
	"github.com/devtron-labs/devtron/api/helm-app/gRPC"
	"github.com/devtron-labs/devtron/api/restHandler/common"
	util2 "github.com/devtron-labs/devtron/internal/util"
	"github.com/devtron-labs/devtron/pkg/argoApplication/helper"
	"github.com/devtron-labs/devtron/pkg/argoApplication/read"
	"github.com/devtron-labs/devtron/pkg/auth/authorisation/casbin"
	"github.com/devtron-labs/devtron/pkg/auth/user"
	bean4 "github.com/devtron-labs/devtron/pkg/cluster/environment/bean"
	clientErrors "github.com/devtron-labs/devtron/pkg/errors"
	"github.com/devtron-labs/devtron/pkg/fluxApplication"
	"github.com/devtron-labs/devtron/pkg/k8s"
	application2 "github.com/devtron-labs/devtron/pkg/k8s/application"
	bean2 "github.com/devtron-labs/devtron/pkg/k8s/application/bean"
	bean3 "github.com/devtron-labs/devtron/pkg/k8s/bean"
	"github.com/devtron-labs/devtron/pkg/terminal"
	"github.com/devtron-labs/devtron/util"
	"github.com/devtron-labs/devtron/util/rbac"
	"github.com/google/uuid"
	"github.com/gorilla/mux"
	errors2 "github.com/juju/errors"
	"go.uber.org/zap"
	"io"
	k8sError "k8s.io/apimachinery/pkg/api/errors"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"regexp"
	"time"
)

type K8sApplicationRestHandler interface {
	GetResource(w http.ResponseWriter, r *http.Request)
	CreateResource(w http.ResponseWriter, r *http.Request)
	UpdateResource(w http.ResponseWriter, r *http.Request)
	DeleteResource(w http.ResponseWriter, r *http.Request)
	ListEvents(w http.ResponseWriter, r *http.Request)
	GetPodLogs(w http.ResponseWriter, r *http.Request)
	DownloadPodLogs(w http.ResponseWriter, r *http.Request)
	GetTerminalSession(w http.ResponseWriter, r *http.Request)
	GetResourceInfo(w http.ResponseWriter, r *http.Request)
	GetHostUrlsByBatch(w http.ResponseWriter, r *http.Request)
	GetAllApiResources(w http.ResponseWriter, r *http.Request)
	GetResourceList(w http.ResponseWriter, r *http.Request)
	GetRecommendationList(w http.ResponseWriter, r *http.Request)
	RecommendationDetails(w http.ResponseWriter, r *http.Request)
	SyncRecommendations(w http.ResponseWriter, r *http.Request)
	GetRecommendedResource(w http.ResponseWriter, r *http.Request)
	ApplyResources(w http.ResponseWriter, r *http.Request)
	RotatePod(w http.ResponseWriter, r *http.Request)
	CreateEphemeralContainer(w http.ResponseWriter, r *http.Request)
	DeleteEphemeralContainer(w http.ResponseWriter, r *http.Request)
	GetAllApiResourceGVKWithoutAuthorization(w http.ResponseWriter, r *http.Request)
	GetResourceSecurityInfo(w http.ResponseWriter, r *http.Request)
	DebugPodInfo(w http.ResponseWriter, r *http.Request)
	PortForwarding(w http.ResponseWriter, r *http.Request)
	HandleK8sProxyRequest(w http.ResponseWriter, r *http.Request)
	DownloadPodContent(w http.ResponseWriter, r *http.Request)
}

type K8sApplicationRestHandlerImpl struct {
	logger                     *zap.SugaredLogger
	k8sApplicationService      application2.K8sApplicationService
	pump                       connector.Pump
	terminalSessionHandler     terminal.TerminalSessionHandler
	enforcer                   casbin.Enforcer
	validator                  *validator.Validate
	enforcerUtil               rbac.EnforcerUtil
	enforcerUtilHelm           rbac.EnforcerUtilHelm
	helmAppService             client.HelmAppService
	userService                user.UserService
	k8sCommonService           k8s.K8sCommonService
	terminalEnvVariables       *util.TerminalEnvVariables
	globalEnvVariables         *util.GlobalEnvVariables
	fluxAppService             fluxApplication.FluxApplicationService
	argoApplicationReadService read.ArgoApplicationReadService
	clusterReadService         clusterRead.ClusterReadService
	clusterRbacService         rbac2.ClusterRbacService
}

func NewK8sApplicationRestHandlerImpl(
	logger *zap.SugaredLogger,
	k8sApplicationService application2.K8sApplicationService,
	pump connector.Pump,
	terminalSessionHandler terminal.TerminalSessionHandler,
	enforcer casbin.Enforcer,
	enforcerUtilHelm rbac.EnforcerUtilHelm,
	enforcerUtil rbac.EnforcerUtil,
	helmAppService client.HelmAppService,
	userService user.UserService,
	k8sCommonService k8s.K8sCommonService,
	validator *validator.Validate,
	envVariables *util.EnvironmentVariables,
	fluxAppService fluxApplication.FluxApplicationService,
	argoApplicationReadService read.ArgoApplicationReadService,
	clusterReadService clusterRead.ClusterReadService,
	clusterRbacService rbac2.ClusterRbacService,
) *K8sApplicationRestHandlerImpl {
	return &K8sApplicationRestHandlerImpl{
		logger:                     logger,
		k8sApplicationService:      k8sApplicationService,
		pump:                       pump,
		terminalSessionHandler:     terminalSessionHandler,
		enforcer:                   enforcer,
		validator:                  validator,
		enforcerUtilHelm:           enforcerUtilHelm,
		enforcerUtil:               enforcerUtil,
		helmAppService:             helmAppService,
		userService:                userService,
		k8sCommonService:           k8sCommonService,
		terminalEnvVariables:       envVariables.TerminalEnvVariables,
		globalEnvVariables:         envVariables.GlobalEnvVariables,
		fluxAppService:             fluxAppService,
		argoApplicationReadService: argoApplicationReadService,
		clusterReadService:         clusterReadService,
		clusterRbacService:         clusterRbacService,
	}
}

func (handler *K8sApplicationRestHandlerImpl) RotatePod(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	appIdString := vars["appId"]
	if appIdString == "" {
		common.WriteJsonResp(w, fmt.Errorf("empty appid in request"), nil, http.StatusBadRequest)
		return
	}
	decoder := json.NewDecoder(r.Body)
	podRotateRequest := &bean3.RotatePodRequest{}
	err := decoder.Decode(podRotateRequest)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	appIdentifier, err := handler.helmAppService.DecodeAppId(appIdString)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	// RBAC enforcer applying
	rbacObject, rbacObject2 := handler.enforcerUtilHelm.GetHelmObjectByClusterIdNamespaceAndAppName(appIdentifier.ClusterId, appIdentifier.Namespace, appIdentifier.ReleaseName)
	token := r.Header.Get("token")
	ok := handler.enforcer.Enforce(token, casbin.ResourceHelmApp, casbin.ActionUpdate, rbacObject) || handler.enforcer.Enforce(token, casbin.ResourceHelmApp, casbin.ActionUpdate, rbacObject2)
	if !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	// RBAC enforcer Ends
	handler.logger.Infow("rotate pod request", "payload", podRotateRequest)
	rotatePodRequest := &bean3.RotatePodRequest{
		ClusterId: appIdentifier.ClusterId,
		Resources: podRotateRequest.Resources,
	}
	response, err := handler.k8sCommonService.RotatePods(r.Context(), rotatePodRequest)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, response, http.StatusOK)
}

// validate and enrich
// todo seprate concerns of both
func (handler *K8sApplicationRestHandlerImpl) validateGetResourceRequest(w http.ResponseWriter, r *http.Request) (*bean3.ResourceRequestBean, error) {
	decoder := json.NewDecoder(r.Body)
	var request bean3.ResourceRequestBean
	err := decoder.Decode(&request)
	if err != nil {
		handler.logger.Errorw("error in decoding request body", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return &request, err
	}
	rbacObject := ""
	rbacObject2 := ""
	envObject := ""
	token := r.Header.Get("token")
	if request.AppId != "" && request.AppType == bean2.HelmAppType {
		appIdentifier, err := handler.helmAppService.DecodeAppId(request.AppId)
		if err != nil {
			handler.logger.Errorw("error in decoding appId", "err", err, "appId", request.AppId)
			common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
			return &request, err
		}
		// setting appIdentifier value in request
		request.AppIdentifier = appIdentifier
		request.ClusterId = request.AppIdentifier.ClusterId
		if request.DeploymentType == bean2.HelmInstalledType {
			if err := handler.k8sApplicationService.ValidateResourceRequest(r.Context(), request.AppIdentifier, request.K8sRequest); err != nil {
				common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
				return &request, err
			}
		} else if request.DeploymentType == bean2.ArgoInstalledType {
			// TODO Implement ResourceRequest Validation for ArgoCD Installed APPs From ResourceTree
		}
		// RBAC enforcer applying for Helm App
		rbacObject, rbacObject2 = handler.enforcerUtilHelm.GetHelmObjectByClusterIdNamespaceAndAppName(request.AppIdentifier.ClusterId, request.AppIdentifier.Namespace, request.AppIdentifier.ReleaseName)
		ok := handler.enforcer.Enforce(token, casbin.ResourceHelmApp, casbin.ActionGet, rbacObject) || handler.enforcer.Enforce(token, casbin.ResourceHelmApp, casbin.ActionGet, rbacObject2)
		if !ok {
			common.WriteJsonResp(w, errors2.New("unauthorized"), nil, http.StatusForbidden)
			return &request, err
		}
		// RBAC enforcer Ends
	} else if request.AppId != "" && request.AppType == bean2.DevtronAppType {
		devtronAppIdentifier, err := handler.k8sApplicationService.DecodeDevtronAppId(request.AppId)
		if err != nil {
			handler.logger.Errorw("error in decoding appId", "err", err, "appId", request.AppId)
			common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
			return &request, err
		}
		// setting devtronAppIdentifier value in request
		request.DevtronAppIdentifier = devtronAppIdentifier
		request.ClusterId = request.DevtronAppIdentifier.ClusterId
		if request.DeploymentType == bean2.HelmInstalledType {
			// TODO Implement ResourceRequest Validation for Helm Installed Devtron APPs
		} else if request.DeploymentType == bean2.ArgoInstalledType {
			// TODO Implement ResourceRequest Validation for ArgoCD Installed APPs From ResourceTree
		}
		// RBAC enforcer applying for Devtron App
		envObject = handler.enforcerUtil.GetEnvRBACNameByAppId(request.DevtronAppIdentifier.AppId, request.DevtronAppIdentifier.EnvId)
		hasReadAccessForEnv := handler.enforcer.Enforce(token, casbin.ResourceEnvironment, casbin.ActionGet, envObject)
		if !hasReadAccessForEnv {
			common.WriteJsonResp(w, errors2.New("unauthorized"), nil, http.StatusForbidden)
			return &request, err
		}
		// RBAC enforcer Ends
	} else if request.AppId != "" && request.AppType == bean2.FluxAppType {
		// For flux app resource
		appIdentifier, err := helper2.DecodeFluxExternalAppId(request.AppId)
		if err != nil {
			handler.logger.Errorw(bean2.AppIdDecodingError, "err", err, "appId", request.AppId)
			common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
			return &request, err
		}
		//setting fluxAppIdentifier value in request
		request.ExternalFluxAppIdentifier = appIdentifier
		request.ClusterId = appIdentifier.ClusterId
		valid, err := handler.k8sApplicationService.ValidateFluxResourceRequest(r.Context(), request.ExternalFluxAppIdentifier, request.K8sRequest)
		if err != nil || !valid {
			handler.logger.Errorw("error in validating resource request", "err", err)
			return &request, err
		}
		//RBAC enforcer starts here
		if ok := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionGet, "*"); !ok {
			return &request, err
		}
		//RBAC enforcer ends here
	} else if request.AppId != "" && request.AppType == bean2.ArgoAppType {
		// For flux app resource
		appIdentifier, err := helper.DecodeExternalArgoAppId(request.AppId)
		if err != nil {
			handler.logger.Errorw(bean2.AppIdDecodingError, "err", err, "appId", request.AppId)
			common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
			return &request, err
		}

		//setting fluxAppIdentifier value in request
		request.ExternalArgoApplicationName = appIdentifier.AppName
		request.ClusterId = appIdentifier.ClusterId
		request.ExternalArgoAppIdentifier = appIdentifier
		valid, err := handler.argoApplicationReadService.ValidateArgoResourceRequest(r.Context(), appIdentifier, request.K8sRequest)
		if err != nil || !valid {
			handler.logger.Errorw("error in validating resource request", "err", err)
			return &request, err
		}

		//RBAC enforcer starts here
		if ok := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionGet, "*"); !ok {
			return &request, err
		}
		//RBAC enforcer ends here
	}
	// Invalid cluster id
	if request.ClusterId <= 0 {
		err = errors.New("can not resource manifest as target cluster is not provided")
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return &request, err
	}
	return &request, nil
}

func (handler *K8sApplicationRestHandlerImpl) GetResourceSecurityInfo(w http.ResponseWriter, r *http.Request) {
	request, err := handler.validateGetResourceRequest(w, r)
	if err != nil {
		return
	}
	info, err := handler.k8sApplicationService.GetResourceSecurityInfo(r.Context(), request)
	if err != nil {
		handler.logger.Errorw("error in getting security details for resource", "err", err)
		common.WriteJsonResp(w, err, "", http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, info, http.StatusOK)
}

func (handler *K8sApplicationRestHandlerImpl) GetResource(w http.ResponseWriter, r *http.Request) {
	decoder := json.NewDecoder(r.Body)
	var request bean3.ResourceRequestBean
	err := decoder.Decode(&request)
	if err != nil {
		handler.logger.Errorw("error in decoding request body", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	token := r.Header.Get("token")

	//rbac validation for the apps requests
	if request.AppId != "" {
		ok, err := handler.k8sApplicationService.VerifyRbacForAppRequests(token, &request, r, casbin.ActionGet)
		if err != nil {
			common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
			return
		} else if !ok {
			common.WriteJsonResp(w, errors2.New("unauthorized"), nil, http.StatusForbidden)
			return
		}
	}
	// Invalid cluster id
	if request.ClusterId <= 0 {
		common.WriteJsonResp(w, errors.New("can not resource manifest as target cluster is not provided"), nil, http.StatusBadRequest)
		return
	}
	// Fetching requested resource
	resource, err := handler.k8sCommonService.GetResource(r.Context(), &request)
	if err != nil {
		handler.logger.Errorw("error in getting resource", "err", err)
		common.WriteJsonResp(w, err, resource, http.StatusInternalServerError)
		return
	}
	if resource != nil && resource.ManifestResponse != nil {
		err = resource.ManifestResponse.SetRunningEphemeralContainers()
		if err != nil {
			handler.logger.Errorw("error in setting running ephemeral containers and setting them in resource response", "err", err)
			common.WriteJsonResp(w, err, resource, http.StatusInternalServerError)
			return
		}
	}

	canUpdate := false
	// Obfuscate secret if user does not have edit access
	if request.AppIdentifier == nil && request.DevtronAppIdentifier == nil && request.AppType != bean2.ArgoAppType && request.ClusterId > 0 { // if the appType is not argoAppType,then verify logic w.r.t resource browser, when rbac for argoApp is introduced, handle rbac accordingly
		// Verify update access for Resource Browser
		canUpdate = handler.k8sApplicationService.ValidateClusterResourceBean(r.Context(), request.ClusterId, resource.ManifestResponse.Manifest, request.K8sRequest.ResourceIdentifier.GroupVersionKind, handler.k8sApplicationService.GetRbacCallbackForResource(token, casbin.ActionUpdate))
		if !canUpdate {
			// Verify read access for Resource Browser
			readAllowed := handler.k8sApplicationService.ValidateClusterResourceBean(r.Context(), request.ClusterId, resource.ManifestResponse.Manifest, request.K8sRequest.ResourceIdentifier.GroupVersionKind, handler.k8sApplicationService.GetRbacCallbackForResource(token, casbin.ActionGet))
			if !readAllowed {
				common.WriteJsonResp(w, errors2.New("unauthorized"), nil, http.StatusForbidden)
				return
			}
		}
	}
	if !canUpdate && resource != nil {
		// Hide secret for read only access
		modifiedManifest, err := k8sObjectsUtil.HideValuesIfSecret(&resource.ManifestResponse.Manifest)
		if err != nil {
			handler.logger.Errorw("error in hiding secret values", "err", err)
			common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
			return
		}
		resource.ManifestResponse.Manifest = *modifiedManifest
	}
	// setting flag for secret view access only for resource browser
	resource.SecretViewAccess = canUpdate
	common.WriteJsonResp(w, nil, resource, http.StatusOK)
}

func (handler *K8sApplicationRestHandlerImpl) GetHostUrlsByBatch(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	appIdString := vars["appId"]
	if appIdString == "" {
		common.WriteJsonResp(w, fmt.Errorf("empty appid in request"), nil, http.StatusBadRequest)
		return
	}
	appTypeString := vars["appType"]
	if appTypeString == "" {
		common.WriteJsonResp(w, fmt.Errorf("empty appType in request"), nil, http.StatusBadRequest)
		return
	}
	appType, err := strconv.Atoi(appTypeString)
	if err != nil {
		common.WriteJsonResp(w, fmt.Errorf("invalid appType in request"), nil, http.StatusBadRequest)
		return
	}

	token := r.Header.Get("token")
	ctx := r.Context()
	ctx = context.WithValue(ctx, "token", token)
	var k8sAppDetail AppView.AppDetailContainer
	var resourceTreeResponse *gRPC.ResourceTreeResponse
	var clusterId int
	var namespace string
	var resourceTreeInf map[string]interface{}
	var externalArgoApplicationName string

	if appType == bean2.HelmAppType {
		appIdentifier, err := handler.helmAppService.DecodeAppId(appIdString)
		if err != nil {
			common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
			return
		}
		// RBAC enforcer applying
		rbacObject, rbacObject2 := handler.enforcerUtilHelm.GetHelmObjectByClusterIdNamespaceAndAppName(appIdentifier.ClusterId, appIdentifier.Namespace, appIdentifier.ReleaseName)

		ok := handler.enforcer.Enforce(token, casbin.ResourceHelmApp, casbin.ActionGet, rbacObject) || handler.enforcer.Enforce(token, casbin.ResourceHelmApp, casbin.ActionGet, rbacObject2)

		if !ok {
			common.WriteJsonResp(w, fmt.Errorf("unauthorized"), nil, http.StatusForbidden)
			return
		}
		//RBAC enforcer Ends
		appDetail, err := handler.helmAppService.GetApplicationDetail(ctx, appIdentifier)
		if err != nil {
			apiError := clientErrors.ConvertToApiError(err)
			if apiError != nil {
				err = apiError
			}
			common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
			return
		}

		clusterId = appIdentifier.ClusterId
		namespace = appIdentifier.Namespace
		resourceTreeResponse = appDetail.ResourceTreeResponse

	} else if appType == bean2.ArgoAppType {
		appIdentifier, err := helper.DecodeExternalArgoAppId(appIdString)
		if err != nil {
			common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
			return
		}
		// RBAC enforcer applying
		if ok := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionGet, "*"); !ok {
			common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
			return
		}
		//RBAC enforcer Ends

		appDetail, err := handler.argoApplicationReadService.GetAppDetailEA(ctx, appIdentifier.AppName, appIdentifier.Namespace, appIdentifier.ClusterId)
		if err != nil {
			apiError := clientErrors.ConvertToApiError(err)
			if apiError != nil {
				err = apiError
			}
			common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
			return
		}
		clusterId = appIdentifier.ClusterId
		namespace = appIdentifier.Namespace
		resourceTreeResponse = appDetail.ResourceTree
		externalArgoApplicationName = appIdentifier.AppName

	} else if appType == bean2.FluxAppType {
		appIdentifier, err := helper2.DecodeFluxExternalAppId(appIdString)
		if err != nil {
			common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
			return
		}
		// RBAC enforcer applying
		if ok := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionGet, "*"); !ok {
			common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
			return
		}
		//RBAC enforcer Ends

		appDetail, err := handler.fluxAppService.GetFluxAppDetail(r.Context(), appIdentifier)
		if err != nil {
			apiError := clientErrors.ConvertToApiError(err)
			if apiError != nil {
				err = apiError
			}
			common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
			return
		}
		clusterId = appIdentifier.ClusterId
		namespace = appIdentifier.Namespace
		resourceTreeResponse = appDetail.ResourceTreeResponse
	}
	k8sAppDetail = AppView.AppDetailContainer{
		DeploymentDetailContainer: AppView.DeploymentDetailContainer{
			ClusterId: clusterId,
			Namespace: namespace,
		},
	}

	bytes, _ := json.Marshal(resourceTreeResponse)
	err = json.Unmarshal(bytes, &resourceTreeInf)
	if err != nil {
		common.WriteJsonResp(w, fmt.Errorf("unmarshal error of resource tree response"), nil, http.StatusInternalServerError)
		return
	}

	validRequests := handler.k8sCommonService.FilterK8sResources(r.Context(), resourceTreeInf, k8sAppDetail, appIdString, []string{k8sCommonBean.ServiceKind, k8sCommonBean.IngressKind}, externalArgoApplicationName)
	if len(validRequests) == 0 {
		handler.logger.Error("neither service nor ingress found for this app", "appId", appIdString)
		common.WriteJsonResp(w, err, nil, http.StatusNoContent)
		return
	}

	resp, err := handler.k8sCommonService.GetManifestsByBatch(r.Context(), validRequests)
	if err != nil {
		handler.logger.Errorw("error in getting manifests in batch", "err", err, "clusterId", k8sAppDetail.ClusterId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	result := handler.k8sApplicationService.GetUrlsByBatchForIngress(r.Context(), resp)
	common.WriteJsonResp(w, nil, result, http.StatusOK)
}

func (handler *K8sApplicationRestHandlerImpl) CreateResource(w http.ResponseWriter, r *http.Request) {
	decoder := json.NewDecoder(r.Body)
	var request bean3.ResourceRequestBean
	err := decoder.Decode(&request)
	if err != nil {
		handler.logger.Errorw("error in decoding request body", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	appIdentifier, err := handler.helmAppService.DecodeAppId(request.AppId)
	if err != nil {
		handler.logger.Errorw("error in decoding appId", "err", err, "appId", request.AppId)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	// setting appIdentifier value in request
	request.AppIdentifier = appIdentifier
	// RBAC enforcer applying
	rbacObject, rbacObject2 := handler.enforcerUtilHelm.GetHelmObjectByClusterIdNamespaceAndAppName(request.AppIdentifier.ClusterId, request.AppIdentifier.Namespace, request.AppIdentifier.ReleaseName)
	token := r.Header.Get("token")
	ok := handler.enforcer.Enforce(token, casbin.ResourceHelmApp, casbin.ActionUpdate, rbacObject) || handler.enforcer.Enforce(token, casbin.ResourceHelmApp, casbin.ActionUpdate, rbacObject2)
	if !ok {
		common.WriteJsonResp(w, errors2.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	// RBAC enforcer Ends
	resource, err := handler.k8sApplicationService.RecreateResource(r.Context(), &request)
	if err != nil {
		handler.logger.Errorw("error in creating resource", "err", err)
		common.WriteJsonResp(w, err, resource, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, resource, http.StatusOK)
}

func (handler *K8sApplicationRestHandlerImpl) UpdateResource(w http.ResponseWriter, r *http.Request) {
	decoder := json.NewDecoder(r.Body)
	var request bean3.ResourceRequestBean
	token := r.Header.Get("token")
	err := decoder.Decode(&request)
	if err != nil {
		handler.logger.Errorw("error in decoding request body", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	//rbac validation for the apps requests
	if request.AppId != "" {
		ok, err := handler.k8sApplicationService.VerifyRbacForAppRequests(token, &request, r, casbin.ActionUpdate)
		if err != nil {
			common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
			return
		} else if !ok {
			common.WriteJsonResp(w, errors2.New("unauthorized"), nil, http.StatusForbidden)
			return
		}
	} else if request.ClusterId > 0 {
		// RBAC enforcer applying for Resource Browser
		if ok := handler.handleRbac(r, w, request, token, casbin.ActionUpdate); !ok {
			return
		}
		// RBAC enforcer Ends
	} else {
		common.WriteJsonResp(w, errors.New("can not update resource as target cluster is not provided"), nil, http.StatusBadRequest)
		return
	}

	resource, err := handler.k8sCommonService.UpdateResource(r.Context(), &request)
	if err != nil {
		handler.logger.Errorw("error in updating resource", "err", err)
		common.WriteJsonResp(w, err, resource, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, resource, http.StatusOK)
}

func (handler *K8sApplicationRestHandlerImpl) handleRbac(r *http.Request, w http.ResponseWriter, request bean3.ResourceRequestBean, token string, casbinAction string) bool {
	// assume direct update in cluster
	allowed, err := handler.k8sApplicationService.ValidateClusterResourceRequest(r.Context(), &request, handler.k8sApplicationService.GetRbacCallbackForResource(token, casbinAction))
	if err != nil {
		common.WriteJsonResp(w, errors.New("invalid request"), nil, http.StatusBadRequest)
		return false
	}
	if !allowed {
		common.WriteJsonResp(w, errors2.New("unauthorized"), nil, http.StatusForbidden)
	}
	return allowed
}

func (handler *K8sApplicationRestHandlerImpl) DeleteResource(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	var request bean3.ResourceRequestBean
	err = json.NewDecoder(r.Body).Decode(&request)
	if err != nil {
		handler.logger.Errorw("error in decoding request body", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	token := r.Header.Get("token")
	vars := r.URL.Query()
	request.ExternalArgoApplicationName = vars.Get("externalArgoApplicationName")

	//rbac handle for the apps requests
	if request.AppId != "" {
		ok, err := handler.k8sApplicationService.VerifyRbacForAppRequests(token, &request, r, casbin.ActionDelete)
		if err != nil {
			handler.logger.Errorw("error in decoding appId", "err", err, "appId", request.AppId)
			common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
			return
		} else if !ok {
			common.WriteJsonResp(w, errors2.New("unauthorized"), nil, http.StatusForbidden)
			return
		}
	} else if request.ClusterId > 0 {
		// RBAC enforcer applying for resource Browser
		if ok := handler.handleRbac(r, w, request, token, casbin.ActionDelete); !ok {
			return
		}
		// RBAC enforcer Ends
	} else {
		common.WriteJsonResp(w, errors.New("can not delete resource as target cluster is not provided"), nil, http.StatusBadRequest)
		return
	}

	resource, err := handler.k8sApplicationService.DeleteResourceWithAudit(r.Context(), &request, userId)
	if err != nil {
		errCode := http.StatusInternalServerError
		if apiErr, ok := err.(*utils.ApiError); ok {
			errCode = apiErr.HttpStatusCode
			switch errCode {
			case http.StatusNotFound:
				errorMessage := bean3.ResourceNotFoundErr
				err = fmt.Errorf("%s: %w", errorMessage, err)
			}
		}
		handler.logger.Errorw("error in deleting resource", "err", err)
		common.WriteJsonResp(w, err, resource, errCode)
		return
	}
	common.WriteJsonResp(w, nil, resource, http.StatusOK)
}

func (handler *K8sApplicationRestHandlerImpl) ListEvents(w http.ResponseWriter, r *http.Request) {
	decoder := json.NewDecoder(r.Body)
	token := r.Header.Get("token")
	var request bean3.ResourceRequestBean
	err := decoder.Decode(&request)
	if err != nil {
		handler.logger.Errorw("error in decoding request body", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	//rbac validation for the apps requests
	if request.AppId != "" {
		ok, err := handler.k8sApplicationService.VerifyRbacForAppRequests(token, &request, r, casbin.ActionGet)
		if err != nil {
			handler.logger.Errorw("error in decoding appId", "err", err, "appId", request.AppId)
			common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
			return
		} else if !ok {
			common.WriteJsonResp(w, errors2.New("unauthorized user"), nil, http.StatusForbidden)
			return
		}
	} else if request.ClusterId > 0 {
		// RBAC enforcer applying for resource Browser
		if ok := handler.handleRbac(r, w, request, token, casbin.ActionGet); !ok {
			return
		}
		// RBAC enforcer Ends
	} else {
		common.WriteJsonResp(w, errors.New("can not get resource as target cluster is not provided"), nil, http.StatusBadRequest)
		return
	}
	events, err := handler.k8sCommonService.ListEvents(r.Context(), &request)
	if err != nil {
		handler.logger.Errorw("error in getting events list", "err", err)
		common.WriteJsonResp(w, err, events, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, events, http.StatusOK)
}

func (handler *K8sApplicationRestHandlerImpl) GetPodLogs(w http.ResponseWriter, r *http.Request) {
	token := r.Header.Get("token")
	request, err := handler.k8sApplicationService.ValidatePodLogsRequestQuery(r)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	handler.logger.Infow("get pod logs request", "request", request)
	handler.requestValidationAndRBAC(w, r, token, request)
	lastEventId := r.Header.Get(bean2.LastEventID)
	isReconnect := false
	if len(lastEventId) > 0 {
		lastSeenMsgId, err := strconv.ParseInt(lastEventId, bean2.IntegerBase, bean2.IntegerBitSize)
		if err != nil {
			common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
			return
		}
		lastSeenMsgId = lastSeenMsgId + bean2.TimestampOffsetToAvoidDuplicateLogs // increased by one ns to avoid duplicate
		t := v1.Unix(0, lastSeenMsgId)
		request.K8sRequest.PodLogsRequest.SinceTime = &t
		isReconnect = true
	}
	stream, err := handler.k8sApplicationService.GetPodLogs(r.Context(), request)
	// err is handled inside StartK8sStreamWithHeartBeat method
	ctx, cancel := context.WithCancel(r.Context())
	if cn, ok := w.(http.CloseNotifier); ok {
		go func(done <-chan struct{}, closed <-chan bool) {
			select {
			case <-done:
			case <-closed:
				cancel()
			}
		}(ctx.Done(), cn.CloseNotify())
	}
	defer cancel()
	defer util.Close(stream, handler.logger)
	handler.pump.StartK8sStreamWithHeartBeat(w, isReconnect, stream, err)
}

func (handler *K8sApplicationRestHandlerImpl) DownloadPodLogs(w http.ResponseWriter, r *http.Request) {
	token := r.Header.Get("token")
	request, err := handler.k8sApplicationService.ValidatePodLogsRequestQuery(r)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	handler.requestValidationAndRBAC(w, r, token, request)

	// just to make sure follow flag is set to false when downloading logs
	request.K8sRequest.PodLogsRequest.Follow = false

	stream, err := handler.k8sApplicationService.GetPodLogs(r.Context(), request)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	ctx, cancel := context.WithCancel(r.Context())
	if cn, ok := w.(http.CloseNotifier); ok {
		go func(done <-chan struct{}, closed <-chan bool) {
			select {
			case <-done:
			case <-closed:
				cancel()
			}
		}(ctx.Done(), cn.CloseNotify())
	}
	defer cancel()
	defer util.Close(stream, handler.logger)

	var dataBuffer bytes.Buffer
	bufReader := bufio.NewReader(stream)
	eof := false
	for !eof {
		log, err := bufReader.ReadString('\n')
		log = strings.TrimSpace(log) // Remove trailing line ending
		a := regexp.MustCompile(" ")
		var res []byte
		splitLog := a.Split(log, 2)
		if len(splitLog[0]) > 0 {
			parsedTime, err := time.Parse(time.RFC3339, splitLog[0])
			if err != nil {
				common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
				return
			}
			gmtTimeLoc := time.FixedZone(bean2.LocalTimezoneInGMT, bean2.LocalTimeOffset)
			humanReadableTime := parsedTime.In(gmtTimeLoc).Format(time.RFC1123)
			res = append(res, humanReadableTime...)
		}

		if len(splitLog) == 2 {
			res = append(res, " "...)
			res = append(res, splitLog[1]...)
		}
		res = append(res, "\n"...)
		if err == io.EOF {
			eof = true
			// stop if we reached end of stream and the next line is empty
			if log == "" {
				break
			}
		} else if err != nil && err != io.EOF {
			common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
			return
		}
		_, err = dataBuffer.Write(res)
		if err != nil {
			common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
			return
		}
	}
	if len(dataBuffer.Bytes()) == 0 {
		common.WriteJsonResp(w, nil, nil, http.StatusNoContent)
		return
	}
	podLogsFilename := generatePodLogsFilename(request.K8sRequest.ResourceIdentifier.Name)
	common.WriteOctetStreamResp(w, r, dataBuffer.Bytes(), podLogsFilename)
	return
}

func generatePodLogsFilename(filename string) string {
	return fmt.Sprintf("podlogs-%s-%s.log", filename, uuid.New().String())
}

func (handler *K8sApplicationRestHandlerImpl) requestValidationAndRBAC(w http.ResponseWriter, r *http.Request, token string, request *bean3.ResourceRequestBean) bool {
	if request.AppType == bean2.HelmAppType && request.AppIdentifier != nil {
		if request.DeploymentType == bean2.HelmInstalledType {
			if err := handler.k8sApplicationService.ValidateResourceRequest(r.Context(), request.AppIdentifier, request.K8sRequest); err != nil {
				common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
				return false
			}
		} else if request.DeploymentType == bean2.ArgoInstalledType {
			// TODO Implement ResourceRequest Validation for ArgoCD Installed APPs From ResourceTree
		}
		// RBAC enforcer applying for Helm App
		rbacObject, rbacObject2 := handler.enforcerUtilHelm.GetHelmObjectByClusterIdNamespaceAndAppName(request.AppIdentifier.ClusterId, request.AppIdentifier.Namespace, request.AppIdentifier.ReleaseName)
		ok := handler.enforcer.Enforce(token, casbin.ResourceHelmApp, casbin.ActionGet, rbacObject) || handler.enforcer.Enforce(token, casbin.ResourceHelmApp, casbin.ActionGet, rbacObject2)

		if !ok {
			common.WriteJsonResp(w, errors2.New("unauthorized"), nil, http.StatusForbidden)
			return false
		}
		//RBAC enforcer Ends
	} else if request.AppType == bean2.DevtronAppType && request.DevtronAppIdentifier != nil {
		if request.DeploymentType == bean2.HelmInstalledType {
			//TODO Implement ResourceRequest Validation for Helm Installed Devtron APPs
		} else if request.DeploymentType == bean2.ArgoInstalledType {
			//TODO Implement ResourceRequest Validation for ArgoCD Installed APPs From ResourceTree
		}
		// RBAC enforcer applying For Devtron App
		envObject := handler.enforcerUtil.GetEnvRBACNameByAppId(request.DevtronAppIdentifier.AppId, request.DevtronAppIdentifier.EnvId)
		if !handler.enforcer.Enforce(token, casbin.ResourceEnvironment, casbin.ActionGet, envObject) {
			common.WriteJsonResp(w, errors2.New("unauthorized"), nil, http.StatusForbidden)
			return false
		}
		//RBAC enforcer Ends
	} else if request.AppType == bean2.FluxAppType && request.ExternalFluxAppIdentifier != nil {
		valid, err := handler.k8sApplicationService.ValidateFluxResourceRequest(r.Context(), request.ExternalFluxAppIdentifier, request.K8sRequest)
		if err != nil || !valid {
			handler.logger.Errorw("error in validating resource request", "err", err)
			common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
			return false
		}
		//RBAC enforcer starts here
		if ok := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionGet, "*"); !ok {
			common.WriteJsonResp(w, errors2.New("unauthorized"), nil, http.StatusForbidden)
			return false
		}
		//RBAC enforcer ends here
	} else if request.AppType == bean2.ArgoAppType && request.ExternalArgoApplicationName != "" {
		appIdentifier, err := helper.DecodeExternalArgoAppId(request.AppId)
		if err != nil {
			handler.logger.Errorw(bean2.AppIdDecodingError, "err", err, "appIdentifier", request.AppIdentifier)
			common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
			return false
		}
		valid, err := handler.argoApplicationReadService.ValidateArgoResourceRequest(r.Context(), appIdentifier, request.K8sRequest)
		if err != nil || !valid {
			handler.logger.Errorw("error in validating resource request", "err", err)
			common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
			return false
		}

		//RBAC enforcer starts here
		if ok := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionGet, "*"); !ok {
			common.WriteJsonResp(w, errors2.New("unauthorized"), nil, http.StatusForbidden)
			return false
		}
		//RBAC enforcer ends here
	} else if request.AppIdentifier == nil && request.DevtronAppIdentifier == nil && request.ClusterId > 0 && request.ExternalArgoApplicationName == "" {
		//RBAC enforcer applying For Resource Browser
		if !handler.handleRbac(r, w, *request, token, casbin.ActionGet) {
			return false
		}
		//RBAC enforcer Ends
	} else if request.ClusterId <= 0 {
		common.WriteJsonResp(w, errors.New("can not get pod logs as target cluster is not provided"), nil, http.StatusBadRequest)
		return false
	}
	return true
}

func (handler *K8sApplicationRestHandlerImpl) GetTerminalSession(w http.ResponseWriter, r *http.Request) {
	token := r.Header.Get("token")
	userId, err := handler.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	vars := r.URL.Query()
	appTypeStr := vars.Get("appType")
	appType, _ := strconv.Atoi(appTypeStr) // ignore error as this var is not expected for devtron apps/helm apps/resource bowser. appType var is needed in case of Argo Apps
	request, resourceRequestBean, err := handler.k8sApplicationService.ValidateTerminalRequestQuery(r)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	userEmail, _, err := handler.userService.GetEmailAndVersionFromToken(token)
	if err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	if !handler.verifyTerminalAccess(w, r, resourceRequestBean, token, appType) {
		return
	}
	request.UserId = userId
	request.UserEmail = userEmail
	status, message, err := handler.terminalSessionHandler.GetTerminalSession(request)
	common.WriteJsonResp(w, err, message, status)
}

func (handler *K8sApplicationRestHandlerImpl) verifyTerminalAccess(w http.ResponseWriter, r *http.Request, resourceRequestBean *bean3.ResourceRequestBean, token string, appType int) bool {

	if resourceRequestBean.AppIdentifier != nil && appType == bean2.HelmAppType {
		// RBAC enforcer applying For Helm App
		rbacObject, rbacObject2 := handler.enforcerUtilHelm.GetHelmObjectByClusterIdNamespaceAndAppName(resourceRequestBean.AppIdentifier.ClusterId, resourceRequestBean.AppIdentifier.Namespace, resourceRequestBean.AppIdentifier.ReleaseName)
		// Validating for custom exec role in build and deploy
		isAuthorised := false
		if ok := handler.enforcer.Enforce(token, casbin.ResourceTerminal, casbin.ActionExec, rbacObject) || handler.enforcer.Enforce(token, casbin.ResourceTerminal, casbin.ActionExec, rbacObject2); ok {
			isAuthorised = true
		} else if !handler.terminalEnvVariables.RestrictTerminalAccessToTerminalRole {
			isAuthorised = handler.enforcer.Enforce(token, casbin.ResourceHelmApp, "*", rbacObject) || handler.enforcer.Enforce(token, casbin.ResourceHelmApp, "*", rbacObject2)
		}
		if !isAuthorised {
			common.WriteJsonResp(w, errors2.New("unauthorized"), nil, http.StatusForbidden)
			return false
		}
		// RBAC enforcer Ends
	} else if resourceRequestBean.DevtronAppIdentifier != nil && appType == bean2.DevtronAppType {
		// RBAC enforcer applying For Devtron App
		teamEnvRbacObject := handler.enforcerUtil.GetTeamEnvRBACNameByAppId(resourceRequestBean.DevtronAppIdentifier.AppId, resourceRequestBean.DevtronAppIdentifier.EnvId)
		envObject := handler.enforcerUtil.GetEnvRBACNameByAppId(resourceRequestBean.DevtronAppIdentifier.AppId, resourceRequestBean.DevtronAppIdentifier.EnvId)
		appObject := handler.enforcerUtil.GetAppRBACNameByAppId(resourceRequestBean.DevtronAppIdentifier.AppId)
		if teamEnvRbacObject == "" && envObject == "" && appObject == "" {
			common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
			return false
		}
		isAuthorised := false
		// Validating for custom exec role in build and deploy OR edit access
		if handler.enforcer.Enforce(token, casbin.ResourceTerminal, casbin.ActionExec, teamEnvRbacObject) {
			isAuthorised = true
		} else if !handler.terminalEnvVariables.RestrictTerminalAccessToTerminalRole {
			isAuthorised = handler.enforcer.Enforce(token, casbin.ResourceEnvironment, casbin.ActionUpdate, envObject) && handler.enforcer.Enforce(token, casbin.ResourceApplications, casbin.ActionUpdate, appObject)
		}
		if !isAuthorised {
			common.WriteJsonResp(w, errors2.New("unauthorized"), nil, http.StatusForbidden)
			return false
		}
		// RBAC enforcer Ends
	} else if resourceRequestBean.ExternalFluxAppIdentifier != nil && appType == bean2.FluxAppType {
		// RBAC enforcer applying For external flux app
		if ok := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionUpdate, "*"); !ok {
			common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
			return false
		}
		//RBAC enforcer Ends

	} else if resourceRequestBean.ExternalArgoApplicationName != "" && appType == bean2.ArgoAppType {
		// RBAC enforcer applying For external Argo app
		if ok := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionUpdate, "*"); !ok {
			common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
			return false
		}
		//RBAC enforcer Ends

	} else if resourceRequestBean.AppIdentifier == nil && resourceRequestBean.DevtronAppIdentifier == nil && resourceRequestBean.ExternalFluxAppIdentifier == nil && resourceRequestBean.ExternalArgoApplicationName == "" && resourceRequestBean.ClusterId > 0 {
		//RBAC enforcer applying for Resource Browser
		resource, object := handler.enforcerUtil.GetRbacResourceAndObjectForNodeByClusterId(resourceRequestBean.ClusterId, bean2.ALL)
		if !(handler.enforcer.Enforce(token, resource, casbin.ActionUpdate, object) || handler.handleRbac(r, w, *resourceRequestBean, token, casbin.ActionUpdate)) {
			return false
		}
		// RBAC enforcer Ends
	} else if resourceRequestBean.ClusterId <= 0 {
		common.WriteJsonResp(w, errors.New("can not get terminal session as target cluster is not provided"), nil, http.StatusBadRequest)
		return false
	}
	return true
}

func (handler *K8sApplicationRestHandlerImpl) GetResourceInfo(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}

	// this is auth free api
	response, err := handler.k8sApplicationService.GetResourceInfo(r.Context())
	if err != nil {
		handler.logger.Errorw("error on resource info", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, response, http.StatusOK)
	return
}

// GetAllApiResourceGVKWithoutAuthorization  This function will the all the available api resource GVK list for specific cluster
func (handler *K8sApplicationRestHandlerImpl) GetAllApiResourceGVKWithoutAuthorization(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}

	// get clusterId from request
	vars := mux.Vars(r)
	clusterId, err := strconv.Atoi(vars["clusterId"])
	if err != nil {
		handler.logger.Errorw("request err in getting clusterId in GetAllApiResources", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	// get data from service
	response, err := handler.k8sApplicationService.GetAllApiResourceGVKWithoutAuthorization(r.Context(), clusterId)
	if err != nil {
		handler.logger.Errorw("error in getting api-resources", "clusterId", clusterId, "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	common.WriteJsonResp(w, nil, response, http.StatusOK)
}

func (handler *K8sApplicationRestHandlerImpl) GetAllApiResources(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}

	// get clusterId from request
	vars := mux.Vars(r)
	clusterId, err := strconv.Atoi(vars["clusterId"])
	if err != nil {
		handler.logger.Errorw("request err in getting clusterId in GetAllApiResources", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	isSuperAdmin := false
	token := r.Header.Get("token")
	if ok := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionGet, "*"); ok {
		isSuperAdmin = true
	}

	// get data from service
	response, err := handler.k8sApplicationService.GetAllApiResources(r.Context(), clusterId, isSuperAdmin, userId, token)
	if err != nil {
		handler.logger.Errorw("error in getting api-resources", "clusterId", clusterId, "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	// send unauthorised if response is empty
	if !isSuperAdmin && (response == nil || len(response.ApiResources) == 0) {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}

	common.WriteJsonResp(w, nil, response, http.StatusOK)
}

func (handler *K8sApplicationRestHandlerImpl) GetResourceList(w http.ResponseWriter, r *http.Request) {
	decoder := json.NewDecoder(r.Body)
	token := r.Header.Get("token")
	var request bean3.ResourceRequestBean
	err := decoder.Decode(&request)
	if err != nil {
		handler.logger.Errorw("error in decoding request body", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	isSuperAdmin := false
	if ok := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionGet, "*"); ok {
		isSuperAdmin = true
	}
	clusterRbacFunc := handler.verifyRbacForCluster
	if isSuperAdmin {
		clusterRbacFunc = func(token, clusterName string, request bean3.ResourceRequestBean, casbinAction string) bool {
			return true
		}
	}
	ctx, span := otel.Tracer("K8sApplicationRestHandler").Start(r.Context(), "GetResourceList")
	defer span.End()
	response, err := handler.k8sApplicationService.GetResourceList(ctx, token, &request, clusterRbacFunc)
	if err != nil {
		handler.logger.Errorw("error in getting resource list", "err", err)
		if statusErr, ok := err.(*k8sError.StatusError); ok && statusErr.Status().Code == 404 {
			err = &util2.ApiError{Code: "404", HttpStatusCode: 404, UserMessage: "no resource found", InternalMessage: err.Error()}
		}
		common.WriteJsonResp(w, err, response, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, response, http.StatusOK)
}

func (handler *K8sApplicationRestHandlerImpl) GetRecommendationList(w http.ResponseWriter, r *http.Request) {
	decoder := json.NewDecoder(r.Body)
	token := r.Header.Get("token")
	var request bean3.ResourceRequestBean
	err := decoder.Decode(&request)
	if err != nil {
		handler.logger.Errorw("error in decoding request body", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	isSuperAdmin := false
	if ok := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionGet, "*"); ok {
		isSuperAdmin = true
	}
	clusterRbacFunc := handler.verifyRbacForCluster
	if isSuperAdmin {
		clusterRbacFunc = func(token, clusterName string, request bean3.ResourceRequestBean, casbinAction string) bool {
			return true
		}
	}
	if !handler.globalEnvVariables.IsFeatureResourceRecommendationEnable() {
		common.WriteJsonResp(w, errors.New("resource recommendation feature is disabled"), nil, http.StatusBadRequest)
		return
	}
	response, err := handler.k8sApplicationService.GetRecommendationList(r.Context(), token, &request, clusterRbacFunc)
	if err != nil {
		handler.logger.Errorw("error in getting resource list", "err", err)
		if statusErr, ok := err.(*k8sError.StatusError); ok && statusErr.Status().Code == 404 {
			err = &util2.ApiError{Code: "404", HttpStatusCode: 404, UserMessage: "no resource found", InternalMessage: err.Error()}
		}
		common.WriteJsonResp(w, err, response, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, response, http.StatusOK)
}

func (handler *K8sApplicationRestHandlerImpl) RecommendationDetails(w http.ResponseWriter, r *http.Request) {
	// get clusterId from request
	vars := mux.Vars(r)
	clusterId, err := strconv.Atoi(vars["clusterId"])
	if err != nil {
		handler.logger.Errorw("request err in getting clusterId in RecommendationDetails", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	if !handler.globalEnvVariables.IsFeatureResourceRecommendationEnable() {
		common.WriteJsonResp(w, errors.New("resource recommendation feature is disabled"), nil, http.StatusBadRequest)
		return
	}
	response, err := handler.k8sApplicationService.RecommendationDetails(clusterId)
	if err != nil {
		handler.logger.Errorw("error in getting recommendation details", "clusterId", clusterId, "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, response, http.StatusOK)
}

func (handler *K8sApplicationRestHandlerImpl) SyncRecommendations(w http.ResponseWriter, r *http.Request) {
	token := r.Header.Get("token")
	decoder := json.NewDecoder(r.Body)
	var request bean3.SyncResourceRecommendation
	err := decoder.Decode(&request)
	if err != nil {
		handler.logger.Errorw("error in decoding request body", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	if err = handler.validator.Struct(request); err != nil {
		handler.logger.Errorw("invalid request payload", "err", err, "payload", request)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	handler.logger.Infow("request payload", "err", err, "payload", request)
	if !handler.globalEnvVariables.IsFeatureResourceRecommendationEnable() {
		common.WriteJsonResp(w, errors.New("resource recommendation feature is disabled"), nil, http.StatusBadRequest)
		return
	}
	// RBAC enforcer applying
	clusterBean, err := handler.clusterReadService.FindById(request.ClusterId)
	if err != nil && !errors.Is(err, pg.ErrNoRows) {
		handler.logger.Errorw("error in getting cluster by id", "err", err, "clusterId", request.ClusterId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	} else if errors.Is(err, pg.ErrNoRows) {
		handler.logger.Errorw("cluster not found", "err", err, "clusterId", request.ClusterId)
		common.WriteJsonResp(w, errors.New("cluster not found"), nil, http.StatusNotFound)
		return
	}
	// RBAC enforcer applying
	authenticated := handler.clusterRbacService.CheckAuthorisationForAllK8sPermissions(token, clusterBean.ClusterName, casbin.ActionUpdate)
	if !authenticated {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	// RBAC enforcer ends
	err = handler.k8sApplicationService.SyncRecommendations(&request)
	if err != nil {
		handler.logger.Errorw("error in syncing recommendations", "err", err, "request", request)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, "Recommendations synced successfully", http.StatusOK)
}

func (handler *K8sApplicationRestHandlerImpl) GetRecommendedResource(w http.ResponseWriter, r *http.Request) {
	decoder := json.NewDecoder(r.Body)
	var request bean3.ResourceRequestBean
	err := decoder.Decode(&request)
	if err != nil {
		handler.logger.Errorw("error in decoding request body", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	token := r.Header.Get("token")

	//rbac validation for the apps requests
	if request.AppId != "" {
		ok, err := handler.k8sApplicationService.VerifyRbacForAppRequests(token, &request, r, casbin.ActionGet)
		if err != nil {
			common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
			return
		} else if !ok {
			common.WriteJsonResp(w, errors2.New("unauthorized"), nil, http.StatusForbidden)
			return
		}
	}
	// Invalid cluster id
	if request.ClusterId <= 0 {
		common.WriteJsonResp(w, errors.New("can not resource manifest as target cluster is not provided"), nil, http.StatusBadRequest)
		return
	}
	if !handler.globalEnvVariables.IsFeatureResourceRecommendationEnable() {
		common.WriteJsonResp(w, errors.New("resource recommendation feature is disabled"), nil, http.StatusBadRequest)
		return
	}
	// Fetching requested resource
	resource, err := handler.k8sCommonService.GetResource(r.Context(), &request)
	if err != nil && !k8sError.IsNotFound(err) {
		handler.logger.Errorw("error in getting resource", "err", err)
		common.WriteJsonResp(w, err, resource, http.StatusInternalServerError)
		return
	} else if k8sError.IsNotFound(err) {
		handler.logger.Infow("resource not found", "err", err, "resource", request)
		common.WriteJsonResp(w, fmt.Errorf("resource not found"), nil, http.StatusNotFound)
		return
	}
	if resource != nil && resource.ManifestResponse != nil {
		err = resource.ManifestResponse.SetRunningEphemeralContainers()
		if err != nil {
			handler.logger.Errorw("error in setting running ephemeral containers and setting them in resource response", "err", err)
			common.WriteJsonResp(w, err, resource, http.StatusInternalServerError)
			return
		}
	}

	canUpdate := false
	// Obfuscate secret if user does not have edit access
	if request.AppIdentifier == nil && request.DevtronAppIdentifier == nil && request.AppType != bean2.ArgoAppType && request.ClusterId > 0 { // if the appType is not argoAppType,then verify logic w.r.t resource browser, when rbac for argoApp is introduced, handle rbac accordingly
		// Verify update access for Resource Browser
		canUpdate = handler.k8sApplicationService.ValidateClusterResourceBean(r.Context(), request.ClusterId, resource.ManifestResponse.Manifest, request.K8sRequest.ResourceIdentifier.GroupVersionKind, handler.k8sApplicationService.GetRbacCallbackForResource(token, casbin.ActionUpdate))
		if !canUpdate {
			// Verify read access for Resource Browser
			readAllowed := handler.k8sApplicationService.ValidateClusterResourceBean(r.Context(), request.ClusterId, resource.ManifestResponse.Manifest, request.K8sRequest.ResourceIdentifier.GroupVersionKind, handler.k8sApplicationService.GetRbacCallbackForResource(token, casbin.ActionGet))
			if !readAllowed {
				common.WriteJsonResp(w, errors2.New("unauthorized"), nil, http.StatusForbidden)
				return
			}
		}
	}
	if !canUpdate && resource != nil {
		// Hide secret for read only access
		modifiedManifest, err := k8sObjectsUtil.HideValuesIfSecret(&resource.ManifestResponse.Manifest)
		if err != nil {
			handler.logger.Errorw("error in hiding secret values", "err", err)
			common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
			return
		}
		resource.ManifestResponse.Manifest = *modifiedManifest
	}
	resource, err = handler.k8sApplicationService.GetRecommendedResource(request.ClusterId, resource)
	if err != nil {
		handler.logger.Errorw("error in getting recommended resource", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	if resource != nil {
		// setting flag for secret view access only for resource browser
		resource.SecretViewAccess = canUpdate
	}
	common.WriteJsonResp(w, nil, resource, http.StatusOK)
}

func (handler *K8sApplicationRestHandlerImpl) ApplyResources(w http.ResponseWriter, r *http.Request) {
	decoder := json.NewDecoder(r.Body)
	var request util4.ApplyResourcesRequest
	token := r.Header.Get("token")
	err := decoder.Decode(&request)
	if err != nil {
		handler.logger.Errorw("error in decoding request body", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	response, err := handler.k8sApplicationService.ApplyResources(r.Context(), token, &request, handler.verifyRbacForCluster)
	if err != nil {
		handler.logger.Errorw("error in applying resource", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, response, http.StatusOK)
}

func (handler *K8sApplicationRestHandlerImpl) verifyRbacForCluster(token string, clusterName string, request bean3.ResourceRequestBean, casbinAction string) bool {
	k8sRequest := request.K8sRequest
	return handler.k8sApplicationService.VerifyRbacForResource(token, clusterName, k8sRequest.ResourceIdentifier, casbinAction)
}

func (handler *K8sApplicationRestHandlerImpl) CreateEphemeralContainer(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	decoder := json.NewDecoder(r.Body)
	var request bean4.EphemeralContainerRequest
	err = decoder.Decode(&request)
	if err != nil {
		handler.logger.Errorw("error in decoding request body", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	if err = handler.validator.Struct(request); err != nil || (request.BasicData == nil && request.AdvancedData == nil) {
		if err != nil {
			err = errors.New("invalid request payload")
		}
		handler.logger.Errorw("invalid request payload", "err", err, "payload", request)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	// rbac applied in below function
	resourceRequestBean := handler.handleEphemeralRBAC(request.PodName, request.Namespace, w, r)
	if resourceRequestBean == nil {
		return
	}
	if resourceRequestBean.ClusterId != request.ClusterId {
		common.WriteJsonResp(w, errors.New("clusterId mismatch in param and request body"), nil, http.StatusBadRequest)
		return
	}
	request.UserId = userId
	request.ExternalArgoAppIdentifier = resourceRequestBean.ExternalArgoAppIdentifier

	err = handler.k8sApplicationService.CreatePodEphemeralContainers(&request)
	if err != nil {
		handler.logger.Errorw("error occurred in creating ephemeral container", "err", err, "requestPayload", request)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	common.WriteJsonResp(w, err, request.BasicData.ContainerName, http.StatusOK)
}

func (handler *K8sApplicationRestHandlerImpl) DeleteEphemeralContainer(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	decoder := json.NewDecoder(r.Body)
	var request bean4.EphemeralContainerRequest
	err = decoder.Decode(&request)
	if err != nil {
		handler.logger.Errorw("error in decoding request body", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	if err = handler.validator.Struct(request); err != nil || request.BasicData == nil {
		if err != nil {
			err = errors.New("invalid request payload")
		}
		handler.logger.Errorw("invalid request payload", "err", err, "payload", request)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	// rbac applied in below function
	resourceRequestBean := handler.handleEphemeralRBAC(request.PodName, request.Namespace, w, r)
	if resourceRequestBean == nil {
		return
	}
	if resourceRequestBean.ClusterId != request.ClusterId {
		common.WriteJsonResp(w, errors.New("clusterId mismatch in param and request body"), nil, http.StatusBadRequest)
		return
	}
	request.UserId = userId
	request.ExternalArgoAppIdentifier = resourceRequestBean.ExternalArgoAppIdentifier
	_, err = handler.k8sApplicationService.TerminatePodEphemeralContainer(request)
	if err != nil {
		handler.logger.Errorw("error occurred in terminating ephemeral container", "err", err, "requestPayload", request)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	common.WriteJsonResp(w, err, request.BasicData.ContainerName, http.StatusOK)

}

func (handler *K8sApplicationRestHandlerImpl) handleEphemeralRBAC(podName, namespace string, w http.ResponseWriter, r *http.Request) *bean3.ResourceRequestBean {
	token := r.Header.Get("token")
	_, resourceRequestBean, err := handler.k8sApplicationService.ValidateTerminalRequestQuery(r)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return resourceRequestBean
	}
	if resourceRequestBean.AppIdentifier != nil {
		// RBAC enforcer applying For Helm App
		rbacObject, rbacObject2 := handler.enforcerUtilHelm.GetHelmObjectByClusterIdNamespaceAndAppName(resourceRequestBean.AppIdentifier.ClusterId, resourceRequestBean.AppIdentifier.Namespace, resourceRequestBean.AppIdentifier.ReleaseName)
		isAuthorised := false
		if ok := handler.enforcer.Enforce(token, casbin.ResourceTerminal, casbin.ActionExec, rbacObject) || handler.enforcer.Enforce(token, casbin.ResourceTerminal, casbin.ActionExec, rbacObject2); ok {
			isAuthorised = true
		} else if !handler.terminalEnvVariables.RestrictTerminalAccessToTerminalRole {
			isAuthorised = handler.enforcer.Enforce(token, casbin.ResourceHelmApp, casbin.ActionUpdate, rbacObject) || handler.enforcer.Enforce(token, casbin.ResourceHelmApp, casbin.ActionUpdate, rbacObject2)
		}
		if !isAuthorised {
			common.WriteJsonResp(w, errors2.New("unauthorized"), nil, http.StatusForbidden)
			return resourceRequestBean
		}
		// RBAC enforcer Ends
	} else if resourceRequestBean.DevtronAppIdentifier != nil {
		// RBAC enforcer applying For Devtron App
		teamEnvRbacObject := handler.enforcerUtil.GetTeamEnvRBACNameByAppId(resourceRequestBean.DevtronAppIdentifier.AppId, resourceRequestBean.DevtronAppIdentifier.EnvId)
		envObject := handler.enforcerUtil.GetEnvRBACNameByAppId(resourceRequestBean.DevtronAppIdentifier.AppId, resourceRequestBean.DevtronAppIdentifier.EnvId)
		appObject := handler.enforcerUtil.GetAppRBACNameByAppId(resourceRequestBean.DevtronAppIdentifier.AppId)
		if teamEnvRbacObject == "" && envObject == "" && appObject == "" {
			common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
			return resourceRequestBean
		}
		isAuthorised := false
		// Validating for custom exec role in build and deploy OR edit access
		if handler.enforcer.Enforce(token, casbin.ResourceTerminal, casbin.ActionExec, teamEnvRbacObject) {
			isAuthorised = true
		} else if !handler.terminalEnvVariables.RestrictTerminalAccessToTerminalRole {
			isAuthorised = handler.enforcer.Enforce(token, casbin.ResourceEnvironment, casbin.ActionUpdate, envObject) && handler.enforcer.Enforce(token, casbin.ResourceApplications, casbin.ActionUpdate, appObject)
		}
		if !isAuthorised {
			common.WriteJsonResp(w, errors2.New("unauthorized"), nil, http.StatusForbidden)
			return resourceRequestBean
		}
		//RBAC enforcer Ends
	} else if resourceRequestBean.ExternalFluxAppIdentifier != nil {
		//RBAC enforcer starts here
		if ok := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionGet, "*"); !ok {
			common.WriteJsonResp(w, errors2.New("unauthorized"), nil, http.StatusForbidden)
			return resourceRequestBean
		}
		//RBAC enforcer ends here
	} else if resourceRequestBean.ExternalArgoApplicationName != "" {
		//RBAC enforcer starts here
		if ok := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionGet, "*"); !ok {
			common.WriteJsonResp(w, errors2.New("unauthorized"), nil, http.StatusForbidden)
			return resourceRequestBean
		}
		//RBAC enforcer ends here

	} else if resourceRequestBean.AppIdentifier == nil && resourceRequestBean.DevtronAppIdentifier == nil && resourceRequestBean.ExternalArgoApplicationName == "" && resourceRequestBean.ExternalFluxAppIdentifier == nil && resourceRequestBean.ClusterId > 0 {
		//RBAC enforcer applying for Resource Browser
		resourceRequestBean.K8sRequest.ResourceIdentifier.Name = podName
		resourceRequestBean.K8sRequest.ResourceIdentifier.Namespace = namespace
		if !handler.handleRbac(r, w, *resourceRequestBean, token, casbin.ActionUpdate) {
			return resourceRequestBean
		}
		// RBAC enforcer Ends
	} else if resourceRequestBean.ClusterId <= 0 {
		common.WriteJsonResp(w, errors.New("can not create/terminate ephemeral containers as target cluster is not provided"), nil, http.StatusBadRequest)
		return resourceRequestBean
	}
	return resourceRequestBean
}

func (handler *K8sApplicationRestHandlerImpl) DebugPodInfo(w http.ResponseWriter, r *http.Request) {
	token := r.Header.Get("token")
	vars := mux.Vars(r)
	clusterIdString := vars["clusterId"]
	if len(clusterIdString) == 0 {
		common.WriteJsonResp(w, errors.New("clusterid not present"), nil, http.StatusBadRequest)
		return
	}
	clusterId, err := strconv.Atoi(clusterIdString)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	queryValues := r.URL.Query()
	podName := queryValues.Get("name")
	namespace := queryValues.Get("namespace")
	appId := queryValues.Get("appId")
	if len(podName) == 0 {
		podName = "*"
	}
	if len(namespace) == 0 {
		namespace = "*"
	}
	allowed, err := handler.k8sApplicationService.ValidateK8sResourceAccess(token, clusterId, namespace, schema.GroupVersionKind{Version: "v1", Kind: "Pod"}, casbin.ActionGet, podName, handler.k8sApplicationService.VerifyRbacForResource)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusConflict)
		return
	}

	if !allowed && appId != "" {
		request, err := handler.k8sApplicationService.ValidatePodLogsRequestQuery(r)
		if err != nil {
			common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
			return
		}
		k8sRequest := request.K8sRequest
		if k8sRequest != nil {
			k8sRequest.ResourceIdentifier.GroupVersionKind = schema.GroupVersionKind{
				Group:   "",
				Kind:    "Pod",
				Version: "v1",
			}
		}
		allowed = handler.requestValidationAndRBAC(w, r, token, request)
		if !allowed {
			return
		}
		// not verifying pod request and matching it because in case the pod do not have the required labels or it was managed manually in that case it will throw unauthorized
		//if allowed {
		//	allowed, err = handler.handlePodRequestVerification(w, r, request, podName)
		//	if err != nil {
		//		return
		//	}
	}
	if !allowed {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	resp, err := handler.k8sApplicationService.GetPodInfo(r.Context(), clusterId, podName, namespace)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	// this is a special handling for dashboard backward compatibility as it expects 200 status code with error msg
	//.previously scoop was returning 200 status code with error msg when running as proxy
	// when we introduced scoop client, we have to send the error with common.WriteJsonResp which does not allow that
	if resp.Code == http.StatusNoContent {
		handler.logger.Errorw("no pod found", "podName", podName, "namespace", namespace, "err", resp.Errors)
		respObj := map[string]interface{}{
			"code":   http.StatusOK,
			"status": "OK",
			"errors": "unable to retrieve data",
		}

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(respObj)
		return
	}
	common.WriteJsonResp(w, nil, resp.Result, resp.Code)
}

func (handler *K8sApplicationRestHandlerImpl) PortForwarding(w http.ResponseWriter, r *http.Request) {
	token := r.Header.Get("token")
	queryValues := r.URL.Query()
	clusterIdString := queryValues.Get("clusterId")
	if len(clusterIdString) == 0 {
		common.WriteJsonResp(w, errors.New("clusterId not present"), nil, http.StatusBadRequest)
		return
	}
	clusterId, err := strconv.Atoi(clusterIdString)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	serviceName := queryValues.Get("serviceName")
	if len(serviceName) == 0 {
		common.WriteJsonResp(w, errors.New("serviceName not present"), nil, http.StatusBadRequest)
		return
	}
	servicePort := queryValues.Get("servicePort")
	if len(servicePort) == 0 {
		servicePort = "80"
	}

	namespace := queryValues.Get("namespace")
	if len(namespace) == 0 {
		common.WriteJsonResp(w, errors.New("namespace not present"), nil, http.StatusBadRequest)
		return
	}

	allowed, err := handler.k8sApplicationService.ValidateK8sResourceAccess(token, clusterId, namespace, schema.GroupVersionKind{Version: "v1", Kind: "Service"}, casbin.ActionUpdate, serviceName, handler.k8sApplicationService.VerifyRbacForResource)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusConflict)
		return
	}
	if !allowed {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}

	proxy, err := handler.k8sApplicationService.PortForwarding(r.Context(), clusterId, serviceName, namespace, servicePort)
	if err != nil {
		handler.logger.Errorw("Error in port forwarding: ", "Error: ", err)
		_ = json.NewEncoder(w).Encode(bean.Error{Code: 500, Message: "Error doing port forwarding."})
		return
	}
	r.URL.Path = strings.TrimPrefix(r.URL.Path, "/orchestrator/k8s/port-forward")
	proxy.ServeHTTP(w, r)
}

func (handler *K8sApplicationRestHandlerImpl) handleK8sProxyRequest(w http.ResponseWriter, r *http.Request) {
	token := getDevtronLoginTokenFromK8sProxyRequest(r)
	userEmail, _, authError := handler.userService.GetEmailAndGroupClaimsFromToken(token)
	if authError != nil {
		errorResponse := bean3.ErrorResponse{
			Kind:    "Status",
			Code:    400,
			Message: "Wrong or expired token.",
			Reason:  "Bad Request",
		}
		if token == "" {
			errorResponse.Message = "Authorization token not present. Are you using http instead of https?"
		}
		w.WriteHeader(http.StatusBadRequest)
		_ = json.NewEncoder(w).Encode(errorResponse)
		return
	}
	// Authorization header is deleted as it is sent by Kubectl and K8s understands it as Auth-Token for the Cluster/Node
	r.Header.Del(bean2.Authorization)

	vars := mux.Vars(r)
	clusterIdentifier := vars[bean2.ClusterIdentifier]
	envIdentifier := vars[bean2.EnvIdentifier]

	k8sProxyRequest := bean2.K8sProxyRequest{}

	if clusterIdentifier != bean2.Empty {
		clusterId, err := strconv.Atoi(clusterIdentifier)
		if err != nil {
			k8sProxyRequest.ClusterName = clusterIdentifier
		} else {
			k8sProxyRequest.ClusterId = clusterId
		}
		r.URL.Path = strings.TrimPrefix(r.URL.Path, fmt.Sprintf("%s/%s/%s", bean2.BaseForK8sProxy, bean2.Cluster, clusterIdentifier))
	} else {
		envId, err := strconv.Atoi(envIdentifier)
		if err != nil {
			k8sProxyRequest.EnvName = envIdentifier
		} else {
			k8sProxyRequest.EnvId = envId
		}
		r.URL.Path = strings.TrimPrefix(r.URL.Path, fmt.Sprintf("%s/%s/%s", bean2.BaseForK8sProxy, bean2.Env, envIdentifier))
	}

	clusterRequested, err := handler.k8sApplicationService.GetClusterForK8sProxy(&k8sProxyRequest)
	if err != nil {
		handler.logger.Errorw("Error in finding cluster", "Error:", err)

		errorResponse := bean3.ErrorResponse{
			Kind:    "Status",
			Code:    400,
			Message: "Cannot find requested env or cluster.",
			Reason:  "Bad Request",
		}
		w.WriteHeader(http.StatusBadRequest)
		_ = json.NewEncoder(w).Encode(errorResponse)
		return
	}

	namespace, gvk, resourceName := util3.ParseK8sProxyURL(r.URL.Path)
	resourceAction := casbin.ActionGet
	if r.Method != http.MethodGet {
		resourceAction = bean2.ALL
	}

	allowed := handler.k8sApplicationService.ValidateK8sResourceForCluster(token, resourceName, namespace, gvk, handler.k8sApplicationService.VerifyRbacForResource, clusterRequested.ClusterName, resourceAction)
	if !allowed && !util3.IsUrlWhiteListed(r.URL.Path) {
		role := bean2.RoleView
		if resourceAction == bean2.ALL {
			role = bean2.RoleAdmin
		}
		errorResponse := bean3.ErrorResponse{
			Kind:    "Status",
			Code:    403,
			Message: fmt.Sprintf("You need %s access on Cluster: %s, Namespace: %s, Group: %s, Version: %s, Kind: %s, Resource Name: %s. Here * represents all.", role, clusterRequested.ClusterName, namespace, gvk.Group, gvk.Version, gvk.Kind, resourceName),
			Reason:  "Forbidden",
		}

		w.WriteHeader(http.StatusForbidden)
		_ = json.NewEncoder(w).Encode(errorResponse)
		return
	}
	proxyServer, err := handler.k8sApplicationService.StartProxyServer(r.Context(), clusterRequested.Id)

	if err != nil {
		handler.logger.Errorw("Error in starting proxy server: ", "Error: ", err)
		errorResponse := bean3.ErrorResponse{
			Kind:    "Status",
			Code:    500,
			Message: "An error occurred. Please try again.",
			Reason:  "Internal Server Error",
		}
		w.WriteHeader(http.StatusForbidden)
		_ = json.NewEncoder(w).Encode(errorResponse)
		return
	}

	handler.logger.Infow("K8sProxyRequest", "Method:", r.Method, "Path:", r.URL.Path, "Email:", userEmail)

	r.Header.Set("Cluster-Id", strconv.Itoa(clusterRequested.Id))
	proxyServer.ServeHTTP(w, r)
}

func (handler *K8sApplicationRestHandlerImpl) HandleK8sProxyRequest(w http.ResponseWriter, r *http.Request) {
	// Devtron login token, extract this before forwarding as this can be deleted
	token := getDevtronLoginTokenFromK8sProxyRequest(r)

	// handle the forward logic
	handler.handleK8sProxyRequest(w, r)

	// set any headers that needs for our use cases
	r.Header.Set("token", token)
}

func getDevtronLoginTokenFromK8sProxyRequest(r *http.Request) string {
	token := strings.TrimPrefix(r.Header.Get(bean2.Authorization), "Bearer ")
	return token
}

func (handler *K8sApplicationRestHandlerImpl) DownloadPodContent(w http.ResponseWriter, r *http.Request) {
	token := r.Header.Get("token")
	userId, err := handler.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusUnauthorized)
		return
	}

	query := r.URL.Query()
	path := strings.TrimSpace(query.Get("path"))

	request, resourceRequestBean, err := handler.k8sApplicationService.ValidateTerminalRequestQuery(r)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	if request.Namespace == "" || request.PodName == "" || request.ContainerName == "" || path == "" {
		common.WriteJsonResp(w, util3.MissingParamsErrorInContentDownload, nil, http.StatusBadRequest)
		return
	}

	appTypeStr := query.Get("appType")
	appType, _ := strconv.Atoi(appTypeStr) // ignore error as this var is not expected for devtron apps/helm apps/resource bowser. appType var is needed in case of Argo Apps

	if !handler.verifyTerminalAccess(w, r, resourceRequestBean, token, appType) {
		return
	}

	request.ExternalArgoApplicationName = resourceRequestBean.ExternalArgoApplicationName
	request.ExternalArgoAppIdentifier = resourceRequestBean.ExternalArgoAppIdentifier

	// in case "/" is the path, the filename skips . and the files is saved without .tar. To avoid this fileName is set to devtron
	fileName := filepath.Base(path)
	if fileName == "/" {
		fileName = "root"
	}

	w.Header().Set("Content-Type", "application/zip")
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s.zip", fileName))
	err = handler.k8sApplicationService.DownloadPodContent(r.Context(), path, request, w)
	// not writing the error to writer as this can corrupt already written data.
	handler.logger.Infow("pod content download is done", "request", request, "err", err)

}

func (handler *K8sApplicationRestHandlerImpl) copyToWriterFromPipe(writer io.Writer, pipeReader *io.PipeReader, requestData interface{}, path string) {
	var err error
	defer func(pipeReader *io.PipeReader) {
		err = pipeReader.Close()
		if err != nil {
			handler.logger.Errorw("err", fmt.Sprintf("error closing writer %v", pipeReader), "err", err)
		}
		if r := recover(); r != nil {
			handler.logger.Errorw("go-routine recovered from panic", "err", r, "stack", string(debug.Stack()))
			//errorChan <- fmt.Errorf("copyToWriterFromPipe paniced, request: %v", requestData)
			//return
		}

		//if err != nil {
		//	//errorChan <- err
		//	return
		//}
		//errorChan <- nil

	}(pipeReader)

	// below is blocking code
	bytesWritten, err := io.Copy(writer, pipeReader)
	if err != nil {
		handler.logger.Errorw("err", "error copying from pipe: %v\n", err, "request", requestData, "path", path)
	}

	handler.logger.Infow("successfully copied content from pipe to io writer", "bytesWritten", bytesWritten, "request", requestData, "path", path)
}
