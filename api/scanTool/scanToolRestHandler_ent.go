/*
 * Copyright (c) 2024. Devtron Inc.
 */

package scanTool

import (
	"encoding/json"
	"errors"
	"github.com/devtron-labs/devtron/api/restHandler/common"
	"github.com/devtron-labs/devtron/pkg/auth/authorisation/casbin"
	"github.com/devtron-labs/devtron/pkg/auth/user"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/security/scanTool"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/security/scanTool/bean"
	"github.com/devtron-labs/devtron/util/rbac"
	"go.uber.org/zap"
	"gopkg.in/go-playground/validator.v9"
	"net/http"
)

type ScanToolRestHandler interface {
	RegisterScanTools(w http.ResponseWriter, r *http.Request)
}

type ScanToolRestHandlerImpl struct {
	logger          *zap.SugaredLogger
	userService     user.UserService
	enforcer        casbin.Enforcer
	enforcerUtil    rbac.EnforcerUtil
	validator       *validator.Validate
	scanToolService scanTool.ScanToolMetadataService
}

func NewScanToolRestHandlerImpl(
	logger *zap.SugaredLogger,
	userService user.UserService,
	scanToolService scanTool.ScanToolMetadataService,
	enforcer casbin.Enforcer,
	enforcerUtil rbac.EnforcerUtil,
	validator *validator.Validate,
) *ScanToolRestHandlerImpl {
	return &ScanToolRestHandlerImpl{
		logger:          logger,
		userService:     userService,
		scanToolService: scanToolService,
		enforcer:        enforcer,
		enforcerUtil:    enforcerUtil,
		validator:       validator,
	}
}

func (impl *ScanToolRestHandlerImpl) RegisterScanTools(w http.ResponseWriter, r *http.Request) {
	userId, err := impl.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	// since adding/registering a scan tool operates at global level hence super admin check
	// RBAC
	token := r.Header.Get("token")
	if ok := impl.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionGet, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized User"), nil, http.StatusForbidden)
		return
	}
	// RBAC
	var registerScanToolsDto bean.RegisterScanToolsDto
	decoder := json.NewDecoder(r.Body)
	err = decoder.Decode(&registerScanToolsDto)
	if err != nil {
		impl.logger.Errorw("error in decoding register scan tools payload", "registerScanToolPayload", registerScanToolsDto, "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	err = impl.validator.Struct(registerScanToolsDto)
	if err != nil {
		impl.logger.Errorw("validation err, RegisterScanTools", "registerScanToolPayload", registerScanToolsDto, "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	scanToolId, err := impl.scanToolService.RegisterScanTools(&registerScanToolsDto, userId)
	if err != nil {
		impl.logger.Errorw("service err, RegisterScanTools", "registerScanToolPayload", registerScanToolsDto, "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, &bean.ScanToolsMetadataDto{ScanToolId: scanToolId}, http.StatusOK)
}
