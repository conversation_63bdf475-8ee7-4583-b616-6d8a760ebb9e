/*
 * Copyright (c) 2024. Devtron Inc.
 */

package globalPolicy

import (
	"github.com/gorilla/mux"
	"go.uber.org/zap"
)

type GlobalPolicyRouter interface {
	InitGlobalPolicyRouter(router *mux.Router)
}

type GlobalPolicyRouterImpl struct {
	logger                  *zap.SugaredLogger
	globalPolicyRestHandler GlobalPolicyRestHandler
}

func NewGlobalPolicyRouterImpl(logger *zap.SugaredLogger,
	globalPolicyRestHandler GlobalPolicyRestHandler) *GlobalPolicyRouterImpl {
	return &GlobalPolicyRouterImpl{
		logger:                  logger,
		globalPolicyRestHandler: globalPolicyRestHandler,
	}
}

func (router *GlobalPolicyRouterImpl) InitGlobalPolicyRouter(policyRouter *mux.Router) {
	policyRouter.Path("/deployment-approval/pipeline/{pipelineId}").
		HandlerFunc(router.globalPolicyRestHandler.GetApprovalPolicyMetadataForPipeline).Methods("GET")
}
