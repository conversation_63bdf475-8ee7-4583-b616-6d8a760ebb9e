/*
 * Copyright (c) 2024. Devtron Inc.
 */

package globalPolicy

import (
	"fmt"
	"github.com/devtron-labs/devtron/api/restHandler/common"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/pkg/auth/authorisation/casbin"
	read2 "github.com/devtron-labs/devtron/pkg/policyGovernance/artifactApproval/read"
	util2 "github.com/devtron-labs/devtron/util"
	"github.com/devtron-labs/devtron/util/rbac"
	"github.com/gorilla/mux"
	"go.uber.org/zap"
	"net/http"
	"strconv"
)

type GlobalPolicyRestHandler interface {
	GetApprovalPolicyMetadataForPipeline(w http.ResponseWriter, r *http.Request)
}

type GlobalPolicyRestHandlerImpl struct {
	logger                          *zap.SugaredLogger
	pipelineRepo                    pipelineConfig.PipelineRepository
	enforcerUtil                    rbac.EnforcerUtil
	artifactApprovalDataReadService read2.ArtifactApprovalDataReadService
}

func NewGlobalPolicyRestHandlerImpl(logger *zap.SugaredLogger,
	pipelineRepo pipelineConfig.PipelineRepository,
	artifactApprovalDataReadService read2.ArtifactApprovalDataReadService,
	enforcerUtil rbac.EnforcerUtil) *GlobalPolicyRestHandlerImpl {
	return &GlobalPolicyRestHandlerImpl{
		logger:                          logger,
		enforcerUtil:                    enforcerUtil,
		pipelineRepo:                    pipelineRepo,
		artifactApprovalDataReadService: artifactApprovalDataReadService,
	}
}

func (handler *GlobalPolicyRestHandlerImpl) GetApprovalPolicyMetadataForPipeline(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	pipelineId, err := strconv.Atoi(vars["pipelineId"])
	if err != nil {
		handler.logger.Errorw("error in decoding pipelineId from path params", "pipelineId", pipelineId, "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	pipelineMinData, err := handler.pipelineRepo.FindPipelineMinById(pipelineId)
	if err != nil {
		handler.logger.Errorw("error in getting the pipeline details by pipelineId", "pipelineId", pipelineId, "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	// RBAC
	ctx := util2.NewRequestCtx(r.Context())
	resourceName := handler.enforcerUtil.GetAppRBACNameByAppId(pipelineMinData.AppId)
	ok := handler.enforcerUtil.CheckAppRbacForAppOrJob(ctx.GetToken(), resourceName, casbin.ActionGet)
	if !ok {
		common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
		return
	}
	// RBAC ends
	resp, err := handler.artifactApprovalDataReadService.GetDeploymentTriggerEligibleApproverInfoForPipeline(ctx, pipelineMinData)
	if err != nil {
		handler.logger.Errorw("error in getting the pipeline details by pipelineId", "pipelineId", pipelineId, "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, resp, http.StatusOK)

}
