/*
 * Copyright (c) 2024. Devtron Inc.
 */

package scoop

import (
	"context"
	"encoding/json"
	"fmt"
	scoopBean "github.com/devtron-labs/devtron/api/scoop/bean"
	utils2 "github.com/devtron-labs/devtron/api/scoop/utils"
	client2 "github.com/devtron-labs/devtron/client/events"
	"github.com/devtron-labs/devtron/client/events/notificationBean"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/pkg/apiToken"
	"github.com/devtron-labs/devtron/pkg/attributes"
	bean2 "github.com/devtron-labs/devtron/pkg/attributes/bean"
	bean3 "github.com/devtron-labs/devtron/pkg/auth/user/bean"
	"github.com/devtron-labs/devtron/pkg/autoRemediation"
	"github.com/devtron-labs/devtron/pkg/autoRemediation/repository"
	types2 "github.com/devtron-labs/devtron/pkg/autoRemediation/types"
	"github.com/devtron-labs/devtron/pkg/bean"
	"github.com/devtron-labs/devtron/pkg/bean/common"
	"github.com/devtron-labs/devtron/pkg/build/trigger"
	"github.com/devtron-labs/devtron/pkg/cluster/environment"
	"github.com/devtron-labs/devtron/pkg/cluster/read"
	"github.com/devtron-labs/devtron/pkg/pipeline"
	"github.com/devtron-labs/devtron/pkg/sql"
	util5 "github.com/devtron-labs/devtron/util/event"
	"github.com/devtron-labs/scoop/types"
	"github.com/go-pg/pg"
	"github.com/pkg/errors"
	uuid "github.com/satori/go.uuid"
	"go.uber.org/zap"
	"strings"
	"time"
)

type Service interface {
	HandleInterceptedEvent(ctx context.Context, event *types.InterceptedEvent) error
	HandleNotificationEvent(ctx context.Context, notification map[string]interface{}) error
}

type ServiceImpl struct {
	*ServiceEAImpl
	ciHandler                    pipeline.CiHandler
	ciPipelineMaterialRepository pipelineConfig.CiPipelineMaterialRepository
	clusterReadService           read.ClusterReadService
	environmentService           environment.EnvironmentService
	attributesService            attributes.AttributesService
	tokenService                 apiToken.ApiTokenService
	eventClient                  client2.EventClient
	eventFactory                 client2.EventFactory
	ciHandlerService             trigger.HandlerService
}

func NewServiceImpl(logger *zap.SugaredLogger,
	watcherService autoRemediation.WatcherService,
	ciHandler pipeline.CiHandler,
	ciPipelineMaterialRepository pipelineConfig.CiPipelineMaterialRepository,
	interceptedEventsRepository repository.InterceptedEventsRepository,
	clusterReadService read.ClusterReadService,
	attributesService attributes.AttributesService,
	tokenService apiToken.ApiTokenService,
	eventClient client2.EventClient,
	eventFactory client2.EventFactory,
	environmentService environment.EnvironmentService,
	ciHandlerService trigger.HandlerService,
) *ServiceImpl {
	return &ServiceImpl{
		ServiceEAImpl:                NewServiceEAImpl(logger, watcherService, interceptedEventsRepository),
		ciHandler:                    ciHandler,
		ciPipelineMaterialRepository: ciPipelineMaterialRepository,
		clusterReadService:           clusterReadService,
		attributesService:            attributesService,
		tokenService:                 tokenService,
		eventClient:                  eventClient,
		eventFactory:                 eventFactory,
		environmentService:           environmentService,
		ciHandlerService:             ciHandlerService,
	}
}

func (impl ServiceImpl) HandleInterceptedEvent(ctx context.Context, interceptedEvent *types.InterceptedEvent) error {

	// 1) get the host url from the attributes table and set hostUrl
	hostUrlObj, err := impl.attributesService.GetByKey(bean2.HostUrlKey)
	if err != nil {
		impl.logger.Errorw("error in getting the host url from attributes table", "err", err)
		return err
	}

	// 2) create a temp token to trigger notification

	expireAtInMs := time.Now().Add(24 * time.Hour).UnixMilli()
	token, err := impl.tokenService.CreateApiJwtToken("", 1, expireAtInMs)
	if err != nil {
		impl.logger.Errorw("error in creating api token", "err", err)
		return err
	}
	hostUrl := hostUrlObj.Value
	involvedObj, metadata, triggers, watchersMap, err := impl.getTriggersAndEventData(interceptedEvent)
	if err != nil {
		impl.logger.Errorw("error in getting triggers and intercepted event data", "interceptedEvent", interceptedEvent, "err", err)
		return err
	}

	tx, err := impl.interceptedEventsRepository.StartTx()
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			impl.logger.Debugw("rolling back db tx")
			rollbackErr := impl.interceptedEventsRepository.RollbackTx(tx)
			if err != nil {
				impl.logger.Errorw("error in rolling back db transaction while saving intercepted event executions", "err", rollbackErr)
			}
		}
	}()

	triggerMap := make(map[int]*types2.Trigger)
	interceptEventExecs := make([]*repository.InterceptedEventExecution, 0, len(triggers))
	for _, trigger := range triggers {
		switch trigger.IdentifierType {
		case types2.DEVTRON_JOB, types2.WEBHOOK:
			triggerMap[trigger.Id] = trigger
			interceptEventExec := &repository.InterceptedEventExecution{
				TriggerId: trigger.Id,
			}
			interceptEventExec.ClusterId = interceptedEvent.ClusterId
			interceptEventExec.Metadata = metadata
			interceptEventExec.SearchData = fmt.Sprintf("%s/%s/%s", interceptedEvent.ObjectMeta.Group, interceptedEvent.ObjectMeta.Kind, interceptedEvent.ObjectMeta.Name)
			interceptEventExec.InvolvedObjects = involvedObj
			interceptEventExec.InterceptedAt = interceptedEvent.InterceptedAt
			interceptEventExec.Namespace = interceptedEvent.Namespace
			interceptEventExec.Action = interceptedEvent.Action
			interceptEventExec.Status = repository.Initiated
			interceptEventExec.AuditLog = sql.NewDefaultAuditLog(1)
			interceptEventExecs = append(interceptEventExecs, interceptEventExec)
		}
	}

	// save the intercepted events first
	err = impl.saveInterceptedEvents(tx, interceptEventExecs)
	if err != nil {
		impl.logger.Errorw("error in saving intercepted event executions", "interceptEventExecs", interceptEventExecs, "err", err)
		return err
	}

	// trigger them, this can be triggered through NATS
	for _, interceptEventExec := range interceptEventExecs {
		if triggerMap[interceptEventExec.TriggerId].IsWebhookType() {
			interceptEventExec = impl.triggerWebhook(triggerMap[interceptEventExec.TriggerId], interceptEventExec, interceptedEvent)
		} else {
			interceptEventExec = impl.triggerJob(triggerMap[interceptEventExec.TriggerId], interceptEventExec, watchersMap, interceptedEvent, hostUrl, token)
		}
	}

	// update the status accordingly
	err = impl.updateInterceptedEvents(tx, interceptEventExecs)
	if err != nil {
		impl.logger.Errorw("error in updating intercepted event executions", "interceptEventExecs", interceptEventExecs, "err", err)
		return err
	}

	err = impl.interceptedEventsRepository.CommitTx(tx)
	if err != nil {
		impl.logger.Errorw("error in committing transaction while saving intercepted event executions", "interceptEventExecs", interceptEventExecs, "err", err)
		return err
	}

	return err
}

func (impl ServiceImpl) triggerJob(trigger *types2.Trigger, interceptEventExec *repository.InterceptedEventExecution, watchersMap map[int]*types.Watcher, interceptedEvent *types.InterceptedEvent, hostUrl, token string) *repository.InterceptedEventExecution {

	ciWorkflowId := 0
	status := repository.Progressing
	executionMessage := ""
	var err error
	defer func() {
		interceptEventExec.UpdatedOn = time.Now()
		interceptEventExec.Status = status
		// store the error here if something goes wrong before triggering the job
		interceptEventExec.ExecutionMessage = executionMessage
		interceptEventExec.TriggerExecutionId = ciWorkflowId
	}()

	request, err := impl.createTriggerRequest(trigger, interceptedEvent.Namespace, interceptedEvent.ClusterId)
	if err != nil {
		impl.logger.Errorw("error in creating trigger request", "err", err)
		status = repository.Errored
		executionMessage = err.Error()
		return interceptEventExec
	}

	cluster, err := impl.clusterReadService.FindById(interceptedEvent.ClusterId)
	if err != nil {
		impl.logger.Errorw("error in finding cluster using cluster id", "clusterId", interceptedEvent.ClusterId, "err", err)
		status = repository.Errored
		executionMessage = err.Error()
		return interceptEventExec
	}

	runtimeParams, err := impl.extractRuntimeParams(trigger, watchersMap, interceptedEvent, cluster.ClusterName, hostUrl, token, interceptEventExec.Id)
	if err != nil {
		impl.logger.Errorw("error in extracting runtime params for intercepted event trigger", "err", err)
		status = repository.Errored
		executionMessage = err.Error()
		return interceptEventExec
	}
	request.RuntimeParams = runtimeParams

	// get the commit for this pipeline as we need it during trigger
	// this call internally fetches the commits from git-sensor.
	gitCommits, err := impl.ciHandler.FetchMaterialsByPipelineId(trigger.Data.PipelineId, true)

	// if errored or no git commits are find, we should not trigger the job as, it will eventually fail.
	if err != nil || len(gitCommits) == 0 || len(gitCommits[0].History) == 0 {
		if err == nil {
			err = errors.New("no git commits found")
		}
		impl.logger.Errorw("error in getting git commits for ci pipeline", "ciPipelineId", trigger.Data.PipelineId, "err", err)
		executionMessage = err.Error()
		status = repository.Errored
	} else {
		// TODO: multi git case is not handled
		request.CiPipelineMaterial = []bean.CiPipelineMaterial{
			{
				Id: gitCommits[0].Id,
				GitCommit: pipelineConfig.GitCommit{
					Commit: gitCommits[0].History[0].Commit,
				},
			},
		}

		// trigger job pipeline
		ciWorkflowId, err = impl.ciHandlerService.HandleCIManual(*request)
		if err != nil {
			impl.logger.Errorw("error in trigger job ci pipeline", "triggerRequest", request, "err", err)
			executionMessage = err.Error()
			status = repository.Errored
		}

	}

	return interceptEventExec
}

func (impl ServiceImpl) HandleNotificationEvent(ctx context.Context, notification map[string]interface{}) error {

	var configType string
	var ok bool
	var configName string

	if configType, ok = notification["configType"].(string); !ok {
		return errors.New("config type not set")
	}

	if !(string(util5.Slack) == configType || string(util5.Webhook) == configType || string(util5.SES) == configType || string(util5.SMTP) == configType) {
		return errors.New("un-supported config type")
	}

	if configNameIf, ok := notification["configName"]; ok {
		if configName, ok = configNameIf.(string); !ok {
			return errors.New("un-supported config name")
		}

		if (string(util5.Slack) == configType || string(util5.Webhook) == configType) && configName == "" {
			return errors.New("config name is required for webhook/slack")
		}
	}

	notificationData := notificationBean.InterceptEventNotificationData{}
	dataString := ""
	if dataString, ok = notification["data"].(string); !ok {
		return errors.New("invalid notification data")
	}

	err := json.Unmarshal([]byte(dataString), &notificationData)
	if err != nil {
		return errors.New("invalid notification data, err : " + err.Error())
	}

	notification["eventTypeId"] = util5.ScoopNotification
	notification["eventTime"] = time.Now()
	notification["correlationId"] = fmt.Sprintf("%s", uuid.NewV4())
	emailIds := make([]string, 0)
	if emailsStr, ok := notification["emailIds"].(string); ok {
		emailIds = strings.Split(emailsStr, ",")
	}

	payload, err := impl.eventFactory.BuildScoopNotificationEventProviders(util5.Channel(configType), configName, emailIds, notificationData)
	if err != nil {
		impl.logger.Errorw("error in constructing event payload ", "notification", notification, "err", err)
		return err
	}

	notification["payload"] = payload
	_, err = impl.eventClient.SendAnyEvent(notification)
	if err != nil {
		impl.logger.Errorw("error in sending scoop event notification", "notification", notification, "err", err)
	}
	return err
}

func (impl ServiceImpl) extractRuntimeParams(trigger *types2.Trigger, watchersMap map[int]*types.Watcher, interceptedEvent *types.InterceptedEvent, clusterName, hostUrl, token string, interceptEventId int) (*common.RuntimeParameters, error) {
	runtimeParams := trigger.Data.GetRuntimeParams()
	var err error
	initialResource, finalResource, err := utils2.ComputeInitialAndFinalResource(interceptedEvent)
	if err != nil {
		impl.logger.Errorw("error in marshalling initial and final resource spec", "interceptedEvent", interceptedEvent, "err", err)
		return &runtimeParams, err
	}
	watcherName := watchersMap[trigger.WatcherId].Name
	action := strings.ToLower(string(interceptedEvent.Action))
	notificationData := notificationBean.NewInterceptEventNotificationData(
		interceptedEvent.ObjectMeta.Kind, interceptedEvent.ObjectMeta.Name,
		action, clusterName, interceptedEvent.ObjectMeta.Namespace,
		watcherName, hostUrl, trigger.Data.PipelineName,
		interceptedEvent.InterceptedAt, interceptEventId)

	notificationDataBytes, err := json.Marshal(notificationData)
	if err != nil {
		return &runtimeParams, err
	}
	presetEnvVariables := map[string]string{
		types.DevtronFinalManifest:   string(finalResource),
		types.DevtronInitialManifest: string(initialResource),
		scoopBean.NotificationData:   string(notificationDataBytes),
		scoopBean.NotificationToken:  token,
		scoopBean.NotificationUrl:    scoopBean.GetNotificationUrl(hostUrl),
	}
	for key, value := range presetEnvVariables {
		// new runtime plugin variable
		newRuntimePluginVar := common.NewRuntimeSystemVariableDto(key, value)
		runtimeParams.RuntimePluginVariables = append(runtimeParams.RuntimePluginVariables, newRuntimePluginVar)
	}
	return &runtimeParams, nil
}

func (impl ServiceImpl) createTriggerRequest(trigger *types2.Trigger, namespace string, clusterId int) (*bean.CiTriggerRequest, error) {
	if trigger.Data.ExecutionEnvironment == types2.SourceEnvironment {
		env, err := impl.environmentService.FindOneByNamespaceAndClusterId(namespace, clusterId)

		// if env is not found for the namespace in given cluster ,
		// then set the trigger env to 0. so that the trigger will happen in default env
		if err != nil && !errors.Is(err, pg.ErrNoRows) {
			return nil, err
		}
		if env != nil {
			trigger.Data.ExecutionEnvironmentId = env.Id
		}
	}

	return &bean.CiTriggerRequest{
		PipelineId: trigger.Data.PipelineId,
		// system user
		TriggeredBy:   bean3.SYSTEM_USER_ID,
		EnvironmentId: trigger.Data.ExecutionEnvironmentId,
		PipelineType:  "CI_BUILD",
	}, nil
}
