/*
 * Copyright (c) 2024. Devtron Inc.
 */

package util

import (
	bean2 "github.com/devtron-labs/devtron/pkg/auth/user/bean"
	"github.com/devtron-labs/devtron/pkg/auth/user/util"
	"strings"
)

func IsGroupsOrAccessRoleFiltersPresent(groups []string, accessRoleFilters []bean2.RoleFilter) bool {
	if len(groups) > 0 {
		return true
	}
	if len(accessRoleFilters) > 0 {
		return true
	}
	return false
}

func FilterRoleGroupIfAlreadyPresent(roleGroups []bean2.UserRoleGroup, mapOfExistingUserRoleGroupWithTwc map[string]bool) []bean2.UserRoleGroup {
	finalRoleGroups := make([]bean2.UserRoleGroup, 0, len(roleGroups))
	for _, roleGrp := range roleGroups {
		if _, ok := mapOfExistingUserRoleGroupWithTwc[util.GetUniqueKeyForUserRoleGroup(roleGrp)]; !ok {
			finalRoleGroups = append(finalRoleGroups, roleGrp)
		}
	}
	return finalRoleGroups

}

// CheckValidFilterAndRemoveRoleTypeAction this removed terminal exec role from role filter
func CheckValidFilterAndRemoveRoleTypeAction(filter bean2.RoleFilter, roleType bean2.RoleType) (bean2.RoleFilter, bool) {
	var newRoleFilter bean2.RoleFilter
	newRoleFilter = filter
	if strings.Contains(filter.Action, roleType.String()) {
		allActions := strings.Split(filter.Action, ",")
		var filteredActions []string

		for _, act := range allActions {
			if act != roleType.String() {
				filteredActions = append(filteredActions, act)
			}
		}
		newRoleFilter.Action = strings.Join(filteredActions, ",")
	}
	if newRoleFilter.Action == "" {
		// signifies new role filter action is empty and has to be removed
		return newRoleFilter, false
	}
	return newRoleFilter, true

}
