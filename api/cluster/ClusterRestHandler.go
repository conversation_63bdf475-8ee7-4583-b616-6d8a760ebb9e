/*
 * Copyright (c) 2020-2024. Devtron Inc.
 */

package cluster

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/devtron-labs/devtron/pkg/attributes"
	"github.com/devtron-labs/devtron/pkg/attributes/bean"
	"github.com/devtron-labs/devtron/pkg/cluster/adapter"
	clusterBean "github.com/devtron-labs/devtron/pkg/cluster/bean"
	"github.com/devtron-labs/devtron/pkg/cluster/environment"
	bean2 "github.com/devtron-labs/devtron/pkg/cluster/environment/bean"
	"github.com/devtron-labs/devtron/pkg/cluster/rbac"
	"github.com/devtron-labs/devtron/pkg/cluster/read"
	"github.com/devtron-labs/devtron/pkg/panel"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/devtron-labs/devtron/api/restHandler/common"
	"github.com/devtron-labs/devtron/pkg/auth/authorisation/casbin"
	"github.com/devtron-labs/devtron/pkg/auth/user"
	"github.com/devtron-labs/devtron/pkg/cluster"
	delete2 "github.com/devtron-labs/devtron/pkg/delete"
	"github.com/devtron-labs/devtron/pkg/genericNotes"
	"github.com/devtron-labs/devtron/pkg/genericNotes/repository"
	util2 "github.com/devtron-labs/devtron/util"
	"github.com/go-pg/pg"
	"github.com/gorilla/mux"
	"go.uber.org/zap"
	"gopkg.in/go-playground/validator.v9"
)

const CLUSTER_DELETE_SUCCESS_RESP = "Cluster deleted successfully."

type ClusterRestHandler interface {
	Save(w http.ResponseWriter, r *http.Request)
	SaveClusters(w http.ResponseWriter, r *http.Request)
	ValidateKubeconfig(w http.ResponseWriter, r *http.Request)
	SaveVirtualCluster(w http.ResponseWriter, r *http.Request)
	FindAll(w http.ResponseWriter, r *http.Request)
	FindById(w http.ResponseWriter, r *http.Request)
	FindNoteByClusterId(w http.ResponseWriter, r *http.Request)
	Update(w http.ResponseWriter, r *http.Request)
	UpdateVirtualCluster(w http.ResponseWriter, r *http.Request)
	UpdateClusterDescription(w http.ResponseWriter, r *http.Request)
	UpdateClusterNote(w http.ResponseWriter, r *http.Request)
	FindAllForAutoComplete(w http.ResponseWriter, r *http.Request)
	DeleteCluster(w http.ResponseWriter, r *http.Request)
	DeleteVirtualCluster(w http.ResponseWriter, r *http.Request)
	GetClusterNamespaces(w http.ResponseWriter, r *http.Request)
	GetClusterNamespacesMetadata(w http.ResponseWriter, r *http.Request)
	GetAllClusterNamespaces(w http.ResponseWriter, r *http.Request)
	FindAllForClusterPermission(w http.ResponseWriter, r *http.Request)
	ListPanels(w http.ResponseWriter, r *http.Request)
	CreatePanel(w http.ResponseWriter, r *http.Request)
	GetPanel(w http.ResponseWriter, r *http.Request)
	UpdatePanel(w http.ResponseWriter, r *http.Request)
	DeletePanel(w http.ResponseWriter, r *http.Request)
	AddPanelTemplateData(w http.ResponseWriter, r *http.Request)
	GetPanelTemplateData(w http.ResponseWriter, r *http.Request)

	GetCategories(w http.ResponseWriter, r *http.Request)
	GetCategoryByClusterId(w http.ResponseWriter, r *http.Request)
	AddUpdateCategories(w http.ResponseWriter, r *http.Request)
}

type ClusterRestHandlerImpl struct {
	clusterService            cluster.ClusterService
	clusterNoteService        genericNotes.GenericNoteService
	clusterDescriptionService cluster.ClusterDescriptionService
	logger                    *zap.SugaredLogger
	userService               user.UserService
	validator                 *validator.Validate
	enforcer                  casbin.Enforcer
	deleteService             delete2.DeleteService
	environmentService        environment.EnvironmentService
	clusterRbacService        rbac.ClusterRbacService
	panelService              panel.PanelService
	attributesService         attributes.AttributesService
	clusterReadService        read.ClusterReadService
	clusterCategoryService    cluster.ClusterCategoryService
}

func NewClusterRestHandlerImpl(clusterService cluster.ClusterService,
	clusterNoteService genericNotes.GenericNoteService,
	clusterDescriptionService cluster.ClusterDescriptionService,
	logger *zap.SugaredLogger,
	userService user.UserService,
	validator *validator.Validate,
	enforcer casbin.Enforcer,
	deleteService delete2.DeleteService,
	environmentService environment.EnvironmentService,
	clusterRbacService rbac.ClusterRbacService,
	panelService panel.PanelService,
	attributesService attributes.AttributesService,
	clusterReadService read.ClusterReadService,
	clusterCategoryService cluster.ClusterCategoryService) *ClusterRestHandlerImpl {
	return &ClusterRestHandlerImpl{
		clusterService:            clusterService,
		clusterNoteService:        clusterNoteService,
		clusterDescriptionService: clusterDescriptionService,
		logger:                    logger,
		userService:               userService,
		validator:                 validator,
		enforcer:                  enforcer,
		deleteService:             deleteService,
		environmentService:        environmentService,
		clusterRbacService:        clusterRbacService,
		panelService:              panelService,
		attributesService:         attributesService,
		clusterReadService:        clusterReadService,
		clusterCategoryService:    clusterCategoryService,
	}
}

func (impl ClusterRestHandlerImpl) SaveClusters(w http.ResponseWriter, r *http.Request) {
	token := r.Header.Get("token")
	decoder := json.NewDecoder(r.Body)
	userId, err := impl.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	beans := []*clusterBean.ClusterBean{}
	err = decoder.Decode(&beans)
	if err != nil {
		impl.logger.Errorw("request err, Save", "error", err, "payload", beans)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	// not logging bean object as it contains sensitive data
	impl.logger.Infow("request payload received for save clusters")

	// RBAC enforcer applying
	if ok := impl.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionCreate, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized User"), nil, http.StatusForbidden)
		return
	}
	//RBAC enforcer Ends
	ctx, cancel := context.WithCancel(r.Context())
	if cn, ok := w.(http.CloseNotifier); ok {
		go func(done <-chan struct{}, closed <-chan bool) {
			select {
			case <-done:
			case <-closed:
				cancel()
			}
		}(ctx.Done(), cn.CloseNotify())
	}
	if util2.IsBaseStack() {
		ctx = context.WithValue(ctx, "token", token)
	}

	for _, bean := range beans {
		l := len(bean.ServerUrl)
		if l > 1 && bean.ServerUrl[l-1:] == "/" {
			bean.ServerUrl = bean.ServerUrl[0 : l-1]
		}
		if bean.Id != 0 {
			_, err1 := impl.clusterService.Update(ctx, bean, userId)
			if err1 != nil {
				bean.ErrorInConnecting = err1.Error()
			} else {
				bean.ClusterUpdated = true
			}
		} else {
			_, err1 := impl.clusterService.Save(ctx, nil, bean, userId)
			if err1 != nil {
				bean.ErrorInConnecting = err1.Error()
			}
		}
	}

	res := beans

	common.WriteJsonResp(w, err, res, http.StatusOK)
}

func (impl ClusterRestHandlerImpl) Save(w http.ResponseWriter, r *http.Request) {
	token := r.Header.Get("token")
	decoder := json.NewDecoder(r.Body)
	userId, err := impl.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	bean := new(clusterBean.ClusterBean)
	err = decoder.Decode(bean)
	if err != nil {
		impl.logger.Errorw("request err, Save", "error", err, "payload", bean)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	impl.logger.Infow("request payload, Save", "payload", bean)
	err = impl.validator.Struct(bean)
	if err != nil {
		impl.logger.Errorw("validation err, Save", "err", err, "payload", bean)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	// RBAC enforcer applying
	if ok := impl.enforcer.Enforce(token, casbin.ResourceCluster, casbin.ActionCreate, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	//RBAC enforcer Ends
	ctx, cancel := context.WithCancel(r.Context())
	if cn, ok := w.(http.CloseNotifier); ok {
		go func(done <-chan struct{}, closed <-chan bool) {
			select {
			case <-done:
			case <-closed:
				cancel()
			}
		}(ctx.Done(), cn.CloseNotify())
	}
	if util2.IsBaseStack() {
		ctx = context.WithValue(ctx, "token", token)
	}
	bean, err = impl.clusterService.Save(ctx, nil, bean, userId)
	if err != nil {
		impl.logger.Errorw("service err, Save", "err", err, "payload", bean)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	/*	isTriggered, err := impl.installedAppService.DeployDefaultChartOnCluster(bean, userId)
		if err != nil {
			impl.logger.Errorw("service err, Save, on DeployDefaultChartOnCluster", "err", err, "payload", bean)
		}
		if isTriggered {
			bean.AgentInstallationStage = 1
		} else {
			bean.AgentInstallationStage = 0
		}*/
	adapter.ConvertNewClusterBeanToOldClusterBean(bean)
	common.WriteJsonResp(w, err, bean, http.StatusOK)
}

func (impl ClusterRestHandlerImpl) SaveVirtualCluster(w http.ResponseWriter, r *http.Request) {
	token := r.Header.Get("token")
	decoder := json.NewDecoder(r.Body)
	userId, err := impl.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	bean := new(clusterBean.VirtualClusterBean)
	err = decoder.Decode(bean)
	if err != nil {
		impl.logger.Errorw("request err, Save", "error", err, "payload", bean)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	impl.logger.Infow("request payload, Save", "payload", bean)
	err = impl.validator.Struct(bean)
	if err != nil {
		impl.logger.Errorw("validation err, Save", "err", err, "payload", bean)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	// RBAC enforcer applying
	if ok := impl.enforcer.Enforce(token, casbin.ResourceCluster, casbin.ActionCreate, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	clusterBean, err := impl.clusterService.SaveVirtualCluster(bean, userId)
	if err != nil {
		impl.logger.Errorw("error in saving cluster", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, clusterBean, http.StatusOK)
}

func (impl ClusterRestHandlerImpl) ValidateKubeconfig(w http.ResponseWriter, r *http.Request) {
	token := r.Header.Get("token")
	decoder := json.NewDecoder(r.Body)
	userId, err := impl.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	bean := &clusterBean.Kubeconfig{}
	err = decoder.Decode(bean)
	if err != nil {
		impl.logger.Errorw("request err, Validate", "error", err, "payload", bean)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	err = impl.validator.Struct(bean)
	if err != nil {
		impl.logger.Errorw("validation err, Validate", "err", err, "payload", bean)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	// RBAC enforcer applying
	if ok := impl.enforcer.Enforce(token, casbin.ResourceCluster, casbin.ActionCreate, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	//RBAC enforcer Ends
	ctx, cancel := context.WithCancel(r.Context())
	if cn, ok := w.(http.CloseNotifier); ok {
		go func(done <-chan struct{}, closed <-chan bool) {
			select {
			case <-done:
			case <-closed:
				cancel()
			}
		}(ctx.Done(), cn.CloseNotify())
	}
	if util2.IsBaseStack() {
		ctx = context.WithValue(ctx, "token", token)
	}
	res, err := impl.clusterService.ValidateKubeconfig(bean.Config)
	if err != nil {
		impl.logger.Errorw("error in validating kubeconfig")
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	common.WriteJsonResp(w, err, res, http.StatusOK)
}

func (impl ClusterRestHandlerImpl) FindAll(w http.ResponseWriter, r *http.Request) {
	token := r.Header.Get("token")
	clusterList, err := impl.clusterService.FindAllWithoutConfig()
	if err != nil {
		impl.logger.Errorw("service err, FindAll", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	// RBAC enforcer applying
	var result []*clusterBean.ClusterBean
	for _, item := range clusterList {
		if ok := impl.enforcer.Enforce(token, casbin.ResourceCluster, casbin.ActionGet, item.ClusterName); ok {
			adapter.ConvertNewClusterBeanToOldClusterBean(item)
			result = append(result, item)
		}
	}
	//RBAC enforcer Ends

	common.WriteJsonResp(w, err, result, http.StatusOK)
}

func (impl ClusterRestHandlerImpl) FindById(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]
	i, err := strconv.Atoi(id)
	if err != nil {
		impl.logger.Errorw("request err, FindById", "error", err, "clusterId", id)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	bean, err := impl.clusterService.FindByIdWithoutConfig(i)
	if err != nil {
		impl.logger.Errorw("service err, FindById", "err", err, "clusterId", id)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	// RBAC enforcer applying
	token := r.Header.Get("token")
	if ok := impl.enforcer.Enforce(token, casbin.ResourceCluster, casbin.ActionGet, bean.ClusterName); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	//RBAC enforcer Ends
	adapter.ConvertNewClusterBeanToOldClusterBean(bean)
	common.WriteJsonResp(w, err, bean, http.StatusOK)
}

func (impl ClusterRestHandlerImpl) FindNoteByClusterId(w http.ResponseWriter, r *http.Request) {
	userId, err := impl.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	vars := mux.Vars(r)
	id := vars["id"]
	clusterId, err := strconv.Atoi(id)
	if err != nil {
		impl.logger.Errorw("request err, FindById", "error", err, "clusterId", id)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	bean, err := impl.clusterDescriptionService.FindByClusterIdWithClusterDetails(clusterId)
	if err != nil {
		if err == pg.ErrNoRows {
			impl.logger.Errorw("cluster not found, FindById", "err", err, "clusterId", id)
			common.WriteJsonResp(w, errors.New("invalid cluster id"), nil, http.StatusNotFound)
			return
		}
		impl.logger.Errorw("service err, FindById", "err", err, "clusterId", id)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	// RBAC enforcer applying
	token := r.Header.Get("token")
	authenticated, err := impl.clusterRbacService.CheckAuthorization(bean.ClusterName, bean.ClusterId, token, userId, true)
	if err != nil {
		impl.logger.Errorw("error in checking rbac for cluster", "err", err, "clusterId", bean.ClusterId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	if !authenticated {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	//RBAC enforcer Ends
	common.WriteJsonResp(w, err, bean, http.StatusOK)
}

func (impl ClusterRestHandlerImpl) Update(w http.ResponseWriter, r *http.Request) {
	token := r.Header.Get("token")
	decoder := json.NewDecoder(r.Body)
	userId, err := impl.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		impl.logger.Errorw("service err, Update", "error", err, "userId", userId)
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	var bean clusterBean.ClusterBean
	err = decoder.Decode(&bean)
	if err != nil {
		impl.logger.Errorw("request err, Update", "error", err, "payload", bean)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	impl.logger.Infow("request payload, Update", "payload", bean)
	err = impl.validator.Struct(bean)
	if err != nil {
		impl.logger.Errorw("validate err, Update", "error", err, "payload", bean)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	// RBAC enforcer applying
	if ok := impl.enforcer.Enforce(token, casbin.ResourceCluster, casbin.ActionUpdate, bean.ClusterName); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	// RBAC enforcer ends
	ctx, cancel := context.WithCancel(r.Context())
	if cn, ok := w.(http.CloseNotifier); ok {
		go func(done <-chan struct{}, closed <-chan bool) {
			select {
			case <-done:
			case <-closed:
				cancel()
			}
		}(ctx.Done(), cn.CloseNotify())
	}
	if util2.IsBaseStack() {
		ctx = context.WithValue(ctx, "token", token)
	}
	_, err = impl.clusterService.Update(ctx, &bean, userId)
	if err != nil {
		impl.logger.Errorw("service err, Update", "error", err, "payload", bean)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	adapter.ConvertNewClusterBeanToOldClusterBean(&bean)
	common.WriteJsonResp(w, err, bean, http.StatusOK)
}

func (impl ClusterRestHandlerImpl) UpdateVirtualCluster(w http.ResponseWriter, r *http.Request) {
	token := r.Header.Get("token")
	decoder := json.NewDecoder(r.Body)
	userId, err := impl.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	bean := new(clusterBean.VirtualClusterBean)
	err = decoder.Decode(bean)
	if err != nil {
		impl.logger.Errorw("request err, Save", "error", err, "payload", bean)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	impl.logger.Infow("request payload, Save", "payload", bean)
	err = impl.validator.Struct(bean)
	if err != nil {
		impl.logger.Errorw("validation err, Save", "err", err, "payload", bean)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	// RBAC enforcer applying
	if ok := impl.enforcer.Enforce(token, casbin.ResourceCluster, casbin.ActionUpdate, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}

	bean, err = impl.clusterService.UpdateVirtualCluster(bean, userId)
	if err != nil {
		impl.logger.Errorw("service err, Update", "error", err, "payload", bean)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	common.WriteJsonResp(w, err, bean, http.StatusOK)

}

func (impl ClusterRestHandlerImpl) UpdateClusterDescription(w http.ResponseWriter, r *http.Request) {
	token := r.Header.Get("token")
	decoder := json.NewDecoder(r.Body)
	userId, err := impl.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		impl.logger.Errorw("service err, Update", "error", err, "userId", userId)
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	var bean clusterBean.ClusterBean
	err = decoder.Decode(&bean)
	if err != nil {
		impl.logger.Errorw("request err, UpdateClusterDescription", "error", err, "payload", bean)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	impl.logger.Infow("request payload, UpdateClusterDescription", "payload", bean)
	//TODO: add apt validation
	clusterDescription, err := impl.clusterDescriptionService.FindByClusterIdWithClusterDetails(bean.Id)
	if err != nil {
		impl.logger.Errorw("service err, FindById", "err", err, "clusterId", bean.Id)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	// RBAC enforcer applying
	authenticated := impl.clusterRbacService.CheckAuthorisationForAllK8sPermissions(token, clusterDescription.ClusterName, casbin.ActionUpdate)
	if !authenticated {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	// RBAC enforcer ends
	err = impl.clusterService.UpdateClusterDescription(&bean, userId)
	if err != nil {
		impl.logger.Errorw("service err, UpdateClusterDescription", "error", err, "payload", bean)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, "Cluster description updated successfully", http.StatusOK)
}

func (impl ClusterRestHandlerImpl) UpdateClusterNote(w http.ResponseWriter, r *http.Request) {
	token := r.Header.Get("token")
	decoder := json.NewDecoder(r.Body)
	userId, err := impl.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		impl.logger.Errorw("service err, Update", "error", err, "userId", userId)
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	var bean repository.GenericNote
	err = decoder.Decode(&bean)
	if err != nil {
		impl.logger.Errorw("request err, Update", "error", err, "payload", bean)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	impl.logger.Infow("request payload, Update", "payload", bean)
	err = impl.validator.Struct(bean)
	if err != nil {
		impl.logger.Errorw("validate err, Update", "error", err, "payload", bean)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	clusterDescription, err := impl.clusterDescriptionService.FindByClusterIdWithClusterDetails(bean.Identifier)
	if err != nil {
		impl.logger.Errorw("service err, FindById", "err", err, "clusterId", bean.Identifier)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	// RBAC enforcer applying
	authenticated := impl.clusterRbacService.CheckAuthorisationForAllK8sPermissions(token, clusterDescription.ClusterName, casbin.ActionUpdate)
	if !authenticated {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	// RBAC enforcer ends

	bean.IdentifierType = repository.ClusterType
	clusterNoteResponseBean, err := impl.clusterNoteService.Update(&bean, userId)

	if err != nil {
		impl.logger.Errorw("cluster note service err, Update", "error", err, "payload", bean)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, clusterNoteResponseBean, http.StatusOK)
}

func (impl ClusterRestHandlerImpl) FindAllForAutoComplete(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	clusterList, err := impl.clusterService.FindAllForAutoComplete()
	dbOperationTime := time.Since(start)
	if err != nil {
		impl.logger.Errorw("service err, FindAllForAutoComplete", "error", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	var result []clusterBean.ClusterBean
	v := r.URL.Query()
	authEnabled := true
	auth := v.Get("auth")
	if len(auth) > 0 {
		authEnabled, err = strconv.ParseBool(auth)
		if err != nil {
			authEnabled = true
			err = nil
			//ignore error, apply rbac by default
		}
	}
	// RBAC enforcer applying
	token := r.Header.Get("token")
	start = time.Now()
	for _, item := range clusterList {
		adapter.ConvertNewClusterBeanToOldClusterBean(&item)
		if authEnabled == true {
			if ok := impl.enforcer.Enforce(token, casbin.ResourceCluster, casbin.ActionGet, item.ClusterName); ok {
				result = append(result, item)
			}
		} else {
			result = append(result, item)
		}

	}
	impl.logger.Infow("Cluster elapsed Time for enforcer", "dbElapsedTime", dbOperationTime, "enforcerTime", time.Since(start), "envSize", len(result))
	//RBAC enforcer Ends

	if len(result) == 0 {
		result = make([]clusterBean.ClusterBean, 0)
	}
	common.WriteJsonResp(w, err, result, http.StatusOK)
}

func (impl ClusterRestHandlerImpl) DeleteCluster(w http.ResponseWriter, r *http.Request) {
	decoder := json.NewDecoder(r.Body)
	userId, err := impl.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		impl.logger.Errorw("service err, Delete", "error", err, "userId", userId)
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	var bean clusterBean.DeleteClusterBean
	err = decoder.Decode(&bean)
	if err != nil {
		impl.logger.Errorw("request err, Delete", "error", err, "payload", bean)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	impl.logger.Debugw("request payload, Delete", "payload", bean)
	err = impl.validator.Struct(bean)
	if err != nil {
		impl.logger.Errorw("validate err, Delete", "error", err, "payload", bean)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	// RBAC enforcer applying
	token := r.Header.Get("token")
	if ok := impl.enforcer.Enforce(token, casbin.ResourceCluster, casbin.ActionCreate, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	//RBAC enforcer Ends
	err = impl.deleteService.DeleteCluster(&bean, userId)
	if err != nil {
		impl.logger.Errorw("error in deleting cluster", "err", err, "id", bean.Id)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, CLUSTER_DELETE_SUCCESS_RESP, http.StatusOK)
}

func (impl ClusterRestHandlerImpl) GetAllClusterNamespaces(w http.ResponseWriter, r *http.Request) {
	token := r.Header.Get("token")
	userId, err := impl.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		impl.logger.Errorw("service err, GetAllClusterNamespaces", "error", err, "userId", userId)
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	clusterNamespaces := impl.clusterService.GetAllClusterNamespaces()

	// RBAC enforcer applying
	filteredClusterNamespaces, err := impl.HandleRbacForClusterNamespace(userId, token, clusterNamespaces)
	if err != nil {
		impl.logger.Errorw("error in GetAllClusterNamespaces", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	//RBAC enforcer Ends
	common.WriteJsonResp(w, nil, filteredClusterNamespaces, http.StatusOK)
}

func (impl ClusterRestHandlerImpl) HandleRbacForClusterNamespace(userId int32, token string, clusterNamespaces map[string][]string) (map[string][]string, error) {
	filteredClusterNamespaces := make(map[string][]string)
	if ok := impl.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionGet, "*"); ok {
		return clusterNamespaces, nil
	}
	roles, err := impl.clusterService.FetchRolesFromGroup(userId, token)
	if err != nil {
		impl.logger.Errorw("error on fetching user roles for cluster list", "err", err)
		return nil, err
	}

	clusterAndNameSpaceVsAllowedMap := make(map[string]bool, len(roles))
	clusterNameVsAllAllowedMap := make(map[string]bool, len(roles))
	for _, role := range roles {
		clusterAndNameSpaceVsAllowedMap[strings.ToLower(role.Cluster+"_"+role.Namespace)] = true
		if role.Namespace == "" {
			clusterNameVsAllAllowedMap[role.Cluster] = true
		} else {
			clusterNameVsAllAllowedMap[role.Cluster] = false
		}
	}

	for clusterName, allNamespaces := range clusterNamespaces {
		if val, exist := clusterNameVsAllAllowedMap[clusterName]; val {
			filteredClusterNamespaces[clusterName] = allNamespaces
		} else if exist {
			for _, namespace := range allNamespaces {
				if val2, exist2 := clusterAndNameSpaceVsAllowedMap[strings.ToLower(clusterName+"_"+namespace)]; exist2 && val2 {
					filteredClusterNamespaces[clusterName] = append(filteredClusterNamespaces[clusterName], namespace)
				}
			}
		}
	}
	return filteredClusterNamespaces, nil

}

func (impl ClusterRestHandlerImpl) GetClusterNamespaces(w http.ResponseWriter, r *http.Request) {
	//token := r.Header.Get("token")
	vars := mux.Vars(r)
	clusterIdString := vars["clusterId"]

	userId, err := impl.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		impl.logger.Errorw("user not authorized", "error", err, "userId", userId)
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	token := r.Header.Get("token")
	isActionUserSuperAdmin := false
	if ok := impl.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionGet, "*"); ok {
		isActionUserSuperAdmin = true
	}
	clusterId, err := strconv.Atoi(clusterIdString)
	if err != nil {
		impl.logger.Errorw("failed to extract clusterId from param", "error", err, "clusterId", clusterIdString)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	allClusterNamespaces, err := impl.clusterService.FindAllNamespacesByUserIdAndClusterId(userId, clusterId, isActionUserSuperAdmin, token)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, allClusterNamespaces, http.StatusOK)
}

func (impl ClusterRestHandlerImpl) GetClusterNamespacesMetadata(w http.ResponseWriter, r *http.Request) {
	//token := r.Header.Get("token")
	vars := mux.Vars(r)
	clusterIdString := vars["clusterId"]

	userId, err := impl.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		impl.logger.Errorw("user not authorized", "userId", userId, "error", err)
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	token := r.Header.Get("token")
	isActionUserSuperAdmin := false
	if ok := impl.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionGet, "*"); ok {
		isActionUserSuperAdmin = true
	}
	clusterId, err := strconv.Atoi(clusterIdString)
	if err != nil {
		impl.logger.Errorw("failed to extract clusterId from param", "clusterId", clusterIdString, "error", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	clusterName, allClusterNamespaces, err := impl.clusterService.FindAllNamespacesMetadataByClusterId(clusterId)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	if isActionUserSuperAdmin {
		common.WriteJsonResp(w, nil, allClusterNamespaces, http.StatusOK)
		return
	}

	authorizedNamespaces, err := impl.clusterRbacService.FilterUnauthorizedNamespaces(allClusterNamespaces, clusterName, userId, token)
	if err != nil {
		impl.logger.Errorw("error in filtering unauthorized namespaces", "err", err)
		common.WriteJsonResp(w, errors.New("error in fetching namespaces"), nil, http.StatusInternalServerError)
		return
	}

	common.WriteJsonResp(w, nil, authorizedNamespaces, http.StatusOK)
}

func (impl ClusterRestHandlerImpl) FindAllForClusterPermission(w http.ResponseWriter, r *http.Request) {
	userId, err := impl.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		impl.logger.Errorw("user not authorized", "error", err, "userId", userId)
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	token := r.Header.Get("token")
	isActionUserSuperAdmin := false
	if ok := impl.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionGet, "*"); ok {
		isActionUserSuperAdmin = true
	}
	clusterList, err := impl.clusterService.FindAllForClusterByUserId(userId, isActionUserSuperAdmin, token)
	if err != nil {
		impl.logger.Errorw("error in deleting cluster", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	// RBAC enforcer applying
	// Already applied at service layer
	//RBAC enforcer Ends

	if len(clusterList) == 0 {
		// assumption is that if list is empty, then it can happen only in case of Unauthorized (but not sending Unauthorized for super-admin user)
		if isActionUserSuperAdmin {
			clusterList = make([]clusterBean.ClusterBean, 0)
		} else {
			common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
			return
		}
	}
	common.WriteJsonResp(w, err, clusterList, http.StatusOK)
}

func (impl *ClusterRestHandlerImpl) DeleteVirtualCluster(w http.ResponseWriter, r *http.Request) {
	decoder := json.NewDecoder(r.Body)
	userId, err := impl.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		impl.logger.Errorw("service err, Delete", "error", err, "userId", userId)
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	var bean clusterBean.VirtualClusterBean
	err = decoder.Decode(&bean)
	if err != nil {
		impl.logger.Errorw("request err, Delete", "error", err, "payload", bean)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	impl.logger.Debugw("request payload, Delete", "payload", bean)
	err = impl.validator.Struct(bean)
	if err != nil {
		impl.logger.Errorw("validate err, Delete", "error", err, "payload", bean)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	// RBAC enforcer applying
	token := r.Header.Get("token")
	if ok := impl.enforcer.Enforce(token, casbin.ResourceCluster, casbin.ActionDelete, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	//RBAC enforcer Ends
	err = impl.deleteService.DeleteVirtualCluster(&bean, userId)
	if err != nil {
		impl.logger.Errorw("error in deleting cluster", "err", err, "id", bean.Id, "name", bean.ClusterName)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, CLUSTER_DELETE_SUCCESS_RESP, http.StatusOK)
}

func (impl *ClusterRestHandlerImpl) ListPanels(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	clusterID := vars["cluster_id"]

	token := r.Header.Get("token")
	userId, err := impl.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}

	clusterIdInt, err := strconv.Atoi(clusterID)
	if err != nil {
		impl.logger.Errorw("error in converting cluster id to int", "err", err, "clusterID", clusterID)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	cluster, err := impl.clusterReadService.FindById(clusterIdInt)
	if err != nil {
		impl.logger.Errorw("error in finding cluster", "err", err, "clusterID", clusterID)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	authenticated, err := impl.clusterRbacService.CheckAuthorization(cluster.ClusterName, cluster.Id, token, userId, true)
	if err != nil {
		impl.logger.Errorw("error in checking rbac for cluster", "err", err, "clusterId", cluster.Id)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	if !authenticated {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}

	panels, err := impl.panelService.ListPanels(r.Context(), clusterIdInt)
	if err != nil {
		impl.logger.Errorw("error in listing panels", "err", err, "clusterID", clusterID)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, panels, http.StatusOK)
}

func (impl *ClusterRestHandlerImpl) CreatePanel(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	clusterID := vars["cluster_id"]

	userId, err := impl.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}

	var newPanel clusterBean.PanelBean
	decoder := json.NewDecoder(r.Body)
	err = decoder.Decode(&newPanel)
	if err != nil {
		impl.logger.Errorw("request err, Create", "error", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	clusterIdInt, err := strconv.Atoi(clusterID)
	if err != nil {
		impl.logger.Errorw("error in converting cluster id to int", "err", err, "clusterID", clusterID)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	cluster, err := impl.clusterReadService.FindById(clusterIdInt)
	if err != nil {
		impl.logger.Errorw("error in finding cluster", "err", err, "clusterID", clusterID)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	// RBAC enforcer applying
	token := r.Header.Get("token")
	if ok := impl.enforcer.Enforce(token, casbin.ResourceCluster, casbin.ActionUpdate, cluster.ClusterName); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	// RBAC enforcer ends

	err = impl.validatePanelBean(newPanel)
	if err != nil {
		impl.logger.Errorw("validation err, Create", "error", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	createdPanel, err := impl.panelService.CreatePanel(r.Context(), newPanel, userId, clusterID)
	if err != nil {
		impl.logger.Errorw("error in creating panel", "err", err, "clusterID", clusterID)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, createdPanel, http.StatusCreated)
}

func (impl *ClusterRestHandlerImpl) validatePanelBean(panel clusterBean.PanelBean) error {
	if panel.Name == "" {
		return errors.New("panel name is required")
	}

	if !impl.panelService.ValidateEmbedIframe(panel.EmbedIframe) {
		return errors.New("invalid embed_iframe")
	}

	return nil
}

func (impl *ClusterRestHandlerImpl) GetPanel(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	clusterID := vars["cluster_id"]
	panelID := vars["panel_id"]
	token := r.Header.Get("token")
	userId, err := impl.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}

	clusterIdInt, err := strconv.Atoi(clusterID)
	if err != nil {
		impl.logger.Errorw("error in converting cluster id to int", "err", err, "clusterID", clusterID)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	cluster, err := impl.clusterReadService.FindById(clusterIdInt)
	if err != nil {
		impl.logger.Errorw("error in finding cluster", "err", err, "clusterID", clusterID)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	authenticated, err := impl.clusterRbacService.CheckAuthorization(cluster.ClusterName, cluster.Id, token, userId, true)
	if err != nil {
		impl.logger.Errorw("error in checking rbac for cluster", "err", err, "clusterId", cluster.Id)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	if !authenticated {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}

	panel, err := impl.panelService.GetPanel(r.Context(), clusterID, panelID)
	if err != nil {
		impl.logger.Errorw("error in getting panel", "err", err, "clusterID", clusterID, "panelID", panelID)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, panel, http.StatusOK)
}

func (impl ClusterRestHandlerImpl) UpdatePanel(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	clusterID := vars["cluster_id"]
	panelID := vars["panel_id"]

	userId, err := impl.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}

	var updatedPanel clusterBean.PanelBean
	decoder := json.NewDecoder(r.Body)
	err = decoder.Decode(&updatedPanel)
	if err != nil {
		impl.logger.Errorw("request err, Update", "error", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	clusterIdInt, err := strconv.Atoi(clusterID)
	if err != nil {
		impl.logger.Errorw("error in converting cluster id to int", "err", err, "clusterID", clusterID)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	cluster, err := impl.clusterReadService.FindById(clusterIdInt)
	if err != nil {
		impl.logger.Errorw("error in finding cluster", "err", err, "clusterID", clusterID)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	// RBAC enforcer applying
	token := r.Header.Get("token")
	if ok := impl.enforcer.Enforce(token, casbin.ResourceCluster, casbin.ActionUpdate, cluster.ClusterName); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	// RBAC enforcer ends

	err = impl.validatePanelBean(updatedPanel)
	if err != nil {
		impl.logger.Errorw("validation err, Create", "error", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	panel, err := impl.panelService.UpdatePanel(r.Context(), clusterID, panelID, updatedPanel, userId)
	if err != nil {
		impl.logger.Errorw("error in updating panel", "err", err, "clusterID", clusterID, "panelID", panelID)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, panel, http.StatusOK)
}

func (impl ClusterRestHandlerImpl) DeletePanel(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	clusterID := vars["cluster_id"]
	panelID := vars["panel_id"]

	clusterIdInt, err := strconv.Atoi(clusterID)
	if err != nil {
		impl.logger.Errorw("error in converting cluster id to int", "err", err, "clusterID", clusterID)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	cluster, err := impl.clusterReadService.FindById(clusterIdInt)
	if err != nil {
		impl.logger.Errorw("error in finding cluster", "err", err, "clusterID", clusterID)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	// RBAC enforcer applying
	token := r.Header.Get("token")
	if ok := impl.enforcer.Enforce(token, casbin.ResourceCluster, casbin.ActionUpdate, cluster.ClusterName); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	// RBAC enforcer ends

	err = impl.panelService.DeletePanel(r.Context(), clusterID, panelID)
	if err != nil {
		impl.logger.Errorw("error in deleting panel", "err", err, "clusterID", clusterID, "panelID", panelID)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, "Panel deleted successfully", http.StatusOK)
}

func (handler ClusterRestHandlerImpl) AddPanelTemplateData(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	decoder := json.NewDecoder(r.Body)
	var dto bean.AttributesDto
	err = decoder.Decode(&dto)
	if err != nil {
		handler.logger.Errorw("request err, AddAttributes", "err", err, "payload", dto)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	// validation to check prefix in key ie panel-template
	if !isPanelTemplateKey(dto.Key) {
		common.WriteJsonResp(w, errors.New("invalid key, must have panel-template as prefix"), nil, http.StatusBadRequest)
		return
	}
	// validation to check cluster id in key
	clusterId := getClusterIdFromKey(dto.Key)
	var clusterIdInt int
	if clusterIdInt, err = strconv.Atoi(clusterId); err != nil {
		common.WriteJsonResp(w, errors.New("invalid cluster_id in key"), nil, http.StatusBadRequest)
		return
	}

	cluster, err := handler.clusterReadService.FindById(clusterIdInt)
	if err != nil {
		handler.logger.Errorw("error in finding cluster", "err", err, "clusterID", clusterIdInt)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	// RBAC enforcer applying
	token := r.Header.Get("token")
	if ok := handler.enforcer.Enforce(token, casbin.ResourceCluster, casbin.ActionUpdate, cluster.ClusterName); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}
	// RBAC enforcer ends

	handler.logger.Infow("request payload, AddAttributes", "payload", dto)
	resp, err := handler.attributesService.AddAttributes(&dto)
	if err != nil {
		handler.logger.Errorw("service err, AddAttributes", "err", err, "payload", dto)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, resp, http.StatusOK)
}

func (handler ClusterRestHandlerImpl) GetPanelTemplateData(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}

	vars := mux.Vars(r)
	key := vars["key"]
	res, err := handler.attributesService.GetByKey(key)
	if err != nil {
		handler.logger.Errorw("service err, GetAttributesById", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, res, http.StatusOK)
}

func isPanelTemplateKey(key string) bool {
	return strings.HasPrefix(key, "panel-template")
}

func getClusterIdFromKey(key string) string {
	parts := strings.Split(key, "-")
	return parts[len(parts)-1]
}

func (handler ClusterRestHandlerImpl) GetCategories(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	token := r.Header.Get("token")
	//RBAC block starts from here
	if ok := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionGet, "*"); !ok {
		common.WriteJsonResp(w, errors.New("unauthorized User"), nil, http.StatusForbidden)
		return
	}
	//RBAC block ends here

	res, err := handler.clusterCategoryService.GetCategories()
	if err != nil {
		handler.logger.Errorw("service err, GetCategories", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, res, http.StatusOK)
}

func (handler ClusterRestHandlerImpl) GetCategoryByClusterId(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}

	vars := mux.Vars(r)
	clusterID := vars["cluster_id"]
	token := r.Header.Get("token")
	clusterIdInt, err := strconv.Atoi(clusterID)
	if err != nil {
		handler.logger.Errorw("error in converting cluster id to int", "err", err, "clusterID", clusterID)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	cluster, err := handler.clusterReadService.FindById(clusterIdInt)
	if err != nil {
		handler.logger.Errorw("error in finding cluster", "err", err, "clusterID", clusterID)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	authenticated, err := handler.clusterRbacService.CheckAuthorization(cluster.ClusterName, cluster.Id, token, userId, true)
	if err != nil {
		handler.logger.Errorw("error in checking rbac for cluster", "err", err, "clusterId", cluster.Id)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	if !authenticated {
		common.WriteJsonResp(w, errors.New("unauthorized"), nil, http.StatusForbidden)
		return
	}

	res, err := handler.clusterCategoryService.GetCategoryByClusterId(clusterIdInt)
	if err != nil {
		handler.logger.Errorw("service err, clusterCategory", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, res, http.StatusOK)
}

func (handler ClusterRestHandlerImpl) AddUpdateCategories(w http.ResponseWriter, r *http.Request) {
	//RBAC block starts from here
	token := r.Header.Get("token")
	if ok := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionCreate, "*"); !ok {
		common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
		return
	}
	//RBAC block ends here

	decoder := json.NewDecoder(r.Body)
	userId, err := handler.userService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	var bean bean2.CategoriesDto
	err = decoder.Decode(&bean)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	bean.UserId = userId
	handler.logger.Infow("request payload", "payload", bean)
	err = handler.validator.Struct(bean)
	if err != nil {
		handler.logger.Errorw("validation err", "err", err, "payload", bean)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	uniqueCaseInsensitive := make(map[string]bool)
	for _, category := range bean.Categories {
		if _, ok := uniqueCaseInsensitive[strings.ToLower(strings.TrimSpace(category.Name))]; !ok {
			uniqueCaseInsensitive[strings.ToLower(strings.TrimSpace(category.Name))] = true
		} else {
			common.WriteJsonResp(w, fmt.Errorf("duplicate category found in request"), nil, http.StatusBadRequest)
			return
		}
	}

	res, err := handler.clusterCategoryService.AddUpdateCategories(&bean)
	if err != nil {
		handler.logger.Errorw("service err", "err", err, "payload", bean)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, res, http.StatusOK)
}
