/*
 * Copyright (c) 2020-2024. Devtron Inc.
 */

package cluster

import (
	"github.com/gorilla/mux"
)

type ClusterRouter interface {
	InitClusterRouter(clusterRouter *mux.Router)
}

type ClusterRouterImpl struct {
	clusterRestHandler ClusterRestHandler
}

func NewClusterRouterImpl(handler ClusterRestHandler) *ClusterRouterImpl {
	return &ClusterRouterImpl{
		clusterRestHandler: handler,
	}
}

func (impl ClusterRouterImpl) InitClusterRouter(clusterRouter *mux.Router) {
	clusterRouter.Path("").
		Methods("POST").
		HandlerFunc(impl.clusterRestHandler.Save)

	clusterRouter.Path("/virtual").
		Methods("POST").
		HandlerFunc(impl.clusterRestHandler.SaveVirtualCluster)

	clusterRouter.Path("/saveClusters").
		Methods("POST").
		HandlerFunc(impl.clusterRestHandler.SaveClusters)

	clusterRouter.Path("/validate").
		Methods("POST").
		HandlerFunc(impl.clusterRestHandler.ValidateKubeconfig)

	clusterRouter.Path("").
		Methods("GET").
		Queries("id", "{id}").
		HandlerFunc(impl.clusterRestHandler.FindById)

	clusterRouter.Path("/description").
		Methods("GET").
		Queries("id", "{id}").
		HandlerFunc(impl.clusterRestHandler.FindNoteByClusterId)

	clusterRouter.Path("").
		Methods("GET").
		Queries("clusterId", "{clusterId}").
		HandlerFunc(impl.clusterRestHandler.FindByIds)

	clusterRouter.Path("").
		Methods("GET").
		HandlerFunc(impl.clusterRestHandler.FindAll)

	clusterRouter.Path("").
		Methods("PUT").
		HandlerFunc(impl.clusterRestHandler.Update)

	clusterRouter.Path("/note").
		Methods("PUT").
		HandlerFunc(impl.clusterRestHandler.UpdateClusterNote)

	clusterRouter.Path("/virtual").
		Methods("PUT").
		HandlerFunc(impl.clusterRestHandler.UpdateVirtualCluster)

	clusterRouter.Path("/autocomplete").
		Methods("GET").
		HandlerFunc(impl.clusterRestHandler.FindAllForAutoComplete)

	clusterRouter.Path("/namespaces/{clusterId}").
		Methods("GET").
		HandlerFunc(impl.clusterRestHandler.GetClusterNamespaces)

	clusterRouter.Path("/namespaces/{clusterId}/v2").
		Methods("GET").
		HandlerFunc(impl.clusterRestHandler.GetClusterNamespacesMetadata)

	clusterRouter.Path("/namespaces").
		Methods("GET").
		HandlerFunc(impl.clusterRestHandler.GetAllClusterNamespaces)

	clusterRouter.Path("").
		Methods("DELETE").
		HandlerFunc(impl.clusterRestHandler.DeleteCluster)

	clusterRouter.Path("/virtual").
		Methods("DELETE").
		HandlerFunc(impl.clusterRestHandler.DeleteVirtualCluster)

	clusterRouter.Path("/auth-list").
		Methods("GET").
		HandlerFunc(impl.clusterRestHandler.FindAllForClusterPermission)

	clusterRouter.Path("/description").
		Methods("PUT").
		HandlerFunc(impl.clusterRestHandler.UpdateClusterDescription)

	// New routes for managing panels
	clusterRouter.Path("/{cluster_id}/panels").
		Methods("GET").
		HandlerFunc(impl.clusterRestHandler.ListPanels)

	clusterRouter.Path("/{cluster_id}/panel").
		Methods("POST").
		HandlerFunc(impl.clusterRestHandler.CreatePanel)

	clusterRouter.Path("/{cluster_id}/panel/{panel_id}").
		Methods("GET").
		HandlerFunc(impl.clusterRestHandler.GetPanel)

	clusterRouter.Path("/{cluster_id}/panel/{panel_id}").
		Methods("PUT").
		HandlerFunc(impl.clusterRestHandler.UpdatePanel)

	clusterRouter.Path("/{cluster_id}/panel/{panel_id}").
		Methods("DELETE").
		HandlerFunc(impl.clusterRestHandler.DeletePanel)

	clusterRouter.Path("/panel-template").
		HandlerFunc(impl.clusterRestHandler.AddPanelTemplateData).Methods("PUT")

	clusterRouter.Path("/panel-template").Queries("key", "{key}").
		HandlerFunc(impl.clusterRestHandler.GetPanelTemplateData).Methods("GET")

	clusterRouter.Path("/categories").
		HandlerFunc(impl.clusterRestHandler.GetCategories).Methods("GET")
	clusterRouter.Path("/{cluster_id}/categories").
		HandlerFunc(impl.clusterRestHandler.GetCategoryByClusterId).Methods("GET")
	clusterRouter.Path("/categories").HandlerFunc(impl.clusterRestHandler.AddUpdateCategories).Methods("POST")

}
