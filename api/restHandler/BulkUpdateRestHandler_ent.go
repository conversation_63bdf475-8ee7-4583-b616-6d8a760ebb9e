/*
 * Copyright (c) 2024. Devtron Inc.
 */

package restHandler

import (
	"encoding/json"
	"fmt"
	"github.com/devtron-labs/devtron/api/restHandler/common"
	"github.com/devtron-labs/devtron/internal/sql/repository/helper"
	"github.com/devtron-labs/devtron/pkg/auth/authorisation/casbin"
	util2 "github.com/devtron-labs/devtron/pkg/auth/user/util"
	api "github.com/devtron-labs/devtron/pkg/bulkAction/v1beta2/bulkEdit/bean"
	"github.com/devtron-labs/devtron/pkg/bulkAction/v1beta2/bulkEdit/util"
	ctxUtil "github.com/devtron-labs/devtron/util"
	"github.com/gorilla/mux"
	"net/http"
)

type BulkEditV1Beta2RestHandlerEnt interface {
	GetBulkEditConfigV1Beta2(w http.ResponseWriter, r *http.Request)
	DryRunBulkEditV1Beta2(w http.ResponseWriter, r *http.Request)
	BulkEditV1Beta2(w http.ResponseWriter, r *http.Request)
}

func (handler BulkUpdateRestHandlerImpl) BulkHibernateV1(w http.ResponseWriter, r *http.Request) {
	request, err := handler.decodeAndValidateBulkRequest(w, r)
	if err != nil {
		return // response already written by the helper on error.
	}
	token := r.Header.Get("token")
	isSuperAdmin := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionCreate, "*")
	userMetadata := util2.GetUserMetadata(r.Context(), request.UserId, isSuperAdmin)
	response, err := handler.bulkUpdateService.BulkHibernateV1(r.Context(), request, handler.checkAuthForBulkHibernateAndUnhibernate, userMetadata)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, response, http.StatusOK)
}

// GetBulkEditConfigV1Beta2 retrieves the configuration for bulk edit operations.
func (handler BulkUpdateRestHandlerImpl) GetBulkEditConfigV1Beta2(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	apiVersion := vars["apiVersion"]
	kind := vars["kind"]
	response, err := handler.bulkUpdateService.GetBulkEditConfigV1Beta2(apiVersion, kind)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	// No RBAC check is needed here as this is a read-only operation.
	common.WriteJsonResp(w, nil, response, http.StatusOK)
}

// DryRunBulkEditV1Beta2 performs a dry run of the bulk edit operation without making any changes.
func (handler BulkUpdateRestHandlerImpl) DryRunBulkEditV1Beta2(w http.ResponseWriter, r *http.Request) {
	// Decode the request body into the BulkEdit struct
	decoder := json.NewDecoder(r.Body)
	var script api.BulkEdit
	err := decoder.Decode(&script)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	// Validate the script using the validator
	err = handler.validator.Struct(&script)
	if err != nil {
		handler.logger.Errorw("validation err, Script", "err", err, "script", script)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	config, err := handler.bulkUpdateService.GetBulkEditConfigV1Beta2(script.GetApiVersion(), script.GetKind())
	if err != nil {
		handler.logger.Errorw("error in getting bulk edit config", "err", err, "request", script)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	// Validate JSON schema for the script
	result, err := util.ValidateSchemaAndObjectData(config.Schema, script)
	if err != nil {
		handler.logger.Errorw("error in validating schema for bulk edit script", "err", err, "request", script, "result", result)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	// Perform a dry run to get the impacted objects
	impactedObjects, _, err := handler.bulkUpdateService.DryRunBulkEditV1Beta2(script.Spec)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	// Validate RBAC for the impacted objects
	token := r.Header.Get("token")
	if !handler.validateBulkEditRBAC(token, impactedObjects, casbin.ActionGet) {
		common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
		return
	}
	common.WriteJsonResp(w, err, impactedObjects, http.StatusOK)
}

// BulkEditV1Beta2 handles the bulk edit operation for applications.
func (handler BulkUpdateRestHandlerImpl) BulkEditV1Beta2(w http.ResponseWriter, r *http.Request) {
	ctx := ctxUtil.NewRequestCtx(r.Context())
	// Decode the request body into the BulkEdit struct
	decoder := json.NewDecoder(r.Body)
	var script api.BulkEdit
	err := decoder.Decode(&script)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	// Validate the script using the validator
	err = handler.validator.Struct(&script)
	if err != nil {
		handler.logger.Errorw("validation err, Script", "err", err, "script", script)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	config, err := handler.bulkUpdateService.GetBulkEditConfigV1Beta2(script.GetApiVersion(), script.GetKind())
	if err != nil {
		handler.logger.Errorw("error in getting bulk edit config", "err", err, "request", script)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	// Validate JSON schema for the script
	result, err := util.ValidateSchemaAndObjectData(config.Schema, script)
	if err != nil {
		handler.logger.Errorw("error in validating schema for bulk edit script", "err", err, "request", script, "result", result)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	// Perform a dry run to get the impacted objects
	impactedObjects, resourceScopes, err := handler.bulkUpdateService.DryRunBulkEditV1Beta2(script.Spec)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	// Validate RBAC for the impacted objects
	token := r.Header.Get("token")
	if !handler.validateBulkEditRBAC(token, impactedObjects, casbin.ActionUpdate) {
		common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
		return
	}
	// Proceed with the bulk edit operation
	isSuperAdmin := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionCreate, "*")
	userMetadata := util2.GetUserMetadata(ctx, ctx.GetUserId(), isSuperAdmin)
	response, err := handler.bulkUpdateService.BulkEditV1Beta2(ctx, script.Spec, resourceScopes, userMetadata)
	if err != nil {
		handler.logger.Errorw("error in bulk edit v1beta2", "err", err, "request", script)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, response, http.StatusOK)
}

func (handler BulkUpdateRestHandlerImpl) validateBulkEditRBAC(token string, impactedObjects *api.ImpactedObjects, action string) bool {
	var rbacObjects, appResourceObjects map[int]string
	var envResourceObjects map[string]string
	if action == casbin.ActionUpdate {
		rbacObjects = handler.enforcerUtil.GetRbacObjectsForAllApps(helper.CustomApp)
	} else if action == casbin.ActionGet {
		appResourceObjects, envResourceObjects = handler.enforcerUtil.GetRbacObjectsForAllAppsAndEnvironments()
	}
	for _, deploymentTemplateImpactedApp := range impactedObjects.DeploymentTemplate {
		if action == casbin.ActionUpdate {
			ok := handler.CheckAuthForBulkUpdate(deploymentTemplateImpactedApp.AppId, deploymentTemplateImpactedApp.EnvId, deploymentTemplateImpactedApp.AppName, rbacObjects, token)
			if !ok {
				return false
			}
		} else if action == casbin.ActionGet {
			ok := handler.CheckAuthForImpactedObjects(deploymentTemplateImpactedApp.AppId, deploymentTemplateImpactedApp.EnvId, appResourceObjects, envResourceObjects, token)
			if !ok {
				return false
			}
		} else {
			handler.logger.Errorw("invalid action for bulk edit", "action", action)
			return false
		}
	}
	for _, impactedConfigMap := range impactedObjects.ConfigMap {
		if action == casbin.ActionUpdate {
			ok := handler.CheckAuthForBulkUpdate(impactedConfigMap.AppId, impactedConfigMap.EnvId, impactedConfigMap.AppName, rbacObjects, token)
			if !ok {
				return false
			}
		} else if action == casbin.ActionGet {
			ok := handler.CheckAuthForImpactedObjects(impactedConfigMap.AppId, impactedConfigMap.EnvId, appResourceObjects, envResourceObjects, token)
			if !ok {
				return false
			}
		} else {
			handler.logger.Errorw("invalid action for bulk edit", "action", action)
			return false
		}
	}
	for _, impactedSecret := range impactedObjects.Secret {
		if action == casbin.ActionUpdate {
			ok := handler.CheckAuthForBulkUpdate(impactedSecret.AppId, impactedSecret.EnvId, impactedSecret.AppName, rbacObjects, token)
			if !ok {
				return false
			}
		} else if action == casbin.ActionGet {
			ok := handler.CheckAuthForImpactedObjects(impactedSecret.AppId, impactedSecret.EnvId, appResourceObjects, envResourceObjects, token)
			if !ok {
				return false
			}
		} else {
			handler.logger.Errorw("invalid action for bulk edit", "action", action)
			return false
		}
	}
	return true
}
