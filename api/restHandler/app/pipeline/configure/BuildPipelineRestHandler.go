/*
 * Copyright (c) 2024. Devtron Inc.
 */

package configure

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	apiBean "github.com/devtron-labs/devtron/api/restHandler/app/pipeline/configure/bean"
	constants2 "github.com/devtron-labs/devtron/internal/sql/constants"
	bean3 "github.com/devtron-labs/devtron/pkg/appWorkflow/bean"
	"github.com/devtron-labs/devtron/pkg/bean/common/pagination"
	"github.com/devtron-labs/devtron/pkg/build/artifacts/imageTagging"
	bean2 "github.com/devtron-labs/devtron/pkg/build/pipeline/bean"
	common2 "github.com/devtron-labs/devtron/pkg/build/pipeline/bean/common"
	commonUtil "github.com/devtron-labs/devtron/pkg/devtronResource/util"
	eventProcessorBean "github.com/devtron-labs/devtron/pkg/eventProcessor/bean"
	"github.com/devtron-labs/devtron/pkg/pipeline/bulkSwitchCi/beans"
	errors2 "github.com/devtron-labs/devtron/pkg/pipeline/bulkSwitchCi/errors"
	"github.com/devtron-labs/devtron/pkg/pipeline/constants"
	helper2 "github.com/devtron-labs/devtron/pkg/pipeline/helper"
	"github.com/devtron-labs/devtron/pkg/pipeline/runtimeParam"
	"github.com/devtron-labs/devtron/pkg/resourceGroup"
	"github.com/devtron-labs/devtron/pkg/variables/parsers"
	"github.com/devtron-labs/devtron/util/beHelper"
	"github.com/devtron-labs/devtron/util/stringsUtil"
	"golang.org/x/exp/maps"
	validator2 "gopkg.in/go-playground/validator.v9"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"

	"github.com/gorilla/schema"

	"github.com/devtron-labs/devtron/api/restHandler/common"
	"github.com/devtron-labs/devtron/client/gitSensor"
	"github.com/devtron-labs/devtron/internal/sql/repository"
	dockerRegistryRepository "github.com/devtron-labs/devtron/internal/sql/repository/dockerRegistry"
	"github.com/devtron-labs/devtron/internal/sql/repository/helper"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/internal/util"
	"github.com/devtron-labs/devtron/pkg/auth/authorisation/casbin"
	"github.com/devtron-labs/devtron/pkg/bean"
	"github.com/devtron-labs/devtron/pkg/pipeline"
	pipelineConfigBean "github.com/devtron-labs/devtron/pkg/pipeline/bean"
	"github.com/devtron-labs/devtron/pkg/pipeline/types"
	util2 "github.com/devtron-labs/devtron/util"
	"github.com/devtron-labs/devtron/util/response"
	"github.com/go-pg/pg"
	"github.com/gorilla/mux"
	"go.opentelemetry.io/otel"
)

type DevtronAppBuildRestHandler interface {
	CreateCiConfig(w http.ResponseWriter, r *http.Request)
	UpdateCiTemplate(w http.ResponseWriter, r *http.Request)

	GetCiPipeline(w http.ResponseWriter, r *http.Request)
	GetExternalCi(w http.ResponseWriter, r *http.Request)
	GetExternalCiById(w http.ResponseWriter, r *http.Request)
	PatchCiPipelines(w http.ResponseWriter, r *http.Request)
	SwitchCiPipelines(w http.ResponseWriter, r *http.Request)
	PatchCiPipelineBulk(w http.ResponseWriter, r *http.Request)
	PatchCiMaterialSourceWithAppIdAndEnvironmentId(w http.ResponseWriter, r *http.Request)
	PatchCiMaterialSourceWithAppIdsAndEnvironmentId(w http.ResponseWriter, r *http.Request)
	TriggerCiPipeline(w http.ResponseWriter, r *http.Request)
	CiStageFileUpload(w http.ResponseWriter, r *http.Request)
	GetCiPipelineMin(w http.ResponseWriter, r *http.Request)
	GetCIPipelineById(w http.ResponseWriter, r *http.Request)
	GetCIPipelineByPipelineId(w http.ResponseWriter, r *http.Request)
	HandleWorkflowWebhook(w http.ResponseWriter, r *http.Request)
	GetBuildLogs(w http.ResponseWriter, r *http.Request)
	FetchWorkflowDetails(w http.ResponseWriter, r *http.Request)
	GetArtifactsForCiJob(w http.ResponseWriter, r *http.Request)
	// CancelWorkflow CancelBuild
	CancelWorkflow(w http.ResponseWriter, r *http.Request)

	UpdateBranchCiPipelinesWithRegex(w http.ResponseWriter, r *http.Request)
	GetAppMetadataListByEnvironment(w http.ResponseWriter, r *http.Request)
	GetCiPipelineByEnvironment(w http.ResponseWriter, r *http.Request)
	GetCiPipelineByEnvironmentMin(w http.ResponseWriter, r *http.Request)
	GetExternalCiByEnvironment(w http.ResponseWriter, r *http.Request)
	// GetSourceCiDownStreamFilters will fetch the environments attached to all the linked CIs for the given ciPipelineId
	GetSourceCiDownStreamFilters(w http.ResponseWriter, r *http.Request)
	// GetSourceCiDownStreamInfo will fetch the deployment information of all the linked CIs for the given ciPipelineId
	GetSourceCiDownStreamInfo(w http.ResponseWriter, r *http.Request)

	GetCIRuntimeParams(w http.ResponseWriter, r *http.Request)
	GetCiPvcSelectorCache(w http.ResponseWriter, r *http.Request)
	FetchCiPipelinesByStepDetails(w http.ResponseWriter, r *http.Request)
}

type DevtronAppBuildMaterialRestHandler interface {
	CreateMaterial(w http.ResponseWriter, r *http.Request)
	UpdateMaterial(w http.ResponseWriter, r *http.Request)
	FetchMaterials(w http.ResponseWriter, r *http.Request)
	FetchMaterialsByMaterialId(w http.ResponseWriter, r *http.Request)
	RefreshMaterials(w http.ResponseWriter, r *http.Request)
	FetchMaterialInfo(w http.ResponseWriter, r *http.Request)
	FetchMaterialInfoForArtifact(w http.ResponseWriter, r *http.Request)
	FetchChanges(w http.ResponseWriter, r *http.Request)
	DeleteMaterial(w http.ResponseWriter, r *http.Request)
	GetCommitMetadataForPipelineMaterial(w http.ResponseWriter, r *http.Request)
	ReloadAndSyncAppsGitMaterial(w http.ResponseWriter, r *http.Request)
}

type DevtronAppBuildHistoryRestHandler interface {
	GetHistoricBuildLogs(w http.ResponseWriter, r *http.Request)
	GetBuildHistory(w http.ResponseWriter, r *http.Request)
	DownloadCiWorkflowArtifacts(w http.ResponseWriter, r *http.Request)
}

type ImageTaggingRestHandler interface {
	CreateUpdateImageTagging(w http.ResponseWriter, r *http.Request)
	GetImageTaggingData(w http.ResponseWriter, r *http.Request)
	GetImageTagList(w http.ResponseWriter, r *http.Request)
}

func (handler *PipelineConfigRestHandlerImpl) CreateCiConfig(w http.ResponseWriter, r *http.Request) {
	userId, ok := handler.getUserIdOrUnauthorized(w, r)
	if !ok {
		return
	}
	var createRequest bean.CiConfigRequest
	if !handler.decodeJsonBody(w, r, &createRequest, "create ci config") {
		return
	}
	createRequest.UserId = userId
	handler.Logger.Infow("request payload, create ci config", "create request", createRequest)
	if !handler.validateRequestBody(w, createRequest, "create ci config") {
		return
	}
	// validates if the dockerRegistry can store CONTAINER
	isValid := handler.dockerRegistryConfig.ValidateRegistryStorageType(createRequest.DockerRegistry, dockerRegistryRepository.OCI_REGISRTY_REPO_TYPE_CONTAINER, dockerRegistryRepository.STORAGE_ACTION_TYPE_PUSH, dockerRegistryRepository.STORAGE_ACTION_TYPE_PULL_AND_PUSH)
	if !isValid {
		err := fmt.Errorf("invalid registry type")
		handler.Logger.Errorw("validation err, create ci config", "err", err, "create request", createRequest)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	token := r.Header.Get("token")
	_, authorized := handler.getAppAndCheckAuthForAction(w, createRequest.AppId, token, casbin.ActionCreate)
	if !authorized {
		return
	}
	createResp, err := handler.pipelineBuilder.CreateCiPipeline(&createRequest)
	if err != nil {
		handler.Logger.Errorw("service err, create", "err", err, "create request", createRequest)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, createResp, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) UpdateCiTemplate(w http.ResponseWriter, r *http.Request) {
	userId, ok := handler.getUserIdOrUnauthorized(w, r)
	if !ok {
		return
	}
	var configRequest bean.CiConfigRequest
	if !handler.decodeJsonBody(w, r, &configRequest, "UpdateCiTemplate") {
		return
	}
	configRequest.UserId = userId
	handler.Logger.Infow("request payload, update ci template", "UpdateCiTemplate", configRequest, "userId", userId)
	if !handler.validateRequestBody(w, configRequest, "UpdateCiTemplate") {
		return
	}
	token := r.Header.Get("token")
	_, authorized := handler.getAppAndCheckAuthForAction(w, configRequest.AppId, token, casbin.ActionCreate)
	if !authorized {
		return
	}
	createResp, err := handler.pipelineBuilder.UpdateCiTemplate(&configRequest)
	if err != nil {
		handler.Logger.Errorw("service err, UpdateCiTemplate", "err", err, "UpdateCiTemplate", configRequest)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, createResp, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) UpdateBranchCiPipelinesWithRegex(w http.ResponseWriter, r *http.Request) {
	userId, ok := handler.getUserIdOrUnauthorized(w, r)
	if !ok {
		return
	}
	var patchRequest bean.CiRegexPatchRequest
	if !handler.decodeJsonBody(w, r, &patchRequest, "PatchCiPipelines") {
		return
	}
	patchRequest.UserId = userId

	handler.Logger.Debugw("update request ", "req", patchRequest)
	token := r.Header.Get("token")
	_, authorized := handler.getAppAndCheckAuthForAction(w, patchRequest.AppId, token, casbin.ActionTrigger)
	if !authorized {
		return
	}

	var materialList []*bean.CiPipelineMaterial
	for _, material := range patchRequest.CiPipelineMaterial {
		if handler.ciPipelineMaterialRepository.CheckRegexExistsForMaterial(material.Id) {
			materialList = append(materialList, material)
		}
	}
	if len(materialList) == 0 {
		common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
		return
	}
	patchRequest.CiPipelineMaterial = materialList

	err := handler.pipelineBuilder.PatchRegexCiPipeline(&patchRequest)
	if err != nil {
		handler.Logger.Errorw("service err, PatchCiPipelines", "err", err, "PatchCiPipelines", patchRequest)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	// if include/exclude configured showAll will include excluded materials also in list, if not configured it will ignore this flag
	resp, err := handler.ciHandler.FetchMaterialsByPipelineId(patchRequest.Id, false)
	if err != nil {
		handler.Logger.Errorw("service err, FetchMaterials", "err", err, "pipelineId", patchRequest.Id)
		common.WriteJsonResp(w, err, resp, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, resp, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) parseSourceChangeRequest(w http.ResponseWriter, r *http.Request) (*bean.CiMaterialPatchRequest, int32, error) {
	userId, ok := handler.getUserIdOrUnauthorized(w, r)
	if !ok {
		return nil, 0, fmt.Errorf("unauthorized user")
	}
	var patchRequest bean.CiMaterialPatchRequest
	if !handler.decodeJsonBody(w, r, &patchRequest, "PatchCiPipeline") {
		return nil, 0, fmt.Errorf("failed to decode request body")
	}
	return &patchRequest, userId, nil
}

func (handler *PipelineConfigRestHandlerImpl) parseBulkSourceChangeRequest(w http.ResponseWriter, r *http.Request) (*bean.CiMaterialBulkPatchRequest, int32, error) {
	userId, ok := handler.getUserIdOrUnauthorized(w, r)
	if !ok {
		return nil, 0, fmt.Errorf("unauthorized user")
	}
	var patchRequest bean.CiMaterialBulkPatchRequest
	if !handler.decodeJsonBody(w, r, &patchRequest, "BulkPatchCiPipeline") {
		return nil, 0, fmt.Errorf("failed to decode request body")
	}
	if !handler.validateRequestBody(w, patchRequest, "BulkPatchCiPipeline") {
		return nil, 0, fmt.Errorf("failed to validate request body")
	}
	return &patchRequest, userId, nil
}

func (handler *PipelineConfigRestHandlerImpl) authorizeCiSourceChangeRequest(w http.ResponseWriter, patchRequest *bean.CiMaterialPatchRequest, token string) error {
	handler.Logger.Debugw("update request ", "req", patchRequest)
	app, authorized := handler.getAppAndCheckAuthForAction(w, patchRequest.AppId, token, casbin.ActionUpdate)
	if !authorized {
		return fmt.Errorf("unauthorized user")
	}
	if app.AppType != helper.CustomApp {
		err := fmt.Errorf("only custom apps supported")
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return err
	}
	if !handler.validateRequestBody(w, patchRequest, "authorizeCiSourceChangeRequest") {
		return fmt.Errorf("validation failed")
	}
	return nil
}

func (handler *PipelineConfigRestHandlerImpl) PatchCiMaterialSourceWithAppIdAndEnvironmentId(w http.ResponseWriter, r *http.Request) {
	patchRequest, userId, err := handler.parseSourceChangeRequest(w, r)
	if err != nil {
		handler.Logger.Errorw("Parse error, PatchCiMaterialSource", "err", err, "PatchCiMaterialSource", patchRequest)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	if !(patchRequest.Source.Type == constants2.SOURCE_TYPE_BRANCH_FIXED || patchRequest.Source.Type == constants2.SOURCE_TYPE_BRANCH_REGEX) {
		handler.Logger.Errorw("Unsupported source type, PatchCiMaterialSource", "err", err, "PatchCiMaterialSource", patchRequest)
		common.WriteJsonResp(w, err, "source.type not supported", http.StatusBadRequest)
		return
	}
	token := r.Header.Get("token")
	if err = handler.authorizeCiSourceChangeRequest(w, patchRequest, token); err != nil {
		handler.Logger.Errorw("Authorization error, PatchCiMaterialSource", "err", err, "PatchCiMaterialSource", patchRequest)
		common.WriteJsonResp(w, err, nil, http.StatusUnauthorized)
		return
	}

	createResp, err := handler.pipelineBuilder.PatchCiMaterialSource(patchRequest, userId)
	if err != nil {
		handler.Logger.Errorw("service err, PatchCiPipelines", "err", err, "PatchCiPipelines", patchRequest)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, createResp, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) PatchCiMaterialSourceWithAppIdsAndEnvironmentId(w http.ResponseWriter, r *http.Request) {
	bulkPatchRequest, userId, err := handler.parseBulkSourceChangeRequest(w, r)
	if err != nil {
		handler.Logger.Errorw("Parse error, PatchCiMaterialSource", "err", err, "PatchCiMaterialSource", bulkPatchRequest)
		return
	}
	token := r.Header.Get("token")
	// Here passing the checkAppSpecificAccess func to check RBAC
	bulkPatchResponse, err := handler.pipelineBuilder.BulkPatchCiMaterialSource(bulkPatchRequest, userId, token, handler.checkAppSpecificAccess)
	if err != nil {
		handler.Logger.Errorw("service err, BulkPatchCiPipelines", "err", err, "BulkPatchCiPipelines", bulkPatchRequest)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, bulkPatchResponse, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) PatchCiPipelines(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	userId, ok := handler.getUserIdOrUnauthorized(w, r)
	if !ok {
		return
	}
	var patchRequest bean.CiPatchRequest
	if !handler.decodeJsonBody(w, r, &patchRequest, "PatchCiPipelines") {
		return
	}
	patchRequest.UserId = userId
	handler.Logger.Infow("request payload, PatchCiPipelines", "PatchCiPipelines", patchRequest)
	if !handler.validateRequestBody(w, patchRequest, "PatchCiPipelines") {
		return
	}
	handler.Logger.Debugw("update request ", "req", patchRequest)
	token := r.Header.Get("token")
	app, err := handler.pipelineBuilder.GetApp(patchRequest.AppId)
	if err != nil {
		handler.Logger.Errorw("service err, GetApp", "err", err, "appId", patchRequest.AppId)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	if app.AppType == helper.CustomApp {
		resourceName := handler.enforcerUtil.GetAppRBACName(app.AppName)
		if ok := handler.enforcer.Enforce(token, casbin.ResourceApplications, casbin.ActionGet, resourceName); !ok {
			common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
			return
		}
	}

	appWorkflowName := ""
	if patchRequest.AppWorkflowId != 0 {
		appWorkflow, err := handler.appWorkflowService.FindAppWorkflowById(patchRequest.AppWorkflowId, app.Id)
		if err != nil {
			handler.Logger.Errorw("error in getting app workflow", "err", err, "workflowId", patchRequest.AppWorkflowId, "appId", app.Id)
			common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
			return
		}
		appWorkflowName = appWorkflow.Name
	}
	resourceName := handler.enforcerUtil.GetAppRBACName(app.AppName)
	workflowResourceName := handler.enforcerUtil.GetRbacObjectNameByAppAndWorkflow(app.AppName, appWorkflowName)
	ok = true
	if app.AppType == helper.Job {
		ok = handler.enforcer.Enforce(token, casbin.ResourceJobs, casbin.ActionCreate, resourceName) && handler.enforcer.Enforce(token, casbin.ResourceWorkflow, casbin.ActionCreate, workflowResourceName)
	} else {

		// get the pipelines of the current workflow

		cdPipelines, err := handler.getCdPipelinesForCIPatchRbac(&patchRequest)
		if err != nil && err != pg.ErrNoRows {
			handler.Logger.Errorw("error in finding ccd cdPipelines by ciPipelineId", "err", err)
			common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
			return
		}
		haveCiPatchAccess := handler.checkCiPatchAccess(token, resourceName, cdPipelines)
		if patchRequest.IsLinkedCdRequest() || patchRequest.IsSwitchCiPipelineRequest() {
			ok = haveCiPatchAccess
		} else if !haveCiPatchAccess {
			// user doesn't have app level access, then checking for branch change
			hasBranchChangeAccess := handler.checkIfHasBranchChangeAccess(token, app.AppName, cdPipelines)
			// user does not have app create permission but has branch change permission and request.action is update_pipeline
			// downgrading the request to update source only
			if patchRequest.Action == bean.UPDATE_PIPELINE && hasBranchChangeAccess {
				patchRequest.Action = bean.UPDATE_SOURCE
			} else {
				ok = false
			}
		}
	}
	if !ok {
		common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
		return
	}

	var cdPipeline *bean.CDPipelineConfigObject
	if patchRequest.IsLinkedCdRequest() {
		cdPipeline, err = handler.validateAndEnrichForLinkedCDRequest(w, &patchRequest, app, token)
		if err != nil {
			return
		}
	}

	ciConf, err := handler.pipelineBuilder.GetCiPipeline(patchRequest.AppId)

	var emptyDockerRegistry string
	if app.AppType == helper.Job && ciConf == nil {
		ciConfigRequest := bean.CiConfigRequest{}
		ciConfigRequest.DockerRegistry = emptyDockerRegistry
		ciConfigRequest.AppId = patchRequest.AppId
		ciConfigRequest.CiBuildConfig = &bean2.CiBuildConfigBean{}
		ciConfigRequest.CiBuildConfig.CiBuildType = bean2.SKIP_BUILD_TYPE
		ciConfigRequest.UserId = patchRequest.UserId
		if patchRequest.CiPipeline == nil || patchRequest.CiPipeline.CiMaterial == nil {
			handler.Logger.Errorw("Invalid patch ci-pipeline request", "request", patchRequest, "err", "invalid CiPipeline data")
			common.WriteJsonResp(w, fmt.Errorf("invalid CiPipeline data"), nil, http.StatusBadRequest)
			return
		}
		ciConfigRequest.CiBuildConfig.GitMaterialId = patchRequest.CiPipeline.CiMaterial[0].GitMaterialId
		ciConfigRequest.IsJob = true
		_, err = handler.pipelineBuilder.CreateCiPipeline(&ciConfigRequest)
		if err != nil {
			handler.Logger.Errorw("error occurred in creating ci-pipeline for the Job", "payload", ciConfigRequest, "err", err)
			common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
			return
		}
	}
	if app.AppType == helper.Job {
		patchRequest.IsJob = true
	}
	createResp, err := handler.pipelineBuilder.PatchCiPipeline(&patchRequest)
	if err != nil {
		if err.Error() == bean2.PIPELINE_NAME_ALREADY_EXISTS_ERROR {
			handler.Logger.Errorw("service err, pipeline name already exist ", "err", err)
			common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
			return
		}
		handler.Logger.Errorw("service err, PatchCiPipelines", "err", err, "PatchCiPipelines", patchRequest)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	if createResp != nil && app != nil {
		createResp.AppName = app.AppName
	}

	if patchRequest.DeployEnvId > 0 {
		err := handler.createCDPipeline(w, app, createResp, patchRequest, cdPipeline, ctx)
		if err != nil {
			return
		}
	}
	common.WriteJsonResp(w, err, createResp, http.StatusOK)
}

func validateBulkCiChangeRequest(destinationPipelineData *beans.DestinationPipelineData, validator *validator2.Validate) error {
	if destinationPipelineData.CustomConfigurations == nil {
		return errors.New("invalid payload, customConfiguration is empty")
	} else {
		if destinationPipelineData.PipelineType == common2.CI_BUILD {
			if destinationPipelineData.CustomConfigurations.CiBuildCustomConfiguration == nil {
				return errors.New("ci build configuration is not provided")
			} else {
				err := validator.Struct(destinationPipelineData.CustomConfigurations.CiMaterialSource)
				if err != nil {
					return err
				}
			}
		} else if destinationPipelineData.PipelineType == common2.LINKED_CD {
			if destinationPipelineData.CustomConfigurations.LinkedCdCustomConfiguration == nil {
				return errors.New("linked cd configuration is not provided")
			} else if destinationPipelineData.CustomConfigurations.LinkedCdEnvironment == "" {
				return errors.New("parent environment is not provided for linked-cd")
			}
		}
	}
	return nil
}

func (handler *PipelineConfigRestHandlerImpl) SwitchCiPipelines(w http.ResponseWriter, r *http.Request) {
	_, ok := handler.getUserIdOrUnauthorized(w, r)
	if !ok {
		return
	}

	ctx := util2.NewRequestCtx(r.Context())
	var switchRequest beans.BulkSwitchCiReq
	if !handler.decodeJsonBody(w, r, &switchRequest, "SwitchCiPipelines") {
		return
	}

	dryRun := r.URL.Query().Get("dryRun") == "true"
	if !dryRun {
		if err := validateBulkCiChangeRequest(switchRequest.DestinationPipelineData, handler.validator); err != nil {
			common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
			return
		}
	}

	requestSourcesWithCiPatchAccessRights, requestSourcesWithOutCiPatchAccessRights, err := handler.filterSourcesWithCiPatchAccessRights(ctx, switchRequest.SourcePipelineData)
	if err != nil {
		handler.Logger.Errorw("error in filtering user accessible sources", "sourcesData", switchRequest.SourcePipelineData, "error", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	bulkCiChangeResponse := make([]*beans.CiPipelineConversionResponseItem, 0, len(switchRequest.SourcePipelineData))

	if len(requestSourcesWithCiPatchAccessRights) > 0 {
		// re assign the valid requests to the request source pipeline data
		switchRequest.SourcePipelineData = requestSourcesWithCiPatchAccessRights
		bulkCiChangeResponse, err = handler.bulkSwitchCIService.ChangeCiInBulk(ctx, &switchRequest, dryRun)
		if err != nil {
			handler.Logger.Errorw("error in converting ci in bulk", "error", err)
			common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
			return
		}
	}

	if len(requestSourcesWithOutCiPatchAccessRights) > 0 {
		for _, invalidRequestSource := range requestSourcesWithOutCiPatchAccessRights {
			bulkCiChangeResponse = append(bulkCiChangeResponse, &beans.CiPipelineConversionResponseItem{
				SourcePipelineId:   invalidRequestSource.PipelineId,
				SourcePipelineType: invalidRequestSource.PipelineType,
				Status:             beans.FAIL,
				StatusDetails:      errors2.ErrNoAccessToSourcePipeline.String(),
			})
		}
	}

	common.WriteJsonResp(w, nil, bulkCiChangeResponse, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) filterSourcesWithCiPatchAccessRights(ctx *util2.RequestCtx, requests []*beans.SwitchPipelineSourceData) (validRequests []*beans.SwitchPipelineSourceData, invalidRequests []*beans.SwitchPipelineSourceData, err error) {

	interestedSourceWorkflowMappings, err := handler.bulkSwitchCIService.FetchWorkflowMappingsForSourceCiNodes(requests)
	if err != nil {
		handler.Logger.Errorw("error in fetching workflow mappings for source ci nodes", "error", err)
		return nil, nil, err
	}

	cdPipelineWFMappings, err := handler.appWorkflowService.FindWFCDMappingsByWorkflowIds(maps.Keys(interestedSourceWorkflowMappings))
	if err != nil {
		handler.Logger.Errorw("error in finding the appWorkflowMappings of cd pipeline for an appWorkflow", "appWorkflowIds", maps.Keys(interestedSourceWorkflowMappings), "err", err)
		return nil, nil, err
	}

	// if no cd pipelines found for the given sources, these are in-valid source pipelines
	if len(cdPipelineWFMappings) == 0 {
		return nil, requests, util.DefaultApiError().WithUserMessage("no selected ci source belong to the current resource group").WithHttpStatusCode(http.StatusBadRequest)
	}

	pipelinesGroupedByAppWorkflow := make(map[int]map[int]bool)
	cdPipelineIds := make([]int, 0, len(cdPipelineWFMappings))
	for _, cdWfMapping := range cdPipelineWFMappings {
		if _, ok := pipelinesGroupedByAppWorkflow[cdWfMapping.AppWorkflowId]; !ok {
			pipelinesGroupedByAppWorkflow[cdWfMapping.AppWorkflowId] = make(map[int]bool)
		}
		pipelinesGroupedByAppWorkflow[cdWfMapping.AppWorkflowId][cdWfMapping.ComponentId] = true
		cdPipelineIds = append(cdPipelineIds, cdWfMapping.ComponentId)
	}

	authorisedCdPipelines := handler.getTriggerAccessCdPipelines(ctx.GetToken(), cdPipelineIds)
	workflowsWithCiChangeAccessRights := make(map[int]bool)
	for appWorkflowId, cdPipelines := range pipelinesGroupedByAppWorkflow {
		workflowsWithCiChangeAccessRights[appWorkflowId] = checkCiPatchAccess(maps.Keys(cdPipelines), authorisedCdPipelines)
	}

	for _, request := range requests {
		found := false
		for appWorkflowId, workflow := range interestedSourceWorkflowMappings {
			if (request.PipelineId == workflow.ComponentId && workflow.Type == beans.ComponentType(request.PipelineType)) && workflowsWithCiChangeAccessRights[appWorkflowId] {
				validRequests = append(validRequests, request)
				found = true
			}
		}

		if !found {
			invalidRequests = append(invalidRequests, request)
		}
	}

	return validRequests, invalidRequests, nil
}

// checkCiPatchAccess will check , if the user has access to patch a cipipeline.
// given the cdPipelinesThoseBelongToCiDAG and cdPipelinesWithTriggerAccess map.
func checkCiPatchAccess(cdPipelinesThoseBelongToCiDAG []int, cdPipelinesWithTriggerAccess map[int]bool) bool {
	for _, cdPipelineId := range cdPipelinesThoseBelongToCiDAG {
		if cdPipelinesWithTriggerAccess[cdPipelineId] {
			return true
		}
	}
	return false
}

// getTriggerAccessCdPipelines returns the cd pipelines which the user has access to cd trigger
func (handler *PipelineConfigRestHandlerImpl) getTriggerAccessCdPipelines(token string, cdPipelineIds []int) (authorisedCdPipelines map[int]bool) {
	authorisedCdPipelines = make(map[int]bool)
	pipelineVsAppAndEnvRbacObjects := handler.enforcerUtil.GetAppAndEnvObjectByPipelineIds(cdPipelineIds)
	appRbacObjects := make([]string, 0, len(pipelineVsAppAndEnvRbacObjects))
	envRbacObjects := make([]string, 0, len(pipelineVsAppAndEnvRbacObjects))
	for _, appAndEnvRbacObjects := range pipelineVsAppAndEnvRbacObjects {
		appRbacObjects = append(appRbacObjects, appAndEnvRbacObjects[0])
		envRbacObjects = append(envRbacObjects, appAndEnvRbacObjects[1])
	}

	accessibleAppObejctsMap, accessibleEnvObejctsMap := handler.checkCdTriggerAuthBatch(token, appRbacObjects, envRbacObjects)
	for _, cdPipelineId := range cdPipelineIds {
		if _, ok := pipelineVsAppAndEnvRbacObjects[cdPipelineId]; !ok {
			handler.Logger.Warnw("skipping pipeline as no object found for it", "pipelineId", cdPipelineId)
			continue
		}
		appRbacObject := pipelineVsAppAndEnvRbacObjects[cdPipelineId][0]
		envRbacObject := pipelineVsAppAndEnvRbacObjects[cdPipelineId][1]
		if accessibleAppObejctsMap[appRbacObject] && accessibleEnvObejctsMap[envRbacObject] {
			authorisedCdPipelines[cdPipelineId] = true
		}
	}

	return
}

func (handler *PipelineConfigRestHandlerImpl) PatchCiPipelineBulk(w http.ResponseWriter, r *http.Request) {
	ctx := util2.NewRequestCtx(r.Context())
	decoder := json.NewDecoder(r.Body)
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if err != nil || userId == 0 {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}

	//RBAC
	token := r.Header.Get("token")
	if isSuperAdmin := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionUpdate, "*"); !isSuperAdmin {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusForbidden)
		return
	}
	//RBAC ends
	var patchRequest bean.PatchCiPipelineRequest
	err = decoder.Decode(&patchRequest)
	if err != nil {
		handler.Logger.Errorw("request err, PatchCiPipelines bulk", "PatchCiPipelines", patchRequest, "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	patchRequest.UserId = userId
	handler.Logger.Infow("request payload, PatchCiPipelines bulk", "PatchCiPipelines", patchRequest)
	validationErr := helper2.ValidatePatchCiPipelineBulkRequest(&patchRequest)
	if validationErr != nil {
		common.WriteJsonResp(w, validationErr, nil, http.StatusBadRequest)
		return
	}

	resp, err := handler.pipelineBuilder.PatchCiPipelineBulk(ctx, &patchRequest)
	if err != nil {
		handler.Logger.Errorw("service err, PatchCiPipelines", "PatchCiPipelines", patchRequest, "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, resp, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) FetchCiPipelinesByStepDetails(w http.ResponseWriter, r *http.Request) {
	ctx := util2.NewRequestCtx(r.Context())
	decoder := json.NewDecoder(r.Body)
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if err != nil || userId == 0 {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}

	//RBAC
	token := r.Header.Get("token")
	if isSuperAdmin := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionGet, "*"); !isSuperAdmin {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusForbidden)
		return
	}
	//RBAC ends

	var getCiPipelineReq bean.GetCiPipelinesByStepDetailsRequest
	err = decoder.Decode(&getCiPipelineReq)
	if err != nil {
		handler.Logger.Errorw("request err, PatchCiPipelines bulk", "payload", getCiPipelineReq, "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	getCiPipelineReq.UserId = userId
	handler.Logger.Infow("request payload, PatchCiPipelines bulk", "payload", getCiPipelineReq)

	validationErr := helper2.ValidateFetchCiPipelinesByStepDetails(&getCiPipelineReq)
	if validationErr != nil {
		common.WriteJsonResp(w, validationErr, nil, http.StatusBadRequest)
		return
	}

	resp, err := handler.pipelineBuilder.FetchCiPipelinesByStepDetails(ctx, &getCiPipelineReq)
	if err != nil {
		handler.Logger.Errorw("service err, PatchCiPipelines", "payload", getCiPipelineReq, "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, resp, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) createCDPipeline(w http.ResponseWriter, app *bean.CreateAppDTO, createResp *bean.CiConfigRequest, patchRequest bean.CiPatchRequest, cdPipeline *bean.CDPipelineConfigObject, ctx context.Context) error {

	env, err := handler.envService.FindById(patchRequest.DeployEnvId)
	if err != nil {
		handler.Logger.Errorw("error in getting env", "err", err, "envId", patchRequest.DeployEnvId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return err
	}
	triggerType := pipelineConfig.TRIGGER_TYPE_AUTOMATIC
	if env.IsVirtualEnvironment {
		triggerType = pipelineConfig.TRIGGER_TYPE_MANUAL
	}
	// RBAC
	suggestedName := beHelper.GetCDPipelineName(app.Id)
	newCdPipeline := &bean.CdPipelines{
		Pipelines: []*bean.CDPipelineConfigObject{{
			Name:               suggestedName,
			AppWorkflowId:      createResp.AppWorkflowId,
			CiPipelineId:       createResp.CiPipelines[0].Id,
			ParentPipelineType: bean3.CI_PIPELINE_TYPE,
			ParentPipelineId:   createResp.CiPipelines[0].Id,
			TriggerType:        triggerType,
			EnvironmentId:      patchRequest.DeployEnvId,
			Strategies:         cdPipeline.Strategies,
		}},
		AppId:  createResp.AppId,
		UserId: createResp.UserId,
	}

	_, err = handler.pipelineBuilder.CreateCdPipelines(ctx, newCdPipeline)
	if err != nil {
		handler.Logger.Errorw("service err, CreateCdPipelines", "err", err, "CreateCdPipelines", patchRequest)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return err
	}
	return nil
}

func (handler *PipelineConfigRestHandlerImpl) validateAndEnrichForLinkedCDRequest(w http.ResponseWriter, patchRequest *bean.CiPatchRequest, app *bean.CreateAppDTO, token string) (*bean.CDPipelineConfigObject, error) {
	cdPipeline, err := handler.pipelineBuilder.GetCdPipelineById(patchRequest.ParentCDPipeline)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return nil, err
	}

	var parentCi *bean.CiPipeline
	if cdPipeline.CiPipelineId != 0 {
		parentCi, err = handler.pipelineBuilder.GetCiPipelineById(cdPipeline.CiPipelineId)
		if err != nil {
			common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
			return nil, err
		}

		if parentCi.PipelineType == common2.LINKED_CD {
			common.WriteJsonResp(w, fmt.Errorf("invalid operation"), "cannot use linked cd as source for workflow", http.StatusBadRequest)
			return nil, fmt.Errorf("invalid operation")
		}
	}

	object := handler.enforcerUtil.GetAppRBACByAppNameAndEnvId(app.AppName, cdPipeline.EnvironmentId)
	if ok := handler.enforcer.Enforce(token, casbin.ResourceEnvironment, casbin.ActionGet, object); !ok {
		common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
		return nil, fmt.Errorf("unauthorized user")
	}

	if patchRequest.DeployEnvId > 0 {
		object := handler.enforcerUtil.GetAppRBACByAppNameAndEnvId(app.AppName, patchRequest.DeployEnvId)
		if ok := handler.enforcer.Enforce(token, casbin.ResourceEnvironment, casbin.ActionCreate, object); !ok {
			common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
			return nil, fmt.Errorf("unauthorized user")
		}
	}

	var ciMaterial []*bean.CiMaterial
	if parentCi != nil {
		ciMaterial = parentCi.CiMaterial
	}
	patchRequest.CiPipeline = &bean.CiPipeline{
		ParentCiPipeline: cdPipeline.Id,
		PipelineType:     common2.LINKED_CD,
		Name:             beHelper.GetLinkedCDPipelineName(app.Id, patchRequest.ParentCDPipeline),
		CiMaterial:       ciMaterial,
		DockerArgs:       make(map[string]string),
	}
	return cdPipeline, nil
}

func (handler *PipelineConfigRestHandlerImpl) getCdPipelinesForCIPatchRbac(patchRequest *bean.CiPatchRequest) (cdPipelines []*pipelineConfig.Pipeline, err error) {
	// if the request is for create, then there will be no cd pipelines created yet
	if patchRequest.IsCreateRequest() {
		return
	}
	// request is to either patch the existing pipeline or patch it by switching the source pipeline or delete or update-source

	// for switch , this API handles following switches
	// any -> any (except switching to external-ci)

	// to find the cd pipelines of the current ci pipelines workflow , we should query from appWorkflow Mappings.
	// cannot directly query cd-pipeline table as we don't store info about external pipeline in cdPipeline.

	// approach:
	// find the workflow in which we are patching and use the workflow id to fetch all the workflow mappings using the workflow.
	// get cd pipeline ids from those workflows and fetch the cd pipelines.

	// get the ciPipeline patch source info
	componentId, componentType := patchRequest.PatchSourceInfo()

	// the appWorkflowId can be taken from patchRequest.AppWorkflowId but doing this can make 2 sources of truth to find the workflow
	sourceAppWorkflowMapping, err := handler.appWorkflowService.FindWFMappingByComponent(componentType, componentId)
	if err != nil {
		handler.Logger.Errorw("error in finding the appWorkflowMapping using componentId and componentType", "componentType", componentType, "componentId", componentId, "err", err)
		return nil, err
	}

	cdPipelineWFMappings, err := handler.appWorkflowService.FindWFCDMappingsByWorkflowId(sourceAppWorkflowMapping.AppWorkflowId)
	if err != nil {
		handler.Logger.Errorw("error in finding the appWorkflowMappings of cd pipeline for an appWorkflow", "appWorkflowId", sourceAppWorkflowMapping.AppWorkflowId, "err", err)
		return cdPipelines, err
	}

	if len(cdPipelineWFMappings) == 0 {
		return
	}

	cdPipelineIds := make([]int, 0, len(cdPipelineWFMappings))
	for _, cdWfMapping := range cdPipelineWFMappings {
		cdPipelineIds = append(cdPipelineIds, cdWfMapping.ComponentId)
	}

	return handler.pipelineRepository.FindPipelineByIdsIn(cdPipelineIds)
}

// checkCiPatchAccess assumes all the cdPipelines belong to same app
func (handler *PipelineConfigRestHandlerImpl) checkCiPatchAccess(token string, resourceName string, cdPipelines []*pipelineConfig.Pipeline) bool {

	if len(cdPipelines) == 0 {
		// no cd pipelines are present , so user can edit if he has app admin access
		return handler.enforcer.Enforce(token, casbin.ResourceApplications, casbin.ActionCreate, resourceName)
	}

	appId := 0
	envIds := make([]int, len(cdPipelines))
	for _, cdPipeline := range cdPipelines {
		envIds = append(envIds, cdPipeline.EnvironmentId)
		appId = cdPipeline.AppId
	}

	rbacObjectsMap, _ := handler.enforcerUtil.GetRbacObjectsByEnvIdsAndAppId(envIds, appId)
	envRbacResultMap := handler.enforcer.EnforceInBatch(token, casbin.ResourceEnvironment, casbin.ActionUpdate, maps.Values(rbacObjectsMap))

	for _, hasAccess := range envRbacResultMap {
		if hasAccess {
			return true
		}
	}

	return false
}

func (handler *PipelineConfigRestHandlerImpl) checkIfHasBranchChangeAccess(token, appName string, cdPipelines []*pipelineConfig.Pipeline) bool {
	if len(cdPipelines) == 0 {
		noEnvObject := handler.enforcerUtil.GetTeamNoEnvRBACNameByAppName(appName)
		return handler.enforcer.Enforce(token, casbin.ResourceCiPipelineSourceValue, casbin.ActionUpdate, noEnvObject)
	}

	// checking if user has branch change access by checking on cd pipelines
	// checking rbac for cd cdPipelines
	if len(cdPipelines) != 0 {
		cdPipelineIds := make([]int, len(cdPipelines))
		for i, cdPipeline := range cdPipelines {
			cdPipelineIds[i] = cdPipeline.Id
		}

		rbacObjects, _ := handler.enforcerUtil.GetTeamRbacObjectsByPipelineIds(cdPipelineIds)
		envRbacResultMap := handler.enforcer.EnforceInBatch(token, casbin.ResourceCiPipelineSourceValue, casbin.ActionUpdate, rbacObjects)
		for _, rbacResultOk := range envRbacResultMap {
			if !rbacResultOk {
				return false
			}
		}

	}
	return true
}
func (handler *PipelineConfigRestHandlerImpl) GetCiPipeline(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	appId, err := strconv.Atoi(vars["appId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	token := r.Header.Get("token")
	app, err := handler.pipelineBuilder.GetApp(appId)
	if err != nil {
		handler.Logger.Errorw("service err, GetCiPipeline", "err", err, "appId", appId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	resourceName := handler.enforcerUtil.GetAppRBACName(app.AppName)
	ok := handler.enforcerUtil.CheckAppRbacForAppOrJob(token, resourceName, casbin.ActionGet)
	if !ok {
		common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
		return
	}
	ciConf, err := handler.pipelineBuilder.GetCiPipelineRespResolved(appId)
	if err != nil {
		handler.Logger.Errorw("service err, GetCiPipelineRespResolved", "appId", appId, "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, ciConf, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) GetExternalCi(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	appId, err := strconv.Atoi(vars["appId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	token := r.Header.Get("token")
	app, err := handler.pipelineBuilder.GetApp(appId)
	if err != nil {
		handler.Logger.Errorw("service err, GetExternalCi", "err", err, "appId", appId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	resourceName := handler.enforcerUtil.GetAppRBACName(app.AppName)
	if ok := handler.enforcer.Enforce(token, casbin.ResourceApplications, casbin.ActionGet, resourceName); !ok {
		common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
		return
	}
	ciConf, err := handler.pipelineBuilder.GetExternalCi(appId)
	if err != nil {
		handler.Logger.Errorw("service err, GetExternalCi", "err", err, "appId", appId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	common.WriteJsonResp(w, err, ciConf, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) GetExternalCiById(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	appId, err := strconv.Atoi(vars["appId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	externalCiId, err := strconv.Atoi(vars["externalCiId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	token := r.Header.Get("token")
	app, err := handler.pipelineBuilder.GetApp(appId)
	if err != nil {
		handler.Logger.Errorw("service err, GetExternalCiById", "err", err, "appId", appId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	resourceName := handler.enforcerUtil.GetAppRBACName(app.AppName)
	if ok := handler.enforcer.Enforce(token, casbin.ResourceApplications, casbin.ActionGet, resourceName); !ok {
		common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
		return
	}
	ciConf, err := handler.pipelineBuilder.GetExternalCiById(appId, externalCiId)
	if err != nil {
		handler.Logger.Errorw("service err, GetExternalCiById", "err", err, "appId", appId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	common.WriteJsonResp(w, err, ciConf, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) GetCIRuntimeParams(w http.ResponseWriter, r *http.Request) {
	token := r.Header.Get("token")
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	var ciPipelineId int
	v := r.URL.Query()
	pipelineId := v.Get("pipelineId")
	if len(pipelineId) != 0 {
		ciPipelineId, err = strconv.Atoi(pipelineId)
		if err != nil {
			handler.Logger.Errorw("request err, GetCIRuntimeParams", "err", err, "pipelineIdParam", pipelineId)
			response.WriteResponse(http.StatusBadRequest, "please send valid pipelineId", w, errors.New("pipelineId id invalid"))
			return
		}
	} else {
		response.WriteResponse(http.StatusBadRequest, "please send valid pipelineId", w, errors.New("pipelineId id invalid"))
		return
	}
	handler.Logger.Infow("request payload, GetCIRuntimeParams", "pipelineId", pipelineId)
	ciPipeline, err := handler.ciPipelineConfigReadService.GetCiPipelineById(ciPipelineId)
	if err != nil {
		handler.Logger.Infow("service error, GetCIRuntimeParams", "err", err, "pipelineId", pipelineId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	object := handler.enforcerUtil.GetAppRBACNameByAppId(ciPipeline.AppId)
	if ok := handler.enforcerUtil.CheckAppRbacForAppOrJob(token, object, casbin.ActionGet); !ok {
		common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
		return
	}

	runtimeParams, err := handler.pipelineBuilder.GetCIRuntimeParams(ciPipelineId)
	if err != nil {
		handler.Logger.Errorw("error in getting ci runtime params", "err", err, "ciPipelineId", ciPipelineId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, runtimeParams, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) GetCiPvcSelectorCache(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	token := r.Header.Get("token")
	handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionGet, "*")
	resourceStatusMap, ciWorkflowPvcMap := handler.ciHandler.GetCiPvcSelectorCache()
	resp := map[string]interface{}{
		"resourceStatusMap": resourceStatusMap,
		"ciWorkflowPvcMap":  ciWorkflowPvcMap,
	}
	common.WriteJsonResp(w, nil, resp, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) validateCiTriggerRBAC(token string, ciPipelineId, triggerEnvironmentId int) error {
	// RBAC STARTS
	// checking if user has trigger access on app, if not will be forbidden to trigger independent of number of cd cdPipelines
	ciPipeline, err := handler.ciPipelineRepository.FindById(ciPipelineId)
	if err != nil {
		handler.Logger.Errorw("err in finding ci pipeline, TriggerCiPipeline", "err", err, "ciPipelineId", ciPipelineId)
		errMsg := fmt.Sprintf("error in finding ci pipeline for id '%d'", ciPipelineId)
		return util.NewApiError(http.StatusBadRequest, errMsg, errMsg)
	}
	appWorkflowMapping, err := handler.appWorkflowService.FindAppWorkflowByCiPipelineId(ciPipelineId)
	if err != nil {
		handler.Logger.Errorw("err in finding appWorkflowMapping, TriggerCiPipeline", "err", err, "ciPipelineId", ciPipelineId)
		errMsg := fmt.Sprintf("error in finding appWorkflowMapping for ciPipelineId '%d'", ciPipelineId)
		return util.NewApiError(http.StatusBadRequest, errMsg, errMsg)
	}
	workflowName := ""
	if len(appWorkflowMapping) > 0 {
		workflowName = appWorkflowMapping[0].AppWorkflow.Name
	}
	// This is being done for jobs, jobs execute in default-env (devtron-ci) namespace by default. so considering DefaultCiNamespace as env for rbac enforcement
	envName := ""
	if triggerEnvironmentId == 0 {
		envName = constants.DefaultCiWorkflowNamespace
	}
	appObject := handler.enforcerUtil.GetAppRBACNameByAppId(ciPipeline.AppId)
	workflowObject := handler.enforcerUtil.GetWorkflowRBACByCiPipelineId(ciPipelineId, workflowName)
	triggerObject := handler.enforcerUtil.GetTeamEnvRBACNameByCiPipelineIdAndEnvIdOrName(ciPipelineId, triggerEnvironmentId, envName)
	var appRbacOk bool
	if ciPipeline.App.AppType == helper.CustomApp {
		appRbacOk = handler.enforcer.Enforce(token, casbin.ResourceApplications, casbin.ActionTrigger, appObject)
	} else if ciPipeline.App.AppType == helper.Job {
		appRbacOk = handler.enforcer.Enforce(token, casbin.ResourceJobs, casbin.ActionTrigger, appObject) && handler.enforcer.Enforce(token, casbin.ResourceWorkflow, casbin.ActionTrigger, workflowObject) && handler.enforcer.Enforce(token, casbin.ResourceJobsEnv, casbin.ActionTrigger, triggerObject)
	}

	if !appRbacOk {
		handler.Logger.Debug(fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
		return util.NewApiError(http.StatusForbidden, common.UnAuthorisedUser, common.UnAuthorisedUser)
	}
	// checking rbac for cd cdPipelines
	cdPipelines, err := handler.pipelineRepository.FindByCiPipelineId(ciPipelineId)
	if err != nil {
		handler.Logger.Errorw("error in finding ccd cdPipelines by ciPipelineId", "err", err, "ciPipelineId", ciPipelineId)
		errMsg := fmt.Sprintf("error in finding cd cdPipelines for ciPipelineId '%d'", ciPipelineId)
		return util.NewApiError(http.StatusBadRequest, errMsg, errMsg)
	}
	cdPipelineRbacObjects := make([]string, len(cdPipelines))
	rbacObjectCdTriggerTypeMap := make(map[string]pipelineConfig.TriggerType, len(cdPipelines))
	for i, cdPipeline := range cdPipelines {
		envObject := handler.enforcerUtil.GetAppRBACByAppIdAndPipelineId(cdPipeline.AppId, cdPipeline.Id)
		cdPipelineRbacObjects[i] = envObject
		rbacObjectCdTriggerTypeMap[envObject] = cdPipeline.TriggerType
	}

	hasAnyEnvTriggerAccess := len(cdPipelines) == 0 //if no pipelines then appAccess is enough. For jobs also, this will be true
	if !hasAnyEnvTriggerAccess {
		//cdPipelines present, to check access for cd trigger
		envRbacResultMap := handler.enforcer.EnforceInBatch(token, casbin.ResourceEnvironment, casbin.ActionTrigger, cdPipelineRbacObjects)
		for rbacObject, rbacResultOk := range envRbacResultMap {
			if rbacObjectCdTriggerTypeMap[rbacObject] == pipelineConfig.TRIGGER_TYPE_AUTOMATIC && !rbacResultOk {
				return util.NewApiError(http.StatusForbidden, common.UnAuthorisedUser, common.UnAuthorisedUser)
			}
			if rbacResultOk { //this flow will come if pipeline is automatic and has access or if pipeline is manual,
				// by which we can ensure if there are no automatic pipelines then atleast access on one manual is present
				hasAnyEnvTriggerAccess = true
			}
		}
		if !hasAnyEnvTriggerAccess {
			return util.NewApiError(http.StatusForbidden, common.UnAuthorisedUser, common.UnAuthorisedUser)
		}
	}
	// RBAC ENDS
	return nil
}

func (handler *PipelineConfigRestHandlerImpl) TriggerCiPipeline(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	decoder := json.NewDecoder(r.Body)
	var ciTriggerRequest bean.CiTriggerRequest
	err = decoder.Decode(&ciTriggerRequest)
	if err != nil {
		handler.Logger.Errorw("request err, TriggerCiPipeline", "err", err, "payload", ciTriggerRequest)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	token := r.Header.Get("token")
	// RBAC block starts
	err = handler.validateCiTriggerRBAC(token, ciTriggerRequest.PipelineId, ciTriggerRequest.EnvironmentId)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	// RBAC block ends
	if !handler.validForMultiMaterial(ciTriggerRequest) {
		handler.Logger.Errorw("invalid req, commit hash not present for multi-git", "payload", ciTriggerRequest)
		common.WriteJsonResp(w, errors.New("invalid req, commit hash not present for multi-git"),
			nil, http.StatusBadRequest)
	}
	if err := runtimeParam.ValidateRuntimeParams(ciTriggerRequest.RuntimeParams); err != nil {
		handler.Logger.Errorw("invalid ci trigger req, reserved env vars present in request", "err", err, "payload", ciTriggerRequest)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	ciTriggerRequest.TriggeredBy = userId
	handler.Logger.Infow("request payload, TriggerCiPipeline", "payload", ciTriggerRequest)
	response := make(map[string]string)
	resp, err := handler.ciHandlerService.HandleCIManual(ciTriggerRequest)
	if errors.Is(err, pipelineConfigBean.ErrImagePathInUse) {
		handler.Logger.Errorw("service err duplicate image tag, TriggerCiPipeline", "err", err, "payload", ciTriggerRequest)
		common.WriteJsonResp(w, err, err, http.StatusConflict)
		return
	}
	if err != nil {
		if parsers.IsVariableParsingError(err) {
			handler.Logger.Errorw("scope variable not resolved due to empty/unknown string, TriggerCiPipeline", "err", err, "payload", ciTriggerRequest)
			common.WriteJsonResp(w, err, err.Error(), http.StatusConflict)
			return
		}
		handler.Logger.Errorw("service err, TriggerCiPipeline", "err", err, "payload", ciTriggerRequest)
		common.WriteJsonResp(w, err, err.Error(), http.StatusInternalServerError)
		return
	}
	response["apiResponse"] = strconv.Itoa(resp)
	common.WriteJsonResp(w, err, response, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) CiStageFileUpload(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	appId, err := strconv.Atoi(vars["appId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	ciPipelineId, err := strconv.Atoi(vars["ciPipelineId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	var decoder = schema.NewDecoder()
	decoder.IgnoreUnknownKeys(true)
	request := apiBean.NewCiStageFileUploadQuery()
	err = decoder.Decode(request, r.URL.Query())
	if err != nil {
		handler.Logger.Errorw("request err, CiStageFileUpload", "err", err, "query", r.URL.Query())
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	token := r.Header.Get("token")
	if ciPipelineId != 0 {
		// Trigger Ci Pipeline RBAC for Existing Ci Pipeline
		err = handler.validateCiTriggerRBAC(token, ciPipelineId, request.EnvironmentId)
		if err != nil {
			common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
			return
		}
		// RBAC
	} else {
		// Trigger App RBAC for New Ci Pipeline
		object := handler.enforcerUtil.GetAppRBACNameByAppId(appId)
		if ok := handler.enforcer.Enforce(token, casbin.ResourceApplications, casbin.ActionTrigger, object); !ok {
			common.WriteJsonResp(w, err, common.UnAuthorisedUser, http.StatusForbidden)
			return
		}
		// RBAC
	}
	fileDetails, err := handler.fileUploadService.FileUploadHandler(w, r, &request.FileUploadQuery)
	if err != nil {
		handler.Logger.Errorw("service err, CiStageFileUpload", "err", err, "query", request)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, fileDetails, http.StatusOK)
	return
}

func (handler *PipelineConfigRestHandlerImpl) FetchMaterials(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	vars := mux.Vars(r)
	pipelineId, err := strconv.Atoi(vars["pipelineId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	v := r.URL.Query()
	showAll := false
	show := v.Get("showAll")
	if len(show) > 0 {
		showAll, err = strconv.ParseBool(show)
		if err != nil {
			showAll = true
			err = nil
			// ignore error, apply rbac by default
		}
	}
	handler.Logger.Infow("request payload, FetchMaterials", "pipelineId", pipelineId)
	ciPipeline, err := handler.ciPipelineRepository.FindById(pipelineId)
	if err != nil {
		handler.Logger.Errorw("service err, UpdateCiTemplate", "err", err, "pipelineId", pipelineId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	// RBAC
	token := r.Header.Get("token")
	object := handler.enforcerUtil.GetAppRBACNameByAppId(ciPipeline.AppId)
	ok := handler.enforcerUtil.CheckAppRbacForAppOrJob(token, object, casbin.ActionGet)
	if !ok {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusForbidden)
		return
	}
	// RBAC
	resp, err := handler.ciHandler.FetchMaterialsByPipelineId(pipelineId, showAll)
	if err != nil {
		handler.Logger.Errorw("service err, FetchMaterials", "err", err, "pipelineId", pipelineId)
		common.WriteJsonResp(w, err, resp, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, resp, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) FetchMaterialsByMaterialId(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	vars := mux.Vars(r)
	pipelineId, err := strconv.Atoi(vars["pipelineId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	gitMaterialId, err := strconv.Atoi(vars["gitMaterialId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	v := r.URL.Query()
	showAll := false
	show := v.Get("showAll")
	if len(show) > 0 {
		showAll, err = strconv.ParseBool(show)
		if err != nil {
			showAll = true
			err = nil
			// ignore error, apply rbac by default
		}
	}
	handler.Logger.Infow("request payload, FetchMaterials", "pipelineId", pipelineId)
	ciPipeline, err := handler.ciPipelineRepository.FindById(pipelineId)
	if err != nil {
		handler.Logger.Errorw("service err, UpdateCiTemplate", "err", err, "pipelineId", pipelineId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	// RBAC
	token := r.Header.Get("token")
	object := handler.enforcerUtil.GetAppRBACNameByAppId(ciPipeline.AppId)
	ok := handler.enforcerUtil.CheckAppRbacForAppOrJob(token, object, casbin.ActionGet)
	if !ok {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusForbidden)
		return
	}
	// RBAC
	resp, err := handler.ciHandler.FetchMaterialsByPipelineIdAndGitMaterialId(pipelineId, gitMaterialId, showAll)
	if err != nil {
		handler.Logger.Errorw("service err, FetchMaterials", "err", err, "pipelineId", pipelineId)
		common.WriteJsonResp(w, err, resp, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, resp, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) RefreshMaterials(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	vars := mux.Vars(r)
	gitMaterialId, err := strconv.Atoi(vars["gitMaterialId"])
	if err != nil {
		handler.Logger.Error(err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	handler.Logger.Infow("request payload, RefreshMaterials", "gitMaterialId", gitMaterialId)
	material, err := handler.gitMaterialReadService.FindById(gitMaterialId)
	if err != nil {
		handler.Logger.Errorw("service err, RefreshMaterials", "err", err, "gitMaterialId", gitMaterialId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	// RBAC
	token := r.Header.Get("token")
	object := handler.enforcerUtil.GetAppRBACNameByAppId(material.AppId)
	ok := handler.enforcerUtil.CheckAppRbacForAppOrJob(token, object, casbin.ActionGet)
	if !ok {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusForbidden)
		return
	}
	// RBAC

	resp, err := handler.ciHandler.RefreshMaterialByCiPipelineMaterialId(material.Id)
	if err != nil {
		handler.Logger.Errorw("service err, RefreshMaterials", "err", err, "gitMaterialId", gitMaterialId)
		common.WriteJsonResp(w, err, resp, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, resp, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) GetCiPipelineMin(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	vars := mux.Vars(r)
	appId, err := strconv.Atoi(vars["appId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	v := r.URL.Query()
	envIdsString := v.Get("envIds")
	token := r.Header.Get("token")
	paramEnvIds := make([]int, 0)
	if len(envIdsString) > 0 {
		paramEnvIds, err = stringsUtil.SplitCommaSeparatedIntValues(envIdsString)
		if err != nil {
			common.WriteJsonResp(w, err, "please provide valid envIds", http.StatusBadRequest)
			return
		}
	}
	envIds, err := handler.getAuthorizedEnvIds(userId, appId, paramEnvIds, token)
	if err != nil {
		common.WriteJsonResp(w, err, err.Error(), http.StatusInternalServerError)
		return
	}

	// RBAC
	handler.Logger.Infow("request payload, GetCiPipelineMin", "appId", appId)
	object := handler.enforcerUtil.GetAppRBACNameByAppId(appId)
	ok := handler.enforcerUtil.CheckAppRbacForAppOrJob(token, object, casbin.ActionGet)
	if !ok {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusForbidden)
		return
	}
	// RBAC

	//giveSingleCiPipelines flag tells if giveSingleCiPipelines data should be given or not, if no filters are applied giveSingleCiPipelines should be given
	giveSingleCiPipelines := false
	if len(paramEnvIds) == 0 {
		giveSingleCiPipelines = true
	}
	ciPipelines, err := handler.pipelineBuilder.GetCiPipelineMin(appId, envIds, giveSingleCiPipelines)
	if err != nil {
		handler.Logger.Errorw("service err, GetCiPipelineMin", "err", err, "appId", appId)
		if util.IsErrNoRows(err) {
			err = &util.ApiError{Code: "404", HttpStatusCode: http.StatusNotFound, UserMessage: "no data found"}
			common.WriteJsonResp(w, err, nil, http.StatusOK)
			return
		} else {
			common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
			return
		}
	}
	common.WriteJsonResp(w, err, ciPipelines, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) getAuthorizedEnvIds(userId int32, appId int, paramEnvIds []int, token string) ([]int, error) {
	envIds := make([]int, 0)
	canOnlyViewPermittedData, err := handler.globalFlagService.CanOnlyViewPermittedData(userId)
	if err != nil {
		handler.Logger.Errorw("service err, GetCiPipelineMin", "err", err, "userId", userId)
		return nil, err
	}
	if len(paramEnvIds) == 0 {
		envIds, err = handler.pipelineBuilder.GetPipelineEnvironmentsForApplication(appId)
		if err != nil {
			handler.Logger.Errorw("service err, GetCiPipelineMin", "err", err, "appId", appId)
			return nil, err
		}
	} else {
		envIds = paramEnvIds
	}

	if canOnlyViewPermittedData {
		// RBAC for authorised Envs
		authorizedResourceIds := handler.rbackFilterUtil.FilterAuthorizedResources(envIds, appId, token)
		// find common envIds between authorizedResourceIds and envIds
		envIds = commonUtil.GetCommonEnvIds(authorizedResourceIds, envIds)
	}
	return envIds, nil
}

func (handler *PipelineConfigRestHandlerImpl) DownloadCiWorkflowArtifacts(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	vars := mux.Vars(r)
	pipelineId, err := strconv.Atoi(vars["pipelineId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	buildId, err := strconv.Atoi(vars["workflowId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	handler.Logger.Infow("request payload, DownloadCiWorkflowArtifacts", "pipelineId", pipelineId, "buildId", buildId)
	ciPipeline, err := handler.ciPipelineRepository.FindById(pipelineId)
	if err != nil {
		handler.Logger.Error(err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	// RBAC
	token := r.Header.Get("token")
	object := handler.enforcerUtil.GetAppRBACNameByAppId(ciPipeline.AppId)
	if ok := handler.enforcer.Enforce(token, casbin.ResourceApplications, casbin.ActionGet, object); !ok {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusForbidden)
		return
	}
	// RBAC

	file, err := handler.ciHandlerService.DownloadCiWorkflowArtifacts(pipelineId, buildId)
	defer file.Close()
	if err != nil {
		handler.Logger.Errorw("service err, DownloadCiWorkflowArtifacts", "err", err, "pipelineId", pipelineId, "buildId", buildId)
		if util.IsErrNoRows(err) {
			err = &util.ApiError{Code: "404", HttpStatusCode: 200, UserMessage: "no workflow found"}
			common.WriteJsonResp(w, err, nil, http.StatusOK)
		} else {
			common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		}
		return
	}
	w.Header().Set("Content-Disposition", "attachment; filename="+strconv.Itoa(buildId)+".zip")
	w.Header().Set("Content-Type", "application/octet-stream")
	w.Header().Set("Content-Length", r.Header.Get("Content-Length"))
	_, err = io.Copy(w, file)
	if err != nil {
		handler.Logger.Errorw("service err, DownloadCiWorkflowArtifacts", "err", err, "pipelineId", pipelineId, "buildId", buildId)
	}
}

func (handler *PipelineConfigRestHandlerImpl) GetHistoricBuildLogs(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	vars := mux.Vars(r)
	pipelineId, err := strconv.Atoi(vars["pipelineId"])
	if err != nil {
		handler.Logger.Error(err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	workflowId, err := strconv.Atoi(vars["workflowId"])
	if err != nil {
		handler.Logger.Error(err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	handler.Logger.Infow("request payload, GetHistoricBuildLogs", "pipelineId", pipelineId, "workflowId", workflowId)
	ciPipeline, err := handler.ciPipelineRepository.FindById(pipelineId)
	if err != nil {
		handler.Logger.Errorw("service err, GetHistoricBuildLogs", "err", err, "pipelineId", pipelineId, "workflowId", workflowId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	// RBAC
	token := r.Header.Get("token")
	object := handler.enforcerUtil.GetAppRBACNameByAppId(ciPipeline.AppId)
	if ok := handler.enforcer.Enforce(token, casbin.ResourceApplications, casbin.ActionGet, object); !ok {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusForbidden)
		return
	}
	// RBAC
	resp, err := handler.ciHandlerService.GetHistoricBuildLogs(workflowId, nil)
	if err != nil {
		handler.Logger.Errorw("service err, GetHistoricBuildLogs", "err", err, "pipelineId", pipelineId, "workflowId", workflowId)
		common.WriteJsonResp(w, err, resp, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, resp, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) GetBuildHistory(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	vars := mux.Vars(r)
	pipelineId, err := strconv.Atoi(vars["pipelineId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	offsetQueryParam := r.URL.Query().Get("offset")
	offset, err := strconv.Atoi(offsetQueryParam)
	if offsetQueryParam == "" || err != nil {
		common.WriteJsonResp(w, err, "invalid offset", http.StatusBadRequest)
		return
	}
	sizeQueryParam := r.URL.Query().Get("size")
	limit, err := strconv.Atoi(sizeQueryParam)
	if sizeQueryParam == "" || err != nil {
		common.WriteJsonResp(w, err, "invalid size", http.StatusBadRequest)
		return
	}
	handler.Logger.Infow("request payload, GetBuildHistory", "pipelineId", pipelineId, "offset", offset)
	ciPipeline, err := handler.ciPipelineRepository.FindById(pipelineId)
	if err != nil {
		handler.Logger.Errorw("service err, GetBuildHistory", "err", err, "pipelineId", pipelineId, "offset", offset)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	appWorkflowMapping, err := handler.appWorkflowService.FindAppWorkflowByCiPipelineId(pipelineId)
	if err != nil {
		handler.Logger.Errorw("service err, GetBuildHistory", "err", err, "pipelineId", pipelineId, "offset", offset)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	// RBAC for build history
	token := r.Header.Get("token")
	isAuthorised := false
	workflowName := ""
	if len(appWorkflowMapping) > 0 {
		workflowName = appWorkflowMapping[0].AppWorkflow.Name
	}
	object := handler.enforcerUtil.GetAppRBACNameByAppId(ciPipeline.AppId)
	workflowResourceObject := handler.enforcerUtil.GetWorkflowRBACByCiPipelineId(pipelineId, workflowName)
	if ok := handler.enforcer.Enforce(token, casbin.ResourceApplications, casbin.ActionGet, object); ok {
		isAuthorised = true
	}
	if !isAuthorised {
		isAuthorised = handler.enforcer.Enforce(token, casbin.ResourceJobs, casbin.ActionGet, object) && handler.enforcer.Enforce(token, casbin.ResourceWorkflow, casbin.ActionGet, workflowResourceObject)
	}
	if !isAuthorised {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusForbidden)
		return
	}
	// RBAC
	// RBAC for edit tag access , user should have build permission in current ci-pipeline
	triggerAccess := handler.enforcer.Enforce(token, casbin.ResourceApplications, casbin.ActionTrigger, object) || handler.enforcer.Enforce(token, casbin.ResourceJobs, casbin.ActionTrigger, object)
	// RBAC
	resp := apiBean.BuildHistoryResponse{}
	workflowsResp, err := handler.ciHandler.GetBuildHistory(pipelineId, ciPipeline.AppId, offset, limit)
	resp.CiWorkflows = workflowsResp
	if err != nil {
		handler.Logger.Errorw("service err, GetBuildHistory", "err", err, "pipelineId", pipelineId, "offset", offset)
		common.WriteJsonResp(w, err, resp, http.StatusInternalServerError)
		return
	}
	appTags, err := handler.imageTaggingReadService.GetUniqueTagsByAppId(ciPipeline.AppId)
	if err != nil {
		handler.Logger.Errorw("service err, GetTagsByAppId", "err", err, "appId", ciPipeline.AppId)
		common.WriteJsonResp(w, err, resp, http.StatusInternalServerError)
		return
	}
	resp.AppReleaseTagNames = appTags

	prodEnvExists, err := handler.imageTaggingService.GetProdEnvFromParentAndLinkedWorkflow(ciPipeline.Id)
	resp.TagsEditable = prodEnvExists && triggerAccess
	resp.HideImageTaggingHardDelete = handler.imageTaggingService.IsHardDeleteHidden()
	if err != nil {
		handler.Logger.Errorw("service err, GetProdEnvFromParentAndLinkedWorkflow", "err", err, "ciPipelineId", ciPipeline.Id)
		common.WriteJsonResp(w, err, resp, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, resp, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) GetBuildLogs(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	vars := mux.Vars(r)
	pipelineId, err := strconv.Atoi(vars["pipelineId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	followLogs := true
	if ok := r.URL.Query().Has("followLogs"); ok {
		followLogsStr := r.URL.Query().Get("followLogs")
		follow, err := strconv.ParseBool(followLogsStr)
		if err != nil {
			common.WriteJsonResp(w, err, "followLogs is not a valid bool", http.StatusBadRequest)
			return
		}
		followLogs = follow
	}

	workflowId, err := strconv.Atoi(vars["workflowId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	handler.Logger.Infow("request payload, GetBuildLogs", "pipelineId", pipelineId, "workflowId", workflowId)
	ciPipeline, err := handler.ciPipelineRepository.FindById(pipelineId)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	// RBAC
	token := r.Header.Get("token")
	object := handler.enforcerUtil.GetAppRBACNameByAppId(ciPipeline.AppId)
	ok := handler.enforcerUtil.CheckAppRbacForAppOrJob(token, object, casbin.ActionGet)
	if !ok {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusForbidden)
		return
	}
	// RBAC
	lastSeenMsgId := -1
	lastEventId := r.Header.Get("Last-Event-ID")
	if len(lastEventId) > 0 {
		lastSeenMsgId, err = strconv.Atoi(lastEventId)
		if err != nil {
			handler.Logger.Errorw("request err, GetBuildLogs", "err", err, "pipelineId", pipelineId, "workflowId", workflowId, "lastEventId", lastEventId)
			common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
			return
		}
	}
	logsReader, cleanUp, err := handler.ciHandlerService.GetRunningWorkflowLogs(workflowId, followLogs)
	if err != nil {
		handler.Logger.Errorw("service err, GetBuildLogs", "err", err, "pipelineId", pipelineId, "workflowId", workflowId, "lastEventId", lastEventId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	ctx, cancel := context.WithCancel(r.Context())
	if cn, ok := w.(http.CloseNotifier); ok {
		go func(done <-chan struct{}, closed <-chan bool) {
			select {
			case <-done:
			case <-closed:
				cancel()
			}
		}(ctx.Done(), cn.CloseNotify())
	}
	defer cancel()
	defer func() {
		if cleanUp != nil {
			cleanUp()
		}
	}()
	handler.streamOutput(w, logsReader, lastSeenMsgId)
}

func (handler *PipelineConfigRestHandlerImpl) FetchMaterialInfo(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	vars := mux.Vars(r)
	ciArtifactId, err := strconv.Atoi(vars["ciArtifactId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	envId, err := strconv.Atoi(vars["envId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	handler.Logger.Infow("request payload, FetchMaterialInfo", "err", err, "ciArtifactId", ciArtifactId)
	resp, err := handler.ciHandler.FetchMaterialInfoByArtifactId(ciArtifactId, envId)
	if err != nil {
		handler.Logger.Errorw("service err, FetchMaterialInfo", "err", err, "ciArtifactId", ciArtifactId)
		if util.IsErrNoRows(err) {
			err = &util.ApiError{Code: "404", HttpStatusCode: http.StatusNotFound, UserMessage: "no material info found"}
			common.WriteJsonResp(w, err, nil, http.StatusOK)
		} else {
			common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		}
		return
	}
	// RBAC
	token := r.Header.Get("token")
	object := handler.enforcerUtil.GetAppRBACNameByAppId(resp.AppId)
	if ok := handler.enforcer.Enforce(token, casbin.ResourceApplications, casbin.ActionGet, object); !ok {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusForbidden)
		return
	}
	// RBAC

	common.WriteJsonResp(w, err, resp, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) FetchMaterialInfoForArtifact(w http.ResponseWriter, r *http.Request) {
	ctx := util2.NewRequestCtx(r.Context())
	vars := mux.Vars(r)
	ciArtifactId, err := strconv.Atoi(vars["ciArtifactId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	handler.Logger.Infow("request payload, FetchMaterialInfoForArtifact", "err", err, "ciArtifactId", ciArtifactId)
	resp, err := handler.ciHandler.FetchMaterialInfoForArtifact(ctx, ciArtifactId)
	if err != nil {
		handler.Logger.Errorw("service err, FetchMaterialInfoForArtifact", "err", err, "ciArtifactId", ciArtifactId)
		if util.IsErrNoRows(err) {
			err = &util.ApiError{Code: "404", HttpStatusCode: http.StatusNotFound, UserMessage: "no material info found"}
			common.WriteJsonResp(w, err, nil, http.StatusOK)
		} else {
			common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		}
		return
	}
	// RBAC
	token := r.Header.Get("token")
	object := handler.enforcerUtil.GetAppRBACNameByAppId(resp.AppId)
	if ok := handler.enforcer.Enforce(token, casbin.ResourceApplications, casbin.ActionGet, object); !ok {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusForbidden)
		return
	}
	// RBAC

	common.WriteJsonResp(w, err, resp, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) GetCIPipelineById(w http.ResponseWriter, r *http.Request) {
	token := r.Header.Get("token")
	vars := mux.Vars(r)
	appId, err := strconv.Atoi(vars["appId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	pipelineId, err := strconv.Atoi(vars["pipelineId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	handler.Logger.Infow("request payload, GetCIPipelineById", "err", err, "appId", appId, "pipelineId", pipelineId)

	app, err := handler.pipelineBuilder.GetApp(appId)
	if err != nil {
		handler.Logger.Infow("service error, GetCIPipelineById", "err", err, "appId", appId, "pipelineId", pipelineId)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	resourceName := handler.enforcerUtil.GetAppRBACName(app.AppName)
	ok := handler.enforcerUtil.CheckAppRbacForAppOrJob(token, resourceName, casbin.ActionGet)
	if !ok {
		common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
		return
	}

	pipelineData, err := handler.pipelineRepository.FindActiveByAppIdAndPipelineId(appId, pipelineId)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	var environmentIds []int
	for _, pipeline := range pipelineData {
		environmentIds = append(environmentIds, pipeline.EnvironmentId)
	}
	if handler.appWorkflowService.CheckCdPipelineByCiPipelineId(pipelineId) {
		for _, envId := range environmentIds {
			envObject := handler.enforcerUtil.GetEnvRBACNameByCiPipelineIdAndEnvId(pipelineId, envId)
			if ok := handler.enforcer.Enforce(token, casbin.ResourceEnvironment, casbin.ActionGet, envObject); !ok {
				common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
				return
			}
		}
	}

	ciPipeline, err := handler.pipelineBuilder.GetCiPipelineByIdWithDefaultTag(pipelineId)
	if err != nil {
		handler.Logger.Infow("service error, GetCiPipelineByIdWithDefaultTag", "err", err, "appId", appId, "pipelineId", pipelineId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, ciPipeline, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) GetCIPipelineByPipelineId(w http.ResponseWriter, r *http.Request) {
	token := r.Header.Get("token")
	var ciPipelineId int
	var err error
	v := r.URL.Query()
	pipelineId := v.Get("pipelineId")
	if len(pipelineId) != 0 {
		ciPipelineId, err = strconv.Atoi(pipelineId)
		if err != nil {
			handler.Logger.Errorw("request err, GetCIPipelineByPipelineId", "err", err, "pipelineIdParam", pipelineId)
			response.WriteResponse(http.StatusBadRequest, "please send valid pipelineId", w, errors.New("pipelineId id invalid"))
			return
		}
	} else {
		response.WriteResponse(http.StatusBadRequest, "please send valid pipelineId", w, errors.New("pipelineId id invalid"))
		return
	}

	handler.Logger.Infow("request payload, GetCIPipelineByPipelineId", "pipelineId", pipelineId)

	ciPipeline, err := handler.pipelineBuilder.GetCiPipelineById(ciPipelineId)
	if err != nil {
		handler.Logger.Infow("service error, GetCIPipelineById", "err", err, "pipelineId", pipelineId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	app, err := handler.pipelineBuilder.GetApp(ciPipeline.AppId)
	if err != nil {
		handler.Logger.Infow("service error, GetCIPipelineByPipelineId", "err", err, "appId", ciPipeline.AppId, "pipelineId", pipelineId)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	ciPipeline.AppName = app.AppName
	ciPipeline.AppType = app.AppType

	resourceName := handler.enforcerUtil.GetAppRBACNameByAppId(app.Id)
	if ok := handler.enforcer.Enforce(token, casbin.ResourceApplications, casbin.ActionGet, resourceName); !ok {
		common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
		return
	}

	pipelineData, err := handler.pipelineRepository.FindActiveByAppIdAndPipelineId(ciPipeline.AppId, ciPipelineId)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	var environmentIds []int
	for _, pipeline := range pipelineData {
		environmentIds = append(environmentIds, pipeline.EnvironmentId)
	}
	if handler.appWorkflowService.CheckCdPipelineByCiPipelineId(ciPipelineId) {
		for _, envId := range environmentIds {
			envObject := handler.enforcerUtil.GetEnvRBACNameByCiPipelineIdAndEnvId(ciPipelineId, envId)
			if ok := handler.enforcer.Enforce(token, casbin.ResourceEnvironment, casbin.ActionUpdate, envObject); !ok {
				common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
				return
			}
		}
	}
	common.WriteJsonResp(w, err, ciPipeline, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) CreateMaterial(w http.ResponseWriter, r *http.Request) {
	token := r.Header.Get("token")
	decoder := json.NewDecoder(r.Body)
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	var createMaterialDto bean.CreateMaterialDTO
	err = decoder.Decode(&createMaterialDto)
	createMaterialDto.UserId = userId
	if err != nil {
		handler.Logger.Errorw("request err, CreateMaterial", "err", err, "CreateMaterial", createMaterialDto)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	handler.Logger.Infow("request payload, CreateMaterial", "CreateMaterial", createMaterialDto)
	err = handler.validator.Struct(createMaterialDto)
	if err != nil {
		handler.Logger.Errorw("validation err, CreateMaterial", "err", err, "CreateMaterial", createMaterialDto)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	resourceObject := handler.enforcerUtil.GetAppRBACNameByAppId(createMaterialDto.AppId)
	isAuthorised := handler.enforcerUtil.CheckAppRbacForAppOrJob(token, resourceObject, casbin.ActionCreate)
	if !isAuthorised {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusForbidden)
		return
	}
	for _, gitMaterial := range createMaterialDto.Material {
		validationResult, err := handler.ValidateGitMaterialUrl(gitMaterial.GitProviderId, gitMaterial.Url)
		if err != nil {
			handler.Logger.Errorw("service err, CreateMaterial", "err", err, "CreateMaterial", createMaterialDto)
			common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
			return
		} else {
			if !validationResult {
				handler.Logger.Errorw("validation err, CreateMaterial : invalid git material url", "err", err, "gitMaterialUrl", gitMaterial.Url, "CreateMaterial", createMaterialDto)
				common.WriteJsonResp(w, fmt.Errorf("validation for url failed"), nil, http.StatusBadRequest)
				return
			}
		}
	}

	createResp, err := handler.pipelineBuilder.CreateMaterialsForApp(&createMaterialDto)
	if err != nil {
		handler.Logger.Errorw("service err, CreateMaterial", "err", err, "CreateMaterial", createMaterialDto)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, createResp, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) UpdateMaterial(w http.ResponseWriter, r *http.Request) {
	token := r.Header.Get("token")
	decoder := json.NewDecoder(r.Body)
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	var updateMaterialDto bean.UpdateMaterialDTO
	err = decoder.Decode(&updateMaterialDto)
	updateMaterialDto.UserId = userId
	if err != nil {
		handler.Logger.Errorw("request err, UpdateMaterial", "err", err, "UpdateMaterial", updateMaterialDto)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	handler.Logger.Infow("request payload, UpdateMaterial", "UpdateMaterial", updateMaterialDto)
	err = handler.validator.Struct(updateMaterialDto)
	if err != nil {
		handler.Logger.Errorw("validation err, UpdateMaterial", "err", err, "UpdateMaterial", updateMaterialDto)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	validationResult, err := handler.ValidateGitMaterialUrl(updateMaterialDto.Material.GitProviderId, updateMaterialDto.Material.Url)
	if err != nil {
		handler.Logger.Errorw("service err, UpdateMaterial", "err", err, "UpdateMaterial", updateMaterialDto)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	} else {
		if !validationResult {
			handler.Logger.Errorw("validation err, UpdateMaterial : invalid git material url", "err", err, "gitMaterialUrl", updateMaterialDto.Material.Url, "UpdateMaterial", updateMaterialDto)
			common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
			return
		}
	}
	resourceObject := handler.enforcerUtil.GetAppRBACNameByAppId(updateMaterialDto.AppId)
	isAuthorised := handler.enforcerUtil.CheckAppRbacForAppOrJob(token, resourceObject, casbin.ActionCreate)
	if !isAuthorised {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusForbidden)
		return
	}

	createResp, err := handler.pipelineBuilder.UpdateMaterialsForApp(&updateMaterialDto)
	if err != nil {
		handler.Logger.Errorw("service err, UpdateMaterial", "err", err, "UpdateMaterial", updateMaterialDto)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, createResp, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) DeleteMaterial(w http.ResponseWriter, r *http.Request) {
	decoder := json.NewDecoder(r.Body)
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	var deleteMaterial bean.UpdateMaterialDTO
	err = decoder.Decode(&deleteMaterial)
	deleteMaterial.UserId = userId
	if err != nil {
		handler.Logger.Errorw("request err, DeleteMaterial", "err", err, "DeleteMaterial", deleteMaterial)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	handler.Logger.Infow("request payload, DeleteMaterial", "DeleteMaterial", deleteMaterial)
	err = handler.validator.Struct(deleteMaterial)
	if err != nil {
		handler.Logger.Errorw("validation err, DeleteMaterial", "err", err, "DeleteMaterial", deleteMaterial)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	// rbac starts
	resourceObject := handler.enforcerUtil.GetAppRBACNameByAppId(deleteMaterial.AppId)
	token := r.Header.Get("token")
	if ok := handler.enforcerUtil.CheckAppRbacForAppOrJob(token, resourceObject, casbin.ActionCreate); !ok {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusForbidden)
		return
	}

	// rbac ends
	err = handler.pipelineBuilder.DeleteMaterial(&deleteMaterial)
	if err != nil {
		handler.Logger.Errorw("service err, DeleteMaterial", "err", err, "DeleteMaterial", deleteMaterial)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, apiBean.GIT_MATERIAL_DELETE_SUCCESS_RESP, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) ReloadAndSyncAppsGitMaterial(w http.ResponseWriter, r *http.Request) {
	token := r.Header.Get("token")
	decoder := json.NewDecoder(r.Body)
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		handler.Logger.Errorw("service err, Update", "error", err, "userId", userId)
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}

	var syncRequest pipeline.GitMaterialsSyncRequest
	err = decoder.Decode(&syncRequest)
	if err != nil {
		handler.Logger.Errorw("request err, Update", "error", err, "payload", syncRequest)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	//How to handle RBAC ?
	for _, appId := range syncRequest.AppIds {
		object := handler.enforcerUtil.GetAppRBACNameByAppId(appId)
		ok := handler.enforcerUtil.CheckAppRbacForAppOrJob(token, object, casbin.ActionUpdate)
		if !ok {
			common.WriteJsonResp(w, err, "Unauthorized User", http.StatusForbidden)
			return
		}
	}
	// RBAC enforcer ends

	//call main service function
	err = handler.ciCdPipelineOrchestrator.ReloadAndSyncAppsGitMaterial(syncRequest)
	if err != nil {
		handler.Logger.Errorw("ReloadAndSyncAppsGitMaterial err", "error", err, "payload", syncRequest)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
}

func (handler *PipelineConfigRestHandlerImpl) HandleWorkflowWebhook(w http.ResponseWriter, r *http.Request) {
	decoder := json.NewDecoder(r.Body)
	var wfUpdateReq eventProcessorBean.CiCdStatus
	err := decoder.Decode(&wfUpdateReq)
	if err != nil {
		handler.Logger.Errorw("request err, HandleWorkflowWebhook", "err", err, "payload", wfUpdateReq)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	handler.Logger.Infow("request payload, HandleWorkflowWebhook", "payload", wfUpdateReq)
	resp, _, err := handler.ciHandler.UpdateWorkflow(wfUpdateReq)
	if err != nil {
		handler.Logger.Errorw("service err, HandleWorkflowWebhook", "err", err, "payload", wfUpdateReq)
		common.WriteJsonResp(w, err, resp, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, resp, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) validForMultiMaterial(ciTriggerRequest bean.CiTriggerRequest) bool {
	if len(ciTriggerRequest.CiPipelineMaterial) > 1 {
		for _, m := range ciTriggerRequest.CiPipelineMaterial {
			if m.GitCommit.Commit == "" {
				return false
			}
		}
	}
	return true
}

const AWS_HOST = "amazonaws.com"
const AWS_REGEX = "git-codecommit.*.amazonaws.com"

func validateAwsProvider(providerUrl, requestUrl string) (bool, error) {
	parsedUrl, err := url.Parse(providerUrl)
	if err != nil {
		return false, err
	}
	if !strings.HasSuffix(parsedUrl.Host, AWS_HOST) {
		return false, nil
	}
	parsedUrl, err = url.Parse(requestUrl)
	if err != nil {
		return true, err
	}

	host := parsedUrl.Host
	if parsedUrl.Scheme == "" {
		host = strings.Split(parsedUrl.Path, "/")[0]
	}

	isValid, err := regexp.MatchString(AWS_REGEX, host)
	if err != nil {
		return true, err
	}
	if !isValid {
		return true, fmt.Errorf("not a valid url")
	}

	return true, nil
}

func (handler *PipelineConfigRestHandlerImpl) ValidateGitMaterialUrl(gitProviderId int, url string) (bool, error) {
	gitProvider, err := handler.gitProviderReadService.FetchOneGitProvider(strconv.Itoa(gitProviderId))
	if err != nil {
		return false, err
	}

	isAwsUrl, err := validateAwsProvider(gitProvider.Url, url)
	if err != nil {
		return false, err
	}
	if isAwsUrl {
		return true, fmt.Errorf("AWS code commit not currently supported")
	}

	if gitProvider.AuthMode == constants2.AUTH_MODE_SSH {
		// this regex is used to generic ssh providers like gogs where format is <user>@<host>:<org>/<repo>.git
		var scpLikeSSHRegex = regexp.MustCompile(`^[\w-]+@[\w.-]+:[\w./-]+\.git$`)
		hasPrefixResult := strings.HasPrefix(url, SSH_URL_PREFIX) || scpLikeSSHRegex.MatchString(url)
		return hasPrefixResult, nil
	}
	hasPrefixResult := strings.HasPrefix(url, HTTPS_URL_PREFIX) || strings.HasPrefix(url, HTTP_URL_PREFIX)
	return hasPrefixResult, nil
}

func (handler *PipelineConfigRestHandlerImpl) CancelWorkflow(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	queryVars := r.URL.Query()
	vars := mux.Vars(r)
	workflowId, err := strconv.Atoi(vars["workflowId"])
	if err != nil {
		handler.Logger.Errorw("request err, CancelWorkflow", "err", err, "workflowId", workflowId)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	pipelineId, err := strconv.Atoi(vars["pipelineId"])
	if err != nil {
		handler.Logger.Errorw("request err, CancelWorkflow", "err", err, "pipelineId", pipelineId)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	var forceAbort bool
	forceAbortQueryParam := queryVars.Get("forceAbort")
	if len(forceAbortQueryParam) > 0 {
		forceAbort, err = strconv.ParseBool(forceAbortQueryParam)
		if err != nil {
			handler.Logger.Errorw("request err, CancelWorkflow", "err", err)
			common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
			return
		}
	}

	handler.Logger.Infow("request payload, CancelWorkflow", "workflowId", workflowId, "pipelineId", pipelineId)

	ciPipeline, err := handler.ciPipelineRepository.FindById(pipelineId)
	if err != nil {
		handler.Logger.Errorw("service err, CancelWorkflow", "err", err, "workflowId", workflowId, "pipelineId", pipelineId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	// RBAC
	token := r.Header.Get("token")
	object := handler.enforcerUtil.GetAppRBACNameByAppId(ciPipeline.AppId)
	ok := handler.enforcerUtil.CheckAppRbacForAppOrJob(token, object, casbin.ActionTrigger)
	if !ok {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusForbidden)
		return
	}
	if handler.appWorkflowService.CheckCdPipelineByCiPipelineId(pipelineId) {
		pipelineData, err := handler.pipelineRepository.FindActiveByAppIdAndPipelineId(ciPipeline.AppId, pipelineId)
		if err != nil {
			common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
			return
		}
		var environmentIds []int
		for _, pipeline := range pipelineData {
			environmentIds = append(environmentIds, pipeline.EnvironmentId)
		}
		if handler.appWorkflowService.CheckCdPipelineByCiPipelineId(pipelineId) {
			for _, envId := range environmentIds {
				envObject := handler.enforcerUtil.GetEnvRBACNameByCiPipelineIdAndEnvId(pipelineId, envId)
				if ok := handler.enforcer.Enforce(token, casbin.ResourceEnvironment, casbin.ActionTrigger, envObject); !ok {
					common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
					return
				}
			}
		}
	}

	// RBAC

	resp, err := handler.ciHandlerService.CancelBuild(workflowId, forceAbort)
	if err != nil {
		handler.Logger.Errorw("service err, CancelWorkflow", "err", err, "workflowId", workflowId, "pipelineId", pipelineId)
		if util.IsErrNoRows(err) {
			common.WriteJsonResp(w, err, nil, http.StatusNotFound)
		} else {
			common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		}
		return
	}
	common.WriteJsonResp(w, err, resp, http.StatusOK)
}

// FetchChanges FIXME check if deprecated
func (handler *PipelineConfigRestHandlerImpl) FetchChanges(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	vars := mux.Vars(r)
	ciMaterialId, err := strconv.Atoi(vars["ciMaterialId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	pipelineId, err := strconv.Atoi(vars["pipelineId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	showAll := false
	v := r.URL.Query()
	show := v.Get("showAll")
	if len(show) > 0 {
		showAll, err = strconv.ParseBool(show)
		if err != nil {
			showAll = true
			err = nil
			// ignore error, apply rbac by default
		}
	}
	handler.Logger.Infow("request payload, FetchChanges", "ciMaterialId", ciMaterialId, "pipelineId", pipelineId)
	ciPipeline, err := handler.ciPipelineRepository.FindById(pipelineId)
	if err != nil {
		handler.Logger.Errorw("request err, FetchChanges", "err", err, "ciMaterialId", ciMaterialId, "pipelineId", pipelineId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	// RBAC
	token := r.Header.Get("token")
	object := handler.enforcerUtil.GetAppRBACNameByAppId(ciPipeline.AppId)
	if ok := handler.enforcer.Enforce(token, casbin.ResourceApplications, casbin.ActionGet, object); !ok {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusForbidden)
		return
	}
	// RBAC

	changeRequest := &gitSensor.FetchScmChangesRequest{
		PipelineMaterialId: ciMaterialId,
		ShowAll:            showAll,
	}
	changes, err := handler.gitSensorClient.FetchChanges(context.Background(), changeRequest)
	if err != nil {
		handler.Logger.Errorw("service err, FetchChanges", "err", err, "ciMaterialId", ciMaterialId, "pipelineId", pipelineId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, changes.Commits, http.StatusCreated)
}

func (handler *PipelineConfigRestHandlerImpl) GetCommitMetadataForPipelineMaterial(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	vars := mux.Vars(r)
	ciPipelineMaterialId, err := strconv.Atoi(vars["ciPipelineMaterialId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	gitHash := vars["gitHash"]
	handler.Logger.Infow("request payload, GetCommitMetadataForPipelineMaterial", "ciPipelineMaterialId", ciPipelineMaterialId, "gitHash", gitHash)

	// get ci-pipeline-material
	ciPipelineMaterial, err := handler.ciPipelineMaterialRepository.GetById(ciPipelineMaterialId)
	if err != nil {
		handler.Logger.Errorw("error while fetching ciPipelineMaterial", "err", err, "ciPipelineMaterialId", ciPipelineMaterialId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	// RBAC
	token := r.Header.Get("token")
	object := handler.enforcerUtil.GetAppRBACNameByAppId(ciPipelineMaterial.CiPipeline.AppId)
	if ok := handler.enforcer.Enforce(token, casbin.ResourceApplications, casbin.ActionGet, object); !ok {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusForbidden)
		return
	}
	// RBAC

	commitMetadataRequest := &gitSensor.CommitMetadataRequest{
		PipelineMaterialId: ciPipelineMaterialId,
		GitHash:            gitHash,
	}
	commit, err := handler.gitSensorClient.GetCommitMetadataForPipelineMaterial(context.Background(), commitMetadataRequest)
	if err != nil {
		handler.Logger.Errorw("error while fetching commit metadata for pipeline material", "commitMetadataRequest", commitMetadataRequest, "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, commit, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) FetchWorkflowDetails(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	vars := mux.Vars(r)
	appId, err := strconv.Atoi(vars["appId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	pipelineId, err := strconv.Atoi(vars["pipelineId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	buildId, err := strconv.Atoi(vars["workflowId"])
	if err != nil || buildId == 0 {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	handler.Logger.Infow("request payload, FetchWorkflowDetails", "appId", appId, "pipelineId", pipelineId, "buildId", buildId, "buildId", buildId)
	ciPipeline, err := handler.ciPipelineRepository.FindById(pipelineId)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	// RBAC
	token := r.Header.Get("token")
	object := handler.enforcerUtil.GetAppRBACNameByAppId(ciPipeline.AppId)
	ok := handler.enforcerUtil.CheckAppRbacForAppOrJob(token, object, casbin.ActionGet)
	if !ok {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusForbidden)
		return
	}
	// RBAC
	resp, err := handler.ciHandler.FetchWorkflowDetails(appId, pipelineId, buildId)
	if err != nil {
		handler.Logger.Errorw("service err, FetchWorkflowDetails", "err", err, "appId", appId, "pipelineId", pipelineId, "buildId", buildId, "buildId", buildId)
		if util.IsErrNoRows(err) {
			err = &util.ApiError{Code: "404", HttpStatusCode: http.StatusNotFound, UserMessage: "no workflow found"}
			common.WriteJsonResp(w, err, nil, http.StatusOK)
		} else {
			common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		}
		return
	}
	common.WriteJsonResp(w, err, resp, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) GetArtifactsForCiJob(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	vars := mux.Vars(r)
	pipelineId, err := strconv.Atoi(vars["pipelineId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	buildId, err := strconv.Atoi(vars["workflowId"])
	if err != nil || buildId == 0 {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	handler.Logger.Infow("request payload, GetArtifactsForCiJob", "pipelineId", pipelineId, "buildId", buildId, "buildId", buildId)
	ciPipeline, err := handler.ciPipelineRepository.FindById(pipelineId)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	// RBAC
	token := r.Header.Get("token")
	object := handler.enforcerUtil.GetAppRBACNameByAppId(ciPipeline.AppId)
	if ok := handler.enforcer.Enforce(token, casbin.ResourceApplications, casbin.ActionGet, object); !ok {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusForbidden)
		return
	}
	// RBAC
	resp, err := handler.ciHandler.FetchArtifactsForCiJob(buildId)
	if err != nil {
		handler.Logger.Errorw("service err, FetchArtifactsForCiJob", "err", err, "pipelineId", pipelineId, "buildId", buildId, "buildId", buildId)
		if util.IsErrNoRows(err) {
			err = &util.ApiError{Code: "404", HttpStatusCode: http.StatusNotFound, UserMessage: "no artifact found"}
			common.WriteJsonResp(w, err, nil, http.StatusOK)
		} else {
			common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		}
		return
	}
	common.WriteJsonResp(w, err, resp, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) GetAppMetadataListByEnvironment(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	token := r.Header.Get("token")
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	envId, err := strconv.Atoi(vars["envId"])
	if err != nil {
		handler.Logger.Errorw("request err, GetAppMetadataListByEnvironment", "err", err, "envId", envId)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	v := r.URL.Query()
	appIdsString := v.Get("appIds")
	var appIds []int
	if len(appIdsString) > 0 {
		appIdsSlices := strings.Split(appIdsString, ",")
		for _, appId := range appIdsSlices {
			id, err := strconv.Atoi(appId)
			if err != nil {
				common.WriteJsonResp(w, err, "please provide valid appIds", http.StatusBadRequest)
				return
			}
			appIds = append(appIds, id)
		}
	}

	resp, err := handler.pipelineBuilder.GetAppMetadataListByEnvironment(envId, appIds)
	if err != nil {
		handler.Logger.Errorw("service err, GetAppMetadataListByEnvironment", "err", err, "envId", envId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	// return all if user is super admin
	if isActionUserSuperAdmin := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionGet, "*"); isActionUserSuperAdmin {
		common.WriteJsonResp(w, err, resp, http.StatusOK)
		return
	}

	// get all the appIds
	appIds = make([]int, 0)
	appContainers := resp.Apps
	for _, appBean := range resp.Apps {
		appIds = append(appIds, appBean.AppId)
	}

	// get rbac objects for the appids
	rbacObjectsWithAppId := handler.enforcerUtil.GetRbacObjectsByAppIds(appIds)
	rbacObjects := make([]string, len(rbacObjectsWithAppId))
	itr := 0
	for _, object := range rbacObjectsWithAppId {
		rbacObjects[itr] = object
		itr++
	}
	// enforce rbac in batch
	rbacResult := handler.enforcer.EnforceInBatch(token, casbin.ResourceApplications, casbin.ActionGet, rbacObjects)
	// filter out rbac passed apps
	resp.Apps = make([]*pipelineConfigBean.AppMetaData, 0)
	for _, appBean := range appContainers {
		rbacObject := rbacObjectsWithAppId[appBean.AppId]
		if rbacResult[rbacObject] {
			resp.Apps = append(resp.Apps, appBean)
		}
	}
	resp.AppCount = len(resp.Apps)
	common.WriteJsonResp(w, err, resp, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) GetCiPipelineByEnvironment(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	token := r.Header.Get("token")
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	envId, err := strconv.Atoi(vars["envId"])
	if err != nil {
		handler.Logger.Errorw("request err, GetCdPipelines", "err", err, "envId", envId)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	v := r.URL.Query()
	appIdsString := v.Get("appIds")
	var appIds []int
	if len(appIdsString) > 0 {
		appIdsSlices := strings.Split(appIdsString, ",")
		for _, appId := range appIdsSlices {
			id, err := strconv.Atoi(appId)
			if err != nil {
				common.WriteJsonResp(w, err, "please provide valid appIds", http.StatusBadRequest)
				return
			}
			appIds = append(appIds, id)
		}
	}
	var appGroupId int
	appGroupIdStr := v.Get("appGroupId")
	if len(appGroupIdStr) > 0 {
		appGroupId, err = strconv.Atoi(appGroupIdStr)
		if err != nil {
			common.WriteJsonResp(w, err, "please provide valid appGroupId", http.StatusBadRequest)
			return
		}
	}

	request := resourceGroup.ResourceGroupingRequest{
		ParentResourceId:  envId,
		ResourceGroupId:   appGroupId,
		ResourceGroupType: resourceGroup.APP_GROUP,
		ResourceIds:       appIds,
		CheckAuthBatch:    handler.checkAuthBatch,
		UserId:            userId,
		Ctx:               r.Context(),
	}
	_, span := otel.Tracer("orchestrator").Start(r.Context(), "ciHandler.FetchCiPipelinesForAppGrouping")
	ciConf, err := handler.pipelineBuilder.GetCiPipelineByEnvironment(request, token)
	span.End()
	if err != nil {
		handler.Logger.Errorw("service err, GetCiPipeline", "err", err, "envId", envId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, ciConf, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) GetCiPipelineByEnvironmentMin(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	token := r.Header.Get("token")
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	envId, err := strconv.Atoi(vars["envId"])
	if err != nil {
		handler.Logger.Errorw("request err, GetCdPipelines", "err", err, "envId", envId)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	v := r.URL.Query()
	appIdsString := v.Get("appIds")
	var appIds []int
	if len(appIdsString) > 0 {
		appIdsSlices := strings.Split(appIdsString, ",")
		for _, appId := range appIdsSlices {
			id, err := strconv.Atoi(appId)
			if err != nil {
				common.WriteJsonResp(w, err, "please provide valid appIds", http.StatusBadRequest)
				return
			}
			appIds = append(appIds, id)
		}
	}
	var appGroupId int
	appGroupIdStr := v.Get("appGroupId")
	if len(appGroupIdStr) > 0 {
		appGroupId, err = strconv.Atoi(appGroupIdStr)
		if err != nil {
			common.WriteJsonResp(w, err, "please provide valid appGroupId", http.StatusBadRequest)
			return
		}
	}
	request := resourceGroup.ResourceGroupingRequest{
		ParentResourceId:  envId,
		ResourceGroupId:   appGroupId,
		ResourceGroupType: resourceGroup.APP_GROUP,
		ResourceIds:       appIds,
		CheckAuthBatch:    handler.checkAuthBatch,
		UserId:            userId,
		Ctx:               r.Context(),
	}
	_, span := otel.Tracer("orchestrator").Start(r.Context(), "ciHandler.FetchCiPipelinesForAppGrouping")
	results, err := handler.pipelineBuilder.GetCiPipelineByEnvironmentMin(request, token)
	span.End()
	if err != nil {
		handler.Logger.Errorw("service err, GetCiPipeline", "err", err, "envId", envId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, results, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) GetExternalCiByEnvironment(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	token := r.Header.Get("token")
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	envId, err := strconv.Atoi(vars["envId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	v := r.URL.Query()
	appIdsString := v.Get("appIds")
	var appIds []int
	if len(appIdsString) > 0 {
		appIdsSlices := strings.Split(appIdsString, ",")
		for _, appId := range appIdsSlices {
			id, err := strconv.Atoi(appId)
			if err != nil {
				common.WriteJsonResp(w, err, "please provide valid appIds", http.StatusBadRequest)
				return
			}
			appIds = append(appIds, id)
		}
	}

	var appGroupId int
	appGroupIdStr := v.Get("appGroupId")
	if len(appGroupIdStr) > 0 {
		appGroupId, err = strconv.Atoi(appGroupIdStr)
		if err != nil {
			common.WriteJsonResp(w, err, "please provide valid appGroupId", http.StatusBadRequest)
			return
		}
	}
	request := resourceGroup.ResourceGroupingRequest{
		ParentResourceId:  envId,
		ResourceGroupId:   appGroupId,
		ResourceGroupType: resourceGroup.APP_GROUP,
		ResourceIds:       appIds,
		CheckAuthBatch:    handler.checkAuthBatch,
		UserId:            userId,
		Ctx:               r.Context(),
	}
	_, span := otel.Tracer("orchestrator").Start(r.Context(), "ciHandler.FetchExternalCiPipelinesForAppGrouping")
	ciConf, err := handler.pipelineBuilder.GetExternalCiByEnvironment(request, token)
	span.End()
	if err != nil {
		handler.Logger.Errorw("service err, GetExternalCi", "err", err, "envId", envId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, ciConf, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) CreateUpdateImageTagging(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	token := r.Header.Get("token")
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	isSuperAdmin := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionCreate, "*")

	artifactId, err := strconv.Atoi(vars["artifactId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	externalCi, ciPipelineId, appId, err := handler.extractCipipelineMetaForImageTags(artifactId)
	if err != nil {
		handler.Logger.Errorw("error occurred in fetching extractCipipelineMetaForImageTags by artifact Id ", "err", err, "artifactId", artifactId)
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusInternalServerError)
		return
	}

	decoder := json.NewDecoder(r.Body)
	req := &types.ImageTaggingRequestDTO{}
	err = decoder.Decode(&req)
	if err != nil {
		handler.Logger.Errorw("request err, CreateUpdateImageTagging", "err", err, "payload", req)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	// RBAC
	if !isSuperAdmin {
		object := handler.enforcerUtil.GetAppRBACNameByAppId(appId)
		if ok := handler.enforcer.Enforce(token, casbin.ResourceApplications, casbin.ActionTrigger, object); !ok {
			common.WriteJsonResp(w, err, "Unauthorized User", http.StatusForbidden)
			return
		}
	}
	// RBAC
	// check prod env exists
	prodEnvExists := false
	if externalCi {
		prodEnvExists, err = handler.imageTaggingService.FindProdEnvExists(true, []int{ciPipelineId})
	} else {
		prodEnvExists, err = handler.imageTaggingService.GetProdEnvFromParentAndLinkedWorkflow(ciPipelineId)
	}
	if err != nil {
		handler.Logger.Errorw("error occurred in checking existence of prod environment ", "err", err, "ciPipelineId", ciPipelineId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	// not allowed to perform edit/save if no cd exists in prod env in the app_workflow
	if !prodEnvExists {
		handler.Logger.Errorw("save or edit operation not possible for this artifact", "err", nil, "artifactId", artifactId, "ciPipelineId", ciPipelineId)
		common.WriteJsonResp(w, errors.New("save or edit operation not possible for this artifact"), nil, http.StatusBadRequest)
		return
	}

	if !isSuperAdmin && len(req.HardDeleteTags) > 0 {
		errMsg := errors.New("user dont have permission to delete the tags")
		handler.Logger.Errorw("request err, CreateUpdateImageTagging", "err", errMsg, "payload", req)
		common.WriteJsonResp(w, errMsg, nil, http.StatusBadRequest)
		return
	}
	// validate request
	isValidRequest, err := handler.imageTaggingService.ValidateImageTaggingRequest(req, appId, artifactId)
	if err != nil || !isValidRequest {
		handler.Logger.Errorw("request validation failed", "error", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	req.ExternalCi = externalCi
	// pass it to the service layer
	resp, err := handler.imageTaggingService.CreateOrUpdateImageTagging(ciPipelineId, appId, artifactId, int(userId), req)
	if err != nil {
		if err.Error() == imageTagging.DuplicateTagsInAppError {
			appReleaseTags, err1 := handler.imageTaggingReadService.GetUniqueTagsByAppId(appId)
			if err1 != nil {
				handler.Logger.Errorw("error occurred in getting unique tags in app", "err", err1, "appId", appId)
				err = err1
			}
			resp = &types.ImageTaggingResponseDTO{}
			resp.AppReleaseTags = appReleaseTags
		}
		handler.Logger.Errorw("error occurred in creating/updating image tagging data", "err", err, "ciPipelineId", ciPipelineId)
		common.WriteJsonResp(w, err, resp, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, resp, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) GetImageTaggingData(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	token := r.Header.Get("token")
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	artifactId, err := strconv.Atoi(vars["artifactId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	externalCi, ciPipelineId, appId, err := handler.extractCipipelineMetaForImageTags(artifactId)
	if err != nil {
		handler.Logger.Errorw("error occurred in fetching extract ci pipeline metadata for ImageTags by artifact id", "err", err, "artifactId", artifactId)
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusInternalServerError)
		return
	}

	// RBAC
	object := handler.enforcerUtil.GetAppRBACNameByAppId(appId)
	if ok := handler.enforcer.Enforce(token, casbin.ResourceApplications, casbin.ActionTrigger, object); !ok {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusForbidden)
		return
	}
	// RBAC

	resp, err := handler.imageTaggingService.GetTagsData(ciPipelineId, appId, artifactId, externalCi)
	if err != nil {
		handler.Logger.Errorw("error occurred in fetching GetTagsData for artifact ", "err", err, "artifactId", artifactId, "ciPipelineId", ciPipelineId, "externalCi", externalCi, "appId", appId)
		common.WriteJsonResp(w, err, resp, http.StatusInternalServerError)
		return
	}

	common.WriteJsonResp(w, err, resp, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) GetImageTagList(w http.ResponseWriter, r *http.Request) {
	ctx := util2.NewRequestCtx(r.Context())
	queryParams := apiBean.ImageTagsQuery{}
	v := r.URL.Query()
	var decoder = schema.NewDecoder()
	decoder.IgnoreUnknownKeys(true)
	err := decoder.Decode(&queryParams, v)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	appIds, err := handler.appService.FindDevtronAppIdsByNames(queryParams.AppNames)
	if err != nil {
		handler.Logger.Errorw("error in fetching apps using appNames", "appNames", queryParams.AppNames, "error", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	resp := make([]string, 0)
	imageTags, err := handler.imageTaggingService.GetUniqueTagsByAppIds(ctx, appIds)
	if err != nil {
		handler.Logger.Errorw("error occurred in fetching GetTagsData for App Ids ", "err", err, "appIds", appIds)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	if len(imageTags) == 0 {
		common.WriteJsonResp(w, err, resp, http.StatusOK)
		return
	}
	// get rbac objects for the appIds
	rbacObjectsWithAppId := handler.enforcerUtil.GetRbacObjectsByAppIds(appIds)
	rbacObjects := make([]string, len(rbacObjectsWithAppId))
	i := 0
	for _, value := range rbacObjectsWithAppId { //iterating map
		rbacObjects[i] = value
		i++
	}
	// enforce rbac in batch
	token := r.Header.Get("token")
	rbacResult := handler.enforcer.EnforceInBatch(token, casbin.ResourceApplications, casbin.ActionGet, rbacObjects)
	// filter out rbac passed apps
	for _, imageTag := range imageTags {
		rbacObject := rbacObjectsWithAppId[imageTag.AppId]
		if rbacResult[rbacObject] {
			resp = append(resp, imageTag.TagName)
		}
	}
	common.WriteJsonResp(w, err, resp, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) extractCipipelineMetaForImageTags(artifactId int) (externalCi bool, ciPipelineId int, appId int, err error) {
	externalCi = false
	ciPipelineId = 0
	appId = 0
	ciArtifact, err := handler.ciArtifactRepository.Get(artifactId)
	if err != nil {
		handler.Logger.Errorw("Error in fetching ci artifact by ci artifact id", "err", err)
		return externalCi, ciPipelineId, appId, err
	}
	if ciArtifact.DataSource == repository.POST_CI {
		ciPipelineId = ciArtifact.ComponentId
		ciPipeline, err := handler.pipelineBuilder.GetCiPipelineById(ciPipelineId)
		if err != nil {
			handler.Logger.Errorw("no ci pipeline found for given artifact", "err", err, "artifactId", artifactId, "ciPipelineId", ciPipelineId)
			return externalCi, ciPipelineId, appId, err
		}
		appId = ciPipeline.AppId
	} else if ciArtifact.DataSource == repository.PRE_CD || ciArtifact.DataSource == repository.POST_CD {
		cdPipelineId := ciArtifact.ComponentId
		cdPipeline, err := handler.pipelineBuilder.GetCdPipelineById(cdPipelineId)
		if err != nil {
			handler.Logger.Errorw("no cd pipeline found for given artifact", "err", err, "artifactId", artifactId, "cdPipelineId", cdPipelineId)
			return externalCi, ciPipelineId, appId, err
		}
		ciPipelineId = cdPipeline.CiPipelineId
		appId = cdPipeline.AppId
	} else {
		ciPipeline, err := handler.ciPipelineRepository.GetCiPipelineByArtifactId(artifactId)
		var externalCiPipeline *pipelineConfig.ExternalCiPipeline
		if err != nil {
			if err == pg.ErrNoRows {
				handler.Logger.Infow("no ciPipeline found by artifact Id, fetching external ci-pipeline ", "artifactId", artifactId)
				externalCiPipeline, err = handler.ciPipelineRepository.GetExternalCiPipelineByArtifactId(artifactId)
			}
			if err != nil {
				handler.Logger.Errorw("error occurred in fetching ciPipeline/externalCiPipeline by artifact Id ", "err", err, "artifactId", artifactId)
				return externalCi, ciPipelineId, appId, err
			}
		}
		if ciPipeline.Id != 0 {
			ciPipelineId = ciPipeline.Id
			appId = ciPipeline.AppId
		} else {
			externalCi = true
			ciPipelineId = externalCiPipeline.Id
			appId = externalCiPipeline.AppId
		}
	}
	return externalCi, ciPipelineId, appId, nil
}

func (handler *PipelineConfigRestHandlerImpl) checkAppSpecificAccess(token, action string, appId int) (bool, error) {
	app, err := handler.pipelineBuilder.GetApp(appId)
	if err != nil {
		return false, err
	}
	if app.AppType != helper.CustomApp {
		return false, errors.New("only custom apps supported")
	}

	resourceName := handler.enforcerUtil.GetAppRBACName(app.AppName)
	if ok := handler.enforcer.Enforce(token, casbin.ResourceApplications, action, resourceName); !ok {
		return false, errors.New(string(bean.CI_PATCH_NOT_AUTHORIZED_MESSAGE))
	}
	return true, nil
}

func (handler *PipelineConfigRestHandlerImpl) GetSourceCiDownStreamFilters(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	vars := mux.Vars(r)
	ciPipelineId, err := strconv.Atoi(vars["ciPipelineId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	ciPipeline, err := handler.ciPipelineRepository.FindOneWithMinData(ciPipelineId)
	if util.IsErrNoRows(err) {
		common.WriteJsonResp(w, fmt.Errorf("invalid CiPipelineId %d", ciPipelineId), nil, http.StatusBadRequest)
		return
	} else if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	token := r.Header.Get("token")
	// RBAC enforcer applying
	resourceName := handler.enforcerUtil.GetAppRBACName(ciPipeline.App.AppName)
	if ok := handler.enforcer.Enforce(token, casbin.ResourceApplications, casbin.ActionGet, resourceName); !ok {
		common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
		return
	}
	// RBAC enforcer Ends
	resp, err := handler.ciCdPipelineOrchestrator.GetSourceCiDownStreamFilters(r.Context(), ciPipelineId)
	if err != nil {
		common.WriteJsonResp(w, fmt.Errorf("error getting environment info for given source Ci pipeline id"), "error getting environment info for given source Ci pipeline id", http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, resp, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) GetSourceCiDownStreamInfo(w http.ResponseWriter, r *http.Request) {
	decoder := schema.NewDecoder()
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	vars := mux.Vars(r)
	ciPipelineId, err := strconv.Atoi(vars["ciPipelineId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	req := &bean2.SourceCiDownStreamFilters{}
	err = decoder.Decode(req, r.URL.Query())
	if err != nil {
		handler.Logger.Errorw("request err, GetSourceCiDownStreamInfo", "err", err, "payload", req)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	// Convert searchKey to lowercase
	req.SearchKey = strings.ToLower(req.SearchKey)
	req.SortBy = pagination.AppName
	if req.Size == 0 {
		req.Size = 20
	}
	if len(req.SortOrder) == 0 {
		req.SortOrder = pagination.Asc
	}
	token := r.Header.Get("token")
	ciPipeline, err := handler.ciPipelineRepository.FindOneWithMinData(ciPipelineId)
	if util.IsErrNoRows(err) {
		common.WriteJsonResp(w, fmt.Errorf("invalid CiPipelineId %d", ciPipelineId), nil, http.StatusBadRequest)
		return
	} else if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	// RBAC enforcer applying
	resourceName := handler.enforcerUtil.GetAppRBACName(ciPipeline.App.AppName)
	if ok := handler.enforcer.Enforce(token, casbin.ResourceApplications, casbin.ActionGet, resourceName); !ok {
		common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
		return
	}
	// RBAC enforcer Ends
	linkedCIDetails, err := handler.ciCdPipelineOrchestrator.GetSourceCiDownStreamInfo(r.Context(), ciPipelineId, req)
	if err != nil {
		handler.Logger.Errorw("service err, PatchCiPipelines", "err", err, "ciPipelineId", ciPipelineId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, linkedCIDetails, http.StatusOK)
}
