/*
 * Copyright (c) 2024. Devtron Inc.
 */

package configure

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/devtron-labs/devtron/api/restHandler/app/pipeline/configure/adapter"
	"github.com/devtron-labs/devtron/api/restHandler/common"
	"github.com/devtron-labs/devtron/pkg/auth/authorisation/casbin"
	util3 "github.com/devtron-labs/devtron/pkg/auth/user/util"
	"github.com/devtron-labs/devtron/pkg/chart/adaptor"
	chartsBean "github.com/devtron-labs/devtron/pkg/chart/bean"
	"github.com/devtron-labs/devtron/pkg/policyGovernance/approvalConfig/bean"
	"github.com/devtron-labs/devtron/pkg/resourceQualifiers"
	globalUtil "github.com/devtron-labs/devtron/util"
	"golang.org/x/exp/maps"
	"net/http"
	"strconv"
	"time"
)

type DevtronAppDeploymentConfigRestHandlerEnt interface {
	ChangeDeploymentTemplateRef(w http.ResponseWriter, r *http.Request)
	ValidateAppOverride(w http.ResponseWriter, r *http.Request)
	GetStrategyWithStatus(w http.ResponseWriter, r *http.Request)
}

func (handler *PipelineConfigRestHandlerImpl) populatePipelineIdToEnvIdMap(request *chartsBean.TemplateRefChangeRequest) (*chartsBean.TemplateRefChangeRequest, error) {
	pipelineModels, err := handler.pipelineRepository.FindActiveByAppId(request.AppId)
	if err != nil {
		handler.Logger.Errorw("error in fetching pipelines by appId", "appId", request.AppId, "err", err)
		return request, err
	}
	envIds := make(map[int]int)
	for _, pipelineModel := range pipelineModels {
		envIds[pipelineModel.Id] = pipelineModel.EnvironmentId
	}
	request.PipelineIdToEnvIdMap = envIds
	return request, nil
}

func (handler *PipelineConfigRestHandlerImpl) ChangeDeploymentTemplateRef(w http.ResponseWriter, r *http.Request) {
	ctx := globalUtil.NewRequestCtx(r.Context())
	decoder := json.NewDecoder(r.Body)
	var payload chartsBean.TemplateRefChangeRequest
	err := decoder.Decode(&payload)
	if err != nil {
		handler.Logger.Errorw("error in decoding request body", "payload", payload, "err", err)
		common.WriteJsonResp(w, err, payload, http.StatusBadRequest)
		return
	}
	request, err := handler.deploymentTemplateValidationService.ValidateAndGetTemplateChangeRefRequest(&payload)
	if err != nil {
		handler.Logger.Errorw("validation error for base deployment chart change", "payload", request, "err", err)
		common.WriteJsonResp(w, err, request, http.StatusBadRequest)
		return
	}
	request, err = handler.populatePipelineIdToEnvIdMap(request)
	if err != nil {
		handler.Logger.Errorw("error in populating pipelineIdToEnvIdMap", "payload", request, "err", err)
		common.WriteJsonResp(w, err, request, http.StatusInternalServerError)
		return
	}
	handler.Logger.Infow("request payload, EnvConfigOverrideCreate", "payload", request)
	envIdToChartRefChangeRequest, err := handler.propertiesConfigService.ValidateAndGetChartRefChangeRequestForEnvIds(ctx, request, ctx.GetUserId())
	if err != nil {
		handler.Logger.Errorw("validation error for env deployment chart override change", "payload", request, "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	resourceName := handler.enforcerUtil.GetAppRBACNameByAppId(request.AppId)
	if ok := handler.enforcer.Enforce(ctx.GetToken(), casbin.ResourceApplications, casbin.ActionCreate, resourceName); !ok {
		common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
		return
	}
	if len(request.PipelineIdToEnvIdMap) != 0 {
		if ok := handler.isAuthorizedForAllEnvsInApp(request.AppId, maps.Values(request.PipelineIdToEnvIdMap), ctx.GetToken(), casbin.ActionUpdate); !ok {
			common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
			return
		}
	}
	if request.DryRun {
		common.WriteJsonResp(w, nil, "Template chart can be changed successfully!", http.StatusOK)
		return
	}
	_newCtx, cancel := context.WithCancel(ctx)
	defer cancel()
	if cn, ok := w.(http.CloseNotifier); ok {
		go func(done <-chan struct{}, closed <-chan bool) {
			select {
			case <-done:
			case <-closed:
				cancel()
			}
		}(_newCtx.Done(), cn.CloseNotify())
	}
	newCtx := globalUtil.NewRequestCtx(_newCtx)

	err = handler.processChartRefChangesAtBaseLevel(newCtx, request)
	if err != nil {
		handler.Logger.Errorw("service err, processChartRefChangesAtBaseLevel", "payload", request, "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	err = handler.processChartRefChangesAtEnvLevel(newCtx, request, envIdToChartRefChangeRequest)
	if err != nil {
		handler.Logger.Errorw("service err, processChartRefChangesAtEnvLevel", "payload", request, "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	common.WriteJsonResp(w, err, "Template chart has been changed successfully!", http.StatusOK)
	return
}

func (handler *PipelineConfigRestHandlerImpl) processChartRefChangesAtBaseLevel(newCtx *globalUtil.RequestCtx, request *chartsBean.TemplateRefChangeRequest) error {
	err := handler.chartService.ChangeApplicationTemplateChart(newCtx, newCtx.GetUserId(), request)
	if err != nil {
		handler.Logger.Errorw("service err, processChartRefChangesAtBaseLevel", "payload", request, "err", err)
		return err
	}
	postChartChangeReq := adaptor.GetPostChartChangeRequest(request, 0, bean.BASE_CONFIG_ENV_ID, newCtx.GetUserId())
	err = handler.chartService.HandlePostChartChangeOperations(newCtx, postChartChangeReq)
	if err != nil {
		handler.Logger.Errorw("service err, processChartRefChangesAtBaseLevel", "payload", request, "err", err)
		return err
	}
	return nil
}

func (handler *PipelineConfigRestHandlerImpl) processChartRefChangesAtEnvLevel(newCtx *globalUtil.RequestCtx, request *chartsBean.TemplateRefChangeRequest,
	envIdToChartRefChangeRequest map[int]*chartsBean.ChartRefChangeRequest) error {
	for pipelineId, envId := range request.PipelineIdToEnvIdMap {
		if chartRefChangeRequest, ok := envIdToChartRefChangeRequest[envId]; ok {
			_, err := handler.propertiesConfigService.ChangeChartRefForEnvConfigOverride(newCtx, chartRefChangeRequest, newCtx.GetUserId(), newCtx.GetToken())
			if err != nil {
				handler.Logger.Errorw("error in updating chartRefId in propertiesConfig", "appId", request.AppId, "envId", envId, "err", err)
				return err
			}
		}
		postChartChangeReq := adaptor.GetPostChartChangeRequest(request, pipelineId, envId, newCtx.GetUserId())
		err := handler.chartService.HandlePostChartChangeOperations(newCtx, postChartChangeReq)
		if err != nil {
			handler.Logger.Errorw("service err, processChartRefChangesAtEnvLevel", "payload", request, "pipelineId", pipelineId, "err", err)
			return err
		}
	}
	return nil
}

func (handler *PipelineConfigRestHandlerImpl) isAuthorizedForAllEnvsInApp(appId int, envIds []int, token, action string) bool {
	rbacObjectsMap, _ := handler.enforcerUtil.GetRbacObjectsByEnvIdsAndAppId(envIds, appId)
	envRbacResultMap := handler.enforcer.EnforceInBatch(token, casbin.ResourceEnvironment, action, maps.Values(rbacObjectsMap))
	for _, hasAccess := range envRbacResultMap {
		if !hasAccess {
			return false
		}
	}
	return true
}

func (handler *PipelineConfigRestHandlerImpl) ValidateAppOverride(w http.ResponseWriter, r *http.Request) {
	decoder := json.NewDecoder(r.Body)
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	var templateRequest *chartsBean.TemplateRequest
	err = decoder.Decode(&templateRequest)
	templateRequest.UserId = userId
	if err != nil {
		handler.Logger.Errorw("request err, ValidateAppOverride", "err", err, "payload", templateRequest)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	chartRefId := templateRequest.ChartRefId
	// VARIABLE_RESOLVE
	scope := resourceQualifiers.Scope{
		AppId: templateRequest.AppId,
	}
	if !templateRequest.SaveEligibleChanges {
		validate, err2 := handler.deploymentTemplateValidationService.DeploymentTemplateValidate(r.Context(), templateRequest.ValuesOverride, chartRefId, scope)
		if !validate {
			common.WriteJsonResp(w, err2, nil, http.StatusBadRequest)
			return
		}
	}

	handler.Logger.Infow("request payload, ValidateAppOverride", "payload", templateRequest)
	err = handler.validator.Struct(templateRequest)
	if err != nil {
		handler.Logger.Errorw("validation err, ValidateAppOverride", "err", err, "payload", templateRequest)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	token := r.Header.Get("token")
	app, err := handler.pipelineBuilder.GetApp(templateRequest.AppId)
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	resourceName := handler.enforcerUtil.GetAppRBACName(app.AppName)
	if ok := handler.enforcer.Enforce(token, casbin.ResourceApplications, casbin.ActionCreate, resourceName); !ok {
		common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), "Unauthorized User", http.StatusForbidden)
		return
	}

	ctx, cancel := context.WithCancel(r.Context())
	if cn, ok := w.(http.CloseNotifier); ok {
		go func(done <-chan struct{}, closed <-chan bool) {
			select {
			case <-done:
			case <-closed:
				cancel()
			}
		}(ctx.Done(), cn.CloseNotify())
	}
	validateResp, err := handler.chartService.ValidateAppOverride(templateRequest, token)
	if err != nil {
		handler.Logger.Errorw("service err, ValidateAppOverride", "err", err, "payload", templateRequest)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, validateResp, http.StatusOK)
}

func (handler *PipelineConfigRestHandlerImpl) GetStrategyWithStatus(w http.ResponseWriter, r *http.Request) {
	userId, err := handler.userAuthService.GetLoggedInUser(r)
	if userId == 0 || err != nil {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusUnauthorized)
		return
	}
	envId, err := common.ExtractIntQueryParam(w, r, "envId", 0)
	if err != nil {
		return
	}
	appIds, err := common.ExtractIntArrayQueryParam(w, r, "appIds")
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	fetchStatusString := r.URL.Query().Get("fetchStatus")
	var fetchStatus bool
	if len(fetchStatusString) != 0 {
		fetchStatus, err = strconv.ParseBool(fetchStatusString)
		if err != nil {
			common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
			return
		}
	}
	// RBAC enforcer applying
	token := r.Header.Get(common.TokenHeaderKey)
	filteredAppIds := handler.filterAuthorizedResourcesForGroup(appIds, envId, token)
	if len(filteredAppIds) == 0 {
		common.WriteJsonResp(w, err, "Unauthorized User", http.StatusForbidden)
		return
	}
	ctx, cancel := context.WithTimeout(r.Context(), 120*time.Second)
	defer cancel()

	isSuperAdmin := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionCreate, "*")
	userMetadata := util3.GetUserMetadata(r.Context(), userId, isSuperAdmin)
	request := adapter.BuildDeploymentStrategyRequest(ctx, filteredAppIds, envId, userMetadata, fetchStatus)
	resp, err := handler.deploymentPipelineStrategyService.GetStrategyForAppIdsAndEnvId(request)
	if err != nil {
		handler.Logger.Errorw("service err, GetRestartWorkloadData", "resp", resp, "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, resp, http.StatusOK)
}
