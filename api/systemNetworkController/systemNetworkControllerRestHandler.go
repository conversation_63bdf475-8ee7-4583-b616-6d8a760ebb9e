package systemNetworkController

import (
	"encoding/json"
	"github.com/devtron-labs/devtron/api/restHandler/common"
	"github.com/devtron-labs/devtron/api/systemNetworkController/bean"
	"github.com/devtron-labs/devtron/pkg/auth/authorisation/casbin"
	"github.com/devtron-labs/devtron/pkg/systemNetworkController"
	serviceBean "github.com/devtron-labs/devtron/pkg/systemNetworkController/bean"
	"github.com/devtron-labs/devtron/util"
	"github.com/gorilla/mux"
	"go.uber.org/zap"
	"gopkg.in/go-playground/validator.v9"
	"net/http"
	"strconv"
)

type systemNetworkControllerRestHandler interface {
	GetConfiguration(w http.ResponseWriter, r *http.Request)
	CreateOrUpdateConfiguration(w http.ResponseWriter, r *http.Request)
	DeleteConfiguration(w http.ResponseWriter, r *http.Request)
	GetAllSystemControllerInfo(w http.ResponseWriter, r *http.Request)
}

type systemNetworkControllerRestHandlerImpl struct {
	logger                  *zap.SugaredLogger
	validator               *validator.Validate
	enforcer                casbin.Enforcer
	sysNetControllerService systemNetworkController.SysNetControllerService
}

func NewSystemNetworkControllerRestHandlerImpl(logger *zap.SugaredLogger,
	validator *validator.Validate,
	enforcer casbin.Enforcer,
	sysNetControllerService systemNetworkController.SysNetControllerService) *systemNetworkControllerRestHandlerImpl {
	return &systemNetworkControllerRestHandlerImpl{
		logger:                  logger,
		validator:               validator,
		enforcer:                enforcer,
		sysNetControllerService: sysNetControllerService,
	}
}
func (handler *systemNetworkControllerRestHandlerImpl) GetConfiguration(w http.ResponseWriter, r *http.Request) {
	ctx := util.NewRequestCtx(r.Context())
	vars := mux.Vars(r)
	id, err := strconv.Atoi(vars["id"])
	if err != nil {
		handler.logger.Errorw("request err, GetConfiguration", "err", err, "id", id)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	token := r.Header.Get("token")
	//RBAC starts
	if ok := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionGet, "*"); !ok {
		handler.logger.Infow("user forbidden GetConfiguration", "userId", ctx.GetUserId())
		common.WriteJsonResp(w, nil, "Unauthorized User", http.StatusForbidden)
		return
	}
	//RBAC ends
	//Service Call Starts
	resp, err := handler.sysNetControllerService.GetConfig(id)
	if err != nil {
		handler.logger.Errorw("service error, GetConfiguration", "err", err, "id", id)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	//Service Call Ends
	common.WriteJsonResp(w, err, resp, http.StatusOK)
	return

}

func (handler *systemNetworkControllerRestHandlerImpl) CreateOrUpdateConfiguration(w http.ResponseWriter, r *http.Request) {
	ctx := util.NewRequestCtx(r.Context())

	// request decoding
	decoder := json.NewDecoder(r.Body)
	var reqBean serviceBean.SysNetControllerConfig
	err := decoder.Decode(&reqBean)
	if err != nil {
		handler.logger.Errorw("error in decoding request body", "err", err, "requestBody", r.Body)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	// req validation
	err = handler.validator.Struct(reqBean)
	if err != nil {
		handler.logger.Errorw("validation err, CreateOrUpdateConfiguration", "err", err, "reqBean", reqBean)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	token := r.Header.Get("token")
	userId := ctx.GetUserId()
	//RBAC starts
	if ok := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionGet, "*"); !ok {
		handler.logger.Infow("user forbidden CreateOrUpdateConfiguration", "userId", userId)
		common.WriteJsonResp(w, nil, "Unauthorized User", http.StatusForbidden)
		return
	}
	//RBAC ends

	//Service Call starts
	err = handler.sysNetControllerService.CreateOrUpdateConfig(ctx, reqBean, userId)
	if err != nil {
		handler.logger.Errorw("service error, CreateOrUpdateConfiguration", "err", err, "request", reqBean)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	//Service Call Ends
	common.WriteJsonResp(w, err, bean.SuccessMessage, http.StatusOK)
	return
}

func (handler *systemNetworkControllerRestHandlerImpl) DeleteConfiguration(w http.ResponseWriter, r *http.Request) {
	ctx := util.NewRequestCtx(r.Context())
	vars := mux.Vars(r)
	id, err := strconv.Atoi(vars["id"])
	if err != nil {
		handler.logger.Errorw("request err, DeleteConfiguration", "err", err, "id", id)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	token := r.Header.Get("token")
	userId := ctx.GetUserId()
	//RBAC starts
	if ok := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionGet, "*"); !ok {
		handler.logger.Infow("user forbidden DeleteConfiguration", "userId", userId)
		common.WriteJsonResp(w, nil, "Unauthorized User", http.StatusForbidden)
		return
	}
	//RBAC ends

	//Service Call
	err = handler.sysNetControllerService.DeleteConfig(id, userId)
	if err != nil {
		handler.logger.Errorw("service error, DeleteConfiguration", "err", err, "id", id)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	//Service Call Ends
	common.WriteJsonResp(w, err, bean.SuccessMessage, http.StatusOK)
	return
}

func (handler *systemNetworkControllerRestHandlerImpl) GetAllSystemControllerInfo(w http.ResponseWriter, r *http.Request) {
	ctx := util.NewRequestCtx(r.Context())
	token := r.Header.Get("token")
	//RBAC starts
	if ok := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionGet, "*"); !ok {
		handler.logger.Infow("user forbidden GetAllSystemControllerInfo", "userId", ctx.GetUserId())
		common.WriteJsonResp(w, nil, "Unauthorized User", http.StatusForbidden)
		return
	}
	//RBAC ends

	//Service Call starts
	resp, err := handler.sysNetControllerService.GetSysNetControllerInfo()
	if err != nil {
		handler.logger.Errorw("service error, GetAllSystemControllerInfo", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	//Service Call Ends
	common.WriteJsonResp(w, err, resp, http.StatusOK)
	return

}
