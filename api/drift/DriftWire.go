package drift

import (
	service "github.com/devtron-labs/devtron/pkg/drift/managedResourcesService"
	"github.com/google/wire"
)

var DriftWireSet = wire.NewSet(
	NewDriftRestHandlerImpl,
	wire.Bind(new(DriftRestHandler), new(*DriftRestHandlerImpl)),
	NewDriftRouterImpl,
	wire.Bind(new(DriftRouter), new(*DriftRouterImpl)),
	service.NewManagedResourceServiceImpl,
	wire.Bind(new(service.ManagedResourceService), new(*service.ManagedResourceServiceImpl)),
)
