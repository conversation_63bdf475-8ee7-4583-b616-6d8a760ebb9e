package drift

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/devtron-labs/devtron/api/restHandler/app/appList"
	"github.com/devtron-labs/devtron/api/restHandler/common"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/pkg/auth/authorisation/casbin"
	common2 "github.com/devtron-labs/devtron/pkg/deployment/common"
	"github.com/devtron-labs/devtron/pkg/deployment/resourceTree/devtronApp"
	service "github.com/devtron-labs/devtron/pkg/drift/managedResourcesService"
	"github.com/devtron-labs/devtron/pkg/generateManifest"
	"github.com/devtron-labs/devtron/pkg/k8s"
	application2 "github.com/devtron-labs/devtron/pkg/k8s/application"
	"github.com/devtron-labs/devtron/pkg/k8s/bean"
	"github.com/devtron-labs/devtron/util"
	"github.com/devtron-labs/devtron/util/rbac"
	"github.com/go-pg/pg"
	"github.com/gorilla/mux"
	errors2 "github.com/juju/errors"
	"go.uber.org/zap"
	"net/http"
	"strconv"
)

type DriftRestHandler interface {
	GetManagedResource(w http.ResponseWriter, r *http.Request)
	GetManagedResources(w http.ResponseWriter, r *http.Request)
}

type DriftRestHandlerImpl struct {
	// Add necessary dependencies here
	logger                *zap.SugaredLogger
	enforcer              casbin.Enforcer
	enforcerUtil          rbac.EnforcerUtil
	k8sCommonService      k8s.K8sCommonService
	k8sApplicationService application2.K8sApplicationService
	generateManifest.DeploymentTemplateService
	pipelineRepository            pipelineConfig.PipelineRepository
	deploymentConfigService       common2.DeploymentConfigService
	virtualEnvResourceTreeService devtronApp.VirtualEnvResourceTreeService
	appListingRestHandler         appList.AppListingRestHandler
	managedResourceService        service.ManagedResourceService
}

func NewDriftRestHandlerImpl(logger *zap.SugaredLogger, enforcer casbin.Enforcer, enforcerUtil rbac.EnforcerUtil, k8sCommonService k8s.K8sCommonService, k8sApplicationService application2.K8sApplicationService, service generateManifest.DeploymentTemplateService, pipelineRepository pipelineConfig.PipelineRepository, deploymentConfigService common2.DeploymentConfigService, virtualEnvResourceTreeService devtronApp.VirtualEnvResourceTreeService, appListingRestHandler appList.AppListingRestHandler, managedResourceService service.ManagedResourceService) *DriftRestHandlerImpl {
	return &DriftRestHandlerImpl{
		// Initialize dependencies here
		logger:                        logger,
		enforcer:                      enforcer,
		enforcerUtil:                  enforcerUtil,
		DeploymentTemplateService:     service,
		k8sCommonService:              k8sCommonService,
		k8sApplicationService:         k8sApplicationService,
		pipelineRepository:            pipelineRepository,
		deploymentConfigService:       deploymentConfigService,
		virtualEnvResourceTreeService: virtualEnvResourceTreeService,
		appListingRestHandler:         appListingRestHandler,
		managedResourceService:        managedResourceService,
	}
}

func (handler *DriftRestHandlerImpl) GetManagedResource(w http.ResponseWriter, r *http.Request) {
	decoder := json.NewDecoder(r.Body)
	var request bean.ResourceRequestBean
	err := decoder.Decode(&request)
	if err != nil {
		handler.logger.Errorw("error in decoding request body", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	token := r.Header.Get("token")

	//rbac validation for the apps requests
	if request.AppId != "" {
		ok, err := handler.k8sApplicationService.VerifyRbacForAppRequests(token, &request, r, casbin.ActionGet)
		if err != nil {
			common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
			return
		} else if !ok {
			common.WriteJsonResp(w, errors2.New("unauthorized"), nil, http.StatusForbidden)
			return
		}
	}
	// Invalid cluster id
	if request.ClusterId <= 0 {
		common.WriteJsonResp(w, errors.New("can not resource manifest as target cluster is not provided"), nil, http.StatusBadRequest)
		return
	}
	managedResource, err := handler.managedResourceService.GetManagedResource(r.Context(), &request, token)
	if err != nil {
		handler.logger.Errorw("error in getting managed resource", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, nil, managedResource, http.StatusOK)
}

func (handler *DriftRestHandlerImpl) GetManagedResources(w http.ResponseWriter, r *http.Request) {
	// Add implementation here
	vars := mux.Vars(r)
	token := r.Header.Get("token")
	appId, err := strconv.Atoi(vars["appId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}
	envId, err := strconv.Atoi(vars["envId"])
	if err != nil {
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	pipelines, err := handler.pipelineRepository.FindActiveByAppIdAndEnvironmentId(appId, envId)
	if errors.Is(err, pg.ErrNoRows) {
		common.WriteJsonResp(w, fmt.Errorf("no pipeline found for the given application and environment"), nil, http.StatusNotFound)
		return
	} else if err != nil {
		handler.logger.Errorw("error in fetching pipelines from db", "appId", appId, "envId", envId)
		common.WriteJsonResp(w, err, "error in fetching pipeline from database", http.StatusInternalServerError)
		return
	}

	if len(pipelines) == 0 {
		common.WriteJsonResp(w, fmt.Errorf("app deleted"), nil, http.StatusNotFound)
		return
	} else if len(pipelines) != 1 {
		common.WriteJsonResp(w, err, "multiple pipelines found for given application and environment", http.StatusBadRequest)
		return
	}

	cdPipeline := pipelines[0]
	object := handler.enforcerUtil.GetAppRBACNameByAppId(appId)
	if ok := handler.enforcer.Enforce(token, casbin.ResourceApplications, casbin.ActionGet, object); !ok {
		common.WriteJsonResp(w, fmt.Errorf("unauthorized user"), nil, http.StatusForbidden)
		return
	}
	if cdPipeline.Environment.IsVirtualEnvironment {
		resourceTreeResp, err := handler.virtualEnvResourceTreeService.GetResourceTreeForPipeline(cdPipeline.Id)
		if err != nil {
			handler.logger.Errorw("error in fetching resource tree", "err", err, "appId", appId, "envId", envId)
			common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
			return
		}
		common.WriteJsonResp(w, nil, resourceTreeResp, http.StatusOK)
		return
	}

	envDeploymentConfig, err := handler.deploymentConfigService.GetConfigForDevtronApps(nil, appId, envId)
	if err != nil {
		handler.logger.Errorw("error in fetching deployment config", "appId", appId, "envId", envId, "err", err)
		common.WriteJsonResp(w, fmt.Errorf("error in getting deployment config for env"), nil, http.StatusInternalServerError)
		return
	}

	ctx, cancel := context.WithCancel(r.Context())
	if cn, ok := w.(http.CloseNotifier); ok {
		go func(done <-chan struct{}, closed <-chan bool) {
			select {
			case <-done:
			case <-closed:
				cancel()
			}
		}(ctx.Done(), cn.CloseNotify())
	}
	defer cancel()
	isSuperAdmin := handler.enforcer.Enforce(token, casbin.ResourceGlobal, casbin.ActionGet, "*")
	ctx = util.SetSuperAdminInContext(ctx, isSuperAdmin)
	managedResources, err := handler.managedResourceService.GetManagedResources(ctx, appId, envId, cdPipeline, envDeploymentConfig)
	if err != nil {
		handler.logger.Errorw("service err, FetchManagedResources", "err", err, "appId", appId, "envId", envId)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	common.WriteJsonResp(w, err, managedResources, http.StatusOK)
}
