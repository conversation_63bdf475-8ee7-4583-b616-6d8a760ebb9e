/*
 * Copyright (c) 2024. Devtron Inc.
 */

package bean

import "github.com/devtron-labs/devtron/pkg/devtronResource/bean"

type GetQueryParams struct {
	Id         int    `schema:"id"`
	Identifier string `schema:"identifier"`
}

type QueryParams interface {
	GetQueryParams | GetResourceQueryParams | GetDependencyQueryParams |
		GetConfigOptionsQueryParams | GetTaskRunInfoQueryParams | GetDependencyOptionsQueryParams |
		GetResSummaryQueryParams | GetDepsOffendingQueryParams
}

type GetResourceQueryParams struct {
	GetQueryParams
	Component []bean.DtResUIComponent `schema:"component"`
}

type GetDependencyQueryParams struct {
	GetQueryParams
	IsLite           bool     `schema:"lite"`
	DependenciesInfo []string `schema:"dependencyInfo"`
}

type GetTaskRunInfoQueryParams struct {
	GetQueryParams
	IsLite                bool `schema:"lite"`
	LevelIndex            int  `schema:"levelIndex"`
	ShowAll               bool `schema:"showAll"`
	FetchWithUnmappedData bool `schema:"fetchWithUnmappedData"`
}

type GetDependencyOptionsQueryParams struct {
	GetQueryParams
	FilterCriteria            []string `schema:"filterCriteria"`
	Limit                     int      `schema:"limit"`
	Offset                    int      `schema:"offset"`
	FetchWithChildInheritance bool     `schema:"fetchWithChildInheritance"`
}

type ConfigOptionType = string

type GetConfigOptionsQueryParams struct {
	GetQueryParams
	DependenciesInfo []string         `schema:"dependencyInfo,required"`
	ConfigOption     ConfigOptionType `schema:"configOption"`
	FilterCriteria   []string         `schema:"filterCriteria"`
	SearchKey        string           `schema:"searchKey"`
	Limit            int              `schema:"limit"`
	Offset           int              `schema:"offset"`
}

type GetResSummaryQueryParams struct {
	GetQueryParams
}

type GetDepsOffendingQueryParams struct {
	GetQueryParams
}

const (
	ArtifactConfig ConfigOptionType = "artifact"
	CommitConfig   ConfigOptionType = "commit"
)

type GetResourceListQueryParams struct {
	IsLite         bool     `schema:"lite"`
	FetchChild     bool     `schema:"fetchChild"`
	FilterCriteria []string `schema:"filterCriteria"`
}

type GetHistoryQueryParams struct {
	FilterCriteria []string `schema:"filterCriteria"`
	OffSet         int      `schema:"offSet"`
	Limit          int      `schema:"limit"`
}

type GetHistoryConfigQueryParams struct {
	BaseConfigurationId  int      `schema:"baseConfigurationId"`
	HistoryComponent     string   `schema:"historyComponent"`
	HistoryComponentName string   `schema:"historyComponentName"`
	FilterCriteria       []string `schema:"filterCriteria"`
}

type GetObjListAuthorisedDtoMap map[string]map[int]bool //map of kind vs authorised ids map

const (
	RequestInvalidKindVersionErrMessage = "Invalid kind and version! Implementation not supported."
	PathParamKind                       = "kind"
	PathParamVersion                    = "version"
	QueryParamIsExposed                 = "onlyIsExposed"
	QueryParamLite                      = "lite"
	QueryParamIdentifier                = "identifier"
	QueryParamFetchChild                = "fetchChild"
	QueryParamId                        = "id"
	QueryParamName                      = "name"
	QueryParamComponent                 = "component"
	ResourceUpdateSuccessMessage        = "Resource object updated successfully."
	ResourceCreateSuccessMessage        = "Resource object created successfully."
	ResourceCloneSuccessMessage         = "Resource object cloned successfully."
	DependenciesUpdateSuccessMessage    = "Resource dependencies updated successfully."
)

const (
	GetResourceObjectListByKindAndVersionMethodName = "GetResourceObjectListByKindAndVersion"
	GetResourceObjectMethodName                     = "GetResourceObject"
	CreateResourceObjectMethodName                  = "CreateResourceObject"
	CloneResourceObjectMethodName                   = "CloneResourceObject"
	CreateOrUpdateResourceObjectMethodName          = "CreateOrUpdateResourceObject"
	PatchResourceObjectMethodName                   = "PatchResourceObject"
	DeleteResourceObjectMethodName                  = "DeleteResourceObject"
	GetResourceSummaryMethodName                    = "GetResourceSummary"
	GetResourceDependenciesMethodName               = "GetResourceDependencies"
	GetDependencyOptionsMethodName                  = "GetDependencyOptions"
	GetDependencyConfigOptionsMethodName            = "GetDependencyConfigOptions"
	CreateOrUpdateResourceDependenciesMethodName    = "CreateOrUpdateResourceDependencies"
	PatchResourceDependenciesMethodName             = "PatchResourceDependencies"
	ExecuteTaskMethodName                           = "ExecuteTask"
	GetTaskRunInfoWithFiltersMethodName             = "GetTaskRunInfoWithFilters"
	GetDependencyOffendingStateMethodName           = "GetDependencyOffendingState"
)

var ApiVsAllowedKindSubKindVersionMap = map[string][]bean.DtResTypeInternalReq{
	GetResourceObjectListByKindAndVersionMethodName: {
		{ResourceKind: bean.DevtronResourceRelease, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceReleaseTrack, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceTenant, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceInstallation, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceReleaseChannel, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceApplication, ResourceSubKind: bean.DevtronResourceDevtronApplication, ResourceVersion: bean.DevtronResourceVersion1},
	},
	GetResourceObjectMethodName: {
		{ResourceKind: bean.DevtronResourceRelease, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceReleaseTrack, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceApplication, ResourceSubKind: bean.DevtronResourceDevtronApplication, ResourceVersion: bean.DevtronResourceVersion1},
		{ResourceKind: bean.DevtronResourceApplication, ResourceSubKind: bean.DevtronResourceHelmApplication, ResourceVersion: bean.DevtronResourceVersion1},
		{ResourceKind: bean.DevtronResourceJob, ResourceVersion: bean.DevtronResourceVersion1},
		{ResourceKind: bean.DevtronResourceCluster, ResourceVersion: bean.DevtronResourceVersion1},
		{ResourceKind: bean.DevtronResourceTenant, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceInstallation, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceReleaseChannel, ResourceVersion: bean.DevtronResourceVersionAlpha1},
	},
	CreateResourceObjectMethodName: {
		{ResourceKind: bean.DevtronResourceRelease, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceReleaseTrack, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceTenant, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceInstallation, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceReleaseChannel, ResourceVersion: bean.DevtronResourceVersionAlpha1},
	},
	CloneResourceObjectMethodName: {
		{ResourceKind: bean.DevtronResourceRelease, ResourceVersion: bean.DevtronResourceVersionAlpha1},
	},
	CreateOrUpdateResourceObjectMethodName: {
		{ResourceKind: bean.DevtronResourceRelease, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceReleaseTrack, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceApplication, ResourceSubKind: bean.DevtronResourceDevtronApplication, ResourceVersion: bean.DevtronResourceVersion1},
		{ResourceKind: bean.DevtronResourceApplication, ResourceSubKind: bean.DevtronResourceHelmApplication, ResourceVersion: bean.DevtronResourceVersion1},
		{ResourceKind: bean.DevtronResourceJob, ResourceVersion: bean.DevtronResourceVersion1},
		{ResourceKind: bean.DevtronResourceCluster, ResourceVersion: bean.DevtronResourceVersion1},
		{ResourceKind: bean.DevtronResourceTenant, ResourceVersion: bean.DevtronResourceVersionAlpha1},
	},
	PatchResourceObjectMethodName: {
		{ResourceKind: bean.DevtronResourceRelease, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceReleaseTrack, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceTenant, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceInstallation, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceReleaseChannel, ResourceVersion: bean.DevtronResourceVersionAlpha1},
	},
	DeleteResourceObjectMethodName: {
		{ResourceKind: bean.DevtronResourceRelease, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceReleaseTrack, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceTenant, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceInstallation, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceReleaseChannel, ResourceVersion: bean.DevtronResourceVersionAlpha1},
	},
	GetResourceDependenciesMethodName: {
		{ResourceKind: bean.DevtronResourceRelease, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceReleaseTrack, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceApplication, ResourceSubKind: bean.DevtronResourceDevtronApplication, ResourceVersion: bean.DevtronResourceVersion1},
		{ResourceKind: bean.DevtronResourceApplication, ResourceSubKind: bean.DevtronResourceHelmApplication, ResourceVersion: bean.DevtronResourceVersion1},
		{ResourceKind: bean.DevtronResourceTenant, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceInstallation, ResourceVersion: bean.DevtronResourceVersionAlpha1},
	},
	GetDependencyOptionsMethodName: {
		{ResourceKind: bean.DevtronResourceRelease, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceReleaseTrack, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceInstallation, ResourceVersion: bean.DevtronResourceVersionAlpha1},
	},
	GetDependencyConfigOptionsMethodName: {
		{ResourceKind: bean.DevtronResourceRelease, ResourceVersion: bean.DevtronResourceVersionAlpha1},
	},
	CreateOrUpdateResourceDependenciesMethodName: {
		{ResourceKind: bean.DevtronResourceRelease, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceReleaseTrack, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceApplication, ResourceSubKind: bean.DevtronResourceDevtronApplication, ResourceVersion: bean.DevtronResourceVersion1},
		{ResourceKind: bean.DevtronResourceApplication, ResourceSubKind: bean.DevtronResourceHelmApplication, ResourceVersion: bean.DevtronResourceVersion1},
		{ResourceKind: bean.DevtronResourceTenant, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceInstallation, ResourceVersion: bean.DevtronResourceVersionAlpha1},
	},
	PatchResourceDependenciesMethodName: {
		{ResourceKind: bean.DevtronResourceRelease, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceReleaseTrack, ResourceVersion: bean.DevtronResourceVersionAlpha1},
	},
	ExecuteTaskMethodName: {
		{ResourceKind: bean.DevtronResourceRelease, ResourceVersion: bean.DevtronResourceVersionAlpha1},
	},
	GetTaskRunInfoWithFiltersMethodName: {
		{ResourceKind: bean.DevtronResourceRelease, ResourceVersion: bean.DevtronResourceVersionAlpha1},
	},
	GetResourceSummaryMethodName: {
		{ResourceKind: bean.DevtronResourceReleaseTrack, ResourceVersion: bean.DevtronResourceVersionAlpha1},
		{ResourceKind: bean.DevtronResourceRelease, ResourceVersion: bean.DevtronResourceVersionAlpha1},
	},
	GetDependencyOffendingStateMethodName: {
		{ResourceKind: bean.DevtronResourceRelease, ResourceVersion: bean.DevtronResourceVersionAlpha1},
	},
}
