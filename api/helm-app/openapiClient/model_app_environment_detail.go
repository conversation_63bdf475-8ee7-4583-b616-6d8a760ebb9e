/*
Devtron Labs

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

API version: 1.0.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package openapi

import (
	"encoding/json"
)

// AppEnvironmentDetail struct for AppEnvironmentDetail
type AppEnvironmentDetail struct {
	// name of the environemnt
	EnvironmentName *string `json:"environmentName,omitempty"`
	// id in which app is deployed
	EnvironmentId *int32 `json:"environmentId,omitempty"`
	// namespace corresponding to the environemnt
	Namespace *string `json:"namespace,omitempty"`
	// if given environemnt is marked as production or not, nullable
	IsPrduction *bool `json:"isPrduction,omitempty"`
	// cluster corresponding to the environemt where application is deployed
	ClusterName *string `json:"clusterName,omitempty"`
	// clusterId corresponding to the environemt where application is deployed
	ClusterId *int32 `json:"clusterId,omitempty"`
}

// NewAppEnvironmentDetail instantiates a new AppEnvironmentDetail object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewAppEnvironmentDetail() *AppEnvironmentDetail {
	this := AppEnvironmentDetail{}
	return &this
}

// NewAppEnvironmentDetailWithDefaults instantiates a new AppEnvironmentDetail object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewAppEnvironmentDetailWithDefaults() *AppEnvironmentDetail {
	this := AppEnvironmentDetail{}
	return &this
}

// GetEnvironmentName returns the EnvironmentName field value if set, zero value otherwise.
func (o *AppEnvironmentDetail) GetEnvironmentName() string {
	if o == nil || o.EnvironmentName == nil {
		var ret string
		return ret
	}
	return *o.EnvironmentName
}

// GetEnvironmentNameOk returns a tuple with the EnvironmentName field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AppEnvironmentDetail) GetEnvironmentNameOk() (*string, bool) {
	if o == nil || o.EnvironmentName == nil {
		return nil, false
	}
	return o.EnvironmentName, true
}

// HasEnvironmentName returns a boolean if a field has been set.
func (o *AppEnvironmentDetail) HasEnvironmentName() bool {
	if o != nil && o.EnvironmentName != nil {
		return true
	}

	return false
}

// SetEnvironmentName gets a reference to the given string and assigns it to the EnvironmentName field.
func (o *AppEnvironmentDetail) SetEnvironmentName(v string) {
	o.EnvironmentName = &v
}

// GetEnvironmentId returns the EnvironmentId field value if set, zero value otherwise.
func (o *AppEnvironmentDetail) GetEnvironmentId() int32 {
	if o == nil || o.EnvironmentId == nil {
		var ret int32
		return ret
	}
	return *o.EnvironmentId
}

// GetEnvironmentIdOk returns a tuple with the EnvironmentId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AppEnvironmentDetail) GetEnvironmentIdOk() (*int32, bool) {
	if o == nil || o.EnvironmentId == nil {
		return nil, false
	}
	return o.EnvironmentId, true
}

// HasEnvironmentId returns a boolean if a field has been set.
func (o *AppEnvironmentDetail) HasEnvironmentId() bool {
	if o != nil && o.EnvironmentId != nil {
		return true
	}

	return false
}

// SetEnvironmentId gets a reference to the given int32 and assigns it to the EnvironmentId field.
func (o *AppEnvironmentDetail) SetEnvironmentId(v int32) {
	o.EnvironmentId = &v
}

// GetNamespace returns the Namespace field value if set, zero value otherwise.
func (o *AppEnvironmentDetail) GetNamespace() string {
	if o == nil || o.Namespace == nil {
		var ret string
		return ret
	}
	return *o.Namespace
}

// GetNamespaceOk returns a tuple with the Namespace field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AppEnvironmentDetail) GetNamespaceOk() (*string, bool) {
	if o == nil || o.Namespace == nil {
		return nil, false
	}
	return o.Namespace, true
}

// HasNamespace returns a boolean if a field has been set.
func (o *AppEnvironmentDetail) HasNamespace() bool {
	if o != nil && o.Namespace != nil {
		return true
	}

	return false
}

// SetNamespace gets a reference to the given string and assigns it to the Namespace field.
func (o *AppEnvironmentDetail) SetNamespace(v string) {
	o.Namespace = &v
}

// GetIsPrduction returns the IsPrduction field value if set, zero value otherwise.
func (o *AppEnvironmentDetail) GetIsPrduction() bool {
	if o == nil || o.IsPrduction == nil {
		var ret bool
		return ret
	}
	return *o.IsPrduction
}

// GetIsPrductionOk returns a tuple with the IsPrduction field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AppEnvironmentDetail) GetIsPrductionOk() (*bool, bool) {
	if o == nil || o.IsPrduction == nil {
		return nil, false
	}
	return o.IsPrduction, true
}

// HasIsPrduction returns a boolean if a field has been set.
func (o *AppEnvironmentDetail) HasIsPrduction() bool {
	if o != nil && o.IsPrduction != nil {
		return true
	}

	return false
}

// SetIsPrduction gets a reference to the given bool and assigns it to the IsPrduction field.
func (o *AppEnvironmentDetail) SetIsPrduction(v bool) {
	o.IsPrduction = &v
}

// GetClusterName returns the ClusterName field value if set, zero value otherwise.
func (o *AppEnvironmentDetail) GetClusterName() string {
	if o == nil || o.ClusterName == nil {
		var ret string
		return ret
	}
	return *o.ClusterName
}

// GetClusterNameOk returns a tuple with the ClusterName field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AppEnvironmentDetail) GetClusterNameOk() (*string, bool) {
	if o == nil || o.ClusterName == nil {
		return nil, false
	}
	return o.ClusterName, true
}

// HasClusterName returns a boolean if a field has been set.
func (o *AppEnvironmentDetail) HasClusterName() bool {
	if o != nil && o.ClusterName != nil {
		return true
	}

	return false
}

// SetClusterName gets a reference to the given string and assigns it to the ClusterName field.
func (o *AppEnvironmentDetail) SetClusterName(v string) {
	o.ClusterName = &v
}

// GetClusterId returns the ClusterId field value if set, zero value otherwise.
func (o *AppEnvironmentDetail) GetClusterId() int32 {
	if o == nil || o.ClusterId == nil {
		var ret int32
		return ret
	}
	return *o.ClusterId
}

// GetClusterIdOk returns a tuple with the ClusterId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AppEnvironmentDetail) GetClusterIdOk() (*int32, bool) {
	if o == nil || o.ClusterId == nil {
		return nil, false
	}
	return o.ClusterId, true
}

// HasClusterId returns a boolean if a field has been set.
func (o *AppEnvironmentDetail) HasClusterId() bool {
	if o != nil && o.ClusterId != nil {
		return true
	}

	return false
}

// SetClusterId gets a reference to the given int32 and assigns it to the ClusterId field.
func (o *AppEnvironmentDetail) SetClusterId(v int32) {
	o.ClusterId = &v
}

func (o AppEnvironmentDetail) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.EnvironmentName != nil {
		toSerialize["environmentName"] = o.EnvironmentName
	}
	if o.EnvironmentId != nil {
		toSerialize["environmentId"] = o.EnvironmentId
	}
	if o.Namespace != nil {
		toSerialize["namespace"] = o.Namespace
	}
	if o.IsPrduction != nil {
		toSerialize["isPrduction"] = o.IsPrduction
	}
	if o.ClusterName != nil {
		toSerialize["clusterName"] = o.ClusterName
	}
	if o.ClusterId != nil {
		toSerialize["clusterId"] = o.ClusterId
	}
	return json.Marshal(toSerialize)
}

type NullableAppEnvironmentDetail struct {
	value *AppEnvironmentDetail
	isSet bool
}

func (v NullableAppEnvironmentDetail) Get() *AppEnvironmentDetail {
	return v.value
}

func (v *NullableAppEnvironmentDetail) Set(val *AppEnvironmentDetail) {
	v.value = val
	v.isSet = true
}

func (v NullableAppEnvironmentDetail) IsSet() bool {
	return v.isSet
}

func (v *NullableAppEnvironmentDetail) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableAppEnvironmentDetail(val *AppEnvironmentDetail) *NullableAppEnvironmentDetail {
	return &NullableAppEnvironmentDetail{value: val, isSet: true}
}

func (v NullableAppEnvironmentDetail) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableAppEnvironmentDetail) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
