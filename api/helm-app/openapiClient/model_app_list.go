/*
Devtron Labs

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

API version: 1.0.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package openapi

import (
	"encoding/json"
)

// AppList struct for AppList
type AppList struct {
	// clusters to which result corresponds
	ClusterIds *[]int32 `json:"clusterIds,omitempty"`
	// application type inside the array
	ApplicationType *string `json:"applicationType,omitempty"`
	// if data fetch for that cluster produced error
	Errored *bool `json:"errored,omitempty"`
	// error msg if client failed to fetch
	ErrorMsg *string `json:"errorMsg,omitempty"`
	// all helm app list, EA+ devtronapp
	HelmApps *[]HelmApp `json:"helmApps,omitempty"`
	// all helm app list, EA+ devtronapp
	DevtronApps *[]DevtronApp `json:"devtronApps,omitempty"`
}

// NewAppList instantiates a new AppList object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewAppList() *AppList {
	this := AppList{}
	return &this
}

// NewAppListWithDefaults instantiates a new AppList object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewAppListWithDefaults() *AppList {
	this := AppList{}
	return &this
}

// GetClusterIds returns the ClusterIds field value if set, zero value otherwise.
func (o *AppList) GetClusterIds() []int32 {
	if o == nil || o.ClusterIds == nil {
		var ret []int32
		return ret
	}
	return *o.ClusterIds
}

// GetClusterIdsOk returns a tuple with the ClusterIds field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AppList) GetClusterIdsOk() (*[]int32, bool) {
	if o == nil || o.ClusterIds == nil {
		return nil, false
	}
	return o.ClusterIds, true
}

// HasClusterIds returns a boolean if a field has been set.
func (o *AppList) HasClusterIds() bool {
	if o != nil && o.ClusterIds != nil {
		return true
	}

	return false
}

// SetClusterIds gets a reference to the given []int32 and assigns it to the ClusterIds field.
func (o *AppList) SetClusterIds(v []int32) {
	o.ClusterIds = &v
}

// GetApplicationType returns the ApplicationType field value if set, zero value otherwise.
func (o *AppList) GetApplicationType() string {
	if o == nil || o.ApplicationType == nil {
		var ret string
		return ret
	}
	return *o.ApplicationType
}

// GetApplicationTypeOk returns a tuple with the ApplicationType field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AppList) GetApplicationTypeOk() (*string, bool) {
	if o == nil || o.ApplicationType == nil {
		return nil, false
	}
	return o.ApplicationType, true
}

// HasApplicationType returns a boolean if a field has been set.
func (o *AppList) HasApplicationType() bool {
	if o != nil && o.ApplicationType != nil {
		return true
	}

	return false
}

// SetApplicationType gets a reference to the given string and assigns it to the ApplicationType field.
func (o *AppList) SetApplicationType(v string) {
	o.ApplicationType = &v
}

// GetErrored returns the Errored field value if set, zero value otherwise.
func (o *AppList) GetErrored() bool {
	if o == nil || o.Errored == nil {
		var ret bool
		return ret
	}
	return *o.Errored
}

// GetErroredOk returns a tuple with the Errored field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AppList) GetErroredOk() (*bool, bool) {
	if o == nil || o.Errored == nil {
		return nil, false
	}
	return o.Errored, true
}

// HasErrored returns a boolean if a field has been set.
func (o *AppList) HasErrored() bool {
	if o != nil && o.Errored != nil {
		return true
	}

	return false
}

// SetErrored gets a reference to the given bool and assigns it to the Errored field.
func (o *AppList) SetErrored(v bool) {
	o.Errored = &v
}

// GetErrorMsg returns the ErrorMsg field value if set, zero value otherwise.
func (o *AppList) GetErrorMsg() string {
	if o == nil || o.ErrorMsg == nil {
		var ret string
		return ret
	}
	return *o.ErrorMsg
}

// GetErrorMsgOk returns a tuple with the ErrorMsg field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AppList) GetErrorMsgOk() (*string, bool) {
	if o == nil || o.ErrorMsg == nil {
		return nil, false
	}
	return o.ErrorMsg, true
}

// HasErrorMsg returns a boolean if a field has been set.
func (o *AppList) HasErrorMsg() bool {
	if o != nil && o.ErrorMsg != nil {
		return true
	}

	return false
}

// SetErrorMsg gets a reference to the given string and assigns it to the ErrorMsg field.
func (o *AppList) SetErrorMsg(v string) {
	o.ErrorMsg = &v
}

// GetHelmApps returns the HelmApps field value if set, zero value otherwise.
func (o *AppList) GetHelmApps() []HelmApp {
	if o == nil || o.HelmApps == nil {
		var ret []HelmApp
		return ret
	}
	return *o.HelmApps
}

// GetHelmAppsOk returns a tuple with the HelmApps field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AppList) GetHelmAppsOk() (*[]HelmApp, bool) {
	if o == nil || o.HelmApps == nil {
		return nil, false
	}
	return o.HelmApps, true
}

// HasHelmApps returns a boolean if a field has been set.
func (o *AppList) HasHelmApps() bool {
	if o != nil && o.HelmApps != nil {
		return true
	}

	return false
}

// SetHelmApps gets a reference to the given []HelmApp and assigns it to the HelmApps field.
func (o *AppList) SetHelmApps(v []HelmApp) {
	o.HelmApps = &v
}

// GetDevtronApps returns the DevtronApps field value if set, zero value otherwise.
func (o *AppList) GetDevtronApps() []DevtronApp {
	if o == nil || o.DevtronApps == nil {
		var ret []DevtronApp
		return ret
	}
	return *o.DevtronApps
}

// GetDevtronAppsOk returns a tuple with the DevtronApps field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *AppList) GetDevtronAppsOk() (*[]DevtronApp, bool) {
	if o == nil || o.DevtronApps == nil {
		return nil, false
	}
	return o.DevtronApps, true
}

// HasDevtronApps returns a boolean if a field has been set.
func (o *AppList) HasDevtronApps() bool {
	if o != nil && o.DevtronApps != nil {
		return true
	}

	return false
}

// SetDevtronApps gets a reference to the given []DevtronApp and assigns it to the DevtronApps field.
func (o *AppList) SetDevtronApps(v []DevtronApp) {
	o.DevtronApps = &v
}

func (o AppList) MarshalJSON() ([]byte, error) {
	toSerialize := map[string]interface{}{}
	if o.ClusterIds != nil {
		toSerialize["clusterIds"] = o.ClusterIds
	}
	if o.ApplicationType != nil {
		toSerialize["applicationType"] = o.ApplicationType
	}
	if o.Errored != nil {
		toSerialize["errored"] = o.Errored
	}
	if o.ErrorMsg != nil {
		toSerialize["errorMsg"] = o.ErrorMsg
	}
	if o.HelmApps != nil {
		toSerialize["helmApps"] = o.HelmApps
	}
	if o.DevtronApps != nil {
		toSerialize["devtronApps"] = o.DevtronApps
	}
	return json.Marshal(toSerialize)
}

type NullableAppList struct {
	value *AppList
	isSet bool
}

func (v NullableAppList) Get() *AppList {
	return v.value
}

func (v *NullableAppList) Set(val *AppList) {
	v.value = val
	v.isSet = true
}

func (v NullableAppList) IsSet() bool {
	return v.isSet
}

func (v *NullableAppList) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableAppList(val *AppList) *NullableAppList {
	return &NullableAppList{value: val, isSet: true}
}

func (v NullableAppList) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableAppList) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
