# This is the global Nginx configuration file (typically contains user, worker_processes, http block, etc.)
# /etc/nginx/nginx.conf

# user nginx;
worker_processes auto;
error_log  /var/log/nginx/error.log notice;
pid        /tmp/nginx.pid;
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    access_log  /var/log/nginx/access.log;
    error_log   /var/log/nginx/error.log;

    sendfile        on;
    keepalive_timeout  65;

    include /etc/nginx/http.d/*.conf;
}
