# This contains a server block defining how a specific domain/route should be handled.
# nginx-default.conf

# To allow a non-root container process to bind to privileged ports (e.g., 80 or 443),
# you need to add the NET_BIND_SERVICE capability to the security context:
#
#   securityContext:
#     allowPrivilegeEscalation: false
#     capabilities:
#       add:
#         - NET_BIND_SERVICE
#       drop:
#         - ALL
#
# Since adding capabilities may reduce security or require extra setup in Kubernetes,
# it's simpler and safer to use an unprivileged port like 8080 for your app.


server {
    listen       8080;
    listen       [::]:8080;
    root         /app;
    server_name localhost;


    location / {
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_pass http://127.0.0.1:3000;
    }
    
}