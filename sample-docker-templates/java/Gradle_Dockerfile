################################# Build Container ###############################

# Use latest Gradle with JDK 21 and Alpine for minimal size and speed
FROM gradle:8.13.0-jdk21-alpine AS build

# Set working directory and ensure proper permissions
COPY --chown=gradle:gradle . /home/<USER>/src

WORKDIR /home/<USER>/src

# Build the application without using the Gradle daemon
RUN gradle build --no-daemon

################################# Prod Container #################################

# Use a minimal JDK base image for production
FROM eclipse-temurin:21-jdk-jammy

# Create a non-root user to run the app securely
RUN addgroup --gid 2002 nonroot && \
    adduser --gid 2002 --uid 2002 nonroot --disabled-password --gecos ""

# Set the working directory
WORKDIR /app

# Copy the JAR file from the build stage
COPY --from=build /home/<USER>/src/build/libs/*.jar /app/demo.jar

# Set ownership of the jar file
RUN chown nonroot:nonroot /app/demo.jar

# Switch to non-root user
USER nonroot

# Expose the application port
EXPOSE 8080

# Run the jar file
CMD ["java", "-jar", "/app/demo.jar"]
