# Using latest stable PHP with Apache (8.3)
FROM php:8.3-apache

# Enable apache mod_rewrite
RUN a2enmod rewrite

# Create non-root user with UID/GID 2002 and set ownership
RUN groupadd -g 2002 nonroot && \
    useradd -u 2002 -g nonroot -m nonroot && \
    chown -R nonroot:www-data /var/www/html

# Copy application source code
COPY --chown=nonroot:www-data . /var/www/html/

# Switch to non-root user for security
USER nonroot

# Apache runs as www-data internally, so no need to restart here
# CMD is inherited from base image and will run apache2 in foreground by default
