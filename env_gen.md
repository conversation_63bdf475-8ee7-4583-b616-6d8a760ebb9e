

## CD Related Environment Variables
| Key   | Type     | Default Value     | Description       | Example       | Deprecated       |
|-------|----------|-------------------|-------------------|-----------------------|------------------|
 | ARGO_APP_MANUAL_SYNC_TIME | int |3 | retry argocd app manual sync if the timeline is stuck in ARGOCD_SYNC_INITIATED state for more than this defined time (in mins) |  | false |
 | CD_FLUX_PIPELINE_STATUS_CRON_TIME | string |*/2 * * * * | Cron time to check the pipeline status for flux cd pipeline |  | false |
 | CD_HELM_PIPELINE_STATUS_CRON_TIME | string |*/2 * * * * | Cron time to check the pipeline status  |  | false |
 | CD_PIPELINE_STATUS_CRON_TIME | string |*/2 * * * * | Cron time for CD pipeline status |  | false |
 | CD_PIPELINE_STATUS_TIMEOUT_DURATION | string |20 | Timeout for CD pipeline to get healthy |  | false |
 | DEPLOY_STATUS_CRON_GET_PIPELINE_DEPLOYED_WITHIN_HOURS | int |12 | This flag is used to fetch the deployment status of the application. It retrieves the status of deployments that occurred between 12 hours and 10 minutes prior to the current time. It fetches non-terminal statuses. |  | false |
 | DEVTRON_CHART_ARGO_CD_INSTALL_REQUEST_TIMEOUT | int |1 | Context timeout for gitops concurrent async deployments |  | false |
 | DEVTRON_CHART_INSTALL_REQUEST_TIMEOUT | int |6 | Context timeout for no gitops concurrent async deployments |  | false |
 | EXPOSE_CD_METRICS | bool |false |  |  | false |
 | FEATURE_MIGRATE_ARGOCD_APPLICATION_ENABLE | bool |false | enable migration of external argocd application to devtron pipeline |  | false |
 | FEATURE_MIGRATE_FLUX_APPLICATION_ENABLE | bool |false | enable flux application services |  | false |
 | FLUX_CD_PIPELINE_STATUS_CHECK_ELIGIBLE_TIME | string |120 | eligible time for checking flux app status periodically and update in db, value is in seconds., default is 120, if wfr is updated within configured time i.e. FLUX_CD_PIPELINE_STATUS_CHECK_ELIGIBLE_TIME then do not include for this cron cycle. |  | false |
 | HELM_PIPELINE_STATUS_CHECK_ELIGIBLE_TIME | string |120 | eligible time for checking helm app status periodically and update in db, value is in seconds., default is 120, if wfr is updated within configured time i.e. HELM_PIPELINE_STATUS_CHECK_ELIGIBLE_TIME then do not include for this cron cycle. |  | false |
 | IS_INTERNAL_USE | bool |true | If enabled then cd pipeline and helm apps will not need the deployment app type mandatorily. Couple this flag with HIDE_GITOPS_OR_HELM_OPTION (in Dashborad) and if gitops is configured and allowed for the env, pipeline/ helm app will gitops else no-gitops. |  | false |
 | MIGRATE_DEPLOYMENT_CONFIG_DATA | bool |false | migrate deployment config data from charts table to deployment_config table |  | false |
 | PIPELINE_DEGRADED_TIME | string |10 | Time to mark a pipeline degraded if not healthy in defined time |  | false |
 | REVISION_HISTORY_LIMIT_DEVTRON_APP | int |1 | Count for devtron application rivision history |  | false |
 | REVISION_HISTORY_LIMIT_EXTERNAL_HELM_APP | int |0 | Count for external helm application rivision history |  | false |
 | REVISION_HISTORY_LIMIT_HELM_APP | int |1 | To set the history limit for the helm app being deployed through devtron |  | false |
 | REVISION_HISTORY_LIMIT_LINKED_HELM_APP | int |15 |  |  | false |
 | RUN_HELM_INSTALL_IN_ASYNC_MODE_HELM_APPS | bool |false | If enabled devtron will deploy helm apps in async mode. Make sure to couple this flag with RUN_HELM_INSTALL_IN_ASYNC_MODE (in Kubelink). |  | false |
 | SHOULD_CHECK_NAMESPACE_ON_CLONE | bool |false | should we check if namespace exists or not while cloning app |  | false |
 | USE_DEPLOYMENT_CONFIG_DATA | bool |false | use deployment config data from deployment_config table |  | true |
 | VALIDATE_EXT_APP_CHART_TYPE | bool |false | validate external flux app chart |  | false |


## CI_BUILDX Related Environment Variables
| Key   | Type     | Default Value     | Description       | Example       | Deprecated       |
|-------|----------|-------------------|-------------------|-----------------------|------------------|
 | ASYNC_BUILDX_CACHE_EXPORT | bool |false | To enable async container image cache export |  | false |
 | BUILDX_CACHE_MODE_MIN | bool |false | To set build cache mode to minimum in buildx |  | false |
 | BUILDX_INTERRUPTION_MAX_RETRY | int |3 | Maximum number of retries for buildx builder interruption |  | false |


## CI_RUNNER Related Environment Variables
| Key   | Type     | Default Value     | Description       | Example       | Deprecated       |
|-------|----------|-------------------|-------------------|-----------------------|------------------|
 | AWS_INSPECTOR_CONFIG | string | | Aws Inspector Scanning tool config for scanning docker images (It should have valid permissions to scan sbom) | {"awsRegion": "", "accessKey": "", "secretKey": "", "sbomGenerationTimeout": 10, "scanningTimeout": 10} | false |
 | AZURE_ACCOUNT_KEY | string | | If blob storage is being used of azure then pass the secret key to access the bucket |  | false |
 | AZURE_ACCOUNT_NAME | string | | Account name for azure blob storage |  | false |
 | AZURE_BLOB_CONTAINER_CI_CACHE | string | | Cache bucket name for azure blob storage |  | false |
 | AZURE_BLOB_CONTAINER_CI_LOG | string | | Log bucket for azure blob storage |  | false |
 | AZURE_GATEWAY_CONNECTION_INSECURE | bool |true | Azure gateway connection allows insecure if true |  | false |
 | AZURE_GATEWAY_URL | string |http://devtron-minio.devtroncd:9000 | sent to CI runner for blob |  | false |
 | BASE_LOG_LOCATION_PATH | string |/home/<USER>/ | used to store, download logs of ci workflow, artifact |  | false |
 | BLOB_STORAGE_GCP_CREDENTIALS_JSON | string | | GCP cred json for GCS blob storage |  | false |
 | BLOB_STORAGE_PROVIDER |  |S3 | Blob storage provider name(AWS/GCP/Azure) |  | false |
 | BLOB_STORAGE_S3_ACCESS_KEY | string | | S3 access key for s3 blob storage |  | false |
 | BLOB_STORAGE_S3_BUCKET_VERSIONED | bool |true | To enable bucket versioning for blob storage |  | false |
 | BLOB_STORAGE_S3_ENDPOINT | string | | S3 endpoint URL for s3 blob storage |  | false |
 | BLOB_STORAGE_S3_ENDPOINT_INSECURE | bool |false | To use insecure s3 endpoint |  | false |
 | BLOB_STORAGE_S3_SECRET_KEY | string | | Secret key for s3 blob storage |  | false |
 | BUILDX_CACHE_PATH | string |/var/lib/devtron/buildx | Path for the buildx cache |  | false |
 | BUILDX_DRIVER_IMAGE | string | | Buildx driver image |  | false |
 | BUILDX_K8S_DRIVER_OPTIONS | string | | To enable the k8s driver and pass args for k8s driver in buildx |  | false |
 | BUILDX_PROVENANCE_MODE | string | | provinance is set to true by default by docker. this will add some build related data in generated build manifest.it also adds some unknown:unknown key:value pair which may not be compatible by some container registries. with buildx k8s driver , provinenance=true is causing issue when push manifest to quay registry, so setting it to false |  | false |
 | BUILD_LOG_TTL_VALUE_IN_SECS | int |3600 | this is the time that the pods of ci/pre-cd/post-cd live after completion state. |  | false |
 | CACHE_LIMIT | int64 |5000000000 | Cache limit. |  | false |
 | CAN_APPROVER_DEPLOY | bool |false | In approver config,  approver can deploy or not controlled by this flag. |  | false |
 | CD_DEFAULT_ADDRESS_POOL_BASE_CIDR | string | | To pass the IP cidr for Pre/Post cd |  | false |
 | CD_DEFAULT_ADDRESS_POOL_SIZE | int | | The subnet size to allocate from the base pool for CD |  | false |
 | CD_LIMIT_CI_CPU | string |0.5 | CD CPU limit for post/pre-deploy workflow. |  | false |
 | CD_LIMIT_CI_MEM | string |3G | CD memory limit for post/pre-deploy workflow. |  | false |
 | CD_NODE_LABEL_SELECTOR |  | | CD node label selector. |  | false |
 | CD_NODE_TAINTS_KEY | string |dedicated | CD node taints key. |  | false |
 | CD_NODE_TAINTS_VALUE | string |ci | CD node taints value. |  | false |
 | CD_REQ_CI_CPU | string |0.5 | CD CPU request for post/pre-deploy workflow. |  | false |
 | CD_REQ_CI_MEM | string |3G | CD memory request for post/pre-deploy workflow. |  | false |
 | CD_WORKFLOW_EXECUTOR_TYPE |  |AWF | Executor type for Pre/Post CD(AWF,System) |  | false |
 | CD_WORKFLOW_SERVICE_ACCOUNT | string |cd-runner | CD workflow service account. |  | false |
 | CI_DEFAULT_ADDRESS_POOL_BASE_CIDR | string | | To pass the IP cidr for CI |  | false |
 | CI_DEFAULT_ADDRESS_POOL_SIZE | int | | The subnet size to allocate from the base pool for CI |  | false |
 | CI_IGNORE_DOCKER_CACHE | bool | | Ignoring docker cache |  | false |
 | CI_LOGS_KEY_PREFIX | string | | Key prefix for CI artifacts. |  | false |
 | CI_NODE_LABEL_SELECTOR |  | | CI node label selector. |  | false |
 | CI_NODE_TAINTS_KEY | string | | Toleration key for CI |  | false |
 | CI_NODE_TAINTS_VALUE | string | | Toleration value for CI |  | false |
 | CI_RUNNER_DOCKER_MTU_VALUE | int |-1 | This is to control the bytes of inofrmation passed in a network packet in ci-runner.  default is -1 (defaults to the underlying node mtu value) |  | false |
 | CI_SUCCESS_AUTO_TRIGGER_BATCH_SIZE | int |1 | this is to control the no of linked pipelines should be hanled in one go when a ci-success event of an parent ci is received |  | false |
 | CI_VOLUME_MOUNTS_JSON | string | | additional volume mount data for CI and JOB |  | false |
 | CI_WORKFLOW_EXECUTOR_TYPE |  |AWF | Executor type for CI(AWF,System) |  | false |
 | DEFAULT_ARTIFACT_KEY_LOCATION | string |arsenal-v1/ci-artifacts | Default location for CI artifacts. |  | false |
 | DEFAULT_BUILD_LOGS_BUCKET | string |devtron-pro-ci-logs | Default bucket for build logs. |  | false |
 | DEFAULT_BUILD_LOGS_KEY_PREFIX | string |arsenal-v1 | Default key prefix for build logs. |  | false |
 | DEFAULT_CACHE_BUCKET | string |ci-caching | Bucket name for build cache |  | false |
 | DEFAULT_CACHE_BUCKET_REGION | string |us-east-2 | Default region for the cache bucket. |  | false |
 | DEFAULT_CD_ARTIFACT_KEY_LOCATION | string | | Default location for CD artifacts. |  | false |
 | DEFAULT_CD_LOGS_BUCKET_REGION | string |us-east-2 | Default region for CD logs bucket. |  | false |
 | DEFAULT_CD_NAMESPACE | string | | Default namespace for CD. |  | false |
 | DEFAULT_CD_TIMEOUT | int64 |3600 | Default timeout for CD. |  | false |
 | DEFAULT_CI_IMAGE | string |686244538589.dkr.ecr.us-east-2.amazonaws.com/cirunner:47 | Default image for CI pods. |  | false |
 | DEFAULT_NAMESPACE | string |devtron-ci | Default namespace for CI. |  | false |
 | DEFAULT_TARGET_PLATFORM | string | | Default architecture for buildx |  | false |
 | DOCKER_BUILD_CACHE_PATH | string |/var/lib/docker | Path to store cache of docker build  (/var/lib/docker-> for legacy docker build, /var/lib/devtron-> for buildx) |  | false |
 | ENABLE_BUILD_CONTEXT | bool |false | To Enable build context in Devtron. |  | false |
 | ENABLE_SECRET_MASKING | bool |true | Enable secret masking |  | false |
 | ENABLE_WORKFLOW_EXECUTION_STAGE | bool |true | if enabled then we will display build stages separately for CI/Job/Pre-Post CD | true | false |
 | EXTERNAL_BLOB_STORAGE_CM_NAME | string |blob-storage-cm | name of the config map(contains bucket name, etc.) in external cluster when there is some operation related to external cluster, for example:-downloading cd artifact pushed in external cluster's env and we need to download from there, downloads ci logs pushed in external cluster's blob |  | false |
 | EXTERNAL_BLOB_STORAGE_SECRET_NAME | string |blob-storage-secret | name of the secret(contains password, accessId,passKeys, etc.) in external cluster when there is some operation related to external cluster, for example:-downloading cd artifact pushed in external cluster's env and we need to download from there, downloads ci logs pushed in external cluster's blob |  | false |
 | EXTERNAL_CD_NODE_LABEL_SELECTOR |  | | This is an array of strings used when submitting a workflow for pre or post-CD execution. If the  |  | false |
 | EXTERNAL_CD_NODE_TAINTS_KEY | string |dedicated |  |  | false |
 | EXTERNAL_CD_NODE_TAINTS_VALUE | string |ci |  |  | false |
 | EXTERNAL_CI_API_SECRET | string |devtroncd-secret | External CI API secret. |  | false |
 | EXTERNAL_CI_PAYLOAD | string |{"ciProjectDetails":[{"gitRepository":"https://github.com/vikram1601/getting-started-nodejs.git","checkoutPath":"./abc","commitHash":"239077135f8cdeeccb7857e2851348f558cb53d3","commitTime":"2022-10-30T20:00:00","branch":"master","message":"Update README.md","author":"User Name "}],"dockerImage":"445808685819.dkr.ecr.us-east-2.amazonaws.com/orch:23907713-2"} |  |  | false |
 | EXTERNAL_CI_WEB_HOOK_URL | string | | default is {{HOST_URL}}/orchestrator/webhook/ext-ci. It is used for external ci. |  | false |
 | FEATURE_DEPLOYMENT_APPROVAL_SUPERADMIN_ONLY_ENABLE | bool |false | Approval for super admin only |  | false |
 | GIT_PROVIDERS | string |github,gitlab | List of git providers like github, gitlab etc. Used for shallow cloning service. |  | false |
 | IGNORE_CM_CS_IN_CI_JOB | bool |false | Ignore CM/CS in CI-pipeline |  | false |
 | IMAGE_RETRY_COUNT | int |0 | push artifact(image) in ci retry count |  | false |
 | IMAGE_RETRY_INTERVAL | int |5 | image retry interval takes value in seconds |  | false |
 | IMAGE_SCANNER_ENDPOINT | string |http://image-scanner-new-demo-devtroncd-service.devtroncd:80 | URL of the image scanner microservice. |  | false |
 | IMAGE_SCAN_MAX_RETRIES | int |3 | Max retry count for image-scanning |  | false |
 | IMAGE_SCAN_RETRY_DELAY | int |5 | Delay for the image-scaning to start |  | false |
 | IN_APP_LOGGING_ENABLED | bool |false | Used in case of argo workflow is enabled. If enabled logs push will be managed by us, else will be managed by argo workflow. |  | false |
 | MAX_CD_WORKFLOW_RUNNER_RETRIES | int |0 | Maximum time pre/post-cd-workflow create pod if it fails to complete |  | false |
 | MAX_CI_WORKFLOW_RETRIES | int |0 | Maximum time CI-workflow create pod if it fails to complete |  | false |
 | MODE | string |DEV | Operating mode. |  | false |
 | NATS_SERVER_HOST | string |localhost:4222 | Nats micro-service URL |  | false |
 | ORCH_HOST | string |http://devtroncd-orchestrator-service-prod.devtroncd/webhook/msg/nats | Orchestrator micro-service URL |  | false |
 | ORCH_TOKEN | string | | Orchestrator token |  | false |
 | PRE_CI_CACHE_PATH | string |/devtroncd-cache | Cache path for Pre CI tasks |  | false |
 | S3_BLOB_CONCURRENCY_MULTIPLIER | int |2 | Concurrency multiplier for s3 blob storage upload/download. This number *2 is final concurrent threads that will be run | 2 | false |
 | S3_BLOB_PART_SIZE | int64 |500 | Part size for s3 blob storage upload/download in MB. It should be in the range of 5MB to 5GB | 500 | false |
 | SHOW_DOCKER_BUILD_ARGS | bool |true | To enable showing the args passed for CI in build logs |  | false |
 | SKIP_CI_JOB_BUILD_CACHE_PUSH_PULL | bool |false | To skip cache Push/Pull for ci job |  | false |
 | SKIP_CREATING_ECR_REPO | bool |false | By disabling this ECR repo won't get created if it's not available on ECR from build configuration |  | false |
 | SKIP_MANDATORY_PLUGIN_ENFORCEMENT | bool |false | Can be only used for those who doesn't use mandatory plugins |  | false |
 | TERMINATION_GRACE_PERIOD_SECS | int |180 | this is the time given to workflow pods to shutdown. (grace full termination time) |  | false |
 | USE_ARTIFACT_LISTING_QUERY_V2 | bool |true | To use the V2 query for listing artifacts |  | false |
 | USE_BLOB_STORAGE_CONFIG_IN_CD_WORKFLOW | bool |true | To enable blob storage in pre and post cd |  | false |
 | USE_BLOB_STORAGE_CONFIG_IN_CI_WORKFLOW | bool |true | to enable blob storage in pre and post ci |  | false |
 | USE_BUILDX | bool |false | To enable buildx feature globally |  | false |
 | USE_DOCKER_API_TO_GET_DIGEST | bool |false | when user do not pass the digest  then this flag controls , finding the image digest using docker API or not. if set to true we get the digest from docker API call else use docker pull command |  | false |
 | USE_EXTERNAL_NODE | bool |false | It is used in case of Pre/ Post Cd with run in application mode. If enabled the node lebels are read from EXTERNAL_CD_NODE_LABEL_SELECTOR else from CD_NODE_LABEL_SELECTOR MODE: if the vale is DEV, it will read the local kube config file or else from the cluser location. |  | false |
 | USE_IMAGE_TAG_FROM_GIT_PROVIDER_FOR_TAG_BASED_BUILD | bool |false | To use the same tag in container image as that of git tag |  | false |
 | WF_CONTROLLER_INSTANCE_ID | string |devtron-runner | Workflow controller instance ID. |  | false |
 | WORKFLOW_CACHE_CONFIG | string |{} | flag is used to configure how Docker caches are handled during a CI/CD |  | false |
 | WORKFLOW_SERVICE_ACCOUNT | string |ci-runner | Workflow service account for CI. |  | false |


## DEVTRON Related Environment Variables
| Key   | Type     | Default Value     | Description       | Example       | Deprecated       |
|-------|----------|-------------------|-------------------|-----------------------|------------------|
 | ACCESS_KEY | string | |  |  | false |
 | ADDITIONAL_NODE_GROUP_LABELS |  | | Add comma separated list of additional node group labels to default labels | karpenter.sh/nodepool,cloud.google.com/gke-nodepool | false |
 | API_TIMEOUT_SECS | int |5 |  |  | false |
 | APP_SYNC_IMAGE | string |quay.io/devtron/chart-sync:1227622d-132-3775 | For the app sync image, this image will be used in app-manual sync job |  | false |
 | APP_SYNC_JOB_RESOURCES_OBJ | string | | To pass the resource of app sync |  | false |
 | APP_SYNC_SERVICE_ACCOUNT | string |chart-sync | Service account to be used in app sync Job |  | false |
 | APP_SYNC_SHUTDOWN_WAIT_DURATION | int |120 |  |  | false |
 | ARGO_AUTO_SYNC_ENABLED | bool |true | If enabled all argocd application will have auto sync enabled | true | false |
 | ARGO_GIT_COMMIT_RETRY_COUNT_ON_CONFLICT | int |3 | Max retry count while commit the manifest on gitops |  | false |
 | ARGO_GIT_COMMIT_RETRY_DELAY_ON_CONFLICT | int |1 |  |  | false |
 | ARGO_REPO_REGISTER_RETRY_COUNT | int |4 | Retry count for registering a GitOps repository to ArgoCD | 3 | false |
 | ARGO_REPO_REGISTER_RETRY_DELAY | int |5 | Delay (in Seconds) between the retries for registering a GitOps repository to ArgoCD | 5 | false |
 | ARRAY_DIFF_MEMOIZATION | bool |false |  |  | false |
 | AUTH_API_PORT | int |5000 |  |  | false |
 | AWS_REGION | string | |  |  | false |
 | BATCH_SIZE | int |5 | there is feature to get URL's of services/ingresses. so to extract those, we need to parse all the servcie and ingress objects of the application. this BATCH_SIZE flag controls the no of these objects get parsed in one go. |  | false |
 | BITBUCKET_DC_HOST | string |https://bitbucket-cloud.devtron.info/ | BitBucket Data Center host URL |  | false |
 | BITBUCKET_DC_PASSWORD | string | | BitBucket Data Center password |  | false |
 | BITBUCKET_DC_PROJECT_KEY | string |DEV | BitBucket Data Center project key |  | false |
 | BITBUCKET_DC_USERNAME | string |admin | BitBucket Data Center username |  | false |
 | BLACKOUT_WINDOW_TIME_PER_PIPELINE | int |300 |  |  | false |
 | BLOB_STORAGE_ENABLED | bool |false |  |  | false |
 | CACHED_GVKs | string |[] |  |  | false |
 | CACHED_NAMESPACES |  | |  |  | false |
 | CACHE_PVCs |  |java-cache-pvc-2,node-cache-pvc-1,node-cache-pvc-2 |  |  | false |
 | CAN_ONLY_VIEW_PERMITTED_ENV_ORG_LEVEL | bool |false |  |  | false |
 | CASBIN_CLIENT_URL | string |127.0.0.1:9000 |  |  | false |
 | CASBIN_GRPC_DATA_TRANSFER_MAX_SIZE | int |30 |  |  | false |
 | CD_HOST | string |localhost | Host for the devtron stack |  | false |
 | CD_NAMESPACE | string |devtroncd | Namespace for devtron stack |  | false |
 | CD_PORT | string |8000 | Port for pre/post-cd |  | false |
 | CExpirationTime | int |600 | Caching expiration time. |  | false |
 | CHAT_CLIENT_TIMEOUT | int |30 | Timeout for getting response from the chat client |  | false |
 | CI_TRIGGER_CRON_TIME | int |2 | For image poll plugin |  | false |
 | CI_WORKFLOW_STATUS_UPDATE_CRON | string |*/5 * * * * | Cron schedule for CI pipeline status |  | false |
 | CLEAN_UP_RBAC_POLICIES | bool |false |  |  | false |
 | CLEAN_UP_RBAC_POLICIES_CRON_TIME | string |0 0 * * * |  |  | false |
 | CLI_CMD_TIMEOUT_GLOBAL_SECONDS | int |0 | Used in git cli opeartion timeout |  | false |
 | CLUSTER_CACHE_ATTEMPT_LIMIT | int32 |1 |  |  | false |
 | CLUSTER_CACHE_LIST_PAGE_BUFFER_SIZE | int32 |10 |  |  | false |
 | CLUSTER_CACHE_LIST_PAGE_SIZE | int64 |500 |  |  | false |
 | CLUSTER_CACHE_LIST_SEMAPHORE_SIZE | int64 |5 |  |  | false |
 | CLUSTER_CACHE_RESYNC_DURATION |  |12h |  |  | false |
 | CLUSTER_CACHE_RETRY_USE_BACKOFF | bool | |  |  | false |
 | CLUSTER_CACHE_WATCH_RESYNC_DURATION |  |10m |  |  | false |
 | CLUSTER_CHAT_CONFIG | string |{} |  |  | false |
 | CLUSTER_ID | int |1 |  |  | false |
 | CLUSTER_STATUS_CRON_TIME | int |15 | Cron schedule for cluster status on resource browser |  | false |
 | CLUSTER_SYNC_RETRY_TIMEOUT_DURATION |  |10s |  |  | false |
 | CONSUMER_CONFIG_JSON | string | |  |  | false |
 | CUSTOM_ROLE_CACHE_ALLOWED | bool |false |  |  | false |
 | DEFAULT_LOG_TIME_LIMIT | int64 |1 |  |  | false |
 | DEFAULT_TIMEOUT | float64 |3600 | Timeout for CI to be completed |  | false |
 | DEPLOYMENT_WINDOW_FETCH_DAYS_BLACKOUT | int |90 |  |  | false |
 | DEPLOYMENT_WINDOW_FETCH_DAYS_MAINTENANCE | int |90 |  |  | false |
 | DEVTRON_BOM_URL | string |https://raw.githubusercontent.com/devtron-labs/devtron/%s/charts/devtron/devtron-bom.yaml |  |  | false |
 | DEVTRON_DEFAULT_NAMESPACE | string |devtroncd |  |  | false |
 | DEVTRON_DEX_SECRET_NAMESPACE | string |devtroncd |  |  | false |
 | DEVTRON_HELM_RELEASE_CHART_NAME | string |devtron-operator |  |  | false |
 | DEVTRON_HELM_RELEASE_NAME | string |devtron | Name of the Devtron Helm release. |  | false |
 | DEVTRON_HELM_RELEASE_NAMESPACE | string |devtroncd | Namespace of the Devtron Helm release |  | false |
 | DEVTRON_HELM_REPO_NAME | string |devtron | Is used to install modules (stack manager) |  | false |
 | DEVTRON_HELM_REPO_URL | string |https://helm.devtron.ai | Is used to install modules (stack manager) |  | false |
 | DEVTRON_INSTALLATION_TYPE | string | | Devtron Installation type(EA/Full) |  | false |
 | DEVTRON_INSTALLER_MODULES_PATH | string |devtron.installer.modules | Path to devtron installer modules, used to find the helm charts and values files |  | false |
 | DEVTRON_INSTALLER_RELEASE_PATH | string |devtron.installer.release | Path to devtron installer release, used to find the helm charts and values files |  | false |
 | DEVTRON_MANAGED_LICENSING_ENABLED | bool |true | flag to enable devtron managed licensing used to hide stack manager and do not register installer informers,module cron and no helm release expectation to show version |  | false |
 | DEVTRON_MODULES_IDENTIFIER_IN_HELM_VALUES | string |installer.modules |  |  | false |
 | DEVTRON_OPERATOR_BASE_PATH | string |devtron | Base path for devtron operator, used to find the helm charts and values files |  | false |
 | DEVTRON_SECRET_NAME | string |devtron-secret | Secret name for orchestrator micro-services |  | false |
 | DEVTRON_VERSION_IDENTIFIER_IN_HELM_VALUES | string |installer.release | devtron operator version identifier in helm values yaml |  | false |
 | DEX_CID | string |example-app | Dex client id  |  | false |
 | DEX_CLIENT_ID | string |argo-cd |  |  | false |
 | DEX_CSTOREKEY | string | | DEX CSTOREKEY. |  | false |
 | DEX_JWTKEY | string | | DEX JWT key.  |  | false |
 | DEX_RURL | string |http://127.0.0.1:8080/callback | Dex redirect URL(http://argocd-dex-server.devtroncd:8080/callback) |  | false |
 | DEX_SCOPES |  | |  |  | false |
 | DEX_SECRET | string | | Dex secret |  | false |
 | DEX_URL | string | | Dex service endpoint with dex path(http://argocd-dex-server.devtroncd:5556/dex) |  | false |
 | ECR_REPO_NAME_PREFIX | string |test/ | Prefix for ECR repo to be created in does not exist |  | false |
 | EMPTY_STRING_AS_UNKNOWN_VARIABLES | bool |false |  |  | false |
 | ENABLE_ASYNC_ARGO_CD_INSTALL_DEVTRON_CHART | bool |false | To enable async installation of gitops application |  | false |
 | ENABLE_ASYNC_INSTALL_DEVTRON_CHART | bool |false | To enable async installation of no-gitops application |  | false |
 | ENABLE_MANIFEST_SCANNING | bool |false |  |  | false |
 | ENTERPRISE_ENFORCER_ENABLED | bool |true |  |  | false |
 | EPHEMERAL_SERVER_VERSION_REGEX | string |v[1-9]\.\b(2[3-9]\|[3-9][0-9])\b.* | Ephemeral containers support version regex that is compared with k8sServerVersion |  | false |
 | EVENT_URL | string |http://localhost:3000/notify | Notifier service url |  | false |
 | EXECUTE_WIRE_NIL_CHECKER | bool |false | Checks for any nil pointer in wire.go |  | false |
 | EXPOSE_CI_METRICS | bool |false | To expose CI metrics |  | false |
 | FEATURE_RESOURCE_RECOMMENDATION_ENABLE | bool |false | enable resource recommendation feature |  | false |
 | FEATURE_RESTART_WORKLOAD_BATCH_SIZE | int |1 | Restart workload retrieval batch size  |  | false |
 | FEATURE_RESTART_WORKLOAD_WORKER_POOL_SIZE | int |5 | Restart workload retrieval pool size |  | false |
 | FEATURE_STRATEGY_STATUS_BATCH_SIZE | int |1 | Strategy Status retrieval batch size  |  | false |
 | FEATURE_STRATEGY_STATUS_WORKER_POOL_SIZE | int |5 | Strategy Status retrieval pool size |  | false |
 | FETCH_HELM_PIPELINE_APP_STATUS | bool |false |  |  | false |
 | FLUX_APPLY_STATUS_TIMEOUT | int |2 | Duration in minutes after which apply status will be moved from Applying to TimedOut state |  | false |
 | FLUX_INSTALLATION_DELETE_CRON_TIME | int |1 | Duration in minutes between each polling interval to check the Terraform installation delete status. |  | false |
 | FLUX_INSTALLATION_HELM_RELEASE_CRON_TIME | int |30 | Duration in seconds for cron job that periodically scans for versions stuck in applying (timeout) or still pending (missed event) and moves them along |  | false |
 | FLUX_INSTALLATION_STATUS_CRON_TIME | int |1 | Duration in minutes between each polling interval to check the Terraform installation status. |  | false |
 | FLUX_PENDING_STATUS_TIME | int |3 | Duration in minutes after which helm release event will be sent for installation stuck in pending state |  | false |
 | FORCE_SECURITY_SCANNING | bool |false | By enabling this no one can disable image scaning on ci-pipeline from UI |  | false |
 | GITHUB_ORG_NAME | string | |  |  | false |
 | GITHUB_TOKEN | string | |  |  | false |
 | GITHUB_USERNAME | string | |  |  | false |
 | GITOPS_REPO_PREFIX | string | | Prefix for Gitops repo being creation for argocd application |  | false |
 | GO_RUNTIME_ENV | string |production |  |  | false |
 | GRAFANA_HOST | string |localhost | Host URL for the grafana dashboard |  | false |
 | GRAFANA_NAMESPACE | string |devtroncd | Namespace for grafana |  | false |
 | GRAFANA_ORG_ID | int |2 | Org ID for grafana for application metrics |  | false |
 | GRAFANA_PASSWORD | string |prom-operator | Password for grafana dashboard |  | false |
 | GRAFANA_PORT | string |8090 | Port for grafana micro-service |  | false |
 | GRAFANA_URL | string | | Host URL for the grafana dashboard |  | false |
 | GRAFANA_USERNAME | string |admin | Username for grafana |  | false |
 | HIDE_API_TOKENS | bool |false | Boolean flag for should the api tokens generated be hidden from the UI |  | false |
 | HIDE_IMAGE_TAGGING_HARD_DELETE | bool |false |  |  | false |
 | IGNORE_AUTOCOMPLETE_AUTH_CHECK | bool |false | Flag for ignoring auth check in autocomplete apis. |  | false |
 | IGNORE_UNKNOWN_SCOPE_VARIABLES | bool |true |  |  | false |
 | INFRA_CONFIG | string | | configuration describing chart which will be used for particular installation. sample -> {    |  | false |
 | INSTALLED_MODULES |  | | List of installed modules given in helm values/yaml are written in cm and used by devtron to know which modules are given | security.trivy,security.clair | false |
 | INSTALLER_CRD_NAMESPACE | string |devtroncd | Namespace where Custom Resource Definitions get installed |  | false |
 | INSTALLER_CRD_OBJECT_GROUP_NAME | string |installer.devtron.ai | Devtron installer CRD group name, partially deprecated. |  | false |
 | INSTALLER_CRD_OBJECT_RESOURCE | string |installers | Devtron installer CRD resource name, partially deprecated |  | false |
 | INSTALLER_CRD_OBJECT_VERSION | string |v1alpha1 | version of the CRDs |  | false |
 | IS_AIR_GAP_ENVIRONMENT | bool |false |  |  | false |
 | JwtExpirationTime | int |120 | JWT expiration time. |  | false |
 | K8s_CLIENT_MAX_IDLE_CONNS_PER_HOST | int |25 |  |  | false |
 | K8s_TCP_IDLE_CONN_TIMEOUT | int |300 |  |  | false |
 | K8s_TCP_KEEPALIVE | int |30 |  |  | false |
 | K8s_TCP_TIMEOUT | int |30 |  |  | false |
 | K8s_TLS_HANDSHAKE_TIMEOUT | int |10 |  |  | false |
 | KRR_SCAN_LISTING_PARALLELISM_LIMIT | int |1 |  |  | false |
 | KUBECTL_PORT_FORWARD_WEBSOCKETS | bool |true | To use websockets instead of spdy in port forwarding |  | false |
 | LENS_TIMEOUT | int |0 | Lens microservice timeout. |  | false |
 | LENS_URL | string |http://lens-milandevtron-service:80 | Lens micro-service URL |  | false |
 | LICENSE_CERTIFICATE_SUFFIX_LENGTH | int |5 | this is used to set the length of license certificate suffix to be shown to user |  | false |
 | LICENSE_FREE_TRAIL_DAYS | int64 |14 | number of days for which we are giving free trail |  | false |
 | LICENSE_GRACE_PERIOD_SECS | int64 |0 | this is the grace period in seconds for the license, this is used to allow user to use the application for some time after the license is expired |  | false |
 | LIMIT_CI_CPU | string |0.5 |  |  | false |
 | LIMIT_CI_MEM | string |3G |  |  | false |
 | LOGGER_DEV_MODE | bool |false | Enables a different logger theme. |  | false |
 | LOG_LEVEL | int |-1 | Log level to set in orchestrator |  | false |
 | MAX_SESSION_PER_USER | int |5 | Max no of cluster terminal pods can be created by an user |  | false |
 | MODULE_METADATA_API_URL | string |https://api.devtron.ai/module?name=%s | Modules list and meta info will be fetched from this server, that is central api server of devtron. |  | false |
 | MODULE_STATUS_HANDLING_CRON_DURATION_MIN | int |3 | Module status update cron, used in stack manager, to update the module installation status. |  | false |
 | NATS_MSG_ACK_WAIT_IN_SECS | int |120 |  |  | false |
 | NATS_MSG_BUFFER_SIZE | int |-1 |  |  | false |
 | NATS_MSG_MAX_AGE | int |86400 |  |  | false |
 | NATS_MSG_PROCESSING_BATCH_SIZE | int |1 |  |  | false |
 | NATS_MSG_REPLICAS | int |0 |  |  | false |
 | NOTIFICATION_MEDIUM | NotificationMedium |rest | notification medium |  | false |
 | NOTIFICATION_TOKEN_EXPIRY_TIME_HOURS | int64 |720 |  |  | false |
 | ORCHESTRATOR_URL | string | |  |  | false |
 | OTEL_COLLECTOR_URL | string | | Opentelemetry URL |  | false |
 | PATCH_RECREATE_TO_CANARY_STRATEGY_CONFIG | string |{"deployment":{"strategy":{"canary":{"maxSurge":"0%","maxUnavailable":"100%"}}}} | strategy config replace recreate to canary |  | false |
 | PATCH_RECREATE_TO_CANARY_STRATEGY_IN_CLUSTERS | string | | Cluster list where recreate strategy needs to replace with canary while deployment |  | false |
 | PG_EXPORT_PROM_METRICS | bool |true |  |  | false |
 | PG_LOG_ALL_FAILURE_QUERIES | bool |true |  |  | false |
 | PG_LOG_ALL_QUERY | bool |false |  |  | false |
 | PG_LOG_SLOW_QUERY | bool |true |  |  | false |
 | PG_QUERY_DUR_THRESHOLD | int64 |5000 |  |  | false |
 | PLUGIN_IDENTIFIER_LIST_FOR_DIGEST_PULL |  |improved-image-scan |  |  | false |
 | PLUGIN_NAME | string |Pull images from container repository | Handles image retrieval from a container repository and triggers subsequent CI processes upon detecting new images.Current default plugin name: Pull Images from Container Repository. |  | false |
 | PLUGIN_NAME_LIST_FOR_DIGEST_PULL |  |IMAGE SCAN |  |  | false |
 | PROPAGATE_EXTRA_LABELS | bool |false | Add additional propagate labels like api.devtron.ai/appName, api.devtron.ai/envName, api.devtron.ai/project along with the user defined ones. |  | false |
 | PROXY_SERVICE_CONFIG | string |{} | Proxy configuration for micro-service to be accessible on orhcestrator ingress |  | false |
 | PROXY_UP_TIME | int64 |60 |  |  | false |
 | PVC_MOUNT_PATH_EXPRESSION | string |appLabels['devtron.ai/language'] == 'java' ? '/devtroncd/.m2' : '/devtroncd/node_modules' |  |  | false |
 | PVC_NAME_EXPRESSION | string |appLabels['devtron.ai/language'] == 'java' ? 'java-cache' : 'node-cache' |  |  | false |
 | REMINDER_THRESHOLD_FOR_FREE_TRIAL | int |3 | this is used to set ( to be remind in time) for free trial |  | false |
 | REMINDER_THRESHOLD_FOR_LICENSE | int |15 | this is used to set ( to be remind in time) for license |  | false |
 | REQ_CI_CPU | string |0.5 |  |  | false |
 | REQ_CI_MEM | string |3G |  |  | false |
 | RESTRICT_TERMINAL_ACCESS_TO_TERMINAL_ROLE | bool |false |  |  | false |
 | RUNTIME_CONFIG_LOCAL_DEV | LocalDevMode |true |  |  | false |
 | SCOOP_CLUSTER_CONFIG | string |{} |  |  | false |
 | SCOPED_VARIABLE_ENABLED | bool |true | To enable scoped variable option |  | false |
 | SCOPED_VARIABLE_FORMAT | string |@{{%s}} | Its a scope format for varialbe name. |  | false |
 | SCOPED_VARIABLE_HANDLE_PRIMITIVES | bool |true | This describe should we handle primitives or not in scoped variable template parsing. |  | false |
 | SCOPED_VARIABLE_NAME_REGEX | string |^[a-zA-Z][a-zA-Z0-9_-]{0,62}[a-zA-Z0-9]$ | Regex for scoped variable name that must passed this regex. |  | false |
 | SECRET_KEY | string | |  |  | false |
 | SILVER_SURFER_CLIENT_URL | string |127.0.0.1:8111 |  |  | false |
 | SILVER_SURFER_GRPC_MAX_RECEIVE_MSG_SIZE | int |20 |  |  | false |
 | SILVER_SURFER_GRPC_MAX_SEND_MSG_SIZE | int |4 |  |  | false |
 | SILVER_SURFER_GRPC_TIMEOUT_SECONDS | int |30 |  |  | false |
 | SKIP_SEVERITY_LIST |  | |  |  | false |
 | SLEEP_TIME_BETWEEN_MIGRATION | int |5 | sleep time between migration of two pipelines. It is used to avoid github secondary rate limits |  | false |
 | SOCKET_DISCONNECT_DELAY_SECONDS | int |5 | The server closes a session when a client receiving connection have not been seen for a while.This delay is configured by this setting. By default the session is closed when a receiving connection wasn't seen for 5 seconds. |  | false |
 | SOCKET_HEARTBEAT_SECONDS | int |25 | In order to keep proxies and load balancers from closing long running http requests we need to pretend that the connection is active and send a heartbeat packet once in a while. This setting controls how often this is done. By default a heartbeat packet is sent every 25 seconds. |  | false |
 | STREAM_CONFIG_JSON | string | |  |  | false |
 | SUB_CLOUD_API_PORT | int |8119 |  |  | false |
 | SYSTEM_CONTROLLER_API_PORT | int |6385 |  |  | false |
 | SYSTEM_VAR_PREFIX | string |DEVTRON_ | Scoped variable prefix, variable name must have this prefix. |  | false |
 | SYS_NET_API_TIMEOUT_IN_SECONDS | int |120 |  |  | false |
 | TERMINAL_POD_DEFAULT_NAMESPACE | string |default | Cluster terminal default namespace |  | false |
 | TERMINAL_POD_INACTIVE_DURATION_IN_MINS | int |10 | Timeout for cluster terminal to be inactive |  | false |
 | TERMINAL_POD_STATUS_SYNC_In_SECS | int |600 | This is the time interval at which the status of the cluster terminal pod |  | false |
 | TEST_APP | string |orchestrator | application name |  | false |
 | TEST_PG_ADDR | string |127.0.0.1 | Postgres URL |  | false |
 | TEST_PG_DATABASE | string |orchestrator | Postgres database name |  | false |
 | TEST_PG_LOG_QUERY | bool |true | TEST_PG_LOG_QUERY |  | false |
 | TEST_PG_PASSWORD | string |postgrespw | TEST_PG_PASSWORD |  | false |
 | TEST_PG_PORT | string |55000 | Postgres port number |  | false |
 | TEST_PG_USER | string |postgres | Postgres user |  | false |
 | TIMEOUT_FOR_FAILED_CI_BUILD | string |15 | Timeout for Failed CI build |  | false |
 | TIMEOUT_IN_SECONDS | int |5 | Timeout to compute the urls from services and ingress objects of an application |  | false |
 | TOKEN | string | |  |  | false |
 | TOKEN_TO_AVOID_GITHUB_RATE_LIMITER | string | |  |  | false |
 | USER_SESSION_DURATION_SECONDS | int |86400 |  |  | false |
 | USE_ARTIFACT_LISTING_API_V2 | bool |true | To use the V2 API for listing artifacts in Listing the images in pipeline |  | false |
 | USE_CASBIN_V2 | bool |true |  |  | false |
 | USE_CUSTOM_ENFORCER | bool |true |  |  | false |
 | USE_CUSTOM_HTTP_TRANSPORT | bool |false |  |  | false |
 | USE_GIT_CLI | bool |false | To enable git cli |  | false |
 | USE_RBAC_CREATION_V2 | bool |true | To use the V2 for RBAC creation |  | false |
 | USE_RESOURCE_LIST_V2 | bool | |  |  | true |
 | VARIABLE_CACHE_ENABLED | bool |true | This is used to  control caching of all the scope variables defined in the system. |  | false |
 | VARIABLE_EXPRESSION_REGEX | string |@{{([^}]+)}} | Scoped variable expression regex |  | false |
 | WEBHOOK_TOKEN | string | | If you want to continue using jenkins for CI then please provide this for authentication of requests |  | false |
 | WORKLOAD_LIST_PARALLELISM_LIMIT | int |1 |  |  | false |


## FILE_UPLOAD Related Environment Variables
| Key   | Type     | Default Value     | Description       | Example       | Deprecated       |
|-------|----------|-------------------|-------------------|-----------------------|------------------|
 | - | int64 | |  |  | false |
 | MAX_UPLOAD_SIZE_IN_KB | int64 |1024 | Maximum upload size in KB | 1024 | false |


## GITOPS Related Environment Variables
| Key   | Type     | Default Value     | Description       | Example       | Deprecated       |
|-------|----------|-------------------|-------------------|-----------------------|------------------|
 | ACD_CM | string |argocd-cm | Name of the argocd CM |  | false |
 | ACD_NAMESPACE | string |devtroncd | To pass the argocd namespace |  | false |
 | ACD_PASSWORD | string | | Password for the Argocd (deprecated) |  | false |
 | ACD_USERNAME | string |admin | User name for argocd |  | false |
 | GITOPS_SECRET_NAME | string |devtron-gitops-secret | Devtron-gitops-secret |  | false |
 | RESOURCE_LIST_FOR_REPLICAS | string |Deployment,Rollout,StatefulSet,ReplicaSet | This holds the list of k8s resource names which support replicas key. this list used in hibernate/un hibernate process  |  | false |
 | RESOURCE_LIST_FOR_REPLICAS_BATCH_SIZE | int |5 | This the batch size to control no of above resources can be parsed in one go to determine hibernate status |  | false |
 | RESOURCE_TREE_V2 | bool | |  |  | false |


## INFRA_SETUP Related Environment Variables
| Key   | Type     | Default Value     | Description       | Example       | Deprecated       |
|-------|----------|-------------------|-------------------|-----------------------|------------------|
 | DASHBOARD_HOST | string |localhost | Dashboard micro-service URL |  | false |
 | DASHBOARD_NAMESPACE | string |devtroncd | Dashboard micro-service namespace |  | false |
 | DASHBOARD_PORT | string |3000 | Port for dashboard micro-service |  | false |
 | DEX_HOST | string |http://localhost |  |  | false |
 | DEX_PORT | string |5556 |  |  | false |
 | GIT_SENSOR_GRPC_DATA_TRANSFER_MAX_SIZE | int |4 |  |  | false |
 | GIT_SENSOR_PROTOCOL | string |REST | Protocol to connect with git-sensor micro-service |  | false |
 | GIT_SENSOR_SERVICE_CONFIG | string |{"loadBalancingPolicy":"pick_first"} | git-sensor grpc service config |  | false |
 | GIT_SENSOR_TIMEOUT | int |0 | Timeout for getting response from the git-sensor |  | false |
 | GIT_SENSOR_URL | string |127.0.0.1:7070 | git-sensor micro-service url |  | false |
 | HELM_CLIENT_URL | string |127.0.0.1:50051 | Kubelink micro-service url  |  | false |
 | KUBELINK_GRPC_MAX_RECEIVE_MSG_SIZE | int |20 |  |  | false |
 | KUBELINK_GRPC_MAX_SEND_MSG_SIZE | int |4 |  |  | false |
 | KUBELINK_GRPC_SERVICE_CONFIG | string |{"loadBalancingPolicy":"round_robin"} | kubelink grpc service config |  | false |


## KRR_SYNC_JOB_CONFIG Related Environment Variables
| Key   | Type     | Default Value     | Description       | Example       | Deprecated       |
|-------|----------|-------------------|-------------------|-----------------------|------------------|
 | KRR_ACTIVE_DEADLINE_SECONDS | int64 |15000 | Active deadline seconds for KRR sync job, default is 15000 seconds (~4 hours) |  | false |
 | KRR_DOCKER_IMAGE | string | | Docker image for KRR sync job |  | false |
 | KRR_EVALUATE_STRATEGIES | bool |true | Flag to evaluate strategies during KRR sync job |  | false |
 | KRR_IGNORE_CPU_THROTTLING | bool |false | Flag to ignore CPU throttling during KRR sync job |  | false |
 | KRR_IGNORE_OOM | bool |false | Flag to ignore OOM (Out of Memory) events during KRR sync job |  | false |
 | KRR_LOG_LEVEL | int |-1 | Log level for KRR sync job, default is -1 (debug level), 0 (info), 1 (warn), 2 (error) |  | false |
 | KRR_MATRIX_STEP_INTERVAL | string |5m | Step interval for Prometheus matrix queries in KRR sync job, e.g., '5m' for 5 minutes |  | false |
 | KRR_MATRIX_TIME_RANGE | string |120h | Time range for Prometheus matrix queries in KRR sync job, e.g., '120h' for 120 hours |  | false |
 | KRR_SYNC_JOB_CRON_SCHEDULE | string |0 0 * * * | Cron schedule for KRR sync job, default is '0 0 * * *' (daily at midnight) |  | false |
 | KRR_SYNC_JOB_HTTP_PORT | int |8080 | Port for Prometheus matrix queries in KRR sync job, default is 8080 |  | false |
 | KRR_SYNC_JOB_RESOURCES_OBJ | string |{} | KRR sync job resources object in JSON format |  | false |
 | KRR_SYNC_JOB_SHUTDOWN_WAIT_DURATION | int |120 | Duration in seconds to wait for KRR sync job shutdown before force termination, default is 60 seconds |  | false |
 | KRR_SYNC_SERVICE_ACCOUNT | string |krr-sync | Service account for KRR sync job |  | false |
 | MIN_CPU_REQUEST | float64 |0.1 | Minimum CPU request to consider in recommendations (in cores, 0 means no minimum) |  | false |
 | MIN_MEMORY_REQUEST | float64 |1.0 | Minimum memory request to consider in recommendations (in MiB, 0 means no minimum) |  | false |
 | PARALLELISM_LIMIT_FOR_TAG_PROCESSING | int |1 | Parallelism limit for workload list processing in KRR sync job |  | false |


## POSTGRES Related Environment Variables
| Key   | Type     | Default Value     | Description       | Example       | Deprecated       |
|-------|----------|-------------------|-------------------|-----------------------|------------------|
 | APP | string |orchestrator | Application name |  | false |
 | CASBIN_DATABASE | string |casbin |  |  | false |
 | PG_ADDR | string |127.0.0.1 | address of postgres service | postgresql-postgresql.devtroncd | false |
 | PG_DATABASE | string |orchestrator | postgres database to be made connection with | orchestrator, casbin, git_sensor, lens | false |
 | PG_PASSWORD | string |{password} | password for postgres, associated with PG_USER | confidential ;) | false |
 | PG_PORT | string |5432 | port of postgresql service | 5432 | false |
 | PG_READ_TIMEOUT | int64 |30 |  |  | false |
 | PG_USER | string |postgres | user for postgres | postgres | false |
 | PG_WRITE_TIMEOUT | int64 |30 |  |  | false |


## RBAC Related Environment Variables
| Key   | Type     | Default Value     | Description       | Example       | Deprecated       |
|-------|----------|-------------------|-------------------|-----------------------|------------------|
 | ENFORCER_CACHE | bool |false | To Enable enforcer cache. |  | false |
 | ENFORCER_CACHE_EXPIRATION_IN_SEC | int |86400 | Expiration time (in seconds) for enforcer cache. |  | false |
 | ENFORCER_MAX_BATCH_SIZE | int |1 | Maximum batch size for the enforcer. |  | false |

