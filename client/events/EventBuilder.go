/*
 * Copyright (c) 2020-2024. Devtron Inc.
 */

package client

import (
	"context"
	"fmt"
	"github.com/devtron-labs/devtron/client/events/notificationBean"
	bean3 "github.com/devtron-labs/devtron/client/events/util"
	bean5 "github.com/devtron-labs/devtron/enterprise/pkg/deploymentWindow"
	"github.com/devtron-labs/devtron/enterprise/pkg/resourceFilter"
	"github.com/devtron-labs/devtron/internal/sql/models"
	util2 "github.com/devtron-labs/devtron/internal/util"
	bean4 "github.com/devtron-labs/devtron/pkg/apiToken/bean"
	bean6 "github.com/devtron-labs/devtron/pkg/build/pipeline/bean"
	buildBean "github.com/devtron-labs/devtron/pkg/build/pipeline/bean"
	repository4 "github.com/devtron-labs/devtron/pkg/cluster/environment/repository"
	"github.com/pkg/errors"
	"net/http"
	"strings"
	"time"

	bean2 "github.com/devtron-labs/devtron/api/bean"
	repository2 "github.com/devtron-labs/devtron/internal/sql/repository"
	appRepository "github.com/devtron-labs/devtron/internal/sql/repository/app"
	"github.com/devtron-labs/devtron/internal/sql/repository/chartConfig"
	"github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
	"github.com/devtron-labs/devtron/pkg/apiToken"
	"github.com/devtron-labs/devtron/pkg/auth/user/repository"
	"github.com/devtron-labs/devtron/pkg/bean"
	"github.com/devtron-labs/devtron/util/event"
	"github.com/go-pg/pg"
	"github.com/satori/go.uuid"
	"go.uber.org/zap"
)

type EventFactory interface {
	Build(eventType util.EventType, sourceId *int, appId int, envId *int, pipelineType util.PipelineType) (Event, error)
	BuildExtraCDData(event Event, wfr *pipelineConfig.CdWorkflowRunner, pipelineOverrideId int, stage bean2.WorkflowType) Event
	BuildExtraApprovalData(event Event, approvalActionRequest bean.UserApprovalActionRequest, pipeline *pipelineConfig.Pipeline, userId int32, imageTagNames []string, imageComment string) []Event
	BuildExtraProtectConfigData(event Event, draftNotificationRequest notificationBean.ConfigDataForNotification, draftId int, DraftVersionId int) []Event
	BuildExtraCIData(event Event, material *bean6.MaterialTriggerInfo) Event
	// BuildFinalData(event Event) *Payload
	BuildExtraBlockedTriggerData(event Event, stage bean2.WorkflowType, timeWindowComment string, artifact *repository2.CiArtifact) Event
	SetAdditionalImageScanData(event *Event, ciPipelineId int, artifactId int)
	BuildExtraArtifactPromotionData(event Event, request notificationBean.ArtifactPromotionNotificationRequest) []Event
	BuildScoopNotificationEventProviders(configType util.Channel, configName string, emailIds []string, data notificationBean.InterceptEventNotificationData) (*Payload, error)
}

type EventSimpleFactoryImpl struct {
	logger                       *zap.SugaredLogger
	cdWorkflowRepository         pipelineConfig.CdWorkflowRepository
	pipelineOverrideRepository   chartConfig.PipelineOverrideRepository
	ciWorkflowRepository         pipelineConfig.CiWorkflowRepository
	ciPipelineMaterialRepository pipelineConfig.CiPipelineMaterialRepository
	ciPipelineRepository         pipelineConfig.CiPipelineRepository
	pipelineRepository           pipelineConfig.PipelineRepository
	userRepository               repository.UserRepository
	ciArtifactRepository         repository2.CiArtifactRepository
	resourceApprovalRepository   pipelineConfig.RequestApprovalUserdataRepository
	sesNotificationRepository    repository2.SESNotificationRepository
	smtpNotificationRepository   repository2.SMTPNotificationRepository
	appRepo                      appRepository.AppRepository
	envRepository                repository4.EnvironmentRepository
	apiTokenServiceImpl          *apiToken.ApiTokenServiceImpl
	resourceFilterAuditService   resourceFilter.FilterEvaluationAuditService
	webhookConfigRepo            repository2.WebhookNotificationRepository
	slackConfigRepo              repository2.SlackNotificationRepository
}

func NewEventSimpleFactoryImpl(logger *zap.SugaredLogger, cdWorkflowRepository pipelineConfig.CdWorkflowRepository,
	pipelineOverrideRepository chartConfig.PipelineOverrideRepository, ciWorkflowRepository pipelineConfig.CiWorkflowRepository,
	ciPipelineMaterialRepository pipelineConfig.CiPipelineMaterialRepository,
	ciPipelineRepository pipelineConfig.CiPipelineRepository, pipelineRepository pipelineConfig.PipelineRepository,
	userRepository repository.UserRepository, ciArtifactRepository repository2.CiArtifactRepository, resourceApprovalRepository pipelineConfig.RequestApprovalUserdataRepository,
	sesNotificationRepository repository2.SESNotificationRepository, smtpNotificationRepository repository2.SMTPNotificationRepository,
	appRepo appRepository.AppRepository, envRepository repository4.EnvironmentRepository, apiTokenServiceImpl *apiToken.ApiTokenServiceImpl,
	resourceFilterAuditService resourceFilter.FilterEvaluationAuditService,
	webhookConfigRepo repository2.WebhookNotificationRepository,
	slackConfigRepo repository2.SlackNotificationRepository,
) *EventSimpleFactoryImpl {
	return &EventSimpleFactoryImpl{
		logger:                       logger,
		cdWorkflowRepository:         cdWorkflowRepository,
		pipelineOverrideRepository:   pipelineOverrideRepository,
		ciWorkflowRepository:         ciWorkflowRepository,
		ciPipelineMaterialRepository: ciPipelineMaterialRepository,
		ciPipelineRepository:         ciPipelineRepository,
		pipelineRepository:           pipelineRepository,
		userRepository:               userRepository,
		ciArtifactRepository:         ciArtifactRepository,
		resourceApprovalRepository:   resourceApprovalRepository,
		sesNotificationRepository:    sesNotificationRepository,
		smtpNotificationRepository:   smtpNotificationRepository,
		appRepo:                      appRepo,
		envRepository:                envRepository,
		apiTokenServiceImpl:          apiTokenServiceImpl,
		resourceFilterAuditService:   resourceFilterAuditService,
		webhookConfigRepo:            webhookConfigRepo,
		slackConfigRepo:              slackConfigRepo,
	}

}

func (impl *EventSimpleFactoryImpl) Build(eventType util.EventType, sourceId *int, appId int, envId *int, pipelineType util.PipelineType) (Event, error) {
	correlationId := uuid.NewV4()
	event := Event{}
	event.EventTypeId = int(eventType)
	if sourceId != nil {
		event.PipelineId = *sourceId
	}
	event.AppId = appId
	if envId != nil && *envId > 0 {
		env, err := impl.envRepository.FindById(*envId)
		if err != nil {
			impl.logger.Errorw("error in getting env", "envId", *envId, "err", err)
			return event, err
		}
		event.EnvId = *envId
		event.ClusterId = env.ClusterId
		event.IsProdEnv = env.Default
	}
	event.PipelineType = string(pipelineType)
	event.CorrelationId = fmt.Sprintf("%s", correlationId)
	event.EventTime = time.Now().Format(bean.LayoutRFC3339)
	return event, nil
}

func (impl *EventSimpleFactoryImpl) SetAdditionalImageScanData(event *Event, ciPipelineId int, artifactId int) {
	material, ciWorkflowId, err := impl.getCiMaterialInfo(ciPipelineId, artifactId)
	if err != nil {
		impl.logger.Errorw("error in getting material", "ciPipelineId", ciPipelineId, "artifactId", artifactId, "err", err)
	}
	event.CiWorkflowRunnerId = ciWorkflowId
	event.Payload.MaterialTriggerInfo = material
}

func (impl *EventSimpleFactoryImpl) BuildExtraCDData(event Event, wfr *pipelineConfig.CdWorkflowRunner, pipelineOverrideId int, stage bean2.WorkflowType) Event {
	// event.CdWorkflowRunnerId =
	event.CdWorkflowType = stage
	payload := event.Payload
	if payload == nil {
		payload = &Payload{}
		payload.Stage = string(stage)
		event.Payload = payload
	}
	var emailIDs []string

	if wfr != nil && wfr.DeploymentApprovalRequestId >= 0 {
		deploymentUserData, err := impl.resourceApprovalRepository.FetchApprovedDataByApprovalId(wfr.DeploymentApprovalRequestId, models.DEPLOYMENT_APPROVAL)
		if err != nil {
			impl.logger.Errorw("error in getting deploymentUserData", "err", err, "deploymentApprovalRequestId", wfr.DeploymentApprovalRequestId)
		}
		if deploymentUserData != nil {
			userIDs := []int32{}
			for _, userData := range deploymentUserData {
				userIDs = append(userIDs, userData.UserId)
			}
			users, err := impl.userRepository.GetByIds(userIDs)
			if err != nil {
				impl.logger.Errorw("UserModel not found for users", err)
			}
			emailIDs = []string{}
			for _, user := range users {
				emailIDs = append(emailIDs, user.EmailId)
			}

		}
	}
	payload.TimeWindowComment = wfr.TriggerMetadata
	if wfr != nil && wfr.CdWorkflow != nil && wfr.TriggerMetadata == "" {
		payload.TimeWindowComment, _ = impl.getDeploymentWindowAuditMessage(wfr.CdWorkflow.CiArtifactId, wfr.Id)
	}
	payload.ApprovedByEmail = emailIDs
	if wfr != nil && wfr.WorkflowType != bean2.CD_WORKFLOW_TYPE_DEPLOY {
		material, _, err := impl.getCiMaterialInfo(wfr.CdWorkflow.Pipeline.CiPipelineId, wfr.CdWorkflow.CiArtifactId)
		if err != nil {
			impl.logger.Errorw("found error on payload build for cd stages, skipping this error ", "event", event, "stage", stage, "workflow runner", wfr, "pipelineOverrideId", pipelineOverrideId)
		}
		payload.MaterialTriggerInfo = material
		payload.DockerImageUrl = wfr.CdWorkflow.CiArtifact.Image
		event.UserId = int(wfr.TriggeredBy)
		event.Payload = payload
		event.CdWorkflowRunnerId = wfr.Id
		event.CiArtifactId = wfr.CdWorkflow.CiArtifactId
	} else if pipelineOverrideId > 0 {
		pipelineOverride, err := impl.pipelineOverrideRepository.FindById(pipelineOverrideId)
		if err != nil {
			impl.logger.Errorw("found error on payload build for cd stages, skipping this error ", "event", event, "stage", stage, "workflow runner", wfr, "pipelineOverrideId", pipelineOverrideId)
		}
		if pipelineOverride != nil && pipelineOverride.Id > 0 {
			cdWorkflow, err := impl.cdWorkflowRepository.FindById(pipelineOverride.CdWorkflowId)
			if err != nil {
				impl.logger.Errorw("found error on payload build for cd stages, skipping this error ", "cdWorkflow", cdWorkflow, "event", event, "stage", stage, "workflow runner", wfr, "pipelineOverrideId", pipelineOverrideId)
			}
			wfr, err := impl.cdWorkflowRepository.FindByWorkflowIdAndRunnerType(context.Background(), cdWorkflow.Id, stage)
			if err != nil {
				impl.logger.Errorw("found error on payload build for cd stages, skipping this error ", "wfr", wfr, "event", event, "stage", stage, "workflow runner", wfr, "pipelineOverrideId", pipelineOverrideId)
			}
			if wfr.Id > 0 {
				event.CdWorkflowRunnerId = wfr.Id
				event.CiArtifactId = pipelineOverride.CiArtifactId

				material, _, err := impl.getCiMaterialInfo(pipelineOverride.CiArtifact.PipelineId, pipelineOverride.CiArtifactId)
				if err != nil {
					impl.logger.Errorw("found error on payload build for cd stages, skipping this error ", "material", material)
				}
				payload.MaterialTriggerInfo = material
				payload.DockerImageUrl = wfr.CdWorkflow.CiArtifact.Image
				event.UserId = int(wfr.TriggeredBy)
			}
		}
		event.Payload = payload
	} else if event.PipelineId > 0 {
		impl.setMaterialForPayload(event, payload, 0)
		event.Payload = payload
	}

	if event.UserId > 0 {
		user, err := impl.userRepository.GetById(int32(event.UserId))

		if err != nil {
			impl.logger.Errorw("found error on payload build for cd stages, skipping this error ", "user", user)
		}
		payload = event.Payload
		payload.TriggeredBy = user.EmailId
		event.Payload = payload
	}
	event = impl.addExtraCdDataForEnterprise(event, wfr)
	return event
}

func (impl *EventSimpleFactoryImpl) setMaterialForPayload(event Event, payload *Payload, artifactId int) {
	pipeline, err := impl.pipelineRepository.FindById(event.PipelineId)
	if err != nil {
		impl.logger.Errorw("found error on payload build for cd stages, skipping this error ", "pipeline", pipeline)
	}
	if pipeline != nil {
		material, _, err := impl.getCiMaterialInfo(pipeline.CiPipelineId, artifactId)
		if err != nil {
			impl.logger.Errorw("found error on payload build for cd stages, skipping this error ", "material", material)
		}
		payload.MaterialTriggerInfo = material
	}
}

func (impl *EventSimpleFactoryImpl) BuildExtraBlockedTriggerData(event Event, stage bean2.WorkflowType, timeWindowComment string, artifact *repository2.CiArtifact) Event {
	event.CdWorkflowType = stage
	payload := &Payload{}
	event.Payload = payload
	payload.Stage = string(stage)
	payload.TimeWindowComment = timeWindowComment
	if event.PipelineId > 0 {
		impl.setMaterialForPayload(event, payload, artifact.Id)
	}
	if artifact != nil {
		payload.DockerImageUrl = artifact.Image
	}
	if event.UserId > 0 {
		user, err := impl.userRepository.GetById(int32(event.UserId))
		if err != nil {
			impl.logger.Errorw("found error on payload build for cd stages, skipping this error ", "user", user)
		}
		payload.TriggeredBy = user.EmailId
		event.Payload = payload
	}
	return event
}

func (impl *EventSimpleFactoryImpl) BuildExtraCIData(event Event, material *bean6.MaterialTriggerInfo) Event {
	var ciWfId int
	if material == nil {
		materialInfo, ciWorkflowId, err := impl.getCiMaterialInfo(event.PipelineId, event.CiArtifactId)
		if err != nil {
			impl.logger.Errorw("found error on payload build for ci, skipping this error ", "materialInfo", materialInfo)
		}
		material = materialInfo
		ciWfId = ciWorkflowId
	} else if material.CiMaterials == nil {
		materialInfo, ciWorkflowId, err := impl.getCiMaterialInfo(event.PipelineId, 0)
		if err != nil {
			impl.logger.Errorw("found error on payload build for ci, skipping this error ", "materialInfo", materialInfo)
		}
		materialInfo.GitTriggers = material.GitTriggers
		material = materialInfo
		ciWfId = ciWorkflowId
	}
	if event.CiWorkflowRunnerId == 0 {
		event.CiWorkflowRunnerId = ciWfId
	}
	payload := event.Payload
	if payload == nil {
		payload = &Payload{}
		event.Payload = payload
	}
	event.Payload.MaterialTriggerInfo = material

	if event.UserId > 0 {
		user, err := impl.userRepository.GetById(int32(event.UserId))
		if err != nil {
			impl.logger.Errorw("found error on payload build for cd stages, skipping this error ", "user", user)
		}
		payload = event.Payload
		payload.TriggeredBy = user.EmailId
		event.Payload = payload
	}

	// fetching all the envs which are directly or indirectly linked with the ci pipeline
	if event.PipelineId > 0 {
		// Get the pipeline to check if it's external
		ciPipeline, err := impl.ciPipelineRepository.FindById(event.PipelineId)
		if err != nil {
			impl.logger.Errorw("error in getting ci pipeline", "pipelineId", event.PipelineId, "err", err)
		} else {
			envs, err := impl.envRepository.FindEnvLinkedWithCiPipelines(ciPipeline.IsExternal, []int{event.PipelineId})
			if err != nil {
				impl.logger.Errorw("error in finding environments linked with ci pipeline", "pipelineId", event.PipelineId, "err", err)
			} else {
				event.EnvIdsForCiPipeline = make([]int, 0, len(envs))
				for _, env := range envs {
					event.EnvIdsForCiPipeline = append(event.EnvIdsForCiPipeline, env.Id)
				}
			}
		}
	}

	return event
}

func (impl *EventSimpleFactoryImpl) getCiMaterialInfo(ciPipelineId int, ciArtifactId int) (*bean6.MaterialTriggerInfo, int, error) {
	materialTriggerInfo := &bean6.MaterialTriggerInfo{}
	if ciPipelineId > 0 {
		ciMaterials, err := impl.ciPipelineMaterialRepository.GetByPipelineId(ciPipelineId)
		if err != nil {
			impl.logger.Errorw("error on fetching materials for", "ciPipelineId", ciPipelineId, "err", err)
			return nil, 0, err
		}

		var ciMaterialsArr []buildBean.CiPipelineMaterialResponse
		for _, m := range ciMaterials {
			if m.GitMaterial == nil {
				impl.logger.Warnw("git material are empty", "material", m)
				continue
			}
			res := buildBean.CiPipelineMaterialResponse{
				Id:              m.Id,
				GitMaterialId:   m.GitMaterialId,
				GitMaterialName: m.GitMaterial.Name[strings.Index(m.GitMaterial.Name, "-")+1:],
				Type:            string(m.Type),
				Value:           m.Value,
				Active:          m.Active,
				Url:             m.GitMaterial.Url,
			}
			ciMaterialsArr = append(ciMaterialsArr, res)
		}
		materialTriggerInfo.CiMaterials = ciMaterialsArr
	}
	var ciWorkflowId int
	if ciArtifactId > 0 {
		ciArtifact, err := impl.ciArtifactRepository.Get(ciArtifactId)
		if err != nil {
			impl.logger.Errorw("error fetching artifact data", "err", err)
			return nil, 0, err
		}

		// handling linked ci pipeline
		if ciArtifact.ParentCiArtifact > 0 && ciArtifact.WorkflowId == nil {
			ciArtifactId = ciArtifact.ParentCiArtifact
		}
		ciWf, err := impl.ciWorkflowRepository.FindLastTriggeredWorkflowByArtifactId(ciArtifactId)
		if err != nil && err != pg.ErrNoRows {
			impl.logger.Errorw("error fetching ci workflow data by artifact", "err", err)
			return nil, 0, err
		}
		if ciWf != nil {
			materialTriggerInfo.GitTriggers = ciWf.GitTriggers
			ciWorkflowId = ciWf.Id
		}
	}
	return materialTriggerInfo, ciWorkflowId, nil
}

func (impl *EventSimpleFactoryImpl) BuildExtraApprovalData(event Event, approvalActionRequest bean.UserApprovalActionRequest, cdPipeline *pipelineConfig.Pipeline, userId int32, imageTagNames []string, imageComment string) []Event {
	defaultSesConfig, defaultSmtpConfig, err := impl.getDefaultSESOrSMTPConfig()
	if err != nil {
		impl.logger.Errorw("found error in getting defaultSesConfig or  defaultSmtpConfig data", "err", err)
	}
	var events []Event
	if userId == 0 {
		return events
	}
	user, err := impl.userRepository.GetById(userId)
	if err != nil {
		impl.logger.Errorw("found error on getting user data ", "userId", userId)
	}
	payload, err := impl.setApprovalEventPayload(event, approvalActionRequest, cdPipeline, imageTagNames, imageComment)
	if err != nil {
		impl.logger.Errorw("error in setting payload", "error", err)
		return events
	}
	EmailIds := approvalActionRequest.ApprovalNotificationConfig.EmailIds
	for _, emailId := range EmailIds {
		newPayload := *payload
		setProviderForNotification(emailId, defaultSesConfig, defaultSmtpConfig, &newPayload)
		reqData := &notificationBean.ConfigDataForNotification{
			AppId: cdPipeline.AppId,
			EnvId: cdPipeline.EnvironmentId,
		}
		deploymentApprovalRequest := setDeploymentApprovalRequest(reqData, &approvalActionRequest, emailId)
		err = impl.createAndSetToken(nil, deploymentApprovalRequest, &newPayload)
		newPayload.TriggeredBy = user.EmailId
		event.Payload = &newPayload
		events = append(events, event)
	}

	return events
}
func (impl *EventSimpleFactoryImpl) setApprovalEventPayload(event Event, approvalActionRequest bean.UserApprovalActionRequest, cdPipeline *pipelineConfig.Pipeline, imageTagNames []string, imageComment string) (*Payload, error) {
	payload := &Payload{}
	payload.ImageComment = imageComment
	payload.ImageTagNames = imageTagNames
	ciArtifact, err := impl.ciArtifactRepository.Get(approvalActionRequest.ArtifactId)
	if err != nil {
		impl.logger.Errorw("error fetching ciArtifact", "ciArtifact", ciArtifact, "err", err)
		return payload, err
	}
	payload.AppName = cdPipeline.App.AppName
	payload.EnvName = cdPipeline.Environment.Name
	payload.PipelineName = cdPipeline.Name
	payload.DockerImageUrl = ciArtifact.Image
	_, dockerImageTag, err := ciArtifact.ExtractImageRepoAndTag()
	if err != nil {
		impl.logger.Errorw("error in getting image tag and repo", "err", err)
	}
	payload.ImageApprovalLink = fmt.Sprintf(bean3.ImageApprovalLink, event.AppId, cdPipeline.Id, dockerImageTag)
	return payload, err
}

func (impl *EventSimpleFactoryImpl) BuildExtraProtectConfigData(event Event, request notificationBean.ConfigDataForNotification, draftId int, DraftVersionId int) []Event {
	defaultSesConfig, defaultSmtpConfig, err := impl.getDefaultSESOrSMTPConfig()
	if err != nil {
		impl.logger.Errorw("found error in getting defaultSesConfig or  defaultSmtpConfig data", "err", err)
	}
	var events []Event
	if request.UserId == 0 {
		return events
	}
	user, err := impl.userRepository.GetById(request.UserId)
	if err != nil {
		impl.logger.Errorw("found error on getting user data ", "userId", request.UserId)
	}
	payload, err := impl.setEventPayload(request)
	if err != nil {
		impl.logger.Errorw("error in setting payload", "error", err)
		return events
	}
	for _, email := range request.EmailIds {
		newPayload := *payload
		setProviderForNotification(email, defaultSesConfig, defaultSmtpConfig, &newPayload)
		draftRequest := setDraftApprovalRequest(&request, draftId, DraftVersionId, email)
		err = impl.createAndSetToken(draftRequest, nil, &newPayload)
		if err != nil {
			impl.logger.Errorw("error in generating token for draft approval request", "err", err)

			return events
		}
		newPayload.TriggeredBy = user.EmailId
		event.Payload = &newPayload
		events = append(events, event)

	}

	return events
}

func (impl *EventSimpleFactoryImpl) BuildExtraArtifactPromotionData(event Event, request notificationBean.ArtifactPromotionNotificationRequest) []Event {

	var events []Event

	defaultSesConfig, defaultSmtpConfig, err := impl.getDefaultSESOrSMTPConfig()
	if err != nil {
		impl.logger.Errorw("found error in getting defaultSesConfig or  defaultSmtpConfig data", "err", err)
	}

	user, err := impl.userRepository.GetById(request.UserId)
	if err != nil {
		impl.logger.Errorw("found error on getting user data ", "userId", request.UserId)
	}

	payload := &Payload{
		ImageComment:                     request.ImageComment,
		ImageTagNames:                    request.ImageTags,
		AppName:                          request.AppName,
		EnvName:                          request.EnvName,
		PromotionArtifactSource:          request.ArtifactPromotionSource,
		DockerImageUrl:                   request.ImagePath,
		ArtifactPromotionRequestViewLink: fmt.Sprintf(bean3.ArtifactPromotionRequestViewLink, event.AppId, request.WorkflowId, request.EnvName),
	}

	for _, email := range request.PromoterAccessEmailIds {
		newPayload := *payload
		setProviderForNotification(email, defaultSesConfig, defaultSmtpConfig, &newPayload)
		artifactPromotionApprovalLink, err := impl.getArtifactPromotionApprovalLink(request, user, email)
		if err != nil {
			impl.logger.Errorw("error in building image promotion approval link", "err", err)
			continue
		}
		newPayload.ArtifactPromotionApprovalLink = artifactPromotionApprovalLink
		newPayload.TriggeredBy = user.EmailId
		event.Payload = &newPayload
		events = append(events, event)
	}
	return events
}

func (impl *EventSimpleFactoryImpl) getArtifactPromotionApprovalLink(request notificationBean.ArtifactPromotionNotificationRequest, user *repository.UserModel, email string) (string, error) {
	tokenCustomClaimsForNotification := &bean4.ArtifactPromotionApprovalNotificationClaims{
		AppId:           request.AppId,
		AppName:         request.AppName,
		EnvName:         request.EnvName,
		ArtifactId:      request.ArtifactId,
		UserId:          user.Id,
		EnvId:           request.EnvId,
		WorkflowId:      request.WorkflowId,
		ImageTags:       request.ImageTags,
		ImageComment:    request.ImageComment,
		PromotionSource: request.ArtifactPromotionSource,
		Image:           request.ImagePath,
		ApiTokenCustomClaims: bean4.ApiTokenCustomClaims{
			Email: email,
		},
	}
	token, err := impl.apiTokenServiceImpl.CreateApiJwtTokenForArtifactPromotion(tokenCustomClaimsForNotification, impl.apiTokenServiceImpl.TokenVariableConfig.GetExpiryTimeInMs())
	if err != nil {
		impl.logger.Errorw("error in generating token for deployment approval request", "err", err)
		return "", err
	}
	artifactPromotionApprovalLink := fmt.Sprintf(bean3.ArtifactPromotionApprovalLink, token)
	return artifactPromotionApprovalLink, nil
}

func (impl *EventSimpleFactoryImpl) createAndSetToken(draftRequest *bean4.DraftApprovalRequest, deploymentApprovalRequest *bean4.DeploymentApprovalRequest, payload *Payload) error {
	var emailId string
	if deploymentApprovalRequest != nil {
		emailId = deploymentApprovalRequest.EmailId
	} else {
		emailId = draftRequest.EmailId
	}
	user, err := impl.userRepository.FetchActiveUserByEmail(emailId)
	if err != nil {
		impl.logger.Errorw("error in fetching user", "emailId", emailId)
		return err
	}
	if deploymentApprovalRequest != nil {
		deploymentApprovalRequest.UserId = user.Id
		token, err := impl.apiTokenServiceImpl.CreateApiJwtTokenForNotification(deploymentApprovalRequest.GetClaimsForDeploymentApprovalRequest(), impl.apiTokenServiceImpl.TokenVariableConfig.GetExpiryTimeInMs())
		if err != nil {
			impl.logger.Errorw("error in generating token for deployment approval request", "err", err)
			return err
		}
		payload.ApprovalLink = fmt.Sprintf(bean3.DeploymentApprovalLink, token)

	} else {
		draftRequest.UserId = user.Id
		token, err := impl.apiTokenServiceImpl.CreateApiJwtTokenForNotification(draftRequest.GetClaimsForDraftApprovalRequest(), impl.apiTokenServiceImpl.TokenVariableConfig.GetExpiryTimeInMs())
		if err != nil {
			impl.logger.Errorw("error in generating token for draft approval request", "err", err)
			return err
		}
		payload.ApprovalLink = fmt.Sprintf(bean3.DraftApprovalLink, token)

	}
	return err
}
func setDraftApprovalRequest(request *notificationBean.ConfigDataForNotification, draftId int, DraftVersionId int, emailId string) *bean4.DraftApprovalRequest {
	draftRequest := &bean4.DraftApprovalRequest{
		DraftId:        draftId,
		DraftVersionId: DraftVersionId,
		NotificationApprovalRequest: bean4.NotificationApprovalRequest{
			AppId:   request.AppId,
			EnvId:   request.EnvId,
			EmailId: emailId,
		},
	}
	return draftRequest
}

func setDeploymentApprovalRequest(request *notificationBean.ConfigDataForNotification, approvalActionRequest *bean.UserApprovalActionRequest, emailId string) *bean4.DeploymentApprovalRequest {
	deploymentApprovalRequest := &bean4.DeploymentApprovalRequest{
		ApprovalRequestId: approvalActionRequest.ApprovalRequestId,
		ArtifactId:        approvalActionRequest.ArtifactId,
		PipelineId:        approvalActionRequest.PipelineId,
		NotificationApprovalRequest: bean4.NotificationApprovalRequest{
			AppId:   request.AppId,
			EnvId:   request.EnvId,
			EmailId: emailId,
		},
	}
	return deploymentApprovalRequest
}
func setProviderForNotification(emailId string, defaultSesConfig *repository2.SESConfig, defaultSmtpConfig *repository2.SMTPConfig, payload *Payload) {
	provider := &bean3.Provider{
		ConfigId:  0,
		Recipient: emailId,
	}
	if defaultSesConfig != nil && defaultSesConfig.Id != 0 {
		provider.Destination = util.SES
	} else if defaultSmtpConfig != nil && defaultSmtpConfig.Id != 0 {
		provider.Destination = util.SMTP
	}
	payload.Providers = append(payload.Providers, provider)

}

func (impl *EventSimpleFactoryImpl) setEventPayload(request notificationBean.ConfigDataForNotification) (*Payload, error) {
	payload := &Payload{}
	protectConfigLink := setProtectConfigLink(request)
	payload.ProtectConfigLink = protectConfigLink
	application, err := impl.appRepo.FindById(request.AppId)
	if err != nil {
		impl.logger.Errorw("error occurred while fetching application", "err", err)
		return payload, err
	}
	environment := &repository4.Environment{}
	if request.EnvId != -1 {
		environment, err = impl.envRepository.FindById(request.EnvId)
		if err != nil {
			impl.logger.Errorw("error occurred while fetching environment", "err", err)
			return payload, err
		}
	}
	payload.AppName = application.AppName
	payload.EnvName = environment.Name
	payload.ProtectConfigComment = request.UserComment
	payload.ProtectConfigFileType = string(request.Resource)
	if request.Resource == notificationBean.DeploymentTemplate {
		payload.ProtectConfigFileName = string(notificationBean.DeploymentTemplate)
	} else {
		payload.ProtectConfigFileName = request.ResourceName
	}

	return payload, err
}
func setProtectConfigLink(request notificationBean.ConfigDataForNotification) string {
	var ProtectConfigLink string
	var isAppLevel bool
	if request.EnvId == -1 {
		isAppLevel = true
	}
	if isAppLevel {
		ProtectConfigLink = getAppLevelUrl(request)
	} else {
		ProtectConfigLink = getEnvLevelUrl(request)
	}
	return ProtectConfigLink
}

func getEnvLevelUrl(request notificationBean.ConfigDataForNotification) (ProtectConfigLink string) {
	switch request.Resource {
	case notificationBean.CM:
		ProtectConfigLink = fmt.Sprintf(bean3.EnvLevelBaseUrl+"configmap/%s", request.AppId, request.EnvId, request.ResourceName)
	case notificationBean.CS:
		ProtectConfigLink = fmt.Sprintf(bean3.EnvLevelBaseUrl+"secrets/%s", request.AppId, request.EnvId, request.ResourceName)
	case notificationBean.DeploymentTemplate:
		ProtectConfigLink = fmt.Sprintf(bean3.EnvLevelBaseUrl+"deployment-template", request.AppId, request.EnvId)

	}
	return ProtectConfigLink
}

func getAppLevelUrl(request notificationBean.ConfigDataForNotification) (ProtectConfigLink string) {
	switch request.Resource {
	case notificationBean.CM:
		ProtectConfigLink = fmt.Sprintf(bean3.AppLevelBaseUrl+"configmap/%s", request.AppId, request.ResourceName)
	case notificationBean.CS:
		ProtectConfigLink = fmt.Sprintf(bean3.AppLevelBaseUrl+"secrets/%s", request.AppId, request.ResourceName)
	case notificationBean.DeploymentTemplate:
		ProtectConfigLink = fmt.Sprintf(bean3.AppLevelBaseUrl+"deployment-template", request.AppId)
	}
	return ProtectConfigLink
}

func (impl *EventSimpleFactoryImpl) getDefaultSESOrSMTPConfig() (*repository2.SESConfig, *repository2.SMTPConfig, error) {
	defaultSesConfig, err := impl.sesNotificationRepository.FindDefault()
	if err != nil && err != pg.ErrNoRows {
		impl.logger.Errorw("error fetching defaultSesConfig", "defaultSesConfig", defaultSesConfig, "err", err)
		return defaultSesConfig, nil, nil

	}
	defaultSmtpConfig := &repository2.SMTPConfig{}
	if err == pg.ErrNoRows {
		defaultSmtpConfig, err = impl.smtpNotificationRepository.FindDefault()
		if err != nil {
			impl.logger.Errorw("error fetching defaultSmtpConfig", "defaultSmtpConfig", defaultSmtpConfig, "err", err)
			return defaultSesConfig, defaultSmtpConfig, nil
		}
	}
	return defaultSesConfig, defaultSmtpConfig, nil
}

func (impl *EventSimpleFactoryImpl) getDeploymentWindowAuditMessage(artifactId int, wfrId int) (string, error) {

	filters, err := impl.resourceFilterAuditService.GetLatestByRefAndMultiSubjectAndFilterType(resourceFilter.CdWorkflowRunner, wfrId, resourceFilter.Artifact, []int{artifactId}, resourceFilter.DEPLOYMENT_WINDOW)
	if err != nil {
		return "", err
	}
	if len(filters) != 1 {
		return "", nil
	}
	filter := filters[0]

	if filter == nil || len(filter.FilterHistoryObjects) == 0 {
		return "", nil
	}
	auditData := bean5.GetAuditDataFromSerializedValue(filter.FilterHistoryObjects)
	return auditData.TriggerMessage, nil
}

func (impl *EventSimpleFactoryImpl) BuildScoopNotificationEventProviders(configType util.Channel, configName string, emailIds []string, data notificationBean.InterceptEventNotificationData) (*Payload, error) {

	if configType == util.SES || configType == util.SMTP {
		if len(emailIds) == 0 {
			return nil, errors.New("emailIds cannot be empty for ses/smtp type")
		}
		defaultSesConfig, defaultSmtpConfig, err := impl.getDefaultSESOrSMTPConfig()
		if err != nil {
			impl.logger.Errorw("found error in getting defaultSesConfig or  defaultSmtpConfig data", "err", err)
			if errors.Is(err, pg.ErrNoRows) {
				return nil, errors.New("no default ses/smtp configuration found, cannot send notification")
			}
			return nil, util2.DefaultApiError().WithUserMessage("error in finding ses/smtp config").WithInternalMessage(err.Error()).WithHttpStatusCode(http.StatusInternalServerError)
		}

		providers := make([]*bean3.Provider, 0, len(emailIds))
		for _, emailId := range emailIds {
			provider := &bean3.Provider{
				ConfigId:  0,
				Recipient: emailId,
			}
			if defaultSesConfig != nil && defaultSesConfig.Id != 0 {
				provider.Destination = util.SES
			} else if defaultSmtpConfig != nil && defaultSmtpConfig.Id != 0 {
				provider.Destination = util.SMTP
			}
			providers = append(providers, provider)
		}

		return &Payload{
			Providers: providers,
			ScoopNotificationConfig: map[string]interface{}{
				"data": data,
			},
		}, nil
	}
	return impl.buildWebhookOrSlackNotification(configType, configName, data)
}

func (impl *EventSimpleFactoryImpl) buildWebhookOrSlackNotification(configType util.Channel, configName string, data notificationBean.InterceptEventNotificationData) (*Payload, error) {
	var scoopNotifyConfig = make(map[string]interface{})
	if configType == util.Webhook {
		webhookConfig, err := impl.webhookConfigRepo.FindOneByName(configName)
		if err != nil {
			if errors.Is(err, pg.ErrNoRows) {
				return nil, errors.New("config with given name not found")
			}
			return nil, err
		}

		scoopNotifyConfig["webhookConfig"] = map[string]interface{}{
			"web_hook_url": webhookConfig.WebHookUrl,
			"config_name":  webhookConfig.ConfigName,
			"header":       webhookConfig.Header,
			"payload":      webhookConfig.Payload,
			"description":  webhookConfig.Description,
		}

	} else if configType == util.Slack {
		// we also have a teamId option. future, we can search by teamId.
		slackConfig, err := impl.slackConfigRepo.FindOneByName(configName)
		if err != nil {
			if errors.Is(err, pg.ErrNoRows) {
				return nil, errors.New("config with given name not found")
			}
			return nil, err
		}

		scoopNotifyConfig["slackConfig"] = map[string]interface{}{
			"web_hook_url": slackConfig.WebHookUrl,
			"config_name":  slackConfig.ConfigName,
			"description":  slackConfig.Description,
		}

	}

	scoopNotifyConfig["data"] = data
	return &Payload{
		ScoopNotificationConfig: scoopNotifyConfig,
	}, nil

}
