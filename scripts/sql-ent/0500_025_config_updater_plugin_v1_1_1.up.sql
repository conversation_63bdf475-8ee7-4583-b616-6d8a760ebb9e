INSERT INTO "plugin_parent_metadata" ("id", "name", "identifier", "description", "type", "icon", "deleted", "created_on", "created_by", "updated_on", "updated_by")
SELECT nextval('id_seq_plugin_parent_metadata'), 'Devtron Config Updater','config-updater', 'Update the configurations for the environment','PRESET','https://raw.githubusercontent.com/devtron-labs/devtron/main/assets/devtron-logo-plugin.png','f', 'now()', 1, 'now()', 1
    WHERE NOT EXISTS (
    SELECT 1
    FROM plugin_parent_metadata
    WHERE identifier='config-updater'
    AND deleted = false
);

-- update the plugin_metadata with the plugin_parent_metadata_id
UPDATE plugin_metadata
SET plugin_parent_metadata_id = (
    SELECT id
    FROM plugin_parent_metadata
    WHERE identifier='config-updater'
      AND deleted = false
),plugin_version='1.0.0'
WHERE name='Devtron Config Updater'
  AND (
        plugin_parent_metadata_id IS NULL
        OR plugin_parent_metadata_id = 0
    )
  AND deleted = false;


UPDATE plugin_metadata SET is_latest = false WHERE id = (SELECT id FROM plugin_metadata WHERE name= 'Devtron Config Updater' and is_latest= true);


INSERT INTO "plugin_metadata" ("id", "name", "description","deleted", "created_on", "created_by", "updated_on", "updated_by","plugin_parent_metadata_id","plugin_version","is_deprecated","is_latest")
VALUES (nextval('id_seq_plugin_metadata'), 'Devtron Config Updater','Update the configurations for the environment','f', 'now()', 1, 'now()', 1, (SELECT id FROM plugin_parent_metadata WHERE identifier='config-updater'),'1.1.1', false, true);


INSERT INTO "plugin_stage_mapping" ("plugin_id","stage_type","created_on", "created_by", "updated_on", "updated_by")
VALUES ((SELECT id FROM plugin_metadata WHERE plugin_version='1.1.1' and name='Devtron Config Updater' and deleted= false),0,'now()', 1, 'now()', 1);


INSERT INTO "plugin_pipeline_script" ("id", "script","type","deleted","created_on", "created_by", "updated_on", "updated_by")VALUES (
    nextval('id_seq_plugin_pipeline_script'),
    E'#!/bin/sh
pipeline_type=$(echo $CI_CD_EVENT | jq -r \'.type\')
app_id=$(echo "$CI_CD_EVENT" | jq ".commonWorkflowRequest.appId")
env_id=$(echo "$CI_CD_EVENT" | jq ".commonWorkflowRequest.Env.Id")
mkdir config-updater-assests
cd config-updater-assests
echo "$ExecutionScript" > execution_file.sh
sh execution_file.sh
docker run -v /devtroncd/config-updater-assests:/usr/src/app/files \\
 -e pipeline_type=$pipeline_type \\
 -e app_id=$app_id \\
 -e env_id=$env_id \\
 -e DashboardUrl=$DashboardUrl \\
 -e DevtronApiToken=$DevtronApiToken \\
 -e FileName=$FileName \\
 -e FileNameToBeMounted=$FileNameToBeMounted \\
 -e path=$path \\
 -e setSubpath=$setSubpath \\
 -e MountAs=$MountAs \\
 -e ResourceName=$ResourceName \\
 -e FilePermission=$FilePermission \\
 -e ResourceType=$ResourceType quay.io/devtron/k8s-utils:config-updater-v1.1.1
exit_code=$?
if [ $exit_code == 2 ];then
    echo "The config updater has been failed aborting the current process...."
    exit $exit_code
fi',
    'SHELL',
    'f',
    'now()',
    1,
    'now()',
    1
);

INSERT INTO "plugin_step" ("id", "plugin_id","name","description","index","step_type","script_id","deleted", "created_on", "created_by", "updated_on", "updated_by")
VALUES (nextval('id_seq_plugin_step'),(SELECT id FROM plugin_metadata WHERE plugin_version='1.1.1' and name='Devtron Config Updater' and deleted= false),'Step 1','Step 1 - Triggering Config updater','1','INLINE',(SELECT last_value FROM id_seq_plugin_pipeline_script),'f','now()', 1, 'now()', 1);


INSERT INTO "plugin_step_variable" ("id", "plugin_step_id", "name", "format", "description", "is_exposed", "allow_empty_value", "variable_type", "value_type", "variable_step_index", "deleted", "created_on", "created_by", "updated_on", "updated_by","default_value")
VALUES (nextval('id_seq_plugin_step_variable'), (SELECT ps.id FROM plugin_metadata p inner JOIN plugin_step ps on ps.plugin_id=p.id WHERE p.plugin_version='1.1.1' and p.name='Devtron Config Updater' and p.deleted=false and ps."index"=1 and ps.deleted=false), 'ExecutionScript','STRING','Provide the script to create the config/secret file.',true,true,'INPUT','NEW',1 ,'f','now()', 1, 'now()', 1, null),
(nextval('id_seq_plugin_step_variable'), (SELECT ps.id FROM plugin_metadata p inner JOIN plugin_step ps on ps.plugin_id=p.id WHERE p.plugin_version='1.1.1' and p.name='Devtron Config Updater' and p.deleted=false and ps."index"=1 and ps.deleted=false), 'DashboardUrl','STRING','Dashboard url of Devtron for eg. https://devtron.example.com',true,false,'INPUT','NEW',1 ,'f','now()', 1, 'now()', 1,null),
(nextval('id_seq_plugin_step_variable'), (SELECT ps.id FROM plugin_metadata p inner JOIN plugin_step ps on ps.plugin_id=p.id WHERE p.plugin_version='1.1.1' and p.name='Devtron Config Updater' and p.deleted=false and ps."index"=1 and ps.deleted=false), 'DevtronApiToken','STRING','Devtron API token with required permissions.',true,false,'INPUT','NEW',1 ,'f','now()', 1, 'now()', 1, null),
(nextval('id_seq_plugin_step_variable'), (SELECT ps.id FROM plugin_metadata p inner JOIN plugin_step ps on ps.plugin_id=p.id WHERE p.plugin_version='1.1.1' and p.name='Devtron Config Updater' and p.deleted=false and ps."index"=1 and ps.deleted=false), 'FileName','STRING','Name of the file to be mounted.',true,false,'INPUT','NEW',1 ,'f','now()', 1, 'now()', 1, null),
(nextval('id_seq_plugin_step_variable'), (SELECT ps.id FROM plugin_metadata p inner JOIN plugin_step ps on ps.plugin_id=p.id WHERE p.plugin_version='1.1.1' and p.name='Devtron Config Updater' and p.deleted=false and ps."index"=1 and ps.deleted=false), 'FileNameToBeMounted','STRING','Name of the file to access inside the container. Default is same as FileName',true,true,'INPUT','NEW',1 ,'f','now()', 1, 'now()', 1, null),
(nextval('id_seq_plugin_step_variable'), (SELECT ps.id FROM plugin_metadata p inner JOIN plugin_step ps on ps.plugin_id=p.id WHERE p.plugin_version='1.1.1' and p.name='Devtron Config Updater' and p.deleted=false and ps."index"=1 and ps.deleted=false), 'path','STRING','Path on which the file is to be mounted.',true,true,'INPUT','NEW',1 ,'f','now()', 1, 'now()', 1, null),
(nextval('id_seq_plugin_step_variable'), (SELECT ps.id FROM plugin_metadata p inner JOIN plugin_step ps on ps.plugin_id=p.id WHERE p.plugin_version='1.1.1' and p.name='Devtron Config Updater' and p.deleted=false and ps."index"=1 and ps.deleted=false), 'setSubpath','STRING','true or false, true if you want to set subpath. Default is false',true,true,'INPUT','NEW',1 ,'f','now()', 1, 'now()', 1, 'false'),
(nextval('id_seq_plugin_step_variable'), (SELECT ps.id FROM plugin_metadata p inner JOIN plugin_step ps on ps.plugin_id=p.id WHERE p.plugin_version='1.1.1' and p.name='Devtron Config Updater' and p.deleted=false and ps."index"=1 and ps.deleted=false), 'MountAs','STRING','How do you want to mount this file? Options: volume/environment.',true,false,'INPUT','NEW',1 ,'f','now()', 1, 'now()', 1, null),
(nextval('id_seq_plugin_step_variable'), (SELECT ps.id FROM plugin_metadata p inner JOIN plugin_step ps on ps.plugin_id=p.id WHERE p.plugin_version='1.1.1' and p.name='Devtron Config Updater' and p.deleted=false and ps."index"=1 and ps.deleted=false), 'ResourceName','STRING','Name of the Secret/configMap to be created.',true,false,'INPUT','NEW',1 ,'f','now()', 1, 'now()', 1, null),
(nextval('id_seq_plugin_step_variable'), (SELECT ps.id FROM plugin_metadata p inner JOIN plugin_step ps on ps.plugin_id=p.id WHERE p.plugin_version='1.1.1' and p.name='Devtron Config Updater' and p.deleted=false and ps."index"=1 and ps.deleted=false), 'FilePermission','STRING','Set the permission of the file after mounting as a volume. Default is 0644',true,true,'INPUT','NEW',1 ,'f','now()', 1, 'now()', 1, null),
(nextval('id_seq_plugin_step_variable'), (SELECT ps.id FROM plugin_metadata p inner JOIN plugin_step ps on ps.plugin_id=p.id WHERE p.plugin_version='1.1.1' and p.name='Devtron Config Updater' and p.deleted=false and ps."index"=1 and ps.deleted=false), 'ResourceType','STRING','Specify the type of resource the file is to be mounted as ConfigMap or ConfigSecret. Options: cm/cs',true,false,'INPUT','NEW',1 ,'f','now()', 1, 'now()', 1, null);
