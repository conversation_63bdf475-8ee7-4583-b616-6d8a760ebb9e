DELETE FROM plugin_step_variable WHERE plugin_step_id =(SELECT ps.id FROM plugin_metadata p inner JOIN plugin_step ps on ps.plugin_id=p.id WHERE p.plugin_version='1.1.1' and p.name='Devtron Config Updater' and p.deleted=false and ps."index"=1 and ps.deleted=false);
DELETE FROM plugin_step WHERE plugin_id = (SELECT id FROM plugin_metadata WHERE plugin_version='1.1.1' and name='Devtron Config Updater' and deleted=false);
DELETE from plugin_pipeline_script where id = (SELECT script_id from plugin_step WHERE plugin_id=(SELECT id FROM plugin_metadata WHERE plugin_version='1.1.1' and name='Devtron Config Updater' and deleted=false));
DELETE FROM plugin_stage_mapping WHERE plugin_id =(SELECT id FROM plugin_metadata WHERE plugin_version='1.0.0' and name='Devtron Config Updater' and deleted=false);
DELETE FROM plugin_metadata WHERE plugin_version='1.1.1' and name ='Devtron Config Updater' and deleted=false;
UPDATE plugin_metadata SET is_latest = true WHERE id = (SELECT id FROM plugin_metadata WHERE name= 'Devtron Config Updater' and is_latest= false and plugin_version='1.0.0');
