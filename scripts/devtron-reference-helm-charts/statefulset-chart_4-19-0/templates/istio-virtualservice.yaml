{{- with .Values.istio }}
{{- if and .enable .virtualService.enabled }}
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: {{  template ".Chart.Name .fullname" $ }}-virtualservice
  labels:
    app: {{ template ".Chart.Name .name" $ }}
    appId: {{ $.Values.app | quote }}
    envId: {{ $.Values.env | quote }}
    chart: {{ template ".Chart.Name .chart" $ }}
    release: {{ $.Release.Name }}
{{- if $.Values.appLabels }}
{{ toYaml $.Values.appLabels | indent 4 }}
{{- end }}
    {{- if .virtualService.labels }}
{{ toYaml .virtualService.labels | indent 4 }}
    {{- end }}
{{- if .virtualService.annotations }}
  annotations:
{{ toYaml .virtualService.annotations | indent 4 }}
{{- end }}
spec:
{{- if or .gateway.enabled .virtualService.gateways }}
  gateways:
  {{- if .gateway.enabled }} 
    - {{ template ".Chart.Name .fullname" $ }}-istio-gateway
  {{- end }}
  {{- range .virtualService.gateways }}
    - {{ . | quote }}
  {{- end }}
{{- end }}
{{- if or .gateway.enabled .virtualService.hosts }}
  hosts:
  {{- if .gateway.enabled }}
    - {{ .gateway.host | quote }}
  {{- end }}
  {{- range .virtualService.hosts }}
    - {{ . | quote }}
  {{- end }}
{{- else }}
  hosts: 
    - "{{  include ".servicename" $ }}.{{ $.Release.Namespace }}.svc.cluster.local"
{{- end }}
{{- if $.Values.istio.virtualService.http }}
  http: 
{{ toYaml $.Values.istio.virtualService.http | indent 4 }}
{{- end }}
{{- end }}
{{- end }}