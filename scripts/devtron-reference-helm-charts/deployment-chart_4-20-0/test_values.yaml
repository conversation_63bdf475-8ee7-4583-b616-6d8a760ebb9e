# Default values for myapp.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
kedaAutoscaling:
  enabled: true
  envSourceContainerName: "" # Optional. Default: .spec.template.spec.containers[0]
  cooldownPeriod: 300 # Optional. Default: 300 seconds
  minReplicaCount: 1 
  maxReplicaCount: 2
  idleReplicaCount: 0 # Optional. Must be less than minReplicaCount
  pollingInterval: 30 # Optional. Default: 30 seconds
  # The fallback section is optional. It defines a number of replicas to fallback to if a scaler is in an error state.
  fallback: {} # Optional. Section to specify fallback options
    # failureThreshold: 3 # Mandatory if fallback section is included
    # replicas: 6
  advanced: {}
    # horizontalPodAutoscalerConfig: # Optional. Section to specify HPA related options
    # behavior: # Optional. Use to modify HPA's scaling behavior
    #   scaleDown:
    #     stabilizationWindowSeconds: 300
    #     policies:
    #     - type: Percent
    #       value: 100
    #       periodSeconds: 15
  triggers: 
  - type: kubernetes-workload
    name: trig_one
    metadata:
      podSelector: 'pod=workload-test'
  - type: metrics-api
    name: trig_two
    metadata:
      url: "https://mockbin.org/bin/336a8d99-9e09-4f1f-979d-851a6d1b1423"
      valueLocation: "tasks"

  triggerAuthentication:
    enabled: true
    name: "trigger-test"
    spec: {}
  authenticationRef: {}

deploymentLabels:
  name: kunalverma
  Company: Devtron
  Job: DevRel

deploymentAnnotations:
  name: kunalverma
  Company: Devtron
  Job: DevRel

containerSpec:
  lifecycle:
    enabled: true
    preStop:
      exec:
        command: ["sleep","10"]
    postStart:
      httpGet:
        host: example.com
        path: /example
        port: 90

imagePullSecrets:
  - test1
  - test2
replicaCount: 1
MinReadySeconds: 5
MaxSurge: 1
MaxUnavailable: 0
GracePeriod: 30
ContainerPort:
  - name: app
    port: 8080
    servicePort: 80
    envoyTimeout: 15
    targetPort: 8080
    envoyPort: 8799
    useHTTP2: false
    supportStreaming: false
    idleTimeout: 1800s
    servicemonitor:
      enabled: true
      path: /abc
      scheme: 'http'
      interval: 30s
      scrapeTimeout: 20s
      metricRelabelings:
        - sourceLabels: [namespace]
          regex: '(.*)'
          replacement: myapp
          targetLabel: target_namespace

  - name: app1
    port: 8090
    targetPort: 1234
    servicePort: 8080
    useGRPC: true
    servicemonitor:
      enabled: true
  - name: app2
    port: 8091
    servicePort: 8081
    useGRPC: true

pauseForSecondsBeforeSwitchActive: 30
waitForSecondsBeforeScalingDown: 30
autoPromotionSeconds: 30

Spec:
  Affinity:
    Key:
    #  Key: kops.k8s.io/instancegroup
    Values:


image:
  pullPolicy: IfNotPresent

autoscaling:
  enabled: true
  MinReplicas: 1
  MaxReplicas: 2
  TargetCPUUtilizationPercentage: 90
  TargetMemoryUtilizationPercentage: 80
  behavior: {}
#    scaleDown:
#      stabilizationWindowSeconds: 300
#      policies:
#      - type: Percent
#        value: 100
#        periodSeconds: 15
#    scaleUp:
#      stabilizationWindowSeconds: 0
#      policies:
#      - type: Percent
#        value: 100
#        periodSeconds: 15
#      - type: Pods
#        value: 4
#        periodSeconds: 15
#      selectPolicy: Max

  extraMetrics: []
#    - external:
#        metricName: pubsub.googleapis.com|subscription|num_undelivered_messages
#        metricSelector:
#          matchLabels:
#            resource.labels.subscription_id: echo-read
#        targetAverageValue: "2"
#      type: External
#

secret:
  enabled: false

service:
  type: ClusterIP
  #  name: "1234567890123456789012345678901234567890123456789012345678901234567890"
  annotations: {}
    # test1: test2
  # test3: test4

istio:
  enable: true
  gateway:
    enabled: true
    labels: {}
    annotations: {}
    # host: example.com
    hosts:
    - kamal
    - "example4.com"
    tls:
      enabled: true
      secretName: example-tls-secret
  virtualService:
    enabled: true
    labels: {}
    annotations: {}
    gateways: []
    hosts: []
    http: 
      # - match:
      #   - uri:
      #       prefix: /v1
      #   - uri:
      #       prefix: /v2
      #   rewriteUri: /
      #   timeout: 12
      #   headers:
      #     request:
      #       add:
      #         x-some-header: "value"
      #   retries:
      #     attempts: 2 
      #     perTryTimeout: 3s 
      #   route:
      #   - destination:
      #       host: service1
      #       port: 80
      # - route:
      #   - destination:
      #       host: service2

flaggerCanary:
  enabled: false
  labels: {}
  annotations: {}
  createIstioGateway:
    enabled: false
    labels: {}
    annotations: {}
    host: example.com
    tls:
      enabled: false
      secretName: example-tls-secret
  # Istio gateways (optional)
  addOtherGateways: []
  # Istio virtual service host names (optional)
  addOtherHosts: []
  # Istio gateway refs (optional)
  gatewayRefs:
    # - name: istio-gateway
    #   namespace: istio-system
  #service port
  port: 80
  #containerPort
  targetPort: 8080
  # discover all port open in container
  portDiscovery: false
  # application protocol (optional)
  appProtocol: 
  # Istio retry policy (optional)
  retries: 
    # attempts: 3
    # perTryTimeout: 1s
    # retryOn: "gateway-error,connect-failure,refused-stream"
  # HTTP match conditions (optional)
  match:
    - uri:
        prefix: /
  # HTTP rewrite (optional)
  rewriteUri: 
  # timeout (optional)
  timeout:
  # Add headers (optional)
  headers:
    # request:
    #   add:
    #     x-some-header: "value"
  # cross-origin resource sharing policy (optional)
  corsPolicy:
    # allowOrigin:
    #   - example.com
    # allowMethods:
    #   - GET
    # allowCredentials: false
    # allowHeaders:
    #   - x-some-header
    # maxAge: 24h
  analysis:
    # schedule interval (default 60s)
    interval: 5s
    # max number of failed metric checks before rollback
    threshold: 10
    # max traffic percentage routed to canary
    # percentage (0-100)
    maxWeight: 50
    # canary increment step
    # percentage (0-100)
    stepWeight: 5
  thresholds:
    # minimum req success rate (non 5xx responses)
    # percentage (0-100)
    successRate: 90
    # maximum req duration P99
    # milliseconds
    latency: 500
  loadtest:
    enabled: true
    # load tester address 
    url: http://flagger-loadtester.test/

server:
  deployment:
    image_tag: 1-95af053
    image: ""
deploymentType: "RECREATE"

topologySpreadConstraints:
  - maxSkew: 1
    topologyKey: zone
    whenUnsatisfiable: DoNotSchedule
    autoLabelSelector: true
    customLabelSelector:
      foo: bar

EnvVariables:
  - name: FLASK_ENV
    value: qa

LivenessProbe:
  Path: /
  port: 8080
  initialDelaySeconds: 20
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 5
  failureThreshold: 3
  httpHeaders:
    - name: Custom-Header
      value: abc
    - name: Custom-Header2
      value: xyz

ReadinessProbe:
  Path: /
  port: 8080
  initialDelaySeconds: 20
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 5
  failureThreshold: 3
  httpHeaders:
    - name: Custom-Header
      value: abc
    
StartupProbe:
  Path: "/"
  port: 8080
  initialDelaySeconds: 20
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 5
  failureThreshold: 3
  httpHeaders: []
  command: []
  tcp: false

prometheus:
  release: monitoring

servicemonitor:
  additionalLabels: {}

prometheusRule:
  enabled: true
  additionalLabels: {}
  namespace: ""
  rules:
    # These are just examples rules, please adapt them to your needs
    - alert: TooMany500s
      expr: 100 * ( sum( nginx_ingress_controller_requests{status=~"5.+"} ) / sum(nginx_ingress_controller_requests) ) > 5
      for: 1m
      labels:
        severity: critical
      annotations:
        description: Too many 5XXs
        summary: More than 5% of the all requests did return 5XX, this require your attention
    - alert: TooMany400s
      expr: 100 * ( sum( nginx_ingress_controller_requests{status=~"4.+"} ) / sum(nginx_ingress_controller_requests) ) > 5
      for: 1m
      labels:
        severity: critical
      annotations:
        description: Too many 4XXs
        summary: More than 5% of the all requests did return 4XX, this require your attention


ingress:
  enabled: true
  className: nginx
  annotations: {}
#    nginx.ingress.kubernetes.io/rewrite-target: /
#    nginx.ingress.kubernetes.io/ssl-redirect: "false"
#    kubernetes.io/ingress.class: nginx
#    kubernetes.io/tls-acme: "true"
#    nginx.ingress.kubernetes.io/canary: "true"
#    nginx.ingress.kubernetes.io/canary-weight: "10"
#    Old Ingress Format
#  host: "ingress-example.com"
#  path: "/app"

#    New Ingress Format
  hosts:
    - host: chart-example1.local
      pathType: "ImplementationSpecific"
      paths:
        - /example1
      
    - host: chart-example2.local
      pathType: "ImplementationSpecific"
      paths:
        - /example2
        - /example2/healthz
      additionalBackends: 
        - path: /example1
          pathType: "ImplementationSpecific"
          backend:
            service:
              name: test-service
              port:
                number: 80

  tls: []
### Legacy Ingress Format ##
#  host: abc.com
#  path: "/"
#  pathType: "ImplementationSpecific"
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

ingressInternal:
  enabled: true
  className: nginx-internal
  annotations: {}
 #    kubernetes.io/ingress.class: nginx
 #    kubernetes.io/tls-acme: "true"
 #    nginx.ingress.kubernetes.io/canary: "true"
 #    nginx.ingress.kubernetes.io/canary-weight: "10"
  hosts:
    - host: chart-example1.internal
      pathType: "ImplementationSpecific"
      paths:
        - /example1
      additionalBackends: 
        - path: /internal
          pathType: "ImplementationSpecific"
          backend:
            service:
              name: test-service-internal
              port:
                number: 80
        - path: /internal-01
          pathType: "ImplementationSpecific"
          backend:
            service:
              name: test-service-internal
              port:
                number: 80
    - host: chart-example2.internal
      pathType: "ImplementationSpecific"
      paths:
        - /example2
        - /example2/healthz
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

winterSoldier:
  apiVersion: pincher.devtron.ai/v1alpha1 
  enabled: true
  annotations: {}
  labels: {}
  type: Deployment
  timeRangesWithZone:
    timeZone: "Asia/Kolkata"
    timeRanges: 
      - timeFrom: 00:00
        timeTo: 23:59:59
        weekdayFrom: Sat
        weekdayTo: Sun
      - timeFrom: 00:00
        timeTo: 08:00
        weekdayFrom: Mon
        weekdayTo: Fri
      - timeFrom: 20:00
        timeTo: 23:59:59
        weekdayFrom: Mon
        weekdayTo: Fri
  action: scale
  targetReplicas: [1,1,1]
  fieldSelector: 
    - AfterTime(AddTime(ParseTime({{metadata.creationTimestamp}}, '2006-01-02T15:04:05Z'), '5m'), Now())


dbMigrationConfig:
  enabled: false

command:
  workingDir: /app
  enabled: false
  value: ["ls"]

args:
  enabled: false
  value: []

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 1
    memory: 200Mi
  requests:
    cpu: 0.10
    memory: 100Mi

volumeMounts: []
#     - name: log-volume
#       mountPath: /var/log

volumes: []
#     - name: log-volume
#       emptyDir: {}


nodeSelector: {}


#used for deployment algo selection
orchestrator.deploymant.algo: 1

ConfigMaps:
  enabled: false
  maps: []
#  - name: config-map-1
#    type: environment
#    external: false
#    data:
#     key1: key1value-1
#     key2: key2value-1
#     key3: key3value-1
#  - name: config-map-2
#    type: volume
#    external: false
#    mountPath: /etc/config/2
#    filePermission: 0400
#    data:
#     key1: |
#      club : manchester utd
#      nation : england
#     key2: abc-2
#     key3: abc-2
#  - name: config-map-3
#    type: environment
#    external: true
#    mountPath: /etc/config/3
#    data: []
#  - name: config-map-4
#    type: volume
#    external: true
#    mountPath: /etc/config/4
#    data: []


ConfigSecrets:
  enabled: true
  secrets:
  - name: config-secret-1
    type: environment
    external: false
    externalType: AWSSecretsManager
    esoSecretData:
      secretStore:
        aws:
          service: SecretsManager
          region: us-east-1
          auth:
            secretRef:
              accessKeyIDSecretRef:
                name: awssm-secret
                key: access-key
              secretAccessKeySecretRef:
                name: awssm-secret
                key: secret-access-key
      esoData:
        - secretKey: prod-mysql-password
          key: secrets/prod-mysql-secrets
          property: prodPassword
        - secretKey: prod-mysql-password
          key: secrets/prod-mysql-secrets
          property: prodPassword
        - secretKey: prod-mysql-password
          key: secrets/prod-mysql-secrets
          property: prodPassword
        - secretKey: prod-mysql-password
          key: secrets/prod-mysql-secrets
          property: prodPassword
    data:
      key1: key1value-1
      key2: key2value-1
      key3: key3value-1
  - name: config-secret-2
    type: environment
    external: false
    externalType: ESO_HashiCorpVault
    esoSecretData:
      secretStore:
        vault:
          server: "http://my.vault.server:8200"
          path: "secret"
          version: "v2"
          auth:
            tokenSecretRef:
              name: vault-token
              key: token
      esoData:
        - secretKey: prod-mysql-password
          key: secrets/prod-mysql-secrets
          property: prodPassword
        - secretKey: prod-mysql-password
          key: secrets/prod-mysql-secrets
          property: prodPassword
        - secretKey: prod-mysql-password
          key: secrets/prod-mysql-secrets
          property: prodPassword
    date:
      key1: key1value-1
      key2: key2value-1
      key3: key3value-1

#  - name: config-secret-2
#    type: volume
#    external: false
#    mountPath: /etc/config/2
#    data:
#     key1: |
#      club : manchester utd
#      nation : england
#     key2: abc-2


initContainers:
  ## Additional init containers to run before the Scheduler pods.
  ## for example, be used to run a sidecar that chown Logs storage .
  - command: ["sh", "-c", "chown -R 1000:1000 logs"]
    reuseContainerImage: true
    volumeMounts:
      - mountPath: /usr/local/airflow/logs
        name: logs-data
    securityContext:
      privileged: true
      runAsUser: 1000
      runAsGroup: 3000
      fsGroup: 2000
  - name: init-migrate
    image: busybox:latest
    command: ["sh", "-c", "chown -R 1000:1000 logs"]
    volumeMounts:
      - mountPath: /usr/local/airflow/logs
        name: logs-data
    securityContext:
      capabilities:
        drop:
          - ALL

containers:
  # Additional init containers to run before the Scheduler pods.
  # for example, be used to run a sidecar that chown Logs storage .
  - name: volume-mount-hack
    image: busybox
    command: ["sh", "-c", "chown -R 1000:1000 logs"]
    volumeMounts:
      - mountPath: /usr/local/airflow/logs
        name: logs-data


rawYaml:
 - apiVersion: v1
   kind: Service
   metadata:
    annotations:
    labels:
     app: sample-metrics-app
    name: sample-metrics-app
    namespace: default
   spec:
    ports:
     - name: web
       port: 80
       protocol: TCP
       targetPort: 8080
    selector:
     app: sample-metrics-app
    sessionAffinity: None
    type: ClusterIP
 - apiVersion: v1
   kind: Service
   metadata:
    annotations:
    labels:
     app: sample-metrics-app
    name: sample-metrics-app
    namespace: default
   spec:
    ports:
     - name: web
       port: 80
       protocol: TCP
       targetPort: 8080
    selector:
     app: sample-metrics-app
    sessionAffinity: None
    type: ClusterIP

# If you need to provide some extra specs for main container which are not included by default in deployment template
# then provide them here
containerExtraSpecs: {}

# If you need to provide some extra specs for pod which are not included by default in deployment template
# then provide them here
podExtraSpecs: {}

envoyproxy:
  image: docker.io/envoyproxy/envoy:v1.16.0
  configMapName: ""
  lifecycle: {}
  resources:
    limits:
      cpu: 50m
      memory: 50Mi
    requests:
      cpu: 50m
      memory: 50Mi

podDisruptionBudget:
    minAvailable: 1
    maxUnavailable: 1

  # Node tolerations for server scheduling to nodes with taints
  # Ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/
#

tolerations:
    - key: "key"
      operator: "Equal|Exists"
      value: "value"
      effect: "NoSchedule|PreferNoSchedule|NoExecute(1.6 only)"

appMetrics: true
serviceAccount:
  ## @param serviceAccount.create Enable creation of ServiceAccount for pods
  ##
  create: false
  ## @param serviceAccount.name The name of the ServiceAccount to use.
  ## If not set and create is true, a name is generated using the `.Chart.Name .fullname` template
  name: "test1"
  ## @param serviceAccount.annotations Annotations for service account. Evaluated as a template.
  ## Only used if `create` is `true`.
  ##
  annotations:
     kubernetes.io/service-account.name: build-robot
containerSecurityContext:
  allowPrivilegeEscalation: false
privileged: true
hostAliases: []
#  - ip: "127.0.0.1"
#    hostnames:
#    - "foo.local"


affinity:
  enabled: false
  values:
    podAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchExpressions:
          - key: security
            operator: In
            values:
            - S1
        topologyKey: topology.kubernetes.io/zone

secondaryWorkload:
  enabled: false
  postfix: "od"
  replicaCount: 1
  affinity: {}
  tolerations: []
  autoscaling:
    enabled: true
    MinReplicas: 1
    MaxReplicas: 2
    TargetCPUUtilizationPercentage: 90
    TargetMemoryUtilizationPercentage: 80