{{- if $.Values.kedaAutoscaling.enabled }}
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  {{- if $.Values.kedaAutoscaling.name }}
  name: {{ $.Values.kedaAutoscaling.name }}
  {{- else }}
  name: {{ template ".Chart.Name .fullname" $ }}-keda
  {{- end }}
  labels:
    app: {{ template ".Chart.Name .name" $ }}
    chart: {{ template ".Chart.Name .chart" $ }}
    release: {{ $.Release.Name }}
    appId: {{ $.Values.app | quote }}
    envId: {{ $.Values.env | quote }}
    release: {{ .Release.Name }}
  {{- if .Values.appLabels }}
{{ toYaml .Values.appLabels | indent 4 }}
  {{- end }}
  {{- if .Values.kedaAutoscaling.labels }}
{{ toYaml .Values.kedaAutoscaling.labels | indent 4 }}
  {{- end }}
  {{- if .Values.kedaAutoscaling.annotations }}
  annotations:
{{ toYaml .Values.kedaAutoscaling.annotations | indent 4 }}
  {{- end }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include ".Chart.Name .fullname" $ }}
{{- if $.Values.kedaAutoscaling.envSourceContainerName }}
    envSourceContainerName: {{ $.Values.kedaAutoscaling.envSourceContainerName }}
{{- end }}
{{- if $.Values.kedaAutoscaling.pollingInterval }}
  pollingInterval: {{ $.Values.kedaAutoscaling.pollingInterval }}
{{- end }}
{{- if $.Values.kedaAutoscaling.cooldownPeriod }}
  cooldownPeriod: {{ $.Values.kedaAutoscaling.cooldownPeriod }}
{{- end }}
{{- if and (hasKey $.Values.kedaAutoscaling "idleReplicaCount") (ne (toString $.Values.kedaAutoscaling.idleReplicaCount) "0") }}
  idleReplicaCount: {{ $.Values.kedaAutoscaling.idleReplicaCount }}
{{- end }}
  minReplicaCount: {{ $.Values.kedaAutoscaling.minReplicaCount }}
  maxReplicaCount: {{ $.Values.kedaAutoscaling.maxReplicaCount }}
{{- if $.Values.kedaAutoscaling.fallback }}
  fallback: 
{{ toYaml $.Values.kedaAutoscaling.fallback | indent 4 }}
{{- end }}
{{- if $.Values.kedaAutoscaling.advanced }}
  advanced: 
{{ toYaml $.Values.kedaAutoscaling.advanced | indent 4 }}
{{- end }}
  triggers:
{{ toYaml .Values.kedaAutoscaling.triggers | indent 2}}
{{- if $.Values.kedaAutoscaling.authenticationRef }}
    authenticationRef: 
{{ toYaml $.Values.kedaAutoscaling.authenticationRef | indent 6 }}
{{- end }}
---
{{- if $.Values.kedaAutoscaling.triggerAuthentication.enabled }}
apiVersion: keda.sh/v1alpha1
kind: TriggerAuthentication
metadata:
  name: {{ $.Values.kedaAutoscaling.triggerAuthentication.name }}
  labels:
    app: {{ template ".Chart.Name .name" $ }}
    chart: {{ template ".Chart.Name .chart" $ }}
    release: {{ $.Release.Name }}
    appId: {{ $.Values.app | quote }}
    envId: {{ $.Values.env | quote }}
    {{- if .Values.appLabels }}
{{ toYaml .Values.appLabels | indent 4 }}
    {{- end }}        
spec:
{{ toYaml $.Values.kedaAutoscaling.triggerAuthentication.spec | indent 2 }}
{{- end }}
{{- end }}