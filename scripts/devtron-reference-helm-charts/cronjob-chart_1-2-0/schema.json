{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"ContainerPort": {"type": "array", "description": "defines ports on which application services will be exposed to other services", "title": "Container Port", "items": {"type": "object", "properties": {"envoyPort": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "envoy port for the container", "title": "Envoy Port"}, "idleTimeout": {"type": "string", "description": "duration of time for which a connection is idle before the connection is terminated", "title": "Idle Timeout"}, "name": {"type": "string", "description": "name of the port", "title": "Name"}, "port": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "Port", "title": "port for the container"}, "servicePort": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "port of the corresponding kubernetes service", "title": "Service Port"}, "supportStreaming": {"type": ["boolean", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "field to enable/disable timeout for high performance protocols like grpc", "title": "Support Streaming"}, "useHTTP2": {"type": ["boolean", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": " field for setting if envoy container can accept(or not) HTTP2 requests", "title": "Use HTTP2"}}}}, "EnvVariables": {"type": "array", "items": {}, "description": "contains environment variables needed by the containers", "title": "Environment Variables"}, "GracePeriod": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "time for which <PERSON><PERSON><PERSON><PERSON> waits before terminating the pods", "title": "<PERSON> Period"}, "LivenessProbe": {"type": "object", "description": "used by the kubelet to know when to restart a container", "title": "Liveness Probe", "properties": {"Path": {"type": "string", "description": "defines the path where the liveness needs to be checked", "title": "Path"}, "command": {"type": "array", "items": {}, "description": "commands executed to perform a probe", "title": "Command"}, "failureThreshold": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "defines the maximum number of failures that are acceptable before a given container is not considered as live", "title": "Failure Threshold"}, "httpHeader": {"type": "object", "description": "used to override the default headers by defining .httpHeaders for the probe", "title": "HTTP headers", "properties": {"name": {"type": "string", "description": "name of header", "title": "Name"}, "value": {"type": "string", "description": "Value of header", "title": "Value"}}}, "initialDelaySeconds": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "defines the time to wait before a given container is checked for liveness", "title": "Initial Delay Seconds"}, "periodSeconds": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "defines the time to check a given container for liveness", "title": "Period Seconds"}, "port": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "port to access on the container", "title": "Port"}, "scheme": {"type": "string", "description": "Scheme to use for connecting to the host (HTTP or HTTPS). Defaults to HTTP.", "title": "Scheme"}, "successThreshold": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "defines the number of successes required before a given container is said to fulfil the liveness probe", "title": "Success Threshold"}, "tcp": {"type": ["boolean", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "If enabled, the kubelet will attempt to open a socket to container. If connection is established, the container is considered healthy", "title": "TCP"}, "timeoutSeconds": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "defines the time for checking timeout", "title": "Timeout Seconds"}}}, "MaxSurge": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "maximum number of pods that can be created over the desired number of pods", "title": "Maximum Surge"}, "MaxUnavailable": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "maximum number of pods that can be unavailable during the update process", "title": "Maximum Unavailable"}, "MinReadySeconds": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "minimum number of seconds for which a newly created Pod should be ready without any of its containers crashing, for it to be considered available", "title": "Minimum Ready Seconds"}, "ReadinessProbe": {"type": "object", "description": "k<PERSON>let uses readiness probes to know when a container is ready to start accepting traffic", "title": "Readiness Probe", "properties": {"Path": {"type": "string", "description": "defines the path where the readiness needs to be checked", "title": "Path"}, "command": {"type": "array", "items": {}, "description": "commands executed to perform a probe", "title": "Command"}, "failureThreshold": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "defines the maximum number of failures that are acceptable before a given container is not considered as ready", "title": "Failure Threshold"}, "httpHeader": {"type": "object", "description": "used to override the default headers by defining .httpHeaders for the probe", "title": "HTTP headers", "properties": {"name": {"type": "string", "description": "name of header", "title": "Name"}, "value": {"type": "string", "description": "Value of header", "title": "Value"}}}, "initialDelaySeconds": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "defines the time to wait before a given container is checked for readiness", "title": "Initial Delay Seconds"}, "periodSeconds": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "defines the time to check a given container for readiness", "title": "Period Seconds"}, "port": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "port to access on the container", "title": "Port"}, "scheme": {"type": "string", "description": "Scheme to use for connecting to the host (HTTP or HTTPS). Defaults to HTTP.", "title": "Scheme"}, "successThreshold": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "defines the number of successes required before a given container is said to fulfil the readiness probe", "title": "Success Threshold"}, "tcp": {"type": ["boolean", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "If enabled, the kubelet will attempt to open a socket to container. If connection is established, the container is considered healthy", "title": "TCP"}, "timeoutSeconds": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "defines the time for checking timeout", "title": "Timeout Seconds"}}}, "Spec": {"type": "object", "description": "used to define the desire state of the given container", "title": "Spec", "properties": {"Affinity": {"type": "object", "description": "Node/Inter-pod Affinity allows you to constrain which nodes your pod is eligible to schedule on, based on labels of the node/pods", "title": "Affinity", "properties": {"Key": {"anyOf": [{"type": "null"}, {"type": "string", "description": "Key part of the label for node/pod selection", "title": "Key"}]}, "Values": {"type": "string", "description": "Value part of the label for node/pod selection", "title": "Values"}, "key": {"type": "string"}}}}}, "args": {"type": "object", "description": " used to give arguments to command", "title": "Arguments", "properties": {"enabled": {"type": ["boolean", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "used for enabling/disabling aruguments", "title": "Enabled"}, "value": {"type": "array", "description": "values of the arguments", "title": "Value", "items": [{"type": "string"}, {"type": "string"}, {"type": "string"}]}}}, "autoscaling": {"type": "object", "description": "connected to HPA and controls scaling up and down in response to request load", "title": "Autoscaling", "properties": {"MaxReplicas": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "Maximum number of replicas allowed for scaling", "title": "Maximum Replicas"}, "MinReplicas": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "Minimum number of replicas allowed for scaling", "title": "Minimum Replicas"}, "TargetCPUUtilizationPercentage": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "The target CPU utilization that is expected for a container", "title": "TargetCPUUtilizationPercentage"}, "TargetMemoryUtilizationPercentage": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "The target memory utilization that is expected for a container", "title": "TargetMemoryUtilizationPercentage"}, "enabled": {"type": ["boolean", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "used for enabling/disabling autoscaling", "title": "Enabled"}, "extraMetrics": {"type": "array", "items": {}, "description": "used to give external metrics for autoscaling", "title": "Extra Metrics"}}}, "command": {"type": "object", "description": "contains the commands for the server", "title": "Command", "properties": {"enabled": {"type": ["boolean", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "used for enabling/disabling commands"}, "value": {"type": "array", "items": {}, "description": "contains the commands", "title": "Value"}}}, "containers": {"type": "array", "items": {}, "description": " used to run side-car containers along with the main container within same pod"}, "cronjobConfigs": {"type": "object", "description": "runs a job periodically on a given schedule, written in Cron format", "title": "Cron<PERSON>ob <PERSON>", "properties": {"concurrencyPolicy": {"type": "string", "description": "specifies how to treat concurrent executions of a job that is created by this cron job", "title": "Concurrency Policy", "enum": ["Forbid", "Allow"]}, "failedJobsHistoryLimit": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "", "title": "Failed Job History Limit"}, "restartPolicy": {"type": "string", "description": "specifies policy for restart of containers", "title": "Restart Policy", "enum": ["Always", "OnFailure", "Never"]}, "schedule": {"type": "string", "description": "expression for scheduling CronJobs", "title": "Schedule"}, "startingDeadlineSeconds": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "if a CronJob controller cannot start a job run on its schedule, it will keep retrying until this value is reached", "title": "Starting Deadline Seconds"}, "successfulJobsHistoryLimit": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "specifies how many completed and failed jobs should be kept", "title": "Successful Jobs History Limit"}, "suspend": {"type": ["boolean", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "used to suspend subsequent executions, does not apply on already started executions", "title": "Suspend", "default": false}}}, "dbMigrationConfig": {"type": "object", "description": "used to configure database migration", "title": "Db Migration Config", "properties": {"enabled": {"type": ["boolean", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "used for enabling/disabling the config", "title": "Enabled"}}}, "envoyproxy": {"type": "object", "description": "envoy is attached as a sidecar to the application container to collect metrics like 4XX, 5XX, throughput and latency", "title": "Envoy Proxy", "properties": {"configMapName": {"type": "string", "description": "configMap containing configuration for Envoy", "title": "ConfigMap"}, "image": {"type": "string", "description": "image of envoy to be used"}, "resources": {"type": "object", "description": "minimum and maximum RAM and CPU available to the application", "title": "Resources", "properties": {"limits": {"type": "object", "description": "the maximum values a container can reach", "title": "Limits", "properties": {"cpu": {"type": "string", "format": "cpu", "description": "limit of CPU", "title": "CPU"}, "memory": {"type": "string", "format": "memory", "description": "limit of memory", "title": "Memory"}}}, "requests": {"type": "object", "description": "request is what the container is guaranteed to get", "title": "Requests", "properties": {"cpu": {"type": "string", "format": "cpu", "description": "request value of CPU", "title": "CPU"}, "memory": {"type": "string", "format": "memory", "description": "request value of memory", "title": "Memory"}}}}}}}, "image": {"type": "object", "description": "used to access images in kubernetes", "title": "Image", "properties": {"pullPolicy": {"type": "string", "description": "used to define the instances calling the image", "title": "Pull Policy", "enum": ["IfNotPresent", "Always"]}}}, "imagePullSecrets": {"type": "array", "items": {}, "description": "contains the docker credentials that are used for accessing a registry", "title": "Image PullSecrets"}, "ingress": {"type": "object", "description": "allows public access to URLs", "title": "Ingress", "properties": {"annotations": {"type": "object", "description": "used to configure some options depending on the Ingress controller", "title": "Annotations", "properties": {"kubernetes.io/ingress.class": {"type": "string", "default": "nginx"}, "nginx.ingress.kubernetes.io/force-ssl-redirect": {"type": "string", "format": "boolean", "default": "false"}, "nginx.ingress.kubernetes.io/ssl-redirect": {"type": "string", "format": "boolean", "default": "false"}}}, "enabled": {"type": ["boolean", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "used to enable or disable ingress", "title": "Enabled"}, "hosts": {"type": "array", "description": "list of hosts in ingress", "title": "Hosts", "items": [{"type": "object", "properties": {"host": {"type": "string", "description": "host URL", "title": "Host"}, "paths": {"type": "array", "description": "list of paths for a given host", "title": "Paths", "items": [{"type": "string"}]}}}]}, "tls": {"type": "array", "items": {}, "description": "contains security details - private key and certificate", "title": "TLS"}}}, "ingressInternal": {"type": "object", "description": "allows private access to the URLs", "properties": {"annotations": {"type": "object", "description": "used to configure some options depending on the Ingress controller", "title": "Annotations"}, "enabled": {"type": ["boolean", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "used to enable or disable ingress", "title": "Enabled"}, "hosts": {"type": "array", "description": "list of hosts in ingress", "title": "Hosts", "items": [{"type": "object", "properties": {"host": {"type": "string", "description": "host URL", "title": "Host"}, "paths": {"type": "array", "description": "list of paths for a given host", "title": "Paths", "items": [{"type": "string"}]}}}]}, "tls": {"type": "array", "items": {}, "description": "contains security details - private key and certificate", "title": "TLS"}}}, "initContainers": {"type": "array", "items": {}, "description": "specialized containers that run before app containers in a Pod, can contain utilities or setup scripts not present in an app image", "title": "Init Containers"}, "kind": {"type": "string", "enum": ["Job", "<PERSON><PERSON><PERSON><PERSON>"], "description": "kind of Job", "title": "Job", "default": "<PERSON><PERSON><PERSON><PERSON>"}, "jobConfigs": {"type": "object", "description": "creates one or more Pods and will continue to retry execution of the Pods until a specified number of them successfully terminate", "title": "Job Configs", "properties": {"activeDeadlineSeconds": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "once a Job reaches activeDeadlineSeconds, all of its running Pods are terminated", "title": "Active Deadline Seconds"}, "backoffLimit": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "number of retries after which a job is failed", "title": "BackOff Limit", "default": 6}, "completions": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "used for getting fixed completion count Job", "title": "Completions"}, "parallelism": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "used to run aKubernetes Job with multiple parallel worker processes in a given pod", "title": "Parallelism", "default": 1}, "suspend": {"type": ["boolean", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "used to suspend subsequent executions, does not apply on already started executions", "title": "Suspend", "default": false}, "ttlSecondsAfterFinished": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "feature used for clean up of finished Jobs (Complete or Failed)", "title": "TTL Seconds After Finished"}}}, "pauseForSecondsBeforeSwitchActive": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "tell how much to wait for given period of time before switch active the container", "title": "Pause For Seconds Before SwitchActive"}, "podAnnotations": {"type": "object", "description": "used to attach metadata and configs in Kubernetes", "title": "Pod Annotations"}, "podLabels": {"type": "object", "description": "key/value pairs that are attached to pods, are intended to be used to specify identifying attributes of objects that are meaningful and relevant to users, but do not directly imply semantics to the core system", "title": "Pod Labels"}, "prometheus": {"type": "object", "description": "a kubernetes monitoring tool", "title": "Prometheus", "properties": {"release": {"type": "string", "description": "name of the file to be monitored, describes the state of prometheus"}}}, "rawYaml": {"type": "array", "items": {}, "description": "Accepts an array of Kubernetes objects. One can specify any kubernetes yaml here & it will be applied when a app gets deployed.", "title": "Raw YAML"}, "replicaCount": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "count of Replicas of pod", "title": "REplica Count"}, "resources": {"type": "object", "description": "minimum and maximum RAM and CPU available to the application", "title": "Resources", "properties": {"limits": {"type": "object", "description": "the maximum values a container can reach", "title": "Limits", "properties": {"cpu": {"type": "string", "format": "cpu", "description": "limit of CPU", "title": "CPU"}, "memory": {"type": "string", "format": "memory", "description": "limit of memory", "title": "Memory"}}}, "requests": {"type": "object", "description": "request is what the container is guaranteed to get", "title": "Requests", "properties": {"cpu": {"type": "string", "format": "cpu", "description": "request value of CPU", "title": "CPU"}, "memory": {"type": "string", "format": "memory", "description": "request value of memory", "title": "Memory"}}}}}, "secret": {"type": "object", "properties": {"data": {"type": "object"}, "enabled": {"type": "boolean"}}}, "server": {"type": "object", "description": "used for providing server configurations.", "title": "Server", "properties": {"deployment": {"type": "object", "description": "gives the details for deployment", "title": "Deployment", "properties": {"image": {"type": "string", "description": "URL of the image", "title": "Image"}, "image_tag": {"type": "string", "description": "tag of the image", "title": "Image Tag"}}}}}, "service": {"type": "object", "description": "defines annotations and the type of service", "title": "Service", "properties": {"annotations": {"type": "object", "title": "Annotations", "description": "annotations of service"}, "type": {"type": "string", "description": "type of service", "title": "Type", "enum": ["ClusterIP", "LoadBalancer", "NodePort", "ExternalName"]}}}, "servicemonitor": {"type": "object", "description": "gives the set of targets to be monitored", "title": "Service Monitor", "properties": {"additionalLabels": {"type": "object"}}}, "tolerations": {"type": "array", "items": {}, "description": "a mechanism which work together with Taints which ensures that pods are not placed on inappropriate nodes", "title": "Tolerations"}, "volumeMounts": {"type": "array", "items": {}, "description": "used to provide mounts to the volume", "title": "Volume Mounts"}, "volumes": {"type": "array", "items": {}, "description": "required when some values need to be read from or written to an external disk", "title": "Volumes"}, "waitForSecondsBeforeScalingDown": {"type": ["integer", "string"], "pattern": "^@{{[a-zA-Z0-9-+/*%_\\s]+}}$", "description": "Wait for given period of time before scaling down the container", "title": "Wait For Seconds Before Scaling Down"}}}