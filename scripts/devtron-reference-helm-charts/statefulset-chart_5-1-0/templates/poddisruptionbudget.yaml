{{- if .Values.podDisruptionBudget }}
{{- if semverCompare ">=1.21-0" .Capabilities.KubeVersion.GitVersion -}}
apiVersion: policy/v1
{{- else -}}
apiVersion: policy/v1beta1
{{- end }}
kind: PodDisruptionBudget
metadata:
  {{- if .Values.podDisruptionBudget.name }}
  name: {{ .Values.podDisruptionBudget.name }}
  {{- else }}
  name: {{ include ".Chart.Name .fullname" $ }}
  {{- end }}
  labels:
    appId: {{ $.Values.app | quote }}
    envId: {{ $.Values.env | quote }}
    app: {{ template ".Chart.Name .name" $ }}
    chart: {{ template ".Chart.Name .chart" $ }}
    release: {{ $.Release.Name }}
spec:
  {{- if .Values.podDisruptionBudget.minAvailable }}
  minAvailable: {{ .Values.podDisruptionBudget.minAvailable }}
  {{- end }}
  {{- if .Values.podDisruptionBudget.maxUnavailable }}
  maxUnavailable: {{ .Values.podDisruptionBudget.maxUnavailable }}
  {{- end }}
  selector:
    matchLabels:
    {{- if .Values.customPodLabels }}
{{ toYaml .Values.customPodLabels | indent 6 }}  
    {{- else }}
      appId: {{ $.Values.app | quote }}
      envId: {{ $.Values.env | quote }}
    {{- end }}
  {{- end }}
