{{ $svcName := include ".servicename" . }}
{{ $svcPort := (index .Values.ContainerPort 0).servicePort }}
{{- if $.Values.ambassadorMapping.enabled }}
{{- with $.Values.ambassadorMapping }}
apiVersion: getambassador.io/v3alpha1
kind: Mapping
metadata:
  name:  {{ include ".Chart.Name .fullname" $ }}-mapping
  labels:
    app: {{ template ".Chart.Name .name" $ }}
    chart: {{ template ".Chart.Name .chart" $ }}
    release: {{ $.Release.Name }}
    releaseVersion: {{ $.Values.releaseVersion | quote }}
    pipelineName: {{ $.Values.pipelineName }}
    {{- if .labels }}
{{ toYaml .labels | nindent 4 }}
    {{- end }}
{{- if $.Values.appLabels }}
{{ toYaml $.Values.appLabels | indent 4 }}
{{- end }}
spec:
  {{- if .ambassadorId }}
  ambassador_id: {{ .ambassadorId }}
  {{- end }}
  {{- if .hostname }}
  hostname: {{ .hostname | quote }}
  {{- end }}
  prefix: {{ .prefix }}
  {{- if .rewrite }}
  rewrite: {{ .rewrite }}
  {{- end }}
  service: {{ $svcName }}.{{ $.Release.Namespace }}:{{ $svcPort }}
  {{- if .retryPolicy }}
  retry_policy:
{{ toYaml .retryPolicy | indent 4 }}
  {{- end }}
  {{- if .cors }}
  cors:
{{ toYaml .cors | indent 4 }}
  {{- end }}
  {{- if .weight }}
  weight: {{ .weight }}
  {{- end }}
  {{- if .method }}
  method: {{ .method }}
  {{- end }}
  {{- if .extraSpec }}
{{ toYaml .extraSpec | indent 2 }}
  {{- end }}
  {{- if .tls }}
  {{- if .tls.context }}
  tls: {{ .tls.context }}
{{- if .tls.create }}
---
apiVersion: getambassador.io/v3alpha1
kind: TLSContext
metadata:
  name: {{ .tls.context }}
  labels:
    app: {{ template ".Chart.Name .name" $ }}
    chart: {{ template ".Chart.Name .chart" $ }}
    release: {{ $.Release.Name }}
    releaseVersion: {{ $.Values.releaseVersion | quote }}
    pipelineName: {{ $.Values.pipelineName }}
    {{- if .tls.labels }}
{{ toYaml .tls.labels | nindent 4 }}
    {{- end }}
{{- if $.Values.appLabels }}
{{ toYaml $.Values.appLabels | indent 4 }}
{{- end }}
spec:
  {{- if .tls.secretName }}
  secret: {{ .tls.secretName }}
  {{- end }}
  {{- if .tls.hosts }}
  hosts:
{{ toYaml .tls.hosts | nindent 4 }}
  {{- end }}
  {{- if .tls.extraSpec }}
{{ toYaml .tls.extraSpec | indent 2 }}
  {{- end }}
{{- end }}
  {{- end }}
  {{- end }}
{{- end }}
{{- end }}