  {{- $hasCMEnvExists := false -}}
  {{- $hasCMVolumeExists := false -}}
  {{- if .Values.ConfigMaps.enabled }}
  {{- range .Values.ConfigMaps.maps }}
  {{- if eq .type "volume"}}
  {{- $hasCMVolumeExists = true}}
  {{- end }}
  {{- if eq .type "environment"}}
  {{- $hasCMEnvExists = true}}
  {{- end }}
  {{- end }}
  {{- end }}

  {{- $hasSecretEnvExists := false -}}
  {{- $hasSecretVolumeExists := false -}}
  {{- if .Values.ConfigSecrets.enabled }}
  {{- range .Values.ConfigSecrets.secrets }}
  {{- if eq .type "volume"}}
  {{- $hasSecretVolumeExists = true}}
  {{- end }}
  {{- if eq .type "environment"}}
  {{- $hasSecretEnvExists = true}}
  {{- end }}
  {{- end }}
  {{- end }}
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ include ".Chart.Name .fullname" $ }}
  labels:
    app: {{ template ".Chart.Name .name" $ }}
    chart: {{ template ".Chart.Name .chart" $ }}
    release: {{ $.Release.Name }}
    releaseVersion: {{ $.Values.releaseVersion | quote }}
    pipelineName: {{ .Values.pipelineName }}
    {{- if .Values.statefulSetConfig.labels }}
{{ toYaml .Values.statefulSetConfig.labels | indent 4 }}
    {{- end }}
    {{- if .Values.appLabels }}
{{ toYaml .Values.appLabels  | indent 4}}
    {{- end }}
  {{- if .Values.statefulSetConfig.annotations }}
  annotations:
{{ toYaml .Values.statefulSetConfig.annotations | indent 4 }}
{{- end }}
spec:
  selector:
    matchLabels:
      app: {{ template ".Chart.Name .name" $ }}
      release: {{ $.Release.Name }}
  replicas: {{ $.Values.replicaCount }}
  minReadySeconds: {{ $.Values.MinReadySeconds }}
  {{- if .Values.statefulSetConfig.serviceName  }}
  serviceName: {{ .Values.statefulSetConfig.serviceName }}
  {{- else }}
  serviceName: {{ template ".servicename" . }}-headless
  {{- end}}
  {{- if $.Values.statefulSetConfig.podManagementPolicy }}
  podManagementPolicy: {{ $.Values.statefulSetConfig.podManagementPolicy }}
  {{- end }}
  template:
    metadata:
    {{- if .Values.podAnnotations }}
      annotations:
      {{- range $key, $value := .Values.podAnnotations }}
        {{ $key }}: {{ $value | quote }}
      {{- end }}
    {{- end }}
      labels:
        app: {{ template ".Chart.Name .name" $ }}
        appId: {{ $.Values.app | quote }}
        envId: {{ $.Values.env | quote }}
        release: {{ $.Release.Name }}
        {{- if .Values.appLabels }}
{{ toYaml .Values.appLabels | indent 8 }}
        {{- end }}
        {{- if .Values.podLabels }}
        {{ toYaml .Values.podLabels | indent 8 }}
        {{- end }}
    spec:
{{- if $.Values.podExtraSpecs }}	
{{ toYaml .Values.podExtraSpecs | indent 6 }}	
{{- end }}
      terminationGracePeriodSeconds: {{ $.Values.GracePeriod }}
      restartPolicy: Always
{{- if and $.Values.Spec.Affinity.Key $.Values.Spec.Affinity.Values }}
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ $.Values.Spec.Affinity.Key | indent 14 }}
                operator: In
                values:
                - {{ $.Values.Spec.Affinity.Values | default "nodes"  }}
{{- end }}
{{- if $.Values.serviceAccountName }}
      serviceAccountName: {{ $.Values.serviceAccountName }}
{{- else  }}
      serviceAccountName: {{ template "serviceAccountName" . }}
{{- end }}
  {{- if .Values.tolerations }}
      tolerations:
{{ toYaml .Values.tolerations | indent 8 }}
  {{- end }}
{{- if $.Values.imagePullSecrets}}
      imagePullSecrets:
  {{- range .Values.imagePullSecrets }}
        - name: {{ . }}
  {{- end }}
{{- end}}
{{- if $.Values.topologySpreadConstraints }}
      topologySpreadConstraints:
{{- range $.Values.topologySpreadConstraints }}
      - maxSkew: {{ .maxSkew }}
        topologyKey: {{ .topologyKey }}
        whenUnsatisfiable: {{ .whenUnsatisfiable }}
        labelSelector:
          matchLabels:
          {{- if and .autoLabelSelector .customLabelSelector }}
{{ toYaml .customLabelSelector | indent 12 }}
          {{- else if .autoLabelSelector }}
            app: {{ template ".Chart.Name .name" $ }}
            appId: {{ $.Values.app | quote }}
            envId: {{ $.Values.env | quote }}
            release: {{ $.Release.Name }}
          {{- else if .customLabelSelector }}
{{ toYaml .customLabelSelector | indent 12 }}
          {{- end }}
{{- end }}
{{- end }}
{{- if $.Values.podSecurityContext }}
      securityContext:
{{ toYaml .Values.podSecurityContext | indent 8 }}
{{- end }}
{{- if $.Values.initContainers}}
      initContainers:
{{- range $i, $c := .Values.initContainers }}
{{- if .reuseContainerImage}}
        - name: {{ $.Chart.Name }}-init-{{ add1 $i }}
          image: "{{ $.Values.server.deployment.image }}:{{ $.Values.server.deployment.image_tag }}"
          imagePullPolicy: {{ $.Values.image.pullPolicy }}
{{- if .securityContext }}
          securityContext:
{{ toYaml .securityContext | indent 12 }}
{{- end }}
{{- if .command}}
          command:
{{ toYaml .command | indent 12 -}}
{{- end}}
{{- if .resources}}
          resources:
{{ toYaml .resources | indent 12 -}}
{{- end}}
{{- if .volumeMounts}}
          volumeMounts:
{{ toYaml .volumeMounts | indent 12 -}}
{{- end}}
{{- else}}
        -
{{ toYaml . | indent 10 }}
{{- end}}
{{- end}}
{{- end}}
      containers:
{{- if $.Values.appMetrics }}
        - name: envoy
          image: {{ $.Values.envoyproxy.image | default "envoyproxy/envoy:v1.14.1"}}
          {{- if $.Values.envoyproxy.lifecycle }}
          lifecycle:
{{ toYaml .Values.envoyproxy.lifecycle | indent 12 -}}
          {{- else if $.Values.containerSpec.lifecycle.enabled }}
          lifecycle:
           {{- if $.Values.containerSpec.lifecycle.preStop }}
           preStop:
{{ toYaml $.Values.containerSpec.lifecycle.preStop | indent 12 -}}
           {{- end }}
          {{- end }}
          resources:
{{ toYaml $.Values.envoyproxy.resources | trim | indent 12 }}
          ports:
            - containerPort: 9901
              protocol: TCP
              name: envoy-admin
              {{- range $index, $element := .Values.ContainerPort }}
            - name: {{ $element.name}}
              containerPort: {{ $element.envoyPort | default (add 8790 $index) }}
              protocol: TCP
              {{- end }}
          command: ["/usr/local/bin/envoy"]
          args: ["-c", "/etc/envoy-config/envoy-config.json", "-l", "info", "--log-format", "[METADATA][%Y-%m-%d %T.%e][%t][%l][%n] %v"]
          volumeMounts:
            - name: {{ $.Values.envoyproxy.configMapName | default "envoy-config-volume" }}
              mountPath: /etc/envoy-config/
{{- end}}
{{- if $.Values.containers }}
{{- range $i, $c := .Values.containers }}
{{- if .reuseContainerImage}}
        - name: {{ $.Chart.Name }}-sidecontainer-{{ add1 $i }}
          image: "{{ $.Values.server.deployment.image }}:{{ $.Values.server.deployment.image_tag }}"
          imagePullPolicy: {{ $.Values.image.pullPolicy }}
{{- if .securityContext }}
          securityContext:
{{ toYaml .securityContext | indent 12 }}
{{- end }}
{{- if .command}}
          command:
{{ toYaml .command | indent 12 -}}
{{- end}}
{{- if .resources}}
          resources:
{{ toYaml .resources | indent 12 -}}
{{- end}}
{{- if .volumeMounts}}
          volumeMounts:
{{ toYaml .volumeMounts | indent 12 -}}
{{- end}}
{{- else}}
        -
{{ toYaml . | indent 10 }}
{{- end}}
{{- end}}
{{- end}}
        - name: {{ $.Chart.Name }}
          image: "{{ .Values.server.deployment.image }}:{{ .Values.server.deployment.image_tag }}"
          imagePullPolicy: {{ $.Values.image.pullPolicy }}
          {{- if $.Values.containerSpec.lifecycle.enabled }}
          lifecycle:
           {{- if $.Values.containerSpec.lifecycle.preStop }}
           preStop:
{{ toYaml $.Values.containerSpec.lifecycle.preStop | indent 12 -}}
           {{- end }}
           {{- if $.Values.containerSpec.lifecycle.postStart }}
           postStart:
{{ toYaml $.Values.containerSpec.lifecycle.postStart | indent 12 -}}
           {{- end }}
          {{- end }}
{{- if and $.Values.containerSecurityContext $.Values.privileged }}
          securityContext:
            privileged: true
{{ toYaml .Values.containerSecurityContext | indent 12 }}
{{- else if $.Values.privileged }}
          securityContext:
            privileged: true
{{- else if $.Values.containerSecurityContext }}
          securityContext:
{{ toYaml .Values.containerSecurityContext | indent 12 }}
{{- end }}
{{- if $.Values.containerExtraSpecs }}	
{{ toYaml .Values.containerExtraSpecs | indent 10 }}	
{{- end }}
          ports:
          {{- range $.Values.ContainerPort }}
            - name: {{ .name}}
              containerPort: {{ .port  }}
              protocol: TCP
          {{- end}}
{{- if and $.Values.command.enabled $.Values.command.workingDir }}
          workingDir: {{ $.Values.command.workingDir }}
{{- end}}
{{- if and $.Values.command.value $.Values.command.enabled}}
          command:
{{ toYaml $.Values.command.value | indent 12 -}}
{{- end}}
{{- if and $.Values.args.value $.Values.args.enabled}}
          args:
{{ toYaml $.Values.args.value | indent 12 -}}
{{- end }}
          env:
            - name: CONFIG_HASH
              value: {{ include (print $.Chart.Name "/templates/configmap.yaml") . | sha256sum }}
            - name: SECRET_HASH
              value: {{ include (print $.Chart.Name "/templates/secret.yaml") . | sha256sum }}
            - name: DEVTRON_APP_NAME
              value: {{ template ".Chart.Name .name" $ }}
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: DEVTRON_CONTAINER_REPO
              value: "{{ .Values.server.deployment.image }}"
            - name: DEVTRON_CONTAINER_TAG
              value: "{{ .Values.server.deployment.image_tag }}"
          {{- range $.Values.EnvVariablesFromFieldPath }}
            - name: {{ .name }}
              valueFrom:
                fieldRef:
                 fieldPath: {{ .fieldPath }}
          {{- end}}
          {{- range $.Values.EnvVariables }}
            - name: {{ .name}}
              value: {{ .value | quote }}
          {{- end}}
          {{- range $.Values.EnvVariablesFromSecretKeys }}
          {{- if and .name .secretName .keyName }}
            - name: {{ .name }}
              valueFrom:
                secretKeyRef:
                  name: {{ .secretName }}
                  key: {{ .keyName }}
          {{- end }}
          {{- end }}
          {{- range $.Values.EnvVariablesFromCongigMapKeys }}
          {{- if and .name .configMapName .keyName }}
            - name: {{ .name }}
              valueFrom:
                configMapKeyRef:
                  name: {{ .configMapName }}
                  key: {{ .keyName }}
          {{- end }}
          {{- end }}
          {{- if or (and ($hasCMEnvExists) (.Values.ConfigMaps.enabled)) (and ($hasSecretEnvExists) (.Values.ConfigSecrets.enabled)) }}
          envFrom:
          {{- if .Values.ConfigMaps.enabled }}
          {{- range .Values.ConfigMaps.maps }}
          {{- if eq .type "environment" }}
          - configMapRef:
              {{- if eq .external true }}
              name: {{ .name }}
              {{- else if eq .external false }}
              name: {{ .name}}-{{ $.Values.app }}
              {{- end }}
          {{- end }}
          {{- end }}
          {{- end }}
          {{- if .Values.ConfigSecrets.enabled }}
          {{- range .Values.ConfigSecrets.secrets }}
          {{- if eq .type "environment" }}
          - secretRef:
              {{if eq .external true}}
              name: {{ .name }}
              {{else if eq .external false}}
              name: {{ .name}}-{{ $.Values.app }}
              {{- end }}
          {{- end }}
          {{- end }}
          {{- end }}
          {{- end }}

{{- if or $.Values.LivenessProbe.Path $.Values.LivenessProbe.command $.Values.LivenessProbe.tcp }}
          livenessProbe:
{{- if $.Values.LivenessProbe.Path }}
            httpGet:
              path: {{ $.Values.LivenessProbe.Path  }}
              port: {{ $.Values.LivenessProbe.port }}
            {{- if $.Values.LivenessProbe.httpHeaders }}
              httpHeaders:
              {{- range $.Values.LivenessProbe.httpHeaders}}
                - name: {{.name}}
                  value: {{.value}}
              {{- end}}
	    {{- end }}
{{- end }}
{{- if $.Values.LivenessProbe.command }}
            exec:
              command:
{{ toYaml .Values.LivenessProbe.command | indent 16 }}
{{- end}}
{{- if and $.Values.LivenessProbe.tcp }}
            tcpSocket:
              port: {{ $.Values.LivenessProbe.port }}
{{- end}}
            initialDelaySeconds: {{ $.Values.LivenessProbe.initialDelaySeconds  }}
            periodSeconds: {{ $.Values.LivenessProbe.periodSeconds  }}
            successThreshold: {{ $.Values.LivenessProbe.successThreshold  }}
            timeoutSeconds: {{ $.Values.LivenessProbe.timeoutSeconds  }}
            failureThreshold: {{ $.Values.LivenessProbe.failureThreshold  }}
{{- end }}
{{- if or $.Values.ReadinessProbe.Path  $.Values.ReadinessProbe.command $.Values.ReadinessProbe.tcp }}
          readinessProbe:
{{- if $.Values.ReadinessProbe.Path }}
            httpGet:
              path: {{ $.Values.ReadinessProbe.Path  }}
              port: {{ $.Values.ReadinessProbe.port }}
            {{- if $.Values.ReadinessProbe.httpHeaders }}
              httpHeaders:
              {{- range $.Values.ReadinessProbe.httpHeaders}}
                - name: {{.name}}
                  value: {{.value}}
              {{- end}}
	    {{- end }}
{{- end }}
{{- if $.Values.ReadinessProbe.command }}
            exec:
              command:
{{ toYaml .Values.ReadinessProbe.command | indent 16 }}
{{- end}}
{{- if and $.Values.ReadinessProbe.tcp }}
            tcpSocket:
              port: {{ $.Values.ReadinessProbe.port }}
{{- end}}
            initialDelaySeconds: {{ $.Values.ReadinessProbe.initialDelaySeconds  }}
            periodSeconds: {{ $.Values.ReadinessProbe.periodSeconds  }}
            successThreshold: {{ $.Values.ReadinessProbe.successThreshold  }}
            timeoutSeconds: {{ $.Values.ReadinessProbe.timeoutSeconds  }}
            failureThreshold: {{ $.Values.ReadinessProbe.failureThreshold  }}
{{- end }}
          resources:
{{ toYaml $.Values.resources | trim | indent 12 }} 
          volumeMounts:
            {{- with (index $.Values.statefulSetConfig.volumeClaimTemplates 0) }}
            {{- if and .metadata .metadata.name }}
            - name: {{ .metadata.name }}
            {{- else }}
            - name: {{ template ".Chart.Name .name" $ }}
            {{- end}}
            {{- end}}
            {{- if  .Values.statefulSetConfig.mountPath }}
              mountPath: {{ $.Values.statefulSetConfig.mountPath  }}
            {{- else}}
              mountPath: "/tmp"
            {{- end}}
{{- with .Values.volumeMounts }}
{{ toYaml . | trim | indent 12 }}
{{- end }}  
          {{- if .Values.ConfigMaps.enabled }}
          {{- range .Values.ConfigMaps.maps }}
          {{- if eq .type "volume"}}
          {{- $cmName := .name -}}
          {{- $cmMountPath := .mountPath -}}
          {{- if eq .subPath false }}
            - name: {{ $cmName | replace "." "-"}}-vol
              mountPath: {{ $cmMountPath }}

          {{- else }}
          {{- range $k, $v := .data }}
            - name: {{ $cmName | replace "." "-"}}-vol
              mountPath: {{ $cmMountPath }}/{{ $k}}
              subPath: {{ $k}}
          {{- end }}
          {{- end }}
          {{- end }}
          {{- end }}
          {{- end }}

          {{- if .Values.ConfigSecrets.enabled }}
          {{- range .Values.ConfigSecrets.secrets }}
          {{- if eq .type "volume"}}
          {{- $cmName := .name -}}
          {{- $cmMountPath := .mountPath -}}
          {{- if eq .subPath false }}
            - name: {{ $cmName | replace "." "-"}}-vol
              mountPath: {{ $cmMountPath }}
	      
          {{- else }}
          {{- range $k, $v := .data }}
            - name: {{ $cmName | replace "." "-"}}-vol
              mountPath: {{ $cmMountPath}}/{{ $k}}
              subPath: {{ $k}}
          {{- end }}
          {{- end }}
          {{- end }}
          {{- end }}
          {{- end }}
          {{- if and (eq (len .Values.volumes) 0) (or (eq (.Values.ConfigSecrets.enabled) true) (eq (.Values.ConfigMaps.enabled) true)) (eq ($hasCMVolumeExists) false) (eq ($hasSecretVolumeExists) false) }} []{{- end }}
          {{- if and (eq (len .Values.volumeMounts) 0) (eq (len .Values.statefulSetConfig.volumeClaimTemplates) -1) (eq (.Values.ConfigSecrets.enabled) false) (eq (.Values.ConfigMaps.enabled) false) }} [] {{- end }}
      volumes:
  {{- if $.Values.appMetrics }}
        - name: envoy-config-volume
          configMap:
            name: sidecar-config-{{ template ".Chart.Name .name" $ }}
  {{- end }}
{{- with .Values.volumes }}
{{ toYaml . | trim | indent 8 }}
{{- end }}
      {{- if .Values.ConfigMaps.enabled }}
      {{- range .Values.ConfigMaps.maps }}
      {{- if eq .type "volume"}}
        - name: {{ .name | replace "." "-"}}-vol
          configMap:
            {{- if eq .external true }}
            name: {{ .name }}
            {{- else if eq .external false }}
            name: {{ .name}}-{{ $.Values.app }}
            {{- end }}
            {{- if eq (len .filePermission) 0 }}
            {{- else }}
            defaultMode: {{ .filePermission}}
            {{- end }}
      {{- end }}
      {{- end }}
      {{- end }}
      {{- if .Values.ConfigSecrets.enabled }}
      {{- range .Values.ConfigSecrets.secrets }}
      {{- if eq .type "volume"}}
        - name: {{ .name | replace "." "-"}}-vol
          secret:
            {{- if eq .external true }}
            secretName: {{ .name }}
            {{- else if eq .external false }}
            secretName: {{ .name}}-{{ $.Values.app }}
            {{- end }}
            {{- if eq (len .filePermission) 0 }}
            {{- else }}
            defaultMode: {{ .filePermission}}
            {{- end }}
      {{- end }}
      {{- end }}
      {{- end }}

 {{- if and (eq (len .Values.volumes) 0) (or (eq (.Values.ConfigSecrets.enabled) true) (eq (.Values.ConfigMaps.enabled) true)) (eq ($hasCMVolumeExists) false) (eq ($hasSecretVolumeExists) false) (eq (.Values.appMetrics) false) }} []{{- end }}
      {{- if and (eq (len .Values.volumes) 0) (eq (.Values.ConfigSecrets.enabled) false) (eq (.Values.ConfigMaps.enabled) false) (eq (.Values.appMetrics) false) }} []{{- end }}
 
  {{- if $.Values.statefulSetConfig.volumeClaimTemplates }}
  volumeClaimTemplates: 
    {{- range $.Values.statefulSetConfig.volumeClaimTemplates }}
    - apiVersion: {{ .apiVersion | default "v1" }}
      {{- if .kind }}
      kind: {{.kind}}
      {{- end}}
      metadata:
          {{- if and .metadata .metadata.annotations }}
          annotations: {{ toYaml .metadata.annotations | nindent 14 }}
          {{- end}}
          {{- if and .metadata .metadata.labels }}
          labels: {{ toYaml .metadata.labels | nindent 14 }}
          {{- end}}
          {{- if and .metadata .metadata.name }}
          name: {{ .metadata.name }}
          {{- else }}
          name: {{ template ".Chart.Name .name" $ }}
          {{- end}}
          {{- if  and .metadata .metadata.namespace }}
          namespace: {{  .metadata.namespace  }}
          {{- end}}
      spec:
        accessModes: 
        {{- range .spec.accessModes }}
            - {{ . }}
        {{- end }}
        {{- if .spec.dataSource }}
        dataSource:
            apiGroup: {{ .spec.dataSource.apiGroup}}
            kind:  {{ .spec.dataSource.kind}}
            name:  {{ .spec.dataSource.name}}
          {{- end }}
         {{- if .spec.dataSourceRef }}
        dataSourceRef:
            apiGroup: {{ .spec.dataSourceRef.apiGroup}}
            kind: {{ .spec.dataSourceRef.kind}}
            name:  {{ .spec.dataSourceRef.name}}
          {{- end }}
        {{- if .spec.selector }}
        selector: {{ toYaml .spec.selector | nindent 10 }}
        {{- end}}
        {{- if .spec.storageClassName }}
        storageClassName: {{ .spec.storageClassName }}
        {{- end}}
        resources:
         {{- if .spec.resources.claim }}
            claims:
                name: {{ .spec.resources.claim.name}}
            {{- end}}
            {{- if .spec.resources.limits }}
            limits: {{ toYaml .spec.resources.limits | nindent 14 }}
            {{- end}}
            requests:
              storage: {{ .spec.resources.requests.storage |default "2Gi" }}
        {{- if .volumeMode }}
        volumeMode: {{ .volumeMode}}
        {{- end}}
        {{- if .volumeName }}
        volumeName: {{ .volumeName}}
        {{- end}}
      {{- end }}   
  {{- end }}
    {{- if eq .Values.deploymentType "ROLLINGUPDATE" }}  
  updateStrategy: 
      type: RollingUpdate
    {{- if $.Values.deployment.strategy.rollingUpdate.partition }}
      rollingUpdate:
          partition: {{ .Values.deployment.strategy.rollingUpdate.partition }}
    {{- end }} 
      {{- else if eq .Values.deploymentType "ONDELETE" }}
  updateStrategy:
      type: OnDelete
      {{- end }}
  {{- if.Values.statefulSetConfig.revisionHistoryLimit }}
  revisionHistoryLimit: {{ .Values.statefulSetConfig.revisionHistoryLimit }}
  {{- end }}
