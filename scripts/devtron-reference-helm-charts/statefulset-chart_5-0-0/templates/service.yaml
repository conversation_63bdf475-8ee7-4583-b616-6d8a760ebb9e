{{- if .Values.service.enabled }}
apiVersion: v1
kind: Service
metadata:
  {{- if .Values.service.name  }}
  name: {{ .Values.service.name}}
  {{- else }}
  name: {{ template ".servicename" . }}
  {{- end }}
  labels:
    appId: {{ $.Values.app | quote }}
    envId: {{ $.Values.env | quote }}
    app: {{ template ".Chart.Name .name" $ }}
    chart: {{ template ".Chart.Name .chart" $ }}
    release: {{ $.Release.Name }}
{{- if .Values.appLabels }}
{{ toYaml .Values.appLabels | indent 4 }}
{{- end }}
{{- if .Values.service.annotations }}
  annotations:
{{ toYaml .Values.service.annotations | indent 4 }}
{{- end }}
spec:
  type: {{ .Values.service.type | default "ClusterIP" }}
{{- with .Values.service.extraSpec }}
  {{- toYaml . | nindent 2 }}
  {{- end }}
{{- if (eq .Values.service.type "LoadBalancer") }}
  {{- if .Values.service.loadBalancerIP }}
  loadBalancerIP: {{ .Values.service.loadBalancerIP }}
  {{- end }}
  {{- if .Values.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges: 
  {{- range .Values.service.loadBalancerSourceRanges }}
  - {{ . }}
  {{- end }}
  {{- end }}
{{- end }}
  ports:
    {{- range .Values.ContainerPort }}
      {{- if .servicePort }}
    - port: {{ .servicePort }}
      {{- else }}
    - port: {{ .port }}
       {{- end }}
      {{- if .targetPort }}
      targetPort: {{ .targetPort }}
      {{- else }}
      targetPort: {{ .name }}
      {{- end }}
      {{- if (and (eq $.Values.service.type "NodePort") .nodePort )}}
      nodePort: {{ .nodePort }}
      {{- end }}
      protocol: TCP
      name: {{ .name }}
    {{- end }}
      {{- if $.Values.appMetrics }}
    - port: 9901
      name: envoy-admin
      {{- end }}
  selector:
    app: {{ template ".Chart.Name .name" . }}
{{- end }}
---
{{- if or  .Values.service.enabled .Values.serviceheadless.enabled  }}
apiVersion: v1
kind: Service
metadata:
  {{- if .Values.serviceheadless.enabled  }}
  {{- if .Values.serviceheadless.name  }}
  name: {{ .Values.serviceheadless.name }}
  {{- else  }}
  name: {{ template ".servicename" . }}-headless
  {{- end }}
  {{- else  }}
  name: {{ template ".servicename" . }}-headless
  {{- end }}
  labels:
    appId: {{ $.Values.app | quote }}
    envId: {{ $.Values.env | quote }}
    app: {{ template ".Chart.Name .name" $ }}
    chart: {{ template ".Chart.Name .chart" $ }}
    release: {{ $.Release.Name }}
{{- if .Values.appLabels }}
{{ toYaml .Values.appLabels | indent 4 }}
{{- end }}
{{- if .Values.service.annotations }}
  annotations:
{{ toYaml .Values.service.annotations | indent 4 }}
{{- end }}
spec:
  clusterIP: None
  ports:
    {{- range .Values.ContainerPort }}
      {{- if .servicePort }}
    - port: {{ .servicePort }}
      {{- else }}
    - port: {{ .port }}
       {{- end }}
      {{- if .targetPort }}
      targetPort: {{ .targetPort }}
      {{- else }}
      targetPort: {{ .name }}
      {{- end }}
      {{- if (and (eq $.Values.service.type "NodePort") .nodePort )}}
      nodePort: {{ .nodePort }}
      {{- end }}
      protocol: TCP
      name: {{ .name }}
    {{- end }}
      {{- if $.Values.appMetrics }}
    - port: 9901
      name: envoy-admin
      {{- end }}
  selector:
    app: {{ template ".Chart.Name .name" . }}
  type: ClusterIP
{{- if (and (eq .Values.service.type "LoadBalancer") .Values.service.loadBalancerSourceRanges )}}
  loadBalancerSourceRanges: 
 {{- range .Values.service.loadBalancerSourceRanges }}
      - {{ . }}
 {{- end }}
{{- end }}
{{- end }}
