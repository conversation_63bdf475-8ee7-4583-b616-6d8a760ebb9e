{{- with .Values.istio }}
{{- if and .enable .requestAuthentication.enabled }}
apiVersion: security.istio.io/v1beta1
kind: RequestAuthentication
metadata:
  {{- if .requestAuthentication.name }}
  name: {{ .requestAuthentication.name }}
  {{- else }}
  name: {{ template ".Chart.Name .fullname" $ }}
  {{- end }}
  labels:
    app: {{ template ".Chart.Name .name" $ }}
    appId: {{ $.Values.app | quote }}
    envId: {{ $.Values.env | quote }}
    chart: {{ template ".Chart.Name .chart" $ }}
    release: {{ $.Release.Name }}
{{- if $.Values.appLabels }}
{{ toYaml $.Values.appLabels | indent 4 }}
{{- end }}
    {{- if .requestAuthentication.labels }}
{{ toYaml .requestAuthentication.labels | indent 4 }}
    {{- end }}
{{- if .requestAuthentication.annotations }}
  annotations:
{{ toYaml .requestAuthentication.annotations | indent 4 }}
{{- end }}
spec:
{{- if .requestAuthentication.selector.enabled }}
  selector:
    matchLabels: 
      app.kubernetes.io/name: {{ template ".Chart.Name .fullname" $ }}
{{- end }}
{{- if $.Values.istio.requestAuthentication.jwtRules }}
  jwtRules:
{{ toYaml $.Values.istio.requestAuthentication.jwtRules | indent 2 }}
{{- end }}
{{- end }}
{{- end }}