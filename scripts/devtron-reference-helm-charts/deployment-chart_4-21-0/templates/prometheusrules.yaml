{{- if  .Values.prometheusRule.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  {{- if .Values.prometheusRule.name }}
  name: {{ .Values.prometheusRule.name }}
  {{- else }}
  name: {{ template ".Chart.Name .fullname" . }}
  {{- end }}
  {{- if .Values.prometheusRule.namespace }}
  namespace: {{ .Values.prometheusRule.namespace }}
  {{- end }}
  labels:
    kind: Prometheus
    appId: {{ $.Values.app | quote }}
    envId: {{ $.Values.env | quote }}
    app: {{ template ".Chart.Name .name" $ }}
    chart: {{ template ".Chart.Name .chart" $ }}
    release: {{ .Values.prometheus.release }}
  {{- if .Values.prometheusRule.additionalLabels }}
{{ toYaml .Values.prometheusRule.additionalLabels | indent 4 }}
  {{- end }}
spec:
  {{- with .Values.prometheusRule.rules }}
  groups:
    {{- if $.Values.prometheusRule.name }}
    - name: {{ $.Values.prometheusRule.name }}
    {{- else }}
    - name: {{ template ".Chart.Name .fullname" $ }}
    {{- end }}
      rules: {{- toYaml . | nindent 6 }}
  {{- end }}
  {{- end }}
