{{- if .Values.ConfigMaps.enabled }}
  {{- range .Values.ConfigMaps.maps }}
    {{if eq .external false}}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .name}}-{{ $.Values.app }}
  labels:
    appId: {{ $.Values.app | quote }}
    envId: {{ $.Values.env | quote }}
    app: {{ template ".Chart.Name .name" $ }}
    chart: {{ template ".Chart.Name .chart" $ }}
    release: {{ $.Release.Name }}
{{- if $.Values.appLabels }}
{{ toYaml $.Values.appLabels | indent 4 }}
{{- end }}
data:
{{ toYaml .data | trim | indent 2 }}
    {{- end}}
  {{- end}}
{{- end }}
