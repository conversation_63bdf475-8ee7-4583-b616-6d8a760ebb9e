{{- if $.Values.secret.enabled }}
---
apiVersion: v1
kind: Secret
metadata:
  name: app-secret
type: Opaque
data:
{{ toYaml $.Values.secret.data | indent 2 }}
{{- end }}


{{- if .Values.ConfigSecrets.enabled }}
  {{- range .Values.ConfigSecrets.secrets }}
  {{if eq .external false}}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ .name}}-{{ $.Values.app }}
type: Opaque
data:
{{ toYaml .data | trim | indent 2 }}
{{- end}}
  {{if eq .external true }}
  {{if (or (eq .externalType "AWSSecretsManager") (eq .externalType "AWSSystemManager") (eq .externalType "HashiCorpVault"))}}
---
apiVersion: kubernetes-client.io/v1
kind: ExternalSecret
metadata:
  name: {{ .name}}
spec:
  {{- if .roleARN }}
  roleArn: .roleARN
  {{- end}}
  {{- if eq .externalType "AWSSecretsManager"}}
  backendType: secretsManager
  {{- end}}
  {{- if eq .externalType "AWSSystemManager"}}
  backendType: systemManager
  {{- end}}
  {{- if eq .externalType "HashiCorpVault"}}
  backendType: vault
  {{- end}}
  data:
  {{- range .secretData }}
  - key: {{.key}}
    name: {{.name}}
    {{- if .property }}
    property: {{.property}}
    {{- end}}
    isBinary: {{.isBinary}}
  {{- end}}
  {{- end}}
  {{- end}}
  {{- end}}
  {{- end}}