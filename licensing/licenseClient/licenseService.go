package licenseClient

import (
	_ "embed"
	"encoding/json"
	"errors"
	"github.com/devtron-labs/common-lib/env"
	"github.com/devtron-labs/devtron/internal/constants"
	util2 "github.com/devtron-labs/devtron/internal/util"
	"github.com/devtron-labs/devtron/licensing/licenseClient/adapter"
	"github.com/devtron-labs/devtron/util"
	cron2 "github.com/devtron-labs/devtron/util/cron"
	"github.com/devtron-labs/license-manager/client/licenseCert"
	"github.com/devtron-labs/license-manager/client/licenseKey"
	"github.com/devtron-labs/license-manager/common"
	"github.com/devtron-labs/license-manager/types"
	"github.com/devtron-labs/license-manager/types/adapters"
	"github.com/devtron-labs/license-manager/types/dtos"
	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
	"net/http"
	"sync"
	"time"
)

// dt_lic_pub_key NOTE: do not print this in logs

//go:embed pub_key.txt
var dt_lic_pub_key []byte

//go:embed shared_secret.txt
var shared_secret []byte

type LicenseService interface {
	GetLicenseData() (*dtos.LicenseResponse, error)
	GetDetailedLicenseData() (*dtos.LicenseResponse, error)
	IsValidLicenseExists() bool
	UploadLicenseData(licData *dtos.LicenseData) error
}

type LicenseServiceImpl struct {
	logger                  *zap.SugaredLogger
	licRepo                 LicenseRepo
	validationStatusErr     error
	isValidLicense          bool
	lock                    *sync.Mutex
	reminderThresholdConfig *env.ReminderThresholdConfig
	licenseManagerConfig    *LicenseManagerConfig
}

func NewLicenseServiceImpl(logger *zap.SugaredLogger, licRepo LicenseRepo, reminderThresholdConfig *env.ReminderThresholdConfig, licenseManagerConfig *LicenseManagerConfig, cronLogger *cron2.CronLoggerImpl) (*LicenseServiceImpl, error) {
	licenseServer := &LicenseServiceImpl{
		logger:                  logger,
		licRepo:                 licRepo,
		lock:                    &sync.Mutex{},
		reminderThresholdConfig: reminderThresholdConfig,
		licenseManagerConfig:    licenseManagerConfig,
	}
	if shouldCheckLicensing() {
		cron := cron.New(
			cron.WithChain(cron.Recover(cronLogger)))
		cron.Start()
		// executing it first time manually to set the license status as cron will start after an hour
		licenseServer.validateAndSetLicenseStatus()
		// this is the time duration for which user can use the software even after license is expired.
		// Schedule job to run at midnight every day
		_, err := cron.AddFunc("0 0 * * *", licenseServer.validateAndSetLicenseStatus)
		if err != nil {
			logger.Errorw("error while configure cron job for license", "err", err)
			return nil, err
		}
	} else {
		// if licensing is disabled, set the license as valid
		licenseServer.isValidLicense = true
	}

	return licenseServer, nil
}

func (impl *LicenseServiceImpl) GetSystemMetadata() (string, error) {
	metadata := map[dtos.ClientMetadataKey]interface{}{
		dtos.InstallationMode: util.GetDevtronVersion().ServerMode,
	}
	metadataBytes, err := json.Marshal(metadata)
	if err != nil {
		impl.logger.Errorw(fmtLog("error in marshalling system metadata"), "metadata", metadata, "err", err)
		return "", err
	}

	return common.EncryptAES(shared_secret, metadataBytes)
}

func (impl *LicenseServiceImpl) IsValidLicenseExists() bool {
	return impl.isValidLicense
}

func (impl *LicenseServiceImpl) GetLicenseData() (response *dtos.LicenseResponse, err error) {
	resp, err := impl.GetDetailedLicenseData()
	if err != nil {
		impl.logger.Errorw("error in GetDetailedLicenseData", "err", err)
		return nil, err
	}
	// we are not sharing ttl and grace period time as this is an open service call and need to send min data
	// adapter to set min data
	return adapter.BuildMinLicenseGetResponse(resp, impl.IsValidLicenseExists()), nil
}

// GetDetailedLicenseData will fetch the fingerPrint and the licenseKey
func (impl *LicenseServiceImpl) GetDetailedLicenseData() (response *dtos.LicenseResponse, err error) {
	// have validate this on this api as well as cron can have delay but expiry shown is live so validating it once based on api call as well
	if shouldCheckLicensing() {
		impl.validateAndSetLicenseStatus()
	}

	fp := impl.licRepo.GetFingerPrint()
	licKey, err := impl.licRepo.GetLicenseKey()
	if err != nil {
		impl.logger.Errorw("error in getting license key", "err", err)
		return nil, err
	}

	licCert, err := impl.licRepo.GetLicenseCert()
	if err != nil {
		impl.logger.Errorw("error in getting license cert", "err", err)
		return nil, err
	}
	var licenseCertificate *types.LicenseCert
	var errToReturn error
	if shouldCheckLicensing() && len(licCert) > 0 && len(licKey) > 0 {
		var valid bool
		licenseCertificate, valid = licenseCert.VerifyLicenseCert(string(licCert), string(licKey), string(dt_lic_pub_key))
		if !valid {
			errToReturn = util2.GetApiErrorAdapter(http.StatusBadRequest, constants.TamperedCertificate, "tampered license certificate", "tampered license certificate")
		}
	}
	var encryptedMetadata string
	if shouldCheckLicensing() {
		encryptedMetadata, err = impl.GetSystemMetadata()
		if err != nil {
			impl.logger.Errorw("error in getting system metadata", "err", err)
			return
		}
	}
	licenseRequest := &dtos.LicenseRequest{
		FingerPrint: string(fp),
		LicenseKey:  string(licKey),
		Metadata:    encryptedMetadata,
	}

	licenseRequestBytes, err := json.Marshal(licenseRequest)
	if err != nil {
		impl.logger.Errorw("error in marshalling license request", "err", err)
		return nil, errors.New("error in generating license request")
	}

	portableLicenseGenerateData := common.EncodeBase64(licenseRequestBytes)

	// note: only share some suffix bytes of this licenseCertData
	n := len(licCert)
	suffixBytes := impl.licenseManagerConfig.LicenseCertificateSuffixLength
	if n < suffixBytes {
		suffixBytes = n
	}

	licenseCertSuffix := licCert[n-suffixBytes:]
	// fingerprints should be shared with licenseServer( it is not actual fingerprint but marshalled licenseRequest)
	response, err = adapters.BuildGetLicenseResponse(string(licenseCertSuffix), portableLicenseGenerateData, impl.reminderThresholdConfig.ReminderThresholdForFreeTrial, impl.reminderThresholdConfig.ReminderThresholdForLicense, licenseCertificate, "", "")
	if err != nil {
		impl.logger.Errorw("error in building license response", "err", err)
		return nil, err
	}
	if impl.validationStatusErr != nil {
		errToReturn = impl.validationStatusErr
	}
	response.LicenseStatusError = errToReturn
	response.ShowLicenseData = shouldCheckLicensing()
	return
}

// UploadLicenseData uploads the license data to the server
// licenseKey is optional, if not provided it is expected that this is uploaded earlier.
// licenseKey is needed when user is uploading license for the first time.
// cert is the actual license data that is generated for this client by our remote LICENSE MANAGER.
func (impl *LicenseServiceImpl) UploadLicenseData(licData *dtos.LicenseData) error {
	// 1. if licenseKey is already uploaded, then reject new license.
	// 2. if not store it in our DB.
	// 3. verify the cert signature using our pub key.
	// 4. decode the cert data with licenseKey and verify if the fingerprint is same as the one we have.
	// 5. check the expiry date of the license.

	licKey := licData.LicenseKey
	cert := licData.Cert

	licKeyInStore, err := impl.licRepo.GetLicenseKey()
	if err != nil {
		impl.logger.Errorw("error encountered in GetLicenseKey", "err", err)
		return err
	}
	if len(licKeyInStore) > 0 && licKey != string(licKeyInStore) {
		return util2.GetApiErrorAdapter(http.StatusBadRequest, constants.LicKeyMismatch, "license key already uploaded", "license key already uploaded")
	}

	if len(cert) == 0 {
		return util2.GetApiErrorAdapter(http.StatusBadRequest, constants.NoCertFound, "license certificate is empty", "license certificate is empty")
	}

	err = impl.validateLicenseKey(licKey)
	if err != nil {
		impl.logger.Errorw(fmtLog("error in validating license key"), "err", err)
		return err
	}

	err = impl.validateLicCert([]byte(licKey), []byte(cert))
	if err != nil {
		impl.logger.Errorw(fmtLog("error in validating license cert"), "err", err)
		return err
	}

	// license is valid , store the data in store.
	// license can only be uploaded once in the life-time.
	// if license is already uploaded, no need to upload it again.
	isLicenseKeyPresent := len(licKeyInStore) > 0
	err = impl.licRepo.UploadLicenseKeyAndCert([]byte(licKey), []byte(cert), isLicenseKeyPresent)
	if err != nil {
		impl.logger.Errorw("error in uploading license key and cert", "err", err)
		return err
	}

	// if we reach here, we got a valid license
	impl.setLicenseStatusErrWithLock(nil)
	return nil
}

func (impl *LicenseServiceImpl) setLicenseStatusErrWithLock(err error) {
	impl.lock.Lock()
	impl.isValidLicense = err == nil
	impl.validationStatusErr = err
	impl.lock.Unlock()
}

// validateAndSetLicenseStatus will validate the license data
// return user readable error if licenseKey is absent | cert is absent | cert is tampered | cert is expired
func (impl *LicenseServiceImpl) validateAndSetLicenseStatus() {
	err := impl.validate()
	impl.setLicenseStatusErrWithLock(err)
	if err != nil {
		impl.logger.Errorw(fmtLog("invalid license"), "err", err)
	}
	return
}

func (impl *LicenseServiceImpl) validate() error {
	licKey, err := impl.licRepo.GetLicenseKey()
	if err != nil {
		impl.logger.Errorw("error in getting license key", "err", err)
		return err
	}
	if len(licKey) == 0 {
		return util2.GetApiErrorAdapter(http.StatusBadRequest, constants.LicKeyNotFound, "license key not found", "license key not found")
	}
	cert, err := impl.licRepo.GetLicenseCert()
	if err != nil {
		impl.logger.Errorw("error encountered in GetLicenseCert", "err", err)
		return err
	}
	if len(cert) == 0 {
		return util2.GetApiErrorAdapter(http.StatusBadRequest, constants.NoCertFound, "license certificate not found", "license certificate not found")
	}
	return impl.validateLicCert(licKey, cert)
}

func (impl *LicenseServiceImpl) validateLicCert(licKey, cert []byte) error {
	if len(dt_lic_pub_key) == 0 {
		return util2.GetApiErrorAdapter(http.StatusInternalServerError, constants.NoPublicKey, "cannot validate license at this moment", "cannot validate license at this moment")
	}

	dataset, valid := licenseCert.VerifyLicenseCert(string(cert), string(licKey), string(dt_lic_pub_key))
	if !valid {
		return util2.GetApiErrorAdapter(http.StatusBadRequest, constants.TamperedCertificate, "tampered license certificate", "tampered license certificate")
	}
	// verify fingerprint
	fingerprint := dataset.LicenseData.FingerPrint
	systemFingerprint := impl.licRepo.GetFingerPrint()
	if validFingerPrint, err := common.EqualFingerPrints(shared_secret, fingerprint, string(systemFingerprint)); err != nil {
		impl.logger.Errorw(fmtLog("error in validating fingerprint"), "err", err)
		return util2.GetApiErrorAdapter(http.StatusInternalServerError, "500", "failed to validate fingerprint", "failed to validate fingerprint")
	} else if !validFingerPrint {
		return util2.GetApiErrorAdapter(http.StatusBadRequest, constants.FingerPrintMisMatch, "License key is invalid for this fingerprint", "License key is invalid for this fingerprint")
	}

	err := impl.validateMetadata(dataset)
	if err != nil {
		return err
	}

	if dataset.ExpireAt.Add(time.Duration(dataset.GracePeriodInSecs) * time.Second).Before(time.Now()) {
		return util2.GetApiErrorAdapter(http.StatusBadRequest, constants.LicenseExpired, "license expired", "license expired")
	}
	return nil
}

func (impl *LicenseServiceImpl) validateMetadata(dataset *types.LicenseCert) error {
	// right now only extended to mode but in future can be extended to feature, user count etc.
	if dataset.Metadata[dtos.InstallationMode] != util.GetDevtronVersion().ServerMode {
		return util2.GetApiErrorAdapter(http.StatusBadRequest, constants.InstallationModeMismatch, "invalid license: installation mode mismatch", "invalid license: installation mode mismatch")
	}
	return nil
}

func (impl *LicenseServiceImpl) validateLicenseKey(licKey string) error {
	if !licenseKey.VerifyLicenseKey(licKey, string(dt_lic_pub_key)) {
		return util2.GetApiErrorAdapter(http.StatusBadRequest, constants.TamperedLicenseKey, "tampered license key", "tampered license key")
	}

	return nil
}
