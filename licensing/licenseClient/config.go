package licenseClient

import (
	"fmt"
	"github.com/caarlos0/env"
)

type LicenseManagerConfig struct {
	LicenseCertificateSuffixLength int `env:"LICENSE_CERTIFICATE_SUFFIX_LENGTH" envDefault:"5" description:"this is used to set the length of license certificate suffix to be shown to user"`
}

func GetLicenseManagerConfig() (*LicenseManagerConfig, error) {
	cfg := &LicenseManagerConfig{}
	err := env.Parse(cfg)
	if err != nil {
		fmt.Println("error occurred while parsing config GetLicenseManagerConfig ", "err", err)
		return nil, err
	}
	return cfg, nil
}
