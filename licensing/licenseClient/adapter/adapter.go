package adapter

import (
	"github.com/devtron-labs/devtron/internal/util"
	"github.com/devtron-labs/license-manager/types/dtos"
	"net/http"
	"time"
)

func BuildMinLicenseGetResponse(resp *dtos.LicenseResponse, isValidLicense bool) *dtos.LicenseResponse {
	if isValidLicense {
		// if valid response exist we will set empty data for security concern
		resp.TTL = 0
		resp.Expiry = time.Time{}
		resp.GracePeriod = 0
		resp.OrganisationMetadata = nil
	}
	return resp
}

var InvalidLicenseErr = &util.ApiError{
	HttpStatusCode:  http.StatusBadRequest,
	Code:            "400",
	InternalMessage: "invalid license",
	UserMessage:     "invalid license",
}
