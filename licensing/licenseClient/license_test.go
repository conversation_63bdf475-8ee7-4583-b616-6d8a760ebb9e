package licenseClient

import (
	"testing"
)

func TestKeyGenCERT(t *testing.T) {
	//keygen.PublicKey = "801a2f2c1ed0ee36ecc8295a8f77094a4e62aa424ba89f992000795e3ed812ca"
	////// Read the license file
	////cert, err := ioutil.ReadFile("/etc/example/license.lic")
	////if err != nil {
	////	panic("license file is missing")
	////}
	//cert := "-----BEGIN LICENSE FILE-----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\ncy0yNTYtZ2NtK2VkMjU1MTkifQ==\n-----END LICENSE FILE-----"
	////cert := "Ua05udk4vY0lYR0Ri\n\n\n\nNVRDV01sejVDY0djdnZmaXZhQldIdjIyOTVxa2h0NGx6TGM1NU9HdHRQMzFa\nMGNhaE9tZjZRZXgzRmlmN2dtTUhxYklTL2syRlVLR25ZL3N4a3NkU09pTlQx\nT1B5U1FuZWExaDF5SEVSVWVXWnRqQ2ZCb01sZklsTkJESFU1LytCYTNsWjAx\nMWxkSjR1U0dUT3pQQ1ZCWDlIY1ZRVlUrVFFuSW9QTHNtV1NhallaN2xENjZo\nOE8ySllDZC9EMmNzb3BOLzRHdElEa1pFTUlvaDA5b21Ua0kvbDRrclZweU1G\naC9ON0o0cm8vUjdvUXVaSE1vUFRrWmpadmo2dCtSVmRQZjZrV0s2YmJpN1ZG\nZStTaDZKWFFYWmVPUzdhWE5EaGNNYmlEcDR1TnprN3BNdlN5OXAybC9EOTQ5\nSWQyWFIxMG1UNW4rL2VZeFJsUU5RNWMxOUY4REVtaTFDMlV2cmtMWDhvMnVr\nZXI5d0FHZDdUSEVMN3RDaUxMQjB6QSt6Qll5UE1vRmJrckxzRjlwY1FrME5I\nRHJpZlNCRkU0c3d2UFR1Q2pKeHRLUURyZzE4T3Vpc3oyclpCYlN5SElxRnIy\nb1lTN1llWVYxVU1VaFdQUWlMSGduNGFram44ZTBKd1dJdjVXRGF3QjZyTFh1\ndEtTS3o5eWtINjFnblRTTTg3YmRLZEVlNS9FYWZXZDFKeS92VjVKTkhjZHpl\nQ09zOWFxVWV6NGREUW1mdnJ2SlovSXZOdXpjUUIxVTl2NUI3bXBZOVlnSDk2\nSVQvQVFYTmkwTmFYUjJ0cHpwZ0dWWFFNQno2bWFzeWpFckllblBzRHMxZVJa\nN1ZJUUJVMXhmaDZHWkNkcW5XcW5qSlJZbW5JbGsvSkJNa3hQamFSOXN3RWZs\nbXB5dmpmYjlNK0hJUUdJK2NzY2NCb1MxMFZnbnZuUHU2RVAxNXl3NEtNLy9X\na2krZWE2MkR1SzBnK05Fek5vZEltM0hES3VRNmptMHVXM1EwU1BPbmJaSU1v\naU55WTVIZWVJa2NJNTI3b21CcGRQQlFtQlRDNlNlcUJuUzJJdlVOM2xFRExs\nU2dYcHoyby82ZFhSZFNXV0l5aFptNmExZjltSzdYRFgxVGVRdW1wcnNlVzY5\nWVo5QzNyVG1adXFtTG4wRGxTaGNPUWJ3ZWpweFdCTVZYSUJBVHFUSTdZTGZx\nRi9zVlhrKzFmV2lRYzJPQU1zYWoxUXlNRTNKdnRoSU81L2JMZTFGaXVCM2g2\nSVA2YU11eDRPcVVMTnBtdFZoWHRGbHdlSkdVUUVhNExQRm80b2xwcnZMalNN\nQUNYYk0reHJsZ20vNm9remZQaUhEUDhPSmRtWXc5WGt2WEFBd2RMMVgvWXFz\nNHNETndiNFJOS0dnTEQzVVRvNkdISnEwclVtNVdtdk5nelc2dGZDd0I5L1d5\ncjFsNFphY2haSkJWbjZyN0xFSFg3NFV5OURNNHpTZmJuZE00T0NpV0QyTjc3\nRHE1TzNQdXJROFo3MWExNTRkS2hpeXV3WlUwVldacGhiVzY5NXFzLzloRkJ3\nYXZOeXlzWlVtS1B0NGwwZkJvNmpvR3RmYzhlTEljRjd5VVZkQUhJSUdJSUo3\nbGhNQTdNakdrRE1LNjR0UktqZ3FuNzlzMDk1aFI4eGtxYmVYaEZsM3NTWmlO\nT1FBdStGTDRQUTRyUlJxWWJnTnJ2L09DTnlOU2tYZ2UwaWFtZ2s3TVYxQWEx\nNjhTakN1NG1qMVhURVVGeW1IOW9SN1dodGJlZjdLOXZrRnY3dkU4ZHJsNENa\ndmQwbXlHcmRRazhMN2NHcUpZYnJWamRaK3RNOWUyN0JpMEJDRGlmemRwNlRz\nYmlEVDNMNkFvNkxmcTVtd0pEQU1YZ1BxcFBLcE5CNHRIR2Q0Qkc0VUhGOTRX\nbnpmY1B3a1d4UTd1VmFOL2NzQ0p0Yzg3MzAxRjlBalFmR2p6WWFjam5LVURR\nYWpyd2dwUUMzQUUveTFBZlJZUXZpZnY1WDVEK25jNmdtdEl6N1JiVFNCME5H\nL2RPVmVHNWVzeHB6TUxBNkNxbndxN2tTK3FDUy8vUCtlcVdkK3NJR2JwUjRM\namxBWldyczNEeW5UemhtQXNJVDB3bU1yZGFsblFkQjhwZjhVZDlHN1h2TEJ2\nMFZjbWtqMFIwZ251aWgwbFUwMHgzRStWVU00T0dmU3FRcmtBVU92TExxL0dO\nRkNzR0hab3J5YjUzbkIxUUg3L2VjOWd2bWlnOUcvckhpOWJmZVVQVXg0c1ZB\naVBnZUlqeDNpclFxRDFYbytlYWtHOWVrcDFLcmJndXdnamZLMHVwT3c4b01h\nOEYrMkZBV3FYRUU0WlhjUFMyRXFKUDFGbTU2U1RBL2tYZWFhRGtwZzdlZnZ5\nL3ZjKzR3d3VSVVJwUDhVS0xkSmc3RlhVdGxFSjBWbVlYT1h3RERDTDNxenJF\nc3N2Rm9HcUdLSUJ0ZkF4cGNsbVFFY3ZhRmJBTUZIMDgrNHhPWWhTMm5YTmYv\nRHpQN3FQaWJyUEJCSXZ3bXFNNUlKRUEvbzVLK2hqUTAvc2lydVRRb0R4TDhz\nOXBvUWNyOTY0RGxqUzBkU1F6WXorMUVPL29rd1BseHJmQmg5VW5wVWpXTjZS\naHdjdDFjc3pvKzNiT2lwanNOSVVBT1huN0thQUQrcTFodDlvTkFSd2hldDFX\nQjhDT3h3UU1KdkhuSFJKVjZ0NlZ6Q2U2NUdpUkhSMSt1Wk51Nm5rUFBCTFJh\nbm0xWHlLNWExOGx3YnR5ZkJyc2Q1Z2hsb3Q4b05PaFI3MmQ4K28yYUNxRnFk\na3NzQUNYc0JCR3pwcHN0SldKV1NDLzZrT2N3VEtTcVdYcFdyVHpoNzNUWWRM\nQmFDMlAxc2pVcWVEUUszbDJ1LzNFaThEZzF5Q201WktIS2wreHp0SHZ6NmFD\nTWU5bVdrY3FIRVpWYnVhYzQ2NE51UGVqbkRSQk42N3JUaWx3YVRPa3NWYk55\nNHVOVzBUc2s2ZHlDQldNN01MUWR1SmRpamZBRk9BUnJBWDdUWDR4bzh6aGVK\nYitaak9MTDVSWHZkbE93RkRLcmREWm5Fb3lZbCtlSWJtdzdZMTBKaVFhY1N0\nbnQ2M2FwbkdIazI2a2dyQkZqZitkRUdkcmQ5eEV4RVRIdHpvMFRIUmhQZFgw\nSWt0b0kwSnJVaGpwdFlsV25xeFE4N2hHbTJrTkovenIzZ2tFT0QvMEU5NVlp\nQzdpb3R5elFlQ1hSaGxaUDNBWTJrenlWVnlMdWZJK0llTG1IM0xWV0RJSFJW\ndUpJSEFNbUhJbkcyb2oyaGpjaTE4S3M1cG9Ra1VublI1QWxuQk1BRFVBVFRL\nTUxSS0RHRXU1OUhaMmJ4Y1RCc0hMZE5JVUF0bTZGVmFtYUNVUnErMlpzd24r\nZUlQYzU4TUhVT2NERENGRjBnTHMyTUpvQVhyT1NTcjVEaHp2UHdUL0VOY0hL\nSWRFVDZ5WTVzMnZ6QVcvK0EyUFlYL2tyZ1NSQWZEeUtCSWJXc2g5MFpub1o4\nZmViZWNaeXpGR0Y2cHlLemNmREtidG5TQXlRZmJKNHR3ZzFiM0h2YzBpblZr\nejNTZ25LTTdIa2NCRjd0c0J4bEQ5STVNdzlwYnM0M0hVWHkzcStNK2hMK09G\nTk9rSWNlVy9uZ2JUU21KanY1bW1xM2NjNzJ0MG1wUDVTWkpVMDRhbUxXWG91\nRkhBV0FyVWYwNDlVcm1DZW15WmJDZVRyQ2NlRU1GRWo2QTQzQUNlYjR2OFJn\nMit4RzI2Yjczb2RVdGwrb1V3WFFXZHRKS2RIaW8vMFJsY2FXWFVrSytEZjFa\nb01LN0dJRzVPY0lFRXJjd3RTU0dmKzJXTllRcHV4dHJVZ3Zhb0hwU2sxMDdG\nVGx2aktoSHp4a2ZpVE9oZUltdzR4M2JXTGdZcHJmOEZ1UGw1OCtxb1hZZGIr\nQWVEVkJXVFcrRXF6d2FGRGsvc0g4YTdpcUNaZGxNMlNUQmlVVDRXdUticzZv\nRE5tak9NckNCS3ZCMFkva1l5LzZCN3RLZXE5MU09LlJ2Q0pQV2tBZ2wrSGp5\nM3cucGFnNW9BOTg1YTBvM01idGR3MFcrQT09Iiwic2lnIjoiQ1RsV0lxSlZ2\nMGwwcUd6WmdJM2I0eHpCNUJ4U2E2V2FOYjhHc3M2ZVI5NmZCQ0lIRDBXR3Jq\nU0RjRC9MN292eStCcGFoU3dJWXdBYlJCNGpoV1ZqREE9PSIsImFsZyI6ImFl\ncy0yNTYtZ2NtK2VkMjU1MTkifQ==\n-----END LICENSE FILE-----"
	//// Verify the license file's signature
	//lic := &keygen.LicenseFile{Certificate: string(cert)}
	//err := lic.Verify()
	//fmt.Println(err)
	//if err != nil {
	//	t.Fail()
	//	return
	//}
	//keygen.LicenseKey = "key/SldLSy1XSFZVLTdIVlYtV05YTi1KUjlKLUhOSjMtS0VYRi1FN1A5.kooDM_w7qKA17cdKqBpiK_jSw4JXMIOO7PUmiSZUlJaiAidK_n8zzsxEtPYM8SDEjJza7C6M3y8Ojmp1fWcqCQ=="
	//dataset, err := lic.Decrypt(keygen.LicenseKey)
	//if err != nil {
	//	t.Fail()
	//}
	//if dataset.Expiry.Before(time.Now()) {
	//	fmt.Println("license expired")
	//	t.Fail()
	//}
	//fmt.Println(dataset)
}

func TestKeyGenLICENSE_KEY(t *testing.T) {
	//// baked in the binary
	//keygen.Account = "c791e74b-4fce-4c1e-8c85-418d03d43b67"
	//keygen.Product = "e3ab74e2-c952-4237-88e4-875de07ed4cb"
	//keygen.PublicKey = "801a2f2c1ed0ee36ecc8295a8f77094a4e62aa424ba89f992000795e3ed812ca"
	//
	//// prompt user for the license key
	//keygen.LicenseKey = "key/V05DVS1SWE1YLUE3WUgtTktLTi1IV1dBLTlQV0wtTEVYTS1URVlY.4DmIgFq6KMrHR_mHb721xWLCS3SrinoGKKDOuXHhpMAAObkE0EQOB0jxXc0mqrFia--kxhqT6fW_l2GH61UVCQ=="
	//
	//// validate the license
	//lic, err := keygen.Validate(context.Background(), "test2")
	//fmt.Println("lic: ", lic, "err:", err)
	//if err != nil {
	//	t.Fail()
	//}
	//_, err = lic.Verify()
	//fmt.Println(err)
	////fmt.Println(data)
	//if err != nil {
	//	t.Fail()
	//}
}

/*
           +----------------------+
           |  User Installs Software |
           +----------------------+
                       |
                       v
           +----------------------+
           |  Generates Unique ID  |
           |  (UUID or QR Code)   |
           +----------------------+
                       |
                       v
           +----------------------+
           | User Requests License |
           |  (Scans QR/Login)    |
           +----------------------+
                       |
                       v
           +----------------------+
           |  Intermediate Service |
           |  (Billing Check)     |
           +----------------------+
                       |
                       v
           +----------------------+
           |  Keygen License API  |
           |  (Creates License)   |
           +----------------------+
                       |
                       v
           +----------------------+
           | User Receives .lic File |
           +----------------------+
                       |
                       v
           +----------------------+
           |  Uploads .lic to Software |
           +----------------------+
                       |
                       v
           +----------------------+
           |  Verifies Signature   |
           |  (Using Public Key)   |
           +----------------------+
                       |
                       v
   +----------------------+      +---------------------+
   |  License Valid       | ----> |  Software Activated |
   +----------------------+      +---------------------+
   |  License Invalid     | ----> |  Access Denied      |
   +----------------------+      +---------------------+
*/
