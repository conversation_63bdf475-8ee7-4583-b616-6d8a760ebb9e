package licenseClient

import (
	"errors"
	"github.com/devtron-labs/devtron/api/restHandler/common"
	"github.com/devtron-labs/devtron/internal/middleware"
	"net/http"
	"strings"
)

type LicenseMiddleware struct {
	licenseService LicenseService
}

func NewLicenseMiddleware(licenseService LicenseService) *LicenseMiddleware {
	middleware := &LicenseMiddleware{licenseService: licenseService}
	return middleware
}

var PublicAndSecretKeyPresent = len(dt_lic_pub_key) > 0 && len(shared_secret) > 0

func shouldCheckLicensing() bool {
	return PublicAndSecretKeyPresent
}

func (lm *LicenseMiddleware) LicensingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if shouldCheckLicensing() && !lm.licenseService.IsValidLicenseExists() {
			w.<PERSON><PERSON>().Set("X-License-Status", "inValid")
			if LicensingWhitelistChecker(r.URL.Path) {
				d := middleware.NewDelegator(w, nil)
				next.ServeHTTP(d, r)
				return
			}
			common.WriteJsonResp(w, errors.New("license expired"), nil, http.StatusForbidden)
			return
		} else {
			w.Header().Set("X-License-Status", "valid")
			d := middleware.NewDelegator(w, nil)
			next.ServeHTTP(d, r)
		}
	})
}

func LicensingWhitelistChecker(url string) bool {
	urls := []string{
		"/orchestrator/dashboard-event/dashboardAccessed",
		"/orchestrator/license/data",
		"/",
		"/health",
	}
	for _, a := range urls {
		if a == url {
			return true
		}
	}
	prefixUrls := []string{
		"/dashboard",
	}
	for _, a := range prefixUrls {
		if strings.Contains(url, a) {
			return true
		}
	}
	return false
}
