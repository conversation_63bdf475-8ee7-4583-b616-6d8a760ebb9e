package licenseClient

import (
	"context"
	"errors"
	"github.com/devtron-labs/common-lib-private/utils/k8s"
	"github.com/devtron-labs/devtron/pkg/apiToken"
	"github.com/devtron-labs/devtron/pkg/attributes"
	"github.com/devtron-labs/devtron/pkg/attributes/bean"
	ucidService "github.com/devtron-labs/devtron/pkg/ucid"
	"github.com/devtron-labs/license-manager/common"
	"github.com/go-pg/pg"
	"go.uber.org/zap"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type LicenseRepo interface {
	GetLicenseKey() ([]byte, error)
	GetLicenseCert() ([]byte, error)
	GetFingerPrint() []byte
	UploadLicenseKeyAndCert(licenseKey []byte, cert []byte, isLicenseKeyAlreadyPresent bool) error
}

type LicenseRepoImpl struct {
	logger                   *zap.SugaredLogger
	dbConnection             *pg.DB
	ucid                     ucidService.Service
	licenseAttributesService attributes.LicenseAttributesService
	apiTokenSecretService    apiToken.ApiTokenSecretService
	k8sUtil                  *k8s.K8sUtilExtended
	fingerPrint              []byte
}

func NewLicenseRepoImpl(
	logger *zap.SugaredLogger,
	dbConnection *pg.DB,
	ucid ucidService.Service,
	apiTokenSecretService apiToken.ApiTokenSecretService,
	licenseAttributesService attributes.LicenseAttributesService,
	k8sUtil *k8s.K8sUtilExtended,
) (*LicenseRepoImpl, error) {
	licenseRepo := &LicenseRepoImpl{
		logger:                   logger,
		dbConnection:             dbConnection,
		ucid:                     ucid,
		licenseAttributesService: licenseAttributesService,
		apiTokenSecretService:    apiTokenSecretService,
		k8sUtil:                  k8sUtil,
	}
	if shouldCheckLicensing() {
		// 1. load kube-system namespace UID.
		k8sNamespaceUID, err := licenseRepo.getKubeSystemNamespaceUID()
		if err != nil {
			licenseRepo.logger.Errorw("error in getting KS-ID", "err", err)
			return nil, err
		}

		// 3. load devtron UCID.
		devtronUCID, err := licenseRepo.getDevtronUCID()
		if err != nil {
			licenseRepo.logger.Errorw("error in getting DT-ID", "err", err)
			return nil, err
		}

		// 4. generate fingerprint.
		fingerPrint, err := common.NewFingerPrint("v1.0", devtronUCID, k8sNamespaceUID).Encrypt(shared_secret)
		if err != nil {
			licenseRepo.logger.Errorw("error in generating fingerprint", "err", err)
			return nil, err
		}
		licenseRepo.fingerPrint = fingerPrint
	}
	return licenseRepo, nil
}

func (impl *LicenseRepoImpl) GetLicenseKey() ([]byte, error) {
	licKeyAttr, err := impl.licenseAttributesService.GetByKey(bean.LICENSE_KEY)
	if err != nil {
		impl.logger.Errorw("error in getting license key", "err", err)
		return nil, err
	}
	var licKeyAttrValue string
	if licKeyAttr != nil {
		impl.logger.Debugw("license key found")
		licKeyAttrValue = licKeyAttr.Value
	}
	return []byte(licKeyAttrValue), nil
}

func (impl *LicenseRepoImpl) GetLicenseCert() ([]byte, error) {
	licenseCertAttr, err := impl.licenseAttributesService.GetByKey(bean.LICENSE_CERT)
	if err != nil {
		impl.logger.Errorw("error in getting license cert", "err", err)
		return nil, err
	}
	var licenseCertAttrValue string
	if licenseCertAttr != nil {
		impl.logger.Debugw("license cert found")
		licenseCertAttrValue = licenseCertAttr.Value
	}
	return []byte(licenseCertAttrValue), nil
}

func (impl *LicenseRepoImpl) GetFingerPrint() []byte {
	return impl.fingerPrint
}

func (impl *LicenseRepoImpl) UploadLicenseKeyAndCert(licenseKey []byte, cert []byte, isLicenseKeyAlreadyPresent bool) error {
	tx, err := impl.dbConnection.Begin()
	if err != nil {
		impl.logger.Errorw("error in starting txn", "err", err)
		return err
	}
	defer tx.Rollback()
	// license can only be uploaded once in the life-time.
	// if license is already uploaded, no need to upload it again.
	if !isLicenseKeyAlreadyPresent {
		err = impl.UploadLicenseKey(tx, licenseKey)
		if err != nil {
			impl.logger.Errorw("error in uploading license key", "err", err)
			return err
		}
	}
	err = impl.UploadLicenseCert(tx, cert)
	if err != nil {
		impl.logger.Errorw("error in uploading license cert", "err", err)
		return err
	}

	err = tx.Commit()
	if err != nil {
		impl.logger.Errorw("error in committing txn for storing licenseKey", "err", err)
		return err
	}
	return nil
}

// UploadLicenseKey write the licenseKey to db and cache
// atomic operation
func (impl *LicenseRepoImpl) UploadLicenseKey(tx *pg.Tx, licenseKeyByte []byte) error {
	licenseKey, err := impl.GetLicenseKey()
	if err != nil {
		impl.logger.Errorw("error in getting license key", "err", err)
		return err
	}
	if len(licenseKey) > 0 {
		return errors.New("license key already uploaded")
	}

	// write to db
	_, err = impl.licenseAttributesService.AddAttributeWithTx(&bean.AttributesDto{Key: bean.LICENSE_KEY, Value: string(licenseKeyByte)}, tx)
	if err != nil {
		impl.logger.Errorw("error in adding license key", "err", err)
		return err
	}

	return nil
}

// UploadLicenseCert write the licenseCert to db and cache
// atomic operation
func (impl *LicenseRepoImpl) UploadLicenseCert(tx *pg.Tx, cert []byte) error {
	_, err := impl.licenseAttributesService.AddAttributeWithTx(&bean.AttributesDto{Key: bean.LICENSE_CERT, Value: string(cert)}, tx)
	if err != nil {
		impl.logger.Errorw("error in adding license key", "err", err)
		return err
	}
	return nil
}

func (impl *LicenseRepoImpl) getDevtronUCID() (string, error) {
	ucid, _, err := impl.ucid.GetUCIDWithOutCache()
	return ucid, err
}

func (impl *LicenseRepoImpl) getKubeSystemNamespaceUID() (string, error) {
	coreV1Client, err := impl.k8sUtil.GetClientForInCluster()
	if err != nil {
		impl.logger.Errorw("error in getting k8s client", "err", err)
		return "", err
	}
	kubeSystemNS, err := coreV1Client.Namespaces().Get(context.Background(), "kube-system", metav1.GetOptions{})
	if err != nil {
		impl.logger.Errorw("error in getting namespace", "err", err)
		return "", err
	}
	return string(kubeSystemNS.UID), nil
}
