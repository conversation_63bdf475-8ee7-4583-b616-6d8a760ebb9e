package handler

import (
	"github.com/gorilla/mux"
)

type LicenseRouter interface {
	InitLicenseRouter(configRouter *mux.Router)
}

type LicenseRouterImpl struct {
	licenseRestHandler LicenseHandler
}

func NewLicenseRouterImpl(licenseRestHandler LicenseHandler) *LicenseRouterImpl {
	return &LicenseRouterImpl{licenseRestHandler: licenseRestHandler}
}

func (router *LicenseRouterImpl) InitLicenseRouter(subRouter *mux.Router) {
	subRouter.HandleFunc("/data", router.licenseRestHandler.HandleLicenseUpload).Methods("POST")
	subRouter.HandleFunc("/data", router.licenseRestHandler.GetLicenseData).Methods("GET")
	subRouter.HandleFunc("/data/details", router.licenseRestHandler.GetDetailedLicenseData).Methods("GET")
}
