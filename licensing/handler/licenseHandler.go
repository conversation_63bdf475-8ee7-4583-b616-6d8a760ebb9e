package handler

import (
	"encoding/base64"
	"encoding/json"
	"github.com/devtron-labs/devtron/api/restHandler/common"
	"github.com/devtron-labs/devtron/client/telemetry"
	"github.com/devtron-labs/devtron/licensing/licenseClient"
	"github.com/devtron-labs/devtron/licensing/licenseClient/adapter"
	"github.com/devtron-labs/devtron/pkg/auth/user"
	"github.com/devtron-labs/license-manager/types/dtos"
	"go.uber.org/zap"
	"net/http"
	"time"
)

type LicenseHandler interface {
	HandleLicenseUpload(w http.ResponseWriter, r *http.Request)
	GetLicenseData(w http.ResponseWriter, r *http.Request)
	GetDetailedLicenseData(w http.ResponseWriter, r *http.Request)
}

type LicenseHandlerImpl struct {
	logger         *zap.SugaredLogger
	licenseService licenseClient.LicenseService
	userService    user.UserService
	telemetry      telemetry.TelemetryEventClient
}

func NewLicenseHandler(logger *zap.SugaredLogger, licenseService licenseClient.LicenseService, userService user.UserService, telemetry telemetry.TelemetryEventClient) *LicenseHandlerImpl {
	return &LicenseHandlerImpl{
		logger:         logger,
		licenseService: licenseService,
		userService:    userService,
		telemetry:      telemetry,
	}
}

func (lr *LicenseHandlerImpl) HandleLicenseUpload(w http.ResponseWriter, r *http.Request) {
	lr.logger.Info("License data UPLOAD request received")
	// we are expecting a base64 encoded single json object in the request body
	uploadedData := dtos.LicenseResponse{}
	// Decode the request body
	decoder := json.NewDecoder(r.Body)
	err := decoder.Decode(&uploadedData)
	if err != nil {
		lr.logger.Errorw("error in decoding request body", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusBadRequest)
		return
	}

	decodedBytesOfUploadedLicense, err := base64.StdEncoding.DecodeString(uploadedData.LicenseData)
	if err != nil {
		lr.logger.Errorw("error in decoding request body", "err", err)
		common.WriteJsonResp(w, adapter.InvalidLicenseErr, nil, http.StatusBadRequest)
		return
	}

	// now unmarshal the decoded bytes to LicenseData
	var data dtos.LicenseData
	err = json.Unmarshal(decodedBytesOfUploadedLicense, &data)
	if err != nil {
		lr.logger.Errorw("error in unmarshalling request body", "err", err)
		common.WriteJsonResp(w, adapter.InvalidLicenseErr, nil, http.StatusBadRequest)
		return
	}

	err = lr.licenseService.UploadLicenseData(&data)
	if err != nil {
		lr.logger.Errorw("error in uploading license data", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}
	err = lr.telemetry.SendGenericTelemetryEvent(telemetry.LicenseKeyUploaded.ToString(), map[string]interface{}{"time": time.Now()})
	if err != nil {
		lr.logger.Errorw("error in uploading telemetry posthog data", "err", err)
	}

	common.WriteJsonResp(w, nil, nil, http.StatusOK)
}

// GetLicenseData returns the min license data with expiry with ttl(if expired) and is open for all
func (lr *LicenseHandlerImpl) GetLicenseData(w http.ResponseWriter, r *http.Request) {
	lr.logger.Info("License data GET request received")
	response, err := lr.licenseService.GetLicenseData()
	if err != nil {
		lr.logger.Errorw("error encountered in GetLicenseData", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	common.WriteJsonResp(w, nil, response, http.StatusOK)
}

// GetDetailedLicenseData returns the detailed license data with expiry with ttl and is passed by authorizer
func (lr *LicenseHandlerImpl) GetDetailedLicenseData(w http.ResponseWriter, r *http.Request) {
	lr.logger.Info("License data GET request received")
	response, err := lr.licenseService.GetDetailedLicenseData()
	if err != nil {
		lr.logger.Errorw("error encountered in GetDetailedLicenseData", "err", err)
		common.WriteJsonResp(w, err, nil, http.StatusInternalServerError)
		return
	}

	common.WriteJsonResp(w, nil, response, http.StatusOK)
}
