{{- if $.Values.autoscaling.enabled }}
---
apiVersion: autoscaling/v2beta2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ template ".Chart.Name .fullname" $ }}-hpa
spec:
  scaleTargetRef:
    apiVersion: argoproj.io/v1alpha1
    kind: Rollout
    name: {{ include ".Chart.Name .fullname" $ }}
  minReplicas: {{ $.Values.autoscaling.MinReplicas  }}
  maxReplicas: {{ $.Values.autoscaling.MaxReplicas }}
  {{- if $.Values.autoscaling.TargetMemoryUtilizationPercentage }}
  metrics:
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ $.Values.autoscaling.TargetMemoryUtilizationPercentage }}
    {{- end }}
    {{- if $.Values.autoscaling.TargetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization:  {{ $.Values.autoscaling.TargetCPUUtilizationPercentage }}
    {{- end }}
    {{- if $.Values.autoscaling.extraMetrics }}
  {{toYaml $.Values.autoscaling.extraMetrics | indent 4 }}
  {{- end}}
  {{- end }}
