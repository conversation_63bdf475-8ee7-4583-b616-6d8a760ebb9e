# Pipeline Plugins

Pipeline plugins (a.k.a. preset plugins) are micro tools that allow you to enhance and refine the [CI/CD workflow](../creating-application/workflow/README.md) of your application by adding new features, integrating with external tools, and automating tasks.

Unlike [custom scripts](../creating-application/workflow/ci-build-pre-post-plugins.md#execute-custom-script), preset plugins come bundled with specific variables and conditions that help you make the plugins work seamlessly with your CI/CD pipeline.

Some plugins are meant for pre-build/post-build, while some are meant for pre-deployment/post-deployment.

From this section, you can know more about the individual plugins and its purpose.

