FROM golang:1.24.0  AS build-env

RUN echo $GOPATH && \
    apt update && \
    apt install git gcc musl-dev make -y && \
    go install github.com/google/wire/cmd/wire@latest

WORKDIR /go/src/github.com/devtron-labs/devtron

ADD . /go/src/github.com/devtron-labs/devtron/

ADD ./vendor/github.com/Microsoft/ /go/src/github.com/devtron-labs/devtron/vendor/github.com/microsoft/

ARG ENABLE_LICENSING
ENV ENABLE_LICENSING=${ENABLE_LICENSING}

RUN if [ "$ENABLE_LICENSING" = "TRUE" ]; then \
      echo "Licensing is enabled. Copying license files..."; \
      mkdir -p licensing/licenseClient; \
      cp license-creds/pub_key.txt license-creds/shared_secret.txt licensing/licenseClient/; \
    else \
      echo "Licensing is disabled. Skipping license file copy."; \
    fi

RUN GOOS=linux make build-all

FROM ubuntu:24.04@sha256:72297848456d5d37d1262630108ab308d3e9ec7ed1c3286a32fe09856619a782 AS  devtron-ea

RUN apt update && \
    apt install ca-certificates curl -y && \
    apt clean autoclean && \
    apt autoremove -y && rm -rf /var/lib/apt/lists/*  && \
    useradd -ms /bin/bash devtron

COPY --chown=devtron:devtron --from=build-env  /go/src/github.com/devtron-labs/devtron/auth_model.conf .
COPY --chown=devtron:devtron --from=build-env  /go/src/github.com/devtron-labs/devtron/cmd/external-app/devtron-ea .

COPY --from=build-env  /go/src/github.com/devtron-labs/devtron/argocd-assets/ /go/src/github.com/devtron-labs/devtron/vendor/github.com/argoproj/argo-cd/assets
COPY --from=build-env  /go/src/github.com/devtron-labs/devtron/scripts/devtron-reference-helm-charts scripts/devtron-reference-helm-charts
COPY --from=build-env  /go/src/github.com/devtron-labs/devtron/scripts/sql scripts/sql
COPY --from=build-env  /go/src/github.com/devtron-labs/devtron/scripts/casbin scripts/casbin
COPY --from=build-env  /go/src/github.com/devtron-labs/devtron/scripts/argo-assets/APPLICATION_TEMPLATE.tmpl scripts/argo-assets/APPLICATION_TEMPLATE.tmpl
COPY --from=build-env /go/src/github.com/devtron-labs/devtron/scripts/sql-ent scripts/sql-ent
COPY --from=build-env /go/src/github.com/devtron-labs/devtron/scripts/casbin scripts/casbin

RUN chown -R devtron:devtron ./scripts

USER devtron

CMD ["./devtron-ea"]
