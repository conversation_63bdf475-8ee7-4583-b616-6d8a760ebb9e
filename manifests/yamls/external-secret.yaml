---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: externalsecrets.kubernetes-client.io
  annotations:
    # used in e2e testing
    app.kubernetes.io/managed-by: helm
spec:
  group: kubernetes-client.io
  scope: Namespaced

  versions:
    - name: v1
      served: true
      storage: true
      subresources:
        status: {}
      schema:
        openAPIV3Schema:
          required:
            - spec
          type: object
          properties:
            spec:
              type: object
              properties:
                controllerId:
                  description: The ID of controller instance that manages this ExternalSecret.
                    This is needed in case there is more than a KES controller instances within the cluster.
                  type: string
                type:
                  type: string
                  description: >-
                    DEPRECATED: Use spec.template.type
                template:
                  description: Template which will be deep merged without mutating
                    any existing fields. into generated secret, can be used to
                    set for example annotations or type on the generated secret
                  type: object
                  x-kubernetes-preserve-unknown-fields: true
                backendType:
                  description: >-
                    Determines which backend to use for fetching secrets
                  type: string
                  enum:
                    - secretsManager
                    - systemManager
                    - vault
                    - azureKeyVault
                    - gcpSecretsManager
                    - alicloudSecretsManager
                    - ibmcloudSecretsManager
                    - akeyless
                vaultRole:
                  description: >-
                    Used by: vault
                  type: string
                vaultMountPoint:
                  description: >-
                    Used by: vault
                  type: string
                kvVersion:
                  description: Vault K/V version either 1 or 2, default = 2
                  type: integer
                  minimum: 1
                  maximum: 2
                keyVaultName:
                  description: >-
                    Used by: azureKeyVault
                  type: string
                dataFrom:
                  type: array
                  items:
                    type: string
                dataFromWithOptions:
                  type: array
                  items:
                    type: object
                    properties:
                      key:
                        description: Secret key in backend
                        type: string
                      isBinary:
                        description: >-
                          Whether the backend secret shall be treated as binary data
                          represented by a base64-encoded string. You must set this to true
                          for any base64-encoded binary data in the backend - to ensure it
                          is not encoded in base64 again. Default is false.
                        type: boolean
                      versionStage:
                        description: >-
                          Used by: alicloudSecretsManager, secretsManager
                        type: string
                      versionId:
                        description: >-
                          Used by: secretsManager
                        type: string
                    required:
                    - key
                data:
                  type: array
                  items:
                    type: object
                    properties:
                      key:
                        description: Secret key in backend
                        type: string
                      name:
                        description: Name set for this key in the generated secret
                        type: string
                      property:
                        description: Property to extract if secret in backend is a JSON object
                        type: string
                      isBinary:
                        description: >-
                          Whether the backend secret shall be treated as binary data
                          represented by a base64-encoded string. You must set this to true
                          for any base64-encoded binary data in the backend - to ensure it
                          is not encoded in base64 again. Default is false.
                        type: boolean
                      path:
                        description: >-
                          Path from SSM to scrape secrets
                          This will fetch all secrets and use the key from the secret as variable name
                        type: string
                      recursive:
                        description: Allow to recurse thru all child keys on a given path, default false
                        type: boolean
                      secretType:
                        description: >-
                          Used by: ibmcloudSecretsManager
                          Type of secret - one of username_password, iam_credentials or arbitrary
                        type: string
                      version:
                        description: >-
                          Used by: gcpSecretsManager
                        type: string
                        x-kubernetes-int-or-string: true
                      versionStage:
                        description: >-
                          Used by: alicloudSecretsManager, secretsManager
                        type: string
                      versionId:
                        description: >-
                          Used by: secretsManager
                        type: string
                    oneOf:
                      - required:
                          - key
                          - name
                      - required:
                          - path
                roleArn:
                  type: string
                  description: >-
                    Used by: alicloudSecretsManager, secretsManager, systemManager
                region:
                  type: string
                  description: >-
                    Used by: secretsManager, systemManager
                projectId:
                  type: string
                  description: >-
                    Used by: gcpSecretsManager
                keyByName:
                  type: boolean
                  description: >-
                    Whether to interpret the key as a secret name (if true) or ID (the default).
                    Used by: ibmcloudSecretsManager
              oneOf:
                - properties:
                    backendType:
                      enum:
                        - secretsManager
                        - systemManager
                - properties:
                    backendType:
                      enum:
                        - vault
                - properties:
                    backendType:
                      enum:
                        - azureKeyVault
                  required:
                    - keyVaultName
                - properties:
                    backendType:
                      enum:
                        - gcpSecretsManager
                - properties:
                    backendType:
                      enum:
                        - alicloudSecretsManager
                - properties:
                    backendType:
                      enum:
                        - ibmcloudSecretsManager
                - properties:
                    backendType:
                      enum:
                        - akeyless
              anyOf:
                - required:
                    - data
                - required:
                    - dataFrom
                - required:
                    - dataFromWithOptions
            status:
              type: object
              properties:
                lastSync:
                  type: string
                status:
                  type: string
                observedGeneration:
                  type: number
      additionalPrinterColumns:
        - jsonPath: .status.lastSync
          name: Last Sync
          type: date
        - jsonPath: .status.status
          name: status
          type: string
        - jsonPath: .metadata.creationTimestamp
          name: Age
          type: date

  names:
    shortNames:
      - es
    kind: ExternalSecret
    plural: externalsecrets
    singular: externalsecret
---
# Source: devtron/templates/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: devtron-kubernetes-external-secret
  labels:
    release: devtron
type: Opaque
---
# Source: devtron/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: devtron-kubernetes-external-cm
  labels:
    release: devtron
---
# Source: kubernetes-external-secrets/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: devtron-kubernetes-external-secrets
  namespace: "devtroncd"
  labels:
    app.kubernetes.io/name: kubernetes-external-secrets
    helm.sh/chart: kubernetes-external-secrets-8.5.1
    app.kubernetes.io/instance: devtron
---
# Source: kubernetes-external-secrets/templates/rbac.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: devtron-kubernetes-external-secrets
  labels:
    app.kubernetes.io/name: kubernetes-external-secrets
    helm.sh/chart: kubernetes-external-secrets-8.5.1
    app.kubernetes.io/instance: devtron
rules:
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["create", "update", "get"]
  - apiGroups: [""]
    resources: ["namespaces"]
    verbs: ["get", "watch", "list"]
  - apiGroups: ["apiextensions.k8s.io"]
    resources: ["customresourcedefinitions"]
    resourceNames: ["externalsecrets.kubernetes-client.io"]
    verbs: ["get", "update"]
  - apiGroups: ["kubernetes-client.io"]
    resources: ["externalsecrets"]
    verbs: ["get", "watch", "list"]
  - apiGroups: ["kubernetes-client.io"]
    resources: ["externalsecrets/status"]
    verbs: ["get", "update"]
---
# Source: kubernetes-external-secrets/templates/rbac.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: devtron-kubernetes-external-secrets
  labels:
    app.kubernetes.io/name: kubernetes-external-secrets
    helm.sh/chart: kubernetes-external-secrets-8.5.1
    app.kubernetes.io/instance: devtron
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: devtron-kubernetes-external-secrets
subjects:
  - name: devtron-kubernetes-external-secrets
    namespace: "devtroncd"
    kind: ServiceAccount
---
# Source: kubernetes-external-secrets/templates/rbac.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: devtron-kubernetes-external-secrets-auth
  labels:
    app.kubernetes.io/name: kubernetes-external-secrets
    helm.sh/chart: kubernetes-external-secrets-8.5.1
    app.kubernetes.io/instance: devtron
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: system:auth-delegator
subjects:
- name: devtron-kubernetes-external-secrets
  namespace: "devtroncd"
  kind: ServiceAccount
---
# Source: kubernetes-external-secrets/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: devtron-kubernetes-external-secrets
  namespace: "devtroncd"
  labels:
    app.kubernetes.io/name: kubernetes-external-secrets
    helm.sh/chart: kubernetes-external-secrets-8.5.1
    app.kubernetes.io/instance: devtron
spec:
  selector:
    app.kubernetes.io/name: kubernetes-external-secrets
  ports:
    - protocol: TCP
      port: 3001
      name: prometheus
      targetPort: prometheus
---
# Source: kubernetes-external-secrets/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: devtron-kubernetes-external-secrets
  namespace: "devtroncd"
  labels:
    app.kubernetes.io/name: kubernetes-external-secrets
    helm.sh/chart: kubernetes-external-secrets-8.5.1
    app.kubernetes.io/instance: devtron
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: kubernetes-external-secrets
      app.kubernetes.io/instance: devtron
  template:
    metadata:
      labels:
        app.kubernetes.io/name: kubernetes-external-secrets
        app.kubernetes.io/instance: devtron
    spec:
      serviceAccountName: devtron-kubernetes-external-secrets
      containers:
        - name: kubernetes-external-secrets
          image: "ghcr.io/external-secrets/kubernetes-external-secrets:8.5.1"
          ports:
          - name: prometheus
            containerPort: 3001
          imagePullPolicy: IfNotPresent
          resources:
            {}
          env:
          - name: "AKEYLESS_API_ENDPOINT"
            value: "https://api.akeyless.io"
          - name: "LOG_LEVEL"
            value: "info"
          - name: "LOG_MESSAGE_KEY"
            value: "msg"
          - name: "METRICS_PORT"
            value: "3001"
          - name: "POLLER_INTERVAL_MILLISECONDS"
            value: "10000"
          - name: "VAULT_ADDR"
            value: "http://127.0.0.1:8200"
          - name: "WATCH_TIMEOUT"
            value: "60000"
          # Params for env vars populated from k8s secrets and configmap
          envFrom:
          - configMapRef:
              name: devtron-kubernetes-external-cm
          - secretRef:
              name: devtron-kubernetes-external-secret
      securityContext:
        runAsNonRoot: true