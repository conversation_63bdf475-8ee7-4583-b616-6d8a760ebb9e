{"__inputs": [], "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "7.3.1"}, {"type": "panel", "id": "graph", "name": "Graph", "version": ""}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}], "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "This Graph shows latency of apps on Devtron dashboard.\n\nhttps://www.devtron.ai", "editable": true, "gnetId": 13320, "graphTooltip": 0, "id": null, "iteration": 1639744548837, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "hiddenSeries": false, "id": 5, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by(pod,app,rollouts_pod_template_hash, env) (label_replace(histogram_quantile($percentile, sum(rate(envoy_cluster_upstream_rq_time_bucket{appId=\"$app\", envId=\"$env\"}[1m])) by (le, pod)), \"rollouts_pod_template_hash\",  \"$1(new)\", \"rollouts_pod_template_hash\",  \"($new_rollout_pod_template_hash)\"))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{pod}}", "refId": "A"}, {"expr": "sum by(pod,app,rollouts_pod_template_hash, env) (label_replace(histogram_quantile($percentile, sum(rate(envoy_cluster_upstream_rq_time_bucket{app_id=\"$app\", env_id=\"$env\"}[1m])) by (le, pod)), \"rollouts_pod_template_hash\",  \"$1(new)\", \"rollouts_pod_template_hash\",  \"($new_rollout_pod_template_hash)\"))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{pod}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 0}, "hiddenSeries": false, "id": 3, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.1", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by(pod,app,rollouts_pod_template_hash, env) (label_replace(histogram_quantile($percentile, sum(rate(envoy_cluster_upstream_rq_time_bucket{appId=\"$app\", envId=\"$env\"}[1m])) by (le, pod)), \"rollouts_pod_template_hash\",  \"$1(new)\", \"rollouts_pod_template_hash\",  \"($new_rollout_pod_template_hash)\"))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{pod}}", "refId": "A"}, {"expr": "sum by(pod,app,rollouts_pod_template_hash, env) (label_replace(histogram_quantile($percentile, sum(rate(envoy_cluster_upstream_rq_time_bucket{app_id=\"$app\", env_id=\"$env\"}[1m])) by (le, pod)), \"rollouts_pod_template_hash\",  \"$1(new)\", \"rollouts_pod_template_hash\",  \"($new_rollout_pod_template_hash)\"))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{pod}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 8}, "hiddenSeries": false, "id": 2, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.1", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by(app,rollouts_pod_template_hash, env) (label_replace(histogram_quantile($percentile, sum(rate(envoy_cluster_upstream_rq_time_bucket{appId=\"$app\", envId=\"$env\"}[1m])) by (le,rollouts_pod_template_hash)), \"rollouts_pod_template_hash\",  \"$1(new)\", \"rollouts_pod_template_hash\",  \"($new_rollout_pod_template_hash)\"))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{rollouts_pod_template_hash}}", "refId": "A"}, {"expr": "sum by(app,rollouts_pod_template_hash, env) (label_replace(histogram_quantile($percentile, sum(rate(envoy_cluster_upstream_rq_time_bucket{app_id=\"$app\", env_id=\"$env\"}[1m])) by (le,rollouts_pod_template_hash)), \"rollouts_pod_template_hash\", \"$1(new)\", \"rollouts_pod_template_hash\",  \"($new_rollout_pod_template_hash)\"))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{rollouts_pod_template_hash}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 9}, "hiddenSeries": false, "id": 4, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.1", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by(app,rollouts_pod_template_hash, env) (label_replace(histogram_quantile($percentile, sum(rate(envoy_cluster_upstream_rq_time_bucket{appId=\"$app\", envId=\"$env\"}[1m])) by (le,rollouts_pod_template_hash)), \"rollouts_pod_template_hash\",  \"$1(new)\", \"rollouts_pod_template_hash\",  \"($new_rollout_pod_template_hash)\"))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{rollouts_pod_template_hash}}", "refId": "A"}, {"expr": "sum by(app,rollouts_pod_template_hash, env) (label_replace(histogram_quantile($percentile, sum(rate(envoy_cluster_upstream_rq_time_bucket{app_id=\"$app\", env_id=\"$env\"}[1m])) by (le,rollouts_pod_template_hash)), \"rollouts_pod_template_hash\", \"$1(new)\", \"rollouts_pod_template_hash\",  \"($new_rollout_pod_template_hash)\"))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{rollouts_pod_template_hash}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "10s", "schemaVersion": 26, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {}, "datasource": "$datasource", "definition": "label_values(kube_pod_labels,label_app_id)", "error": null, "hide": 0, "includeAll": false, "label": "app", "multi": false, "name": "app", "options": [], "query": "label_values(kube_pod_labels,label_app_id)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {}, "datasource": "$datasource", "definition": "label_values(kube_pod_labels, label_envId)", "error": null, "hide": 0, "includeAll": false, "label": "env", "multi": false, "name": "env", "options": [], "query": "label_values(kube_pod_labels, label_envId)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": true, "text": "6b68bf4b89", "value": "6b68bf4b89"}, "error": null, "hide": 0, "label": "new_rollout_pod_template_hash", "name": "new_rollout_pod_template_hash", "options": [{"selected": true, "text": "6b68bf4b89", "value": "6b68bf4b89"}], "query": "6b68bf4b89", "skipUrlSync": false, "type": "textbox"}, {"current": {"selected": false, "text": "Prometheus-devtron", "value": "Prometheus-devtron"}, "error": null, "hide": 0, "includeAll": false, "label": "datasource", "multi": false, "name": "datasource", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": null, "current": {"selected": false, "text": "0.5", "value": "0.5"}, "error": null, "hide": 0, "includeAll": false, "label": "percentile", "multi": false, "name": "percentile", "options": [{"selected": true, "text": "0.5", "value": "0.5"}, {"selected": false, "text": "0.6", "value": "0.6"}, {"selected": false, "text": "0.9", "value": "0.9"}, {"selected": false, "text": "0.95", "value": "0.95"}, {"selected": false, "text": "0.99", "value": "0.99"}], "query": "0.5, 0.6, 0.9, 0.95, 0.99", "skipUrlSync": false, "type": "custom"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "latency", "uid": "devtron-app-metrics-latency", "version": 60}