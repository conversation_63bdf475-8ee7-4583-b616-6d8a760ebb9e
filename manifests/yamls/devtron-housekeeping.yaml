apiVersion: v1
kind: ConfigMap
metadata:
  name: devtron-housekeeping
  labels:
    app: devtron-housekeeping
    release: "devtron"
data:
  apply-labels.sh: |
    kubectl -n devtroncd label all --all "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl -n devtroncd annotate all --all "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl -n devtroncd label secret --all "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl -n devtroncd annotate secret --all "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl -n devtroncd label cm --all "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl -n devtroncd annotate cm --all "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl -n devtroncd label sa --all "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl -n devtroncd annotate sa --all "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl -n devtroncd label role --all "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl -n devtroncd annotate role --all "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl -n devtroncd label rolebinding --all "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl -n devtroncd annotate rolebinding --all "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrole argocd-application-controller "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrole argocd-application-controller "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrolebinding argocd-application-controller "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrolebinding argocd-application-controller "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrole argocd-server "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrole argocd-server "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrolebinding argocd-server "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrolebinding argocd-server "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrole devtron-kubernetes-external-secrets "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrole devtron-kubernetes-external-secrets "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrolebinding devtron-kubernetes-external-secrets "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrolebinding devtron-kubernetes-external-secrets "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrole devtron-grafana-clusterrole "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrole devtron-grafana-clusterrole "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrolebinding devtron-grafana-clusterrole "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrolebinding devtron-grafana-clusterrole "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrole nats-server "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrole nats-server "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrolebinding nats-server "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrolebinding nats-server "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrole argo-rollouts-aggregate-to-admin "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrole argo-rollouts-aggregate-to-admin "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrolebinding argo-rollouts-aggregate-to-admin "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrolebinding argo-rollouts-aggregate-to-admin "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrole argo-rollouts-aggregate-to-edit "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrole argo-rollouts-aggregate-to-edit "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrolebinding argo-rollouts-aggregate-to-edit "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrolebinding argo-rollouts-aggregate-to-edit "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrole argo-aggregate-to-view "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrole argo-aggregate-to-view "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrolebinding argo-aggregate-to-view "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrolebinding argo-aggregate-to-view "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrole argo-cluster-role "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrole argo-cluster-role "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrolebinding argo-cluster-role "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrolebinding argo-cluster-role "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrole argo-rollouts-aggregate-to-view "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrole argo-rollouts-aggregate-to-view "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrolebinding argo-rollouts-aggregate-to-view "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrolebinding argo-rollouts-aggregate-to-view "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrole argo-rollouts-clusterrole "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrole argo-rollouts-clusterrole "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrolebinding argo-rollouts-clusterrole "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrolebinding argo-rollouts-clusterrole "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrole workflow-cluster-role "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrole workflow-cluster-role "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrolebinding workflow-cluster-role "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrolebinding workflow-cluster-role "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrole argo-ui-cluster-role "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrole argo-ui-cluster-role "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrolebinding argo-ui-cluster-role "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrolebinding argo-ui-cluster-role "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label sa argo -n argo "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate sa argo -n argo "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label role argo-role -n argo "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate role argo-role -n argo "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label rolebinding argo-binding -n argo "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate rolebinding argo-binding -n argo "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label cm workflow-controller-configmap -n argo "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate cm workflow-controller-configmap -n argo "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label deploy workflow-controller -n argo "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate deploy workflow-controller -n argo "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl -n devtron-cd label secret --all "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl -n devtron-cd annotate secret --all "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl -n devtron-ci label secret --all "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl -n devtron-ci annotate secret --all "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label ns argo "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate ns argo "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label ns devtron-ci "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate ns devtron-ci "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label ns devtron-cd "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate ns devtron-cd "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label crd workflows.argoproj.io "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate crd workflows.argoproj.io "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label crd workflowtemplates.argoproj.io "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate crd workflowtemplates.argoproj.io "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrole argo-aggregate-to-admin "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrole argo-aggregate-to-admin "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrole argo-aggregate-to-edit "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrole argo-aggregate-to-edit "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrole argo-binding "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrole argo-binding "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrolebinding argo-aggregate-to-admin "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrolebinding argo-aggregate-to-admin "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrolebinding argo-aggregate-to-edit "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrolebinding argo-aggregate-to-edit "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrolebinding argo-binding "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrolebinding argo-binding "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl -n devtroncd label PodSecurityPolicy --all "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl -n devtroncd annotate --all PodSecurityPolicy "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl -n devtroncd label pvc --all "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl -n devtroncd annotate pvc --all "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrole devtron-grafana-clusterrole "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrole devtron-grafana-clusterrole "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrolebinding devtron-grafana-clusterrolebinding "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrolebinding devtron-grafana-clusterrolebinding "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrole kubewatch "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrole kubewatch "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
    kubectl label clusterrolebinding kubewatch "app.kubernetes.io/managed-by=Helm" --overwrite
    kubectl annotate clusterrolebinding kubewatch "meta.helm.sh/release-name=$RELEASE_NAME" "meta.helm.sh/release-namespace=devtroncd" --overwrite
---
apiVersion: batch/v1
kind: Job
metadata:
  name: devtron-housekeeping
spec:
  template:
    spec:
      serviceAccountName: devtron
      containers:
      - name: devtron-housekeeping
        image: quay.io/devtron/kubectl:latest
        command: ['sh', '-c', 'sh /apply-labels.sh; exit 0']
        env:
        - name: RELEASE_NAME
          valueFrom:
            configMapKeyRef:
              name: devtron-operator-cm
              key: DEVTRON_HELM_RELEASE_NAME
        resources:
          limits:
            cpu: "2"
            memory: 2Gi
          requests:
            cpu: "50m"
            memory: 50Mi
        volumeMounts:
          - name: apply-helm-labels
            mountPath: /apply-labels.sh
            subPath: apply-labels.sh
      volumes:
        - name: apply-helm-labels
          configMap:
            name: devtron-housekeeping
      restartPolicy: OnFailure
  backoffLimit: 20
  activeDeadlineSeconds: 1500
