apiVersion: v1
kind: Secret
metadata:
  name: devtron-operator-secret
  namespace: devtroncd
type: Opaque
data:
#  WEBHOOK_TOKEN: Y2hhbmdlX21l
#  If No POSTGRESQL_PASSWORD is provided, a password is automatically generated and saved in secret devtron-secret
#  POSTGRESQL_PASSWORD: "change-me"

#  REQUIRED IF BLOB_STORAGE_PROVIDER=AZURE Token with read write access to AZURE_BLOB_CONTAINER_CI_LOG and AZURE_BLOB_CONTAINER_CI_CACHE
#  AZURE_ACCOUNT_KEY: "xxxxxxxxxx"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: devtron-operator-cm
  namespace: devtroncd
data:
  BLOB_STORAGE_PROVIDER: "MINIO" # AZURE| GCP| S3 | MINIO
  ENABLE_LEGACY_API: "false"  # Set to true if you are installing Devtron on a kubernetes version < k8s 1.19
# Amazon AWS S3 bucket and region for storing Build-cache for faster build process. Mandatory if BLOB_STORAGE_PROVIDER is AWS.
  #DEFAULT_CACHE_BUCKET: "change-me"   #Do not include s3://
  #DEFAULT_CACHE_BUCKET_REGION: "us-east-1"
# Amazon AWS S3 bucket and region for storing Build-logs. Mandatory if BLOB_STORAGE_PROVIDER is AWS.
  #DEFAULT_BUILD_LOGS_BUCKET: "change-me"   #Do not include s3://
  #DEFAULT_CD_LOGS_BUCKET_REGION: "us-east-1"
# Amazon AWS Secret Region if you will be using AWS Secret manager for storing secrets.
  #EXTERNAL_SECRET_AMAZON_REGION: ""
# Azure Blob storage Info for storing Build Logs and Build cache for faster build process.
  #AZURE_ACCOUNT_NAME: "test-account"
  #AZURE_BLOB_CONTAINER_CI_LOG: "ci-log-container"
  #AZURE_BLOB_CONTAINER_CI_CACHE: "ci-cache-container"
