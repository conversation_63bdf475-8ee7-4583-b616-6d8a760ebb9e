apiVersion: v1
kind: ConfigMap
metadata:
  name: minio-storage-override-cm
  namespace:  devtroncd
data:
  override: |
    apiVersion: v1
    kind: Secret
    metadata:
      name: devtron-minio
    #    update:
    #      data:
    #        accesskey: ""
    #        secretkey: ""
    ---
    apiVersion: apps/v1
    kind: StatefulSet
    metadata:
      name: devtron-minio
    update:
      spec:
        template:
          spec:
            containers:
            - resources:
                requests:
                  memory: 100Mi
    ---
    apiVersion: batch/v1
    kind: Job
    metadata:
      name: devtron-minio-make-bucket-job
    #    update:
    #      spec:
    #        template:
    #          spec:
    #            containers:
    #            - resources:
    #                requests:
    #                  memory: 128Mi














