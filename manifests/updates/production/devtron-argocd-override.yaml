apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-override-cm
  namespace:  devtroncd
data:
  override: |
    apiVersion: v1
    kind: ConfigMap
    metadata:
      name: argocd-ssh-known-hosts-cm
    #   update:
    #     data:
    #       ssh_known_hosts: |
    #         ssh-public-key
    ---
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: argocd-dex-server
    update:
      spec:
        template:
          spec:
            containers:
            - resources:
                limits:
                  cpu: 0.02
                  memory: 50Mi
                requests:
                  cpu: 0.02
                  memory: 50Mi
    ---
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: argocd-redis
    update:
      spec:
        template:
          spec:
            containers:
            - resources:
                limits:
                  cpu: 0.02
                  memory: 100Mi
                requests:
                  cpu: 0.02
                  memory: 100Mi
    ---
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: argocd-repo-server
    update:
      spec:
        template:
          spec:
            containers:
            - resources:
                limits:
                  cpu: 0.5
                  memory: 1Gi
                requests:
                  cpu: 0.5
                  memory: 1Gi
    ---
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: argocd-server
    update:
      spec:
        template:
          spec:
            containers:
            - resources:
                limits:
                  cpu: 0.3
                  memory: 400Mi
                requests:
                  cpu: 0.3
                  memory: 400Mi
    ---
    apiVersion: apps/v1
    kind: StatefulSet
    metadata:
      name: argocd-application-controller
    update:
      spec:
        template:
          spec:
            containers:
            - resources:
                limits:
                  cpu: 1
                  memory: 2Gi
                requests:
                  cpu: 1
                  memory: 2Gi

















