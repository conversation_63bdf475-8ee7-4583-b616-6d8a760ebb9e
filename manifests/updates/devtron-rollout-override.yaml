apiVersion: v1
kind: ConfigMap
metadata:
  name: rollout-override-cm
  namespace:  devtroncd
data:
  override: |
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: argo-rollouts
    update:
      spec:
        template:
          spec:
            containers:
            - resources:
                requests:                  
                  cpu: 0.02
                  memory: 50Mi
                limits:                 
                  cpu: 0.02
                  memory: 50Mi