apiVersion: v1
kind: ConfigMap
metadata:
  name: workflow-override-cm
  namespace:  devtroncd
data:
  override: |
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: argo-ui
    update:
      spec:
        template:
          spec:
            containers:
            - resources:
                limits:
                  cpu: 0.01
                  memory: 30Mi
                requests:
                  cpu: 0.01
                  memory: 30Mi
#            - env:
#              - name: ARGO_NAMESPACE
#                valueFrom:
#                  fieldRef:
#                    apiVersion: v1
#                    fieldPath: metadata.namespace
#              - name: IN_CLUSTER
#                value: "true"
#              - name: ENABLE_WEB_CONSOLE
#                value: "false"
#              - name: BASE_HREF
#                value: /