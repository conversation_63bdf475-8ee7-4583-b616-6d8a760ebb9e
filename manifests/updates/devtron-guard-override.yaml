apiVersion: v1
kind: ConfigMap
metadata:
  name: guard-override-cm
  namespace:  devtroncd
data:
  override: |
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: guard
    update:
      spec:
        template:
          spec:
            containers:
            - resources:
                limits:
                  cpu: 0.01
                  memory: 30Mi
                requests:
                  cpu: 0.01
                  memory: 30Mi
#             env:
#              - name: CONFIG_HASH
#                value: give-value
#              - name: SECRET_HASH
#                value: give-value
#              - name: DEVTRON_APP_NAME
#                value: guard

