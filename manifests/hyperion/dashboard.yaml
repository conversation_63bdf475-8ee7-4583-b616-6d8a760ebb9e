---
# Source: dashboard/templates/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: devtron-dashboard-secret
  labels:
    release: devtron
type: Opaque
---
# Source: dashboard/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: dashboard-cm
  labels:
    release: devtron
data:
  GA_ENABLED: "false"
  HOTJAR_ENABLED: "false"
  SENTRY_ENABLED: "false"
  SENTRY_ENV: PRODUCTION
---
# Source: dashboard/templates/sidecar-configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: sidecar-config-dashboard
  labels:
    release: devtron
data:
  envoy-config.json: |
    {
      "stats_config": {
        "use_all_default_tags": false,
        "stats_tags": [
          {
            "tag_name": "cluster_name",
            "regex": "^cluster\\.((.+?(\\..+?\\.svc\\.cluster\\.local)?)\\.)"
          },
          {
            "tag_name": "tcp_prefix",
            "regex": "^tcp\\.((.*?)\\.)\\w+?$"
          },
          {
            "tag_name": "response_code",
            "regex": "_rq(_(\\d{3}))$"
          },
          {
            "tag_name": "response_code_class",
            "regex": ".*_rq(_(\\dxx))$"
          },
          {
            "tag_name": "http_conn_manager_listener_prefix",
            "regex": "^listener(?=\\.).*?\\.http\\.(((?:[_.[:digit:]]*|[_\\[\\]aAbBcCdDeEfF[:digit:]]*))\\.)"
          },
          {
            "tag_name": "http_conn_manager_prefix",
            "regex": "^http\\.(((?:[_.[:digit:]]*|[_\\[\\]aAbBcCdDeEfF[:digit:]]*))\\.)"
          },
          {
            "tag_name": "listener_address",
            "regex": "^listener\\.(((?:[_.[:digit:]]*|[_\\[\\]aAbBcCdDeEfF[:digit:]]*))\\.)"
          },
          {
            "tag_name": "mongo_prefix",
            "regex": "^mongo\\.(.+?)\\.(collection|cmd|cx_|op_|delays_|decoding_)(.*?)$"
          }
        ],
        "stats_matcher": {
          "inclusion_list": {
            "patterns": [
              {
              "regex": ".*_rq_\\dxx$"
              },
              {
              "regex": ".*_rq_time$"
              },
              {
              "regex": "cluster.*"
              },
            ]
          }
        }
      },
      "admin": {
        "access_log_path": "/dev/null",
        "address": {
          "socket_address": {
            "address": "0.0.0.0",
            "port_value": 9901
          }
        }
      },
      "static_resources": {
        "clusters": [
          {
            "name": "1-0",
            "type": "STATIC",
            "connect_timeout": "0.250s",
            "lb_policy": "ROUND_ROBIN",
            "load_assignment": {
              "cluster_name": "9",
              "endpoints": {
                "lb_endpoints": [
                {
                  "endpoint": {
                    "address": {
                      "socket_address": {
                        "protocol": "TCP",
                        "address": "127.0.0.1",
                        "port_value": 80
                      }
                    }
                  }
                }
                ]
              }
            }
          },
        ],
        "listeners":[
          {
            "address": {
              "socket_address": {
                "protocol": "TCP",
                "address": "0.0.0.0",
                "port_value": 8790
              }
            },
            "filter_chains": [
              {
                "filters": [
                  {
                    "name": "envoy.filters.network.http_connection_manager",
                    "config": {
                      "codec_type": "AUTO",
                      "stat_prefix": "stats",
                      "route_config": {
                        "virtual_hosts": [
                          {
                            "name": "backend",
                            "domains": [
                              "*"
                            ],
                            "routes": [
                              {
                                "match": {
                                  "prefix": "/"
                                },
                                "route": {
                                  "cluster": "1-0"
                                }
                              }
                            ]
                          }
                        ]
                      },
                      "http_filters": {
                        "name": "envoy.filters.http.router"
                      }
                    }
                  }
                ]
              }
            ]
          },
        ]
      }
    }
---
# Source: dashboard/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: dashboard-service
  labels:
    app: dashboard
    chart: dashboard-3.9.1
    release: devtron
spec:
  type: ClusterIP
  ports:
    - port: 80
      targetPort: app
      protocol: TCP
      name: app
    - port: 9901
      name: envoy-admin
  selector:
    app: dashboard
---
# Source: dashboard/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dashboard
  labels:
    app: dashboard
    chart: dashboard-3.9.1
    release: devtron
spec:
  selector:
    matchLabels:
      app: dashboard
      release: devtron
  replicas: 1
  minReadySeconds: 60
  template:
    metadata:
      labels:
        app: dashboard
        release: devtron
    spec:
      terminationGracePeriodSeconds: 30
      restartPolicy: Always
      containers:
        - name: envoy
          image: "quay.io/devtron/envoy:v1.14.1"
          ports:
            - containerPort: 9901
              protocol: TCP
              name: envoy-admin
            - name: app
              containerPort: 8790
              protocol: TCP
          command: ["/usr/local/bin/envoy"]
          args: ["-c", "/etc/envoy-config/envoy-config.json", "-l", "info", "--log-format", "[METADATA][%Y-%m-%d %T.%e][%t][%l][%n] %v"]
          volumeMounts:
            - name: envoy-config-volume
              mountPath: /etc/envoy-config/
        - name: dashboard
          image: "quay.io/devtron/dashboard:075cba2a-136-6172"
          imagePullPolicy: IfNotPresent
          ports:
            - name: app
              containerPort: 80
              protocol: TCP
          env:
            - name: CONFIG_HASH
              value: d79e473c6352af87345c540cbbe1307c4cdc5a014f3393d3d46ff194442179e0
            - name: SECRET_HASH
              value: 552efd3342e616f7ff4a7d2fffd4879809af5efabeda4c1a8597aeafefcb017d
            - name: DEVTRON_APP_NAME
              value: dashboard
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
          envFrom:
          - configMapRef:
              name: dashboard-cm
          - secretRef:    
              name: devtron-dashboard-secret
          volumeMounts: []
      volumes:
        - name: envoy-config-volume
          configMap:
            name: sidecar-config-dashboard
  revisionHistoryLimit: 3
---
# Source: dashboard/templates/metrics-service-monitor.yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: dashboard
  labels:
    app: dashboard
    chart: dashboard-3.9.1
    release: devtron
spec:
  jobLabel: dashboard
  endpoints:
    - port: envoy-admin
      interval: 30s
      path: /stats/prometheus
  selector:
    matchLabels:
      app: dashboard
  namespaceSelector:
    matchNames:
      - devtroncd:w
