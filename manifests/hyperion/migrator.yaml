apiVersion: batch/v1
kind: Job
metadata:
  name: postgresql-migrate-devtron
spec:
  ttlSecondsAfterFinished: 1000
  template:
    spec:
      containers:
      - name: postgresql-migrate-devtron
        image: quay.io/devtron/migrator:6687f572-133-2208
        env:
        - name: GIT_BRANCH
          value: main
        - name: SCRIPT_LOCATION
          value: scripts/sql/
        - name: GIT_REPO_URL
          value: https://github.com/devtron-labs/devtron.git
        - name: DB_TYPE
          value: postgres
        - name: DB_USER_NAME
          value: postgres
        - name: DB_HOST
          value: postgresql-postgresql.devtroncd
        - name: DB_PORT
          value: "5432"
        - name: DB_NAME
          value: orchestrator                      
        - name: MIGRATE_TO_VERSION
          value: "0"
        - name: GIT_HASH
          value: b1b935700ef3dbb3222bdbc1c6bf286d45a4566d
        envFrom:
          - secretRef:
              name: postgresql-migrator
      restartPolicy: OnFailure
  backoffLimit: 20
  activeDeadlineSeconds: 1500
---
apiVersion: batch/v1
kind: Job
metadata:
  name: postgresql-migrate-casbin
spec:
  template:
    spec:
      serviceAccountName: devtron
      containers:
      - name: devtron-rollout
        image: quay.io/bitnami/kubectl:latest
        command: ['sh', '-c', 'kubectl rollout restart deployment/devtron -n devtroncd']
      initContainers:
      - name: postgresql-migrate-casbin
        image: quay.io/devtron/migrator:6687f572-133-2208
        env:
        - name: SCRIPT_LOCATION
          value: scripts/casbin/
        - name: GIT_REPO_URL
          value: https://github.com/devtron-labs/devtron.git
        - name: DB_TYPE
          value: postgres
        - name: DB_USER_NAME
          value: postgres
        - name: DB_HOST
          value: postgresql-postgresql.devtroncd
        - name: DB_PORT
          value: "5432"
        - name: DB_NAME
          value: casbin
        - name: MIGRATE_TO_VERSION
          value: "0"
        - name: GIT_HASH
          value: b1b935700ef3dbb3222bdbc1c6bf286d45a4566d
        - name: GIT_BRANCH
          value: main
        envFrom:
          - secretRef:
              name: postgresql-migrator
        resources:
          requests:
            cpu: 0.5
            memory: 500Mi
      restartPolicy: OnFailure
  backoffLimit: 20
  activeDeadlineSeconds: 1500